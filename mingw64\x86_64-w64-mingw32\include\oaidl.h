/*** Autogenerated by WIDL 1.6 from include/oaidl.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __oaidl_h__
#define __oaidl_h__

/* Forward declarations */

#ifndef __ICreateTypeInfo_FWD_DEFINED__
#define __ICreateTypeInfo_FWD_DEFINED__
typedef interface ICreateTypeInfo ICreateTypeInfo;
#endif

#ifndef __ICreateTypeInfo2_FWD_DEFINED__
#define __ICreateTypeInfo2_FWD_DEFINED__
typedef interface ICreateTypeInfo2 ICreateTypeInfo2;
#endif

#ifndef __ICreateTypeLib_FWD_DEFINED__
#define __ICreateTypeLib_FWD_DEFINED__
typedef interface ICreateTypeLib ICreateTypeLib;
#endif

#ifndef __ICreateTypeLib2_FWD_DEFINED__
#define __ICreateTypeLib2_FWD_DEFINED__
typedef interface ICreateTypeLib2 ICreateTypeLib2;
#endif

#ifndef __IDispatch_FWD_DEFINED__
#define __IDispatch_FWD_DEFINED__
typedef interface IDispatch IDispatch;
#endif

#ifndef __IEnumVARIANT_FWD_DEFINED__
#define __IEnumVARIANT_FWD_DEFINED__
typedef interface IEnumVARIANT IEnumVARIANT;
#endif

#ifndef __ITypeComp_FWD_DEFINED__
#define __ITypeComp_FWD_DEFINED__
typedef interface ITypeComp ITypeComp;
#endif

#ifndef __ITypeInfo_FWD_DEFINED__
#define __ITypeInfo_FWD_DEFINED__
typedef interface ITypeInfo ITypeInfo;
#endif

#ifndef __ITypeInfo2_FWD_DEFINED__
#define __ITypeInfo2_FWD_DEFINED__
typedef interface ITypeInfo2 ITypeInfo2;
#endif

#ifndef __ITypeLib_FWD_DEFINED__
#define __ITypeLib_FWD_DEFINED__
typedef interface ITypeLib ITypeLib;
#endif

#ifndef __ITypeLib2_FWD_DEFINED__
#define __ITypeLib2_FWD_DEFINED__
typedef interface ITypeLib2 ITypeLib2;
#endif

#ifndef __ITypeChangeEvents_FWD_DEFINED__
#define __ITypeChangeEvents_FWD_DEFINED__
typedef interface ITypeChangeEvents ITypeChangeEvents;
#endif

#ifndef __IErrorInfo_FWD_DEFINED__
#define __IErrorInfo_FWD_DEFINED__
typedef interface IErrorInfo IErrorInfo;
#endif

#ifndef __ICreateErrorInfo_FWD_DEFINED__
#define __ICreateErrorInfo_FWD_DEFINED__
typedef interface ICreateErrorInfo ICreateErrorInfo;
#endif

#ifndef __ISupportErrorInfo_FWD_DEFINED__
#define __ISupportErrorInfo_FWD_DEFINED__
typedef interface ISupportErrorInfo ISupportErrorInfo;
#endif

#ifndef __ITypeFactory_FWD_DEFINED__
#define __ITypeFactory_FWD_DEFINED__
typedef interface ITypeFactory ITypeFactory;
#endif

#ifndef __ITypeMarshal_FWD_DEFINED__
#define __ITypeMarshal_FWD_DEFINED__
typedef interface ITypeMarshal ITypeMarshal;
#endif

#ifndef __IRecordInfo_FWD_DEFINED__
#define __IRecordInfo_FWD_DEFINED__
typedef interface IRecordInfo IRecordInfo;
#endif

#ifndef __IErrorLog_FWD_DEFINED__
#define __IErrorLog_FWD_DEFINED__
typedef interface IErrorLog IErrorLog;
#endif

#ifndef __IPropertyBag_FWD_DEFINED__
#define __IPropertyBag_FWD_DEFINED__
typedef interface IPropertyBag IPropertyBag;
#endif

/* Headers for imported files */

#include <objidl.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __ICreateTypeInfo_FWD_DEFINED__
#define __ICreateTypeInfo_FWD_DEFINED__
typedef interface ICreateTypeInfo ICreateTypeInfo;
#endif

#ifndef __ICreateTypeInfo2_FWD_DEFINED__
#define __ICreateTypeInfo2_FWD_DEFINED__
typedef interface ICreateTypeInfo2 ICreateTypeInfo2;
#endif

#ifndef __ICreateTypeLib_FWD_DEFINED__
#define __ICreateTypeLib_FWD_DEFINED__
typedef interface ICreateTypeLib ICreateTypeLib;
#endif

#ifndef __ICreateTypeLib2_FWD_DEFINED__
#define __ICreateTypeLib2_FWD_DEFINED__
typedef interface ICreateTypeLib2 ICreateTypeLib2;
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#ifndef __IDispatch_FWD_DEFINED__
#define __IDispatch_FWD_DEFINED__
typedef interface IDispatch IDispatch;
#endif

#ifndef __IEnumVARIANT_FWD_DEFINED__
#define __IEnumVARIANT_FWD_DEFINED__
typedef interface IEnumVARIANT IEnumVARIANT;
#endif

#ifndef __ITypeComp_FWD_DEFINED__
#define __ITypeComp_FWD_DEFINED__
typedef interface ITypeComp ITypeComp;
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __ITypeInfo_FWD_DEFINED__
#define __ITypeInfo_FWD_DEFINED__
typedef interface ITypeInfo ITypeInfo;
#endif

#ifndef __ITypeInfo2_FWD_DEFINED__
#define __ITypeInfo2_FWD_DEFINED__
typedef interface ITypeInfo2 ITypeInfo2;
#endif

#ifndef __ITypeLib_FWD_DEFINED__
#define __ITypeLib_FWD_DEFINED__
typedef interface ITypeLib ITypeLib;
#endif

#ifndef __ITypeLib2_FWD_DEFINED__
#define __ITypeLib2_FWD_DEFINED__
typedef interface ITypeLib2 ITypeLib2;
#endif

#ifndef __ITypeChangeEvents_FWD_DEFINED__
#define __ITypeChangeEvents_FWD_DEFINED__
typedef interface ITypeChangeEvents ITypeChangeEvents;
#endif

#ifndef __IErrorInfo_FWD_DEFINED__
#define __IErrorInfo_FWD_DEFINED__
typedef interface IErrorInfo IErrorInfo;
#endif

#ifndef __ICreateErrorInfo_FWD_DEFINED__
#define __ICreateErrorInfo_FWD_DEFINED__
typedef interface ICreateErrorInfo ICreateErrorInfo;
#endif

#ifndef __ISupportErrorInfo_FWD_DEFINED__
#define __ISupportErrorInfo_FWD_DEFINED__
typedef interface ISupportErrorInfo ISupportErrorInfo;
#endif

#ifndef __ITypeFactory_FWD_DEFINED__
#define __ITypeFactory_FWD_DEFINED__
typedef interface ITypeFactory ITypeFactory;
#endif

#ifndef __ITypeMarshal_FWD_DEFINED__
#define __ITypeMarshal_FWD_DEFINED__
typedef interface ITypeMarshal ITypeMarshal;
#endif

#ifndef __IRecordInfo_FWD_DEFINED__
#define __IRecordInfo_FWD_DEFINED__
typedef interface IRecordInfo IRecordInfo;
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IOleAutomationTypes interface (v1.0)
 */
#ifndef __IOleAutomationTypes_INTERFACE_DEFINED__
#define __IOleAutomationTypes_INTERFACE_DEFINED__

extern RPC_IF_HANDLE IOleAutomationTypes_v1_0_c_ifspec;
extern RPC_IF_HANDLE IOleAutomationTypes_v1_0_s_ifspec;
typedef CY CURRENCY;
typedef struct tagSAFEARRAYBOUND {
    ULONG cElements;
    LONG lLbound;
} SAFEARRAYBOUND;
typedef struct tagSAFEARRAYBOUND *LPSAFEARRAYBOUND;

typedef struct _wireVARIANT *wireVARIANT;
typedef struct _wireBRECORD *wireBRECORD;

typedef struct _wireSAFEARR_BSTR {
    ULONG Size;
    wireBSTR *aBstr;
} SAFEARR_BSTR;

typedef struct _wireSAFEARR_UNKNOWN {
    ULONG Size;
    IUnknown **apUnknown;
} SAFEARR_UNKNOWN;

typedef struct _wireSAFEARR_DISPATCH {
    ULONG Size;
    IDispatch **apDispatch;
} SAFEARR_DISPATCH;

typedef struct _wireSAFEARR_VARIANT {
    ULONG Size;
    wireVARIANT *aVariant;
} SAFEARR_VARIANT;

typedef struct _wireSAFEARR_BRECORD {
    ULONG Size;
    wireBRECORD *aRecord;
} SAFEARR_BRECORD;

typedef struct _wireSAFEARR_HAVEIID {
    ULONG Size;
    IUnknown **apUnknown;
    IID iid;
} SAFEARR_HAVEIID;

typedef enum tagSF_TYPE {
    SF_ERROR = VT_ERROR,
    SF_I1 = VT_I1,
    SF_I2 = VT_I2,
    SF_I4 = VT_I4,
    SF_I8 = VT_I8,
    SF_BSTR = VT_BSTR,
    SF_UNKNOWN = VT_UNKNOWN,
    SF_DISPATCH = VT_DISPATCH,
    SF_VARIANT = VT_VARIANT,
    SF_RECORD = VT_RECORD,
    SF_HAVEIID = VT_UNKNOWN | VT_RESERVED
} SF_TYPE;

typedef struct _wireSAFEARRAY_UNION {
    ULONG sfType;
    union {
        SAFEARR_BSTR BstrStr;
        SAFEARR_UNKNOWN UnknownStr;
        SAFEARR_DISPATCH DispatchStr;
        SAFEARR_VARIANT VariantStr;
        SAFEARR_BRECORD RecordStr;
        SAFEARR_HAVEIID HaveIidStr;
        BYTE_SIZEDARR ByteStr;
        WORD_SIZEDARR WordStr;
        DWORD_SIZEDARR LongStr;
        HYPER_SIZEDARR HyperStr;
    } u;
} SAFEARRAYUNION;

typedef struct _wireSAFEARRAY {
    USHORT cDims;
    USHORT fFeatures;
    ULONG cbElements;
    ULONG cLocks;
    SAFEARRAYUNION uArrayStructs;
    SAFEARRAYBOUND rgsabound[1];
} *wireSAFEARRAY;
typedef wireSAFEARRAY *wirePSAFEARRAY;

typedef struct tagSAFEARRAY {
    USHORT cDims;
    USHORT fFeatures;
    ULONG cbElements;
    ULONG cLocks;
    PVOID pvData;
    SAFEARRAYBOUND rgsabound[1];
} SAFEARRAY;
typedef SAFEARRAY *LPSAFEARRAY;

#define FADF_AUTO (0x1)

#define FADF_STATIC (0x2)

#define FADF_EMBEDDED (0x4)

#define FADF_FIXEDSIZE (0x10)

#define FADF_RECORD (0x20)

#define FADF_HAVEIID (0x40)

#define FADF_HAVEVARTYPE (0x80)

#define FADF_BSTR (0x100)

#define FADF_UNKNOWN (0x200)

#define FADF_DISPATCH (0x400)

#define FADF_VARIANT (0x800)

#define FADF_RESERVED (0xf008)


/* Kludge for 3.0 release to disable new default-behavior.
 * For now we define _FORCENAMELESSUNION.  For 4.0 this define
 * will be removed. */
#ifndef _FORCENAMELESSUNION
#define _FORCENAMELESSUNION 1
#endif

#if (__STDC__ && !defined(__cplusplus) && !defined(_FORCENAMELESSUNION)) || defined(NONAMELESSUNION) || (defined (_MSC_VER) && !defined(_MSC_EXTENSIONS) && !defined(_FORCENAMELESSUNION))
#define __VARIANT_NAME_1 n1
#define __VARIANT_NAME_2 n2
#define __VARIANT_NAME_3 n3
#define __VARIANT_NAME_4 brecVal
#else
#define __tagVARIANT
#define __VARIANT_NAME_1
#define __VARIANT_NAME_2
#define __VARIANT_NAME_3
#define __tagBRECORD
#define __VARIANT_NAME_4
#endif

typedef struct tagVARIANT VARIANT;

struct tagVARIANT {
    union {
        struct __tagVARIANT {
            VARTYPE vt;
            WORD wReserved1;
            WORD wReserved2;
            WORD wReserved3;
            union {
                LONGLONG llVal;
                LONG lVal;
                BYTE bVal;
                SHORT iVal;
                FLOAT fltVal;
                DOUBLE dblVal;
                VARIANT_BOOL boolVal;
                SCODE scode;
                CY cyVal;
                DATE date;
                BSTR bstrVal;
                IUnknown *punkVal;
                IDispatch *pdispVal;
                SAFEARRAY *parray;
                BYTE *pbVal;
                SHORT *piVal;
                LONG *plVal;
                LONGLONG *pllVal;
                FLOAT *pfltVal;
                DOUBLE *pdblVal;
                VARIANT_BOOL *pboolVal;
                SCODE *pscode;
                CY *pcyVal;
                DATE *pdate;
                BSTR *pbstrVal;
                IUnknown **ppunkVal;
                IDispatch **ppdispVal;
                SAFEARRAY **pparray;
                VARIANT *pvarVal;
                PVOID byref;
                CHAR cVal;
                USHORT uiVal;
                ULONG ulVal;
                ULONGLONG ullVal;
                INT intVal;
                UINT uintVal;
                DECIMAL *pdecVal;
                CHAR *pcVal;
                USHORT *puiVal;
                ULONG *pulVal;
                ULONGLONG *pullVal;
                INT *pintVal;
                UINT *puintVal;
                struct __tagBRECORD {
                    PVOID pvRecord;
                    IRecordInfo *pRecInfo;
                } __VARIANT_NAME_4;
            } __VARIANT_NAME_3;
        } __VARIANT_NAME_2;
        DECIMAL decVal;
    } __VARIANT_NAME_1;
};


typedef VARIANT *LPVARIANT;
typedef VARIANT VARIANTARG;
typedef VARIANT *LPVARIANTARG;
#if 0
typedef const VARIANT *REFVARIANT;
#else

#ifndef _REFVARIANT_DEFINED
#define _REFVARIANT_DEFINED
#ifdef __cplusplus
#define REFVARIANT const VARIANT &
#else
#define REFVARIANT const VARIANT * __MIDL_CONST
#endif
#endif
#endif

struct _wireBRECORD {
    ULONG fFlags;
    ULONG clSize;
    IRecordInfo *pRecInfo;
    byte *pRecord;
};


struct _wireVARIANT {
    DWORD clSize;
    DWORD rpcReserved;
    USHORT vt;
    USHORT wReserved1;
    USHORT wReserved2;
    USHORT wReserved3;
    __C89_NAMELESS union {
        LONGLONG llVal;
        LONG lVal;
        BYTE bVal;
        SHORT iVal;
        FLOAT fltVal;
        DOUBLE dblVal;
        VARIANT_BOOL boolVal;
        SCODE scode;
        CY cyVal;
        DATE date;
        wireBSTR bstrVal;
        IUnknown *punkVal;
        IDispatch *pdispVal;
        wirePSAFEARRAY parray;
        wireBRECORD brecVal;
        BYTE *pbVal;
        SHORT *piVal;
        LONG *plVal;
        LONGLONG *pllVal;
        FLOAT *pfltVal;
        DOUBLE *pdblVal;
        VARIANT_BOOL *pboolVal;
        SCODE *pscode;
        CY *pcyVal;
        DATE *pdate;
        wireBSTR *pbstrVal;
        IUnknown **ppunkVal;
        IDispatch **ppdispVal;
        wirePSAFEARRAY *pparray;
        wireVARIANT *pvarVal;
        CHAR cVal;
        USHORT uiVal;
        ULONG ulVal;
        ULONGLONG ullVal;
        INT intVal;
        UINT uintVal;
        DECIMAL decVal;
        DECIMAL *pdecVal;
        CHAR *pcVal;
        USHORT *puiVal;
        ULONG *pulVal;
        ULONGLONG *pullVal;
        INT *pintVal;
        UINT *puintVal;
    } __C89_NAMELESSUNIONNAME;
};


typedef LONG DISPID;
typedef DISPID MEMBERID;
typedef DWORD HREFTYPE;
typedef enum tagTYPEKIND {
    TKIND_ENUM = 0,
    TKIND_RECORD = 1,
    TKIND_MODULE = 2,
    TKIND_INTERFACE = 3,
    TKIND_DISPATCH = 4,
    TKIND_COCLASS = 5,
    TKIND_ALIAS = 6,
    TKIND_UNION = 7,
    TKIND_MAX = 8
} TYPEKIND;

typedef struct tagTYPEDESC {
    __C89_NAMELESS union {
        struct tagTYPEDESC *lptdesc;
        struct tagARRAYDESC *lpadesc;
        HREFTYPE hreftype;
    } __C89_NAMELESSUNIONNAME;
    VARTYPE vt;
} TYPEDESC;

typedef struct tagARRAYDESC {
    TYPEDESC tdescElem;
    USHORT cDims;
    SAFEARRAYBOUND rgbounds[1];
} ARRAYDESC;

typedef struct tagPARAMDESCEX {
    ULONG cBytes;
    VARIANTARG varDefaultValue;
} PARAMDESCEX;
typedef struct tagPARAMDESCEX *LPPARAMDESCEX;

typedef struct tagPARAMDESC {
    LPPARAMDESCEX pparamdescex;
    USHORT wParamFlags;
} PARAMDESC;
typedef struct tagPARAMDESC *LPPARAMDESC;

#define PARAMFLAG_NONE (0x0)

#define PARAMFLAG_FIN (0x1)

#define PARAMFLAG_FOUT (0x2)

#define PARAMFLAG_FLCID (0x4)

#define PARAMFLAG_FRETVAL (0x8)

#define PARAMFLAG_FOPT (0x10)

#define PARAMFLAG_FHASDEFAULT (0x20)

#define PARAMFLAG_FHASCUSTDATA (0x40)


typedef struct tagIDLDESC {
    ULONG_PTR dwReserved;
    USHORT wIDLFlags;
} IDLDESC;
typedef struct tagIDLDESC *LPIDLDESC;

#define IDLFLAG_NONE (PARAMFLAG_NONE)

#define IDLFLAG_FIN (PARAMFLAG_FIN)

#define IDLFLAG_FOUT (PARAMFLAG_FOUT)

#define IDLFLAG_FLCID (PARAMFLAG_FLCID)

#define IDLFLAG_FRETVAL (PARAMFLAG_FRETVAL)


#if 0
typedef struct tagELEMDESC {
    TYPEDESC tdesc;
    PARAMDESC paramdesc;
} ELEMDESC;
#else

  typedef struct tagELEMDESC {
    TYPEDESC tdesc;
    __C89_NAMELESS union {
      IDLDESC idldesc;
      PARAMDESC paramdesc;
    } DUMMYUNIONNAME;
  } ELEMDESC,*LPELEMDESC;
#endif

typedef struct tagTYPEATTR {
    GUID guid;
    LCID lcid;
    DWORD dwReserved;
    MEMBERID memidConstructor;
    MEMBERID memidDestructor;
    LPOLESTR lpstrSchema;
    ULONG cbSizeInstance;
    TYPEKIND typekind;
    WORD cFuncs;
    WORD cVars;
    WORD cImplTypes;
    WORD cbSizeVft;
    WORD cbAlignment;
    WORD wTypeFlags;
    WORD wMajorVerNum;
    WORD wMinorVerNum;
    TYPEDESC tdescAlias;
    IDLDESC idldescType;
} TYPEATTR;
typedef struct tagTYPEATTR *LPTYPEATTR;

typedef struct tagDISPPARAMS {
    VARIANTARG *rgvarg;
    DISPID *rgdispidNamedArgs;
    UINT cArgs;
    UINT cNamedArgs;
} DISPPARAMS;

#if 0
typedef struct tagEXCEPINFO {
    WORD wCode;
    WORD wReserved;
    BSTR bstrSource;
    BSTR bstrDescription;
    BSTR bstrHelpFile;
    DWORD dwHelpContext;
    ULONG_PTR pvReserved;
    ULONG_PTR pfnDeferredFillIn;
    SCODE scode;
} EXCEPINFO;
#else
  typedef struct tagEXCEPINFO {
    WORD wCode;
    WORD wReserved;
    BSTR bstrSource;
    BSTR bstrDescription;
    BSTR bstrHelpFile;
    DWORD dwHelpContext;
    PVOID pvReserved;
    HRESULT (__stdcall *pfnDeferredFillIn)(struct tagEXCEPINFO *);
    SCODE scode;
  } EXCEPINFO, *LPEXCEPINFO;
#endif

typedef enum tagCALLCONV {
    CC_FASTCALL = 0,
    CC_CDECL = 1,
    CC_MSCPASCAL = 2,
    CC_PASCAL = CC_MSCPASCAL,
    CC_MACPASCAL = 3,
    CC_STDCALL = 4,
    CC_FPFASTCALL = 5,
    CC_SYSCALL = 6,
    CC_MPWCDECL = 7,
    CC_MPWPASCAL = 8,
    CC_MAX = 9
} CALLCONV;

typedef enum tagFUNCKIND {
    FUNC_VIRTUAL = 0,
    FUNC_PUREVIRTUAL = 1,
    FUNC_NONVIRTUAL = 2,
    FUNC_STATIC = 3,
    FUNC_DISPATCH = 4
} FUNCKIND;

typedef enum tagINVOKEKIND {
    INVOKE_FUNC = 1,
    INVOKE_PROPERTYGET = 2,
    INVOKE_PROPERTYPUT = 4,
    INVOKE_PROPERTYPUTREF = 8
} INVOKEKIND;

typedef struct tagFUNCDESC {
    MEMBERID memid;
    SCODE *lprgscode;
    ELEMDESC *lprgelemdescParam;
    FUNCKIND funckind;
    INVOKEKIND invkind;
    CALLCONV callconv;
    SHORT cParams;
    SHORT cParamsOpt;
    SHORT oVft;
    SHORT cScodes;
    ELEMDESC elemdescFunc;
    WORD wFuncFlags;
} FUNCDESC;
typedef struct tagFUNCDESC *LPFUNCDESC;

typedef enum tagVARKIND {
    VAR_PERINSTANCE = 0,
    VAR_STATIC = 1,
    VAR_CONST = 2,
    VAR_DISPATCH = 3
} VARKIND;

#define IMPLTYPEFLAG_FDEFAULT (0x1)

#define IMPLTYPEFLAG_FSOURCE (0x2)

#define IMPLTYPEFLAG_FRESTRICTED (0x4)

#define IMPLTYPEFLAG_FDEFAULTVTABLE (0x8)


typedef struct tagVARDESC {
    MEMBERID memid;
    LPOLESTR lpstrSchema;
    __C89_NAMELESS union {
        ULONG oInst;
        VARIANT *lpvarValue;
    } __C89_NAMELESSUNIONNAME;
    ELEMDESC elemdescVar;
    WORD wVarFlags;
    VARKIND varkind;
} VARDESC;
typedef struct tagVARDESC *LPVARDESC;

typedef enum tagTYPEFLAGS {
    TYPEFLAG_FAPPOBJECT = 0x1,
    TYPEFLAG_FCANCREATE = 0x2,
    TYPEFLAG_FLICENSED = 0x4,
    TYPEFLAG_FPREDECLID = 0x8,
    TYPEFLAG_FHIDDEN = 0x10,
    TYPEFLAG_FCONTROL = 0x20,
    TYPEFLAG_FDUAL = 0x40,
    TYPEFLAG_FNONEXTENSIBLE = 0x80,
    TYPEFLAG_FOLEAUTOMATION = 0x100,
    TYPEFLAG_FRESTRICTED = 0x200,
    TYPEFLAG_FAGGREGATABLE = 0x400,
    TYPEFLAG_FREPLACEABLE = 0x800,
    TYPEFLAG_FDISPATCHABLE = 0x1000,
    TYPEFLAG_FREVERSEBIND = 0x2000,
    TYPEFLAG_FPROXY = 0x4000
} TYPEFLAGS;

typedef enum tagFUNCFLAGS {
    FUNCFLAG_FRESTRICTED = 0x1,
    FUNCFLAG_FSOURCE = 0x2,
    FUNCFLAG_FBINDABLE = 0x4,
    FUNCFLAG_FREQUESTEDIT = 0x8,
    FUNCFLAG_FDISPLAYBIND = 0x10,
    FUNCFLAG_FDEFAULTBIND = 0x20,
    FUNCFLAG_FHIDDEN = 0x40,
    FUNCFLAG_FUSESGETLASTERROR = 0x80,
    FUNCFLAG_FDEFAULTCOLLELEM = 0x100,
    FUNCFLAG_FUIDEFAULT = 0x200,
    FUNCFLAG_FNONBROWSABLE = 0x400,
    FUNCFLAG_FREPLACEABLE = 0x800,
    FUNCFLAG_FIMMEDIATEBIND = 0x1000
} FUNCFLAGS;

typedef enum tagVARFLAGS {
    VARFLAG_FREADONLY = 0x1,
    VARFLAG_FSOURCE = 0x2,
    VARFLAG_FBINDABLE = 0x4,
    VARFLAG_FREQUESTEDIT = 0x8,
    VARFLAG_FDISPLAYBIND = 0x10,
    VARFLAG_FDEFAULTBIND = 0x20,
    VARFLAG_FHIDDEN = 0x40,
    VARFLAG_FRESTRICTED = 0x80,
    VARFLAG_FDEFAULTCOLLELEM = 0x100,
    VARFLAG_FUIDEFAULT = 0x200,
    VARFLAG_FNONBROWSABLE = 0x400,
    VARFLAG_FREPLACEABLE = 0x800,
    VARFLAG_FIMMEDIATEBIND = 0x1000
} VARFLAGS;

typedef struct tagCLEANLOCALSTORAGE {
    IUnknown *pInterface;
    PVOID pStorage;
    DWORD flags;
} CLEANLOCALSTORAGE;

typedef struct tagCUSTDATAITEM {
    GUID guid;
    VARIANTARG varValue;
} CUSTDATAITEM;
typedef struct tagCUSTDATAITEM *LPCUSTDATAITEM;

typedef struct tagCUSTDATA {
    DWORD cCustData;
    LPCUSTDATAITEM prgCustData;
} CUSTDATA;
typedef struct tagCUSTDATA *LPCUSTDATA;

#endif  /* __IOleAutomationTypes_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * ICreateTypeInfo interface
 */
#ifndef __ICreateTypeInfo_INTERFACE_DEFINED__
#define __ICreateTypeInfo_INTERFACE_DEFINED__

typedef ICreateTypeInfo *LPCREATETYPEINFO;

DEFINE_GUID(IID_ICreateTypeInfo, 0x00020405, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020405-0000-0000-c000-000000000046")
ICreateTypeInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetGuid(
        REFGUID guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTypeFlags(
        UINT uTypeFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDocString(
        LPOLESTR pStrDoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHelpContext(
        DWORD dwHelpContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVersion(
        WORD wMajorVerNum,
        WORD wMinorVerNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddRefTypeInfo(
        ITypeInfo *pTInfo,
        HREFTYPE *phRefType) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddFuncDesc(
        UINT index,
        FUNCDESC *pFuncDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddImplType(
        UINT index,
        HREFTYPE hRefType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetImplTypeFlags(
        UINT index,
        INT implTypeFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAlignment(
        WORD cbAlignment) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSchema(
        LPOLESTR pStrSchema) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddVarDesc(
        UINT index,
        VARDESC *pVarDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFuncAndParamNames(
        UINT index,
        LPOLESTR *rgszNames,
        UINT cNames) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVarName(
        UINT index,
        LPOLESTR szName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTypeDescAlias(
        TYPEDESC *pTDescAlias) = 0;

    virtual HRESULT STDMETHODCALLTYPE DefineFuncAsDllEntry(
        UINT index,
        LPOLESTR szDllName,
        LPOLESTR szProcName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFuncDocString(
        UINT index,
        LPOLESTR szDocString) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVarDocString(
        UINT index,
        LPOLESTR szDocString) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFuncHelpContext(
        UINT index,
        DWORD dwHelpContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVarHelpContext(
        UINT index,
        DWORD dwHelpContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMops(
        UINT index,
        BSTR bstrMops) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTypeIdldesc(
        IDLDESC *pIdlDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE LayOut(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICreateTypeInfo, 0x00020405, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ICreateTypeInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICreateTypeInfo* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICreateTypeInfo* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICreateTypeInfo* This);

    /*** ICreateTypeInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *SetGuid)(
        ICreateTypeInfo* This,
        REFGUID guid);

    HRESULT (STDMETHODCALLTYPE *SetTypeFlags)(
        ICreateTypeInfo* This,
        UINT uTypeFlags);

    HRESULT (STDMETHODCALLTYPE *SetDocString)(
        ICreateTypeInfo* This,
        LPOLESTR pStrDoc);

    HRESULT (STDMETHODCALLTYPE *SetHelpContext)(
        ICreateTypeInfo* This,
        DWORD dwHelpContext);

    HRESULT (STDMETHODCALLTYPE *SetVersion)(
        ICreateTypeInfo* This,
        WORD wMajorVerNum,
        WORD wMinorVerNum);

    HRESULT (STDMETHODCALLTYPE *AddRefTypeInfo)(
        ICreateTypeInfo* This,
        ITypeInfo *pTInfo,
        HREFTYPE *phRefType);

    HRESULT (STDMETHODCALLTYPE *AddFuncDesc)(
        ICreateTypeInfo* This,
        UINT index,
        FUNCDESC *pFuncDesc);

    HRESULT (STDMETHODCALLTYPE *AddImplType)(
        ICreateTypeInfo* This,
        UINT index,
        HREFTYPE hRefType);

    HRESULT (STDMETHODCALLTYPE *SetImplTypeFlags)(
        ICreateTypeInfo* This,
        UINT index,
        INT implTypeFlags);

    HRESULT (STDMETHODCALLTYPE *SetAlignment)(
        ICreateTypeInfo* This,
        WORD cbAlignment);

    HRESULT (STDMETHODCALLTYPE *SetSchema)(
        ICreateTypeInfo* This,
        LPOLESTR pStrSchema);

    HRESULT (STDMETHODCALLTYPE *AddVarDesc)(
        ICreateTypeInfo* This,
        UINT index,
        VARDESC *pVarDesc);

    HRESULT (STDMETHODCALLTYPE *SetFuncAndParamNames)(
        ICreateTypeInfo* This,
        UINT index,
        LPOLESTR *rgszNames,
        UINT cNames);

    HRESULT (STDMETHODCALLTYPE *SetVarName)(
        ICreateTypeInfo* This,
        UINT index,
        LPOLESTR szName);

    HRESULT (STDMETHODCALLTYPE *SetTypeDescAlias)(
        ICreateTypeInfo* This,
        TYPEDESC *pTDescAlias);

    HRESULT (STDMETHODCALLTYPE *DefineFuncAsDllEntry)(
        ICreateTypeInfo* This,
        UINT index,
        LPOLESTR szDllName,
        LPOLESTR szProcName);

    HRESULT (STDMETHODCALLTYPE *SetFuncDocString)(
        ICreateTypeInfo* This,
        UINT index,
        LPOLESTR szDocString);

    HRESULT (STDMETHODCALLTYPE *SetVarDocString)(
        ICreateTypeInfo* This,
        UINT index,
        LPOLESTR szDocString);

    HRESULT (STDMETHODCALLTYPE *SetFuncHelpContext)(
        ICreateTypeInfo* This,
        UINT index,
        DWORD dwHelpContext);

    HRESULT (STDMETHODCALLTYPE *SetVarHelpContext)(
        ICreateTypeInfo* This,
        UINT index,
        DWORD dwHelpContext);

    HRESULT (STDMETHODCALLTYPE *SetMops)(
        ICreateTypeInfo* This,
        UINT index,
        BSTR bstrMops);

    HRESULT (STDMETHODCALLTYPE *SetTypeIdldesc)(
        ICreateTypeInfo* This,
        IDLDESC *pIdlDesc);

    HRESULT (STDMETHODCALLTYPE *LayOut)(
        ICreateTypeInfo* This);

    END_INTERFACE
} ICreateTypeInfoVtbl;
interface ICreateTypeInfo {
    CONST_VTBL ICreateTypeInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICreateTypeInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICreateTypeInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICreateTypeInfo_Release(This) (This)->lpVtbl->Release(This)
/*** ICreateTypeInfo methods ***/
#define ICreateTypeInfo_SetGuid(This,guid) (This)->lpVtbl->SetGuid(This,guid)
#define ICreateTypeInfo_SetTypeFlags(This,uTypeFlags) (This)->lpVtbl->SetTypeFlags(This,uTypeFlags)
#define ICreateTypeInfo_SetDocString(This,pStrDoc) (This)->lpVtbl->SetDocString(This,pStrDoc)
#define ICreateTypeInfo_SetHelpContext(This,dwHelpContext) (This)->lpVtbl->SetHelpContext(This,dwHelpContext)
#define ICreateTypeInfo_SetVersion(This,wMajorVerNum,wMinorVerNum) (This)->lpVtbl->SetVersion(This,wMajorVerNum,wMinorVerNum)
#define ICreateTypeInfo_AddRefTypeInfo(This,pTInfo,phRefType) (This)->lpVtbl->AddRefTypeInfo(This,pTInfo,phRefType)
#define ICreateTypeInfo_AddFuncDesc(This,index,pFuncDesc) (This)->lpVtbl->AddFuncDesc(This,index,pFuncDesc)
#define ICreateTypeInfo_AddImplType(This,index,hRefType) (This)->lpVtbl->AddImplType(This,index,hRefType)
#define ICreateTypeInfo_SetImplTypeFlags(This,index,implTypeFlags) (This)->lpVtbl->SetImplTypeFlags(This,index,implTypeFlags)
#define ICreateTypeInfo_SetAlignment(This,cbAlignment) (This)->lpVtbl->SetAlignment(This,cbAlignment)
#define ICreateTypeInfo_SetSchema(This,pStrSchema) (This)->lpVtbl->SetSchema(This,pStrSchema)
#define ICreateTypeInfo_AddVarDesc(This,index,pVarDesc) (This)->lpVtbl->AddVarDesc(This,index,pVarDesc)
#define ICreateTypeInfo_SetFuncAndParamNames(This,index,rgszNames,cNames) (This)->lpVtbl->SetFuncAndParamNames(This,index,rgszNames,cNames)
#define ICreateTypeInfo_SetVarName(This,index,szName) (This)->lpVtbl->SetVarName(This,index,szName)
#define ICreateTypeInfo_SetTypeDescAlias(This,pTDescAlias) (This)->lpVtbl->SetTypeDescAlias(This,pTDescAlias)
#define ICreateTypeInfo_DefineFuncAsDllEntry(This,index,szDllName,szProcName) (This)->lpVtbl->DefineFuncAsDllEntry(This,index,szDllName,szProcName)
#define ICreateTypeInfo_SetFuncDocString(This,index,szDocString) (This)->lpVtbl->SetFuncDocString(This,index,szDocString)
#define ICreateTypeInfo_SetVarDocString(This,index,szDocString) (This)->lpVtbl->SetVarDocString(This,index,szDocString)
#define ICreateTypeInfo_SetFuncHelpContext(This,index,dwHelpContext) (This)->lpVtbl->SetFuncHelpContext(This,index,dwHelpContext)
#define ICreateTypeInfo_SetVarHelpContext(This,index,dwHelpContext) (This)->lpVtbl->SetVarHelpContext(This,index,dwHelpContext)
#define ICreateTypeInfo_SetMops(This,index,bstrMops) (This)->lpVtbl->SetMops(This,index,bstrMops)
#define ICreateTypeInfo_SetTypeIdldesc(This,pIdlDesc) (This)->lpVtbl->SetTypeIdldesc(This,pIdlDesc)
#define ICreateTypeInfo_LayOut(This) (This)->lpVtbl->LayOut(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICreateTypeInfo_QueryInterface(ICreateTypeInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICreateTypeInfo_AddRef(ICreateTypeInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICreateTypeInfo_Release(ICreateTypeInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** ICreateTypeInfo methods ***/
static FORCEINLINE HRESULT ICreateTypeInfo_SetGuid(ICreateTypeInfo* This,REFGUID guid) {
    return This->lpVtbl->SetGuid(This,guid);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetTypeFlags(ICreateTypeInfo* This,UINT uTypeFlags) {
    return This->lpVtbl->SetTypeFlags(This,uTypeFlags);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetDocString(ICreateTypeInfo* This,LPOLESTR pStrDoc) {
    return This->lpVtbl->SetDocString(This,pStrDoc);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetHelpContext(ICreateTypeInfo* This,DWORD dwHelpContext) {
    return This->lpVtbl->SetHelpContext(This,dwHelpContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetVersion(ICreateTypeInfo* This,WORD wMajorVerNum,WORD wMinorVerNum) {
    return This->lpVtbl->SetVersion(This,wMajorVerNum,wMinorVerNum);
}
static FORCEINLINE HRESULT ICreateTypeInfo_AddRefTypeInfo(ICreateTypeInfo* This,ITypeInfo *pTInfo,HREFTYPE *phRefType) {
    return This->lpVtbl->AddRefTypeInfo(This,pTInfo,phRefType);
}
static FORCEINLINE HRESULT ICreateTypeInfo_AddFuncDesc(ICreateTypeInfo* This,UINT index,FUNCDESC *pFuncDesc) {
    return This->lpVtbl->AddFuncDesc(This,index,pFuncDesc);
}
static FORCEINLINE HRESULT ICreateTypeInfo_AddImplType(ICreateTypeInfo* This,UINT index,HREFTYPE hRefType) {
    return This->lpVtbl->AddImplType(This,index,hRefType);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetImplTypeFlags(ICreateTypeInfo* This,UINT index,INT implTypeFlags) {
    return This->lpVtbl->SetImplTypeFlags(This,index,implTypeFlags);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetAlignment(ICreateTypeInfo* This,WORD cbAlignment) {
    return This->lpVtbl->SetAlignment(This,cbAlignment);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetSchema(ICreateTypeInfo* This,LPOLESTR pStrSchema) {
    return This->lpVtbl->SetSchema(This,pStrSchema);
}
static FORCEINLINE HRESULT ICreateTypeInfo_AddVarDesc(ICreateTypeInfo* This,UINT index,VARDESC *pVarDesc) {
    return This->lpVtbl->AddVarDesc(This,index,pVarDesc);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetFuncAndParamNames(ICreateTypeInfo* This,UINT index,LPOLESTR *rgszNames,UINT cNames) {
    return This->lpVtbl->SetFuncAndParamNames(This,index,rgszNames,cNames);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetVarName(ICreateTypeInfo* This,UINT index,LPOLESTR szName) {
    return This->lpVtbl->SetVarName(This,index,szName);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetTypeDescAlias(ICreateTypeInfo* This,TYPEDESC *pTDescAlias) {
    return This->lpVtbl->SetTypeDescAlias(This,pTDescAlias);
}
static FORCEINLINE HRESULT ICreateTypeInfo_DefineFuncAsDllEntry(ICreateTypeInfo* This,UINT index,LPOLESTR szDllName,LPOLESTR szProcName) {
    return This->lpVtbl->DefineFuncAsDllEntry(This,index,szDllName,szProcName);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetFuncDocString(ICreateTypeInfo* This,UINT index,LPOLESTR szDocString) {
    return This->lpVtbl->SetFuncDocString(This,index,szDocString);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetVarDocString(ICreateTypeInfo* This,UINT index,LPOLESTR szDocString) {
    return This->lpVtbl->SetVarDocString(This,index,szDocString);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetFuncHelpContext(ICreateTypeInfo* This,UINT index,DWORD dwHelpContext) {
    return This->lpVtbl->SetFuncHelpContext(This,index,dwHelpContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetVarHelpContext(ICreateTypeInfo* This,UINT index,DWORD dwHelpContext) {
    return This->lpVtbl->SetVarHelpContext(This,index,dwHelpContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetMops(ICreateTypeInfo* This,UINT index,BSTR bstrMops) {
    return This->lpVtbl->SetMops(This,index,bstrMops);
}
static FORCEINLINE HRESULT ICreateTypeInfo_SetTypeIdldesc(ICreateTypeInfo* This,IDLDESC *pIdlDesc) {
    return This->lpVtbl->SetTypeIdldesc(This,pIdlDesc);
}
static FORCEINLINE HRESULT ICreateTypeInfo_LayOut(ICreateTypeInfo* This) {
    return This->lpVtbl->LayOut(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetGuid_Proxy(
    ICreateTypeInfo* This,
    REFGUID guid);
void __RPC_STUB ICreateTypeInfo_SetGuid_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetTypeFlags_Proxy(
    ICreateTypeInfo* This,
    UINT uTypeFlags);
void __RPC_STUB ICreateTypeInfo_SetTypeFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetDocString_Proxy(
    ICreateTypeInfo* This,
    LPOLESTR pStrDoc);
void __RPC_STUB ICreateTypeInfo_SetDocString_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetHelpContext_Proxy(
    ICreateTypeInfo* This,
    DWORD dwHelpContext);
void __RPC_STUB ICreateTypeInfo_SetHelpContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetVersion_Proxy(
    ICreateTypeInfo* This,
    WORD wMajorVerNum,
    WORD wMinorVerNum);
void __RPC_STUB ICreateTypeInfo_SetVersion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_AddRefTypeInfo_Proxy(
    ICreateTypeInfo* This,
    ITypeInfo *pTInfo,
    HREFTYPE *phRefType);
void __RPC_STUB ICreateTypeInfo_AddRefTypeInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_AddFuncDesc_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    FUNCDESC *pFuncDesc);
void __RPC_STUB ICreateTypeInfo_AddFuncDesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_AddImplType_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    HREFTYPE hRefType);
void __RPC_STUB ICreateTypeInfo_AddImplType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetImplTypeFlags_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    INT implTypeFlags);
void __RPC_STUB ICreateTypeInfo_SetImplTypeFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetAlignment_Proxy(
    ICreateTypeInfo* This,
    WORD cbAlignment);
void __RPC_STUB ICreateTypeInfo_SetAlignment_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetSchema_Proxy(
    ICreateTypeInfo* This,
    LPOLESTR pStrSchema);
void __RPC_STUB ICreateTypeInfo_SetSchema_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_AddVarDesc_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    VARDESC *pVarDesc);
void __RPC_STUB ICreateTypeInfo_AddVarDesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetFuncAndParamNames_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    LPOLESTR *rgszNames,
    UINT cNames);
void __RPC_STUB ICreateTypeInfo_SetFuncAndParamNames_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetVarName_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    LPOLESTR szName);
void __RPC_STUB ICreateTypeInfo_SetVarName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetTypeDescAlias_Proxy(
    ICreateTypeInfo* This,
    TYPEDESC *pTDescAlias);
void __RPC_STUB ICreateTypeInfo_SetTypeDescAlias_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_DefineFuncAsDllEntry_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    LPOLESTR szDllName,
    LPOLESTR szProcName);
void __RPC_STUB ICreateTypeInfo_DefineFuncAsDllEntry_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetFuncDocString_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    LPOLESTR szDocString);
void __RPC_STUB ICreateTypeInfo_SetFuncDocString_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetVarDocString_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    LPOLESTR szDocString);
void __RPC_STUB ICreateTypeInfo_SetVarDocString_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetFuncHelpContext_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    DWORD dwHelpContext);
void __RPC_STUB ICreateTypeInfo_SetFuncHelpContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetVarHelpContext_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    DWORD dwHelpContext);
void __RPC_STUB ICreateTypeInfo_SetVarHelpContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetMops_Proxy(
    ICreateTypeInfo* This,
    UINT index,
    BSTR bstrMops);
void __RPC_STUB ICreateTypeInfo_SetMops_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_SetTypeIdldesc_Proxy(
    ICreateTypeInfo* This,
    IDLDESC *pIdlDesc);
void __RPC_STUB ICreateTypeInfo_SetTypeIdldesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo_LayOut_Proxy(
    ICreateTypeInfo* This);
void __RPC_STUB ICreateTypeInfo_LayOut_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ICreateTypeInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICreateTypeInfo2 interface
 */
#ifndef __ICreateTypeInfo2_INTERFACE_DEFINED__
#define __ICreateTypeInfo2_INTERFACE_DEFINED__

typedef ICreateTypeInfo2 *LPCREATETYPEINFO2;

DEFINE_GUID(IID_ICreateTypeInfo2, 0x0002040e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0002040e-0000-0000-c000-000000000046")
ICreateTypeInfo2 : public ICreateTypeInfo
{
    virtual HRESULT STDMETHODCALLTYPE DeleteFuncDesc(
        UINT index) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteFuncDescByMemId(
        MEMBERID memid,
        INVOKEKIND invKind) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteVarDesc(
        UINT index) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteVarDescByMemId(
        MEMBERID memid) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteImplType(
        UINT index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCustData(
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFuncCustData(
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetParamCustData(
        UINT indexFunc,
        UINT indexParam,
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVarCustData(
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetImplTypeCustData(
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHelpStringContext(
        ULONG dwHelpStringContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFuncHelpStringContext(
        UINT index,
        ULONG dwHelpStringContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVarHelpStringContext(
        UINT index,
        ULONG dwHelpStringContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE Invalidate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetName(
        LPOLESTR szName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICreateTypeInfo2, 0x0002040e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ICreateTypeInfo2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICreateTypeInfo2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICreateTypeInfo2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICreateTypeInfo2* This);

    /*** ICreateTypeInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *SetGuid)(
        ICreateTypeInfo2* This,
        REFGUID guid);

    HRESULT (STDMETHODCALLTYPE *SetTypeFlags)(
        ICreateTypeInfo2* This,
        UINT uTypeFlags);

    HRESULT (STDMETHODCALLTYPE *SetDocString)(
        ICreateTypeInfo2* This,
        LPOLESTR pStrDoc);

    HRESULT (STDMETHODCALLTYPE *SetHelpContext)(
        ICreateTypeInfo2* This,
        DWORD dwHelpContext);

    HRESULT (STDMETHODCALLTYPE *SetVersion)(
        ICreateTypeInfo2* This,
        WORD wMajorVerNum,
        WORD wMinorVerNum);

    HRESULT (STDMETHODCALLTYPE *AddRefTypeInfo)(
        ICreateTypeInfo2* This,
        ITypeInfo *pTInfo,
        HREFTYPE *phRefType);

    HRESULT (STDMETHODCALLTYPE *AddFuncDesc)(
        ICreateTypeInfo2* This,
        UINT index,
        FUNCDESC *pFuncDesc);

    HRESULT (STDMETHODCALLTYPE *AddImplType)(
        ICreateTypeInfo2* This,
        UINT index,
        HREFTYPE hRefType);

    HRESULT (STDMETHODCALLTYPE *SetImplTypeFlags)(
        ICreateTypeInfo2* This,
        UINT index,
        INT implTypeFlags);

    HRESULT (STDMETHODCALLTYPE *SetAlignment)(
        ICreateTypeInfo2* This,
        WORD cbAlignment);

    HRESULT (STDMETHODCALLTYPE *SetSchema)(
        ICreateTypeInfo2* This,
        LPOLESTR pStrSchema);

    HRESULT (STDMETHODCALLTYPE *AddVarDesc)(
        ICreateTypeInfo2* This,
        UINT index,
        VARDESC *pVarDesc);

    HRESULT (STDMETHODCALLTYPE *SetFuncAndParamNames)(
        ICreateTypeInfo2* This,
        UINT index,
        LPOLESTR *rgszNames,
        UINT cNames);

    HRESULT (STDMETHODCALLTYPE *SetVarName)(
        ICreateTypeInfo2* This,
        UINT index,
        LPOLESTR szName);

    HRESULT (STDMETHODCALLTYPE *SetTypeDescAlias)(
        ICreateTypeInfo2* This,
        TYPEDESC *pTDescAlias);

    HRESULT (STDMETHODCALLTYPE *DefineFuncAsDllEntry)(
        ICreateTypeInfo2* This,
        UINT index,
        LPOLESTR szDllName,
        LPOLESTR szProcName);

    HRESULT (STDMETHODCALLTYPE *SetFuncDocString)(
        ICreateTypeInfo2* This,
        UINT index,
        LPOLESTR szDocString);

    HRESULT (STDMETHODCALLTYPE *SetVarDocString)(
        ICreateTypeInfo2* This,
        UINT index,
        LPOLESTR szDocString);

    HRESULT (STDMETHODCALLTYPE *SetFuncHelpContext)(
        ICreateTypeInfo2* This,
        UINT index,
        DWORD dwHelpContext);

    HRESULT (STDMETHODCALLTYPE *SetVarHelpContext)(
        ICreateTypeInfo2* This,
        UINT index,
        DWORD dwHelpContext);

    HRESULT (STDMETHODCALLTYPE *SetMops)(
        ICreateTypeInfo2* This,
        UINT index,
        BSTR bstrMops);

    HRESULT (STDMETHODCALLTYPE *SetTypeIdldesc)(
        ICreateTypeInfo2* This,
        IDLDESC *pIdlDesc);

    HRESULT (STDMETHODCALLTYPE *LayOut)(
        ICreateTypeInfo2* This);

    /*** ICreateTypeInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *DeleteFuncDesc)(
        ICreateTypeInfo2* This,
        UINT index);

    HRESULT (STDMETHODCALLTYPE *DeleteFuncDescByMemId)(
        ICreateTypeInfo2* This,
        MEMBERID memid,
        INVOKEKIND invKind);

    HRESULT (STDMETHODCALLTYPE *DeleteVarDesc)(
        ICreateTypeInfo2* This,
        UINT index);

    HRESULT (STDMETHODCALLTYPE *DeleteVarDescByMemId)(
        ICreateTypeInfo2* This,
        MEMBERID memid);

    HRESULT (STDMETHODCALLTYPE *DeleteImplType)(
        ICreateTypeInfo2* This,
        UINT index);

    HRESULT (STDMETHODCALLTYPE *SetCustData)(
        ICreateTypeInfo2* This,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *SetFuncCustData)(
        ICreateTypeInfo2* This,
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *SetParamCustData)(
        ICreateTypeInfo2* This,
        UINT indexFunc,
        UINT indexParam,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *SetVarCustData)(
        ICreateTypeInfo2* This,
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *SetImplTypeCustData)(
        ICreateTypeInfo2* This,
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *SetHelpStringContext)(
        ICreateTypeInfo2* This,
        ULONG dwHelpStringContext);

    HRESULT (STDMETHODCALLTYPE *SetFuncHelpStringContext)(
        ICreateTypeInfo2* This,
        UINT index,
        ULONG dwHelpStringContext);

    HRESULT (STDMETHODCALLTYPE *SetVarHelpStringContext)(
        ICreateTypeInfo2* This,
        UINT index,
        ULONG dwHelpStringContext);

    HRESULT (STDMETHODCALLTYPE *Invalidate)(
        ICreateTypeInfo2* This);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ICreateTypeInfo2* This,
        LPOLESTR szName);

    END_INTERFACE
} ICreateTypeInfo2Vtbl;
interface ICreateTypeInfo2 {
    CONST_VTBL ICreateTypeInfo2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICreateTypeInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICreateTypeInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICreateTypeInfo2_Release(This) (This)->lpVtbl->Release(This)
/*** ICreateTypeInfo methods ***/
#define ICreateTypeInfo2_SetGuid(This,guid) (This)->lpVtbl->SetGuid(This,guid)
#define ICreateTypeInfo2_SetTypeFlags(This,uTypeFlags) (This)->lpVtbl->SetTypeFlags(This,uTypeFlags)
#define ICreateTypeInfo2_SetDocString(This,pStrDoc) (This)->lpVtbl->SetDocString(This,pStrDoc)
#define ICreateTypeInfo2_SetHelpContext(This,dwHelpContext) (This)->lpVtbl->SetHelpContext(This,dwHelpContext)
#define ICreateTypeInfo2_SetVersion(This,wMajorVerNum,wMinorVerNum) (This)->lpVtbl->SetVersion(This,wMajorVerNum,wMinorVerNum)
#define ICreateTypeInfo2_AddRefTypeInfo(This,pTInfo,phRefType) (This)->lpVtbl->AddRefTypeInfo(This,pTInfo,phRefType)
#define ICreateTypeInfo2_AddFuncDesc(This,index,pFuncDesc) (This)->lpVtbl->AddFuncDesc(This,index,pFuncDesc)
#define ICreateTypeInfo2_AddImplType(This,index,hRefType) (This)->lpVtbl->AddImplType(This,index,hRefType)
#define ICreateTypeInfo2_SetImplTypeFlags(This,index,implTypeFlags) (This)->lpVtbl->SetImplTypeFlags(This,index,implTypeFlags)
#define ICreateTypeInfo2_SetAlignment(This,cbAlignment) (This)->lpVtbl->SetAlignment(This,cbAlignment)
#define ICreateTypeInfo2_SetSchema(This,pStrSchema) (This)->lpVtbl->SetSchema(This,pStrSchema)
#define ICreateTypeInfo2_AddVarDesc(This,index,pVarDesc) (This)->lpVtbl->AddVarDesc(This,index,pVarDesc)
#define ICreateTypeInfo2_SetFuncAndParamNames(This,index,rgszNames,cNames) (This)->lpVtbl->SetFuncAndParamNames(This,index,rgszNames,cNames)
#define ICreateTypeInfo2_SetVarName(This,index,szName) (This)->lpVtbl->SetVarName(This,index,szName)
#define ICreateTypeInfo2_SetTypeDescAlias(This,pTDescAlias) (This)->lpVtbl->SetTypeDescAlias(This,pTDescAlias)
#define ICreateTypeInfo2_DefineFuncAsDllEntry(This,index,szDllName,szProcName) (This)->lpVtbl->DefineFuncAsDllEntry(This,index,szDllName,szProcName)
#define ICreateTypeInfo2_SetFuncDocString(This,index,szDocString) (This)->lpVtbl->SetFuncDocString(This,index,szDocString)
#define ICreateTypeInfo2_SetVarDocString(This,index,szDocString) (This)->lpVtbl->SetVarDocString(This,index,szDocString)
#define ICreateTypeInfo2_SetFuncHelpContext(This,index,dwHelpContext) (This)->lpVtbl->SetFuncHelpContext(This,index,dwHelpContext)
#define ICreateTypeInfo2_SetVarHelpContext(This,index,dwHelpContext) (This)->lpVtbl->SetVarHelpContext(This,index,dwHelpContext)
#define ICreateTypeInfo2_SetMops(This,index,bstrMops) (This)->lpVtbl->SetMops(This,index,bstrMops)
#define ICreateTypeInfo2_SetTypeIdldesc(This,pIdlDesc) (This)->lpVtbl->SetTypeIdldesc(This,pIdlDesc)
#define ICreateTypeInfo2_LayOut(This) (This)->lpVtbl->LayOut(This)
/*** ICreateTypeInfo2 methods ***/
#define ICreateTypeInfo2_DeleteFuncDesc(This,index) (This)->lpVtbl->DeleteFuncDesc(This,index)
#define ICreateTypeInfo2_DeleteFuncDescByMemId(This,memid,invKind) (This)->lpVtbl->DeleteFuncDescByMemId(This,memid,invKind)
#define ICreateTypeInfo2_DeleteVarDesc(This,index) (This)->lpVtbl->DeleteVarDesc(This,index)
#define ICreateTypeInfo2_DeleteVarDescByMemId(This,memid) (This)->lpVtbl->DeleteVarDescByMemId(This,memid)
#define ICreateTypeInfo2_DeleteImplType(This,index) (This)->lpVtbl->DeleteImplType(This,index)
#define ICreateTypeInfo2_SetCustData(This,guid,pVarVal) (This)->lpVtbl->SetCustData(This,guid,pVarVal)
#define ICreateTypeInfo2_SetFuncCustData(This,index,guid,pVarVal) (This)->lpVtbl->SetFuncCustData(This,index,guid,pVarVal)
#define ICreateTypeInfo2_SetParamCustData(This,indexFunc,indexParam,guid,pVarVal) (This)->lpVtbl->SetParamCustData(This,indexFunc,indexParam,guid,pVarVal)
#define ICreateTypeInfo2_SetVarCustData(This,index,guid,pVarVal) (This)->lpVtbl->SetVarCustData(This,index,guid,pVarVal)
#define ICreateTypeInfo2_SetImplTypeCustData(This,index,guid,pVarVal) (This)->lpVtbl->SetImplTypeCustData(This,index,guid,pVarVal)
#define ICreateTypeInfo2_SetHelpStringContext(This,dwHelpStringContext) (This)->lpVtbl->SetHelpStringContext(This,dwHelpStringContext)
#define ICreateTypeInfo2_SetFuncHelpStringContext(This,index,dwHelpStringContext) (This)->lpVtbl->SetFuncHelpStringContext(This,index,dwHelpStringContext)
#define ICreateTypeInfo2_SetVarHelpStringContext(This,index,dwHelpStringContext) (This)->lpVtbl->SetVarHelpStringContext(This,index,dwHelpStringContext)
#define ICreateTypeInfo2_Invalidate(This) (This)->lpVtbl->Invalidate(This)
#define ICreateTypeInfo2_SetName(This,szName) (This)->lpVtbl->SetName(This,szName)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICreateTypeInfo2_QueryInterface(ICreateTypeInfo2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICreateTypeInfo2_AddRef(ICreateTypeInfo2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICreateTypeInfo2_Release(ICreateTypeInfo2* This) {
    return This->lpVtbl->Release(This);
}
/*** ICreateTypeInfo methods ***/
static FORCEINLINE HRESULT ICreateTypeInfo2_SetGuid(ICreateTypeInfo2* This,REFGUID guid) {
    return This->lpVtbl->SetGuid(This,guid);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetTypeFlags(ICreateTypeInfo2* This,UINT uTypeFlags) {
    return This->lpVtbl->SetTypeFlags(This,uTypeFlags);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetDocString(ICreateTypeInfo2* This,LPOLESTR pStrDoc) {
    return This->lpVtbl->SetDocString(This,pStrDoc);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetHelpContext(ICreateTypeInfo2* This,DWORD dwHelpContext) {
    return This->lpVtbl->SetHelpContext(This,dwHelpContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetVersion(ICreateTypeInfo2* This,WORD wMajorVerNum,WORD wMinorVerNum) {
    return This->lpVtbl->SetVersion(This,wMajorVerNum,wMinorVerNum);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_AddRefTypeInfo(ICreateTypeInfo2* This,ITypeInfo *pTInfo,HREFTYPE *phRefType) {
    return This->lpVtbl->AddRefTypeInfo(This,pTInfo,phRefType);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_AddFuncDesc(ICreateTypeInfo2* This,UINT index,FUNCDESC *pFuncDesc) {
    return This->lpVtbl->AddFuncDesc(This,index,pFuncDesc);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_AddImplType(ICreateTypeInfo2* This,UINT index,HREFTYPE hRefType) {
    return This->lpVtbl->AddImplType(This,index,hRefType);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetImplTypeFlags(ICreateTypeInfo2* This,UINT index,INT implTypeFlags) {
    return This->lpVtbl->SetImplTypeFlags(This,index,implTypeFlags);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetAlignment(ICreateTypeInfo2* This,WORD cbAlignment) {
    return This->lpVtbl->SetAlignment(This,cbAlignment);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetSchema(ICreateTypeInfo2* This,LPOLESTR pStrSchema) {
    return This->lpVtbl->SetSchema(This,pStrSchema);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_AddVarDesc(ICreateTypeInfo2* This,UINT index,VARDESC *pVarDesc) {
    return This->lpVtbl->AddVarDesc(This,index,pVarDesc);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetFuncAndParamNames(ICreateTypeInfo2* This,UINT index,LPOLESTR *rgszNames,UINT cNames) {
    return This->lpVtbl->SetFuncAndParamNames(This,index,rgszNames,cNames);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetVarName(ICreateTypeInfo2* This,UINT index,LPOLESTR szName) {
    return This->lpVtbl->SetVarName(This,index,szName);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetTypeDescAlias(ICreateTypeInfo2* This,TYPEDESC *pTDescAlias) {
    return This->lpVtbl->SetTypeDescAlias(This,pTDescAlias);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_DefineFuncAsDllEntry(ICreateTypeInfo2* This,UINT index,LPOLESTR szDllName,LPOLESTR szProcName) {
    return This->lpVtbl->DefineFuncAsDllEntry(This,index,szDllName,szProcName);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetFuncDocString(ICreateTypeInfo2* This,UINT index,LPOLESTR szDocString) {
    return This->lpVtbl->SetFuncDocString(This,index,szDocString);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetVarDocString(ICreateTypeInfo2* This,UINT index,LPOLESTR szDocString) {
    return This->lpVtbl->SetVarDocString(This,index,szDocString);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetFuncHelpContext(ICreateTypeInfo2* This,UINT index,DWORD dwHelpContext) {
    return This->lpVtbl->SetFuncHelpContext(This,index,dwHelpContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetVarHelpContext(ICreateTypeInfo2* This,UINT index,DWORD dwHelpContext) {
    return This->lpVtbl->SetVarHelpContext(This,index,dwHelpContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetMops(ICreateTypeInfo2* This,UINT index,BSTR bstrMops) {
    return This->lpVtbl->SetMops(This,index,bstrMops);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetTypeIdldesc(ICreateTypeInfo2* This,IDLDESC *pIdlDesc) {
    return This->lpVtbl->SetTypeIdldesc(This,pIdlDesc);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_LayOut(ICreateTypeInfo2* This) {
    return This->lpVtbl->LayOut(This);
}
/*** ICreateTypeInfo2 methods ***/
static FORCEINLINE HRESULT ICreateTypeInfo2_DeleteFuncDesc(ICreateTypeInfo2* This,UINT index) {
    return This->lpVtbl->DeleteFuncDesc(This,index);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_DeleteFuncDescByMemId(ICreateTypeInfo2* This,MEMBERID memid,INVOKEKIND invKind) {
    return This->lpVtbl->DeleteFuncDescByMemId(This,memid,invKind);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_DeleteVarDesc(ICreateTypeInfo2* This,UINT index) {
    return This->lpVtbl->DeleteVarDesc(This,index);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_DeleteVarDescByMemId(ICreateTypeInfo2* This,MEMBERID memid) {
    return This->lpVtbl->DeleteVarDescByMemId(This,memid);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_DeleteImplType(ICreateTypeInfo2* This,UINT index) {
    return This->lpVtbl->DeleteImplType(This,index);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetCustData(ICreateTypeInfo2* This,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->SetCustData(This,guid,pVarVal);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetFuncCustData(ICreateTypeInfo2* This,UINT index,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->SetFuncCustData(This,index,guid,pVarVal);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetParamCustData(ICreateTypeInfo2* This,UINT indexFunc,UINT indexParam,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->SetParamCustData(This,indexFunc,indexParam,guid,pVarVal);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetVarCustData(ICreateTypeInfo2* This,UINT index,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->SetVarCustData(This,index,guid,pVarVal);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetImplTypeCustData(ICreateTypeInfo2* This,UINT index,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->SetImplTypeCustData(This,index,guid,pVarVal);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetHelpStringContext(ICreateTypeInfo2* This,ULONG dwHelpStringContext) {
    return This->lpVtbl->SetHelpStringContext(This,dwHelpStringContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetFuncHelpStringContext(ICreateTypeInfo2* This,UINT index,ULONG dwHelpStringContext) {
    return This->lpVtbl->SetFuncHelpStringContext(This,index,dwHelpStringContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetVarHelpStringContext(ICreateTypeInfo2* This,UINT index,ULONG dwHelpStringContext) {
    return This->lpVtbl->SetVarHelpStringContext(This,index,dwHelpStringContext);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_Invalidate(ICreateTypeInfo2* This) {
    return This->lpVtbl->Invalidate(This);
}
static FORCEINLINE HRESULT ICreateTypeInfo2_SetName(ICreateTypeInfo2* This,LPOLESTR szName) {
    return This->lpVtbl->SetName(This,szName);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_DeleteFuncDesc_Proxy(
    ICreateTypeInfo2* This,
    UINT index);
void __RPC_STUB ICreateTypeInfo2_DeleteFuncDesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_DeleteFuncDescByMemId_Proxy(
    ICreateTypeInfo2* This,
    MEMBERID memid,
    INVOKEKIND invKind);
void __RPC_STUB ICreateTypeInfo2_DeleteFuncDescByMemId_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_DeleteVarDesc_Proxy(
    ICreateTypeInfo2* This,
    UINT index);
void __RPC_STUB ICreateTypeInfo2_DeleteVarDesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_DeleteVarDescByMemId_Proxy(
    ICreateTypeInfo2* This,
    MEMBERID memid);
void __RPC_STUB ICreateTypeInfo2_DeleteVarDescByMemId_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_DeleteImplType_Proxy(
    ICreateTypeInfo2* This,
    UINT index);
void __RPC_STUB ICreateTypeInfo2_DeleteImplType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetCustData_Proxy(
    ICreateTypeInfo2* This,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ICreateTypeInfo2_SetCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetFuncCustData_Proxy(
    ICreateTypeInfo2* This,
    UINT index,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ICreateTypeInfo2_SetFuncCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetParamCustData_Proxy(
    ICreateTypeInfo2* This,
    UINT indexFunc,
    UINT indexParam,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ICreateTypeInfo2_SetParamCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetVarCustData_Proxy(
    ICreateTypeInfo2* This,
    UINT index,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ICreateTypeInfo2_SetVarCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetImplTypeCustData_Proxy(
    ICreateTypeInfo2* This,
    UINT index,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ICreateTypeInfo2_SetImplTypeCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetHelpStringContext_Proxy(
    ICreateTypeInfo2* This,
    ULONG dwHelpStringContext);
void __RPC_STUB ICreateTypeInfo2_SetHelpStringContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetFuncHelpStringContext_Proxy(
    ICreateTypeInfo2* This,
    UINT index,
    ULONG dwHelpStringContext);
void __RPC_STUB ICreateTypeInfo2_SetFuncHelpStringContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetVarHelpStringContext_Proxy(
    ICreateTypeInfo2* This,
    UINT index,
    ULONG dwHelpStringContext);
void __RPC_STUB ICreateTypeInfo2_SetVarHelpStringContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_Invalidate_Proxy(
    ICreateTypeInfo2* This);
void __RPC_STUB ICreateTypeInfo2_Invalidate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeInfo2_SetName_Proxy(
    ICreateTypeInfo2* This,
    LPOLESTR szName);
void __RPC_STUB ICreateTypeInfo2_SetName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ICreateTypeInfo2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICreateTypeLib interface
 */
#ifndef __ICreateTypeLib_INTERFACE_DEFINED__
#define __ICreateTypeLib_INTERFACE_DEFINED__

typedef ICreateTypeLib *LPCREATETYPELIB;

DEFINE_GUID(IID_ICreateTypeLib, 0x00020406, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020406-0000-0000-c000-000000000046")
ICreateTypeLib : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateTypeInfo(
        LPOLESTR szName,
        TYPEKIND tkind,
        ICreateTypeInfo **ppCTInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetName(
        LPOLESTR szName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVersion(
        WORD wMajorVerNum,
        WORD wMinorVerNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGuid(
        REFGUID guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDocString(
        LPOLESTR szDoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHelpFileName(
        LPOLESTR szHelpFileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHelpContext(
        DWORD dwHelpContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLcid(
        LCID lcid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLibFlags(
        UINT uLibFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveAllChanges(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICreateTypeLib, 0x00020406, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ICreateTypeLibVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICreateTypeLib* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICreateTypeLib* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICreateTypeLib* This);

    /*** ICreateTypeLib methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTypeInfo)(
        ICreateTypeLib* This,
        LPOLESTR szName,
        TYPEKIND tkind,
        ICreateTypeInfo **ppCTInfo);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ICreateTypeLib* This,
        LPOLESTR szName);

    HRESULT (STDMETHODCALLTYPE *SetVersion)(
        ICreateTypeLib* This,
        WORD wMajorVerNum,
        WORD wMinorVerNum);

    HRESULT (STDMETHODCALLTYPE *SetGuid)(
        ICreateTypeLib* This,
        REFGUID guid);

    HRESULT (STDMETHODCALLTYPE *SetDocString)(
        ICreateTypeLib* This,
        LPOLESTR szDoc);

    HRESULT (STDMETHODCALLTYPE *SetHelpFileName)(
        ICreateTypeLib* This,
        LPOLESTR szHelpFileName);

    HRESULT (STDMETHODCALLTYPE *SetHelpContext)(
        ICreateTypeLib* This,
        DWORD dwHelpContext);

    HRESULT (STDMETHODCALLTYPE *SetLcid)(
        ICreateTypeLib* This,
        LCID lcid);

    HRESULT (STDMETHODCALLTYPE *SetLibFlags)(
        ICreateTypeLib* This,
        UINT uLibFlags);

    HRESULT (STDMETHODCALLTYPE *SaveAllChanges)(
        ICreateTypeLib* This);

    END_INTERFACE
} ICreateTypeLibVtbl;
interface ICreateTypeLib {
    CONST_VTBL ICreateTypeLibVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICreateTypeLib_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICreateTypeLib_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICreateTypeLib_Release(This) (This)->lpVtbl->Release(This)
/*** ICreateTypeLib methods ***/
#define ICreateTypeLib_CreateTypeInfo(This,szName,tkind,ppCTInfo) (This)->lpVtbl->CreateTypeInfo(This,szName,tkind,ppCTInfo)
#define ICreateTypeLib_SetName(This,szName) (This)->lpVtbl->SetName(This,szName)
#define ICreateTypeLib_SetVersion(This,wMajorVerNum,wMinorVerNum) (This)->lpVtbl->SetVersion(This,wMajorVerNum,wMinorVerNum)
#define ICreateTypeLib_SetGuid(This,guid) (This)->lpVtbl->SetGuid(This,guid)
#define ICreateTypeLib_SetDocString(This,szDoc) (This)->lpVtbl->SetDocString(This,szDoc)
#define ICreateTypeLib_SetHelpFileName(This,szHelpFileName) (This)->lpVtbl->SetHelpFileName(This,szHelpFileName)
#define ICreateTypeLib_SetHelpContext(This,dwHelpContext) (This)->lpVtbl->SetHelpContext(This,dwHelpContext)
#define ICreateTypeLib_SetLcid(This,lcid) (This)->lpVtbl->SetLcid(This,lcid)
#define ICreateTypeLib_SetLibFlags(This,uLibFlags) (This)->lpVtbl->SetLibFlags(This,uLibFlags)
#define ICreateTypeLib_SaveAllChanges(This) (This)->lpVtbl->SaveAllChanges(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICreateTypeLib_QueryInterface(ICreateTypeLib* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICreateTypeLib_AddRef(ICreateTypeLib* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICreateTypeLib_Release(ICreateTypeLib* This) {
    return This->lpVtbl->Release(This);
}
/*** ICreateTypeLib methods ***/
static FORCEINLINE HRESULT ICreateTypeLib_CreateTypeInfo(ICreateTypeLib* This,LPOLESTR szName,TYPEKIND tkind,ICreateTypeInfo **ppCTInfo) {
    return This->lpVtbl->CreateTypeInfo(This,szName,tkind,ppCTInfo);
}
static FORCEINLINE HRESULT ICreateTypeLib_SetName(ICreateTypeLib* This,LPOLESTR szName) {
    return This->lpVtbl->SetName(This,szName);
}
static FORCEINLINE HRESULT ICreateTypeLib_SetVersion(ICreateTypeLib* This,WORD wMajorVerNum,WORD wMinorVerNum) {
    return This->lpVtbl->SetVersion(This,wMajorVerNum,wMinorVerNum);
}
static FORCEINLINE HRESULT ICreateTypeLib_SetGuid(ICreateTypeLib* This,REFGUID guid) {
    return This->lpVtbl->SetGuid(This,guid);
}
static FORCEINLINE HRESULT ICreateTypeLib_SetDocString(ICreateTypeLib* This,LPOLESTR szDoc) {
    return This->lpVtbl->SetDocString(This,szDoc);
}
static FORCEINLINE HRESULT ICreateTypeLib_SetHelpFileName(ICreateTypeLib* This,LPOLESTR szHelpFileName) {
    return This->lpVtbl->SetHelpFileName(This,szHelpFileName);
}
static FORCEINLINE HRESULT ICreateTypeLib_SetHelpContext(ICreateTypeLib* This,DWORD dwHelpContext) {
    return This->lpVtbl->SetHelpContext(This,dwHelpContext);
}
static FORCEINLINE HRESULT ICreateTypeLib_SetLcid(ICreateTypeLib* This,LCID lcid) {
    return This->lpVtbl->SetLcid(This,lcid);
}
static FORCEINLINE HRESULT ICreateTypeLib_SetLibFlags(ICreateTypeLib* This,UINT uLibFlags) {
    return This->lpVtbl->SetLibFlags(This,uLibFlags);
}
static FORCEINLINE HRESULT ICreateTypeLib_SaveAllChanges(ICreateTypeLib* This) {
    return This->lpVtbl->SaveAllChanges(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ICreateTypeLib_CreateTypeInfo_Proxy(
    ICreateTypeLib* This,
    LPOLESTR szName,
    TYPEKIND tkind,
    ICreateTypeInfo **ppCTInfo);
void __RPC_STUB ICreateTypeLib_CreateTypeInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SetName_Proxy(
    ICreateTypeLib* This,
    LPOLESTR szName);
void __RPC_STUB ICreateTypeLib_SetName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SetVersion_Proxy(
    ICreateTypeLib* This,
    WORD wMajorVerNum,
    WORD wMinorVerNum);
void __RPC_STUB ICreateTypeLib_SetVersion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SetGuid_Proxy(
    ICreateTypeLib* This,
    REFGUID guid);
void __RPC_STUB ICreateTypeLib_SetGuid_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SetDocString_Proxy(
    ICreateTypeLib* This,
    LPOLESTR szDoc);
void __RPC_STUB ICreateTypeLib_SetDocString_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SetHelpFileName_Proxy(
    ICreateTypeLib* This,
    LPOLESTR szHelpFileName);
void __RPC_STUB ICreateTypeLib_SetHelpFileName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SetHelpContext_Proxy(
    ICreateTypeLib* This,
    DWORD dwHelpContext);
void __RPC_STUB ICreateTypeLib_SetHelpContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SetLcid_Proxy(
    ICreateTypeLib* This,
    LCID lcid);
void __RPC_STUB ICreateTypeLib_SetLcid_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SetLibFlags_Proxy(
    ICreateTypeLib* This,
    UINT uLibFlags);
void __RPC_STUB ICreateTypeLib_SetLibFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib_SaveAllChanges_Proxy(
    ICreateTypeLib* This);
void __RPC_STUB ICreateTypeLib_SaveAllChanges_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ICreateTypeLib_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICreateTypeLib2 interface
 */
#ifndef __ICreateTypeLib2_INTERFACE_DEFINED__
#define __ICreateTypeLib2_INTERFACE_DEFINED__

typedef ICreateTypeLib2 *LPCREATETYPELIB2;
DEFINE_GUID(IID_ICreateTypeLib2, 0x0002040f, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0002040f-0000-0000-c000-000000000046")
ICreateTypeLib2 : public ICreateTypeLib
{
    virtual HRESULT STDMETHODCALLTYPE DeleteTypeInfo(
        LPOLESTR szName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCustData(
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHelpStringContext(
        ULONG dwHelpStringContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHelpStringDll(
        LPOLESTR szFileName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICreateTypeLib2, 0x0002040f, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ICreateTypeLib2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICreateTypeLib2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICreateTypeLib2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICreateTypeLib2* This);

    /*** ICreateTypeLib methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTypeInfo)(
        ICreateTypeLib2* This,
        LPOLESTR szName,
        TYPEKIND tkind,
        ICreateTypeInfo **ppCTInfo);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ICreateTypeLib2* This,
        LPOLESTR szName);

    HRESULT (STDMETHODCALLTYPE *SetVersion)(
        ICreateTypeLib2* This,
        WORD wMajorVerNum,
        WORD wMinorVerNum);

    HRESULT (STDMETHODCALLTYPE *SetGuid)(
        ICreateTypeLib2* This,
        REFGUID guid);

    HRESULT (STDMETHODCALLTYPE *SetDocString)(
        ICreateTypeLib2* This,
        LPOLESTR szDoc);

    HRESULT (STDMETHODCALLTYPE *SetHelpFileName)(
        ICreateTypeLib2* This,
        LPOLESTR szHelpFileName);

    HRESULT (STDMETHODCALLTYPE *SetHelpContext)(
        ICreateTypeLib2* This,
        DWORD dwHelpContext);

    HRESULT (STDMETHODCALLTYPE *SetLcid)(
        ICreateTypeLib2* This,
        LCID lcid);

    HRESULT (STDMETHODCALLTYPE *SetLibFlags)(
        ICreateTypeLib2* This,
        UINT uLibFlags);

    HRESULT (STDMETHODCALLTYPE *SaveAllChanges)(
        ICreateTypeLib2* This);

    /*** ICreateTypeLib2 methods ***/
    HRESULT (STDMETHODCALLTYPE *DeleteTypeInfo)(
        ICreateTypeLib2* This,
        LPOLESTR szName);

    HRESULT (STDMETHODCALLTYPE *SetCustData)(
        ICreateTypeLib2* This,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *SetHelpStringContext)(
        ICreateTypeLib2* This,
        ULONG dwHelpStringContext);

    HRESULT (STDMETHODCALLTYPE *SetHelpStringDll)(
        ICreateTypeLib2* This,
        LPOLESTR szFileName);

    END_INTERFACE
} ICreateTypeLib2Vtbl;
interface ICreateTypeLib2 {
    CONST_VTBL ICreateTypeLib2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICreateTypeLib2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICreateTypeLib2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICreateTypeLib2_Release(This) (This)->lpVtbl->Release(This)
/*** ICreateTypeLib methods ***/
#define ICreateTypeLib2_CreateTypeInfo(This,szName,tkind,ppCTInfo) (This)->lpVtbl->CreateTypeInfo(This,szName,tkind,ppCTInfo)
#define ICreateTypeLib2_SetName(This,szName) (This)->lpVtbl->SetName(This,szName)
#define ICreateTypeLib2_SetVersion(This,wMajorVerNum,wMinorVerNum) (This)->lpVtbl->SetVersion(This,wMajorVerNum,wMinorVerNum)
#define ICreateTypeLib2_SetGuid(This,guid) (This)->lpVtbl->SetGuid(This,guid)
#define ICreateTypeLib2_SetDocString(This,szDoc) (This)->lpVtbl->SetDocString(This,szDoc)
#define ICreateTypeLib2_SetHelpFileName(This,szHelpFileName) (This)->lpVtbl->SetHelpFileName(This,szHelpFileName)
#define ICreateTypeLib2_SetHelpContext(This,dwHelpContext) (This)->lpVtbl->SetHelpContext(This,dwHelpContext)
#define ICreateTypeLib2_SetLcid(This,lcid) (This)->lpVtbl->SetLcid(This,lcid)
#define ICreateTypeLib2_SetLibFlags(This,uLibFlags) (This)->lpVtbl->SetLibFlags(This,uLibFlags)
#define ICreateTypeLib2_SaveAllChanges(This) (This)->lpVtbl->SaveAllChanges(This)
/*** ICreateTypeLib2 methods ***/
#define ICreateTypeLib2_DeleteTypeInfo(This,szName) (This)->lpVtbl->DeleteTypeInfo(This,szName)
#define ICreateTypeLib2_SetCustData(This,guid,pVarVal) (This)->lpVtbl->SetCustData(This,guid,pVarVal)
#define ICreateTypeLib2_SetHelpStringContext(This,dwHelpStringContext) (This)->lpVtbl->SetHelpStringContext(This,dwHelpStringContext)
#define ICreateTypeLib2_SetHelpStringDll(This,szFileName) (This)->lpVtbl->SetHelpStringDll(This,szFileName)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICreateTypeLib2_QueryInterface(ICreateTypeLib2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICreateTypeLib2_AddRef(ICreateTypeLib2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICreateTypeLib2_Release(ICreateTypeLib2* This) {
    return This->lpVtbl->Release(This);
}
/*** ICreateTypeLib methods ***/
static FORCEINLINE HRESULT ICreateTypeLib2_CreateTypeInfo(ICreateTypeLib2* This,LPOLESTR szName,TYPEKIND tkind,ICreateTypeInfo **ppCTInfo) {
    return This->lpVtbl->CreateTypeInfo(This,szName,tkind,ppCTInfo);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetName(ICreateTypeLib2* This,LPOLESTR szName) {
    return This->lpVtbl->SetName(This,szName);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetVersion(ICreateTypeLib2* This,WORD wMajorVerNum,WORD wMinorVerNum) {
    return This->lpVtbl->SetVersion(This,wMajorVerNum,wMinorVerNum);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetGuid(ICreateTypeLib2* This,REFGUID guid) {
    return This->lpVtbl->SetGuid(This,guid);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetDocString(ICreateTypeLib2* This,LPOLESTR szDoc) {
    return This->lpVtbl->SetDocString(This,szDoc);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetHelpFileName(ICreateTypeLib2* This,LPOLESTR szHelpFileName) {
    return This->lpVtbl->SetHelpFileName(This,szHelpFileName);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetHelpContext(ICreateTypeLib2* This,DWORD dwHelpContext) {
    return This->lpVtbl->SetHelpContext(This,dwHelpContext);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetLcid(ICreateTypeLib2* This,LCID lcid) {
    return This->lpVtbl->SetLcid(This,lcid);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetLibFlags(ICreateTypeLib2* This,UINT uLibFlags) {
    return This->lpVtbl->SetLibFlags(This,uLibFlags);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SaveAllChanges(ICreateTypeLib2* This) {
    return This->lpVtbl->SaveAllChanges(This);
}
/*** ICreateTypeLib2 methods ***/
static FORCEINLINE HRESULT ICreateTypeLib2_DeleteTypeInfo(ICreateTypeLib2* This,LPOLESTR szName) {
    return This->lpVtbl->DeleteTypeInfo(This,szName);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetCustData(ICreateTypeLib2* This,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->SetCustData(This,guid,pVarVal);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetHelpStringContext(ICreateTypeLib2* This,ULONG dwHelpStringContext) {
    return This->lpVtbl->SetHelpStringContext(This,dwHelpStringContext);
}
static FORCEINLINE HRESULT ICreateTypeLib2_SetHelpStringDll(ICreateTypeLib2* This,LPOLESTR szFileName) {
    return This->lpVtbl->SetHelpStringDll(This,szFileName);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ICreateTypeLib2_DeleteTypeInfo_Proxy(
    ICreateTypeLib2* This,
    LPOLESTR szName);
void __RPC_STUB ICreateTypeLib2_DeleteTypeInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib2_SetCustData_Proxy(
    ICreateTypeLib2* This,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ICreateTypeLib2_SetCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib2_SetHelpStringContext_Proxy(
    ICreateTypeLib2* This,
    ULONG dwHelpStringContext);
void __RPC_STUB ICreateTypeLib2_SetHelpStringContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateTypeLib2_SetHelpStringDll_Proxy(
    ICreateTypeLib2* This,
    LPOLESTR szFileName);
void __RPC_STUB ICreateTypeLib2_SetHelpStringDll_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ICreateTypeLib2_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IDispatch interface
 */
#ifndef __IDispatch_INTERFACE_DEFINED__
#define __IDispatch_INTERFACE_DEFINED__

typedef IDispatch *LPDISPATCH;


#define DISPID_UNKNOWN (-1)

#define DISPID_VALUE (0)

#define DISPID_PROPERTYPUT (-3)

#define DISPID_NEWENUM (-4)

#define DISPID_EVALUATE (-5)

#define DISPID_CONSTRUCTOR (-6)

#define DISPID_DESTRUCTOR (-7)

#define DISPID_COLLECT (-8)


DEFINE_GUID(IID_IDispatch, 0x00020400, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020400-0000-0000-c000-000000000046")
IDispatch : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetTypeInfoCount(
        UINT *pctinfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeInfo(
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIDsOfNames(
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId) = 0;

    virtual HRESULT STDMETHODCALLTYPE Invoke(
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDispatch, 0x00020400, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IDispatchVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDispatch* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDispatch* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDispatch* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IDispatch* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IDispatch* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IDispatch* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IDispatch* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} IDispatchVtbl;
interface IDispatch {
    CONST_VTBL IDispatchVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDispatch_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDispatch_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDispatch_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IDispatch_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IDispatch_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IDispatch_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IDispatch_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDispatch_QueryInterface(IDispatch* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDispatch_AddRef(IDispatch* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDispatch_Release(IDispatch* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IDispatch_GetTypeInfoCount(IDispatch* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IDispatch_GetTypeInfo(IDispatch* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IDispatch_GetIDsOfNames(IDispatch* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IDispatch_Invoke(IDispatch* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IDispatch_GetTypeInfoCount_Proxy(
    IDispatch* This,
    UINT *pctinfo);
void __RPC_STUB IDispatch_GetTypeInfoCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDispatch_GetTypeInfo_Proxy(
    IDispatch* This,
    UINT iTInfo,
    LCID lcid,
    ITypeInfo **ppTInfo);
void __RPC_STUB IDispatch_GetTypeInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDispatch_GetIDsOfNames_Proxy(
    IDispatch* This,
    REFIID riid,
    LPOLESTR *rgszNames,
    UINT cNames,
    LCID lcid,
    DISPID *rgDispId);
void __RPC_STUB IDispatch_GetIDsOfNames_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDispatch_RemoteInvoke_Proxy(
    IDispatch* This,
    DISPID dispIdMember,
    REFIID riid,
    LCID lcid,
    DWORD dwFlags,
    DISPPARAMS *pDispParams,
    VARIANT *pVarResult,
    EXCEPINFO *pExcepInfo,
    UINT *pArgErr,
    UINT cVarRef,
    UINT *rgVarRefIdx,
    VARIANTARG *rgVarRef);
void __RPC_STUB IDispatch_RemoteInvoke_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IDispatch_Invoke_Proxy(
    IDispatch* This,
    DISPID dispIdMember,
    REFIID riid,
    LCID lcid,
    WORD wFlags,
    DISPPARAMS *pDispParams,
    VARIANT *pVarResult,
    EXCEPINFO *pExcepInfo,
    UINT *puArgErr);
HRESULT __RPC_STUB IDispatch_Invoke_Stub(
    IDispatch* This,
    DISPID dispIdMember,
    REFIID riid,
    LCID lcid,
    DWORD dwFlags,
    DISPPARAMS *pDispParams,
    VARIANT *pVarResult,
    EXCEPINFO *pExcepInfo,
    UINT *pArgErr,
    UINT cVarRef,
    UINT *rgVarRefIdx,
    VARIANTARG *rgVarRef);

#endif  /* __IDispatch_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumVARIANT interface
 */
#ifndef __IEnumVARIANT_INTERFACE_DEFINED__
#define __IEnumVARIANT_INTERFACE_DEFINED__

typedef IEnumVARIANT *LPENUMVARIANT;

DEFINE_GUID(IID_IEnumVARIANT, 0x00020404, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020404-0000-0000-c000-000000000046")
IEnumVARIANT : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        VARIANT *rgVar,
        ULONG *pCeltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumVARIANT **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumVARIANT, 0x00020404, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumVARIANTVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumVARIANT* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumVARIANT* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumVARIANT* This);

    /*** IEnumVARIANT methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumVARIANT* This,
        ULONG celt,
        VARIANT *rgVar,
        ULONG *pCeltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumVARIANT* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumVARIANT* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumVARIANT* This,
        IEnumVARIANT **ppEnum);

    END_INTERFACE
} IEnumVARIANTVtbl;
interface IEnumVARIANT {
    CONST_VTBL IEnumVARIANTVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumVARIANT_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumVARIANT_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumVARIANT_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumVARIANT methods ***/
#define IEnumVARIANT_Next(This,celt,rgVar,pCeltFetched) (This)->lpVtbl->Next(This,celt,rgVar,pCeltFetched)
#define IEnumVARIANT_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumVARIANT_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumVARIANT_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumVARIANT_QueryInterface(IEnumVARIANT* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumVARIANT_AddRef(IEnumVARIANT* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumVARIANT_Release(IEnumVARIANT* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumVARIANT methods ***/
static FORCEINLINE HRESULT IEnumVARIANT_Next(IEnumVARIANT* This,ULONG celt,VARIANT *rgVar,ULONG *pCeltFetched) {
    return This->lpVtbl->Next(This,celt,rgVar,pCeltFetched);
}
static FORCEINLINE HRESULT IEnumVARIANT_Skip(IEnumVARIANT* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumVARIANT_Reset(IEnumVARIANT* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumVARIANT_Clone(IEnumVARIANT* This,IEnumVARIANT **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumVARIANT_RemoteNext_Proxy(
    IEnumVARIANT* This,
    ULONG celt,
    VARIANT *rgVar,
    ULONG *pCeltFetched);
void __RPC_STUB IEnumVARIANT_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumVARIANT_Skip_Proxy(
    IEnumVARIANT* This,
    ULONG celt);
void __RPC_STUB IEnumVARIANT_Skip_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumVARIANT_Reset_Proxy(
    IEnumVARIANT* This);
void __RPC_STUB IEnumVARIANT_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumVARIANT_Clone_Proxy(
    IEnumVARIANT* This,
    IEnumVARIANT **ppEnum);
void __RPC_STUB IEnumVARIANT_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumVARIANT_Next_Proxy(
    IEnumVARIANT* This,
    ULONG celt,
    VARIANT *rgVar,
    ULONG *pCeltFetched);
HRESULT __RPC_STUB IEnumVARIANT_Next_Stub(
    IEnumVARIANT* This,
    ULONG celt,
    VARIANT *rgVar,
    ULONG *pCeltFetched);

#endif  /* __IEnumVARIANT_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ITypeComp interface
 */
#ifndef __ITypeComp_INTERFACE_DEFINED__
#define __ITypeComp_INTERFACE_DEFINED__

typedef ITypeComp *LPTYPECOMP;

typedef enum tagDESCKIND {
    DESCKIND_NONE = 0,
    DESCKIND_FUNCDESC = 1,
    DESCKIND_VARDESC = 2,
    DESCKIND_TYPECOMP = 3,
    DESCKIND_IMPLICITAPPOBJ = 4,
    DESCKIND_MAX = 5
} DESCKIND;

typedef union tagBINDPTR {
    FUNCDESC *lpfuncdesc;
    VARDESC *lpvardesc;
    ITypeComp *lptcomp;
} BINDPTR;
typedef union tagBINDPTR *LPBINDPTR;

DEFINE_GUID(IID_ITypeComp, 0x00020403, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020403-0000-0000-c000-000000000046")
ITypeComp : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Bind(
        LPOLESTR szName,
        ULONG lHashVal,
        WORD wFlags,
        ITypeInfo **ppTInfo,
        DESCKIND *pDescKind,
        BINDPTR *pBindPtr) = 0;

    virtual HRESULT STDMETHODCALLTYPE BindType(
        LPOLESTR szName,
        ULONG lHashVal,
        ITypeInfo **ppTInfo,
        ITypeComp **ppTComp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITypeComp, 0x00020403, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ITypeCompVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITypeComp* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITypeComp* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITypeComp* This);

    /*** ITypeComp methods ***/
    HRESULT (STDMETHODCALLTYPE *Bind)(
        ITypeComp* This,
        LPOLESTR szName,
        ULONG lHashVal,
        WORD wFlags,
        ITypeInfo **ppTInfo,
        DESCKIND *pDescKind,
        BINDPTR *pBindPtr);

    HRESULT (STDMETHODCALLTYPE *BindType)(
        ITypeComp* This,
        LPOLESTR szName,
        ULONG lHashVal,
        ITypeInfo **ppTInfo,
        ITypeComp **ppTComp);

    END_INTERFACE
} ITypeCompVtbl;
interface ITypeComp {
    CONST_VTBL ITypeCompVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITypeComp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITypeComp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITypeComp_Release(This) (This)->lpVtbl->Release(This)
/*** ITypeComp methods ***/
#define ITypeComp_Bind(This,szName,lHashVal,wFlags,ppTInfo,pDescKind,pBindPtr) (This)->lpVtbl->Bind(This,szName,lHashVal,wFlags,ppTInfo,pDescKind,pBindPtr)
#define ITypeComp_BindType(This,szName,lHashVal,ppTInfo,ppTComp) (This)->lpVtbl->BindType(This,szName,lHashVal,ppTInfo,ppTComp)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITypeComp_QueryInterface(ITypeComp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITypeComp_AddRef(ITypeComp* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITypeComp_Release(ITypeComp* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypeComp methods ***/
static FORCEINLINE HRESULT ITypeComp_Bind(ITypeComp* This,LPOLESTR szName,ULONG lHashVal,WORD wFlags,ITypeInfo **ppTInfo,DESCKIND *pDescKind,BINDPTR *pBindPtr) {
    return This->lpVtbl->Bind(This,szName,lHashVal,wFlags,ppTInfo,pDescKind,pBindPtr);
}
static FORCEINLINE HRESULT ITypeComp_BindType(ITypeComp* This,LPOLESTR szName,ULONG lHashVal,ITypeInfo **ppTInfo,ITypeComp **ppTComp) {
    return This->lpVtbl->BindType(This,szName,lHashVal,ppTInfo,ppTComp);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITypeComp_RemoteBind_Proxy(
    ITypeComp* This,
    LPOLESTR szName,
    ULONG lHashVal,
    WORD wFlags,
    ITypeInfo **ppTInfo,
    DESCKIND *pDescKind,
    LPFUNCDESC *ppFuncDesc,
    LPVARDESC *ppVarDesc,
    ITypeComp **ppTypeComp,
    CLEANLOCALSTORAGE *pDummy);
void __RPC_STUB ITypeComp_RemoteBind_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeComp_RemoteBindType_Proxy(
    ITypeComp* This,
    LPOLESTR szName,
    ULONG lHashVal,
    ITypeInfo **ppTInfo);
void __RPC_STUB ITypeComp_RemoteBindType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK ITypeComp_Bind_Proxy(
    ITypeComp* This,
    LPOLESTR szName,
    ULONG lHashVal,
    WORD wFlags,
    ITypeInfo **ppTInfo,
    DESCKIND *pDescKind,
    BINDPTR *pBindPtr);
HRESULT __RPC_STUB ITypeComp_Bind_Stub(
    ITypeComp* This,
    LPOLESTR szName,
    ULONG lHashVal,
    WORD wFlags,
    ITypeInfo **ppTInfo,
    DESCKIND *pDescKind,
    LPFUNCDESC *ppFuncDesc,
    LPVARDESC *ppVarDesc,
    ITypeComp **ppTypeComp,
    CLEANLOCALSTORAGE *pDummy);
HRESULT CALLBACK ITypeComp_BindType_Proxy(
    ITypeComp* This,
    LPOLESTR szName,
    ULONG lHashVal,
    ITypeInfo **ppTInfo,
    ITypeComp **ppTComp);
HRESULT __RPC_STUB ITypeComp_BindType_Stub(
    ITypeComp* This,
    LPOLESTR szName,
    ULONG lHashVal,
    ITypeInfo **ppTInfo);

#endif  /* __ITypeComp_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * ITypeInfo interface
 */
#ifndef __ITypeInfo_INTERFACE_DEFINED__
#define __ITypeInfo_INTERFACE_DEFINED__

typedef ITypeInfo *LPTYPEINFO;

DEFINE_GUID(IID_ITypeInfo, 0x00020401, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020401-0000-0000-c000-000000000046")
ITypeInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetTypeAttr(
        TYPEATTR **ppTypeAttr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeComp(
        ITypeComp **ppTComp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFuncDesc(
        UINT index,
        FUNCDESC **ppFuncDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVarDesc(
        UINT index,
        VARDESC **ppVarDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNames(
        MEMBERID memid,
        BSTR *rgBstrNames,
        UINT cMaxNames,
        UINT *pcNames) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRefTypeOfImplType(
        UINT index,
        HREFTYPE *pRefType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImplTypeFlags(
        UINT index,
        INT *pImplTypeFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIDsOfNames(
        LPOLESTR *rgszNames,
        UINT cNames,
        MEMBERID *pMemId) = 0;

    virtual HRESULT STDMETHODCALLTYPE Invoke(
        PVOID pvInstance,
        MEMBERID memid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentation(
        MEMBERID memid,
        BSTR *pBstrName,
        BSTR *pBstrDocString,
        DWORD *pdwHelpContext,
        BSTR *pBstrHelpFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDllEntry(
        MEMBERID memid,
        INVOKEKIND invKind,
        BSTR *pBstrDllName,
        BSTR *pBstrName,
        WORD *pwOrdinal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRefTypeInfo(
        HREFTYPE hRefType,
        ITypeInfo **ppTInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddressOfMember(
        MEMBERID memid,
        INVOKEKIND invKind,
        PVOID *ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        IUnknown *pUnkOuter,
        REFIID riid,
        PVOID *ppvObj) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMops(
        MEMBERID memid,
        BSTR *pBstrMops) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContainingTypeLib(
        ITypeLib **ppTLib,
        UINT *pIndex) = 0;

    virtual void STDMETHODCALLTYPE ReleaseTypeAttr(
        TYPEATTR *pTypeAttr) = 0;

    virtual void STDMETHODCALLTYPE ReleaseFuncDesc(
        FUNCDESC *pFuncDesc) = 0;

    virtual void STDMETHODCALLTYPE ReleaseVarDesc(
        VARDESC *pVarDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITypeInfo, 0x00020401, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ITypeInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITypeInfo* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITypeInfo* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITypeInfo* This);

    /*** ITypeInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeAttr)(
        ITypeInfo* This,
        TYPEATTR **ppTypeAttr);

    HRESULT (STDMETHODCALLTYPE *GetTypeComp)(
        ITypeInfo* This,
        ITypeComp **ppTComp);

    HRESULT (STDMETHODCALLTYPE *GetFuncDesc)(
        ITypeInfo* This,
        UINT index,
        FUNCDESC **ppFuncDesc);

    HRESULT (STDMETHODCALLTYPE *GetVarDesc)(
        ITypeInfo* This,
        UINT index,
        VARDESC **ppVarDesc);

    HRESULT (STDMETHODCALLTYPE *GetNames)(
        ITypeInfo* This,
        MEMBERID memid,
        BSTR *rgBstrNames,
        UINT cMaxNames,
        UINT *pcNames);

    HRESULT (STDMETHODCALLTYPE *GetRefTypeOfImplType)(
        ITypeInfo* This,
        UINT index,
        HREFTYPE *pRefType);

    HRESULT (STDMETHODCALLTYPE *GetImplTypeFlags)(
        ITypeInfo* This,
        UINT index,
        INT *pImplTypeFlags);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITypeInfo* This,
        LPOLESTR *rgszNames,
        UINT cNames,
        MEMBERID *pMemId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITypeInfo* This,
        PVOID pvInstance,
        MEMBERID memid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    HRESULT (STDMETHODCALLTYPE *GetDocumentation)(
        ITypeInfo* This,
        MEMBERID memid,
        BSTR *pBstrName,
        BSTR *pBstrDocString,
        DWORD *pdwHelpContext,
        BSTR *pBstrHelpFile);

    HRESULT (STDMETHODCALLTYPE *GetDllEntry)(
        ITypeInfo* This,
        MEMBERID memid,
        INVOKEKIND invKind,
        BSTR *pBstrDllName,
        BSTR *pBstrName,
        WORD *pwOrdinal);

    HRESULT (STDMETHODCALLTYPE *GetRefTypeInfo)(
        ITypeInfo* This,
        HREFTYPE hRefType,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *AddressOfMember)(
        ITypeInfo* This,
        MEMBERID memid,
        INVOKEKIND invKind,
        PVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        ITypeInfo* This,
        IUnknown *pUnkOuter,
        REFIID riid,
        PVOID *ppvObj);

    HRESULT (STDMETHODCALLTYPE *GetMops)(
        ITypeInfo* This,
        MEMBERID memid,
        BSTR *pBstrMops);

    HRESULT (STDMETHODCALLTYPE *GetContainingTypeLib)(
        ITypeInfo* This,
        ITypeLib **ppTLib,
        UINT *pIndex);

    void (STDMETHODCALLTYPE *ReleaseTypeAttr)(
        ITypeInfo* This,
        TYPEATTR *pTypeAttr);

    void (STDMETHODCALLTYPE *ReleaseFuncDesc)(
        ITypeInfo* This,
        FUNCDESC *pFuncDesc);

    void (STDMETHODCALLTYPE *ReleaseVarDesc)(
        ITypeInfo* This,
        VARDESC *pVarDesc);

    END_INTERFACE
} ITypeInfoVtbl;
interface ITypeInfo {
    CONST_VTBL ITypeInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITypeInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITypeInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITypeInfo_Release(This) (This)->lpVtbl->Release(This)
/*** ITypeInfo methods ***/
#define ITypeInfo_GetTypeAttr(This,ppTypeAttr) (This)->lpVtbl->GetTypeAttr(This,ppTypeAttr)
#define ITypeInfo_GetTypeComp(This,ppTComp) (This)->lpVtbl->GetTypeComp(This,ppTComp)
#define ITypeInfo_GetFuncDesc(This,index,ppFuncDesc) (This)->lpVtbl->GetFuncDesc(This,index,ppFuncDesc)
#define ITypeInfo_GetVarDesc(This,index,ppVarDesc) (This)->lpVtbl->GetVarDesc(This,index,ppVarDesc)
#define ITypeInfo_GetNames(This,memid,rgBstrNames,cMaxNames,pcNames) (This)->lpVtbl->GetNames(This,memid,rgBstrNames,cMaxNames,pcNames)
#define ITypeInfo_GetRefTypeOfImplType(This,index,pRefType) (This)->lpVtbl->GetRefTypeOfImplType(This,index,pRefType)
#define ITypeInfo_GetImplTypeFlags(This,index,pImplTypeFlags) (This)->lpVtbl->GetImplTypeFlags(This,index,pImplTypeFlags)
#define ITypeInfo_GetIDsOfNames(This,rgszNames,cNames,pMemId) (This)->lpVtbl->GetIDsOfNames(This,rgszNames,cNames,pMemId)
#define ITypeInfo_Invoke(This,pvInstance,memid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,pvInstance,memid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define ITypeInfo_GetDocumentation(This,memid,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile) (This)->lpVtbl->GetDocumentation(This,memid,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile)
#define ITypeInfo_GetDllEntry(This,memid,invKind,pBstrDllName,pBstrName,pwOrdinal) (This)->lpVtbl->GetDllEntry(This,memid,invKind,pBstrDllName,pBstrName,pwOrdinal)
#define ITypeInfo_GetRefTypeInfo(This,hRefType,ppTInfo) (This)->lpVtbl->GetRefTypeInfo(This,hRefType,ppTInfo)
#define ITypeInfo_AddressOfMember(This,memid,invKind,ppv) (This)->lpVtbl->AddressOfMember(This,memid,invKind,ppv)
#define ITypeInfo_CreateInstance(This,pUnkOuter,riid,ppvObj) (This)->lpVtbl->CreateInstance(This,pUnkOuter,riid,ppvObj)
#define ITypeInfo_GetMops(This,memid,pBstrMops) (This)->lpVtbl->GetMops(This,memid,pBstrMops)
#define ITypeInfo_GetContainingTypeLib(This,ppTLib,pIndex) (This)->lpVtbl->GetContainingTypeLib(This,ppTLib,pIndex)
#define ITypeInfo_ReleaseTypeAttr(This,pTypeAttr) (This)->lpVtbl->ReleaseTypeAttr(This,pTypeAttr)
#define ITypeInfo_ReleaseFuncDesc(This,pFuncDesc) (This)->lpVtbl->ReleaseFuncDesc(This,pFuncDesc)
#define ITypeInfo_ReleaseVarDesc(This,pVarDesc) (This)->lpVtbl->ReleaseVarDesc(This,pVarDesc)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITypeInfo_QueryInterface(ITypeInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITypeInfo_AddRef(ITypeInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITypeInfo_Release(ITypeInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypeInfo methods ***/
static FORCEINLINE HRESULT ITypeInfo_GetTypeAttr(ITypeInfo* This,TYPEATTR **ppTypeAttr) {
    return This->lpVtbl->GetTypeAttr(This,ppTypeAttr);
}
static FORCEINLINE HRESULT ITypeInfo_GetTypeComp(ITypeInfo* This,ITypeComp **ppTComp) {
    return This->lpVtbl->GetTypeComp(This,ppTComp);
}
static FORCEINLINE HRESULT ITypeInfo_GetFuncDesc(ITypeInfo* This,UINT index,FUNCDESC **ppFuncDesc) {
    return This->lpVtbl->GetFuncDesc(This,index,ppFuncDesc);
}
static FORCEINLINE HRESULT ITypeInfo_GetVarDesc(ITypeInfo* This,UINT index,VARDESC **ppVarDesc) {
    return This->lpVtbl->GetVarDesc(This,index,ppVarDesc);
}
static FORCEINLINE HRESULT ITypeInfo_GetNames(ITypeInfo* This,MEMBERID memid,BSTR *rgBstrNames,UINT cMaxNames,UINT *pcNames) {
    return This->lpVtbl->GetNames(This,memid,rgBstrNames,cMaxNames,pcNames);
}
static FORCEINLINE HRESULT ITypeInfo_GetRefTypeOfImplType(ITypeInfo* This,UINT index,HREFTYPE *pRefType) {
    return This->lpVtbl->GetRefTypeOfImplType(This,index,pRefType);
}
static FORCEINLINE HRESULT ITypeInfo_GetImplTypeFlags(ITypeInfo* This,UINT index,INT *pImplTypeFlags) {
    return This->lpVtbl->GetImplTypeFlags(This,index,pImplTypeFlags);
}
static FORCEINLINE HRESULT ITypeInfo_GetIDsOfNames(ITypeInfo* This,LPOLESTR *rgszNames,UINT cNames,MEMBERID *pMemId) {
    return This->lpVtbl->GetIDsOfNames(This,rgszNames,cNames,pMemId);
}
static FORCEINLINE HRESULT ITypeInfo_Invoke(ITypeInfo* This,PVOID pvInstance,MEMBERID memid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,pvInstance,memid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
static FORCEINLINE HRESULT ITypeInfo_GetDocumentation(ITypeInfo* This,MEMBERID memid,BSTR *pBstrName,BSTR *pBstrDocString,DWORD *pdwHelpContext,BSTR *pBstrHelpFile) {
    return This->lpVtbl->GetDocumentation(This,memid,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile);
}
static FORCEINLINE HRESULT ITypeInfo_GetDllEntry(ITypeInfo* This,MEMBERID memid,INVOKEKIND invKind,BSTR *pBstrDllName,BSTR *pBstrName,WORD *pwOrdinal) {
    return This->lpVtbl->GetDllEntry(This,memid,invKind,pBstrDllName,pBstrName,pwOrdinal);
}
static FORCEINLINE HRESULT ITypeInfo_GetRefTypeInfo(ITypeInfo* This,HREFTYPE hRefType,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetRefTypeInfo(This,hRefType,ppTInfo);
}
static FORCEINLINE HRESULT ITypeInfo_AddressOfMember(ITypeInfo* This,MEMBERID memid,INVOKEKIND invKind,PVOID *ppv) {
    return This->lpVtbl->AddressOfMember(This,memid,invKind,ppv);
}
static FORCEINLINE HRESULT ITypeInfo_CreateInstance(ITypeInfo* This,IUnknown *pUnkOuter,REFIID riid,PVOID *ppvObj) {
    return This->lpVtbl->CreateInstance(This,pUnkOuter,riid,ppvObj);
}
static FORCEINLINE HRESULT ITypeInfo_GetMops(ITypeInfo* This,MEMBERID memid,BSTR *pBstrMops) {
    return This->lpVtbl->GetMops(This,memid,pBstrMops);
}
static FORCEINLINE HRESULT ITypeInfo_GetContainingTypeLib(ITypeInfo* This,ITypeLib **ppTLib,UINT *pIndex) {
    return This->lpVtbl->GetContainingTypeLib(This,ppTLib,pIndex);
}
static FORCEINLINE void ITypeInfo_ReleaseTypeAttr(ITypeInfo* This,TYPEATTR *pTypeAttr) {
    This->lpVtbl->ReleaseTypeAttr(This,pTypeAttr);
}
static FORCEINLINE void ITypeInfo_ReleaseFuncDesc(ITypeInfo* This,FUNCDESC *pFuncDesc) {
    This->lpVtbl->ReleaseFuncDesc(This,pFuncDesc);
}
static FORCEINLINE void ITypeInfo_ReleaseVarDesc(ITypeInfo* This,VARDESC *pVarDesc) {
    This->lpVtbl->ReleaseVarDesc(This,pVarDesc);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITypeInfo_RemoteGetTypeAttr_Proxy(
    ITypeInfo* This,
    LPTYPEATTR *ppTypeAttr,
    CLEANLOCALSTORAGE *pDummy);
void __RPC_STUB ITypeInfo_RemoteGetTypeAttr_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_GetTypeComp_Proxy(
    ITypeInfo* This,
    ITypeComp **ppTComp);
void __RPC_STUB ITypeInfo_GetTypeComp_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_RemoteGetFuncDesc_Proxy(
    ITypeInfo* This,
    UINT index,
    LPFUNCDESC *ppFuncDesc,
    CLEANLOCALSTORAGE *pDummy);
void __RPC_STUB ITypeInfo_RemoteGetFuncDesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_RemoteGetVarDesc_Proxy(
    ITypeInfo* This,
    UINT index,
    LPVARDESC *ppVarDesc,
    CLEANLOCALSTORAGE *pDummy);
void __RPC_STUB ITypeInfo_RemoteGetVarDesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_RemoteGetNames_Proxy(
    ITypeInfo* This,
    MEMBERID memid,
    BSTR *rgBstrNames,
    UINT cMaxNames,
    UINT *pcNames);
void __RPC_STUB ITypeInfo_RemoteGetNames_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_GetRefTypeOfImplType_Proxy(
    ITypeInfo* This,
    UINT index,
    HREFTYPE *pRefType);
void __RPC_STUB ITypeInfo_GetRefTypeOfImplType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_GetImplTypeFlags_Proxy(
    ITypeInfo* This,
    UINT index,
    INT *pImplTypeFlags);
void __RPC_STUB ITypeInfo_GetImplTypeFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_LocalGetIDsOfNames_Proxy(
    ITypeInfo* This);
void __RPC_STUB ITypeInfo_LocalGetIDsOfNames_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_LocalInvoke_Proxy(
    ITypeInfo* This);
void __RPC_STUB ITypeInfo_LocalInvoke_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_RemoteGetDocumentation_Proxy(
    ITypeInfo* This,
    MEMBERID memid,
    DWORD refPtrFlags,
    BSTR *pBstrName,
    BSTR *pBstrDocString,
    DWORD *pdwHelpContext,
    BSTR *pBstrHelpFile);
void __RPC_STUB ITypeInfo_RemoteGetDocumentation_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_RemoteGetDllEntry_Proxy(
    ITypeInfo* This,
    MEMBERID memid,
    INVOKEKIND invKind,
    DWORD refPtrFlags,
    BSTR *pBstrDllName,
    BSTR *pBstrName,
    WORD *pwOrdinal);
void __RPC_STUB ITypeInfo_RemoteGetDllEntry_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_GetRefTypeInfo_Proxy(
    ITypeInfo* This,
    HREFTYPE hRefType,
    ITypeInfo **ppTInfo);
void __RPC_STUB ITypeInfo_GetRefTypeInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_LocalAddressOfMember_Proxy(
    ITypeInfo* This);
void __RPC_STUB ITypeInfo_LocalAddressOfMember_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_RemoteCreateInstance_Proxy(
    ITypeInfo* This,
    REFIID riid,
    IUnknown **ppvObj);
void __RPC_STUB ITypeInfo_RemoteCreateInstance_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_GetMops_Proxy(
    ITypeInfo* This,
    MEMBERID memid,
    BSTR *pBstrMops);
void __RPC_STUB ITypeInfo_GetMops_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_RemoteGetContainingTypeLib_Proxy(
    ITypeInfo* This,
    ITypeLib **ppTLib,
    UINT *pIndex);
void __RPC_STUB ITypeInfo_RemoteGetContainingTypeLib_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_LocalReleaseTypeAttr_Proxy(
    ITypeInfo* This);
void __RPC_STUB ITypeInfo_LocalReleaseTypeAttr_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_LocalReleaseFuncDesc_Proxy(
    ITypeInfo* This);
void __RPC_STUB ITypeInfo_LocalReleaseFuncDesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo_LocalReleaseVarDesc_Proxy(
    ITypeInfo* This);
void __RPC_STUB ITypeInfo_LocalReleaseVarDesc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK ITypeInfo_GetTypeAttr_Proxy(
    ITypeInfo* This,
    TYPEATTR **ppTypeAttr);
HRESULT __RPC_STUB ITypeInfo_GetTypeAttr_Stub(
    ITypeInfo* This,
    LPTYPEATTR *ppTypeAttr,
    CLEANLOCALSTORAGE *pDummy);
HRESULT CALLBACK ITypeInfo_GetFuncDesc_Proxy(
    ITypeInfo* This,
    UINT index,
    FUNCDESC **ppFuncDesc);
HRESULT __RPC_STUB ITypeInfo_GetFuncDesc_Stub(
    ITypeInfo* This,
    UINT index,
    LPFUNCDESC *ppFuncDesc,
    CLEANLOCALSTORAGE *pDummy);
HRESULT CALLBACK ITypeInfo_GetVarDesc_Proxy(
    ITypeInfo* This,
    UINT index,
    VARDESC **ppVarDesc);
HRESULT __RPC_STUB ITypeInfo_GetVarDesc_Stub(
    ITypeInfo* This,
    UINT index,
    LPVARDESC *ppVarDesc,
    CLEANLOCALSTORAGE *pDummy);
HRESULT CALLBACK ITypeInfo_GetNames_Proxy(
    ITypeInfo* This,
    MEMBERID memid,
    BSTR *rgBstrNames,
    UINT cMaxNames,
    UINT *pcNames);
HRESULT __RPC_STUB ITypeInfo_GetNames_Stub(
    ITypeInfo* This,
    MEMBERID memid,
    BSTR *rgBstrNames,
    UINT cMaxNames,
    UINT *pcNames);
HRESULT CALLBACK ITypeInfo_GetIDsOfNames_Proxy(
    ITypeInfo* This,
    LPOLESTR *rgszNames,
    UINT cNames,
    MEMBERID *pMemId);
HRESULT __RPC_STUB ITypeInfo_GetIDsOfNames_Stub(
    ITypeInfo* This);
HRESULT CALLBACK ITypeInfo_Invoke_Proxy(
    ITypeInfo* This,
    PVOID pvInstance,
    MEMBERID memid,
    WORD wFlags,
    DISPPARAMS *pDispParams,
    VARIANT *pVarResult,
    EXCEPINFO *pExcepInfo,
    UINT *puArgErr);
HRESULT __RPC_STUB ITypeInfo_Invoke_Stub(
    ITypeInfo* This);
HRESULT CALLBACK ITypeInfo_GetDocumentation_Proxy(
    ITypeInfo* This,
    MEMBERID memid,
    BSTR *pBstrName,
    BSTR *pBstrDocString,
    DWORD *pdwHelpContext,
    BSTR *pBstrHelpFile);
HRESULT __RPC_STUB ITypeInfo_GetDocumentation_Stub(
    ITypeInfo* This,
    MEMBERID memid,
    DWORD refPtrFlags,
    BSTR *pBstrName,
    BSTR *pBstrDocString,
    DWORD *pdwHelpContext,
    BSTR *pBstrHelpFile);
HRESULT CALLBACK ITypeInfo_GetDllEntry_Proxy(
    ITypeInfo* This,
    MEMBERID memid,
    INVOKEKIND invKind,
    BSTR *pBstrDllName,
    BSTR *pBstrName,
    WORD *pwOrdinal);
HRESULT __RPC_STUB ITypeInfo_GetDllEntry_Stub(
    ITypeInfo* This,
    MEMBERID memid,
    INVOKEKIND invKind,
    DWORD refPtrFlags,
    BSTR *pBstrDllName,
    BSTR *pBstrName,
    WORD *pwOrdinal);
HRESULT CALLBACK ITypeInfo_AddressOfMember_Proxy(
    ITypeInfo* This,
    MEMBERID memid,
    INVOKEKIND invKind,
    PVOID *ppv);
HRESULT __RPC_STUB ITypeInfo_AddressOfMember_Stub(
    ITypeInfo* This);
HRESULT CALLBACK ITypeInfo_CreateInstance_Proxy(
    ITypeInfo* This,
    IUnknown *pUnkOuter,
    REFIID riid,
    PVOID *ppvObj);
HRESULT __RPC_STUB ITypeInfo_CreateInstance_Stub(
    ITypeInfo* This,
    REFIID riid,
    IUnknown **ppvObj);
HRESULT CALLBACK ITypeInfo_GetContainingTypeLib_Proxy(
    ITypeInfo* This,
    ITypeLib **ppTLib,
    UINT *pIndex);
HRESULT __RPC_STUB ITypeInfo_GetContainingTypeLib_Stub(
    ITypeInfo* This,
    ITypeLib **ppTLib,
    UINT *pIndex);
void CALLBACK ITypeInfo_ReleaseTypeAttr_Proxy(
    ITypeInfo* This,
    TYPEATTR *pTypeAttr);
HRESULT __RPC_STUB ITypeInfo_ReleaseTypeAttr_Stub(
    ITypeInfo* This);
void CALLBACK ITypeInfo_ReleaseFuncDesc_Proxy(
    ITypeInfo* This,
    FUNCDESC *pFuncDesc);
HRESULT __RPC_STUB ITypeInfo_ReleaseFuncDesc_Stub(
    ITypeInfo* This);
void CALLBACK ITypeInfo_ReleaseVarDesc_Proxy(
    ITypeInfo* This,
    VARDESC *pVarDesc);
HRESULT __RPC_STUB ITypeInfo_ReleaseVarDesc_Stub(
    ITypeInfo* This);

#endif  /* __ITypeInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ITypeInfo2 interface
 */
#ifndef __ITypeInfo2_INTERFACE_DEFINED__
#define __ITypeInfo2_INTERFACE_DEFINED__

typedef ITypeInfo2 *LPTYPEINFO2;

DEFINE_GUID(IID_ITypeInfo2, 0x00020412, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020412-0000-0000-c000-000000000046")
ITypeInfo2 : public ITypeInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetTypeKind(
        TYPEKIND *pTypeKind) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeFlags(
        ULONG *pTypeFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFuncIndexOfMemId(
        MEMBERID memid,
        INVOKEKIND invKind,
        UINT *pFuncIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVarIndexOfMemId(
        MEMBERID memid,
        UINT *pVarIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustData(
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFuncCustData(
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParamCustData(
        UINT indexFunc,
        UINT indexParam,
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVarCustData(
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImplTypeCustData(
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentation2(
        MEMBERID memid,
        LCID lcid,
        BSTR *pbstrHelpString,
        DWORD *pdwHelpStringContext,
        BSTR *pbstrHelpStringDll) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllCustData(
        CUSTDATA *pCustData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllFuncCustData(
        UINT index,
        CUSTDATA *pCustData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllParamCustData(
        UINT indexFunc,
        UINT indexParam,
        CUSTDATA *pCustData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllVarCustData(
        UINT index,
        CUSTDATA *pCustData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllImplTypeCustData(
        UINT index,
        CUSTDATA *pCustData) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITypeInfo2, 0x00020412, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ITypeInfo2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITypeInfo2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITypeInfo2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITypeInfo2* This);

    /*** ITypeInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeAttr)(
        ITypeInfo2* This,
        TYPEATTR **ppTypeAttr);

    HRESULT (STDMETHODCALLTYPE *GetTypeComp)(
        ITypeInfo2* This,
        ITypeComp **ppTComp);

    HRESULT (STDMETHODCALLTYPE *GetFuncDesc)(
        ITypeInfo2* This,
        UINT index,
        FUNCDESC **ppFuncDesc);

    HRESULT (STDMETHODCALLTYPE *GetVarDesc)(
        ITypeInfo2* This,
        UINT index,
        VARDESC **ppVarDesc);

    HRESULT (STDMETHODCALLTYPE *GetNames)(
        ITypeInfo2* This,
        MEMBERID memid,
        BSTR *rgBstrNames,
        UINT cMaxNames,
        UINT *pcNames);

    HRESULT (STDMETHODCALLTYPE *GetRefTypeOfImplType)(
        ITypeInfo2* This,
        UINT index,
        HREFTYPE *pRefType);

    HRESULT (STDMETHODCALLTYPE *GetImplTypeFlags)(
        ITypeInfo2* This,
        UINT index,
        INT *pImplTypeFlags);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITypeInfo2* This,
        LPOLESTR *rgszNames,
        UINT cNames,
        MEMBERID *pMemId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITypeInfo2* This,
        PVOID pvInstance,
        MEMBERID memid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    HRESULT (STDMETHODCALLTYPE *GetDocumentation)(
        ITypeInfo2* This,
        MEMBERID memid,
        BSTR *pBstrName,
        BSTR *pBstrDocString,
        DWORD *pdwHelpContext,
        BSTR *pBstrHelpFile);

    HRESULT (STDMETHODCALLTYPE *GetDllEntry)(
        ITypeInfo2* This,
        MEMBERID memid,
        INVOKEKIND invKind,
        BSTR *pBstrDllName,
        BSTR *pBstrName,
        WORD *pwOrdinal);

    HRESULT (STDMETHODCALLTYPE *GetRefTypeInfo)(
        ITypeInfo2* This,
        HREFTYPE hRefType,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *AddressOfMember)(
        ITypeInfo2* This,
        MEMBERID memid,
        INVOKEKIND invKind,
        PVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        ITypeInfo2* This,
        IUnknown *pUnkOuter,
        REFIID riid,
        PVOID *ppvObj);

    HRESULT (STDMETHODCALLTYPE *GetMops)(
        ITypeInfo2* This,
        MEMBERID memid,
        BSTR *pBstrMops);

    HRESULT (STDMETHODCALLTYPE *GetContainingTypeLib)(
        ITypeInfo2* This,
        ITypeLib **ppTLib,
        UINT *pIndex);

    void (STDMETHODCALLTYPE *ReleaseTypeAttr)(
        ITypeInfo2* This,
        TYPEATTR *pTypeAttr);

    void (STDMETHODCALLTYPE *ReleaseFuncDesc)(
        ITypeInfo2* This,
        FUNCDESC *pFuncDesc);

    void (STDMETHODCALLTYPE *ReleaseVarDesc)(
        ITypeInfo2* This,
        VARDESC *pVarDesc);

    /*** ITypeInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeKind)(
        ITypeInfo2* This,
        TYPEKIND *pTypeKind);

    HRESULT (STDMETHODCALLTYPE *GetTypeFlags)(
        ITypeInfo2* This,
        ULONG *pTypeFlags);

    HRESULT (STDMETHODCALLTYPE *GetFuncIndexOfMemId)(
        ITypeInfo2* This,
        MEMBERID memid,
        INVOKEKIND invKind,
        UINT *pFuncIndex);

    HRESULT (STDMETHODCALLTYPE *GetVarIndexOfMemId)(
        ITypeInfo2* This,
        MEMBERID memid,
        UINT *pVarIndex);

    HRESULT (STDMETHODCALLTYPE *GetCustData)(
        ITypeInfo2* This,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *GetFuncCustData)(
        ITypeInfo2* This,
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *GetParamCustData)(
        ITypeInfo2* This,
        UINT indexFunc,
        UINT indexParam,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *GetVarCustData)(
        ITypeInfo2* This,
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *GetImplTypeCustData)(
        ITypeInfo2* This,
        UINT index,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *GetDocumentation2)(
        ITypeInfo2* This,
        MEMBERID memid,
        LCID lcid,
        BSTR *pbstrHelpString,
        DWORD *pdwHelpStringContext,
        BSTR *pbstrHelpStringDll);

    HRESULT (STDMETHODCALLTYPE *GetAllCustData)(
        ITypeInfo2* This,
        CUSTDATA *pCustData);

    HRESULT (STDMETHODCALLTYPE *GetAllFuncCustData)(
        ITypeInfo2* This,
        UINT index,
        CUSTDATA *pCustData);

    HRESULT (STDMETHODCALLTYPE *GetAllParamCustData)(
        ITypeInfo2* This,
        UINT indexFunc,
        UINT indexParam,
        CUSTDATA *pCustData);

    HRESULT (STDMETHODCALLTYPE *GetAllVarCustData)(
        ITypeInfo2* This,
        UINT index,
        CUSTDATA *pCustData);

    HRESULT (STDMETHODCALLTYPE *GetAllImplTypeCustData)(
        ITypeInfo2* This,
        UINT index,
        CUSTDATA *pCustData);

    END_INTERFACE
} ITypeInfo2Vtbl;
interface ITypeInfo2 {
    CONST_VTBL ITypeInfo2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITypeInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITypeInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITypeInfo2_Release(This) (This)->lpVtbl->Release(This)
/*** ITypeInfo methods ***/
#define ITypeInfo2_GetTypeAttr(This,ppTypeAttr) (This)->lpVtbl->GetTypeAttr(This,ppTypeAttr)
#define ITypeInfo2_GetTypeComp(This,ppTComp) (This)->lpVtbl->GetTypeComp(This,ppTComp)
#define ITypeInfo2_GetFuncDesc(This,index,ppFuncDesc) (This)->lpVtbl->GetFuncDesc(This,index,ppFuncDesc)
#define ITypeInfo2_GetVarDesc(This,index,ppVarDesc) (This)->lpVtbl->GetVarDesc(This,index,ppVarDesc)
#define ITypeInfo2_GetNames(This,memid,rgBstrNames,cMaxNames,pcNames) (This)->lpVtbl->GetNames(This,memid,rgBstrNames,cMaxNames,pcNames)
#define ITypeInfo2_GetRefTypeOfImplType(This,index,pRefType) (This)->lpVtbl->GetRefTypeOfImplType(This,index,pRefType)
#define ITypeInfo2_GetImplTypeFlags(This,index,pImplTypeFlags) (This)->lpVtbl->GetImplTypeFlags(This,index,pImplTypeFlags)
#define ITypeInfo2_GetIDsOfNames(This,rgszNames,cNames,pMemId) (This)->lpVtbl->GetIDsOfNames(This,rgszNames,cNames,pMemId)
#define ITypeInfo2_Invoke(This,pvInstance,memid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,pvInstance,memid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define ITypeInfo2_GetDocumentation(This,memid,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile) (This)->lpVtbl->GetDocumentation(This,memid,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile)
#define ITypeInfo2_GetDllEntry(This,memid,invKind,pBstrDllName,pBstrName,pwOrdinal) (This)->lpVtbl->GetDllEntry(This,memid,invKind,pBstrDllName,pBstrName,pwOrdinal)
#define ITypeInfo2_GetRefTypeInfo(This,hRefType,ppTInfo) (This)->lpVtbl->GetRefTypeInfo(This,hRefType,ppTInfo)
#define ITypeInfo2_AddressOfMember(This,memid,invKind,ppv) (This)->lpVtbl->AddressOfMember(This,memid,invKind,ppv)
#define ITypeInfo2_CreateInstance(This,pUnkOuter,riid,ppvObj) (This)->lpVtbl->CreateInstance(This,pUnkOuter,riid,ppvObj)
#define ITypeInfo2_GetMops(This,memid,pBstrMops) (This)->lpVtbl->GetMops(This,memid,pBstrMops)
#define ITypeInfo2_GetContainingTypeLib(This,ppTLib,pIndex) (This)->lpVtbl->GetContainingTypeLib(This,ppTLib,pIndex)
#define ITypeInfo2_ReleaseTypeAttr(This,pTypeAttr) (This)->lpVtbl->ReleaseTypeAttr(This,pTypeAttr)
#define ITypeInfo2_ReleaseFuncDesc(This,pFuncDesc) (This)->lpVtbl->ReleaseFuncDesc(This,pFuncDesc)
#define ITypeInfo2_ReleaseVarDesc(This,pVarDesc) (This)->lpVtbl->ReleaseVarDesc(This,pVarDesc)
/*** ITypeInfo2 methods ***/
#define ITypeInfo2_GetTypeKind(This,pTypeKind) (This)->lpVtbl->GetTypeKind(This,pTypeKind)
#define ITypeInfo2_GetTypeFlags(This,pTypeFlags) (This)->lpVtbl->GetTypeFlags(This,pTypeFlags)
#define ITypeInfo2_GetFuncIndexOfMemId(This,memid,invKind,pFuncIndex) (This)->lpVtbl->GetFuncIndexOfMemId(This,memid,invKind,pFuncIndex)
#define ITypeInfo2_GetVarIndexOfMemId(This,memid,pVarIndex) (This)->lpVtbl->GetVarIndexOfMemId(This,memid,pVarIndex)
#define ITypeInfo2_GetCustData(This,guid,pVarVal) (This)->lpVtbl->GetCustData(This,guid,pVarVal)
#define ITypeInfo2_GetFuncCustData(This,index,guid,pVarVal) (This)->lpVtbl->GetFuncCustData(This,index,guid,pVarVal)
#define ITypeInfo2_GetParamCustData(This,indexFunc,indexParam,guid,pVarVal) (This)->lpVtbl->GetParamCustData(This,indexFunc,indexParam,guid,pVarVal)
#define ITypeInfo2_GetVarCustData(This,index,guid,pVarVal) (This)->lpVtbl->GetVarCustData(This,index,guid,pVarVal)
#define ITypeInfo2_GetImplTypeCustData(This,index,guid,pVarVal) (This)->lpVtbl->GetImplTypeCustData(This,index,guid,pVarVal)
#define ITypeInfo2_GetDocumentation2(This,memid,lcid,pbstrHelpString,pdwHelpStringContext,pbstrHelpStringDll) (This)->lpVtbl->GetDocumentation2(This,memid,lcid,pbstrHelpString,pdwHelpStringContext,pbstrHelpStringDll)
#define ITypeInfo2_GetAllCustData(This,pCustData) (This)->lpVtbl->GetAllCustData(This,pCustData)
#define ITypeInfo2_GetAllFuncCustData(This,index,pCustData) (This)->lpVtbl->GetAllFuncCustData(This,index,pCustData)
#define ITypeInfo2_GetAllParamCustData(This,indexFunc,indexParam,pCustData) (This)->lpVtbl->GetAllParamCustData(This,indexFunc,indexParam,pCustData)
#define ITypeInfo2_GetAllVarCustData(This,index,pCustData) (This)->lpVtbl->GetAllVarCustData(This,index,pCustData)
#define ITypeInfo2_GetAllImplTypeCustData(This,index,pCustData) (This)->lpVtbl->GetAllImplTypeCustData(This,index,pCustData)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITypeInfo2_QueryInterface(ITypeInfo2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITypeInfo2_AddRef(ITypeInfo2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITypeInfo2_Release(ITypeInfo2* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypeInfo methods ***/
static FORCEINLINE HRESULT ITypeInfo2_GetTypeAttr(ITypeInfo2* This,TYPEATTR **ppTypeAttr) {
    return This->lpVtbl->GetTypeAttr(This,ppTypeAttr);
}
static FORCEINLINE HRESULT ITypeInfo2_GetTypeComp(ITypeInfo2* This,ITypeComp **ppTComp) {
    return This->lpVtbl->GetTypeComp(This,ppTComp);
}
static FORCEINLINE HRESULT ITypeInfo2_GetFuncDesc(ITypeInfo2* This,UINT index,FUNCDESC **ppFuncDesc) {
    return This->lpVtbl->GetFuncDesc(This,index,ppFuncDesc);
}
static FORCEINLINE HRESULT ITypeInfo2_GetVarDesc(ITypeInfo2* This,UINT index,VARDESC **ppVarDesc) {
    return This->lpVtbl->GetVarDesc(This,index,ppVarDesc);
}
static FORCEINLINE HRESULT ITypeInfo2_GetNames(ITypeInfo2* This,MEMBERID memid,BSTR *rgBstrNames,UINT cMaxNames,UINT *pcNames) {
    return This->lpVtbl->GetNames(This,memid,rgBstrNames,cMaxNames,pcNames);
}
static FORCEINLINE HRESULT ITypeInfo2_GetRefTypeOfImplType(ITypeInfo2* This,UINT index,HREFTYPE *pRefType) {
    return This->lpVtbl->GetRefTypeOfImplType(This,index,pRefType);
}
static FORCEINLINE HRESULT ITypeInfo2_GetImplTypeFlags(ITypeInfo2* This,UINT index,INT *pImplTypeFlags) {
    return This->lpVtbl->GetImplTypeFlags(This,index,pImplTypeFlags);
}
static FORCEINLINE HRESULT ITypeInfo2_GetIDsOfNames(ITypeInfo2* This,LPOLESTR *rgszNames,UINT cNames,MEMBERID *pMemId) {
    return This->lpVtbl->GetIDsOfNames(This,rgszNames,cNames,pMemId);
}
static FORCEINLINE HRESULT ITypeInfo2_Invoke(ITypeInfo2* This,PVOID pvInstance,MEMBERID memid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,pvInstance,memid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
static FORCEINLINE HRESULT ITypeInfo2_GetDocumentation(ITypeInfo2* This,MEMBERID memid,BSTR *pBstrName,BSTR *pBstrDocString,DWORD *pdwHelpContext,BSTR *pBstrHelpFile) {
    return This->lpVtbl->GetDocumentation(This,memid,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile);
}
static FORCEINLINE HRESULT ITypeInfo2_GetDllEntry(ITypeInfo2* This,MEMBERID memid,INVOKEKIND invKind,BSTR *pBstrDllName,BSTR *pBstrName,WORD *pwOrdinal) {
    return This->lpVtbl->GetDllEntry(This,memid,invKind,pBstrDllName,pBstrName,pwOrdinal);
}
static FORCEINLINE HRESULT ITypeInfo2_GetRefTypeInfo(ITypeInfo2* This,HREFTYPE hRefType,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetRefTypeInfo(This,hRefType,ppTInfo);
}
static FORCEINLINE HRESULT ITypeInfo2_AddressOfMember(ITypeInfo2* This,MEMBERID memid,INVOKEKIND invKind,PVOID *ppv) {
    return This->lpVtbl->AddressOfMember(This,memid,invKind,ppv);
}
static FORCEINLINE HRESULT ITypeInfo2_CreateInstance(ITypeInfo2* This,IUnknown *pUnkOuter,REFIID riid,PVOID *ppvObj) {
    return This->lpVtbl->CreateInstance(This,pUnkOuter,riid,ppvObj);
}
static FORCEINLINE HRESULT ITypeInfo2_GetMops(ITypeInfo2* This,MEMBERID memid,BSTR *pBstrMops) {
    return This->lpVtbl->GetMops(This,memid,pBstrMops);
}
static FORCEINLINE HRESULT ITypeInfo2_GetContainingTypeLib(ITypeInfo2* This,ITypeLib **ppTLib,UINT *pIndex) {
    return This->lpVtbl->GetContainingTypeLib(This,ppTLib,pIndex);
}
static FORCEINLINE void ITypeInfo2_ReleaseTypeAttr(ITypeInfo2* This,TYPEATTR *pTypeAttr) {
    This->lpVtbl->ReleaseTypeAttr(This,pTypeAttr);
}
static FORCEINLINE void ITypeInfo2_ReleaseFuncDesc(ITypeInfo2* This,FUNCDESC *pFuncDesc) {
    This->lpVtbl->ReleaseFuncDesc(This,pFuncDesc);
}
static FORCEINLINE void ITypeInfo2_ReleaseVarDesc(ITypeInfo2* This,VARDESC *pVarDesc) {
    This->lpVtbl->ReleaseVarDesc(This,pVarDesc);
}
/*** ITypeInfo2 methods ***/
static FORCEINLINE HRESULT ITypeInfo2_GetTypeKind(ITypeInfo2* This,TYPEKIND *pTypeKind) {
    return This->lpVtbl->GetTypeKind(This,pTypeKind);
}
static FORCEINLINE HRESULT ITypeInfo2_GetTypeFlags(ITypeInfo2* This,ULONG *pTypeFlags) {
    return This->lpVtbl->GetTypeFlags(This,pTypeFlags);
}
static FORCEINLINE HRESULT ITypeInfo2_GetFuncIndexOfMemId(ITypeInfo2* This,MEMBERID memid,INVOKEKIND invKind,UINT *pFuncIndex) {
    return This->lpVtbl->GetFuncIndexOfMemId(This,memid,invKind,pFuncIndex);
}
static FORCEINLINE HRESULT ITypeInfo2_GetVarIndexOfMemId(ITypeInfo2* This,MEMBERID memid,UINT *pVarIndex) {
    return This->lpVtbl->GetVarIndexOfMemId(This,memid,pVarIndex);
}
static FORCEINLINE HRESULT ITypeInfo2_GetCustData(ITypeInfo2* This,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->GetCustData(This,guid,pVarVal);
}
static FORCEINLINE HRESULT ITypeInfo2_GetFuncCustData(ITypeInfo2* This,UINT index,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->GetFuncCustData(This,index,guid,pVarVal);
}
static FORCEINLINE HRESULT ITypeInfo2_GetParamCustData(ITypeInfo2* This,UINT indexFunc,UINT indexParam,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->GetParamCustData(This,indexFunc,indexParam,guid,pVarVal);
}
static FORCEINLINE HRESULT ITypeInfo2_GetVarCustData(ITypeInfo2* This,UINT index,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->GetVarCustData(This,index,guid,pVarVal);
}
static FORCEINLINE HRESULT ITypeInfo2_GetImplTypeCustData(ITypeInfo2* This,UINT index,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->GetImplTypeCustData(This,index,guid,pVarVal);
}
static FORCEINLINE HRESULT ITypeInfo2_GetDocumentation2(ITypeInfo2* This,MEMBERID memid,LCID lcid,BSTR *pbstrHelpString,DWORD *pdwHelpStringContext,BSTR *pbstrHelpStringDll) {
    return This->lpVtbl->GetDocumentation2(This,memid,lcid,pbstrHelpString,pdwHelpStringContext,pbstrHelpStringDll);
}
static FORCEINLINE HRESULT ITypeInfo2_GetAllCustData(ITypeInfo2* This,CUSTDATA *pCustData) {
    return This->lpVtbl->GetAllCustData(This,pCustData);
}
static FORCEINLINE HRESULT ITypeInfo2_GetAllFuncCustData(ITypeInfo2* This,UINT index,CUSTDATA *pCustData) {
    return This->lpVtbl->GetAllFuncCustData(This,index,pCustData);
}
static FORCEINLINE HRESULT ITypeInfo2_GetAllParamCustData(ITypeInfo2* This,UINT indexFunc,UINT indexParam,CUSTDATA *pCustData) {
    return This->lpVtbl->GetAllParamCustData(This,indexFunc,indexParam,pCustData);
}
static FORCEINLINE HRESULT ITypeInfo2_GetAllVarCustData(ITypeInfo2* This,UINT index,CUSTDATA *pCustData) {
    return This->lpVtbl->GetAllVarCustData(This,index,pCustData);
}
static FORCEINLINE HRESULT ITypeInfo2_GetAllImplTypeCustData(ITypeInfo2* This,UINT index,CUSTDATA *pCustData) {
    return This->lpVtbl->GetAllImplTypeCustData(This,index,pCustData);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITypeInfo2_GetTypeKind_Proxy(
    ITypeInfo2* This,
    TYPEKIND *pTypeKind);
void __RPC_STUB ITypeInfo2_GetTypeKind_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetTypeFlags_Proxy(
    ITypeInfo2* This,
    ULONG *pTypeFlags);
void __RPC_STUB ITypeInfo2_GetTypeFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetFuncIndexOfMemId_Proxy(
    ITypeInfo2* This,
    MEMBERID memid,
    INVOKEKIND invKind,
    UINT *pFuncIndex);
void __RPC_STUB ITypeInfo2_GetFuncIndexOfMemId_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetVarIndexOfMemId_Proxy(
    ITypeInfo2* This,
    MEMBERID memid,
    UINT *pVarIndex);
void __RPC_STUB ITypeInfo2_GetVarIndexOfMemId_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetCustData_Proxy(
    ITypeInfo2* This,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ITypeInfo2_GetCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetFuncCustData_Proxy(
    ITypeInfo2* This,
    UINT index,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ITypeInfo2_GetFuncCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetParamCustData_Proxy(
    ITypeInfo2* This,
    UINT indexFunc,
    UINT indexParam,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ITypeInfo2_GetParamCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetVarCustData_Proxy(
    ITypeInfo2* This,
    UINT index,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ITypeInfo2_GetVarCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetImplTypeCustData_Proxy(
    ITypeInfo2* This,
    UINT index,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ITypeInfo2_GetImplTypeCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_RemoteGetDocumentation2_Proxy(
    ITypeInfo2* This,
    MEMBERID memid,
    LCID lcid,
    DWORD refPtrFlags,
    BSTR *pbstrHelpString,
    DWORD *pdwHelpStringContext,
    BSTR *pbstrHelpStringDll);
void __RPC_STUB ITypeInfo2_RemoteGetDocumentation2_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetAllCustData_Proxy(
    ITypeInfo2* This,
    CUSTDATA *pCustData);
void __RPC_STUB ITypeInfo2_GetAllCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetAllFuncCustData_Proxy(
    ITypeInfo2* This,
    UINT index,
    CUSTDATA *pCustData);
void __RPC_STUB ITypeInfo2_GetAllFuncCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetAllParamCustData_Proxy(
    ITypeInfo2* This,
    UINT indexFunc,
    UINT indexParam,
    CUSTDATA *pCustData);
void __RPC_STUB ITypeInfo2_GetAllParamCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetAllVarCustData_Proxy(
    ITypeInfo2* This,
    UINT index,
    CUSTDATA *pCustData);
void __RPC_STUB ITypeInfo2_GetAllVarCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeInfo2_GetAllImplTypeCustData_Proxy(
    ITypeInfo2* This,
    UINT index,
    CUSTDATA *pCustData);
void __RPC_STUB ITypeInfo2_GetAllImplTypeCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK ITypeInfo2_GetDocumentation2_Proxy(
    ITypeInfo2* This,
    MEMBERID memid,
    LCID lcid,
    BSTR *pbstrHelpString,
    DWORD *pdwHelpStringContext,
    BSTR *pbstrHelpStringDll);
HRESULT __RPC_STUB ITypeInfo2_GetDocumentation2_Stub(
    ITypeInfo2* This,
    MEMBERID memid,
    LCID lcid,
    DWORD refPtrFlags,
    BSTR *pbstrHelpString,
    DWORD *pdwHelpStringContext,
    BSTR *pbstrHelpStringDll);

#endif  /* __ITypeInfo2_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * ITypeLib interface
 */
#ifndef __ITypeLib_INTERFACE_DEFINED__
#define __ITypeLib_INTERFACE_DEFINED__

typedef enum tagSYSKIND {
    SYS_WIN16 = 0,
    SYS_WIN32 = 1,
    SYS_MAC = 2,
    SYS_WIN64 = 3
} SYSKIND;

typedef enum tagLIBFLAGS {
    LIBFLAG_FRESTRICTED = 0x1,
    LIBFLAG_FCONTROL = 0x2,
    LIBFLAG_FHIDDEN = 0x4,
    LIBFLAG_FHASDISKIMAGE = 0x8
} LIBFLAGS;

typedef ITypeLib *LPTYPELIB;

typedef struct tagTLIBATTR {
    GUID guid;
    LCID lcid;
    SYSKIND syskind;
    WORD wMajorVerNum;
    WORD wMinorVerNum;
    WORD wLibFlags;
} TLIBATTR;
typedef struct tagTLIBATTR *LPTLIBATTR;

DEFINE_GUID(IID_ITypeLib, 0x00020402, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020402-0000-0000-c000-000000000046")
ITypeLib : public IUnknown
{
    virtual UINT STDMETHODCALLTYPE GetTypeInfoCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeInfo(
        UINT index,
        ITypeInfo **ppTInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeInfoType(
        UINT index,
        TYPEKIND *pTKind) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeInfoOfGuid(
        REFGUID guid,
        ITypeInfo **ppTinfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLibAttr(
        TLIBATTR **ppTLibAttr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeComp(
        ITypeComp **ppTComp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentation(
        INT index,
        BSTR *pBstrName,
        BSTR *pBstrDocString,
        DWORD *pdwHelpContext,
        BSTR *pBstrHelpFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsName(
        LPOLESTR szNameBuf,
        ULONG lHashVal,
        WINBOOL *pfName) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindName(
        LPOLESTR szNameBuf,
        ULONG lHashVal,
        ITypeInfo **ppTInfo,
        MEMBERID *rgMemId,
        USHORT *pcFound) = 0;

    virtual void STDMETHODCALLTYPE ReleaseTLibAttr(
        TLIBATTR *pTLibAttr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITypeLib, 0x00020402, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ITypeLibVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITypeLib* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITypeLib* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITypeLib* This);

    /*** ITypeLib methods ***/
    UINT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITypeLib* This);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITypeLib* This,
        UINT index,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfoType)(
        ITypeLib* This,
        UINT index,
        TYPEKIND *pTKind);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfoOfGuid)(
        ITypeLib* This,
        REFGUID guid,
        ITypeInfo **ppTinfo);

    HRESULT (STDMETHODCALLTYPE *GetLibAttr)(
        ITypeLib* This,
        TLIBATTR **ppTLibAttr);

    HRESULT (STDMETHODCALLTYPE *GetTypeComp)(
        ITypeLib* This,
        ITypeComp **ppTComp);

    HRESULT (STDMETHODCALLTYPE *GetDocumentation)(
        ITypeLib* This,
        INT index,
        BSTR *pBstrName,
        BSTR *pBstrDocString,
        DWORD *pdwHelpContext,
        BSTR *pBstrHelpFile);

    HRESULT (STDMETHODCALLTYPE *IsName)(
        ITypeLib* This,
        LPOLESTR szNameBuf,
        ULONG lHashVal,
        WINBOOL *pfName);

    HRESULT (STDMETHODCALLTYPE *FindName)(
        ITypeLib* This,
        LPOLESTR szNameBuf,
        ULONG lHashVal,
        ITypeInfo **ppTInfo,
        MEMBERID *rgMemId,
        USHORT *pcFound);

    void (STDMETHODCALLTYPE *ReleaseTLibAttr)(
        ITypeLib* This,
        TLIBATTR *pTLibAttr);

    END_INTERFACE
} ITypeLibVtbl;
interface ITypeLib {
    CONST_VTBL ITypeLibVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITypeLib_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITypeLib_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITypeLib_Release(This) (This)->lpVtbl->Release(This)
/*** ITypeLib methods ***/
#define ITypeLib_GetTypeInfoCount(This) (This)->lpVtbl->GetTypeInfoCount(This)
#define ITypeLib_GetTypeInfo(This,index,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,index,ppTInfo)
#define ITypeLib_GetTypeInfoType(This,index,pTKind) (This)->lpVtbl->GetTypeInfoType(This,index,pTKind)
#define ITypeLib_GetTypeInfoOfGuid(This,guid,ppTinfo) (This)->lpVtbl->GetTypeInfoOfGuid(This,guid,ppTinfo)
#define ITypeLib_GetLibAttr(This,ppTLibAttr) (This)->lpVtbl->GetLibAttr(This,ppTLibAttr)
#define ITypeLib_GetTypeComp(This,ppTComp) (This)->lpVtbl->GetTypeComp(This,ppTComp)
#define ITypeLib_GetDocumentation(This,index,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile) (This)->lpVtbl->GetDocumentation(This,index,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile)
#define ITypeLib_IsName(This,szNameBuf,lHashVal,pfName) (This)->lpVtbl->IsName(This,szNameBuf,lHashVal,pfName)
#define ITypeLib_FindName(This,szNameBuf,lHashVal,ppTInfo,rgMemId,pcFound) (This)->lpVtbl->FindName(This,szNameBuf,lHashVal,ppTInfo,rgMemId,pcFound)
#define ITypeLib_ReleaseTLibAttr(This,pTLibAttr) (This)->lpVtbl->ReleaseTLibAttr(This,pTLibAttr)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITypeLib_QueryInterface(ITypeLib* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITypeLib_AddRef(ITypeLib* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITypeLib_Release(ITypeLib* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypeLib methods ***/
static FORCEINLINE UINT ITypeLib_GetTypeInfoCount(ITypeLib* This) {
    return This->lpVtbl->GetTypeInfoCount(This);
}
static FORCEINLINE HRESULT ITypeLib_GetTypeInfo(ITypeLib* This,UINT index,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,index,ppTInfo);
}
static FORCEINLINE HRESULT ITypeLib_GetTypeInfoType(ITypeLib* This,UINT index,TYPEKIND *pTKind) {
    return This->lpVtbl->GetTypeInfoType(This,index,pTKind);
}
static FORCEINLINE HRESULT ITypeLib_GetTypeInfoOfGuid(ITypeLib* This,REFGUID guid,ITypeInfo **ppTinfo) {
    return This->lpVtbl->GetTypeInfoOfGuid(This,guid,ppTinfo);
}
static FORCEINLINE HRESULT ITypeLib_GetLibAttr(ITypeLib* This,TLIBATTR **ppTLibAttr) {
    return This->lpVtbl->GetLibAttr(This,ppTLibAttr);
}
static FORCEINLINE HRESULT ITypeLib_GetTypeComp(ITypeLib* This,ITypeComp **ppTComp) {
    return This->lpVtbl->GetTypeComp(This,ppTComp);
}
static FORCEINLINE HRESULT ITypeLib_GetDocumentation(ITypeLib* This,INT index,BSTR *pBstrName,BSTR *pBstrDocString,DWORD *pdwHelpContext,BSTR *pBstrHelpFile) {
    return This->lpVtbl->GetDocumentation(This,index,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile);
}
static FORCEINLINE HRESULT ITypeLib_IsName(ITypeLib* This,LPOLESTR szNameBuf,ULONG lHashVal,WINBOOL *pfName) {
    return This->lpVtbl->IsName(This,szNameBuf,lHashVal,pfName);
}
static FORCEINLINE HRESULT ITypeLib_FindName(ITypeLib* This,LPOLESTR szNameBuf,ULONG lHashVal,ITypeInfo **ppTInfo,MEMBERID *rgMemId,USHORT *pcFound) {
    return This->lpVtbl->FindName(This,szNameBuf,lHashVal,ppTInfo,rgMemId,pcFound);
}
static FORCEINLINE void ITypeLib_ReleaseTLibAttr(ITypeLib* This,TLIBATTR *pTLibAttr) {
    This->lpVtbl->ReleaseTLibAttr(This,pTLibAttr);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITypeLib_RemoteGetTypeInfoCount_Proxy(
    ITypeLib* This,
    UINT *pcTInfo);
void __RPC_STUB ITypeLib_RemoteGetTypeInfoCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_GetTypeInfo_Proxy(
    ITypeLib* This,
    UINT index,
    ITypeInfo **ppTInfo);
void __RPC_STUB ITypeLib_GetTypeInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_GetTypeInfoType_Proxy(
    ITypeLib* This,
    UINT index,
    TYPEKIND *pTKind);
void __RPC_STUB ITypeLib_GetTypeInfoType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_GetTypeInfoOfGuid_Proxy(
    ITypeLib* This,
    REFGUID guid,
    ITypeInfo **ppTinfo);
void __RPC_STUB ITypeLib_GetTypeInfoOfGuid_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_RemoteGetLibAttr_Proxy(
    ITypeLib* This,
    LPTLIBATTR *ppTLibAttr,
    CLEANLOCALSTORAGE *pDummy);
void __RPC_STUB ITypeLib_RemoteGetLibAttr_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_GetTypeComp_Proxy(
    ITypeLib* This,
    ITypeComp **ppTComp);
void __RPC_STUB ITypeLib_GetTypeComp_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_RemoteGetDocumentation_Proxy(
    ITypeLib* This,
    INT index,
    DWORD refPtrFlags,
    BSTR *pBstrName,
    BSTR *pBstrDocString,
    DWORD *pdwHelpContext,
    BSTR *pBstrHelpFile);
void __RPC_STUB ITypeLib_RemoteGetDocumentation_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_RemoteIsName_Proxy(
    ITypeLib* This,
    LPOLESTR szNameBuf,
    ULONG lHashVal,
    WINBOOL *pfName,
    BSTR *pBstrLibName);
void __RPC_STUB ITypeLib_RemoteIsName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_RemoteFindName_Proxy(
    ITypeLib* This,
    LPOLESTR szNameBuf,
    ULONG lHashVal,
    ITypeInfo **ppTInfo,
    MEMBERID *rgMemId,
    USHORT *pcFound,
    BSTR *pBstrLibName);
void __RPC_STUB ITypeLib_RemoteFindName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib_LocalReleaseTLibAttr_Proxy(
    ITypeLib* This);
void __RPC_STUB ITypeLib_LocalReleaseTLibAttr_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
UINT CALLBACK ITypeLib_GetTypeInfoCount_Proxy(
    ITypeLib* This);
HRESULT __RPC_STUB ITypeLib_GetTypeInfoCount_Stub(
    ITypeLib* This,
    UINT *pcTInfo);
HRESULT CALLBACK ITypeLib_GetLibAttr_Proxy(
    ITypeLib* This,
    TLIBATTR **ppTLibAttr);
HRESULT __RPC_STUB ITypeLib_GetLibAttr_Stub(
    ITypeLib* This,
    LPTLIBATTR *ppTLibAttr,
    CLEANLOCALSTORAGE *pDummy);
HRESULT CALLBACK ITypeLib_GetDocumentation_Proxy(
    ITypeLib* This,
    INT index,
    BSTR *pBstrName,
    BSTR *pBstrDocString,
    DWORD *pdwHelpContext,
    BSTR *pBstrHelpFile);
HRESULT __RPC_STUB ITypeLib_GetDocumentation_Stub(
    ITypeLib* This,
    INT index,
    DWORD refPtrFlags,
    BSTR *pBstrName,
    BSTR *pBstrDocString,
    DWORD *pdwHelpContext,
    BSTR *pBstrHelpFile);
HRESULT CALLBACK ITypeLib_IsName_Proxy(
    ITypeLib* This,
    LPOLESTR szNameBuf,
    ULONG lHashVal,
    WINBOOL *pfName);
HRESULT __RPC_STUB ITypeLib_IsName_Stub(
    ITypeLib* This,
    LPOLESTR szNameBuf,
    ULONG lHashVal,
    WINBOOL *pfName,
    BSTR *pBstrLibName);
HRESULT CALLBACK ITypeLib_FindName_Proxy(
    ITypeLib* This,
    LPOLESTR szNameBuf,
    ULONG lHashVal,
    ITypeInfo **ppTInfo,
    MEMBERID *rgMemId,
    USHORT *pcFound);
HRESULT __RPC_STUB ITypeLib_FindName_Stub(
    ITypeLib* This,
    LPOLESTR szNameBuf,
    ULONG lHashVal,
    ITypeInfo **ppTInfo,
    MEMBERID *rgMemId,
    USHORT *pcFound,
    BSTR *pBstrLibName);
void CALLBACK ITypeLib_ReleaseTLibAttr_Proxy(
    ITypeLib* This,
    TLIBATTR *pTLibAttr);
HRESULT __RPC_STUB ITypeLib_ReleaseTLibAttr_Stub(
    ITypeLib* This);

#endif  /* __ITypeLib_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * ITypeLib2 interface
 */
#ifndef __ITypeLib2_INTERFACE_DEFINED__
#define __ITypeLib2_INTERFACE_DEFINED__

typedef ITypeLib2 *LPTYPELIB2;

DEFINE_GUID(IID_ITypeLib2, 0x00020411, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020411-0000-0000-c000-000000000046")
ITypeLib2 : public ITypeLib
{
    virtual HRESULT STDMETHODCALLTYPE GetCustData(
        REFGUID guid,
        VARIANT *pVarVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLibStatistics(
        ULONG *pcUniqueNames,
        ULONG *pcchUniqueNames) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentation2(
        INT index,
        LCID lcid,
        BSTR *pbstrHelpString,
        DWORD *pdwHelpStringContext,
        BSTR *pbstrHelpStringDll) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllCustData(
        CUSTDATA *pCustData) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITypeLib2, 0x00020411, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ITypeLib2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITypeLib2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITypeLib2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITypeLib2* This);

    /*** ITypeLib methods ***/
    UINT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITypeLib2* This);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITypeLib2* This,
        UINT index,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfoType)(
        ITypeLib2* This,
        UINT index,
        TYPEKIND *pTKind);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfoOfGuid)(
        ITypeLib2* This,
        REFGUID guid,
        ITypeInfo **ppTinfo);

    HRESULT (STDMETHODCALLTYPE *GetLibAttr)(
        ITypeLib2* This,
        TLIBATTR **ppTLibAttr);

    HRESULT (STDMETHODCALLTYPE *GetTypeComp)(
        ITypeLib2* This,
        ITypeComp **ppTComp);

    HRESULT (STDMETHODCALLTYPE *GetDocumentation)(
        ITypeLib2* This,
        INT index,
        BSTR *pBstrName,
        BSTR *pBstrDocString,
        DWORD *pdwHelpContext,
        BSTR *pBstrHelpFile);

    HRESULT (STDMETHODCALLTYPE *IsName)(
        ITypeLib2* This,
        LPOLESTR szNameBuf,
        ULONG lHashVal,
        WINBOOL *pfName);

    HRESULT (STDMETHODCALLTYPE *FindName)(
        ITypeLib2* This,
        LPOLESTR szNameBuf,
        ULONG lHashVal,
        ITypeInfo **ppTInfo,
        MEMBERID *rgMemId,
        USHORT *pcFound);

    void (STDMETHODCALLTYPE *ReleaseTLibAttr)(
        ITypeLib2* This,
        TLIBATTR *pTLibAttr);

    /*** ITypeLib2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCustData)(
        ITypeLib2* This,
        REFGUID guid,
        VARIANT *pVarVal);

    HRESULT (STDMETHODCALLTYPE *GetLibStatistics)(
        ITypeLib2* This,
        ULONG *pcUniqueNames,
        ULONG *pcchUniqueNames);

    HRESULT (STDMETHODCALLTYPE *GetDocumentation2)(
        ITypeLib2* This,
        INT index,
        LCID lcid,
        BSTR *pbstrHelpString,
        DWORD *pdwHelpStringContext,
        BSTR *pbstrHelpStringDll);

    HRESULT (STDMETHODCALLTYPE *GetAllCustData)(
        ITypeLib2* This,
        CUSTDATA *pCustData);

    END_INTERFACE
} ITypeLib2Vtbl;
interface ITypeLib2 {
    CONST_VTBL ITypeLib2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITypeLib2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITypeLib2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITypeLib2_Release(This) (This)->lpVtbl->Release(This)
/*** ITypeLib methods ***/
#define ITypeLib2_GetTypeInfoCount(This) (This)->lpVtbl->GetTypeInfoCount(This)
#define ITypeLib2_GetTypeInfo(This,index,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,index,ppTInfo)
#define ITypeLib2_GetTypeInfoType(This,index,pTKind) (This)->lpVtbl->GetTypeInfoType(This,index,pTKind)
#define ITypeLib2_GetTypeInfoOfGuid(This,guid,ppTinfo) (This)->lpVtbl->GetTypeInfoOfGuid(This,guid,ppTinfo)
#define ITypeLib2_GetLibAttr(This,ppTLibAttr) (This)->lpVtbl->GetLibAttr(This,ppTLibAttr)
#define ITypeLib2_GetTypeComp(This,ppTComp) (This)->lpVtbl->GetTypeComp(This,ppTComp)
#define ITypeLib2_GetDocumentation(This,index,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile) (This)->lpVtbl->GetDocumentation(This,index,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile)
#define ITypeLib2_IsName(This,szNameBuf,lHashVal,pfName) (This)->lpVtbl->IsName(This,szNameBuf,lHashVal,pfName)
#define ITypeLib2_FindName(This,szNameBuf,lHashVal,ppTInfo,rgMemId,pcFound) (This)->lpVtbl->FindName(This,szNameBuf,lHashVal,ppTInfo,rgMemId,pcFound)
#define ITypeLib2_ReleaseTLibAttr(This,pTLibAttr) (This)->lpVtbl->ReleaseTLibAttr(This,pTLibAttr)
/*** ITypeLib2 methods ***/
#define ITypeLib2_GetCustData(This,guid,pVarVal) (This)->lpVtbl->GetCustData(This,guid,pVarVal)
#define ITypeLib2_GetLibStatistics(This,pcUniqueNames,pcchUniqueNames) (This)->lpVtbl->GetLibStatistics(This,pcUniqueNames,pcchUniqueNames)
#define ITypeLib2_GetDocumentation2(This,index,lcid,pbstrHelpString,pdwHelpStringContext,pbstrHelpStringDll) (This)->lpVtbl->GetDocumentation2(This,index,lcid,pbstrHelpString,pdwHelpStringContext,pbstrHelpStringDll)
#define ITypeLib2_GetAllCustData(This,pCustData) (This)->lpVtbl->GetAllCustData(This,pCustData)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITypeLib2_QueryInterface(ITypeLib2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITypeLib2_AddRef(ITypeLib2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITypeLib2_Release(ITypeLib2* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypeLib methods ***/
static FORCEINLINE UINT ITypeLib2_GetTypeInfoCount(ITypeLib2* This) {
    return This->lpVtbl->GetTypeInfoCount(This);
}
static FORCEINLINE HRESULT ITypeLib2_GetTypeInfo(ITypeLib2* This,UINT index,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,index,ppTInfo);
}
static FORCEINLINE HRESULT ITypeLib2_GetTypeInfoType(ITypeLib2* This,UINT index,TYPEKIND *pTKind) {
    return This->lpVtbl->GetTypeInfoType(This,index,pTKind);
}
static FORCEINLINE HRESULT ITypeLib2_GetTypeInfoOfGuid(ITypeLib2* This,REFGUID guid,ITypeInfo **ppTinfo) {
    return This->lpVtbl->GetTypeInfoOfGuid(This,guid,ppTinfo);
}
static FORCEINLINE HRESULT ITypeLib2_GetLibAttr(ITypeLib2* This,TLIBATTR **ppTLibAttr) {
    return This->lpVtbl->GetLibAttr(This,ppTLibAttr);
}
static FORCEINLINE HRESULT ITypeLib2_GetTypeComp(ITypeLib2* This,ITypeComp **ppTComp) {
    return This->lpVtbl->GetTypeComp(This,ppTComp);
}
static FORCEINLINE HRESULT ITypeLib2_GetDocumentation(ITypeLib2* This,INT index,BSTR *pBstrName,BSTR *pBstrDocString,DWORD *pdwHelpContext,BSTR *pBstrHelpFile) {
    return This->lpVtbl->GetDocumentation(This,index,pBstrName,pBstrDocString,pdwHelpContext,pBstrHelpFile);
}
static FORCEINLINE HRESULT ITypeLib2_IsName(ITypeLib2* This,LPOLESTR szNameBuf,ULONG lHashVal,WINBOOL *pfName) {
    return This->lpVtbl->IsName(This,szNameBuf,lHashVal,pfName);
}
static FORCEINLINE HRESULT ITypeLib2_FindName(ITypeLib2* This,LPOLESTR szNameBuf,ULONG lHashVal,ITypeInfo **ppTInfo,MEMBERID *rgMemId,USHORT *pcFound) {
    return This->lpVtbl->FindName(This,szNameBuf,lHashVal,ppTInfo,rgMemId,pcFound);
}
static FORCEINLINE void ITypeLib2_ReleaseTLibAttr(ITypeLib2* This,TLIBATTR *pTLibAttr) {
    This->lpVtbl->ReleaseTLibAttr(This,pTLibAttr);
}
/*** ITypeLib2 methods ***/
static FORCEINLINE HRESULT ITypeLib2_GetCustData(ITypeLib2* This,REFGUID guid,VARIANT *pVarVal) {
    return This->lpVtbl->GetCustData(This,guid,pVarVal);
}
static FORCEINLINE HRESULT ITypeLib2_GetLibStatistics(ITypeLib2* This,ULONG *pcUniqueNames,ULONG *pcchUniqueNames) {
    return This->lpVtbl->GetLibStatistics(This,pcUniqueNames,pcchUniqueNames);
}
static FORCEINLINE HRESULT ITypeLib2_GetDocumentation2(ITypeLib2* This,INT index,LCID lcid,BSTR *pbstrHelpString,DWORD *pdwHelpStringContext,BSTR *pbstrHelpStringDll) {
    return This->lpVtbl->GetDocumentation2(This,index,lcid,pbstrHelpString,pdwHelpStringContext,pbstrHelpStringDll);
}
static FORCEINLINE HRESULT ITypeLib2_GetAllCustData(ITypeLib2* This,CUSTDATA *pCustData) {
    return This->lpVtbl->GetAllCustData(This,pCustData);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITypeLib2_GetCustData_Proxy(
    ITypeLib2* This,
    REFGUID guid,
    VARIANT *pVarVal);
void __RPC_STUB ITypeLib2_GetCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib2_RemoteGetLibStatistics_Proxy(
    ITypeLib2* This,
    ULONG *pcUniqueNames,
    ULONG *pcchUniqueNames);
void __RPC_STUB ITypeLib2_RemoteGetLibStatistics_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib2_RemoteGetDocumentation2_Proxy(
    ITypeLib2* This,
    INT index,
    LCID lcid,
    DWORD refPtrFlags,
    BSTR *pbstrHelpString,
    DWORD *pdwHelpStringContext,
    BSTR *pbstrHelpStringDll);
void __RPC_STUB ITypeLib2_RemoteGetDocumentation2_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeLib2_GetAllCustData_Proxy(
    ITypeLib2* This,
    CUSTDATA *pCustData);
void __RPC_STUB ITypeLib2_GetAllCustData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK ITypeLib2_GetLibStatistics_Proxy(
    ITypeLib2* This,
    ULONG *pcUniqueNames,
    ULONG *pcchUniqueNames);
HRESULT __RPC_STUB ITypeLib2_GetLibStatistics_Stub(
    ITypeLib2* This,
    ULONG *pcUniqueNames,
    ULONG *pcchUniqueNames);
HRESULT CALLBACK ITypeLib2_GetDocumentation2_Proxy(
    ITypeLib2* This,
    INT index,
    LCID lcid,
    BSTR *pbstrHelpString,
    DWORD *pdwHelpStringContext,
    BSTR *pbstrHelpStringDll);
HRESULT __RPC_STUB ITypeLib2_GetDocumentation2_Stub(
    ITypeLib2* This,
    INT index,
    LCID lcid,
    DWORD refPtrFlags,
    BSTR *pbstrHelpString,
    DWORD *pdwHelpStringContext,
    BSTR *pbstrHelpStringDll);

#endif  /* __ITypeLib2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ITypeChangeEvents interface
 */
#ifndef __ITypeChangeEvents_INTERFACE_DEFINED__
#define __ITypeChangeEvents_INTERFACE_DEFINED__

typedef ITypeChangeEvents *LPTYPECHANGEEVENTS;

typedef enum tagCHANGEKIND {
    CHANGEKIND_ADDMEMBER = 0,
    CHANGEKIND_DELETEMEMBER = 1,
    CHANGEKIND_SETNAMES = 2,
    CHANGEKIND_SETDOCUMENTATION = 3,
    CHANGEKIND_GENERAL = 4,
    CHANGEKIND_INVALIDATE = 5,
    CHANGEKIND_CHANGEFAILED = 6,
    CHANGEKIND_MAX = 7
} CHANGEKIND;

DEFINE_GUID(IID_ITypeChangeEvents, 0x00020410, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00020410-0000-0000-c000-000000000046")
ITypeChangeEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RequestTypeChange(
        CHANGEKIND changeKind,
        ITypeInfo *pTInfoBefore,
        LPOLESTR pStrName,
        INT *pfCancel) = 0;

    virtual HRESULT STDMETHODCALLTYPE AfterTypeChange(
        CHANGEKIND changeKind,
        ITypeInfo *pTInfoAfter,
        LPOLESTR pStrName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITypeChangeEvents, 0x00020410, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ITypeChangeEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITypeChangeEvents* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITypeChangeEvents* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITypeChangeEvents* This);

    /*** ITypeChangeEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *RequestTypeChange)(
        ITypeChangeEvents* This,
        CHANGEKIND changeKind,
        ITypeInfo *pTInfoBefore,
        LPOLESTR pStrName,
        INT *pfCancel);

    HRESULT (STDMETHODCALLTYPE *AfterTypeChange)(
        ITypeChangeEvents* This,
        CHANGEKIND changeKind,
        ITypeInfo *pTInfoAfter,
        LPOLESTR pStrName);

    END_INTERFACE
} ITypeChangeEventsVtbl;
interface ITypeChangeEvents {
    CONST_VTBL ITypeChangeEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITypeChangeEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITypeChangeEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITypeChangeEvents_Release(This) (This)->lpVtbl->Release(This)
/*** ITypeChangeEvents methods ***/
#define ITypeChangeEvents_RequestTypeChange(This,changeKind,pTInfoBefore,pStrName,pfCancel) (This)->lpVtbl->RequestTypeChange(This,changeKind,pTInfoBefore,pStrName,pfCancel)
#define ITypeChangeEvents_AfterTypeChange(This,changeKind,pTInfoAfter,pStrName) (This)->lpVtbl->AfterTypeChange(This,changeKind,pTInfoAfter,pStrName)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITypeChangeEvents_QueryInterface(ITypeChangeEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITypeChangeEvents_AddRef(ITypeChangeEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITypeChangeEvents_Release(ITypeChangeEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypeChangeEvents methods ***/
static FORCEINLINE HRESULT ITypeChangeEvents_RequestTypeChange(ITypeChangeEvents* This,CHANGEKIND changeKind,ITypeInfo *pTInfoBefore,LPOLESTR pStrName,INT *pfCancel) {
    return This->lpVtbl->RequestTypeChange(This,changeKind,pTInfoBefore,pStrName,pfCancel);
}
static FORCEINLINE HRESULT ITypeChangeEvents_AfterTypeChange(ITypeChangeEvents* This,CHANGEKIND changeKind,ITypeInfo *pTInfoAfter,LPOLESTR pStrName) {
    return This->lpVtbl->AfterTypeChange(This,changeKind,pTInfoAfter,pStrName);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITypeChangeEvents_RequestTypeChange_Proxy(
    ITypeChangeEvents* This,
    CHANGEKIND changeKind,
    ITypeInfo *pTInfoBefore,
    LPOLESTR pStrName,
    INT *pfCancel);
void __RPC_STUB ITypeChangeEvents_RequestTypeChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeChangeEvents_AfterTypeChange_Proxy(
    ITypeChangeEvents* This,
    CHANGEKIND changeKind,
    ITypeInfo *pTInfoAfter,
    LPOLESTR pStrName);
void __RPC_STUB ITypeChangeEvents_AfterTypeChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ITypeChangeEvents_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IErrorInfo interface
 */
#ifndef __IErrorInfo_INTERFACE_DEFINED__
#define __IErrorInfo_INTERFACE_DEFINED__

typedef IErrorInfo *LPERRORINFO;

DEFINE_GUID(IID_IErrorInfo, 0x1cf2b120, 0x547d, 0x101b, 0x8e,0x65, 0x08,0x00,0x2b,0x2b,0xd1,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1cf2b120-547d-101b-8e65-08002b2bd119")
IErrorInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetGUID(
        GUID *pGUID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSource(
        BSTR *pBstrSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        BSTR *pBstrDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHelpFile(
        BSTR *pBstrHelpFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHelpContext(
        DWORD *pdwHelpContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IErrorInfo, 0x1cf2b120, 0x547d, 0x101b, 0x8e,0x65, 0x08,0x00,0x2b,0x2b,0xd1,0x19)
#endif
#else
typedef struct IErrorInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IErrorInfo* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IErrorInfo* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IErrorInfo* This);

    /*** IErrorInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IErrorInfo* This,
        GUID *pGUID);

    HRESULT (STDMETHODCALLTYPE *GetSource)(
        IErrorInfo* This,
        BSTR *pBstrSource);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IErrorInfo* This,
        BSTR *pBstrDescription);

    HRESULT (STDMETHODCALLTYPE *GetHelpFile)(
        IErrorInfo* This,
        BSTR *pBstrHelpFile);

    HRESULT (STDMETHODCALLTYPE *GetHelpContext)(
        IErrorInfo* This,
        DWORD *pdwHelpContext);

    END_INTERFACE
} IErrorInfoVtbl;
interface IErrorInfo {
    CONST_VTBL IErrorInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IErrorInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IErrorInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IErrorInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IErrorInfo methods ***/
#define IErrorInfo_GetGUID(This,pGUID) (This)->lpVtbl->GetGUID(This,pGUID)
#define IErrorInfo_GetSource(This,pBstrSource) (This)->lpVtbl->GetSource(This,pBstrSource)
#define IErrorInfo_GetDescription(This,pBstrDescription) (This)->lpVtbl->GetDescription(This,pBstrDescription)
#define IErrorInfo_GetHelpFile(This,pBstrHelpFile) (This)->lpVtbl->GetHelpFile(This,pBstrHelpFile)
#define IErrorInfo_GetHelpContext(This,pdwHelpContext) (This)->lpVtbl->GetHelpContext(This,pdwHelpContext)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IErrorInfo_QueryInterface(IErrorInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IErrorInfo_AddRef(IErrorInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IErrorInfo_Release(IErrorInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IErrorInfo methods ***/
static FORCEINLINE HRESULT IErrorInfo_GetGUID(IErrorInfo* This,GUID *pGUID) {
    return This->lpVtbl->GetGUID(This,pGUID);
}
static FORCEINLINE HRESULT IErrorInfo_GetSource(IErrorInfo* This,BSTR *pBstrSource) {
    return This->lpVtbl->GetSource(This,pBstrSource);
}
static FORCEINLINE HRESULT IErrorInfo_GetDescription(IErrorInfo* This,BSTR *pBstrDescription) {
    return This->lpVtbl->GetDescription(This,pBstrDescription);
}
static FORCEINLINE HRESULT IErrorInfo_GetHelpFile(IErrorInfo* This,BSTR *pBstrHelpFile) {
    return This->lpVtbl->GetHelpFile(This,pBstrHelpFile);
}
static FORCEINLINE HRESULT IErrorInfo_GetHelpContext(IErrorInfo* This,DWORD *pdwHelpContext) {
    return This->lpVtbl->GetHelpContext(This,pdwHelpContext);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IErrorInfo_GetGUID_Proxy(
    IErrorInfo* This,
    GUID *pGUID);
void __RPC_STUB IErrorInfo_GetGUID_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IErrorInfo_GetSource_Proxy(
    IErrorInfo* This,
    BSTR *pBstrSource);
void __RPC_STUB IErrorInfo_GetSource_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IErrorInfo_GetDescription_Proxy(
    IErrorInfo* This,
    BSTR *pBstrDescription);
void __RPC_STUB IErrorInfo_GetDescription_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IErrorInfo_GetHelpFile_Proxy(
    IErrorInfo* This,
    BSTR *pBstrHelpFile);
void __RPC_STUB IErrorInfo_GetHelpFile_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IErrorInfo_GetHelpContext_Proxy(
    IErrorInfo* This,
    DWORD *pdwHelpContext);
void __RPC_STUB IErrorInfo_GetHelpContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IErrorInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICreateErrorInfo interface
 */
#ifndef __ICreateErrorInfo_INTERFACE_DEFINED__
#define __ICreateErrorInfo_INTERFACE_DEFINED__

typedef ICreateErrorInfo *LPCREATEERRORINFO;

DEFINE_GUID(IID_ICreateErrorInfo, 0x22f03340, 0x547d, 0x101b, 0x8e,0x65, 0x08,0x00,0x2b,0x2b,0xd1,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("22f03340-547d-101b-8e65-08002b2bd119")
ICreateErrorInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetGUID(
        REFGUID rguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSource(
        LPOLESTR szSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDescription(
        LPOLESTR szDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHelpFile(
        LPOLESTR szHelpFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHelpContext(
        DWORD dwHelpContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICreateErrorInfo, 0x22f03340, 0x547d, 0x101b, 0x8e,0x65, 0x08,0x00,0x2b,0x2b,0xd1,0x19)
#endif
#else
typedef struct ICreateErrorInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICreateErrorInfo* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICreateErrorInfo* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICreateErrorInfo* This);

    /*** ICreateErrorInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        ICreateErrorInfo* This,
        REFGUID rguid);

    HRESULT (STDMETHODCALLTYPE *SetSource)(
        ICreateErrorInfo* This,
        LPOLESTR szSource);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        ICreateErrorInfo* This,
        LPOLESTR szDescription);

    HRESULT (STDMETHODCALLTYPE *SetHelpFile)(
        ICreateErrorInfo* This,
        LPOLESTR szHelpFile);

    HRESULT (STDMETHODCALLTYPE *SetHelpContext)(
        ICreateErrorInfo* This,
        DWORD dwHelpContext);

    END_INTERFACE
} ICreateErrorInfoVtbl;
interface ICreateErrorInfo {
    CONST_VTBL ICreateErrorInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICreateErrorInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICreateErrorInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICreateErrorInfo_Release(This) (This)->lpVtbl->Release(This)
/*** ICreateErrorInfo methods ***/
#define ICreateErrorInfo_SetGUID(This,rguid) (This)->lpVtbl->SetGUID(This,rguid)
#define ICreateErrorInfo_SetSource(This,szSource) (This)->lpVtbl->SetSource(This,szSource)
#define ICreateErrorInfo_SetDescription(This,szDescription) (This)->lpVtbl->SetDescription(This,szDescription)
#define ICreateErrorInfo_SetHelpFile(This,szHelpFile) (This)->lpVtbl->SetHelpFile(This,szHelpFile)
#define ICreateErrorInfo_SetHelpContext(This,dwHelpContext) (This)->lpVtbl->SetHelpContext(This,dwHelpContext)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICreateErrorInfo_QueryInterface(ICreateErrorInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICreateErrorInfo_AddRef(ICreateErrorInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICreateErrorInfo_Release(ICreateErrorInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** ICreateErrorInfo methods ***/
static FORCEINLINE HRESULT ICreateErrorInfo_SetGUID(ICreateErrorInfo* This,REFGUID rguid) {
    return This->lpVtbl->SetGUID(This,rguid);
}
static FORCEINLINE HRESULT ICreateErrorInfo_SetSource(ICreateErrorInfo* This,LPOLESTR szSource) {
    return This->lpVtbl->SetSource(This,szSource);
}
static FORCEINLINE HRESULT ICreateErrorInfo_SetDescription(ICreateErrorInfo* This,LPOLESTR szDescription) {
    return This->lpVtbl->SetDescription(This,szDescription);
}
static FORCEINLINE HRESULT ICreateErrorInfo_SetHelpFile(ICreateErrorInfo* This,LPOLESTR szHelpFile) {
    return This->lpVtbl->SetHelpFile(This,szHelpFile);
}
static FORCEINLINE HRESULT ICreateErrorInfo_SetHelpContext(ICreateErrorInfo* This,DWORD dwHelpContext) {
    return This->lpVtbl->SetHelpContext(This,dwHelpContext);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ICreateErrorInfo_SetGUID_Proxy(
    ICreateErrorInfo* This,
    REFGUID rguid);
void __RPC_STUB ICreateErrorInfo_SetGUID_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateErrorInfo_SetSource_Proxy(
    ICreateErrorInfo* This,
    LPOLESTR szSource);
void __RPC_STUB ICreateErrorInfo_SetSource_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateErrorInfo_SetDescription_Proxy(
    ICreateErrorInfo* This,
    LPOLESTR szDescription);
void __RPC_STUB ICreateErrorInfo_SetDescription_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateErrorInfo_SetHelpFile_Proxy(
    ICreateErrorInfo* This,
    LPOLESTR szHelpFile);
void __RPC_STUB ICreateErrorInfo_SetHelpFile_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICreateErrorInfo_SetHelpContext_Proxy(
    ICreateErrorInfo* This,
    DWORD dwHelpContext);
void __RPC_STUB ICreateErrorInfo_SetHelpContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ICreateErrorInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISupportErrorInfo interface
 */
#ifndef __ISupportErrorInfo_INTERFACE_DEFINED__
#define __ISupportErrorInfo_INTERFACE_DEFINED__

typedef ISupportErrorInfo *LPSUPPORTERRORINFO;

DEFINE_GUID(IID_ISupportErrorInfo, 0xdf0b3d60, 0x548f, 0x101b, 0x8e,0x65, 0x08,0x00,0x2b,0x2b,0xd1,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("df0b3d60-548f-101b-8e65-08002b2bd119")
ISupportErrorInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InterfaceSupportsErrorInfo(
        REFIID riid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISupportErrorInfo, 0xdf0b3d60, 0x548f, 0x101b, 0x8e,0x65, 0x08,0x00,0x2b,0x2b,0xd1,0x19)
#endif
#else
typedef struct ISupportErrorInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISupportErrorInfo* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISupportErrorInfo* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISupportErrorInfo* This);

    /*** ISupportErrorInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *InterfaceSupportsErrorInfo)(
        ISupportErrorInfo* This,
        REFIID riid);

    END_INTERFACE
} ISupportErrorInfoVtbl;
interface ISupportErrorInfo {
    CONST_VTBL ISupportErrorInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISupportErrorInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISupportErrorInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISupportErrorInfo_Release(This) (This)->lpVtbl->Release(This)
/*** ISupportErrorInfo methods ***/
#define ISupportErrorInfo_InterfaceSupportsErrorInfo(This,riid) (This)->lpVtbl->InterfaceSupportsErrorInfo(This,riid)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISupportErrorInfo_QueryInterface(ISupportErrorInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISupportErrorInfo_AddRef(ISupportErrorInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISupportErrorInfo_Release(ISupportErrorInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** ISupportErrorInfo methods ***/
static FORCEINLINE HRESULT ISupportErrorInfo_InterfaceSupportsErrorInfo(ISupportErrorInfo* This,REFIID riid) {
    return This->lpVtbl->InterfaceSupportsErrorInfo(This,riid);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISupportErrorInfo_InterfaceSupportsErrorInfo_Proxy(
    ISupportErrorInfo* This,
    REFIID riid);
void __RPC_STUB ISupportErrorInfo_InterfaceSupportsErrorInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ISupportErrorInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ITypeFactory interface
 */
#ifndef __ITypeFactory_INTERFACE_DEFINED__
#define __ITypeFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITypeFactory, 0x0000002e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000002e-0000-0000-c000-000000000046")
ITypeFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateFromTypeInfo(
        ITypeInfo *pTypeInfo,
        REFIID riid,
        IUnknown **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITypeFactory, 0x0000002e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ITypeFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITypeFactory* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITypeFactory* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITypeFactory* This);

    /*** ITypeFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFromTypeInfo)(
        ITypeFactory* This,
        ITypeInfo *pTypeInfo,
        REFIID riid,
        IUnknown **ppv);

    END_INTERFACE
} ITypeFactoryVtbl;
interface ITypeFactory {
    CONST_VTBL ITypeFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITypeFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITypeFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITypeFactory_Release(This) (This)->lpVtbl->Release(This)
/*** ITypeFactory methods ***/
#define ITypeFactory_CreateFromTypeInfo(This,pTypeInfo,riid,ppv) (This)->lpVtbl->CreateFromTypeInfo(This,pTypeInfo,riid,ppv)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITypeFactory_QueryInterface(ITypeFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITypeFactory_AddRef(ITypeFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITypeFactory_Release(ITypeFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypeFactory methods ***/
static FORCEINLINE HRESULT ITypeFactory_CreateFromTypeInfo(ITypeFactory* This,ITypeInfo *pTypeInfo,REFIID riid,IUnknown **ppv) {
    return This->lpVtbl->CreateFromTypeInfo(This,pTypeInfo,riid,ppv);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITypeFactory_CreateFromTypeInfo_Proxy(
    ITypeFactory* This,
    ITypeInfo *pTypeInfo,
    REFIID riid,
    IUnknown **ppv);
void __RPC_STUB ITypeFactory_CreateFromTypeInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ITypeFactory_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ITypeMarshal interface
 */
#ifndef __ITypeMarshal_INTERFACE_DEFINED__
#define __ITypeMarshal_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITypeMarshal, 0x0000002d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000002d-0000-0000-c000-000000000046")
ITypeMarshal : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Size(
        PVOID pvType,
        DWORD dwDestContext,
        PVOID pvDestContext,
        ULONG *pSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE Marshal(
        PVOID pvType,
        DWORD dwDestContext,
        PVOID pvDestContext,
        ULONG cbBufferLength,
        BYTE *pBuffer,
        ULONG *pcbWritten) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unmarshal(
        PVOID pvType,
        DWORD dwFlags,
        ULONG cbBufferLength,
        BYTE *pBuffer,
        ULONG *pcbRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE Free(
        PVOID pvType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITypeMarshal, 0x0000002d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ITypeMarshalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITypeMarshal* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITypeMarshal* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITypeMarshal* This);

    /*** ITypeMarshal methods ***/
    HRESULT (STDMETHODCALLTYPE *Size)(
        ITypeMarshal* This,
        PVOID pvType,
        DWORD dwDestContext,
        PVOID pvDestContext,
        ULONG *pSize);

    HRESULT (STDMETHODCALLTYPE *Marshal)(
        ITypeMarshal* This,
        PVOID pvType,
        DWORD dwDestContext,
        PVOID pvDestContext,
        ULONG cbBufferLength,
        BYTE *pBuffer,
        ULONG *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Unmarshal)(
        ITypeMarshal* This,
        PVOID pvType,
        DWORD dwFlags,
        ULONG cbBufferLength,
        BYTE *pBuffer,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Free)(
        ITypeMarshal* This,
        PVOID pvType);

    END_INTERFACE
} ITypeMarshalVtbl;
interface ITypeMarshal {
    CONST_VTBL ITypeMarshalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITypeMarshal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITypeMarshal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITypeMarshal_Release(This) (This)->lpVtbl->Release(This)
/*** ITypeMarshal methods ***/
#define ITypeMarshal_Size(This,pvType,dwDestContext,pvDestContext,pSize) (This)->lpVtbl->Size(This,pvType,dwDestContext,pvDestContext,pSize)
#define ITypeMarshal_Marshal(This,pvType,dwDestContext,pvDestContext,cbBufferLength,pBuffer,pcbWritten) (This)->lpVtbl->Marshal(This,pvType,dwDestContext,pvDestContext,cbBufferLength,pBuffer,pcbWritten)
#define ITypeMarshal_Unmarshal(This,pvType,dwFlags,cbBufferLength,pBuffer,pcbRead) (This)->lpVtbl->Unmarshal(This,pvType,dwFlags,cbBufferLength,pBuffer,pcbRead)
#define ITypeMarshal_Free(This,pvType) (This)->lpVtbl->Free(This,pvType)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITypeMarshal_QueryInterface(ITypeMarshal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITypeMarshal_AddRef(ITypeMarshal* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITypeMarshal_Release(ITypeMarshal* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypeMarshal methods ***/
static FORCEINLINE HRESULT ITypeMarshal_Size(ITypeMarshal* This,PVOID pvType,DWORD dwDestContext,PVOID pvDestContext,ULONG *pSize) {
    return This->lpVtbl->Size(This,pvType,dwDestContext,pvDestContext,pSize);
}
static FORCEINLINE HRESULT ITypeMarshal_Marshal(ITypeMarshal* This,PVOID pvType,DWORD dwDestContext,PVOID pvDestContext,ULONG cbBufferLength,BYTE *pBuffer,ULONG *pcbWritten) {
    return This->lpVtbl->Marshal(This,pvType,dwDestContext,pvDestContext,cbBufferLength,pBuffer,pcbWritten);
}
static FORCEINLINE HRESULT ITypeMarshal_Unmarshal(ITypeMarshal* This,PVOID pvType,DWORD dwFlags,ULONG cbBufferLength,BYTE *pBuffer,ULONG *pcbRead) {
    return This->lpVtbl->Unmarshal(This,pvType,dwFlags,cbBufferLength,pBuffer,pcbRead);
}
static FORCEINLINE HRESULT ITypeMarshal_Free(ITypeMarshal* This,PVOID pvType) {
    return This->lpVtbl->Free(This,pvType);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITypeMarshal_Size_Proxy(
    ITypeMarshal* This,
    PVOID pvType,
    DWORD dwDestContext,
    PVOID pvDestContext,
    ULONG *pSize);
void __RPC_STUB ITypeMarshal_Size_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeMarshal_Marshal_Proxy(
    ITypeMarshal* This,
    PVOID pvType,
    DWORD dwDestContext,
    PVOID pvDestContext,
    ULONG cbBufferLength,
    BYTE *pBuffer,
    ULONG *pcbWritten);
void __RPC_STUB ITypeMarshal_Marshal_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeMarshal_Unmarshal_Proxy(
    ITypeMarshal* This,
    PVOID pvType,
    DWORD dwFlags,
    ULONG cbBufferLength,
    BYTE *pBuffer,
    ULONG *pcbRead);
void __RPC_STUB ITypeMarshal_Unmarshal_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ITypeMarshal_Free_Proxy(
    ITypeMarshal* This,
    PVOID pvType);
void __RPC_STUB ITypeMarshal_Free_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ITypeMarshal_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRecordInfo interface
 */
#ifndef __IRecordInfo_INTERFACE_DEFINED__
#define __IRecordInfo_INTERFACE_DEFINED__

typedef IRecordInfo *LPRECORDINFO;

DEFINE_GUID(IID_IRecordInfo, 0x0000002f, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000002f-0000-0000-c000-000000000046")
IRecordInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RecordInit(
        PVOID pvNew) = 0;

    virtual HRESULT STDMETHODCALLTYPE RecordClear(
        PVOID pvExisting) = 0;

    virtual HRESULT STDMETHODCALLTYPE RecordCopy(
        PVOID pvExisting,
        PVOID pvNew) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGuid(
        GUID *pguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetName(
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSize(
        ULONG *pcbSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeInfo(
        ITypeInfo **ppTypeInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetField(
        PVOID pvData,
        LPCOLESTR szFieldName,
        VARIANT *pvarField) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFieldNoCopy(
        PVOID pvData,
        LPCOLESTR szFieldName,
        VARIANT *pvarField,
        PVOID *ppvDataCArray) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutField(
        ULONG wFlags,
        PVOID pvData,
        LPCOLESTR szFieldName,
        VARIANT *pvarField) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutFieldNoCopy(
        ULONG wFlags,
        PVOID pvData,
        LPCOLESTR szFieldName,
        VARIANT *pvarField) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFieldNames(
        ULONG *pcNames,
        BSTR *rgBstrNames) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsMatchingType(
        IRecordInfo *pRecordInfo) = 0;

    virtual PVOID STDMETHODCALLTYPE RecordCreate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RecordCreateCopy(
        PVOID pvSource,
        PVOID *ppvDest) = 0;

    virtual HRESULT STDMETHODCALLTYPE RecordDestroy(
        PVOID pvRecord) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRecordInfo, 0x0000002f, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IRecordInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRecordInfo* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRecordInfo* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRecordInfo* This);

    /*** IRecordInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *RecordInit)(
        IRecordInfo* This,
        PVOID pvNew);

    HRESULT (STDMETHODCALLTYPE *RecordClear)(
        IRecordInfo* This,
        PVOID pvExisting);

    HRESULT (STDMETHODCALLTYPE *RecordCopy)(
        IRecordInfo* This,
        PVOID pvExisting,
        PVOID pvNew);

    HRESULT (STDMETHODCALLTYPE *GetGuid)(
        IRecordInfo* This,
        GUID *pguid);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IRecordInfo* This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IRecordInfo* This,
        ULONG *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRecordInfo* This,
        ITypeInfo **ppTypeInfo);

    HRESULT (STDMETHODCALLTYPE *GetField)(
        IRecordInfo* This,
        PVOID pvData,
        LPCOLESTR szFieldName,
        VARIANT *pvarField);

    HRESULT (STDMETHODCALLTYPE *GetFieldNoCopy)(
        IRecordInfo* This,
        PVOID pvData,
        LPCOLESTR szFieldName,
        VARIANT *pvarField,
        PVOID *ppvDataCArray);

    HRESULT (STDMETHODCALLTYPE *PutField)(
        IRecordInfo* This,
        ULONG wFlags,
        PVOID pvData,
        LPCOLESTR szFieldName,
        VARIANT *pvarField);

    HRESULT (STDMETHODCALLTYPE *PutFieldNoCopy)(
        IRecordInfo* This,
        ULONG wFlags,
        PVOID pvData,
        LPCOLESTR szFieldName,
        VARIANT *pvarField);

    HRESULT (STDMETHODCALLTYPE *GetFieldNames)(
        IRecordInfo* This,
        ULONG *pcNames,
        BSTR *rgBstrNames);

    WINBOOL (STDMETHODCALLTYPE *IsMatchingType)(
        IRecordInfo* This,
        IRecordInfo *pRecordInfo);

    PVOID (STDMETHODCALLTYPE *RecordCreate)(
        IRecordInfo* This);

    HRESULT (STDMETHODCALLTYPE *RecordCreateCopy)(
        IRecordInfo* This,
        PVOID pvSource,
        PVOID *ppvDest);

    HRESULT (STDMETHODCALLTYPE *RecordDestroy)(
        IRecordInfo* This,
        PVOID pvRecord);

    END_INTERFACE
} IRecordInfoVtbl;
interface IRecordInfo {
    CONST_VTBL IRecordInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRecordInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRecordInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRecordInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IRecordInfo methods ***/
#define IRecordInfo_RecordInit(This,pvNew) (This)->lpVtbl->RecordInit(This,pvNew)
#define IRecordInfo_RecordClear(This,pvExisting) (This)->lpVtbl->RecordClear(This,pvExisting)
#define IRecordInfo_RecordCopy(This,pvExisting,pvNew) (This)->lpVtbl->RecordCopy(This,pvExisting,pvNew)
#define IRecordInfo_GetGuid(This,pguid) (This)->lpVtbl->GetGuid(This,pguid)
#define IRecordInfo_GetName(This,pbstrName) (This)->lpVtbl->GetName(This,pbstrName)
#define IRecordInfo_GetSize(This,pcbSize) (This)->lpVtbl->GetSize(This,pcbSize)
#define IRecordInfo_GetTypeInfo(This,ppTypeInfo) (This)->lpVtbl->GetTypeInfo(This,ppTypeInfo)
#define IRecordInfo_GetField(This,pvData,szFieldName,pvarField) (This)->lpVtbl->GetField(This,pvData,szFieldName,pvarField)
#define IRecordInfo_GetFieldNoCopy(This,pvData,szFieldName,pvarField,ppvDataCArray) (This)->lpVtbl->GetFieldNoCopy(This,pvData,szFieldName,pvarField,ppvDataCArray)
#define IRecordInfo_PutField(This,wFlags,pvData,szFieldName,pvarField) (This)->lpVtbl->PutField(This,wFlags,pvData,szFieldName,pvarField)
#define IRecordInfo_PutFieldNoCopy(This,wFlags,pvData,szFieldName,pvarField) (This)->lpVtbl->PutFieldNoCopy(This,wFlags,pvData,szFieldName,pvarField)
#define IRecordInfo_GetFieldNames(This,pcNames,rgBstrNames) (This)->lpVtbl->GetFieldNames(This,pcNames,rgBstrNames)
#define IRecordInfo_IsMatchingType(This,pRecordInfo) (This)->lpVtbl->IsMatchingType(This,pRecordInfo)
#define IRecordInfo_RecordCreate(This) (This)->lpVtbl->RecordCreate(This)
#define IRecordInfo_RecordCreateCopy(This,pvSource,ppvDest) (This)->lpVtbl->RecordCreateCopy(This,pvSource,ppvDest)
#define IRecordInfo_RecordDestroy(This,pvRecord) (This)->lpVtbl->RecordDestroy(This,pvRecord)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRecordInfo_QueryInterface(IRecordInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRecordInfo_AddRef(IRecordInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRecordInfo_Release(IRecordInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IRecordInfo methods ***/
static FORCEINLINE HRESULT IRecordInfo_RecordInit(IRecordInfo* This,PVOID pvNew) {
    return This->lpVtbl->RecordInit(This,pvNew);
}
static FORCEINLINE HRESULT IRecordInfo_RecordClear(IRecordInfo* This,PVOID pvExisting) {
    return This->lpVtbl->RecordClear(This,pvExisting);
}
static FORCEINLINE HRESULT IRecordInfo_RecordCopy(IRecordInfo* This,PVOID pvExisting,PVOID pvNew) {
    return This->lpVtbl->RecordCopy(This,pvExisting,pvNew);
}
static FORCEINLINE HRESULT IRecordInfo_GetGuid(IRecordInfo* This,GUID *pguid) {
    return This->lpVtbl->GetGuid(This,pguid);
}
static FORCEINLINE HRESULT IRecordInfo_GetName(IRecordInfo* This,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,pbstrName);
}
static FORCEINLINE HRESULT IRecordInfo_GetSize(IRecordInfo* This,ULONG *pcbSize) {
    return This->lpVtbl->GetSize(This,pcbSize);
}
static FORCEINLINE HRESULT IRecordInfo_GetTypeInfo(IRecordInfo* This,ITypeInfo **ppTypeInfo) {
    return This->lpVtbl->GetTypeInfo(This,ppTypeInfo);
}
static FORCEINLINE HRESULT IRecordInfo_GetField(IRecordInfo* This,PVOID pvData,LPCOLESTR szFieldName,VARIANT *pvarField) {
    return This->lpVtbl->GetField(This,pvData,szFieldName,pvarField);
}
static FORCEINLINE HRESULT IRecordInfo_GetFieldNoCopy(IRecordInfo* This,PVOID pvData,LPCOLESTR szFieldName,VARIANT *pvarField,PVOID *ppvDataCArray) {
    return This->lpVtbl->GetFieldNoCopy(This,pvData,szFieldName,pvarField,ppvDataCArray);
}
static FORCEINLINE HRESULT IRecordInfo_PutField(IRecordInfo* This,ULONG wFlags,PVOID pvData,LPCOLESTR szFieldName,VARIANT *pvarField) {
    return This->lpVtbl->PutField(This,wFlags,pvData,szFieldName,pvarField);
}
static FORCEINLINE HRESULT IRecordInfo_PutFieldNoCopy(IRecordInfo* This,ULONG wFlags,PVOID pvData,LPCOLESTR szFieldName,VARIANT *pvarField) {
    return This->lpVtbl->PutFieldNoCopy(This,wFlags,pvData,szFieldName,pvarField);
}
static FORCEINLINE HRESULT IRecordInfo_GetFieldNames(IRecordInfo* This,ULONG *pcNames,BSTR *rgBstrNames) {
    return This->lpVtbl->GetFieldNames(This,pcNames,rgBstrNames);
}
static FORCEINLINE WINBOOL IRecordInfo_IsMatchingType(IRecordInfo* This,IRecordInfo *pRecordInfo) {
    return This->lpVtbl->IsMatchingType(This,pRecordInfo);
}
static FORCEINLINE PVOID IRecordInfo_RecordCreate(IRecordInfo* This) {
    return This->lpVtbl->RecordCreate(This);
}
static FORCEINLINE HRESULT IRecordInfo_RecordCreateCopy(IRecordInfo* This,PVOID pvSource,PVOID *ppvDest) {
    return This->lpVtbl->RecordCreateCopy(This,pvSource,ppvDest);
}
static FORCEINLINE HRESULT IRecordInfo_RecordDestroy(IRecordInfo* This,PVOID pvRecord) {
    return This->lpVtbl->RecordDestroy(This,pvRecord);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRecordInfo_RecordInit_Proxy(
    IRecordInfo* This,
    PVOID pvNew);
void __RPC_STUB IRecordInfo_RecordInit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_RecordClear_Proxy(
    IRecordInfo* This,
    PVOID pvExisting);
void __RPC_STUB IRecordInfo_RecordClear_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_RecordCopy_Proxy(
    IRecordInfo* This,
    PVOID pvExisting,
    PVOID pvNew);
void __RPC_STUB IRecordInfo_RecordCopy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_GetGuid_Proxy(
    IRecordInfo* This,
    GUID *pguid);
void __RPC_STUB IRecordInfo_GetGuid_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_GetName_Proxy(
    IRecordInfo* This,
    BSTR *pbstrName);
void __RPC_STUB IRecordInfo_GetName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_GetSize_Proxy(
    IRecordInfo* This,
    ULONG *pcbSize);
void __RPC_STUB IRecordInfo_GetSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_GetTypeInfo_Proxy(
    IRecordInfo* This,
    ITypeInfo **ppTypeInfo);
void __RPC_STUB IRecordInfo_GetTypeInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_GetField_Proxy(
    IRecordInfo* This,
    PVOID pvData,
    LPCOLESTR szFieldName,
    VARIANT *pvarField);
void __RPC_STUB IRecordInfo_GetField_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_GetFieldNoCopy_Proxy(
    IRecordInfo* This,
    PVOID pvData,
    LPCOLESTR szFieldName,
    VARIANT *pvarField,
    PVOID *ppvDataCArray);
void __RPC_STUB IRecordInfo_GetFieldNoCopy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_PutField_Proxy(
    IRecordInfo* This,
    ULONG wFlags,
    PVOID pvData,
    LPCOLESTR szFieldName,
    VARIANT *pvarField);
void __RPC_STUB IRecordInfo_PutField_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_PutFieldNoCopy_Proxy(
    IRecordInfo* This,
    ULONG wFlags,
    PVOID pvData,
    LPCOLESTR szFieldName,
    VARIANT *pvarField);
void __RPC_STUB IRecordInfo_PutFieldNoCopy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_GetFieldNames_Proxy(
    IRecordInfo* This,
    ULONG *pcNames,
    BSTR *rgBstrNames);
void __RPC_STUB IRecordInfo_GetFieldNames_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
WINBOOL STDMETHODCALLTYPE IRecordInfo_IsMatchingType_Proxy(
    IRecordInfo* This,
    IRecordInfo *pRecordInfo);
void __RPC_STUB IRecordInfo_IsMatchingType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
PVOID STDMETHODCALLTYPE IRecordInfo_RecordCreate_Proxy(
    IRecordInfo* This);
void __RPC_STUB IRecordInfo_RecordCreate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_RecordCreateCopy_Proxy(
    IRecordInfo* This,
    PVOID pvSource,
    PVOID *ppvDest);
void __RPC_STUB IRecordInfo_RecordCreateCopy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRecordInfo_RecordDestroy_Proxy(
    IRecordInfo* This,
    PVOID pvRecord);
void __RPC_STUB IRecordInfo_RecordDestroy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRecordInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IErrorLog interface
 */
#ifndef __IErrorLog_INTERFACE_DEFINED__
#define __IErrorLog_INTERFACE_DEFINED__

typedef IErrorLog *LPERRORLOG;

DEFINE_GUID(IID_IErrorLog, 0x3127ca40, 0x446e, 0x11ce, 0x81,0x35, 0x00,0xaa,0x00,0x4b,0xb8,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3127ca40-446e-11ce-8135-00aa004bb851")
IErrorLog : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddError(
        LPCOLESTR pszPropName,
        EXCEPINFO *pExcepInfo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IErrorLog, 0x3127ca40, 0x446e, 0x11ce, 0x81,0x35, 0x00,0xaa,0x00,0x4b,0xb8,0x51)
#endif
#else
typedef struct IErrorLogVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IErrorLog* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IErrorLog* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IErrorLog* This);

    /*** IErrorLog methods ***/
    HRESULT (STDMETHODCALLTYPE *AddError)(
        IErrorLog* This,
        LPCOLESTR pszPropName,
        EXCEPINFO *pExcepInfo);

    END_INTERFACE
} IErrorLogVtbl;
interface IErrorLog {
    CONST_VTBL IErrorLogVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IErrorLog_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IErrorLog_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IErrorLog_Release(This) (This)->lpVtbl->Release(This)
/*** IErrorLog methods ***/
#define IErrorLog_AddError(This,pszPropName,pExcepInfo) (This)->lpVtbl->AddError(This,pszPropName,pExcepInfo)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IErrorLog_QueryInterface(IErrorLog* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IErrorLog_AddRef(IErrorLog* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IErrorLog_Release(IErrorLog* This) {
    return This->lpVtbl->Release(This);
}
/*** IErrorLog methods ***/
static FORCEINLINE HRESULT IErrorLog_AddError(IErrorLog* This,LPCOLESTR pszPropName,EXCEPINFO *pExcepInfo) {
    return This->lpVtbl->AddError(This,pszPropName,pExcepInfo);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IErrorLog_AddError_Proxy(
    IErrorLog* This,
    LPCOLESTR pszPropName,
    EXCEPINFO *pExcepInfo);
void __RPC_STUB IErrorLog_AddError_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IErrorLog_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyBag interface
 */
#ifndef __IPropertyBag_INTERFACE_DEFINED__
#define __IPropertyBag_INTERFACE_DEFINED__

typedef IPropertyBag *LPPROPERTYBAG;

DEFINE_GUID(IID_IPropertyBag, 0x55272a00, 0x42cb, 0x11ce, 0x81,0x35, 0x00,0xaa,0x00,0x4b,0xb8,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("55272a00-42cb-11ce-8135-00aa004bb851")
IPropertyBag : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Read(
        LPCOLESTR pszPropName,
        VARIANT *pVar,
        IErrorLog *pErrorLog) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write(
        LPCOLESTR pszPropName,
        VARIANT *pVar) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyBag, 0x55272a00, 0x42cb, 0x11ce, 0x81,0x35, 0x00,0xaa,0x00,0x4b,0xb8,0x51)
#endif
#else
typedef struct IPropertyBagVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyBag* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyBag* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyBag* This);

    /*** IPropertyBag methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IPropertyBag* This,
        LPCOLESTR pszPropName,
        VARIANT *pVar,
        IErrorLog *pErrorLog);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IPropertyBag* This,
        LPCOLESTR pszPropName,
        VARIANT *pVar);

    END_INTERFACE
} IPropertyBagVtbl;
interface IPropertyBag {
    CONST_VTBL IPropertyBagVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyBag_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyBag_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyBag_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyBag methods ***/
#define IPropertyBag_Read(This,pszPropName,pVar,pErrorLog) (This)->lpVtbl->Read(This,pszPropName,pVar,pErrorLog)
#define IPropertyBag_Write(This,pszPropName,pVar) (This)->lpVtbl->Write(This,pszPropName,pVar)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPropertyBag_QueryInterface(IPropertyBag* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPropertyBag_AddRef(IPropertyBag* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPropertyBag_Release(IPropertyBag* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyBag methods ***/
static FORCEINLINE HRESULT IPropertyBag_Read(IPropertyBag* This,LPCOLESTR pszPropName,VARIANT *pVar,IErrorLog *pErrorLog) {
    return This->lpVtbl->Read(This,pszPropName,pVar,pErrorLog);
}
static FORCEINLINE HRESULT IPropertyBag_Write(IPropertyBag* This,LPCOLESTR pszPropName,VARIANT *pVar) {
    return This->lpVtbl->Write(This,pszPropName,pVar);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPropertyBag_RemoteRead_Proxy(
    IPropertyBag* This,
    LPCOLESTR pszPropName,
    VARIANT *pVar,
    IErrorLog *pErrorLog,
    DWORD varType,
    IUnknown *pUnkObj);
void __RPC_STUB IPropertyBag_RemoteRead_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPropertyBag_Write_Proxy(
    IPropertyBag* This,
    LPCOLESTR pszPropName,
    VARIANT *pVar);
void __RPC_STUB IPropertyBag_Write_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IPropertyBag_Read_Proxy(
    IPropertyBag* This,
    LPCOLESTR pszPropName,
    VARIANT *pVar,
    IErrorLog *pErrorLog);
HRESULT __RPC_STUB IPropertyBag_Read_Stub(
    IPropertyBag* This,
    LPCOLESTR pszPropName,
    VARIANT *pVar,
    IErrorLog *pErrorLog,
    DWORD varType,
    IUnknown *pUnkObj);

#endif  /* __IPropertyBag_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);
ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER CLEANLOCALSTORAGE_UserSize     (ULONG *, ULONG, CLEANLOCALSTORAGE *);
unsigned char * __RPC_USER CLEANLOCALSTORAGE_UserMarshal  (ULONG *, unsigned char *, CLEANLOCALSTORAGE *);
unsigned char * __RPC_USER CLEANLOCALSTORAGE_UserUnmarshal(ULONG *, unsigned char *, CLEANLOCALSTORAGE *);
void            __RPC_USER CLEANLOCALSTORAGE_UserFree     (ULONG *, CLEANLOCALSTORAGE *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __oaidl_h__ */
