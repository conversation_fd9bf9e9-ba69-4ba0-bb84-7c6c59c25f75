/*** Autogenerated by WIDL 1.6 from include/portabledevicetypes.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __portabledevicetypes_h__
#define __portabledevicetypes_h__

/* Forward declarations */

#ifndef __IPortableDeviceValues_FWD_DEFINED__
#define __IPortableDeviceValues_FWD_DEFINED__
typedef interface IPortableDeviceValues IPortableDeviceValues;
#endif

#ifndef __IPortableDeviceKeyCollection_FWD_DEFINED__
#define __IPortableDeviceKeyCollection_FWD_DEFINED__
typedef interface IPortableDeviceKeyCollection IPortableDeviceKeyCollection;
#endif

#ifndef __IPortableDevicePropVariantCollection_FWD_DEFINED__
#define __IPortableDevicePropVariantCollection_FWD_DEFINED__
typedef interface IPortableDevicePropVariantCollection IPortableDevicePropVariantCollection;
#endif

#ifndef __IPortableDeviceValuesCollection_FWD_DEFINED__
#define __IPortableDeviceValuesCollection_FWD_DEFINED__
typedef interface IPortableDeviceValuesCollection IPortableDeviceValuesCollection;
#endif

#ifndef __PortableDeviceValues_FWD_DEFINED__
#define __PortableDeviceValues_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceValues PortableDeviceValues;
#else
typedef struct PortableDeviceValues PortableDeviceValues;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceValues_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <propsys.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __IPortableDeviceKeyCollection_FWD_DEFINED__
#define __IPortableDeviceKeyCollection_FWD_DEFINED__
typedef interface IPortableDeviceKeyCollection IPortableDeviceKeyCollection;
#endif

#ifndef __IPortableDevicePropVariantCollection_FWD_DEFINED__
#define __IPortableDevicePropVariantCollection_FWD_DEFINED__
typedef interface IPortableDevicePropVariantCollection IPortableDevicePropVariantCollection;
#endif

#ifndef __IPortableDeviceValuesCollection_FWD_DEFINED__
#define __IPortableDeviceValuesCollection_FWD_DEFINED__
typedef interface IPortableDeviceValuesCollection IPortableDeviceValuesCollection;
#endif

/*****************************************************************************
 * IPortableDeviceValues interface
 */
#ifndef __IPortableDeviceValues_INTERFACE_DEFINED__
#define __IPortableDeviceValues_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceValues, 0x6848f6f2, 0x3155, 0x4f86, 0xb6,0xf5, 0x26,0x3e,0xee,0xab,0x31,0x43);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6848f6f2-3155-4f86-b6f5-263eeeab3143")
IPortableDeviceValues : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *pcelt) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        const DWORD index,
        PROPERTYKEY *pKey,
        PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValue(
        REFPROPERTYKEY key,
        const PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        REFPROPERTYKEY key,
        PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStringValue(
        REFPROPERTYKEY key,
        LPCWSTR Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringValue(
        REFPROPERTYKEY key,
        LPWSTR *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUnsignedIntegerValue(
        REFPROPERTYKEY key,
        const ULONG Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnsignedIntegerValue(
        REFPROPERTYKEY key,
        ULONG *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignedIntegerValue(
        REFPROPERTYKEY key,
        const LONG Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignedIntegerValue(
        REFPROPERTYKEY key,
        LONG *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUnsignedLargeIntegerValue(
        REFPROPERTYKEY key,
        const ULONGLONG Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnsignedLargeIntegerValue(
        REFPROPERTYKEY key,
        ULONGLONG *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignedLargeIntegerValue(
        REFPROPERTYKEY key,
        const LONGLONG Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignedLargeIntegerValue(
        REFPROPERTYKEY key,
        LONGLONG *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFloatValue(
        REFPROPERTYKEY key,
        const FLOAT Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFloatValue(
        REFPROPERTYKEY key,
        FLOAT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetErrorValue(
        REFPROPERTYKEY key,
        const HRESULT Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorValue(
        REFPROPERTYKEY key,
        HRESULT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetKeyValue(
        REFPROPERTYKEY key,
        REFPROPERTYKEY Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetKeyValue(
        REFPROPERTYKEY key,
        PROPERTYKEY *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBoolValue(
        REFPROPERTYKEY key,
        const WINBOOL Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoolValue(
        REFPROPERTYKEY key,
        WINBOOL *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIUnknownValue(
        REFPROPERTYKEY key,
        IUnknown *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIUnknownValue(
        REFPROPERTYKEY key,
        IUnknown **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGuidValue(
        REFPROPERTYKEY key,
        REFGUID Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGuidValue(
        REFPROPERTYKEY key,
        GUID *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBufferValue(
        REFPROPERTYKEY key,
        BYTE *pValue,
        DWORD cbValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferValue(
        REFPROPERTYKEY key,
        BYTE **ppValue,
        DWORD *pcbValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIPortableDeviceValuesValue(
        REFPROPERTYKEY key,
        IPortableDeviceValues *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIPortableDeviceValuesValue(
        REFPROPERTYKEY key,
        IPortableDeviceValues **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIPortableDevicePropVariantCollectionValue(
        REFPROPERTYKEY key,
        IPortableDevicePropVariantCollection *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIPortableDevicePropVariantCollectionValue(
        REFPROPERTYKEY key,
        IPortableDevicePropVariantCollection **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIPortableDeviceKeyCollectionValue(
        REFPROPERTYKEY key,
        IPortableDeviceKeyCollection *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIPortableDeviceKeyCollectionValue(
        REFPROPERTYKEY key,
        IPortableDeviceKeyCollection **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIPortableDeviceValuesCollectionValue(
        REFPROPERTYKEY key,
        IPortableDeviceValuesCollection *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIPortableDeviceValuesCollectionValue(
        REFPROPERTYKEY key,
        IPortableDeviceValuesCollection **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveValue(
        REFPROPERTYKEY key) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyValuesFromPropertyStore(
        IPropertyStore *pStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyValuesToPropertyStore(
        IPropertyStore *pStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceValues, 0x6848f6f2, 0x3155, 0x4f86, 0xb6,0xf5, 0x26,0x3e,0xee,0xab,0x31,0x43)
#endif
#else
typedef struct IPortableDeviceValuesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceValues* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceValues* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceValues* This);

    /*** IPortableDeviceValues methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPortableDeviceValues* This,
        DWORD *pcelt);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPortableDeviceValues* This,
        const DWORD index,
        PROPERTYKEY *pKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        const PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *SetStringValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        LPCWSTR Value);

    HRESULT (STDMETHODCALLTYPE *GetStringValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        LPWSTR *pValue);

    HRESULT (STDMETHODCALLTYPE *SetUnsignedIntegerValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        const ULONG Value);

    HRESULT (STDMETHODCALLTYPE *GetUnsignedIntegerValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        ULONG *pValue);

    HRESULT (STDMETHODCALLTYPE *SetSignedIntegerValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        const LONG Value);

    HRESULT (STDMETHODCALLTYPE *GetSignedIntegerValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        LONG *pValue);

    HRESULT (STDMETHODCALLTYPE *SetUnsignedLargeIntegerValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        const ULONGLONG Value);

    HRESULT (STDMETHODCALLTYPE *GetUnsignedLargeIntegerValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        ULONGLONG *pValue);

    HRESULT (STDMETHODCALLTYPE *SetSignedLargeIntegerValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        const LONGLONG Value);

    HRESULT (STDMETHODCALLTYPE *GetSignedLargeIntegerValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        LONGLONG *pValue);

    HRESULT (STDMETHODCALLTYPE *SetFloatValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        const FLOAT Value);

    HRESULT (STDMETHODCALLTYPE *GetFloatValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        FLOAT *pValue);

    HRESULT (STDMETHODCALLTYPE *SetErrorValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        const HRESULT Value);

    HRESULT (STDMETHODCALLTYPE *GetErrorValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        HRESULT *pValue);

    HRESULT (STDMETHODCALLTYPE *SetKeyValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        REFPROPERTYKEY Value);

    HRESULT (STDMETHODCALLTYPE *GetKeyValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        PROPERTYKEY *pValue);

    HRESULT (STDMETHODCALLTYPE *SetBoolValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        const WINBOOL Value);

    HRESULT (STDMETHODCALLTYPE *GetBoolValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        WINBOOL *pValue);

    HRESULT (STDMETHODCALLTYPE *SetIUnknownValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IUnknown *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIUnknownValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IUnknown **ppValue);

    HRESULT (STDMETHODCALLTYPE *SetGuidValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        REFGUID Value);

    HRESULT (STDMETHODCALLTYPE *GetGuidValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        GUID *pValue);

    HRESULT (STDMETHODCALLTYPE *SetBufferValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        BYTE *pValue,
        DWORD cbValue);

    HRESULT (STDMETHODCALLTYPE *GetBufferValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        BYTE **ppValue,
        DWORD *pcbValue);

    HRESULT (STDMETHODCALLTYPE *SetIPortableDeviceValuesValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IPortableDeviceValues *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIPortableDeviceValuesValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IPortableDeviceValues **ppValue);

    HRESULT (STDMETHODCALLTYPE *SetIPortableDevicePropVariantCollectionValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IPortableDevicePropVariantCollection *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIPortableDevicePropVariantCollectionValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IPortableDevicePropVariantCollection **ppValue);

    HRESULT (STDMETHODCALLTYPE *SetIPortableDeviceKeyCollectionValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IPortableDeviceKeyCollection *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIPortableDeviceKeyCollectionValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IPortableDeviceKeyCollection **ppValue);

    HRESULT (STDMETHODCALLTYPE *SetIPortableDeviceValuesCollectionValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IPortableDeviceValuesCollection *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIPortableDeviceValuesCollectionValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key,
        IPortableDeviceValuesCollection **ppValue);

    HRESULT (STDMETHODCALLTYPE *RemoveValue)(
        IPortableDeviceValues* This,
        REFPROPERTYKEY key);

    HRESULT (STDMETHODCALLTYPE *CopyValuesFromPropertyStore)(
        IPortableDeviceValues* This,
        IPropertyStore *pStore);

    HRESULT (STDMETHODCALLTYPE *CopyValuesToPropertyStore)(
        IPortableDeviceValues* This,
        IPropertyStore *pStore);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IPortableDeviceValues* This);

    END_INTERFACE
} IPortableDeviceValuesVtbl;
interface IPortableDeviceValues {
    CONST_VTBL IPortableDeviceValuesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceValues_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceValues_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceValues_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceValues methods ***/
#define IPortableDeviceValues_GetCount(This,pcelt) (This)->lpVtbl->GetCount(This,pcelt)
#define IPortableDeviceValues_GetAt(This,index,pKey,pValue) (This)->lpVtbl->GetAt(This,index,pKey,pValue)
#define IPortableDeviceValues_SetValue(This,key,pValue) (This)->lpVtbl->SetValue(This,key,pValue)
#define IPortableDeviceValues_GetValue(This,key,pValue) (This)->lpVtbl->GetValue(This,key,pValue)
#define IPortableDeviceValues_SetStringValue(This,key,Value) (This)->lpVtbl->SetStringValue(This,key,Value)
#define IPortableDeviceValues_GetStringValue(This,key,pValue) (This)->lpVtbl->GetStringValue(This,key,pValue)
#define IPortableDeviceValues_SetUnsignedIntegerValue(This,key,Value) (This)->lpVtbl->SetUnsignedIntegerValue(This,key,Value)
#define IPortableDeviceValues_GetUnsignedIntegerValue(This,key,pValue) (This)->lpVtbl->GetUnsignedIntegerValue(This,key,pValue)
#define IPortableDeviceValues_SetSignedIntegerValue(This,key,Value) (This)->lpVtbl->SetSignedIntegerValue(This,key,Value)
#define IPortableDeviceValues_GetSignedIntegerValue(This,key,pValue) (This)->lpVtbl->GetSignedIntegerValue(This,key,pValue)
#define IPortableDeviceValues_SetUnsignedLargeIntegerValue(This,key,Value) (This)->lpVtbl->SetUnsignedLargeIntegerValue(This,key,Value)
#define IPortableDeviceValues_GetUnsignedLargeIntegerValue(This,key,pValue) (This)->lpVtbl->GetUnsignedLargeIntegerValue(This,key,pValue)
#define IPortableDeviceValues_SetSignedLargeIntegerValue(This,key,Value) (This)->lpVtbl->SetSignedLargeIntegerValue(This,key,Value)
#define IPortableDeviceValues_GetSignedLargeIntegerValue(This,key,pValue) (This)->lpVtbl->GetSignedLargeIntegerValue(This,key,pValue)
#define IPortableDeviceValues_SetFloatValue(This,key,Value) (This)->lpVtbl->SetFloatValue(This,key,Value)
#define IPortableDeviceValues_GetFloatValue(This,key,pValue) (This)->lpVtbl->GetFloatValue(This,key,pValue)
#define IPortableDeviceValues_SetErrorValue(This,key,Value) (This)->lpVtbl->SetErrorValue(This,key,Value)
#define IPortableDeviceValues_GetErrorValue(This,key,pValue) (This)->lpVtbl->GetErrorValue(This,key,pValue)
#define IPortableDeviceValues_SetKeyValue(This,key,Value) (This)->lpVtbl->SetKeyValue(This,key,Value)
#define IPortableDeviceValues_GetKeyValue(This,key,pValue) (This)->lpVtbl->GetKeyValue(This,key,pValue)
#define IPortableDeviceValues_SetBoolValue(This,key,Value) (This)->lpVtbl->SetBoolValue(This,key,Value)
#define IPortableDeviceValues_GetBoolValue(This,key,pValue) (This)->lpVtbl->GetBoolValue(This,key,pValue)
#define IPortableDeviceValues_SetIUnknownValue(This,key,pValue) (This)->lpVtbl->SetIUnknownValue(This,key,pValue)
#define IPortableDeviceValues_GetIUnknownValue(This,key,ppValue) (This)->lpVtbl->GetIUnknownValue(This,key,ppValue)
#define IPortableDeviceValues_SetGuidValue(This,key,Value) (This)->lpVtbl->SetGuidValue(This,key,Value)
#define IPortableDeviceValues_GetGuidValue(This,key,pValue) (This)->lpVtbl->GetGuidValue(This,key,pValue)
#define IPortableDeviceValues_SetBufferValue(This,key,pValue,cbValue) (This)->lpVtbl->SetBufferValue(This,key,pValue,cbValue)
#define IPortableDeviceValues_GetBufferValue(This,key,ppValue,pcbValue) (This)->lpVtbl->GetBufferValue(This,key,ppValue,pcbValue)
#define IPortableDeviceValues_SetIPortableDeviceValuesValue(This,key,pValue) (This)->lpVtbl->SetIPortableDeviceValuesValue(This,key,pValue)
#define IPortableDeviceValues_GetIPortableDeviceValuesValue(This,key,ppValue) (This)->lpVtbl->GetIPortableDeviceValuesValue(This,key,ppValue)
#define IPortableDeviceValues_SetIPortableDevicePropVariantCollectionValue(This,key,pValue) (This)->lpVtbl->SetIPortableDevicePropVariantCollectionValue(This,key,pValue)
#define IPortableDeviceValues_GetIPortableDevicePropVariantCollectionValue(This,key,ppValue) (This)->lpVtbl->GetIPortableDevicePropVariantCollectionValue(This,key,ppValue)
#define IPortableDeviceValues_SetIPortableDeviceKeyCollectionValue(This,key,pValue) (This)->lpVtbl->SetIPortableDeviceKeyCollectionValue(This,key,pValue)
#define IPortableDeviceValues_GetIPortableDeviceKeyCollectionValue(This,key,ppValue) (This)->lpVtbl->GetIPortableDeviceKeyCollectionValue(This,key,ppValue)
#define IPortableDeviceValues_SetIPortableDeviceValuesCollectionValue(This,key,pValue) (This)->lpVtbl->SetIPortableDeviceValuesCollectionValue(This,key,pValue)
#define IPortableDeviceValues_GetIPortableDeviceValuesCollectionValue(This,key,ppValue) (This)->lpVtbl->GetIPortableDeviceValuesCollectionValue(This,key,ppValue)
#define IPortableDeviceValues_RemoveValue(This,key) (This)->lpVtbl->RemoveValue(This,key)
#define IPortableDeviceValues_CopyValuesFromPropertyStore(This,pStore) (This)->lpVtbl->CopyValuesFromPropertyStore(This,pStore)
#define IPortableDeviceValues_CopyValuesToPropertyStore(This,pStore) (This)->lpVtbl->CopyValuesToPropertyStore(This,pStore)
#define IPortableDeviceValues_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPortableDeviceValues_QueryInterface(IPortableDeviceValues* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPortableDeviceValues_AddRef(IPortableDeviceValues* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPortableDeviceValues_Release(IPortableDeviceValues* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceValues methods ***/
static FORCEINLINE HRESULT IPortableDeviceValues_GetCount(IPortableDeviceValues* This,DWORD *pcelt) {
    return This->lpVtbl->GetCount(This,pcelt);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetAt(IPortableDeviceValues* This,const DWORD index,PROPERTYKEY *pKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetAt(This,index,pKey,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const PROPVARIANT *pValue) {
    return This->lpVtbl->SetValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetValue(IPortableDeviceValues* This,REFPROPERTYKEY key,PROPVARIANT *pValue) {
    return This->lpVtbl->GetValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetStringValue(IPortableDeviceValues* This,REFPROPERTYKEY key,LPCWSTR Value) {
    return This->lpVtbl->SetStringValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetStringValue(IPortableDeviceValues* This,REFPROPERTYKEY key,LPWSTR *pValue) {
    return This->lpVtbl->GetStringValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetUnsignedIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const ULONG Value) {
    return This->lpVtbl->SetUnsignedIntegerValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetUnsignedIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,ULONG *pValue) {
    return This->lpVtbl->GetUnsignedIntegerValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetSignedIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const LONG Value) {
    return This->lpVtbl->SetSignedIntegerValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetSignedIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,LONG *pValue) {
    return This->lpVtbl->GetSignedIntegerValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetUnsignedLargeIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const ULONGLONG Value) {
    return This->lpVtbl->SetUnsignedLargeIntegerValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetUnsignedLargeIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,ULONGLONG *pValue) {
    return This->lpVtbl->GetUnsignedLargeIntegerValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetSignedLargeIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const LONGLONG Value) {
    return This->lpVtbl->SetSignedLargeIntegerValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetSignedLargeIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,LONGLONG *pValue) {
    return This->lpVtbl->GetSignedLargeIntegerValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetFloatValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const FLOAT Value) {
    return This->lpVtbl->SetFloatValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetFloatValue(IPortableDeviceValues* This,REFPROPERTYKEY key,FLOAT *pValue) {
    return This->lpVtbl->GetFloatValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetErrorValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const HRESULT Value) {
    return This->lpVtbl->SetErrorValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetErrorValue(IPortableDeviceValues* This,REFPROPERTYKEY key,HRESULT *pValue) {
    return This->lpVtbl->GetErrorValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetKeyValue(IPortableDeviceValues* This,REFPROPERTYKEY key,REFPROPERTYKEY Value) {
    return This->lpVtbl->SetKeyValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetKeyValue(IPortableDeviceValues* This,REFPROPERTYKEY key,PROPERTYKEY *pValue) {
    return This->lpVtbl->GetKeyValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetBoolValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const WINBOOL Value) {
    return This->lpVtbl->SetBoolValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetBoolValue(IPortableDeviceValues* This,REFPROPERTYKEY key,WINBOOL *pValue) {
    return This->lpVtbl->GetBoolValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetIUnknownValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IUnknown *pValue) {
    return This->lpVtbl->SetIUnknownValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetIUnknownValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IUnknown **ppValue) {
    return This->lpVtbl->GetIUnknownValue(This,key,ppValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetGuidValue(IPortableDeviceValues* This,REFPROPERTYKEY key,REFGUID Value) {
    return This->lpVtbl->SetGuidValue(This,key,Value);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetGuidValue(IPortableDeviceValues* This,REFPROPERTYKEY key,GUID *pValue) {
    return This->lpVtbl->GetGuidValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetBufferValue(IPortableDeviceValues* This,REFPROPERTYKEY key,BYTE *pValue,DWORD cbValue) {
    return This->lpVtbl->SetBufferValue(This,key,pValue,cbValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetBufferValue(IPortableDeviceValues* This,REFPROPERTYKEY key,BYTE **ppValue,DWORD *pcbValue) {
    return This->lpVtbl->GetBufferValue(This,key,ppValue,pcbValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetIPortableDeviceValuesValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceValues *pValue) {
    return This->lpVtbl->SetIPortableDeviceValuesValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetIPortableDeviceValuesValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceValues **ppValue) {
    return This->lpVtbl->GetIPortableDeviceValuesValue(This,key,ppValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetIPortableDevicePropVariantCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDevicePropVariantCollection *pValue) {
    return This->lpVtbl->SetIPortableDevicePropVariantCollectionValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetIPortableDevicePropVariantCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDevicePropVariantCollection **ppValue) {
    return This->lpVtbl->GetIPortableDevicePropVariantCollectionValue(This,key,ppValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetIPortableDeviceKeyCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceKeyCollection *pValue) {
    return This->lpVtbl->SetIPortableDeviceKeyCollectionValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetIPortableDeviceKeyCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceKeyCollection **ppValue) {
    return This->lpVtbl->GetIPortableDeviceKeyCollectionValue(This,key,ppValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_SetIPortableDeviceValuesCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceValuesCollection *pValue) {
    return This->lpVtbl->SetIPortableDeviceValuesCollectionValue(This,key,pValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_GetIPortableDeviceValuesCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceValuesCollection **ppValue) {
    return This->lpVtbl->GetIPortableDeviceValuesCollectionValue(This,key,ppValue);
}
static FORCEINLINE HRESULT IPortableDeviceValues_RemoveValue(IPortableDeviceValues* This,REFPROPERTYKEY key) {
    return This->lpVtbl->RemoveValue(This,key);
}
static FORCEINLINE HRESULT IPortableDeviceValues_CopyValuesFromPropertyStore(IPortableDeviceValues* This,IPropertyStore *pStore) {
    return This->lpVtbl->CopyValuesFromPropertyStore(This,pStore);
}
static FORCEINLINE HRESULT IPortableDeviceValues_CopyValuesToPropertyStore(IPortableDeviceValues* This,IPropertyStore *pStore) {
    return This->lpVtbl->CopyValuesToPropertyStore(This,pStore);
}
static FORCEINLINE HRESULT IPortableDeviceValues_Clear(IPortableDeviceValues* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetCount_Proxy(
    IPortableDeviceValues* This,
    DWORD *pcelt);
void __RPC_STUB IPortableDeviceValues_GetCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetAt_Proxy(
    IPortableDeviceValues* This,
    const DWORD index,
    PROPERTYKEY *pKey,
    PROPVARIANT *pValue);
void __RPC_STUB IPortableDeviceValues_GetAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    const PROPVARIANT *pValue);
void __RPC_STUB IPortableDeviceValues_SetValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    PROPVARIANT *pValue);
void __RPC_STUB IPortableDeviceValues_GetValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetStringValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    LPCWSTR Value);
void __RPC_STUB IPortableDeviceValues_SetStringValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetStringValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    LPWSTR *pValue);
void __RPC_STUB IPortableDeviceValues_GetStringValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetUnsignedIntegerValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    const ULONG Value);
void __RPC_STUB IPortableDeviceValues_SetUnsignedIntegerValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetUnsignedIntegerValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    ULONG *pValue);
void __RPC_STUB IPortableDeviceValues_GetUnsignedIntegerValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetSignedIntegerValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    const LONG Value);
void __RPC_STUB IPortableDeviceValues_SetSignedIntegerValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetSignedIntegerValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    LONG *pValue);
void __RPC_STUB IPortableDeviceValues_GetSignedIntegerValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetUnsignedLargeIntegerValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    const ULONGLONG Value);
void __RPC_STUB IPortableDeviceValues_SetUnsignedLargeIntegerValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetUnsignedLargeIntegerValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    ULONGLONG *pValue);
void __RPC_STUB IPortableDeviceValues_GetUnsignedLargeIntegerValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetSignedLargeIntegerValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    const LONGLONG Value);
void __RPC_STUB IPortableDeviceValues_SetSignedLargeIntegerValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetSignedLargeIntegerValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    LONGLONG *pValue);
void __RPC_STUB IPortableDeviceValues_GetSignedLargeIntegerValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetFloatValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    const FLOAT Value);
void __RPC_STUB IPortableDeviceValues_SetFloatValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetFloatValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    FLOAT *pValue);
void __RPC_STUB IPortableDeviceValues_GetFloatValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetErrorValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    const HRESULT Value);
void __RPC_STUB IPortableDeviceValues_SetErrorValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetErrorValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    HRESULT *pValue);
void __RPC_STUB IPortableDeviceValues_GetErrorValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetKeyValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    REFPROPERTYKEY Value);
void __RPC_STUB IPortableDeviceValues_SetKeyValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetKeyValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    PROPERTYKEY *pValue);
void __RPC_STUB IPortableDeviceValues_GetKeyValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetBoolValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    const WINBOOL Value);
void __RPC_STUB IPortableDeviceValues_SetBoolValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetBoolValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    WINBOOL *pValue);
void __RPC_STUB IPortableDeviceValues_GetBoolValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetIUnknownValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IUnknown *pValue);
void __RPC_STUB IPortableDeviceValues_SetIUnknownValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetIUnknownValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IUnknown **ppValue);
void __RPC_STUB IPortableDeviceValues_GetIUnknownValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetGuidValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    REFGUID Value);
void __RPC_STUB IPortableDeviceValues_SetGuidValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetGuidValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    GUID *pValue);
void __RPC_STUB IPortableDeviceValues_GetGuidValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetBufferValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    BYTE *pValue,
    DWORD cbValue);
void __RPC_STUB IPortableDeviceValues_SetBufferValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetBufferValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    BYTE **ppValue,
    DWORD *pcbValue);
void __RPC_STUB IPortableDeviceValues_GetBufferValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetIPortableDeviceValuesValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IPortableDeviceValues *pValue);
void __RPC_STUB IPortableDeviceValues_SetIPortableDeviceValuesValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetIPortableDeviceValuesValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IPortableDeviceValues **ppValue);
void __RPC_STUB IPortableDeviceValues_GetIPortableDeviceValuesValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetIPortableDevicePropVariantCollectionValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IPortableDevicePropVariantCollection *pValue);
void __RPC_STUB IPortableDeviceValues_SetIPortableDevicePropVariantCollectionValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetIPortableDevicePropVariantCollectionValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IPortableDevicePropVariantCollection **ppValue);
void __RPC_STUB IPortableDeviceValues_GetIPortableDevicePropVariantCollectionValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetIPortableDeviceKeyCollectionValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IPortableDeviceKeyCollection *pValue);
void __RPC_STUB IPortableDeviceValues_SetIPortableDeviceKeyCollectionValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetIPortableDeviceKeyCollectionValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IPortableDeviceKeyCollection **ppValue);
void __RPC_STUB IPortableDeviceValues_GetIPortableDeviceKeyCollectionValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_SetIPortableDeviceValuesCollectionValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IPortableDeviceValuesCollection *pValue);
void __RPC_STUB IPortableDeviceValues_SetIPortableDeviceValuesCollectionValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_GetIPortableDeviceValuesCollectionValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key,
    IPortableDeviceValuesCollection **ppValue);
void __RPC_STUB IPortableDeviceValues_GetIPortableDeviceValuesCollectionValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_RemoveValue_Proxy(
    IPortableDeviceValues* This,
    REFPROPERTYKEY key);
void __RPC_STUB IPortableDeviceValues_RemoveValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_CopyValuesFromPropertyStore_Proxy(
    IPortableDeviceValues* This,
    IPropertyStore *pStore);
void __RPC_STUB IPortableDeviceValues_CopyValuesFromPropertyStore_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_CopyValuesToPropertyStore_Proxy(
    IPortableDeviceValues* This,
    IPropertyStore *pStore);
void __RPC_STUB IPortableDeviceValues_CopyValuesToPropertyStore_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValues_Clear_Proxy(
    IPortableDeviceValues* This);
void __RPC_STUB IPortableDeviceValues_Clear_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPortableDeviceValues_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceKeyCollection interface
 */
#ifndef __IPortableDeviceKeyCollection_INTERFACE_DEFINED__
#define __IPortableDeviceKeyCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceKeyCollection, 0xdada2357, 0xe0ad, 0x492e, 0x98,0xdb, 0xdd,0x61,0xc5,0x3b,0xa3,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dada2357-e0ad-492e-98db-dd61c53ba353")
IPortableDeviceKeyCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *pcElems) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        const DWORD dwIndex,
        PROPERTYKEY *pKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        REFPROPERTYKEY Key) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        const DWORD dwIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceKeyCollection, 0xdada2357, 0xe0ad, 0x492e, 0x98,0xdb, 0xdd,0x61,0xc5,0x3b,0xa3,0x53)
#endif
#else
typedef struct IPortableDeviceKeyCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceKeyCollection* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceKeyCollection* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceKeyCollection* This);

    /*** IPortableDeviceKeyCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPortableDeviceKeyCollection* This,
        DWORD *pcElems);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPortableDeviceKeyCollection* This,
        const DWORD dwIndex,
        PROPERTYKEY *pKey);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IPortableDeviceKeyCollection* This,
        REFPROPERTYKEY Key);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IPortableDeviceKeyCollection* This);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IPortableDeviceKeyCollection* This,
        const DWORD dwIndex);

    END_INTERFACE
} IPortableDeviceKeyCollectionVtbl;
interface IPortableDeviceKeyCollection {
    CONST_VTBL IPortableDeviceKeyCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceKeyCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceKeyCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceKeyCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceKeyCollection methods ***/
#define IPortableDeviceKeyCollection_GetCount(This,pcElems) (This)->lpVtbl->GetCount(This,pcElems)
#define IPortableDeviceKeyCollection_GetAt(This,dwIndex,pKey) (This)->lpVtbl->GetAt(This,dwIndex,pKey)
#define IPortableDeviceKeyCollection_Add(This,Key) (This)->lpVtbl->Add(This,Key)
#define IPortableDeviceKeyCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IPortableDeviceKeyCollection_RemoveAt(This,dwIndex) (This)->lpVtbl->RemoveAt(This,dwIndex)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPortableDeviceKeyCollection_QueryInterface(IPortableDeviceKeyCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPortableDeviceKeyCollection_AddRef(IPortableDeviceKeyCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPortableDeviceKeyCollection_Release(IPortableDeviceKeyCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceKeyCollection methods ***/
static FORCEINLINE HRESULT IPortableDeviceKeyCollection_GetCount(IPortableDeviceKeyCollection* This,DWORD *pcElems) {
    return This->lpVtbl->GetCount(This,pcElems);
}
static FORCEINLINE HRESULT IPortableDeviceKeyCollection_GetAt(IPortableDeviceKeyCollection* This,const DWORD dwIndex,PROPERTYKEY *pKey) {
    return This->lpVtbl->GetAt(This,dwIndex,pKey);
}
static FORCEINLINE HRESULT IPortableDeviceKeyCollection_Add(IPortableDeviceKeyCollection* This,REFPROPERTYKEY Key) {
    return This->lpVtbl->Add(This,Key);
}
static FORCEINLINE HRESULT IPortableDeviceKeyCollection_Clear(IPortableDeviceKeyCollection* This) {
    return This->lpVtbl->Clear(This);
}
static FORCEINLINE HRESULT IPortableDeviceKeyCollection_RemoveAt(IPortableDeviceKeyCollection* This,const DWORD dwIndex) {
    return This->lpVtbl->RemoveAt(This,dwIndex);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPortableDeviceKeyCollection_GetCount_Proxy(
    IPortableDeviceKeyCollection* This,
    DWORD *pcElems);
void __RPC_STUB IPortableDeviceKeyCollection_GetCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceKeyCollection_GetAt_Proxy(
    IPortableDeviceKeyCollection* This,
    const DWORD dwIndex,
    PROPERTYKEY *pKey);
void __RPC_STUB IPortableDeviceKeyCollection_GetAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceKeyCollection_Add_Proxy(
    IPortableDeviceKeyCollection* This,
    REFPROPERTYKEY Key);
void __RPC_STUB IPortableDeviceKeyCollection_Add_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceKeyCollection_Clear_Proxy(
    IPortableDeviceKeyCollection* This);
void __RPC_STUB IPortableDeviceKeyCollection_Clear_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceKeyCollection_RemoveAt_Proxy(
    IPortableDeviceKeyCollection* This,
    const DWORD dwIndex);
void __RPC_STUB IPortableDeviceKeyCollection_RemoveAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPortableDeviceKeyCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDevicePropVariantCollection interface
 */
#ifndef __IPortableDevicePropVariantCollection_INTERFACE_DEFINED__
#define __IPortableDevicePropVariantCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDevicePropVariantCollection, 0x89b2e422, 0x4f1b, 0x4316, 0xbc,0xef, 0xa4,0x4a,0xfe,0xa8,0x3e,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("89b2e422-4f1b-4316-bcef-a44afea83eb3")
IPortableDevicePropVariantCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *pcElems) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        const DWORD dwIndex,
        PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        const PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetType(
        VARTYPE *pvt) = 0;

    virtual HRESULT STDMETHODCALLTYPE ChangeType(
        const VARTYPE vt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        const DWORD dwIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDevicePropVariantCollection, 0x89b2e422, 0x4f1b, 0x4316, 0xbc,0xef, 0xa4,0x4a,0xfe,0xa8,0x3e,0xb3)
#endif
#else
typedef struct IPortableDevicePropVariantCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDevicePropVariantCollection* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDevicePropVariantCollection* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDevicePropVariantCollection* This);

    /*** IPortableDevicePropVariantCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPortableDevicePropVariantCollection* This,
        DWORD *pcElems);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPortableDevicePropVariantCollection* This,
        const DWORD dwIndex,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IPortableDevicePropVariantCollection* This,
        const PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IPortableDevicePropVariantCollection* This,
        VARTYPE *pvt);

    HRESULT (STDMETHODCALLTYPE *ChangeType)(
        IPortableDevicePropVariantCollection* This,
        const VARTYPE vt);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IPortableDevicePropVariantCollection* This);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IPortableDevicePropVariantCollection* This,
        const DWORD dwIndex);

    END_INTERFACE
} IPortableDevicePropVariantCollectionVtbl;
interface IPortableDevicePropVariantCollection {
    CONST_VTBL IPortableDevicePropVariantCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDevicePropVariantCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDevicePropVariantCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDevicePropVariantCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDevicePropVariantCollection methods ***/
#define IPortableDevicePropVariantCollection_GetCount(This,pcElems) (This)->lpVtbl->GetCount(This,pcElems)
#define IPortableDevicePropVariantCollection_GetAt(This,dwIndex,pValue) (This)->lpVtbl->GetAt(This,dwIndex,pValue)
#define IPortableDevicePropVariantCollection_Add(This,pValue) (This)->lpVtbl->Add(This,pValue)
#define IPortableDevicePropVariantCollection_GetType(This,pvt) (This)->lpVtbl->GetType(This,pvt)
#define IPortableDevicePropVariantCollection_ChangeType(This,vt) (This)->lpVtbl->ChangeType(This,vt)
#define IPortableDevicePropVariantCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IPortableDevicePropVariantCollection_RemoveAt(This,dwIndex) (This)->lpVtbl->RemoveAt(This,dwIndex)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPortableDevicePropVariantCollection_QueryInterface(IPortableDevicePropVariantCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPortableDevicePropVariantCollection_AddRef(IPortableDevicePropVariantCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPortableDevicePropVariantCollection_Release(IPortableDevicePropVariantCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDevicePropVariantCollection methods ***/
static FORCEINLINE HRESULT IPortableDevicePropVariantCollection_GetCount(IPortableDevicePropVariantCollection* This,DWORD *pcElems) {
    return This->lpVtbl->GetCount(This,pcElems);
}
static FORCEINLINE HRESULT IPortableDevicePropVariantCollection_GetAt(IPortableDevicePropVariantCollection* This,const DWORD dwIndex,PROPVARIANT *pValue) {
    return This->lpVtbl->GetAt(This,dwIndex,pValue);
}
static FORCEINLINE HRESULT IPortableDevicePropVariantCollection_Add(IPortableDevicePropVariantCollection* This,const PROPVARIANT *pValue) {
    return This->lpVtbl->Add(This,pValue);
}
static FORCEINLINE HRESULT IPortableDevicePropVariantCollection_GetType(IPortableDevicePropVariantCollection* This,VARTYPE *pvt) {
    return This->lpVtbl->GetType(This,pvt);
}
static FORCEINLINE HRESULT IPortableDevicePropVariantCollection_ChangeType(IPortableDevicePropVariantCollection* This,const VARTYPE vt) {
    return This->lpVtbl->ChangeType(This,vt);
}
static FORCEINLINE HRESULT IPortableDevicePropVariantCollection_Clear(IPortableDevicePropVariantCollection* This) {
    return This->lpVtbl->Clear(This);
}
static FORCEINLINE HRESULT IPortableDevicePropVariantCollection_RemoveAt(IPortableDevicePropVariantCollection* This,const DWORD dwIndex) {
    return This->lpVtbl->RemoveAt(This,dwIndex);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPortableDevicePropVariantCollection_GetCount_Proxy(
    IPortableDevicePropVariantCollection* This,
    DWORD *pcElems);
void __RPC_STUB IPortableDevicePropVariantCollection_GetCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDevicePropVariantCollection_GetAt_Proxy(
    IPortableDevicePropVariantCollection* This,
    const DWORD dwIndex,
    PROPVARIANT *pValue);
void __RPC_STUB IPortableDevicePropVariantCollection_GetAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDevicePropVariantCollection_Add_Proxy(
    IPortableDevicePropVariantCollection* This,
    const PROPVARIANT *pValue);
void __RPC_STUB IPortableDevicePropVariantCollection_Add_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDevicePropVariantCollection_GetType_Proxy(
    IPortableDevicePropVariantCollection* This,
    VARTYPE *pvt);
void __RPC_STUB IPortableDevicePropVariantCollection_GetType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDevicePropVariantCollection_ChangeType_Proxy(
    IPortableDevicePropVariantCollection* This,
    const VARTYPE vt);
void __RPC_STUB IPortableDevicePropVariantCollection_ChangeType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDevicePropVariantCollection_Clear_Proxy(
    IPortableDevicePropVariantCollection* This);
void __RPC_STUB IPortableDevicePropVariantCollection_Clear_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDevicePropVariantCollection_RemoveAt_Proxy(
    IPortableDevicePropVariantCollection* This,
    const DWORD dwIndex);
void __RPC_STUB IPortableDevicePropVariantCollection_RemoveAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPortableDevicePropVariantCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceValuesCollection interface
 */
#ifndef __IPortableDeviceValuesCollection_INTERFACE_DEFINED__
#define __IPortableDeviceValuesCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceValuesCollection, 0x6e3f2d79, 0x4e07, 0x48c4, 0x82,0x08, 0xd8,0xc2,0xe5,0xaf,0x4a,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6e3f2d79-4e07-48c4-8208-d8c2e5af4a99")
IPortableDeviceValuesCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *pcElems) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        DWORD dwIndex,
        IPortableDeviceValues **ppValues) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        IPortableDeviceValues *pValues) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        const DWORD dwIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceValuesCollection, 0x6e3f2d79, 0x4e07, 0x48c4, 0x82,0x08, 0xd8,0xc2,0xe5,0xaf,0x4a,0x99)
#endif
#else
typedef struct IPortableDeviceValuesCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceValuesCollection* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceValuesCollection* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceValuesCollection* This);

    /*** IPortableDeviceValuesCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPortableDeviceValuesCollection* This,
        DWORD *pcElems);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPortableDeviceValuesCollection* This,
        DWORD dwIndex,
        IPortableDeviceValues **ppValues);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IPortableDeviceValuesCollection* This,
        IPortableDeviceValues *pValues);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IPortableDeviceValuesCollection* This);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IPortableDeviceValuesCollection* This,
        const DWORD dwIndex);

    END_INTERFACE
} IPortableDeviceValuesCollectionVtbl;
interface IPortableDeviceValuesCollection {
    CONST_VTBL IPortableDeviceValuesCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceValuesCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceValuesCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceValuesCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceValuesCollection methods ***/
#define IPortableDeviceValuesCollection_GetCount(This,pcElems) (This)->lpVtbl->GetCount(This,pcElems)
#define IPortableDeviceValuesCollection_GetAt(This,dwIndex,ppValues) (This)->lpVtbl->GetAt(This,dwIndex,ppValues)
#define IPortableDeviceValuesCollection_Add(This,pValues) (This)->lpVtbl->Add(This,pValues)
#define IPortableDeviceValuesCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IPortableDeviceValuesCollection_RemoveAt(This,dwIndex) (This)->lpVtbl->RemoveAt(This,dwIndex)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPortableDeviceValuesCollection_QueryInterface(IPortableDeviceValuesCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPortableDeviceValuesCollection_AddRef(IPortableDeviceValuesCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPortableDeviceValuesCollection_Release(IPortableDeviceValuesCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceValuesCollection methods ***/
static FORCEINLINE HRESULT IPortableDeviceValuesCollection_GetCount(IPortableDeviceValuesCollection* This,DWORD *pcElems) {
    return This->lpVtbl->GetCount(This,pcElems);
}
static FORCEINLINE HRESULT IPortableDeviceValuesCollection_GetAt(IPortableDeviceValuesCollection* This,DWORD dwIndex,IPortableDeviceValues **ppValues) {
    return This->lpVtbl->GetAt(This,dwIndex,ppValues);
}
static FORCEINLINE HRESULT IPortableDeviceValuesCollection_Add(IPortableDeviceValuesCollection* This,IPortableDeviceValues *pValues) {
    return This->lpVtbl->Add(This,pValues);
}
static FORCEINLINE HRESULT IPortableDeviceValuesCollection_Clear(IPortableDeviceValuesCollection* This) {
    return This->lpVtbl->Clear(This);
}
static FORCEINLINE HRESULT IPortableDeviceValuesCollection_RemoveAt(IPortableDeviceValuesCollection* This,const DWORD dwIndex) {
    return This->lpVtbl->RemoveAt(This,dwIndex);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPortableDeviceValuesCollection_GetCount_Proxy(
    IPortableDeviceValuesCollection* This,
    DWORD *pcElems);
void __RPC_STUB IPortableDeviceValuesCollection_GetCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValuesCollection_GetAt_Proxy(
    IPortableDeviceValuesCollection* This,
    DWORD dwIndex,
    IPortableDeviceValues **ppValues);
void __RPC_STUB IPortableDeviceValuesCollection_GetAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValuesCollection_Add_Proxy(
    IPortableDeviceValuesCollection* This,
    IPortableDeviceValues *pValues);
void __RPC_STUB IPortableDeviceValuesCollection_Add_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValuesCollection_Clear_Proxy(
    IPortableDeviceValuesCollection* This);
void __RPC_STUB IPortableDeviceValuesCollection_Clear_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPortableDeviceValuesCollection_RemoveAt_Proxy(
    IPortableDeviceValuesCollection* This,
    const DWORD dwIndex);
void __RPC_STUB IPortableDeviceValuesCollection_RemoveAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPortableDeviceValuesCollection_INTERFACE_DEFINED__ */


DEFINE_GUID(LIBID_PortableDeviceTypesLib, 0x2b00ba2f, 0xe750, 0x4beb, 0x92,0x35, 0x97,0x14,0x2e,0xde,0x1d,0x3e);

/*****************************************************************************
 * PortableDeviceValues coclass
 */

DEFINE_GUID(CLSID_PortableDeviceValues, 0x0c15d503, 0xd017, 0x47ce, 0x90,0x16, 0x7b,0x3f,0x97,0x87,0x21,0xcc);

#ifdef __cplusplus
class DECLSPEC_UUID("0c15d503-d017-47ce-9016-7b3f978721cc") PortableDeviceValues;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceValues, 0x0c15d503, 0xd017, 0x47ce, 0x90,0x16, 0x7b,0x3f,0x97,0x87,0x21,0xcc)
#endif
#endif

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __portabledevicetypes_h__ */
