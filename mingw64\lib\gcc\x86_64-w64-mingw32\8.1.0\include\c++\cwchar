// -*- C++ -*- forwarding header.

// Copyright (C) 1997-2018 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/cwchar
 *  This is a Standard C++ Library file.  You should @c \#include this file
 *  in your programs, rather than any of the @a *.h implementation files.
 *
 *  This is the C++ version of the Standard C Library header @c wchar.h,
 *  and its contents are (mostly) the same as that header, but are all
 *  contained in the namespace @c std (except for names which are defined
 *  as macros in C).
 */

//
// ISO C++ 14882: 21.4
//

#pragma GCC system_header

#include <bits/c++config.h>

#if _GLIBCXX_HAVE_WCHAR_H
#include <wchar.h>
#endif

#ifndef _GLIBCXX_CWCHAR
#define _GLIBCXX_CWCHAR 1

// Need to do a bit of trickery here with mbstate_t as char_traits
// assumes it is in wchar.h, regardless of wchar_t specializations.
#ifndef _GLIBCXX_HAVE_MBSTATE_T
extern "C"
{
  typedef struct
  {
    int __fill[6];
  } mbstate_t;
}
#endif

namespace std
{
  using ::mbstate_t;
} // namespace std

// Get rid of those macros defined in <wchar.h> in lieu of real functions.
#undef btowc
#undef fgetwc
#undef fgetws
#undef fputwc
#undef fputws
#undef fwide
#undef fwprintf
#undef fwscanf
#undef getwc
#undef getwchar
#undef mbrlen
#undef mbrtowc
#undef mbsinit
#undef mbsrtowcs
#undef putwc
#undef putwchar
#undef swprintf
#undef swscanf
#undef ungetwc
#undef vfwprintf
#if _GLIBCXX_HAVE_VFWSCANF
# undef vfwscanf
#endif
#undef vswprintf
#if _GLIBCXX_HAVE_VSWSCANF
# undef vswscanf
#endif
#undef vwprintf
#if _GLIBCXX_HAVE_VWSCANF
# undef vwscanf
#endif
#undef wcrtomb
#undef wcscat
#undef wcschr
#undef wcscmp
#undef wcscoll
#undef wcscpy
#undef wcscspn
#undef wcsftime
#undef wcslen
#undef wcsncat
#undef wcsncmp
#undef wcsncpy
#undef wcspbrk
#undef wcsrchr
#undef wcsrtombs
#undef wcsspn
#undef wcsstr
#undef wcstod
#if _GLIBCXX_HAVE_WCSTOF
# undef wcstof
#endif
#undef wcstok
#undef wcstol
#undef wcstoul
#undef wcsxfrm
#undef wctob
#undef wmemchr
#undef wmemcmp
#undef wmemcpy
#undef wmemmove
#undef wmemset
#undef wprintf
#undef wscanf

#if _GLIBCXX_USE_WCHAR_T

namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  using ::wint_t;

  using ::btowc;
  using ::fgetwc;
  using ::fgetws;
  using ::fputwc;
  using ::fputws;
  using ::fwide;
  using ::fwprintf;
  using ::fwscanf;
  using ::getwc;
  using ::getwchar;
  using ::mbrlen;
  using ::mbrtowc;
  using ::mbsinit;
  using ::mbsrtowcs;
  using ::putwc;
  using ::putwchar;
#ifndef _GLIBCXX_HAVE_BROKEN_VSWPRINTF
  using ::swprintf;
#endif
  using ::swscanf;
  using ::ungetwc;
  using ::vfwprintf;
#if _GLIBCXX_HAVE_VFWSCANF
  using ::vfwscanf;
#endif
#ifndef _GLIBCXX_HAVE_BROKEN_VSWPRINTF
  using ::vswprintf;
#endif
#if _GLIBCXX_HAVE_VSWSCANF
  using ::vswscanf;
#endif
  using ::vwprintf;
#if _GLIBCXX_HAVE_VWSCANF
  using ::vwscanf;
#endif
  using ::wcrtomb;
  using ::wcscat;
  using ::wcscmp;
  using ::wcscoll;
  using ::wcscpy;
  using ::wcscspn;
  using ::wcsftime;
  using ::wcslen;
  using ::wcsncat;
  using ::wcsncmp;
  using ::wcsncpy;
  using ::wcsrtombs;
  using ::wcsspn;
  using ::wcstod;
#if _GLIBCXX_HAVE_WCSTOF
  using ::wcstof;
#endif
  using ::wcstok;
  using ::wcstol;
  using ::wcstoul;
  using ::wcsxfrm;
  using ::wctob;
  using ::wmemcmp;
  using ::wmemcpy;
  using ::wmemmove;
  using ::wmemset;
  using ::wprintf;
  using ::wscanf;
  using ::wcschr;
  using ::wcspbrk;
  using ::wcsrchr;
  using ::wcsstr;
  using ::wmemchr;

#ifndef __CORRECT_ISO_CPP_WCHAR_H_PROTO
  inline wchar_t*
  wcschr(wchar_t* __p, wchar_t __c)
  { return wcschr(const_cast<const wchar_t*>(__p), __c); }

  inline wchar_t*
  wcspbrk(wchar_t* __s1, const wchar_t* __s2)
  { return wcspbrk(const_cast<const wchar_t*>(__s1), __s2); }

  inline wchar_t*
  wcsrchr(wchar_t* __p, wchar_t __c)
  { return wcsrchr(const_cast<const wchar_t*>(__p), __c); }

  inline wchar_t*
  wcsstr(wchar_t* __s1, const wchar_t* __s2)
  { return wcsstr(const_cast<const wchar_t*>(__s1), __s2); }

  inline wchar_t*
  wmemchr(wchar_t* __p, wchar_t __c, size_t __n)
  { return wmemchr(const_cast<const wchar_t*>(__p), __c, __n); }
#endif

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace

#if _GLIBCXX_USE_C99_WCHAR

#undef wcstold
#undef wcstoll
#undef wcstoull

namespace __gnu_cxx
{
#if _GLIBCXX_USE_C99_CHECK || _GLIBCXX_USE_C99_DYNAMIC
  extern "C" long double
    (wcstold)(const wchar_t * __restrict, wchar_t ** __restrict) throw ();
#endif
#if !_GLIBCXX_USE_C99_DYNAMIC
  using ::wcstold;
#endif
#if _GLIBCXX_USE_C99_LONG_LONG_CHECK || _GLIBCXX_USE_C99_LONG_LONG_DYNAMIC
  extern "C" long long int
    (wcstoll)(const wchar_t * __restrict, wchar_t ** __restrict, int) throw ();
  extern "C" unsigned long long int
    (wcstoull)(const wchar_t * __restrict, wchar_t ** __restrict, int) throw ();
#endif
#if !_GLIBCXX_USE_C99_LONG_LONG_DYNAMIC
  using ::wcstoll;
  using ::wcstoull;
#endif
} // namespace __gnu_cxx

namespace std
{
  using ::__gnu_cxx::wcstold;
  using ::__gnu_cxx::wcstoll;
  using ::__gnu_cxx::wcstoull;
} // namespace

#endif

#endif //_GLIBCXX_USE_WCHAR_T

#if __cplusplus >= 201103L

#ifdef _GLIBCXX_USE_WCHAR_T

namespace std
{
#if _GLIBCXX_HAVE_WCSTOF
  using std::wcstof;
#endif
#if _GLIBCXX_HAVE_VFWSCANF
  using std::vfwscanf;
#endif
#if _GLIBCXX_HAVE_VSWSCANF
  using std::vswscanf;
#endif
#if _GLIBCXX_HAVE_VWSCANF
  using std::vwscanf;
#endif

#if _GLIBCXX_USE_C99_WCHAR
  using std::wcstold;
  using std::wcstoll;
  using std::wcstoull;
#endif
} // namespace

#endif // _GLIBCXX_USE_WCHAR_T

#endif // C++11

#endif
