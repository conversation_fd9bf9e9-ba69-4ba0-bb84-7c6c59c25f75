
# **************************************************************************

version : MinGW-W64-builds-4.3.5
user    : nixman
date    : 05.11.2018- 7:00:39 PM
args    : --mode=gcc-8.1.0 --buildroot=/c/mingw810 --jobs=2 --rev=0 --rt-version=trunk --bootstrap --threads=posix --exceptions=sjlj --arch=x86_64 --bin-compress
PATH    : /usr/local/bin:/usr/bin:/bin:/opt/bin:/c/Windows/System32:/c/Windows:/c/Windows/System32/Wbem:/c/Windows/System32/WindowsPowerShell/v1.0/:/usr/bin/site_perl:/usr/bin/vendor_perl:/usr/bin/core_perl
CFLAGS  : -O2 -pipe -fno-ident
CXXFLAGS: -O2 -pipe -fno-ident
CPPFLAGS: 
LDFLAGS : -pipe -fno-ident

# **************************************************************************

host gcc 32-bit:
Using built-in specs.
COLLECT_GCC=C:\msys64\home\nixman\mingw-builds\toolchains\mingw32\bin\gcc.exe
COLLECT_LTO_WRAPPER=C:/msys64/home/<USER>/mingw-builds/toolchains/mingw32/bin/../libexec/gcc/i686-w64-mingw32/7.3.0/lto-wrapper.exe
Target: i686-w64-mingw32
Configured with: ../../../src/gcc-7.3.0/configure --host=i686-w64-mingw32 --build=i686-w64-mingw32 --target=i686-w64-mingw32 --prefix=/mingw32 --with-sysroot=/c/mingw730/i686-730-posix-sjlj-rt_v5-rev0/mingw32 --enable-shared --enable-static --enable-targets=all --enable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --enable-sjlj-exceptions --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch-32=i686 --with-arch-64=nocona --with-tune-32=generic --with-tune-64=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw730/prerequisites/i686-w64-mingw32-static --with-mpfr=/c/mingw730/prerequisites/i686-w64-mingw32-static --with-mpc=/c/mingw730/prerequisites/i686-w64-mingw32-static --with-isl=/c/mingw730/prerequisites/i686-w64-mingw32-static --with-pkgversion='i686-posix-sjlj-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw730/i686-730-posix-sjlj-rt_v5-rev0/mingw32/opt/include -I/c/mingw730/prerequisites/i686-zlib-static/include -I/c/mingw730/prerequisites/i686-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw730/i686-730-posix-sjlj-rt_v5-rev0/mingw32/opt/include -I/c/mingw730/prerequisites/i686-zlib-static/include -I/c/mingw730/prerequisites/i686-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw730/i686-730-posix-sjlj-rt_v5-rev0/mingw32/opt/include -I/c/mingw730/prerequisites/i686-zlib-static/include -I/c/mingw730/prerequisites/i686-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw730/i686-730-posix-sjlj-rt_v5-rev0/mingw32/opt/lib -L/c/mingw730/prerequisites/i686-zlib-static/lib -L/c/mingw730/prerequisites/i686-w64-mingw32-static/lib -Wl,--large-address-aware'
Thread model: posix
gcc version 7.3.0 (i686-posix-sjlj-rev0, Built by MinGW-W64 project) 

# **************************************************************************

host gcc 64-bit:
Using built-in specs.
COLLECT_GCC=C:\msys64\home\nixman\mingw-builds\toolchains\mingw64\bin\gcc.exe
COLLECT_LTO_WRAPPER=C:/msys64/home/<USER>/mingw-builds/toolchains/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/7.3.0/lto-wrapper.exe
Target: x86_64-w64-mingw32
Configured with: ../../../src/gcc-7.3.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw730/x86_64-730-posix-sjlj-rt_v5-rev0/mingw64 --enable-shared --enable-static --enable-targets=all --enable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --enable-sjlj-exceptions --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch-32=i686 --with-arch-64=nocona --with-tune-32=generic --with-tune-64=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw730/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw730/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw730/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw730/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-sjlj-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw730/x86_64-730-posix-sjlj-rt_v5-rev0/mingw64/opt/include -I/c/mingw730/prerequisites/x86_64-zlib-static/include -I/c/mingw730/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw730/x86_64-730-posix-sjlj-rt_v5-rev0/mingw64/opt/include -I/c/mingw730/prerequisites/x86_64-zlib-static/include -I/c/mingw730/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw730/x86_64-730-posix-sjlj-rt_v5-rev0/mingw64/opt/include -I/c/mingw730/prerequisites/x86_64-zlib-static/include -I/c/mingw730/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw730/x86_64-730-posix-sjlj-rt_v5-rev0/mingw64/opt/lib -L/c/mingw730/prerequisites/x86_64-zlib-static/lib -L/c/mingw730/prerequisites/x86_64-w64-mingw32-static/lib '
Thread model: posix
gcc version 7.3.0 (x86_64-posix-sjlj-rev0, Built by MinGW-W64 project) 

# **************************************************************************

host ld 32-bit:
GNU ld (GNU Binutils) 2.30
  Supported emulations:
   i386pe
   i386pep

# **************************************************************************

host ld 64-bit:
GNU ld (GNU Binutils) 2.30
  Supported emulations:
   i386pep
   i386pe

# **************************************************************************

name         : x86_64-libiconv-static
type         : .tar.gz
version      : 1.15
url          : https://ftp.gnu.org/pub/gnu/libiconv/libiconv-1.15.tar.gz
patches      : libiconv/0001-compile-relocatable-in-gnulib.mingw.patch, libiconv/0002-fix-cr-for-awk-in-configure.all.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/prerequisites/x86_64-libiconv-static --enable-static --disable-shared CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-libiconv-static
type         : .tar.gz
version      : 1.15
url          : https://ftp.gnu.org/pub/gnu/libiconv/libiconv-1.15.tar.gz
patches      : libiconv/0001-compile-relocatable-in-gnulib.mingw.patch, libiconv/0002-fix-cr-for-awk-in-configure.all.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/prerequisites/x86_64-libiconv-static --enable-static --disable-shared CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-zlib-1.2.11-static
type         : .tar.gz
version      : 1.2.11
url          : https://sourceforge.net/projects/libpng/files/zlib/1.2.11/zlib-1.2.11.tar.gz
patches      : zlib/01-zlib-1.2.11-1-buildsys.mingw.patch, zlib/03-dont-put-sodir-into-L.mingw.patch, zlib/013-fix-largefile-support.patch
configuration: --prefix=/c/mingw810/prerequisites/x86_64-zlib-static --static

# **************************************************************************

name         : x86_64-zlib-1.2.11-static
type         : .tar.gz
version      : 1.2.11
url          : https://sourceforge.net/projects/libpng/files/zlib/1.2.11/zlib-1.2.11.tar.gz
patches      : zlib/01-zlib-1.2.11-1-buildsys.mingw.patch, zlib/03-dont-put-sodir-into-L.mingw.patch, zlib/013-fix-largefile-support.patch
configuration: --prefix=/c/mingw810/prerequisites/x86_64-zlib-static --static

# **************************************************************************

name         : x86_64-gmp-6.1.2-static
type         : .tar.bz2
version      : 6.1.2
url          : https://gmplib.org/download/gmp/gmp-6.1.2.tar.bz2
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --enable-static --disable-shared --enable-cxx CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-mpfr-4.0.1-static
type         : .tar.bz2
version      : 4.0.1
url          : https://ftp.gnu.org/gnu/mpfr/mpfr-4.0.1.tar.bz2
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --enable-static --disable-shared --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-mpc-1.1.0-static
type         : .tar.gz
version      : 1.1.0
url          : https://ftp.gnu.org/gnu/mpc/mpc-1.1.0.tar.gz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --enable-static --disable-shared --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-isl-0.18-static
type         : .tar.xz
version      : 0.18
url          : http://isl.gforge.inria.fr/isl-0.18.tar.xz
patches      : isl/isl-0.14.1-no-undefined.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --enable-static --disable-shared --with-gmp-prefix=/c/mingw810/prerequisites/x86_64-w64-mingw32-static CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-mingw-w64-headers-v6-multi
type         : .tar.xz
version      : 0.18
url          : http://isl.gforge.inria.fr/isl-0.18.tar.xz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/runtime/x86_64-mingw-w64-v6-multi --enable-sdk=all --enable-secure-api CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-mingw-w64-crt-v6-multi
type         : .tar.xz
version      : 0.18
url          : http://isl.gforge.inria.fr/isl-0.18.tar.xz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/runtime/x86_64-mingw-w64-v6-multi --with-sysroot=/c/mingw810/runtime/x86_64-mingw-w64-v6-multi --enable-lib32 --enable-lib64 --enable-wildcard CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-winpthreads-v6
type         : .tar.xz
version      : 0.18
url          : http://isl.gforge.inria.fr/isl-0.18.tar.xz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/runtime/x86_64-winpthreads-v6 --enable-shared --enable-static CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-winpthreads-v6
type         : .tar.xz
version      : 0.18
url          : http://isl.gforge.inria.fr/isl-0.18.tar.xz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/runtime/x86_64-winpthreads-v6 --enable-shared --enable-static CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : x86_64-binutils-2.30-multi
type         : .tar.bz2
version      : 2.30
url          : https://ftp.gnu.org/gnu/binutils/binutils-2.30.tar.bz2
patches      : binutils/0001-enable-gold-on.mingw32.patch, binutils/0002-check-for-unusual-file-harder.patch, binutils/0008-fix-libiberty-makefile.mingw.patch, binutils/0009-fix-libiberty-configure.mingw.patch, binutils/0110-binutils-mingw-gnu-print.patch, binutils/binutils_2.30_bug_fix-remove-provide-qualifiers.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/prerequisites/x86_64-binutils-multi --with-sysroot=/c/mingw810/runtime/x86_64-mingw-w64-multi --enable-targets=x86_64-w64-mingw32,i686-w64-mingw32 --enable-multilib --enable-64-bit-bfd --enable-lto --enable-plugins --enable-gold --enable-install-libiberty --with-libiconv-prefix=/c/mingw810/prerequisites/x86_64-libiconv-static --disable-rpath --disable-nls --disable-shared --enable-shared --enable-static CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib "

# **************************************************************************

name         : gcc-8.1.0
type         : .tar.xz
version      : 8.1.0
url          : https://ftp.gnu.org/gnu/gcc/gcc-8.1.0/gcc-8.1.0.tar.xz
patches      : gcc/gcc-4.7-stdthreads.patch, gcc/gcc-5.1-iconv.patch, gcc/gcc-4.8-libstdc++export.patch, gcc/gcc-4.8.2-fix-for-windows-not-minding-non-existant-parent-dirs.patch, gcc/gcc-4.8.2-windows-lrealpath-no-force-lowercase-nor-backslash.patch, gcc/gcc-4.9.1-enable-shared-gnat-implib.mingw.patch, gcc/gcc-5.1.0-make-xmmintrin-header-cplusplus-compatible.patch, gcc/gcc-5.2-fix-mingw-pch.patch, gcc/gcc-5-dwarf-regression.patch, gcc/gcc-5.1.0-fix-libatomic-building-for-threads=win32.patch, gcc/gcc-6-ktietz-libgomp.patch, gcc/gcc-libgomp-ftime64.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 --enable-shared --enable-static --enable-targets=all --enable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-sjlj-exceptions --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch-32=i686 --with-arch-64=nocona --with-tune-32=generic --with-tune-64=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion="x86_64-posix-sjlj-rev0, Built by MinGW-W64 project" --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib "

# **************************************************************************

name         : libmangle-v6
type         : .tar.xz
version      : 8.1.0
url          : https://ftp.gnu.org/gnu/gcc/gcc-8.1.0/gcc-8.1.0.tar.xz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : gendef-v6
type         : .tar.xz
version      : 8.1.0
url          : https://ftp.gnu.org/gnu/gcc/gcc-8.1.0/gcc-8.1.0.tar.xz
patches      : mingw-w64/incorrect_cast_fix.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 --with-mangle=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : genidl-v6
type         : .tar.xz
version      : 8.1.0
url          : https://ftp.gnu.org/gnu/gcc/gcc-8.1.0/gcc-8.1.0.tar.xz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : genpeimg-v6
type         : .tar.xz
version      : 8.1.0
url          : https://ftp.gnu.org/gnu/gcc/gcc-8.1.0/gcc-8.1.0.tar.xz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : widl-v6
type         : .tar.xz
version      : 8.1.0
url          : https://ftp.gnu.org/gnu/gcc/gcc-8.1.0/gcc-8.1.0.tar.xz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : libgnurx-2.5.1
type         : .tar.gz
version      : 2.5.1
url          : https://sourceforge.net/projects/mingw/files/Other/UserContributed/regex/mingw-regex-2.5.1/mingw-libgnurx-2.5.1-src.tar.gz
patches      : libgnurx/mingw32-libgnurx-honor-destdir.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --enable-static --disable-shared CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : bzip2-1.0.6
type         : .tar.gz
version      : 1.0.6
url          : http://www.bzip.org/1.0.6/bzip2-1.0.6.tar.gz
patches      : bzip2/bzip2-1.0.4-bzip2recover.patch, bzip2/bzip2-1.0.6-autoconfiscated.patch, bzip2/bzip2-use-cdecl-calling-convention.patch, bzip2/bzip2-1.0.5-slash.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --enable-shared --disable-static CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : termcap-1.3.1
type         : .tar.gz
version      : 1.3.1
url          : https://ftp.gnu.org/gnu/termcap/termcap-1.3.1.tar.gz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt

# **************************************************************************

name         : libffi-3.2.1
type         : .tar.gz
version      : 3.2.1
url          : ftp://sourceware.org/pub/libffi/libffi-3.2.1.tar.gz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --enable-static --disable-shared CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : expat-2.2.5
type         : .tar.bz2
version      : 2.2.5
url          : http://sourceforge.net/projects/expat/files/expat/2.2.5/expat-2.2.5.tar.bz2
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --enable-static --disable-shared CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : gdbm-1.14.1
type         : .tar.gz
version      : 1.14.1
url          : https://ftp.gnu.org/gnu/gdbm/gdbm-1.14.1.tar.gz
patches      : gdbm/gdbm_1.14.1-win32-support.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --enable-shared --disable-static --enable-libgdbm-compat CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : tcl8.6.8
type         : .tar.gz
version      : 8.6.8
url          : http://prdownloads.sourceforge.net/tcl/tcl8.6.8-src.tar.gz
patches      : tcl/002-fix-forbidden-colon-in-paths.mingw.patch, tcl/004-use-system-zlib.mingw.patch, tcl/005-no-xc.mingw.patch, tcl/006-proper-implib-name.mingw.patch, tcl/007-install.mingw.patch, tcl/008-tcl-8.5.14-hidden.patch, tcl/009-fix-using-gnu-print.patch, tcl/010-dont-link-shared-with--static-libgcc.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --disable-threads --enable-shared --enable-64bit

# **************************************************************************

name         : tk8.6.8
type         : .tar.gz
version      : 8.6.8
url          : http://prdownloads.sourceforge.net/tcl/tk8.6.8-src.tar.gz
patches      : tk/002-implib-name.mingw.patch, tk/003-fix-forbidden-colon-in-paths.mingw.patch, tk/004-install-man.mingw.patch, tk/006-prevent-tclStubsPtr-segfault.patch, tk/008-dont-link-shared-with--static-libgcc.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --with-tcl=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib --enable-shared --enable-64bit

# **************************************************************************

name         : openssl-1.0.2o
type         : .tar.gz
version      : 1.0.2o
url          : https://www.openssl.org/source/openssl-1.0.2o.tar.gz
patches      : openssl/openssl-0.9.6-x509.patch, openssl/openssl-1.0.0a-ldflags.patch, openssl/openssl-1.0.1-x32.patch
configuration: --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt shared threads zlib enable-camellia enable-idea enable-mdc2 enable-tlsext enable-rfc3779 mingw64

# **************************************************************************

name         : sqlite-3230100
type         : .tar.gz
version      : 3230100
url          : https://www.sqlite.org/2018/sqlite-autoconf-3230100.tar.gz
patches      : 
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --enable-threadsafe --enable-shared --disable-static CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include -DSQLITE_ENABLE_COLUMN_METADATA -DSQLITE_ENABLE_RTREE" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : ncurses-6.1
type         : .tar.gz
version      : 6.1
url          : https://ftp.gnu.org/gnu/ncurses/ncurses-6.1.tar.gz
patches      : ncurses/work_around_changed_output_of_GNU_cpp_5.x.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --without-ada --with-cxx --without-pthread --enable-pc-files --with-pkg-config --with-pkg-config-libdir=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib/pkgconfig --disable-rpath --enable-colorfgbg --disable-symlinks --enable-warnings --enable-assertions --disable-home-terminfo --enable-database --enable-sp-funcs --enable-term-driver --enable-interop --enable-widec --without-trace CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include -D__USE_MINGW_ANSI_STDIO=1" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : readline-7.0
type         : .tar.gz
version      : 7.0
url          : https://ftp.gnu.org/gnu/readline/readline-7.0.tar.gz
patches      : readline/readline-7.0-mingw.patch, readline/readline70-001, readline/readline70-002, readline/readline70-003
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --enable-shared --disable-static --without-curses CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include -D__USE_MINGW_ANSI_STDIO=1" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib"

# **************************************************************************

name         : Python-2.7.9
type         : .tar.xz
version      : 2.7.9
url          : http://www.python.org/ftp/python/2.7.9/Python-2.7.9.tar.xz
patches      : Python2/0100-MINGW-BASE-use-NT-thread-model.patch, Python2/0110-MINGW-translate-gcc-internal-defines-to-python-platf.patch, Python2/0120-MINGW-use-header-in-lowercase.patch, Python2/0130-MINGW-configure-MACHDEP-and-platform-for-build.patch, Python2/0140-MINGW-preset-configure-defaults.patch, Python2/0150-MINGW-configure-largefile-support-for-windows-builds.patch, Python2/0160-MINGW-add-wincrypt.h-in-Python-random.c.patch, Python2/0180-MINGW-init-system-calls.patch, Python2/0190-MINGW-detect-REPARSE_DATA_BUFFER.patch, Python2/0200-MINGW-build-in-windows-modules-winreg.patch, Python2/0210-MINGW-determine-if-pwdmodule-should-be-used.patch, Python2/0220-MINGW-default-sys.path-calculations-for-windows-plat.patch, Python2/0230-MINGW-AC_LIBOBJ-replacement-of-fileblocks.patch, Python2/0250-MINGW-compiler-customize-mingw-cygwin-compilers.patch, Python2/0270-CYGWIN-issue13756-Python-make-fail-on-cygwin.patch, Python2/0290-issue6672-v2-Add-Mingw-recognition-to-pyport.h-to-al.patch, Python2/0300-MINGW-configure-for-shared-build.patch, Python2/0310-MINGW-dynamic-loading-support.patch, Python2/0320-MINGW-implement-exec-prefix.patch, Python2/0330-MINGW-ignore-main-program-for-frozen-scripts.patch, Python2/0340-MINGW-setup-exclude-termios-module.patch, Python2/0350-MINGW-setup-_multiprocessing-module.patch, Python2/0360-MINGW-setup-select-module.patch, Python2/0370-MINGW-setup-_ctypes-module-with-system-libffi.patch, Python2/0380-MINGW-defect-winsock2-and-setup-_socket-module.patch, Python2/0390-MINGW-exclude-unix-only-modules.patch, Python2/0400-MINGW-setup-msvcrt-module.patch, Python2/0410-MINGW-build-extensions-with-GCC.patch, Python2/0420-MINGW-use-Mingw32CCompiler-as-default-compiler-for-m.patch, Python2/0430-MINGW-find-import-library.patch, Python2/0440-MINGW-setup-_ssl-module.patch, Python2/0460-MINGW-generalization-of-posix-build-in-sysconfig.py.patch, Python2/0462-MINGW-support-stdcall-without-underscore.patch, Python2/0480-MINGW-generalization-of-posix-build-in-distutils-sys.patch, Python2/0490-MINGW-customize-site.patch, Python2/0500-add-python-config-sh.patch, Python2/0510-cross-darwin-feature.patch, Python2/0520-py3k-mingw-ntthreads-vs-pthreads.patch, Python2/0530-mingw-system-libffi.patch, Python2/0540-mingw-semicolon-DELIM.patch, Python2/0550-mingw-regen-use-stddef_h.patch, Python2/0560-mingw-use-posix-getpath.patch, Python2/0565-mingw-add-ModuleFileName-dir-to-PATH.patch, Python2/0570-mingw-add-BUILDIN_WIN32_MODULEs-time-msvcrt.patch, Python2/0580-mingw32-test-REPARSE_DATA_BUFFER.patch, Python2/0590-mingw-INSTALL_SHARED-LDLIBRARY-LIBPL.patch, Python2/0600-msys-mingw-prefer-unix-sep-if-MSYSTEM.patch, Python2/0610-msys-cygwin-semi-native-build-sysconfig.patch, Python2/0620-mingw-sysconfig-like-posix.patch, Python2/0630-mingw-_winapi_as_builtin_for_Popen_in_cygwinccompiler.patch, Python2/0640-mingw-x86_64-size_t-format-specifier-pid_t.patch, Python2/0650-cross-dont-add-multiarch-paths-if-cross-compiling.patch, Python2/0660-mingw-use-backslashes-in-compileall-py.patch, Python2/0670-msys-convert_path-fix-and-root-hack.patch, Python2/0690-allow-static-tcltk.patch, Python2/0710-CROSS-properly-detect-WINDOW-_flags-for-different-nc.patch, Python2/0720-mingw-pdcurses_ISPAD.patch, Python2/0740-grammar-fixes.patch, Python2/0750-Add-interp-Python-DESTSHARED-to-PYTHONPATH-b4-pybuilddir-txt-dir.patch, Python2/0760-msys-monkeypatch-os-system-via-sh-exe.patch, Python2/0770-msys-replace-slashes-used-in-io-redirection.patch, Python2/0790-mingw-add-_exec_prefix-for-tcltk-dlls.patch, Python2/0800-mingw-install-layout-as-posix.patch, Python2/0820-mingw-reorder-bininstall-ln-symlink-creation.patch, Python2/0830-add-build-sysroot-config-option.patch, Python2/0840-add-builddir-to-library_dirs.patch, Python2/0850-cross-PYTHON_FOR_BUILD-gteq-276-and-fullpath-it.patch, Python2/0855-mingw-fix-ssl-dont-use-enum_certificates.patch, Python2/0860-mingw-build-optimized-ext.patch, Python2/0870-mingw-add-LIBPL-to-library-dirs.patch, Python2/0910-fix-using-dllhandle-and-winver-mingw.patch, Python2/1000-dont-link-with-gettext.patch, Python2/1010-ctypes-python-dll.patch, Python2/1020-gdbm-module-includes.patch, Python2/1030-use-gnu_printf-in-format.patch, Python2/1040-install-msilib.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt --enable-shared --with-threads --with-system-expat --with-system-ffi LIBFFI_INCLUDEDIR=C:/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib/libffi-3.2.1/include OPT= CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include -fwrapv -DNDEBUG -D__USE_MINGW_ANSI_STDIO=1" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include -IC:/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -IC:/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include/ncursesw -IC:/mingw810/prerequisites/x86_64-zlib-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib -LC:/mingw810/prerequisites/x86_64-zlib-static/lib -LC:/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib"

# **************************************************************************

name         : gdb-8.1
type         : .tar.xz
version      : 8.1
url          : https://ftp.gnu.org/gnu/gdb/gdb-8.1.tar.xz
patches      : gdb/gdb-perfomance.patch, gdb/gdb-fix-using-gnu-print.patch, gdb/gdb-7.12-dynamic-libs.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 --enable-targets=x86_64-w64-mingw32,i686-w64-mingw32 --enable-64-bit-bfd --disable-nls --disable-werror --disable-win32-registry --disable-rpath --with-system-gdbinit=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/etc/gdbinit --with-python=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/bin/python-config-u.sh --with-expat --with-libiconv --with-zlib --disable-tui --disable-gdbtk CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include -D__USE_MINGW_ANSI_STDIO=1" CXXFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include -D__USE_MINGW_ANSI_STDIO=1" CPPFLAGS=" -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib "

# **************************************************************************

name         : make-4.2.1
type         : .tar.bz2
version      : 4.2.1
url          : https://ftp.gnu.org/gnu/make/make-4.2.1.tar.bz2
patches      : make/make-linebuf-mingw.patch, make/make-getopt.patch
configuration: --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --prefix=/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64 --program-prefix=mingw32- --enable-job-server --without-guile CFLAGS="-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include" LDFLAGS="-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib -L/c/mingw810/x86_64-810-posix-sjlj-rt_v6-rev0/mingw64/opt/lib"

# **************************************************************************

