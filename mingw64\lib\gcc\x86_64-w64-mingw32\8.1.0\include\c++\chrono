// <chrono> -*- C++ -*-

// Copyright (C) 2008-2018 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/chrono
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_CHRONO
#define _GLIBCXX_CHRONO 1

#pragma GCC system_header

#if __cplusplus < 201103L
# include <bits/c++0x_warning.h>
#else

#include <ratio>
#include <type_traits>
#include <limits>
#include <ctime>
#include <bits/parse_numbers.h> // for literals support.

#ifdef _GLIBCXX_USE_C99_STDINT_TR1

namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  /**
   * @defgroup chrono Time
   * @ingroup utilities
   *
   * Classes and functions for time.
   * @{
   */

  /** @namespace std::chrono
   *  @brief ISO C++ 2011 entities sub-namespace for time and date.
   */
  namespace chrono
  {
    template<typename _Rep, typename _Period = ratio<1>>
      struct duration;

    template<typename _Clock, typename _Dur = typename _Clock::duration>
      struct time_point;
  }

  // 20.11.4.3 specialization of common_type (for duration, sfinae-friendly)

  template<typename _CT, typename _Period1, typename _Period2>
    struct __duration_common_type_wrapper
    {
    private:
      typedef __static_gcd<_Period1::num, _Period2::num> __gcd_num;
      typedef __static_gcd<_Period1::den, _Period2::den> __gcd_den;
      typedef typename _CT::type __cr;
      typedef ratio<__gcd_num::value,
        (_Period1::den / __gcd_den::value) * _Period2::den> __r;
    public:
      typedef __success_type<chrono::duration<__cr, __r>> type;
    };

  template<typename _Period1, typename _Period2>
    struct __duration_common_type_wrapper<__failure_type, _Period1, _Period2>
    { typedef __failure_type type; };

  template<typename _Rep1, typename _Period1, typename _Rep2, typename _Period2>
    struct common_type<chrono::duration<_Rep1, _Period1>,
             chrono::duration<_Rep2, _Period2>>
    : public __duration_common_type_wrapper<typename __member_type_wrapper<
             common_type<_Rep1, _Rep2>>::type, _Period1, _Period2>::type
    { };

  // 20.11.4.3 specialization of common_type (for time_point, sfinae-friendly)

  template<typename _CT, typename _Clock>
    struct __timepoint_common_type_wrapper
    {
      typedef __success_type<chrono::time_point<_Clock, typename _CT::type>>
        type;
    };

  template<typename _Clock>
    struct __timepoint_common_type_wrapper<__failure_type, _Clock>
    { typedef __failure_type type; };

  template<typename _Clock, typename _Duration1, typename _Duration2>
    struct common_type<chrono::time_point<_Clock, _Duration1>,
             chrono::time_point<_Clock, _Duration2>>
    : public __timepoint_common_type_wrapper<typename __member_type_wrapper<
             common_type<_Duration1, _Duration2>>::type, _Clock>::type
    { };

  namespace chrono
  {
    // Primary template for duration_cast impl.
    template<typename _ToDur, typename _CF, typename _CR,
	     bool _NumIsOne = false, bool _DenIsOne = false>
      struct __duration_cast_impl
      {
	template<typename _Rep, typename _Period>
	  static constexpr _ToDur
	  __cast(const duration<_Rep, _Period>& __d)
	  {
	    typedef typename _ToDur::rep			__to_rep;
	    return _ToDur(static_cast<__to_rep>(static_cast<_CR>(__d.count())
	      * static_cast<_CR>(_CF::num)
	      / static_cast<_CR>(_CF::den)));
	  }
      };

    template<typename _ToDur, typename _CF, typename _CR>
      struct __duration_cast_impl<_ToDur, _CF, _CR, true, true>
      {
	template<typename _Rep, typename _Period>
	  static constexpr _ToDur
	  __cast(const duration<_Rep, _Period>& __d)
	  {
	    typedef typename _ToDur::rep			__to_rep;
	    return _ToDur(static_cast<__to_rep>(__d.count()));
	  }
      };

    template<typename _ToDur, typename _CF, typename _CR>
      struct __duration_cast_impl<_ToDur, _CF, _CR, true, false>
      {
	template<typename _Rep, typename _Period>
	  static constexpr _ToDur
	  __cast(const duration<_Rep, _Period>& __d)
	  {
	    typedef typename _ToDur::rep			__to_rep;
	    return _ToDur(static_cast<__to_rep>(
	      static_cast<_CR>(__d.count()) / static_cast<_CR>(_CF::den)));
	  }
      };

    template<typename _ToDur, typename _CF, typename _CR>
      struct __duration_cast_impl<_ToDur, _CF, _CR, false, true>
      {
	template<typename _Rep, typename _Period>
	  static constexpr _ToDur
	  __cast(const duration<_Rep, _Period>& __d)
	  {
	    typedef typename _ToDur::rep			__to_rep;
	    return _ToDur(static_cast<__to_rep>(
	      static_cast<_CR>(__d.count()) * static_cast<_CR>(_CF::num)));
	  }
      };

    template<typename _Tp>
      struct __is_duration
      : std::false_type
      { };

    template<typename _Rep, typename _Period>
      struct __is_duration<duration<_Rep, _Period>>
      : std::true_type
      { };

    template<typename _Tp>
      using __enable_if_is_duration
	= typename enable_if<__is_duration<_Tp>::value, _Tp>::type;

    template<typename _Tp>
      using __disable_if_is_duration
	= typename enable_if<!__is_duration<_Tp>::value, _Tp>::type;

    /// duration_cast
    template<typename _ToDur, typename _Rep, typename _Period>
      constexpr __enable_if_is_duration<_ToDur>
      duration_cast(const duration<_Rep, _Period>& __d)
      {
	typedef typename _ToDur::period				__to_period;
	typedef typename _ToDur::rep				__to_rep;
	typedef ratio_divide<_Period, __to_period> 		__cf;
	typedef typename common_type<__to_rep, _Rep, intmax_t>::type
	  							__cr;
	typedef  __duration_cast_impl<_ToDur, __cf, __cr,
				      __cf::num == 1, __cf::den == 1> __dc;
	return __dc::__cast(__d);
      }

    /// treat_as_floating_point
    template<typename _Rep>
      struct treat_as_floating_point
      : is_floating_point<_Rep>
      { };

#if __cplusplus > 201402L
    template <typename _Rep>
      inline constexpr bool treat_as_floating_point_v =
        treat_as_floating_point<_Rep>::value;
#endif // C++17

#if __cplusplus >= 201703L
# define __cpp_lib_chrono 201611

    template<typename _ToDur, typename _Rep, typename _Period>
      constexpr __enable_if_is_duration<_ToDur>
      floor(const duration<_Rep, _Period>& __d)
      {
	auto __to = chrono::duration_cast<_ToDur>(__d);
	if (__to > __d)
	  return __to - _ToDur{1};
	return __to;
      }

    template<typename _ToDur, typename _Rep, typename _Period>
      constexpr __enable_if_is_duration<_ToDur>
      ceil(const duration<_Rep, _Period>& __d)
      {
	auto __to = chrono::duration_cast<_ToDur>(__d);
	if (__to < __d)
	  return __to + _ToDur{1};
	return __to;
      }

    template <typename _ToDur, typename _Rep, typename _Period>
      constexpr enable_if_t<
	__and_<__is_duration<_ToDur>,
	       __not_<treat_as_floating_point<typename _ToDur::rep>>>::value,
	_ToDur>
      round(const duration<_Rep, _Period>& __d)
      {
	_ToDur __t0 = chrono::floor<_ToDur>(__d);
	_ToDur __t1 = __t0 + _ToDur{1};
	auto __diff0 = __d - __t0;
	auto __diff1 = __t1 - __d;
	if (__diff0 == __diff1)
	{
	    if (__t0.count() & 1)
		return __t1;
	    return __t0;
	}
	else if (__diff0 < __diff1)
	    return __t0;
	return __t1;
      }

    template<typename _Rep, typename _Period>
      constexpr
      enable_if_t<numeric_limits<_Rep>::is_signed, duration<_Rep, _Period>>
      abs(duration<_Rep, _Period> __d)
      {
	if (__d >= __d.zero())
	  return __d;
	return -__d;
      }
#endif // C++17

    /// duration_values
    template<typename _Rep>
      struct duration_values
      {
	static constexpr _Rep
	zero()
	{ return _Rep(0); }

	static constexpr _Rep
	max()
	{ return numeric_limits<_Rep>::max(); }

	static constexpr _Rep
	min()
	{ return numeric_limits<_Rep>::lowest(); }
      };

    template<typename _Tp>
      struct __is_ratio
      : std::false_type
      { };

    template<intmax_t _Num, intmax_t _Den>
      struct __is_ratio<ratio<_Num, _Den>>
      : std::true_type
      { };

    /// duration
    template<typename _Rep, typename _Period>
      struct duration
      {
      private:
	template<typename _Rep2>
	  using __is_float = treat_as_floating_point<_Rep2>;

	// _Period2 is an exact multiple of _Period
	template<typename _Period2>
	  using __is_harmonic
	    = __bool_constant<ratio_divide<_Period2, _Period>::den == 1>;

      public:

	typedef _Rep						rep;
	typedef _Period 					period;

	static_assert(!__is_duration<_Rep>::value, "rep cannot be a duration");
	static_assert(__is_ratio<_Period>::value,
		      "period must be a specialization of ratio");
	static_assert(_Period::num > 0, "period must be positive");

	// ********* construction / copy / destroy
	constexpr duration() = default;

	duration(const duration&) = default;

	template<typename _Rep2, typename = _Require<
		 is_convertible<_Rep2, rep>,
		 __or_<__is_float<rep>, __not_<__is_float<_Rep2>>>>>
	  constexpr explicit duration(const _Rep2& __rep)
	  : __r(static_cast<rep>(__rep)) { }

	template<typename _Rep2, typename _Period2, typename = _Require<
		 __or_<__is_float<rep>,
		       __and_<__is_harmonic<_Period2>,
			      __not_<__is_float<_Rep2>>>>>>
	  constexpr duration(const duration<_Rep2, _Period2>& __d)
	  : __r(duration_cast<duration>(__d).count()) { }

	~duration() = default;
	duration& operator=(const duration&) = default;

	// 20.11.5.2 observer
	constexpr rep
	count() const
	{ return __r; }

	// 20.11.5.3 arithmetic
	constexpr duration
	operator+() const
	{ return *this; }

	constexpr duration
	operator-() const
	{ return duration(-__r); }

	_GLIBCXX17_CONSTEXPR duration&
	operator++()
	{
	  ++__r;
	  return *this;
	}

	_GLIBCXX17_CONSTEXPR duration
	operator++(int)
	{ return duration(__r++); }

	_GLIBCXX17_CONSTEXPR duration&
	operator--()
	{
	  --__r;
	  return *this;
	}

	_GLIBCXX17_CONSTEXPR duration
	operator--(int)
	{ return duration(__r--); }

	_GLIBCXX17_CONSTEXPR duration&
	operator+=(const duration& __d)
	{
	  __r += __d.count();
	  return *this;
	}

	_GLIBCXX17_CONSTEXPR duration&
	operator-=(const duration& __d)
	{
	  __r -= __d.count();
	  return *this;
	}

	_GLIBCXX17_CONSTEXPR duration&
	operator*=(const rep& __rhs)
	{
	  __r *= __rhs;
	  return *this;
	}

	_GLIBCXX17_CONSTEXPR duration&
	operator/=(const rep& __rhs)
	{
	  __r /= __rhs;
	  return *this;
	}

	// DR 934.
	template<typename _Rep2 = rep>
	  _GLIBCXX17_CONSTEXPR
	  typename enable_if<!treat_as_floating_point<_Rep2>::value,
			     duration&>::type
	  operator%=(const rep& __rhs)
	  {
	    __r %= __rhs;
	    return *this;
	  }

	template<typename _Rep2 = rep>
	  _GLIBCXX17_CONSTEXPR
	  typename enable_if<!treat_as_floating_point<_Rep2>::value,
			     duration&>::type
	  operator%=(const duration& __d)
	  {
	    __r %= __d.count();
	    return *this;
	  }

	// 20.11.5.4 special values
	static constexpr duration
	zero()
	{ return duration(duration_values<rep>::zero()); }

	static constexpr duration
	min()
	{ return duration(duration_values<rep>::min()); }

	static constexpr duration
	max()
	{ return duration(duration_values<rep>::max()); }

      private:
	rep __r;
      };

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr typename common_type<duration<_Rep1, _Period1>,
				     duration<_Rep2, _Period2>>::type
      operator+(const duration<_Rep1, _Period1>& __lhs,
		const duration<_Rep2, _Period2>& __rhs)
      {
	typedef duration<_Rep1, _Period1>			__dur1;
	typedef duration<_Rep2, _Period2>			__dur2;
	typedef typename common_type<__dur1,__dur2>::type	__cd;
	return __cd(__cd(__lhs).count() + __cd(__rhs).count());
      }

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr typename common_type<duration<_Rep1, _Period1>,
				     duration<_Rep2, _Period2>>::type
      operator-(const duration<_Rep1, _Period1>& __lhs,
		const duration<_Rep2, _Period2>& __rhs)
      {
	typedef duration<_Rep1, _Period1>			__dur1;
	typedef duration<_Rep2, _Period2>			__dur2;
	typedef typename common_type<__dur1,__dur2>::type	__cd;
	return __cd(__cd(__lhs).count() - __cd(__rhs).count());
      }

    // SFINAE helper to obtain common_type<_Rep1, _Rep2> only if _Rep2
    // is implicitly convertible to it.
    template<typename _Rep1, typename _Rep2,
	     typename _CRep = typename common_type<_Rep1, _Rep2>::type>
      using __common_rep_t
	= typename enable_if<is_convertible<_Rep2, _CRep>::value, _CRep>::type;

    template<typename _Rep1, typename _Period, typename _Rep2>
      constexpr duration<__common_rep_t<_Rep1, _Rep2>, _Period>
      operator*(const duration<_Rep1, _Period>& __d, const _Rep2& __s)
      {
	typedef duration<typename common_type<_Rep1, _Rep2>::type, _Period>
	  __cd;
	return __cd(__cd(__d).count() * __s);
      }

    template<typename _Rep1, typename _Rep2, typename _Period>
      constexpr duration<__common_rep_t<_Rep2, _Rep1>, _Period>
      operator*(const _Rep1& __s, const duration<_Rep2, _Period>& __d)
      { return __d * __s; }

    template<typename _Rep1, typename _Period, typename _Rep2>
      constexpr
      duration<__common_rep_t<_Rep1, __disable_if_is_duration<_Rep2>>, _Period>
      operator/(const duration<_Rep1, _Period>& __d, const _Rep2& __s)
      {
	typedef duration<typename common_type<_Rep1, _Rep2>::type, _Period>
	  __cd;
	return __cd(__cd(__d).count() / __s);
      }

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr typename common_type<_Rep1, _Rep2>::type
      operator/(const duration<_Rep1, _Period1>& __lhs,
		const duration<_Rep2, _Period2>& __rhs)
      {
	typedef duration<_Rep1, _Period1>			__dur1;
	typedef duration<_Rep2, _Period2>			__dur2;
	typedef typename common_type<__dur1,__dur2>::type	__cd;
	return __cd(__lhs).count() / __cd(__rhs).count();
      }

    // DR 934.
    template<typename _Rep1, typename _Period, typename _Rep2>
      constexpr
      duration<__common_rep_t<_Rep1, __disable_if_is_duration<_Rep2>>, _Period>
      operator%(const duration<_Rep1, _Period>& __d, const _Rep2& __s)
      {
	typedef duration<typename common_type<_Rep1, _Rep2>::type, _Period>
	  __cd;
	return __cd(__cd(__d).count() % __s);
      }

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr typename common_type<duration<_Rep1, _Period1>,
				     duration<_Rep2, _Period2>>::type
      operator%(const duration<_Rep1, _Period1>& __lhs,
		const duration<_Rep2, _Period2>& __rhs)
      {
	typedef duration<_Rep1, _Period1>			__dur1;
	typedef duration<_Rep2, _Period2>			__dur2;
	typedef typename common_type<__dur1,__dur2>::type	__cd;
	return __cd(__cd(__lhs).count() % __cd(__rhs).count());
      }

    // comparisons
    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr bool
      operator==(const duration<_Rep1, _Period1>& __lhs,
		 const duration<_Rep2, _Period2>& __rhs)
      {
	typedef duration<_Rep1, _Period1>			__dur1;
	typedef duration<_Rep2, _Period2>			__dur2;
	typedef typename common_type<__dur1,__dur2>::type	__ct;
	return __ct(__lhs).count() == __ct(__rhs).count();
      }

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr bool
      operator<(const duration<_Rep1, _Period1>& __lhs,
		const duration<_Rep2, _Period2>& __rhs)
      {
	typedef duration<_Rep1, _Period1>			__dur1;
	typedef duration<_Rep2, _Period2>			__dur2;
	typedef typename common_type<__dur1,__dur2>::type	__ct;
	return __ct(__lhs).count() < __ct(__rhs).count();
      }

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr bool
      operator!=(const duration<_Rep1, _Period1>& __lhs,
		 const duration<_Rep2, _Period2>& __rhs)
      { return !(__lhs == __rhs); }

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr bool
      operator<=(const duration<_Rep1, _Period1>& __lhs,
		 const duration<_Rep2, _Period2>& __rhs)
      { return !(__rhs < __lhs); }

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr bool
      operator>(const duration<_Rep1, _Period1>& __lhs,
		const duration<_Rep2, _Period2>& __rhs)
      { return __rhs < __lhs; }

    template<typename _Rep1, typename _Period1,
	     typename _Rep2, typename _Period2>
      constexpr bool
      operator>=(const duration<_Rep1, _Period1>& __lhs,
		 const duration<_Rep2, _Period2>& __rhs)
      { return !(__lhs < __rhs); }

    /// nanoseconds
    typedef duration<int64_t, nano> 	    nanoseconds;

    /// microseconds
    typedef duration<int64_t, micro> 	    microseconds;

    /// milliseconds
    typedef duration<int64_t, milli> 	    milliseconds;

    /// seconds
    typedef duration<int64_t> 		    seconds;

    /// minutes
    typedef duration<int64_t, ratio< 60>>   minutes;

    /// hours
    typedef duration<int64_t, ratio<3600>>  hours;

    /// time_point
    template<typename _Clock, typename _Dur>
      struct time_point
      {
	typedef _Clock			  			clock;
	typedef _Dur		  				duration;
	typedef typename duration::rep	  			rep;
	typedef typename duration::period			period;

	constexpr time_point() : __d(duration::zero())
	{ }

	constexpr explicit time_point(const duration& __dur)
	: __d(__dur)
	{ }

	// conversions
	template<typename _Dur2,
		 typename = _Require<is_convertible<_Dur2, _Dur>>>
	  constexpr time_point(const time_point<clock, _Dur2>& __t)
	  : __d(__t.time_since_epoch())
	  { }

	// observer
	constexpr duration
	time_since_epoch() const
	{ return __d; }

	// arithmetic
	_GLIBCXX17_CONSTEXPR time_point&
	operator+=(const duration& __dur)
	{
	  __d += __dur;
	  return *this;
	}

	_GLIBCXX17_CONSTEXPR time_point&
	operator-=(const duration& __dur)
	{
	  __d -= __dur;
	  return *this;
	}

	// special values
	static constexpr time_point
	min()
	{ return time_point(duration::min()); }

	static constexpr time_point
	max()
	{ return time_point(duration::max()); }

      private:
	duration __d;
      };

    /// time_point_cast
    template<typename _ToDur, typename _Clock, typename _Dur>
      constexpr typename enable_if<__is_duration<_ToDur>::value,
				   time_point<_Clock, _ToDur>>::type
      time_point_cast(const time_point<_Clock, _Dur>& __t)
      {
	typedef time_point<_Clock, _ToDur> 			__time_point;
	return __time_point(duration_cast<_ToDur>(__t.time_since_epoch()));
      }

#if __cplusplus > 201402L
    template<typename _ToDur, typename _Clock, typename _Dur>
      constexpr
      enable_if_t<__is_duration<_ToDur>::value, time_point<_Clock, _ToDur>>
      floor(const time_point<_Clock, _Dur>& __tp)
      {
	return time_point<_Clock, _ToDur>{
	    chrono::floor<_ToDur>(__tp.time_since_epoch())};
      }

    template<typename _ToDur, typename _Clock, typename _Dur>
      constexpr
      enable_if_t<__is_duration<_ToDur>::value, time_point<_Clock, _ToDur>>
      ceil(const time_point<_Clock, _Dur>& __tp)
      {
	return time_point<_Clock, _ToDur>{
	    chrono::ceil<_ToDur>(__tp.time_since_epoch())};
      }

    template<typename _ToDur, typename _Clock, typename _Dur>
      constexpr enable_if_t<
	__and_<__is_duration<_ToDur>,
	       __not_<treat_as_floating_point<typename _ToDur::rep>>>::value,
	time_point<_Clock, _ToDur>>
      round(const time_point<_Clock, _Dur>& __tp)
      {
	return time_point<_Clock, _ToDur>{
	    chrono::round<_ToDur>(__tp.time_since_epoch())};
      }
#endif // C++17

    template<typename _Clock, typename _Dur1,
	     typename _Rep2, typename _Period2>
      constexpr time_point<_Clock,
	typename common_type<_Dur1, duration<_Rep2, _Period2>>::type>
      operator+(const time_point<_Clock, _Dur1>& __lhs,
		const duration<_Rep2, _Period2>& __rhs)
      {
	typedef duration<_Rep2, _Period2>			__dur2;
	typedef typename common_type<_Dur1,__dur2>::type	__ct;
	typedef time_point<_Clock, __ct> 			__time_point;
	return __time_point(__lhs.time_since_epoch() + __rhs);
      }

    template<typename _Rep1, typename _Period1,
	     typename _Clock, typename _Dur2>
      constexpr time_point<_Clock,
	typename common_type<duration<_Rep1, _Period1>, _Dur2>::type>
      operator+(const duration<_Rep1, _Period1>& __lhs,
		const time_point<_Clock, _Dur2>& __rhs)
      {
	typedef duration<_Rep1, _Period1>			__dur1;
	typedef typename common_type<__dur1,_Dur2>::type	__ct;
	typedef time_point<_Clock, __ct> 			__time_point;
	return __time_point(__rhs.time_since_epoch() + __lhs);
      }

    template<typename _Clock, typename _Dur1,
	     typename _Rep2, typename _Period2>
      constexpr time_point<_Clock,
	typename common_type<_Dur1, duration<_Rep2, _Period2>>::type>
      operator-(const time_point<_Clock, _Dur1>& __lhs,
		const duration<_Rep2, _Period2>& __rhs)
      {
	typedef duration<_Rep2, _Period2>			__dur2;
	typedef typename common_type<_Dur1,__dur2>::type	__ct;
	typedef time_point<_Clock, __ct> 			__time_point;
	return __time_point(__lhs.time_since_epoch() -__rhs);
      }

    template<typename _Clock, typename _Dur1, typename _Dur2>
      constexpr typename common_type<_Dur1, _Dur2>::type
      operator-(const time_point<_Clock, _Dur1>& __lhs,
		const time_point<_Clock, _Dur2>& __rhs)
      { return __lhs.time_since_epoch() - __rhs.time_since_epoch(); }

    template<typename _Clock, typename _Dur1, typename _Dur2>
      constexpr bool
      operator==(const time_point<_Clock, _Dur1>& __lhs,
		 const time_point<_Clock, _Dur2>& __rhs)
      { return __lhs.time_since_epoch() == __rhs.time_since_epoch(); }

    template<typename _Clock, typename _Dur1, typename _Dur2>
      constexpr bool
      operator!=(const time_point<_Clock, _Dur1>& __lhs,
		 const time_point<_Clock, _Dur2>& __rhs)
      { return !(__lhs == __rhs); }

    template<typename _Clock, typename _Dur1, typename _Dur2>
      constexpr bool
      operator<(const time_point<_Clock, _Dur1>& __lhs,
		const time_point<_Clock, _Dur2>& __rhs)
      { return  __lhs.time_since_epoch() < __rhs.time_since_epoch(); }

    template<typename _Clock, typename _Dur1, typename _Dur2>
      constexpr bool
      operator<=(const time_point<_Clock, _Dur1>& __lhs,
		 const time_point<_Clock, _Dur2>& __rhs)
      { return !(__rhs < __lhs); }

    template<typename _Clock, typename _Dur1, typename _Dur2>
      constexpr bool
      operator>(const time_point<_Clock, _Dur1>& __lhs,
		const time_point<_Clock, _Dur2>& __rhs)
      { return __rhs < __lhs; }

    template<typename _Clock, typename _Dur1, typename _Dur2>
      constexpr bool
      operator>=(const time_point<_Clock, _Dur1>& __lhs,
		 const time_point<_Clock, _Dur2>& __rhs)
      { return !(__lhs < __rhs); }


    // Clocks.

    // Why nanosecond resolution as the default?
    // Why have std::system_clock always count in the highest
    // resolution (ie nanoseconds), even if on some OSes the low 3
    // or 9 decimal digits will be always zero? This allows later
    // implementations to change the system_clock::now()
    // implementation any time to provide better resolution without
    // changing function signature or units.

    // To support the (forward) evolution of the library's defined
    // clocks, wrap inside inline namespace so that the current
    // defintions of system_clock, steady_clock, and
    // high_resolution_clock types are uniquely mangled. This way, new
    // code can use the latests clocks, while the library can contain
    // compatibility definitions for previous versions.  At some
    // point, when these clocks settle down, the inlined namespaces
    // can be removed.  XXX GLIBCXX_ABI Deprecated
    inline namespace _V2 {

    /**
     *  @brief System clock.
     *
     *  Time returned represents wall time from the system-wide clock.
    */
    struct system_clock
    {
      typedef chrono::nanoseconds     				duration;
      typedef duration::rep    					rep;
      typedef duration::period 					period;
      typedef chrono::time_point<system_clock, duration> 	time_point;

      static_assert(system_clock::duration::min()
		    < system_clock::duration::zero(),
		    "a clock's minimum duration cannot be less than its epoch");

      static constexpr bool is_steady = false;

      static time_point
      now() noexcept;

      // Map to C API
      static std::time_t
      to_time_t(const time_point& __t) noexcept
      {
	return std::time_t(duration_cast<chrono::seconds>
			   (__t.time_since_epoch()).count());
      }

      static time_point
      from_time_t(std::time_t __t) noexcept
      {
	typedef chrono::time_point<system_clock, seconds>	__from;
	return time_point_cast<system_clock::duration>
	       (__from(chrono::seconds(__t)));
      }
    };


    /**
     *  @brief Monotonic clock
     *
     *  Time returned has the property of only increasing at a uniform rate.
    */
    struct steady_clock
    {
      typedef chrono::nanoseconds 				duration;
      typedef duration::rep	  				rep;
      typedef duration::period	  				period;
      typedef chrono::time_point<steady_clock, duration> 	time_point;

      static constexpr bool is_steady = true;

      static time_point
      now() noexcept;
    };


    /**
     *  @brief Highest-resolution clock
     *
     *  This is the clock "with the shortest tick period." Alias to
     *  std::system_clock until higher-than-nanosecond definitions
     *  become feasible.
    */
    using high_resolution_clock = system_clock;

    } // end inline namespace _V2
  } // namespace chrono

#if __cplusplus > 201103L

#define __cpp_lib_chrono_udls 201304

  inline namespace literals
  {
  inline namespace chrono_literals
  {
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wliteral-suffix"
    template<typename _Rep, unsigned long long _Val>
      struct _Checked_integral_constant
      : integral_constant<_Rep, static_cast<_Rep>(_Val)>
      {
	static_assert(_Checked_integral_constant::value >= 0
		      && _Checked_integral_constant::value == _Val,
		      "literal value cannot be represented by duration type");
      };

    template<typename _Dur, char... _Digits>
      constexpr _Dur __check_overflow()
      {
	using _Val = __parse_int::_Parse_int<_Digits...>;
	using _Rep = typename _Dur::rep;
	// TODO: should be simply integral_constant<_Rep, _Val::value>
	// but GCC doesn't reject narrowing conversions to _Rep.
	using _CheckedVal = _Checked_integral_constant<_Rep, _Val::value>;
	return _Dur{_CheckedVal::value};
      }

    constexpr chrono::duration<long double, ratio<3600,1>>
    operator""h(long double __hours)
    { return chrono::duration<long double, ratio<3600,1>>{__hours}; }

    template <char... _Digits>
      constexpr chrono::hours
      operator""h()
      { return __check_overflow<chrono::hours, _Digits...>(); }

    constexpr chrono::duration<long double, ratio<60,1>>
    operator""min(long double __mins)
    { return chrono::duration<long double, ratio<60,1>>{__mins}; }

    template <char... _Digits>
      constexpr chrono::minutes
      operator""min()
      { return __check_overflow<chrono::minutes, _Digits...>(); }

    constexpr chrono::duration<long double>
    operator""s(long double __secs)
    { return chrono::duration<long double>{__secs}; }

    template <char... _Digits>
      constexpr chrono::seconds
      operator""s()
      { return __check_overflow<chrono::seconds, _Digits...>(); }

    constexpr chrono::duration<long double, milli>
    operator""ms(long double __msecs)
    { return chrono::duration<long double, milli>{__msecs}; }

    template <char... _Digits>
      constexpr chrono::milliseconds
      operator""ms()
      { return __check_overflow<chrono::milliseconds, _Digits...>(); }

    constexpr chrono::duration<long double, micro>
    operator""us(long double __usecs)
    { return chrono::duration<long double, micro>{__usecs}; }

    template <char... _Digits>
      constexpr chrono::microseconds
      operator""us()
      { return __check_overflow<chrono::microseconds, _Digits...>(); }

    constexpr chrono::duration<long double, nano>
    operator""ns(long double __nsecs)
    { return chrono::duration<long double, nano>{__nsecs}; }

    template <char... _Digits>
      constexpr chrono::nanoseconds
      operator""ns()
      { return __check_overflow<chrono::nanoseconds, _Digits...>(); }

#pragma GCC diagnostic pop
  } // inline namespace chrono_literals
  } // inline namespace literals

  namespace chrono
  {
    using namespace literals::chrono_literals;
  } // namespace chrono

#endif // C++14

  // @} group chrono

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace std

#endif //_GLIBCXX_USE_C99_STDINT_TR1

#endif // C++11

#endif //_GLIBCXX_CHRONO
