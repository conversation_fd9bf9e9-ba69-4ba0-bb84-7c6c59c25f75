/*
 * Copyright (C) 2011 <PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */


#ifndef __RPCSAL_H_VERSION__
# define __RPCSAL_H_VERSION__  100
#endif

#define __RPC__deref_in
#define __RPC__deref_in_opt
#define __RPC__deref_in_string
#define __RPC__deref_in_opt_string
#define __RPC__deref_in_ecount(size)
#define __RPC__deref_in_ecount_opt(size)
#define __RPC__deref_in_ecount_opt_string(size)
#define __RPC__deref_in_ecount_full(size)
#define __RPC__deref_in_ecount_full_opt(size)
#define __RPC__deref_in_ecount_full_string(size)
#define __RPC__deref_in_ecount_full_opt_string(size)
#define __RPC__deref_in_ecount_part(size, length)
#define __RPC__deref_in_ecount_part_opt(size, length)
#define __RPC__deref_in_xcount(size)
#define __RPC__deref_in_xcount_opt(size)
#define __RPC__deref_in_xcount_opt_string(size)
#define __RPC__deref_in_xcount_full(size)
#define __RPC__deref_in_xcount_full_opt(size)
#define __RPC__deref_in_xcount_full_string(size)
#define __RPC__deref_in_xcount_full_opt_string(size)
#define __RPC__deref_in_xcount_part(size, length)
#define __RPC__deref_in_xcount_part_opt(size, length)

#define __RPC__deref_inout
#define __RPC__deref_inout_opt
#define __RPC__deref_inout_string
#define __RPC__deref_inout_opt_string
#define __RPC__deref_inout_ecount_opt(size)
#define __RPC__deref_inout_ecount_full(size)
#define __RPC__deref_inout_ecount_full_opt(size)
#define __RPC__deref_inout_ecount_full_string(size)
#define __RPC__deref_inout_ecount_full_opt_string(size)
#define __RPC__deref_inout_ecount_part_opt(size, length)
#define __RPC__deref_inout_xcount_opt(size)
#define __RPC__deref_inout_xcount_full(size)
#define __RPC__deref_inout_xcount_full_opt(size)
#define __RPC__deref_inout_xcount_full_string(size)
#define __RPC__deref_inout_xcount_full_opt_string(size)
#define __RPC__deref_inout_xcount_part_opt(size, length)

#define __RPC__deref_out
#define __RPC__deref_out_opt
#define __RPC__deref_out_string
#define __RPC__deref_out_opt_string
#define __RPC__deref_out_ecount(size)
#define __RPC__deref_out_ecount_opt(size)
#define __RPC__deref_out_ecount_full(size)
#define __RPC__deref_out_ecount_full_opt(size)
#define __RPC__deref_out_ecount_full_string(size)
#define __RPC__deref_out_ecount_full_opt_string(size)
#define __RPC__deref_out_ecount_part(size, length)
#define __RPC__deref_out_ecount_part_opt(size, length)
#define __RPC__deref_out_xcount(size)
#define __RPC__deref_out_xcount_opt(size)
#define __RPC__deref_out_xcount_full(size)
#define __RPC__deref_out_xcount_full_opt(size)
#define __RPC__deref_out_xcount_full_string(size)
#define __RPC__deref_out_xcount_full_opt_string(size)
#define __RPC__deref_out_xcount_part(size, length)
#define __RPC__deref_out_xcount_part_opt(size, length)

#define __RPC__deref_opt_in
#define __RPC__deref_opt_in_opt
#define __RPC__deref_opt_in_string
#define __RPC__deref_opt_in_opt_string

#define __RPC__deref_opt_inout
#define __RPC__deref_opt_inout_opt
#define __RPC__deref_opt_inout_string
#define __RPC__deref_opt_inout_opt_string
#define __RPC__deref_opt_inout_ecount(size)
#define __RPC__deref_opt_inout_ecount_opt(size)
#define __RPC__deref_opt_inout_ecount_full(size)
#define __RPC__deref_opt_inout_ecount_full_opt(size)
#define __RPC__deref_opt_inout_ecount_full_string(size)
#define __RPC__deref_opt_inout_ecount_full_opt_string(size)
#define __RPC__deref_opt_inout_ecount_part(size, length)
#define __RPC__deref_opt_inout_ecount_part_opt(size, length)
#define __RPC__deref_opt_inout_xcount(size)
#define __RPC__deref_opt_inout_xcount_opt(size)
#define __RPC__deref_opt_inout_xcount_full(size)
#define __RPC__deref_opt_inout_xcount_full_opt(size)
#define __RPC__deref_opt_inout_xcount_full_string(size)
#define __RPC__deref_opt_inout_xcount_full_opt_string(size)
#define __RPC__deref_opt_inout_xcount_part(size, length)
#define __RPC__deref_opt_inout_xcount_part_opt(size, length)

#define __RPC__in
#define __RPC__in_opt
#define __RPC__in_string
#define __RPC__in_opt_string
#define __RPC__in_ecount(size)
#define __RPC__in_ecount_opt(size)
#define __RPC__in_ecount_full(size)
#define __RPC__in_ecount_full_opt(size)
#define __RPC__in_ecount_full_string(size)
#define __RPC__in_ecount_full_opt_string(size)
#define __RPC__in_ecount_part(size, length)
#define __RPC__in_ecount_part_opt(size, length)
#define __RPC__in_xcount(size)
#define __RPC__in_xcount_opt(size)
#define __RPC__in_xcount_full(size)
#define __RPC__in_xcount_full_opt(size)
#define __RPC__in_xcount_full_string(size)
#define __RPC__in_xcount_full_opt_string(size)
#define __RPC__in_xcount_part(size, length)
#define __RPC__in_xcount_part_opt(size, length)

#define __RPC__inout
#define __RPC__inout_opt
#define __RPC__inout_string
#define __RPC__inout_opt_string
#define __RPC__opt_inout
#define __RPC__inout_ecount(size)
#define __RPC__inout_ecount_opt(size)
#define __RPC__inout_ecount_full(size)
#define __RPC__inout_ecount_full_opt(size)
#define __RPC__inout_ecount_full_string(size)
#define __RPC__inout_ecount_full_opt_string(size)
#define __RPC__inout_ecount_part(size, length)
#define __RPC__inout_ecount_part_opt(size, length)
#define __RPC__inout_xcount(size)
#define __RPC__inout_xcount_opt(size)
#define __RPC__inout_xcount_full(size)
#define __RPC__inout_xcount_full_opt(size)
#define __RPC__inout_xcount_full_string(size)
#define __RPC__inout_xcount_full_opt_string(size)
#define __RPC__inout_xcount_part(size, length)
#define __RPC__inout_xcount_part_opt(size, length)

#define __RPC__out
#define __RPC__out_ecount(size)
#define __RPC__out_ecount_full(size)
#define __RPC__out_ecount_full_string(size)
#define __RPC__out_ecount_part(size, length)
#define __RPC__out_xcount(size)
#define __RPC__out_xcount_full(size)
#define __RPC__out_xcount_full_string(size)
#define __RPC__out_xcount_part(size, length)

#define __RPC_full_pointer
#define __RPC_ref_pointer
#define __RPC_string
#define __RPC_unique_pointer

#define __RPC__range(min,max)
#define __RPC__in_range(min,max)
