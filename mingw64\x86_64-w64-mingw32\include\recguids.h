/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
DEFINE_GUID(IID_IReconcileInitiator,0x99180161,0xDA16,0x101A,0x93,0x5C,0x44,0x45,0x53,0x54,0x00,0x00);
DEFINE_GUID(IID_IReconcilableObject,0x99180162,0xDA16,0x101A,0x93,0x5C,0x44,0x45,0x53,0x54,0x00,0x00);
DEFINE_GUID(IID_INotifyReplica,0x99180163,0xDA16,0x101A,0x93,0x5C,0x44,0x45,0x53,0x54,0x00,0x00);
DEFINE_GUID(IID_IBriefcaseInitiator,0x99180164,0xDA16,0x101A,0x93,0x5C,0x44,0x45,0x53,0x54,0x00,0x00);
