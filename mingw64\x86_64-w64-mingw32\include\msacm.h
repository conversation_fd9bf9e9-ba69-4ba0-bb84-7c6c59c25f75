/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _INC_ACM
#define _INC_ACM

#if !defined(_INC_MMREG) || (_INC_MMREG < 142)
#ifndef RC_INVOKED
#error MMREG.H version 142 or greater to be included first
#endif
#endif

#if defined(UNICODE) && !defined(_UNICODE)
#ifndef RC_INVOKED
#warning MSACM.H: Defining _UNICODE because application defined UNICODE
#endif
#define _UNICODE
#endif

#include "pshpack1.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifndef DRV_MAPPER_PREFERRED_INPUT_GET
#define DRV_MAPPER_PREFERRED_INPUT_GET (DRV_USER + 0)
#endif

#ifndef DRV_MAPPER_PREFERRED_OUTPUT_GET
#define DRV_MAPPER_PREFERRED_OUTPUT_GET (DRV_USER + 2)
#endif

#ifndef DRVM_MAPPER_STATUS
#define DRVM_MAPPER (0x2000)
#define DRVM_MAPPER_STATUS (DRVM_MAPPER+0)
#endif

#ifndef WIDM_MAPPER_STATUS
#define WIDM_MAPPER_STATUS (DRVM_MAPPER_STATUS + 0)
#define WAVEIN_MAPPER_STATUS_DEVICE 0
#define WAVEIN_MAPPER_STATUS_MAPPED 1
#define WAVEIN_MAPPER_STATUS_FORMAT 2
#endif

#ifndef WODM_MAPPER_STATUS
#define WODM_MAPPER_STATUS (DRVM_MAPPER_STATUS + 0)
#define WAVEOUT_MAPPER_STATUS_DEVICE 0
#define WAVEOUT_MAPPER_STATUS_MAPPED 1
#define WAVEOUT_MAPPER_STATUS_FORMAT 2
#endif

#define ACMAPI WINAPI

  DECLARE_HANDLE(HACMDRIVERID);

  typedef HACMDRIVERID *PHACMDRIVERID;
  typedef HACMDRIVERID *LPHACMDRIVERID;

  DECLARE_HANDLE(HACMDRIVER);

  typedef HACMDRIVER *PHACMDRIVER;
  typedef HACMDRIVER *LPHACMDRIVER;

  DECLARE_HANDLE(HACMSTREAM);

  typedef HACMSTREAM *PHACMSTREAM;
  typedef HACMSTREAM *LPHACMSTREAM;

  DECLARE_HANDLE(HACMOBJ);

  typedef HACMOBJ *PHACMOBJ;
  typedef HACMOBJ *LPHACMOBJ;

#ifndef _MMRESULT_
#define _MMRESULT_
  typedef UINT MMRESULT;
#endif

#define ACMERR_BASE (512)
#define ACMERR_NOTPOSSIBLE (ACMERR_BASE + 0)
#define ACMERR_BUSY (ACMERR_BASE + 1)
#define ACMERR_UNPREPARED (ACMERR_BASE + 2)
#define ACMERR_CANCELED (ACMERR_BASE + 3)

#define MM_ACM_OPEN (MM_STREAM_OPEN)
#define MM_ACM_CLOSE (MM_STREAM_CLOSE)
#define MM_ACM_DONE (MM_STREAM_DONE)

  DWORD ACMAPI acmGetVersion(void);
  MMRESULT ACMAPI acmMetrics(HACMOBJ hao,UINT uMetric,LPVOID pMetric);

#define ACM_METRIC_COUNT_DRIVERS 1
#define ACM_METRIC_COUNT_CODECS 2
#define ACM_METRIC_COUNT_CONVERTERS 3
#define ACM_METRIC_COUNT_FILTERS 4
#define ACM_METRIC_COUNT_DISABLED 5
#define ACM_METRIC_COUNT_HARDWARE 6
#define ACM_METRIC_COUNT_LOCAL_DRIVERS 20
#define ACM_METRIC_COUNT_LOCAL_CODECS 21
#define ACM_METRIC_COUNT_LOCAL_CONVERTERS 22
#define ACM_METRIC_COUNT_LOCAL_FILTERS 23
#define ACM_METRIC_COUNT_LOCAL_DISABLED 24
#define ACM_METRIC_HARDWARE_WAVE_INPUT 30
#define ACM_METRIC_HARDWARE_WAVE_OUTPUT 31
#define ACM_METRIC_MAX_SIZE_FORMAT 50
#define ACM_METRIC_MAX_SIZE_FILTER 51
#define ACM_METRIC_DRIVER_SUPPORT 100
#define ACM_METRIC_DRIVER_PRIORITY 101

  typedef WINBOOL (CALLBACK *ACMDRIVERENUMCB)(HACMDRIVERID hadid,DWORD_PTR dwInstance,DWORD fdwSupport);

  MMRESULT ACMAPI acmDriverEnum(ACMDRIVERENUMCB fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);

#define ACM_DRIVERENUMF_NOLOCAL __MSABI_LONG(0x40000000)
#define ACM_DRIVERENUMF_DISABLED __MSABI_LONG(0x80000000)

  MMRESULT ACMAPI acmDriverID(HACMOBJ hao,LPHACMDRIVERID phadid,DWORD fdwDriverID);
  MMRESULT ACMAPI acmDriverAddA(LPHACMDRIVERID phadid,HINSTANCE hinstModule,LPARAM lParam,DWORD dwPriority,DWORD fdwAdd);
  MMRESULT ACMAPI acmDriverAddW(LPHACMDRIVERID phadid,HINSTANCE hinstModule,LPARAM lParam,DWORD dwPriority,DWORD fdwAdd);
#ifdef _UNICODE
#define acmDriverAdd acmDriverAddW
#else
#define acmDriverAdd acmDriverAddA
#endif

#define ACM_DRIVERADDF_NAME __MSABI_LONG(0x00000001)
#define ACM_DRIVERADDF_FUNCTION __MSABI_LONG(0x00000003)
#define ACM_DRIVERADDF_NOTIFYHWND __MSABI_LONG(0x00000004)
#define ACM_DRIVERADDF_TYPEMASK __MSABI_LONG(0x00000007)
#define ACM_DRIVERADDF_LOCAL __MSABI_LONG(0x00000000)
#define ACM_DRIVERADDF_GLOBAL __MSABI_LONG(0x00000008)

  typedef LRESULT (CALLBACK *ACMDRIVERPROC)(DWORD_PTR,HACMDRIVERID,UINT,LPARAM,LPARAM);
  typedef ACMDRIVERPROC *LPACMDRIVERPROC;

  MMRESULT ACMAPI acmDriverRemove(HACMDRIVERID hadid,DWORD fdwRemove);
  MMRESULT ACMAPI acmDriverOpen(LPHACMDRIVER phad,HACMDRIVERID hadid,DWORD fdwOpen);
  MMRESULT ACMAPI acmDriverClose(HACMDRIVER had,DWORD fdwClose);
  LRESULT ACMAPI acmDriverMessage(HACMDRIVER had,UINT uMsg,LPARAM lParam1,LPARAM lParam2);

#define ACMDM_USER (DRV_USER + 0x0000)
#define ACMDM_RESERVED_LOW (DRV_USER + 0x2000)
#define ACMDM_RESERVED_HIGH (DRV_USER + 0x2FFF)

#define ACMDM_BASE ACMDM_RESERVED_LOW

#define ACMDM_DRIVER_ABOUT (ACMDM_BASE + 11)

  MMRESULT ACMAPI acmDriverPriority(HACMDRIVERID hadid,DWORD dwPriority,DWORD fdwPriority);

#define ACM_DRIVERPRIORITYF_ENABLE __MSABI_LONG(0x00000001)
#define ACM_DRIVERPRIORITYF_DISABLE __MSABI_LONG(0x00000002)
#define ACM_DRIVERPRIORITYF_ABLEMASK __MSABI_LONG(0x00000003)
#define ACM_DRIVERPRIORITYF_BEGIN __MSABI_LONG(0x00010000)
#define ACM_DRIVERPRIORITYF_END __MSABI_LONG(0x00020000)
#define ACM_DRIVERPRIORITYF_DEFERMASK __MSABI_LONG(0x00030000)

#define ACMDRIVERDETAILS_SHORTNAME_CHARS 32
#define ACMDRIVERDETAILS_LONGNAME_CHARS 128
#define ACMDRIVERDETAILS_COPYRIGHT_CHARS 80
#define ACMDRIVERDETAILS_LICENSING_CHARS 128
#define ACMDRIVERDETAILS_FEATURES_CHARS 512

  typedef struct tACMDRIVERDETAILSA {
    DWORD cbStruct;
    FOURCC fccType;
    FOURCC fccComp;
    WORD wMid;
    WORD wPid;
    DWORD vdwACM;
    DWORD vdwDriver;
    DWORD fdwSupport;
    DWORD cFormatTags;
    DWORD cFilterTags;
    HICON hicon;
    char szShortName[ACMDRIVERDETAILS_SHORTNAME_CHARS];
    char szLongName[ACMDRIVERDETAILS_LONGNAME_CHARS];
    char szCopyright[ACMDRIVERDETAILS_COPYRIGHT_CHARS];
    char szLicensing[ACMDRIVERDETAILS_LICENSING_CHARS];
    char szFeatures[ACMDRIVERDETAILS_FEATURES_CHARS];
  } ACMDRIVERDETAILSA,*PACMDRIVERDETAILSA,*LPACMDRIVERDETAILSA;

  typedef struct tACMDRIVERDETAILSW {
    DWORD cbStruct;
    FOURCC fccType;
    FOURCC fccComp;
    WORD wMid;
    WORD wPid;
    DWORD vdwACM;
    DWORD vdwDriver;
    DWORD fdwSupport;
    DWORD cFormatTags;
    DWORD cFilterTags;
    HICON hicon;
    WCHAR szShortName[ACMDRIVERDETAILS_SHORTNAME_CHARS];
    WCHAR szLongName[ACMDRIVERDETAILS_LONGNAME_CHARS];
    WCHAR szCopyright[ACMDRIVERDETAILS_COPYRIGHT_CHARS];
    WCHAR szLicensing[ACMDRIVERDETAILS_LICENSING_CHARS];
    WCHAR szFeatures[ACMDRIVERDETAILS_FEATURES_CHARS];
  } ACMDRIVERDETAILSW,*PACMDRIVERDETAILSW,*LPACMDRIVERDETAILSW;
#ifdef _UNICODE
#define ACMDRIVERDETAILS ACMDRIVERDETAILSW
#define PACMDRIVERDETAILS PACMDRIVERDETAILSW
#define LPACMDRIVERDETAILS LPACMDRIVERDETAILSW
#else
#define ACMDRIVERDETAILS ACMDRIVERDETAILSA
#define PACMDRIVERDETAILS PACMDRIVERDETAILSA
#define LPACMDRIVERDETAILS LPACMDRIVERDETAILSA
#endif

#define ACMDRIVERDETAILS_FCCTYPE_AUDIOCODEC mmioFOURCC('a','u','d','c')
#define ACMDRIVERDETAILS_FCCCOMP_UNDEFINED mmioFOURCC('\0','\0','\0','\0')

#define ACMDRIVERDETAILS_SUPPORTF_CODEC __MSABI_LONG(0x00000001)
#define ACMDRIVERDETAILS_SUPPORTF_CONVERTER __MSABI_LONG(0x00000002)
#define ACMDRIVERDETAILS_SUPPORTF_FILTER __MSABI_LONG(0x00000004)
#define ACMDRIVERDETAILS_SUPPORTF_HARDWARE __MSABI_LONG(0x00000008)
#define ACMDRIVERDETAILS_SUPPORTF_ASYNC __MSABI_LONG(0x00000010)
#define ACMDRIVERDETAILS_SUPPORTF_LOCAL __MSABI_LONG(0x40000000)
#define ACMDRIVERDETAILS_SUPPORTF_DISABLED __MSABI_LONG(0x80000000)

  MMRESULT ACMAPI acmDriverDetailsA(HACMDRIVERID hadid,LPACMDRIVERDETAILSA padd,DWORD fdwDetails);
  MMRESULT ACMAPI acmDriverDetailsW(HACMDRIVERID hadid,LPACMDRIVERDETAILSW padd,DWORD fdwDetails);
#ifdef _UNICODE
#define acmDriverDetails acmDriverDetailsW
#else
#define acmDriverDetails acmDriverDetailsA
#endif

#define ACMFORMATTAGDETAILS_FORMATTAG_CHARS 48

  typedef struct tACMFORMATTAGDETAILSA {
    DWORD cbStruct;
    DWORD dwFormatTagIndex;
    DWORD dwFormatTag;
    DWORD cbFormatSize;
    DWORD fdwSupport;
    DWORD cStandardFormats;
    char szFormatTag[ACMFORMATTAGDETAILS_FORMATTAG_CHARS];
  } ACMFORMATTAGDETAILSA,*PACMFORMATTAGDETAILSA,*LPACMFORMATTAGDETAILSA;

  typedef struct tACMFORMATTAGDETAILSW {
    DWORD cbStruct;
    DWORD dwFormatTagIndex;
    DWORD dwFormatTag;
    DWORD cbFormatSize;
    DWORD fdwSupport;
    DWORD cStandardFormats;
    WCHAR szFormatTag[ACMFORMATTAGDETAILS_FORMATTAG_CHARS];
  } ACMFORMATTAGDETAILSW,*PACMFORMATTAGDETAILSW,*LPACMFORMATTAGDETAILSW;
#ifdef _UNICODE
#define ACMFORMATTAGDETAILS ACMFORMATTAGDETAILSW
#define PACMFORMATTAGDETAILS PACMFORMATTAGDETAILSW
#define LPACMFORMATTAGDETAILS LPACMFORMATTAGDETAILSW
#else
#define ACMFORMATTAGDETAILS ACMFORMATTAGDETAILSA
#define PACMFORMATTAGDETAILS PACMFORMATTAGDETAILSA
#define LPACMFORMATTAGDETAILS LPACMFORMATTAGDETAILSA
#endif

  MMRESULT ACMAPI acmFormatTagDetailsA(HACMDRIVER had,LPACMFORMATTAGDETAILSA paftd,DWORD fdwDetails);
  MMRESULT ACMAPI acmFormatTagDetailsW(HACMDRIVER had,LPACMFORMATTAGDETAILSW paftd,DWORD fdwDetails);
#ifdef _UNICODE
#define acmFormatTagDetails acmFormatTagDetailsW
#else
#define acmFormatTagDetails acmFormatTagDetailsA
#endif

#define ACM_FORMATTAGDETAILSF_INDEX __MSABI_LONG(0x00000000)
#define ACM_FORMATTAGDETAILSF_FORMATTAG __MSABI_LONG(0x00000001)
#define ACM_FORMATTAGDETAILSF_LARGESTSIZE __MSABI_LONG(0x00000002)
#define ACM_FORMATTAGDETAILSF_QUERYMASK __MSABI_LONG(0x0000000F)

  typedef WINBOOL (CALLBACK *ACMFORMATTAGENUMCBA)(HACMDRIVERID hadid,LPACMFORMATTAGDETAILSA paftd,DWORD_PTR dwInstance,DWORD fdwSupport);
  typedef WINBOOL (CALLBACK *ACMFORMATTAGENUMCBW)(HACMDRIVERID hadid,LPACMFORMATTAGDETAILSW paftd,DWORD_PTR dwInstance,DWORD fdwSupport);

  MMRESULT ACMAPI acmFormatTagEnumA(HACMDRIVER had,LPACMFORMATTAGDETAILSA paftd,ACMFORMATTAGENUMCBA fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);
  MMRESULT ACMAPI acmFormatTagEnumW(HACMDRIVER had,LPACMFORMATTAGDETAILSW paftd,ACMFORMATTAGENUMCBW fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);
#ifdef _UNICODE
#define ACMFORMATTAGENUMCB ACMFORMATTAGENUMCBW
#define acmFormatTagEnum acmFormatTagEnumW
#else
#define ACMFORMATTAGENUMCB ACMFORMATTAGENUMCBA
#define acmFormatTagEnum acmFormatTagEnumA
#endif

#define ACMFORMATDETAILS_FORMAT_CHARS 128

  typedef struct tACMFORMATDETAILSA {
    DWORD cbStruct;
    DWORD dwFormatIndex;
    DWORD dwFormatTag;
    DWORD fdwSupport;
    LPWAVEFORMATEX pwfx;
    DWORD cbwfx;
    char szFormat[ACMFORMATDETAILS_FORMAT_CHARS];
  } ACMFORMATDETAILSA,*PACMFORMATDETAILSA,*LPACMFORMATDETAILSA;

  typedef struct tACMFORMATDETAILSW {
    DWORD cbStruct;
    DWORD dwFormatIndex;
    DWORD dwFormatTag;
    DWORD fdwSupport;
    LPWAVEFORMATEX pwfx;
    DWORD cbwfx;
    WCHAR szFormat[ACMFORMATDETAILS_FORMAT_CHARS];
  } ACMFORMATDETAILSW,*PACMFORMATDETAILSW,*LPACMFORMATDETAILSW;
#ifdef _UNICODE
#define ACMFORMATDETAILS ACMFORMATDETAILSW
#define PACMFORMATDETAILS PACMFORMATDETAILSW
#define LPACMFORMATDETAILS LPACMFORMATDETAILSW
#else
#define ACMFORMATDETAILS ACMFORMATDETAILSA
#define PACMFORMATDETAILS PACMFORMATDETAILSA
#define LPACMFORMATDETAILS LPACMFORMATDETAILSA
#endif

  MMRESULT ACMAPI acmFormatDetailsA(HACMDRIVER had,LPACMFORMATDETAILSA pafd,DWORD fdwDetails);
  MMRESULT ACMAPI acmFormatDetailsW(HACMDRIVER had,LPACMFORMATDETAILSW pafd,DWORD fdwDetails);
#ifdef _UNICODE
#define acmFormatDetails acmFormatDetailsW
#else
#define acmFormatDetails acmFormatDetailsA
#endif

#define ACM_FORMATDETAILSF_INDEX __MSABI_LONG(0x00000000)
#define ACM_FORMATDETAILSF_FORMAT __MSABI_LONG(0x00000001)
#define ACM_FORMATDETAILSF_QUERYMASK __MSABI_LONG(0x0000000F)

  typedef WINBOOL (CALLBACK *ACMFORMATENUMCBA)(HACMDRIVERID hadid,LPACMFORMATDETAILSA pafd,DWORD_PTR dwInstance,DWORD fdwSupport);
  typedef WINBOOL (CALLBACK *ACMFORMATENUMCBW)(HACMDRIVERID hadid,LPACMFORMATDETAILSW pafd,DWORD_PTR dwInstance,DWORD fdwSupport);

  MMRESULT ACMAPI acmFormatEnumA(HACMDRIVER had,LPACMFORMATDETAILSA pafd,ACMFORMATENUMCBA fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);
  MMRESULT ACMAPI acmFormatEnumW(HACMDRIVER had,LPACMFORMATDETAILSW pafd,ACMFORMATENUMCBW fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);
#ifdef _UNICODE
#define ACMFORMATENUMCB ACMFORMATENUMCBW
#define acmFormatEnum acmFormatEnumW
#else
#define ACMFORMATENUMCB ACMFORMATENUMCBA
#define acmFormatEnum acmFormatEnumA
#endif

#define ACM_FORMATENUMF_WFORMATTAG __MSABI_LONG(0x00010000)
#define ACM_FORMATENUMF_NCHANNELS __MSABI_LONG(0x00020000)
#define ACM_FORMATENUMF_NSAMPLESPERSEC __MSABI_LONG(0x00040000)
#define ACM_FORMATENUMF_WBITSPERSAMPLE __MSABI_LONG(0x00080000)
#define ACM_FORMATENUMF_CONVERT __MSABI_LONG(0x00100000)
#define ACM_FORMATENUMF_SUGGEST __MSABI_LONG(0x00200000)
#define ACM_FORMATENUMF_HARDWARE __MSABI_LONG(0x00400000)
#define ACM_FORMATENUMF_INPUT __MSABI_LONG(0x00800000)
#define ACM_FORMATENUMF_OUTPUT __MSABI_LONG(0x01000000)

  MMRESULT ACMAPI acmFormatSuggest
    (HACMDRIVER had,LPWAVEFORMATEX pwfxSrc,LPWAVEFORMATEX pwfxDst,DWORD cbwfxDst,DWORD fdwSuggest);

#define ACM_FORMATSUGGESTF_WFORMATTAG __MSABI_LONG(0x00010000)
#define ACM_FORMATSUGGESTF_NCHANNELS __MSABI_LONG(0x00020000)
#define ACM_FORMATSUGGESTF_NSAMPLESPERSEC __MSABI_LONG(0x00040000)
#define ACM_FORMATSUGGESTF_WBITSPERSAMPLE __MSABI_LONG(0x00080000)

#define ACM_FORMATSUGGESTF_TYPEMASK __MSABI_LONG(0x00FF0000)

#define ACMHELPMSGSTRINGA "acmchoose_help"
#define ACMHELPMSGSTRINGW L"acmchoose_help"
#define ACMHELPMSGCONTEXTMENUA "acmchoose_contextmenu"
#define ACMHELPMSGCONTEXTMENUW L"acmchoose_contextmenu"
#define ACMHELPMSGCONTEXTHELPA "acmchoose_contexthelp"
#define ACMHELPMSGCONTEXTHELPW L"acmchoose_contexthelp"
#ifdef _UNICODE
#define ACMHELPMSGSTRING ACMHELPMSGSTRINGW
#define ACMHELPMSGCONTEXTMENU ACMHELPMSGCONTEXTMENUW
#define ACMHELPMSGCONTEXTHELP ACMHELPMSGCONTEXTHELPW
#else
#define ACMHELPMSGSTRING ACMHELPMSGSTRINGA
#define ACMHELPMSGCONTEXTMENU ACMHELPMSGCONTEXTMENUA
#define ACMHELPMSGCONTEXTHELP ACMHELPMSGCONTEXTHELPA
#endif

#define MM_ACM_FORMATCHOOSE (0x8000)

#define FORMATCHOOSE_MESSAGE 0
#define FORMATCHOOSE_FORMATTAG_VERIFY (FORMATCHOOSE_MESSAGE+0)
#define FORMATCHOOSE_FORMAT_VERIFY (FORMATCHOOSE_MESSAGE+1)
#define FORMATCHOOSE_CUSTOM_VERIFY (FORMATCHOOSE_MESSAGE+2)

  typedef UINT (CALLBACK *ACMFORMATCHOOSEHOOKPROCA)(HWND hwnd,UINT uMsg,WPARAM wParam,LPARAM lParam);
  typedef UINT (CALLBACK *ACMFORMATCHOOSEHOOKPROCW)(HWND hwnd,UINT uMsg,WPARAM wParam,LPARAM lParam);
#ifdef _UNICODE
#define ACMFORMATCHOOSEHOOKPROC ACMFORMATCHOOSEHOOKPROCW
#else
#define ACMFORMATCHOOSEHOOKPROC ACMFORMATCHOOSEHOOKPROCA
#endif

  typedef struct tACMFORMATCHOOSEA {
    DWORD cbStruct;
    DWORD fdwStyle;
    HWND hwndOwner;
    LPWAVEFORMATEX pwfx;
    DWORD cbwfx;
    LPCSTR pszTitle;
    char szFormatTag[ACMFORMATTAGDETAILS_FORMATTAG_CHARS];
    char szFormat[ACMFORMATDETAILS_FORMAT_CHARS];
    LPSTR pszName;
    DWORD cchName;
    DWORD fdwEnum;
    LPWAVEFORMATEX pwfxEnum;
    HINSTANCE hInstance;
    LPCSTR pszTemplateName;
    LPARAM lCustData;
    ACMFORMATCHOOSEHOOKPROCA pfnHook;
  } ACMFORMATCHOOSEA,*PACMFORMATCHOOSEA,*LPACMFORMATCHOOSEA;

  typedef struct tACMFORMATCHOOSEW {
    DWORD cbStruct;
    DWORD fdwStyle;
    HWND hwndOwner;
    LPWAVEFORMATEX pwfx;
    DWORD cbwfx;
    LPCWSTR pszTitle;
    WCHAR szFormatTag[ACMFORMATTAGDETAILS_FORMATTAG_CHARS];
    WCHAR szFormat[ACMFORMATDETAILS_FORMAT_CHARS];
    LPWSTR pszName;
    DWORD cchName;
    DWORD fdwEnum;
    LPWAVEFORMATEX pwfxEnum;
    HINSTANCE hInstance;
    LPCWSTR pszTemplateName;
    LPARAM lCustData;
    ACMFORMATCHOOSEHOOKPROCW pfnHook;
  } ACMFORMATCHOOSEW,*PACMFORMATCHOOSEW,*LPACMFORMATCHOOSEW;
#ifdef _UNICODE
#define ACMFORMATCHOOSE ACMFORMATCHOOSEW
#define PACMFORMATCHOOSE PACMFORMATCHOOSEW
#define LPACMFORMATCHOOSE LPACMFORMATCHOOSEW
#else
#define ACMFORMATCHOOSE ACMFORMATCHOOSEA
#define PACMFORMATCHOOSE PACMFORMATCHOOSEA
#define LPACMFORMATCHOOSE LPACMFORMATCHOOSEA
#endif

#define ACMFORMATCHOOSE_STYLEF_SHOWHELP __MSABI_LONG(0x00000004)
#define ACMFORMATCHOOSE_STYLEF_ENABLEHOOK __MSABI_LONG(0x00000008)
#define ACMFORMATCHOOSE_STYLEF_ENABLETEMPLATE __MSABI_LONG(0x00000010)
#define ACMFORMATCHOOSE_STYLEF_ENABLETEMPLATEHANDLE __MSABI_LONG(0x00000020)
#define ACMFORMATCHOOSE_STYLEF_INITTOWFXSTRUCT __MSABI_LONG(0x00000040)
#define ACMFORMATCHOOSE_STYLEF_CONTEXTHELP __MSABI_LONG(0x00000080)

  MMRESULT ACMAPI acmFormatChooseA(LPACMFORMATCHOOSEA pafmtc);
  MMRESULT ACMAPI acmFormatChooseW(LPACMFORMATCHOOSEW pafmtc);
#ifdef _UNICODE
#define acmFormatChoose acmFormatChooseW
#else
#define acmFormatChoose acmFormatChooseA
#endif

#define ACMFILTERTAGDETAILS_FILTERTAG_CHARS 48

  typedef struct tACMFILTERTAGDETAILSA {
    DWORD cbStruct;
    DWORD dwFilterTagIndex;
    DWORD dwFilterTag;
    DWORD cbFilterSize;
    DWORD fdwSupport;
    DWORD cStandardFilters;
    char szFilterTag[ACMFILTERTAGDETAILS_FILTERTAG_CHARS];
  } ACMFILTERTAGDETAILSA,*PACMFILTERTAGDETAILSA,*LPACMFILTERTAGDETAILSA;

  typedef struct tACMFILTERTAGDETAILSW {
    DWORD cbStruct;
    DWORD dwFilterTagIndex;
    DWORD dwFilterTag;
    DWORD cbFilterSize;
    DWORD fdwSupport;
    DWORD cStandardFilters;
    WCHAR szFilterTag[ACMFILTERTAGDETAILS_FILTERTAG_CHARS];
  } ACMFILTERTAGDETAILSW,*PACMFILTERTAGDETAILSW,*LPACMFILTERTAGDETAILSW;
#ifdef _UNICODE
#define ACMFILTERTAGDETAILS ACMFILTERTAGDETAILSW
#define PACMFILTERTAGDETAILS PACMFILTERTAGDETAILSW
#define LPACMFILTERTAGDETAILS LPACMFILTERTAGDETAILSW
#else
#define ACMFILTERTAGDETAILS ACMFILTERTAGDETAILSA
#define PACMFILTERTAGDETAILS PACMFILTERTAGDETAILSA
#define LPACMFILTERTAGDETAILS LPACMFILTERTAGDETAILSA
#endif

  MMRESULT ACMAPI acmFilterTagDetailsA(HACMDRIVER had,LPACMFILTERTAGDETAILSA paftd,DWORD fdwDetails);
  MMRESULT ACMAPI acmFilterTagDetailsW(HACMDRIVER had,LPACMFILTERTAGDETAILSW paftd,DWORD fdwDetails);
#ifdef _UNICODE
#define acmFilterTagDetails acmFilterTagDetailsW
#else
#define acmFilterTagDetails acmFilterTagDetailsA
#endif

#define ACM_FILTERTAGDETAILSF_INDEX __MSABI_LONG(0x00000000)
#define ACM_FILTERTAGDETAILSF_FILTERTAG __MSABI_LONG(0x00000001)
#define ACM_FILTERTAGDETAILSF_LARGESTSIZE __MSABI_LONG(0x00000002)
#define ACM_FILTERTAGDETAILSF_QUERYMASK __MSABI_LONG(0x0000000F)

  typedef WINBOOL (CALLBACK *ACMFILTERTAGENUMCBA)(HACMDRIVERID hadid,LPACMFILTERTAGDETAILSA paftd,DWORD_PTR dwInstance,DWORD fdwSupport);
  typedef WINBOOL (CALLBACK *ACMFILTERTAGENUMCBW)(HACMDRIVERID hadid,LPACMFILTERTAGDETAILSW paftd,DWORD_PTR dwInstance,DWORD fdwSupport);

  MMRESULT ACMAPI acmFilterTagEnumA(HACMDRIVER had,LPACMFILTERTAGDETAILSA paftd,ACMFILTERTAGENUMCBA fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);
  MMRESULT ACMAPI acmFilterTagEnumW(HACMDRIVER had,LPACMFILTERTAGDETAILSW paftd,ACMFILTERTAGENUMCBW fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);
#ifdef _UNICODE
#define ACMFILTERTAGENUMCB ACMFILTERTAGENUMCBW
#define acmFilterTagEnum acmFilterTagEnumW
#else
#define ACMFILTERTAGENUMCB ACMFILTERTAGENUMCBA
#define acmFilterTagEnum acmFilterTagEnumA
#endif

#define ACMFILTERDETAILS_FILTER_CHARS 128

  typedef struct tACMFILTERDETAILSA {
    DWORD cbStruct;
    DWORD dwFilterIndex;
    DWORD dwFilterTag;
    DWORD fdwSupport;
    LPWAVEFILTER pwfltr;
    DWORD cbwfltr;
    char szFilter[ACMFILTERDETAILS_FILTER_CHARS];
  } ACMFILTERDETAILSA,*PACMFILTERDETAILSA,*LPACMFILTERDETAILSA;

  typedef struct tACMFILTERDETAILSW {
    DWORD cbStruct;
    DWORD dwFilterIndex;
    DWORD dwFilterTag;
    DWORD fdwSupport;
    LPWAVEFILTER pwfltr;
    DWORD cbwfltr;
    WCHAR szFilter[ACMFILTERDETAILS_FILTER_CHARS];
  } ACMFILTERDETAILSW,*PACMFILTERDETAILSW,*LPACMFILTERDETAILSW;
#ifdef _UNICODE
#define ACMFILTERDETAILS ACMFILTERDETAILSW
#define PACMFILTERDETAILS PACMFILTERDETAILSW
#define LPACMFILTERDETAILS LPACMFILTERDETAILSW
#else
#define ACMFILTERDETAILS ACMFILTERDETAILSA
#define PACMFILTERDETAILS PACMFILTERDETAILSA
#define LPACMFILTERDETAILS LPACMFILTERDETAILSA
#endif

  MMRESULT ACMAPI acmFilterDetailsA(HACMDRIVER had,LPACMFILTERDETAILSA pafd,DWORD fdwDetails);
  MMRESULT ACMAPI acmFilterDetailsW(HACMDRIVER had,LPACMFILTERDETAILSW pafd,DWORD fdwDetails);
#ifdef _UNICODE
#define acmFilterDetails acmFilterDetailsW
#else
#define acmFilterDetails acmFilterDetailsA
#endif

#define ACM_FILTERDETAILSF_INDEX __MSABI_LONG(0x00000000)
#define ACM_FILTERDETAILSF_FILTER __MSABI_LONG(0x00000001)
#define ACM_FILTERDETAILSF_QUERYMASK __MSABI_LONG(0x0000000F)

  typedef WINBOOL (CALLBACK *ACMFILTERENUMCBA)(HACMDRIVERID hadid,LPACMFILTERDETAILSA pafd,DWORD_PTR dwInstance,DWORD fdwSupport);
  typedef WINBOOL (CALLBACK *ACMFILTERENUMCBW)(HACMDRIVERID hadid,LPACMFILTERDETAILSW pafd,DWORD_PTR dwInstance,DWORD fdwSupport);

  MMRESULT ACMAPI acmFilterEnumA(HACMDRIVER had,LPACMFILTERDETAILSA pafd,ACMFILTERENUMCBA fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);
  MMRESULT ACMAPI acmFilterEnumW(HACMDRIVER had,LPACMFILTERDETAILSW pafd,ACMFILTERENUMCBW fnCallback,DWORD_PTR dwInstance,DWORD fdwEnum);
#ifdef _UNICODE
#define ACMFILTERENUMCB ACMFILTERENUMCBW
#define acmFilterEnum acmFilterEnumW
#else
#define ACMFILTERENUMCB ACMFILTERENUMCBA
#define acmFilterEnum acmFilterEnumA
#endif

#define ACM_FILTERENUMF_DWFILTERTAG __MSABI_LONG(0x00010000)

#define MM_ACM_FILTERCHOOSE (0x8000)

#define FILTERCHOOSE_MESSAGE 0
#define FILTERCHOOSE_FILTERTAG_VERIFY (FILTERCHOOSE_MESSAGE+0)
#define FILTERCHOOSE_FILTER_VERIFY (FILTERCHOOSE_MESSAGE+1)
#define FILTERCHOOSE_CUSTOM_VERIFY (FILTERCHOOSE_MESSAGE+2)

  typedef UINT (CALLBACK *ACMFILTERCHOOSEHOOKPROCA)(HWND hwnd,UINT uMsg,WPARAM wParam,LPARAM lParam);
  typedef UINT (CALLBACK *ACMFILTERCHOOSEHOOKPROCW)(HWND hwnd,UINT uMsg,WPARAM wParam,LPARAM lParam);
#ifdef _UNICODE
#define ACMFILTERCHOOSEHOOKPROC ACMFILTERCHOOSEHOOKPROCW
#else
#define ACMFILTERCHOOSEHOOKPROC ACMFILTERCHOOSEHOOKPROCA
#endif

  typedef struct tACMFILTERCHOOSEA {
    DWORD cbStruct;
    DWORD fdwStyle;
    HWND hwndOwner;
    LPWAVEFILTER pwfltr;
    DWORD cbwfltr;
    LPCSTR pszTitle;
    char szFilterTag[ACMFILTERTAGDETAILS_FILTERTAG_CHARS];
    char szFilter[ACMFILTERDETAILS_FILTER_CHARS];
    LPSTR pszName;
    DWORD cchName;
    DWORD fdwEnum;
    LPWAVEFILTER pwfltrEnum;
    HINSTANCE hInstance;
    LPCSTR pszTemplateName;
    LPARAM lCustData;
    ACMFILTERCHOOSEHOOKPROCA pfnHook;
  } ACMFILTERCHOOSEA,*PACMFILTERCHOOSEA,*LPACMFILTERCHOOSEA;

  typedef struct tACMFILTERCHOOSEW {
    DWORD cbStruct;
    DWORD fdwStyle;
    HWND hwndOwner;
    LPWAVEFILTER pwfltr;
    DWORD cbwfltr;
    LPCWSTR pszTitle;
    WCHAR szFilterTag[ACMFILTERTAGDETAILS_FILTERTAG_CHARS];
    WCHAR szFilter[ACMFILTERDETAILS_FILTER_CHARS];
    LPWSTR pszName;
    DWORD cchName;
    DWORD fdwEnum;
    LPWAVEFILTER pwfltrEnum;
    HINSTANCE hInstance;
    LPCWSTR pszTemplateName;
    LPARAM lCustData;
    ACMFILTERCHOOSEHOOKPROCW pfnHook;
  } ACMFILTERCHOOSEW,*PACMFILTERCHOOSEW,*LPACMFILTERCHOOSEW;
#ifdef _UNICODE
#define ACMFILTERCHOOSE ACMFILTERCHOOSEW
#define PACMFILTERCHOOSE PACMFILTERCHOOSEW
#define LPACMFILTERCHOOSE LPACMFILTERCHOOSEW
#else
#define ACMFILTERCHOOSE ACMFILTERCHOOSEA
#define PACMFILTERCHOOSE PACMFILTERCHOOSEA
#define LPACMFILTERCHOOSE LPACMFILTERCHOOSEA
#endif

#define ACMFILTERCHOOSE_STYLEF_SHOWHELP __MSABI_LONG(0x00000004)
#define ACMFILTERCHOOSE_STYLEF_ENABLEHOOK __MSABI_LONG(0x00000008)
#define ACMFILTERCHOOSE_STYLEF_ENABLETEMPLATE __MSABI_LONG(0x00000010)
#define ACMFILTERCHOOSE_STYLEF_ENABLETEMPLATEHANDLE __MSABI_LONG(0x00000020)
#define ACMFILTERCHOOSE_STYLEF_INITTOFILTERSTRUCT __MSABI_LONG(0x00000040)
#define ACMFILTERCHOOSE_STYLEF_CONTEXTHELP __MSABI_LONG(0x00000080)

  MMRESULT ACMAPI acmFilterChooseA(LPACMFILTERCHOOSEA pafltrc);
  MMRESULT ACMAPI acmFilterChooseW(LPACMFILTERCHOOSEW pafltrc);
#ifdef _UNICODE
#define acmFilterChoose acmFilterChooseW
#else
#define acmFilterChoose acmFilterChooseA
#endif

#ifdef _WIN64
#define _DRVRESERVED 15
#else
#define _DRVRESERVED 10
#endif

  typedef struct tACMSTREAMHEADER {
    DWORD cbStruct;
    DWORD fdwStatus;
    DWORD_PTR dwUser;
    LPBYTE pbSrc;
    DWORD cbSrcLength;
    DWORD cbSrcLengthUsed;
    DWORD_PTR dwSrcUser;
    LPBYTE pbDst;
    DWORD cbDstLength;
    DWORD cbDstLengthUsed;
    DWORD_PTR dwDstUser;
    DWORD dwReservedDriver[_DRVRESERVED];
  } ACMSTREAMHEADER,*PACMSTREAMHEADER,*LPACMSTREAMHEADER;

#define ACMSTREAMHEADER_STATUSF_DONE __MSABI_LONG(0x00010000)
#define ACMSTREAMHEADER_STATUSF_PREPARED __MSABI_LONG(0x00020000)
#define ACMSTREAMHEADER_STATUSF_INQUEUE __MSABI_LONG(0x00100000)

  MMRESULT ACMAPI acmStreamOpen(LPHACMSTREAM phas,HACMDRIVER had,LPWAVEFORMATEX pwfxSrc,LPWAVEFORMATEX pwfxDst,LPWAVEFILTER pwfltr,DWORD_PTR dwCallback,DWORD_PTR dwInstance,DWORD fdwOpen);

#define ACM_STREAMOPENF_QUERY 0x00000001
#define ACM_STREAMOPENF_ASYNC 0x00000002
#define ACM_STREAMOPENF_NONREALTIME 0x00000004

  MMRESULT ACMAPI acmStreamClose
    (HACMSTREAM has,DWORD fdwClose);

  MMRESULT ACMAPI acmStreamSize(HACMSTREAM has,DWORD cbInput,LPDWORD pdwOutputBytes,DWORD fdwSize);

#define ACM_STREAMSIZEF_SOURCE __MSABI_LONG(0x00000000)
#define ACM_STREAMSIZEF_DESTINATION __MSABI_LONG(0x00000001)
#define ACM_STREAMSIZEF_QUERYMASK __MSABI_LONG(0x0000000F)

  MMRESULT ACMAPI acmStreamReset(HACMSTREAM has,DWORD fdwReset);
  MMRESULT ACMAPI acmStreamMessage(HACMSTREAM has,UINT uMsg,LPARAM lParam1,LPARAM lParam2);
  MMRESULT ACMAPI acmStreamConvert(HACMSTREAM has,LPACMSTREAMHEADER pash,DWORD fdwConvert);

#define ACM_STREAMCONVERTF_BLOCKALIGN 0x00000004
#define ACM_STREAMCONVERTF_START 0x00000010
#define ACM_STREAMCONVERTF_END 0x00000020

  MMRESULT ACMAPI acmStreamPrepareHeader(HACMSTREAM has,LPACMSTREAMHEADER pash,DWORD fdwPrepare);
  MMRESULT ACMAPI acmStreamUnprepareHeader(HACMSTREAM has,LPACMSTREAMHEADER pash,DWORD fdwUnprepare);

#include "poppack.h"

#ifdef __cplusplus
}
#endif
#endif
