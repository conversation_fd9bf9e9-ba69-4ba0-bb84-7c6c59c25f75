/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include "rpc.h"
#include "rpcndr.h"

#ifndef __RPCNDR_H_VERSION__
#error This stub requires an updated version of <rpcndr.h>
#endif

#ifndef __mqoai_h__
#define __mqoai_h__

#ifndef __IMSMQQuery_FWD_DEFINED__
#define __IMSMQQuery_FWD_DEFINED__
typedef struct IMSMQQuery IMSMQQuery;
#endif

#ifndef __IMSMQQueueInfo_FWD_DEFINED__
#define __IMSMQQueueInfo_FWD_DEFINED__
typedef struct IMSMQQueueInfo IMSMQQueueInfo;
#endif

#ifndef __IMSMQQueueInfo2_FWD_DEFINED__
#define __IMSMQQueueInfo2_FWD_DEFINED__
typedef struct IMSMQQueueInfo2 IMSMQQueueInfo2;
#endif

#ifndef __IMSMQQueueInfo3_FWD_DEFINED__
#define __IMSMQQueueInfo3_FWD_DEFINED__
typedef struct IMSMQQueueInfo3 IMSMQQueueInfo3;
#endif

#ifndef __IMSMQQueue_FWD_DEFINED__
#define __IMSMQQueue_FWD_DEFINED__
typedef struct IMSMQQueue IMSMQQueue;
#endif

#ifndef __IMSMQQueue2_FWD_DEFINED__
#define __IMSMQQueue2_FWD_DEFINED__
typedef struct IMSMQQueue2 IMSMQQueue2;
#endif

#ifndef __IMSMQMessage_FWD_DEFINED__
#define __IMSMQMessage_FWD_DEFINED__
typedef struct IMSMQMessage IMSMQMessage;
#endif

#ifndef __IMSMQQueueInfos_FWD_DEFINED__
#define __IMSMQQueueInfos_FWD_DEFINED__
typedef struct IMSMQQueueInfos IMSMQQueueInfos;
#endif

#ifndef __IMSMQQueueInfos2_FWD_DEFINED__
#define __IMSMQQueueInfos2_FWD_DEFINED__
typedef struct IMSMQQueueInfos2 IMSMQQueueInfos2;
#endif

#ifndef __IMSMQQueueInfos3_FWD_DEFINED__
#define __IMSMQQueueInfos3_FWD_DEFINED__
typedef struct IMSMQQueueInfos3 IMSMQQueueInfos3;
#endif

#ifndef __IMSMQEvent_FWD_DEFINED__
#define __IMSMQEvent_FWD_DEFINED__
typedef struct IMSMQEvent IMSMQEvent;
#endif

#ifndef __IMSMQEvent2_FWD_DEFINED__
#define __IMSMQEvent2_FWD_DEFINED__
typedef struct IMSMQEvent2 IMSMQEvent2;
#endif

#ifndef __IMSMQEvent3_FWD_DEFINED__
#define __IMSMQEvent3_FWD_DEFINED__
typedef struct IMSMQEvent3 IMSMQEvent3;
#endif

#ifndef __IMSMQTransaction_FWD_DEFINED__
#define __IMSMQTransaction_FWD_DEFINED__
typedef struct IMSMQTransaction IMSMQTransaction;
#endif

#ifndef __IMSMQCoordinatedTransactionDispenser_FWD_DEFINED__
#define __IMSMQCoordinatedTransactionDispenser_FWD_DEFINED__
typedef struct IMSMQCoordinatedTransactionDispenser IMSMQCoordinatedTransactionDispenser;
#endif

#ifndef __IMSMQTransactionDispenser_FWD_DEFINED__
#define __IMSMQTransactionDispenser_FWD_DEFINED__
typedef struct IMSMQTransactionDispenser IMSMQTransactionDispenser;
#endif

#ifndef __IMSMQQuery2_FWD_DEFINED__
#define __IMSMQQuery2_FWD_DEFINED__
typedef struct IMSMQQuery2 IMSMQQuery2;
#endif

#ifndef __IMSMQQuery3_FWD_DEFINED__
#define __IMSMQQuery3_FWD_DEFINED__
typedef struct IMSMQQuery3 IMSMQQuery3;
#endif

#ifndef __MSMQQuery_FWD_DEFINED__
#define __MSMQQuery_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQQuery MSMQQuery;
#else
typedef struct MSMQQuery MSMQQuery;
#endif
#endif

#ifndef __IMSMQMessage2_FWD_DEFINED__
#define __IMSMQMessage2_FWD_DEFINED__
typedef struct IMSMQMessage2 IMSMQMessage2;
#endif

#ifndef __IMSMQMessage3_FWD_DEFINED__
#define __IMSMQMessage3_FWD_DEFINED__
typedef struct IMSMQMessage3 IMSMQMessage3;
#endif

#ifndef __MSMQMessage_FWD_DEFINED__
#define __MSMQMessage_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQMessage MSMQMessage;
#else
typedef struct MSMQMessage MSMQMessage;
#endif
#endif

#ifndef __IMSMQQueue3_FWD_DEFINED__
#define __IMSMQQueue3_FWD_DEFINED__
typedef struct IMSMQQueue3 IMSMQQueue3;
#endif

#ifndef __MSMQQueue_FWD_DEFINED__
#define __MSMQQueue_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQQueue MSMQQueue;
#else
typedef struct MSMQQueue MSMQQueue;
#endif
#endif

#ifndef __IMSMQPrivateEvent_FWD_DEFINED__
#define __IMSMQPrivateEvent_FWD_DEFINED__
typedef struct IMSMQPrivateEvent IMSMQPrivateEvent;
#endif

#ifndef ___DMSMQEventEvents_FWD_DEFINED__
#define ___DMSMQEventEvents_FWD_DEFINED__
typedef struct _DMSMQEventEvents _DMSMQEventEvents;
#endif

#ifndef __MSMQEvent_FWD_DEFINED__
#define __MSMQEvent_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQEvent MSMQEvent;
#else
typedef struct MSMQEvent MSMQEvent;
#endif
#endif

#ifndef __MSMQQueueInfo_FWD_DEFINED__
#define __MSMQQueueInfo_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQQueueInfo MSMQQueueInfo;
#else
typedef struct MSMQQueueInfo MSMQQueueInfo;
#endif
#endif

#ifndef __MSMQQueueInfos_FWD_DEFINED__
#define __MSMQQueueInfos_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQQueueInfos MSMQQueueInfos;
#else
typedef struct MSMQQueueInfos MSMQQueueInfos;
#endif
#endif

#ifndef __IMSMQTransaction2_FWD_DEFINED__
#define __IMSMQTransaction2_FWD_DEFINED__
typedef struct IMSMQTransaction2 IMSMQTransaction2;
#endif

#ifndef __IMSMQTransaction3_FWD_DEFINED__
#define __IMSMQTransaction3_FWD_DEFINED__
typedef struct IMSMQTransaction3 IMSMQTransaction3;
#endif

#ifndef __MSMQTransaction_FWD_DEFINED__
#define __MSMQTransaction_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQTransaction MSMQTransaction;
#else
typedef struct MSMQTransaction MSMQTransaction;
#endif
#endif

#ifndef __IMSMQCoordinatedTransactionDispenser2_FWD_DEFINED__
#define __IMSMQCoordinatedTransactionDispenser2_FWD_DEFINED__
typedef struct IMSMQCoordinatedTransactionDispenser2 IMSMQCoordinatedTransactionDispenser2;
#endif

#ifndef __IMSMQCoordinatedTransactionDispenser3_FWD_DEFINED__
#define __IMSMQCoordinatedTransactionDispenser3_FWD_DEFINED__
typedef struct IMSMQCoordinatedTransactionDispenser3 IMSMQCoordinatedTransactionDispenser3;
#endif

#ifndef __MSMQCoordinatedTransactionDispenser_FWD_DEFINED__
#define __MSMQCoordinatedTransactionDispenser_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQCoordinatedTransactionDispenser MSMQCoordinatedTransactionDispenser;
#else
typedef struct MSMQCoordinatedTransactionDispenser MSMQCoordinatedTransactionDispenser;
#endif
#endif

#ifndef __IMSMQTransactionDispenser2_FWD_DEFINED__
#define __IMSMQTransactionDispenser2_FWD_DEFINED__
typedef struct IMSMQTransactionDispenser2 IMSMQTransactionDispenser2;
#endif

#ifndef __IMSMQTransactionDispenser3_FWD_DEFINED__
#define __IMSMQTransactionDispenser3_FWD_DEFINED__
typedef struct IMSMQTransactionDispenser3 IMSMQTransactionDispenser3;
#endif

#ifndef __MSMQTransactionDispenser_FWD_DEFINED__
#define __MSMQTransactionDispenser_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQTransactionDispenser MSMQTransactionDispenser;
#else
typedef struct MSMQTransactionDispenser MSMQTransactionDispenser;
#endif
#endif

#ifndef __IMSMQApplication_FWD_DEFINED__
#define __IMSMQApplication_FWD_DEFINED__
typedef struct IMSMQApplication IMSMQApplication;
#endif

#ifndef __IMSMQApplication2_FWD_DEFINED__
#define __IMSMQApplication2_FWD_DEFINED__
typedef struct IMSMQApplication2 IMSMQApplication2;
#endif

#ifndef __IMSMQApplication3_FWD_DEFINED__
#define __IMSMQApplication3_FWD_DEFINED__
typedef struct IMSMQApplication3 IMSMQApplication3;
#endif

#ifndef __MSMQApplication_FWD_DEFINED__
#define __MSMQApplication_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQApplication MSMQApplication;
#else
typedef struct MSMQApplication MSMQApplication;
#endif
#endif

#ifndef __IMSMQDestination_FWD_DEFINED__
#define __IMSMQDestination_FWD_DEFINED__
typedef struct IMSMQDestination IMSMQDestination;
#endif

#ifndef __IMSMQPrivateDestination_FWD_DEFINED__
#define __IMSMQPrivateDestination_FWD_DEFINED__
typedef struct IMSMQPrivateDestination IMSMQPrivateDestination;
#endif

#ifndef __MSMQDestination_FWD_DEFINED__
#define __MSMQDestination_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQDestination MSMQDestination;
#else
typedef struct MSMQDestination MSMQDestination;
#endif
#endif

#ifndef __IMSMQCollection_FWD_DEFINED__
#define __IMSMQCollection_FWD_DEFINED__
typedef struct IMSMQCollection IMSMQCollection;
#endif

#ifndef __MSMQCollection_FWD_DEFINED__
#define __MSMQCollection_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQCollection MSMQCollection;
#else
typedef struct MSMQCollection MSMQCollection;
#endif
#endif

#ifndef __IMSMQManagement_FWD_DEFINED__
#define __IMSMQManagement_FWD_DEFINED__
typedef struct IMSMQManagement IMSMQManagement;
#endif

#ifndef __MSMQManagement_FWD_DEFINED__
#define __MSMQManagement_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQManagement MSMQManagement;
#else
typedef struct MSMQManagement MSMQManagement;
#endif
#endif

#ifndef __IMSMQOutgoingQueueManagement_FWD_DEFINED__
#define __IMSMQOutgoingQueueManagement_FWD_DEFINED__
typedef struct IMSMQOutgoingQueueManagement IMSMQOutgoingQueueManagement;
#endif

#ifndef __MSMQOutgoingQueueManagement_FWD_DEFINED__
#define __MSMQOutgoingQueueManagement_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQOutgoingQueueManagement MSMQOutgoingQueueManagement;
#else
typedef struct MSMQOutgoingQueueManagement MSMQOutgoingQueueManagement;
#endif
#endif

#ifndef __IMSMQQueueManagement_FWD_DEFINED__
#define __IMSMQQueueManagement_FWD_DEFINED__
typedef struct IMSMQQueueManagement IMSMQQueueManagement;
#endif

#ifndef __MSMQQueueManagement_FWD_DEFINED__
#define __MSMQQueueManagement_FWD_DEFINED__
#ifdef __cplusplus
typedef class MSMQQueueManagement MSMQQueueManagement;
#else
typedef struct MSMQQueueManagement MSMQQueueManagement;
#endif
#endif

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __MIDL_user_allocate_free_DEFINED__
#define __MIDL_user_allocate_free_DEFINED__
  void *__RPC_API MIDL_user_allocate(size_t);
  void __RPC_API MIDL_user_free(void *);
#endif

#ifndef __MSMQ_LIBRARY_DEFINED__
#define __MSMQ_LIBRARY_DEFINED__

#ifndef BASETYPES
#define BASETYPES
  typedef unsigned __LONG32 ULONG;
  typedef ULONG *PULONG;
  typedef unsigned short USHORT;
  typedef USHORT *PUSHORT;
  typedef unsigned char UCHAR;
  typedef UCHAR *PUCHAR;
  typedef char *PSZ;
#endif

  typedef short Boolean;
  typedef unsigned char BYTE;
  typedef unsigned __LONG32 DWORD;
  typedef int WINBOOL;

  enum MQCALG {
    MQMSG_CALG_MD2 = 0x8001,MQMSG_CALG_MD4 = 0x8002,MQMSG_CALG_MD5 = 0x8003,MQMSG_CALG_SHA = 0x8004,
    MQMSG_CALG_SHA1 = 0x8004,MQMSG_CALG_MAC = 0x8005,MQMSG_CALG_RSA_SIGN = 0x2400,MQMSG_CALG_DSS_SIGN = 0x2200,
    MQMSG_CALG_RSA_KEYX = 0xa400,MQMSG_CALG_DES = 0x6601,MQMSG_CALG_RC2 = 0x6602,MQMSG_CALG_RC4 = 0x6801,
    MQMSG_CALG_SEAL = 0x6802
  };
  enum MQTRANSACTION {
    MQ_NO_TRANSACTION = 0,MQ_MTS_TRANSACTION = 1,MQ_XA_TRANSACTION = 2,MQ_SINGLE_MESSAGE = 3
  };

  enum RELOPS {
    REL_NOP = 0,REL_EQ,REL_NEQ,REL_LT,REL_GT,REL_LE,REL_GE
  };

  enum MQCERT_REGISTER {
    MQCERT_REGISTER_ALWAYS = 1,MQCERT_REGISTER_IF_NOT_EXIST = 2
  };

  enum MQMSGCURSOR {
    MQMSG_FIRST = 0,MQMSG_CURRENT = 1,MQMSG_NEXT = 2
  };

  enum MQMSGCLASS {
    MQMSG_CLASS_NORMAL = 0,MQMSG_CLASS_REPORT = 0x1,MQMSG_CLASS_ACK_REACH_QUEUE = 0x2,MQMSG_CLASS_ACK_RECEIVE = 0x4000,
    MQMSG_CLASS_NACK_BAD_DST_Q = 0x8000,MQMSG_CLASS_NACK_PURGED = 0x8000 + 0x1,MQMSG_CLASS_NACK_REACH_QUEUE_TIMEOUT = 0x8000 + 0x2,
    MQMSG_CLASS_NACK_Q_EXCEED_QUOTA = 0x8000 + 0x3,MQMSG_CLASS_NACK_ACCESS_DENIED = 0x8000 + 0x4,MQMSG_CLASS_NACK_HOP_COUNT_EXCEEDED = 0x8000 + 0x5,
    MQMSG_CLASS_NACK_BAD_SIGNATURE = 0x8000 + 0x6,MQMSG_CLASS_NACK_BAD_ENCRYPTION = 0x8000 + 0x7,MQMSG_CLASS_NACK_COULD_NOT_ENCRYPT = 0x8000 + 0x8,
    MQMSG_CLASS_NACK_NOT_TRANSACTIONAL_Q = 0x8000 + 0x9,MQMSG_CLASS_NACK_NOT_TRANSACTIONAL_MSG = 0x8000 + 0xa,
    MQMSG_CLASS_NACK_UNSUPPORTED_CRYPTO_PROVIDER = 0x8000 + 0xb,MQMSG_CLASS_NACK_SOURCE_COMPUTER_GUID_CHANGED = 0x8000 + 0xc,
    MQMSG_CLASS_NACK_Q_DELETED = 0x8000 + 0x4000,MQMSG_CLASS_NACK_Q_PURGED = 0x8000 + 0x4000 + 0x1,
    MQMSG_CLASS_NACK_RECEIVE_TIMEOUT = 0x8000 + 0x4000 + 0x2,MQMSG_CLASS_NACK_RECEIVE_TIMEOUT_AT_SENDER = 0x8000 + 0x4000 + 0x3
  };

  enum MQMSGDELIVERY {
    MQMSG_DELIVERY_EXPRESS = 0,MQMSG_DELIVERY_RECOVERABLE = 1
  };

  enum MQMSGACKNOWLEDGEMENT {
    MQMSG_ACKNOWLEDGMENT_NONE = 0,MQMSG_ACKNOWLEDGMENT_POS_ARRIVAL = 0x1,MQMSG_ACKNOWLEDGMENT_POS_RECEIVE = 0x2,MQMSG_ACKNOWLEDGMENT_NEG_ARRIVAL = 0x4,
    MQMSG_ACKNOWLEDGMENT_NEG_RECEIVE = 0x8,MQMSG_ACKNOWLEDGMENT_NACK_REACH_QUEUE = 0x4,MQMSG_ACKNOWLEDGMENT_FULL_REACH_QUEUE = 0x4 + 0x1,
    MQMSG_ACKNOWLEDGMENT_NACK_RECEIVE = 0x4 + 0x8,MQMSG_ACKNOWLEDGMENT_FULL_RECEIVE = 0x4 + 0x8 + 0x2
  };

  enum MQMSGJOURNAL {
    MQMSG_JOURNAL_NONE = 0,MQMSG_DEADLETTER = 1,MQMSG_JOURNAL = 2
  };

  enum MQMSGTRACE {
    MQMSG_TRACE_NONE = 0,MQMSG_SEND_ROUTE_TO_REPORT_QUEUE = 1
  };

  enum MQMSGSENDERIDTYPE {
    MQMSG_SENDERID_TYPE_NONE = 0,MQMSG_SENDERID_TYPE_SID = 1
  };

  enum MQMSGPRIVLEVEL {
    MQMSG_PRIV_LEVEL_NONE = 0,MQMSG_PRIV_LEVEL_BODY_BASE = 1,MQMSG_PRIV_LEVEL_BODY_ENHANCED = 3
  };

  enum MQMSGAUTHLEVEL {
    MQMSG_AUTH_LEVEL_NONE = 0,MQMSG_AUTH_LEVEL_ALWAYS = 1,MQMSG_AUTH_LEVEL_MSMQ10 = 2,MQMSG_AUTH_LEVEL_SIG10 = 2,MQMSG_AUTH_LEVEL_MSMQ20 = 4,
    MQMSG_AUTH_LEVEL_SIG20 = 4,MQMSG_AUTH_LEVEL_SIG30 = 8
  };

  enum MQMSGIDSIZE {
    MQMSG_MSGID_SIZE = 20,MQMSG_CORRELATIONID_SIZE = 20,MQMSG_XACTID_SIZE = 20
  };

  enum MQMSGMAX {
    MQ_MAX_MSG_LABEL_LEN = 249
  };

  enum MQMSGAUTHENTICATION {
    MQMSG_AUTHENTICATION_NOT_REQUESTED = 0,MQMSG_AUTHENTICATION_REQUESTED = 1,MQMSG_AUTHENTICATED_SIG10 = 1,MQMSG_AUTHENTICATION_REQUESTED_EX = 3,
    MQMSG_AUTHENTICATED_SIG20 = 3,MQMSG_AUTHENTICATED_SIG30 = 5,MQMSG_AUTHENTICATED_SIGXML = 9
  };

  enum MQSHARE {
    MQ_DENY_NONE = 0,MQ_DENY_RECEIVE_SHARE = 1
  };

  enum MQACCESS {
    MQ_RECEIVE_ACCESS = 1,MQ_SEND_ACCESS = 2,MQ_PEEK_ACCESS = 0x20,MQ_ADMIN_ACCESS = 0x80
  };

  enum MQJOURNAL {
    MQ_JOURNAL_NONE = 0,MQ_JOURNAL = 1
  };

  enum MQTRANSACTIONAL {
    MQ_TRANSACTIONAL_NONE = 0,MQ_TRANSACTIONAL = 1
  };

  enum MQAUTHENTICATE {
    MQ_AUTHENTICATE_NONE = 0,MQ_AUTHENTICATE = 1
  };

  enum MQPRIVLEVEL {
    MQ_PRIV_LEVEL_NONE = 0,MQ_PRIV_LEVEL_OPTIONAL = 1,MQ_PRIV_LEVEL_BODY = 2
  };

  enum MQPRIORITY {
    MQ_MIN_PRIORITY = 0,MQ_MAX_PRIORITY = 7
  };

  enum MQMAX {
    MQ_MAX_Q_NAME_LEN = 124,MQ_MAX_Q_LABEL_LEN = 124
  };

  enum QUEUE_TYPE {
    MQ_TYPE_PUBLIC = 0,MQ_TYPE_PRIVATE,MQ_TYPE_MACHINE,MQ_TYPE_CONNECTOR,MQ_TYPE_MULTICAST
  };

  enum FOREIGN_STATUS {
    MQ_STATUS_FOREIGN = 0,MQ_STATUS_NOT_FOREIGN,MQ_STATUS_UNKNOWN
  };

  enum XACT_STATUS {
    MQ_XACT_STATUS_XACT = 0,MQ_XACT_STATUS_NOT_XACT,MQ_XACT_STATUS_UNKNOWN
  };

  enum QUEUE_STATE {
    MQ_QUEUE_STATE_LOCAL_CONNECTION = 0,MQ_QUEUE_STATE_DISCONNECTED,MQ_QUEUE_STATE_WAITING,
    MQ_QUEUE_STATE_NEEDVALIDATE,MQ_QUEUE_STATE_ONHOLD,MQ_QUEUE_STATE_NONACTIVE,
    MQ_QUEUE_STATE_CONNECTED,MQ_QUEUE_STATE_DISCONNECTING,MQ_QUEUE_STATE_LOCKED
  };

  enum MQDEFAULT {
    DEFAULT_M_PRIORITY = 3,DEFAULT_M_DELIVERY = 0,DEFAULT_M_ACKNOWLEDGE = 0,DEFAULT_M_JOURNAL = 0,DEFAULT_M_APPSPECIFIC = 0,
    DEFAULT_M_PRIV_LEVEL = 0,DEFAULT_M_AUTH_LEVEL = 0,DEFAULT_M_SENDERID_TYPE = 1,DEFAULT_Q_JOURNAL = 0,DEFAULT_Q_BASEPRIORITY = 0,
    DEFAULT_Q_QUOTA = 0xffffffff,DEFAULT_Q_JOURNAL_QUOTA = 0xffffffff,DEFAULT_Q_TRANSACTION = 0,DEFAULT_Q_AUTHENTICATE = 0,DEFAULT_Q_PRIV_LEVEL = 1,
    DEFAULT_M_LOOKUPID = 0
  };

  enum MQERROR {
    MQ_ERROR = 0xc00e0001,MQ_ERROR_PROPERTY = 0xc00e0002,MQ_ERROR_QUEUE_NOT_FOUND = 0xc00e0003,MQ_ERROR_QUEUE_NOT_ACTIVE = 0xc00e0004,
    MQ_ERROR_QUEUE_EXISTS = 0xc00e0005,MQ_ERROR_INVALID_PARAMETER = 0xc00e0006,MQ_ERROR_INVALID_HANDLE = 0xc00e0007,
    MQ_ERROR_OPERATION_CANCELLED = 0xc00e0008,MQ_ERROR_SHARING_VIOLATION = 0xc00e0009,MQ_ERROR_SERVICE_NOT_AVAILABLE = 0xc00e000b,
    MQ_ERROR_MACHINE_NOT_FOUND = 0xc00e000d,MQ_ERROR_ILLEGAL_SORT = 0xc00e0010,MQ_ERROR_ILLEGAL_USER = 0xc00e0011,MQ_ERROR_NO_DS = 0xc00e0013,
    MQ_ERROR_ILLEGAL_QUEUE_PATHNAME = 0xc00e0014,MQ_ERROR_ILLEGAL_PROPERTY_VALUE = 0xc00e0018,MQ_ERROR_ILLEGAL_PROPERTY_VT = 0xc00e0019,
    MQ_ERROR_BUFFER_OVERFLOW = 0xc00e001a,MQ_ERROR_IO_TIMEOUT = 0xc00e001b,MQ_ERROR_ILLEGAL_CURSOR_ACTION = 0xc00e001c,
    MQ_ERROR_MESSAGE_ALREADY_RECEIVED = 0xc00e001d,MQ_ERROR_ILLEGAL_FORMATNAME = 0xc00e001e,MQ_ERROR_FORMATNAME_BUFFER_TOO_SMALL = 0xc00e001f,
    MQ_ERROR_UNSUPPORTED_FORMATNAME_OPERATION = 0xc00e0020,MQ_ERROR_ILLEGAL_SECURITY_DESCRIPTOR = 0xc00e0021,
    MQ_ERROR_SENDERID_BUFFER_TOO_SMALL = 0xc00e0022,MQ_ERROR_SECURITY_DESCRIPTOR_TOO_SMALL = 0xc00e0023,
    MQ_ERROR_CANNOT_IMPERSONATE_CLIENT = 0xc00e0024,MQ_ERROR_ACCESS_DENIED = 0xc00e0025,MQ_ERROR_PRIVILEGE_NOT_HELD = 0xc00e0026,
    MQ_ERROR_INSUFFICIENT_RESOURCES = 0xc00e0027,MQ_ERROR_USER_BUFFER_TOO_SMALL = 0xc00e0028,MQ_ERROR_MESSAGE_STORAGE_FAILED = 0xc00e002a,
    MQ_ERROR_SENDER_CERT_BUFFER_TOO_SMALL = 0xc00e002b,MQ_ERROR_INVALID_CERTIFICATE = 0xc00e002c,
    MQ_ERROR_CORRUPTED_INTERNAL_CERTIFICATE = 0xc00e002d,MQ_ERROR_INTERNAL_USER_CERT_EXIST = 0xc00e002e,
    MQ_ERROR_NO_INTERNAL_USER_CERT = 0xc00e002f,MQ_ERROR_CORRUPTED_SECURITY_DATA = 0xc00e0030,MQ_ERROR_CORRUPTED_PERSONAL_CERT_STORE = 0xc00e0031,
    MQ_ERROR_COMPUTER_DOES_NOT_SUPPORT_ENCRYPTION = 0xc00e0033,MQ_ERROR_BAD_SECURITY_CONTEXT = 0xc00e0035,MQ_ERROR_COULD_NOT_GET_USER_SID = 0xc00e0036,
    MQ_ERROR_COULD_NOT_GET_ACCOUNT_INFO = 0xc00e0037,MQ_ERROR_ILLEGAL_MQCOLUMNS = 0xc00e0038,MQ_ERROR_ILLEGAL_PROPID = 0xc00e0039,
    MQ_ERROR_ILLEGAL_RELATION = 0xc00e003a,MQ_ERROR_ILLEGAL_PROPERTY_SIZE = 0xc00e003b,MQ_ERROR_ILLEGAL_RESTRICTION_PROPID = 0xc00e003c,
    MQ_ERROR_ILLEGAL_MQQUEUEPROPS = 0xc00e003d,MQ_ERROR_PROPERTY_NOTALLOWED = 0xc00e003e,MQ_ERROR_INSUFFICIENT_PROPERTIES = 0xc00e003f,
    MQ_ERROR_MACHINE_EXISTS = 0xc00e0040,MQ_ERROR_ILLEGAL_MQQMPROPS = 0xc00e0041,MQ_ERROR_DS_IS_FULL = 0xc00e0042,MQ_ERROR_DS_ERROR = 0xc00e0043,
    MQ_ERROR_INVALID_OWNER = 0xc00e0044,MQ_ERROR_UNSUPPORTED_ACCESS_MODE = 0xc00e0045,MQ_ERROR_RESULT_BUFFER_TOO_SMALL = 0xc00e0046,
    MQ_ERROR_DELETE_CN_IN_USE = 0xc00e0048,MQ_ERROR_NO_RESPONSE_FROM_OBJECT_SERVER = 0xc00e0049,MQ_ERROR_OBJECT_SERVER_NOT_AVAILABLE = 0xc00e004a,
    MQ_ERROR_QUEUE_NOT_AVAILABLE = 0xc00e004b,MQ_ERROR_DTC_CONNECT = 0xc00e004c,MQ_ERROR_TRANSACTION_IMPORT = 0xc00e004e,
    MQ_ERROR_TRANSACTION_USAGE = 0xc00e0050,MQ_ERROR_TRANSACTION_SEQUENCE = 0xc00e0051,MQ_ERROR_MISSING_CONNECTOR_TYPE = 0xc00e0055,
    MQ_ERROR_STALE_HANDLE = 0xc00e0056,MQ_ERROR_TRANSACTION_ENLIST = 0xc00e0058,MQ_ERROR_QUEUE_DELETED = 0xc00e005a,
    MQ_ERROR_ILLEGAL_CONTEXT = 0xc00e005b,MQ_ERROR_ILLEGAL_SORT_PROPID = 0xc00e005c,MQ_ERROR_LABEL_TOO_LONG = 0xc00e005d,
    MQ_ERROR_LABEL_BUFFER_TOO_SMALL = 0xc00e005e,MQ_ERROR_MQIS_SERVER_EMPTY = 0xc00e005f,MQ_ERROR_MQIS_READONLY_MODE = 0xc00e0060,
    MQ_ERROR_SYMM_KEY_BUFFER_TOO_SMALL = 0xc00e0061,MQ_ERROR_SIGNATURE_BUFFER_TOO_SMALL = 0xc00e0062,MQ_ERROR_PROV_NAME_BUFFER_TOO_SMALL = 0xc00e0063,
    MQ_ERROR_ILLEGAL_OPERATIO = 0xc00e0064,MQ_ERROR_WRITE_NOT_ALLOWED = 0xc00e0065,MQ_ERROR_WKS_CANT_SERVE_CLIENT = 0xc00e0066,
    MQ_ERROR_DEPEND_WKS_LICENSE_OVERFLOW = 0xc00e0067,MQ_CORRUPTED_QUEUE_WAS_DELETED = 0xc00e0068,MQ_ERROR_REMOTE_MACHINE_NOT_AVAILABLE = 0xc00e0069,
    MQ_ERROR_UNSUPPORTED_OPERATION = 0xc00e006a,MQ_ERROR_ENCRYPTION_PROVIDER_NOT_SUPPORTED = 0xc00e006b,
    MQ_ERROR_CANNOT_SET_CRYPTO_SEC_DESCR = 0xc00e006c,MQ_ERROR_CERTIFICATE_NOT_PROVIDED = 0xc00e006d,
    MQ_ERROR_Q_DNS_PROPERTY_NOT_SUPPORTED = 0xc00e006e,MQ_ERROR_CANT_CREATE_CERT_STORE = 0xc00e006f,MQ_ERROR_CANNOT_CREATE_CERT_STORE = 0xc00e006f,
    MQ_ERROR_CANT_OPEN_CERT_STORE = 0xc00e0070,MQ_ERROR_CANNOT_OPEN_CERT_STORE = 0xc00e0070,MQ_ERROR_ILLEGAL_ENTERPRISE_OPERATION = 0xc00e0071,
    MQ_ERROR_CANNOT_GRANT_ADD_GUID = 0xc00e0072,MQ_ERROR_CANNOT_LOAD_MSMQOCM = 0xc00e0073,MQ_ERROR_NO_ENTRY_POINT_MSMQOCM = 0xc00e0074,
    MQ_ERROR_NO_MSMQ_SERVERS_ON_DC = 0xc00e0075,MQ_ERROR_CANNOT_JOIN_DOMAIN = 0xc00e0076,MQ_ERROR_CANNOT_CREATE_ON_GC = 0xc00e0077,
    MQ_ERROR_GUID_NOT_MATCHING = 0xc00e0078,MQ_ERROR_PUBLIC_KEY_NOT_FOUND = 0xc00e0079,MQ_ERROR_PUBLIC_KEY_DOES_NOT_EXIST = 0xc00e007a,
    MQ_ERROR_ILLEGAL_MQPRIVATEPROPS = 0xc00e007b,MQ_ERROR_NO_GC_IN_DOMAIN = 0xc00e007c,MQ_ERROR_NO_MSMQ_SERVERS_ON_GC = 0xc00e007d,
    MQ_ERROR_CANNOT_GET_DN = 0xc00e007e,MQ_ERROR_CANNOT_HASH_DATA_EX = 0xc00e007f,MQ_ERROR_CANNOT_SIGN_DATA_EX = 0xc00e0080,
    MQ_ERROR_CANNOT_CREATE_HASH_EX = 0xc00e0081,MQ_ERROR_FAIL_VERIFY_SIGNATURE_EX = 0xc00e0082,MQ_ERROR_CANNOT_DELETE_PSC_OBJECTS = 0xc00e0083,
    MQ_ERROR_NO_MQUSER_OU = 0xc00e0084,MQ_ERROR_CANNOT_LOAD_MQAD = 0xc00e0085,MQ_ERROR_CANNOT_LOAD_MQDSSRV = 0xc00e0086,
    MQ_ERROR_PROPERTIES_CONFLICT = 0xc00e0087,MQ_ERROR_MESSAGE_NOT_FOUND = 0xc00e0088,MQ_ERROR_CANT_RESOLVE_SITES = 0xc00e0089,
    MQ_ERROR_NOT_SUPPORTED_BY_DEPENDENT_CLIENTS = 0xc00e008a,MQ_ERROR_OPERATION_NOT_SUPPORTED_BY_REMOTE_COMPUTER = 0xc00e008b,
    MQ_ERROR_NOT_A_CORRECT_OBJECT_CLASS = 0xc00e008c,MQ_ERROR_MULTI_SORT_KEYS = 0xc00e008d,MQ_ERROR_GC_NEEDED = 0xc00e008e,
    MQ_ERROR_DS_BIND_ROOT_FOREST = 0xc00e008f,MQ_ERROR_DS_LOCAL_USER = 0xc00e0090,MQ_ERROR_Q_ADS_PROPERTY_NOT_SUPPORTED = 0xc00e0091,
    MQ_ERROR_BAD_XML_FORMAT = 0xc00e0092,MQ_ERROR_UNSUPPORTED_CLASS = 0xc00e0093,MQ_ERROR_UNINITIALIZED_OBJECT = 0xc00e0094,
    MQ_ERROR_CANNOT_CREATE_PSC_OBJECTS = 0xc00e0095,MQ_ERROR_CANNOT_UPDATE_PSC_OBJECTS = 0xc00e0096
  };

  enum MQWARNING {
    MQ_INFORMATION_PROPERTY = 0x400e0001,MQ_INFORMATION_ILLEGAL_PROPERTY = 0x400e0002,MQ_INFORMATION_PROPERTY_IGNORED = 0x400e0003,
    MQ_INFORMATION_UNSUPPORTED_PROPERTY = 0x400e0004,MQ_INFORMATION_DUPLICATE_PROPERTY = 0x400e0005,MQ_INFORMATION_OPERATION_PENDING = 0x400e0006,
    MQ_INFORMATION_FORMATNAME_BUFFER_TOO_SMALL = 0x400e0009,MQ_INFORMATION_INTERNAL_USER_CERT_EXIST = 0x400e000a,
    MQ_INFORMATION_OWNER_IGNORED = 0x400e000b
  };

  EXTERN_C const IID LIBID_MSMQ;
#ifndef __IMSMQQuery_INTERFACE_DEFINED__
#define __IMSMQQuery_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQuery;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQuery : public IDispatch {
  public:
    virtual HRESULT WINAPI LookupQueue(VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos **ppqinfos) = 0;
  };
#else
  typedef struct IMSMQQueryVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQuery *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQuery *This);
      ULONG (WINAPI *Release)(IMSMQQuery *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQuery *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQuery *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQuery *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQuery *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *LookupQueue)(IMSMQQuery *This,VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos **ppqinfos);
    END_INTERFACE
  } IMSMQQueryVtbl;
  struct IMSMQQuery {
    CONST_VTBL struct IMSMQQueryVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQuery_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQuery_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQuery_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQuery_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQuery_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQuery_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQuery_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQuery_LookupQueue(This,QueueGuid,ServiceTypeGuid,Label,CreateTime,ModifyTime,RelServiceType,RelLabel,RelCreateTime,RelModifyTime,ppqinfos) (This)->lpVtbl->LookupQueue(This,QueueGuid,ServiceTypeGuid,Label,CreateTime,ModifyTime,RelServiceType,RelLabel,RelCreateTime,RelModifyTime,ppqinfos)
#endif
#endif
  HRESULT WINAPI IMSMQQuery_LookupQueue_Proxy(IMSMQQuery *This,VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos **ppqinfos);
  void __RPC_STUB IMSMQQuery_LookupQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQueueInfo_INTERFACE_DEFINED__
#define __IMSMQQueueInfo_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueueInfo;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueueInfo : public IDispatch {
  public:
    virtual HRESULT WINAPI get_QueueGuid(BSTR *pbstrGuidQueue) = 0;
    virtual HRESULT WINAPI get_ServiceTypeGuid(BSTR *pbstrGuidServiceType) = 0;
    virtual HRESULT WINAPI put_ServiceTypeGuid(BSTR bstrGuidServiceType) = 0;
    virtual HRESULT WINAPI get_Label(BSTR *pbstrLabel) = 0;
    virtual HRESULT WINAPI put_Label(BSTR bstrLabel) = 0;
    virtual HRESULT WINAPI get_PathName(BSTR *pbstrPathName) = 0;
    virtual HRESULT WINAPI put_PathName(BSTR bstrPathName) = 0;
    virtual HRESULT WINAPI get_FormatName(BSTR *pbstrFormatName) = 0;
    virtual HRESULT WINAPI put_FormatName(BSTR bstrFormatName) = 0;
    virtual HRESULT WINAPI get_IsTransactional(Boolean *pisTransactional) = 0;
    virtual HRESULT WINAPI get_PrivLevel(__LONG32 *plPrivLevel) = 0;
    virtual HRESULT WINAPI put_PrivLevel(__LONG32 lPrivLevel) = 0;
    virtual HRESULT WINAPI get_Journal(__LONG32 *plJournal) = 0;
    virtual HRESULT WINAPI put_Journal(__LONG32 lJournal) = 0;
    virtual HRESULT WINAPI get_Quota(__LONG32 *plQuota) = 0;
    virtual HRESULT WINAPI put_Quota(__LONG32 lQuota) = 0;
    virtual HRESULT WINAPI get_BasePriority(__LONG32 *plBasePriority) = 0;
    virtual HRESULT WINAPI put_BasePriority(__LONG32 lBasePriority) = 0;
    virtual HRESULT WINAPI get_CreateTime(VARIANT *pvarCreateTime) = 0;
    virtual HRESULT WINAPI get_ModifyTime(VARIANT *pvarModifyTime) = 0;
    virtual HRESULT WINAPI get_Authenticate(__LONG32 *plAuthenticate) = 0;
    virtual HRESULT WINAPI put_Authenticate(__LONG32 lAuthenticate) = 0;
    virtual HRESULT WINAPI get_JournalQuota(__LONG32 *plJournalQuota) = 0;
    virtual HRESULT WINAPI put_JournalQuota(__LONG32 lJournalQuota) = 0;
    virtual HRESULT WINAPI get_IsWorldReadable(Boolean *pisWorldReadable) = 0;
    virtual HRESULT WINAPI Create(VARIANT *IsTransactional,VARIANT *IsWorldReadable) = 0;
    virtual HRESULT WINAPI Delete(void) = 0;
    virtual HRESULT WINAPI Open(__LONG32 Access,__LONG32 ShareMode,IMSMQQueue **ppq) = 0;
    virtual HRESULT WINAPI Refresh(void) = 0;
    virtual HRESULT WINAPI Update(void) = 0;
  };
#else
  typedef struct IMSMQQueueInfoVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueueInfo *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueueInfo *This);
      ULONG (WINAPI *Release)(IMSMQQueueInfo *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueueInfo *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueueInfo *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueueInfo *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueueInfo *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_QueueGuid)(IMSMQQueueInfo *This,BSTR *pbstrGuidQueue);
      HRESULT (WINAPI *get_ServiceTypeGuid)(IMSMQQueueInfo *This,BSTR *pbstrGuidServiceType);
      HRESULT (WINAPI *put_ServiceTypeGuid)(IMSMQQueueInfo *This,BSTR bstrGuidServiceType);
      HRESULT (WINAPI *get_Label)(IMSMQQueueInfo *This,BSTR *pbstrLabel);
      HRESULT (WINAPI *put_Label)(IMSMQQueueInfo *This,BSTR bstrLabel);
      HRESULT (WINAPI *get_PathName)(IMSMQQueueInfo *This,BSTR *pbstrPathName);
      HRESULT (WINAPI *put_PathName)(IMSMQQueueInfo *This,BSTR bstrPathName);
      HRESULT (WINAPI *get_FormatName)(IMSMQQueueInfo *This,BSTR *pbstrFormatName);
      HRESULT (WINAPI *put_FormatName)(IMSMQQueueInfo *This,BSTR bstrFormatName);
      HRESULT (WINAPI *get_IsTransactional)(IMSMQQueueInfo *This,Boolean *pisTransactional);
      HRESULT (WINAPI *get_PrivLevel)(IMSMQQueueInfo *This,__LONG32 *plPrivLevel);
      HRESULT (WINAPI *put_PrivLevel)(IMSMQQueueInfo *This,__LONG32 lPrivLevel);
      HRESULT (WINAPI *get_Journal)(IMSMQQueueInfo *This,__LONG32 *plJournal);
      HRESULT (WINAPI *put_Journal)(IMSMQQueueInfo *This,__LONG32 lJournal);
      HRESULT (WINAPI *get_Quota)(IMSMQQueueInfo *This,__LONG32 *plQuota);
      HRESULT (WINAPI *put_Quota)(IMSMQQueueInfo *This,__LONG32 lQuota);
      HRESULT (WINAPI *get_BasePriority)(IMSMQQueueInfo *This,__LONG32 *plBasePriority);
      HRESULT (WINAPI *put_BasePriority)(IMSMQQueueInfo *This,__LONG32 lBasePriority);
      HRESULT (WINAPI *get_CreateTime)(IMSMQQueueInfo *This,VARIANT *pvarCreateTime);
      HRESULT (WINAPI *get_ModifyTime)(IMSMQQueueInfo *This,VARIANT *pvarModifyTime);
      HRESULT (WINAPI *get_Authenticate)(IMSMQQueueInfo *This,__LONG32 *plAuthenticate);
      HRESULT (WINAPI *put_Authenticate)(IMSMQQueueInfo *This,__LONG32 lAuthenticate);
      HRESULT (WINAPI *get_JournalQuota)(IMSMQQueueInfo *This,__LONG32 *plJournalQuota);
      HRESULT (WINAPI *put_JournalQuota)(IMSMQQueueInfo *This,__LONG32 lJournalQuota);
      HRESULT (WINAPI *get_IsWorldReadable)(IMSMQQueueInfo *This,Boolean *pisWorldReadable);
      HRESULT (WINAPI *Create)(IMSMQQueueInfo *This,VARIANT *IsTransactional,VARIANT *IsWorldReadable);
      HRESULT (WINAPI *Delete)(IMSMQQueueInfo *This);
      HRESULT (WINAPI *Open)(IMSMQQueueInfo *This,__LONG32 Access,__LONG32 ShareMode,IMSMQQueue **ppq);
      HRESULT (WINAPI *Refresh)(IMSMQQueueInfo *This);
      HRESULT (WINAPI *Update)(IMSMQQueueInfo *This);
    END_INTERFACE
  } IMSMQQueueInfoVtbl;
  struct IMSMQQueueInfo {
    CONST_VTBL struct IMSMQQueueInfoVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueueInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueueInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueueInfo_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueueInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueueInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueueInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueueInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueueInfo_get_QueueGuid(This,pbstrGuidQueue) (This)->lpVtbl->get_QueueGuid(This,pbstrGuidQueue)
#define IMSMQQueueInfo_get_ServiceTypeGuid(This,pbstrGuidServiceType) (This)->lpVtbl->get_ServiceTypeGuid(This,pbstrGuidServiceType)
#define IMSMQQueueInfo_put_ServiceTypeGuid(This,bstrGuidServiceType) (This)->lpVtbl->put_ServiceTypeGuid(This,bstrGuidServiceType)
#define IMSMQQueueInfo_get_Label(This,pbstrLabel) (This)->lpVtbl->get_Label(This,pbstrLabel)
#define IMSMQQueueInfo_put_Label(This,bstrLabel) (This)->lpVtbl->put_Label(This,bstrLabel)
#define IMSMQQueueInfo_get_PathName(This,pbstrPathName) (This)->lpVtbl->get_PathName(This,pbstrPathName)
#define IMSMQQueueInfo_put_PathName(This,bstrPathName) (This)->lpVtbl->put_PathName(This,bstrPathName)
#define IMSMQQueueInfo_get_FormatName(This,pbstrFormatName) (This)->lpVtbl->get_FormatName(This,pbstrFormatName)
#define IMSMQQueueInfo_put_FormatName(This,bstrFormatName) (This)->lpVtbl->put_FormatName(This,bstrFormatName)
#define IMSMQQueueInfo_get_IsTransactional(This,pisTransactional) (This)->lpVtbl->get_IsTransactional(This,pisTransactional)
#define IMSMQQueueInfo_get_PrivLevel(This,plPrivLevel) (This)->lpVtbl->get_PrivLevel(This,plPrivLevel)
#define IMSMQQueueInfo_put_PrivLevel(This,lPrivLevel) (This)->lpVtbl->put_PrivLevel(This,lPrivLevel)
#define IMSMQQueueInfo_get_Journal(This,plJournal) (This)->lpVtbl->get_Journal(This,plJournal)
#define IMSMQQueueInfo_put_Journal(This,lJournal) (This)->lpVtbl->put_Journal(This,lJournal)
#define IMSMQQueueInfo_get_Quota(This,plQuota) (This)->lpVtbl->get_Quota(This,plQuota)
#define IMSMQQueueInfo_put_Quota(This,lQuota) (This)->lpVtbl->put_Quota(This,lQuota)
#define IMSMQQueueInfo_get_BasePriority(This,plBasePriority) (This)->lpVtbl->get_BasePriority(This,plBasePriority)
#define IMSMQQueueInfo_put_BasePriority(This,lBasePriority) (This)->lpVtbl->put_BasePriority(This,lBasePriority)
#define IMSMQQueueInfo_get_CreateTime(This,pvarCreateTime) (This)->lpVtbl->get_CreateTime(This,pvarCreateTime)
#define IMSMQQueueInfo_get_ModifyTime(This,pvarModifyTime) (This)->lpVtbl->get_ModifyTime(This,pvarModifyTime)
#define IMSMQQueueInfo_get_Authenticate(This,plAuthenticate) (This)->lpVtbl->get_Authenticate(This,plAuthenticate)
#define IMSMQQueueInfo_put_Authenticate(This,lAuthenticate) (This)->lpVtbl->put_Authenticate(This,lAuthenticate)
#define IMSMQQueueInfo_get_JournalQuota(This,plJournalQuota) (This)->lpVtbl->get_JournalQuota(This,plJournalQuota)
#define IMSMQQueueInfo_put_JournalQuota(This,lJournalQuota) (This)->lpVtbl->put_JournalQuota(This,lJournalQuota)
#define IMSMQQueueInfo_get_IsWorldReadable(This,pisWorldReadable) (This)->lpVtbl->get_IsWorldReadable(This,pisWorldReadable)
#define IMSMQQueueInfo_Create(This,IsTransactional,IsWorldReadable) (This)->lpVtbl->Create(This,IsTransactional,IsWorldReadable)
#define IMSMQQueueInfo_Delete(This) (This)->lpVtbl->Delete(This)
#define IMSMQQueueInfo_Open(This,Access,ShareMode,ppq) (This)->lpVtbl->Open(This,Access,ShareMode,ppq)
#define IMSMQQueueInfo_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IMSMQQueueInfo_Update(This) (This)->lpVtbl->Update(This)
#endif
#endif
  HRESULT WINAPI IMSMQQueueInfo_get_QueueGuid_Proxy(IMSMQQueueInfo *This,BSTR *pbstrGuidQueue);
  void __RPC_STUB IMSMQQueueInfo_get_QueueGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_ServiceTypeGuid_Proxy(IMSMQQueueInfo *This,BSTR *pbstrGuidServiceType);
  void __RPC_STUB IMSMQQueueInfo_get_ServiceTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_ServiceTypeGuid_Proxy(IMSMQQueueInfo *This,BSTR bstrGuidServiceType);
  void __RPC_STUB IMSMQQueueInfo_put_ServiceTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_Label_Proxy(IMSMQQueueInfo *This,BSTR *pbstrLabel);
  void __RPC_STUB IMSMQQueueInfo_get_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_Label_Proxy(IMSMQQueueInfo *This,BSTR bstrLabel);
  void __RPC_STUB IMSMQQueueInfo_put_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_PathName_Proxy(IMSMQQueueInfo *This,BSTR *pbstrPathName);
  void __RPC_STUB IMSMQQueueInfo_get_PathName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_PathName_Proxy(IMSMQQueueInfo *This,BSTR bstrPathName);
  void __RPC_STUB IMSMQQueueInfo_put_PathName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_FormatName_Proxy(IMSMQQueueInfo *This,BSTR *pbstrFormatName);
  void __RPC_STUB IMSMQQueueInfo_get_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_FormatName_Proxy(IMSMQQueueInfo *This,BSTR bstrFormatName);
  void __RPC_STUB IMSMQQueueInfo_put_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_IsTransactional_Proxy(IMSMQQueueInfo *This,Boolean *pisTransactional);
  void __RPC_STUB IMSMQQueueInfo_get_IsTransactional_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_PrivLevel_Proxy(IMSMQQueueInfo *This,__LONG32 *plPrivLevel);
  void __RPC_STUB IMSMQQueueInfo_get_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_PrivLevel_Proxy(IMSMQQueueInfo *This,__LONG32 lPrivLevel);
  void __RPC_STUB IMSMQQueueInfo_put_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_Journal_Proxy(IMSMQQueueInfo *This,__LONG32 *plJournal);
  void __RPC_STUB IMSMQQueueInfo_get_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_Journal_Proxy(IMSMQQueueInfo *This,__LONG32 lJournal);
  void __RPC_STUB IMSMQQueueInfo_put_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_Quota_Proxy(IMSMQQueueInfo *This,__LONG32 *plQuota);
  void __RPC_STUB IMSMQQueueInfo_get_Quota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_Quota_Proxy(IMSMQQueueInfo *This,__LONG32 lQuota);
  void __RPC_STUB IMSMQQueueInfo_put_Quota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_BasePriority_Proxy(IMSMQQueueInfo *This,__LONG32 *plBasePriority);
  void __RPC_STUB IMSMQQueueInfo_get_BasePriority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_BasePriority_Proxy(IMSMQQueueInfo *This,__LONG32 lBasePriority);
  void __RPC_STUB IMSMQQueueInfo_put_BasePriority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_CreateTime_Proxy(IMSMQQueueInfo *This,VARIANT *pvarCreateTime);
  void __RPC_STUB IMSMQQueueInfo_get_CreateTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_ModifyTime_Proxy(IMSMQQueueInfo *This,VARIANT *pvarModifyTime);
  void __RPC_STUB IMSMQQueueInfo_get_ModifyTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_Authenticate_Proxy(IMSMQQueueInfo *This,__LONG32 *plAuthenticate);
  void __RPC_STUB IMSMQQueueInfo_get_Authenticate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_Authenticate_Proxy(IMSMQQueueInfo *This,__LONG32 lAuthenticate);
  void __RPC_STUB IMSMQQueueInfo_put_Authenticate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_JournalQuota_Proxy(IMSMQQueueInfo *This,__LONG32 *plJournalQuota);
  void __RPC_STUB IMSMQQueueInfo_get_JournalQuota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_put_JournalQuota_Proxy(IMSMQQueueInfo *This,__LONG32 lJournalQuota);
  void __RPC_STUB IMSMQQueueInfo_put_JournalQuota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_get_IsWorldReadable_Proxy(IMSMQQueueInfo *This,Boolean *pisWorldReadable);
  void __RPC_STUB IMSMQQueueInfo_get_IsWorldReadable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_Create_Proxy(IMSMQQueueInfo *This,VARIANT *IsTransactional,VARIANT *IsWorldReadable);
  void __RPC_STUB IMSMQQueueInfo_Create_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_Delete_Proxy(IMSMQQueueInfo *This);
  void __RPC_STUB IMSMQQueueInfo_Delete_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_Open_Proxy(IMSMQQueueInfo *This,__LONG32 Access,__LONG32 ShareMode,IMSMQQueue **ppq);
  void __RPC_STUB IMSMQQueueInfo_Open_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_Refresh_Proxy(IMSMQQueueInfo *This);
  void __RPC_STUB IMSMQQueueInfo_Refresh_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo_Update_Proxy(IMSMQQueueInfo *This);
  void __RPC_STUB IMSMQQueueInfo_Update_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQueueInfo2_INTERFACE_DEFINED__
#define __IMSMQQueueInfo2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueueInfo2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueueInfo2 : public IDispatch {
  public:
    virtual HRESULT WINAPI get_QueueGuid(BSTR *pbstrGuidQueue) = 0;
    virtual HRESULT WINAPI get_ServiceTypeGuid(BSTR *pbstrGuidServiceType) = 0;
    virtual HRESULT WINAPI put_ServiceTypeGuid(BSTR bstrGuidServiceType) = 0;
    virtual HRESULT WINAPI get_Label(BSTR *pbstrLabel) = 0;
    virtual HRESULT WINAPI put_Label(BSTR bstrLabel) = 0;
    virtual HRESULT WINAPI get_PathName(BSTR *pbstrPathName) = 0;
    virtual HRESULT WINAPI put_PathName(BSTR bstrPathName) = 0;
    virtual HRESULT WINAPI get_FormatName(BSTR *pbstrFormatName) = 0;
    virtual HRESULT WINAPI put_FormatName(BSTR bstrFormatName) = 0;
    virtual HRESULT WINAPI get_IsTransactional(Boolean *pisTransactional) = 0;
    virtual HRESULT WINAPI get_PrivLevel(__LONG32 *plPrivLevel) = 0;
    virtual HRESULT WINAPI put_PrivLevel(__LONG32 lPrivLevel) = 0;
    virtual HRESULT WINAPI get_Journal(__LONG32 *plJournal) = 0;
    virtual HRESULT WINAPI put_Journal(__LONG32 lJournal) = 0;
    virtual HRESULT WINAPI get_Quota(__LONG32 *plQuota) = 0;
    virtual HRESULT WINAPI put_Quota(__LONG32 lQuota) = 0;
    virtual HRESULT WINAPI get_BasePriority(__LONG32 *plBasePriority) = 0;
    virtual HRESULT WINAPI put_BasePriority(__LONG32 lBasePriority) = 0;
    virtual HRESULT WINAPI get_CreateTime(VARIANT *pvarCreateTime) = 0;
    virtual HRESULT WINAPI get_ModifyTime(VARIANT *pvarModifyTime) = 0;
    virtual HRESULT WINAPI get_Authenticate(__LONG32 *plAuthenticate) = 0;
    virtual HRESULT WINAPI put_Authenticate(__LONG32 lAuthenticate) = 0;
    virtual HRESULT WINAPI get_JournalQuota(__LONG32 *plJournalQuota) = 0;
    virtual HRESULT WINAPI put_JournalQuota(__LONG32 lJournalQuota) = 0;
    virtual HRESULT WINAPI get_IsWorldReadable(Boolean *pisWorldReadable) = 0;
    virtual HRESULT WINAPI Create(VARIANT *IsTransactional,VARIANT *IsWorldReadable) = 0;
    virtual HRESULT WINAPI Delete(void) = 0;
    virtual HRESULT WINAPI Open(__LONG32 Access,__LONG32 ShareMode,IMSMQQueue2 **ppq) = 0;
    virtual HRESULT WINAPI Refresh(void) = 0;
    virtual HRESULT WINAPI Update(void) = 0;
    virtual HRESULT WINAPI get_PathNameDNS(BSTR *pbstrPathNameDNS) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
    virtual HRESULT WINAPI get_Security(VARIANT *pvarSecurity) = 0;
    virtual HRESULT WINAPI put_Security(VARIANT varSecurity) = 0;
  };
#else
  typedef struct IMSMQQueueInfo2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueueInfo2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueueInfo2 *This);
      ULONG (WINAPI *Release)(IMSMQQueueInfo2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueueInfo2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueueInfo2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueueInfo2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueueInfo2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_QueueGuid)(IMSMQQueueInfo2 *This,BSTR *pbstrGuidQueue);
      HRESULT (WINAPI *get_ServiceTypeGuid)(IMSMQQueueInfo2 *This,BSTR *pbstrGuidServiceType);
      HRESULT (WINAPI *put_ServiceTypeGuid)(IMSMQQueueInfo2 *This,BSTR bstrGuidServiceType);
      HRESULT (WINAPI *get_Label)(IMSMQQueueInfo2 *This,BSTR *pbstrLabel);
      HRESULT (WINAPI *put_Label)(IMSMQQueueInfo2 *This,BSTR bstrLabel);
      HRESULT (WINAPI *get_PathName)(IMSMQQueueInfo2 *This,BSTR *pbstrPathName);
      HRESULT (WINAPI *put_PathName)(IMSMQQueueInfo2 *This,BSTR bstrPathName);
      HRESULT (WINAPI *get_FormatName)(IMSMQQueueInfo2 *This,BSTR *pbstrFormatName);
      HRESULT (WINAPI *put_FormatName)(IMSMQQueueInfo2 *This,BSTR bstrFormatName);
      HRESULT (WINAPI *get_IsTransactional)(IMSMQQueueInfo2 *This,Boolean *pisTransactional);
      HRESULT (WINAPI *get_PrivLevel)(IMSMQQueueInfo2 *This,__LONG32 *plPrivLevel);
      HRESULT (WINAPI *put_PrivLevel)(IMSMQQueueInfo2 *This,__LONG32 lPrivLevel);
      HRESULT (WINAPI *get_Journal)(IMSMQQueueInfo2 *This,__LONG32 *plJournal);
      HRESULT (WINAPI *put_Journal)(IMSMQQueueInfo2 *This,__LONG32 lJournal);
      HRESULT (WINAPI *get_Quota)(IMSMQQueueInfo2 *This,__LONG32 *plQuota);
      HRESULT (WINAPI *put_Quota)(IMSMQQueueInfo2 *This,__LONG32 lQuota);
      HRESULT (WINAPI *get_BasePriority)(IMSMQQueueInfo2 *This,__LONG32 *plBasePriority);
      HRESULT (WINAPI *put_BasePriority)(IMSMQQueueInfo2 *This,__LONG32 lBasePriority);
      HRESULT (WINAPI *get_CreateTime)(IMSMQQueueInfo2 *This,VARIANT *pvarCreateTime);
      HRESULT (WINAPI *get_ModifyTime)(IMSMQQueueInfo2 *This,VARIANT *pvarModifyTime);
      HRESULT (WINAPI *get_Authenticate)(IMSMQQueueInfo2 *This,__LONG32 *plAuthenticate);
      HRESULT (WINAPI *put_Authenticate)(IMSMQQueueInfo2 *This,__LONG32 lAuthenticate);
      HRESULT (WINAPI *get_JournalQuota)(IMSMQQueueInfo2 *This,__LONG32 *plJournalQuota);
      HRESULT (WINAPI *put_JournalQuota)(IMSMQQueueInfo2 *This,__LONG32 lJournalQuota);
      HRESULT (WINAPI *get_IsWorldReadable)(IMSMQQueueInfo2 *This,Boolean *pisWorldReadable);
      HRESULT (WINAPI *Create)(IMSMQQueueInfo2 *This,VARIANT *IsTransactional,VARIANT *IsWorldReadable);
      HRESULT (WINAPI *Delete)(IMSMQQueueInfo2 *This);
      HRESULT (WINAPI *Open)(IMSMQQueueInfo2 *This,__LONG32 Access,__LONG32 ShareMode,IMSMQQueue2 **ppq);
      HRESULT (WINAPI *Refresh)(IMSMQQueueInfo2 *This);
      HRESULT (WINAPI *Update)(IMSMQQueueInfo2 *This);
      HRESULT (WINAPI *get_PathNameDNS)(IMSMQQueueInfo2 *This,BSTR *pbstrPathNameDNS);
      HRESULT (WINAPI *get_Properties)(IMSMQQueueInfo2 *This,IDispatch **ppcolProperties);
      HRESULT (WINAPI *get_Security)(IMSMQQueueInfo2 *This,VARIANT *pvarSecurity);
      HRESULT (WINAPI *put_Security)(IMSMQQueueInfo2 *This,VARIANT varSecurity);
    END_INTERFACE
  } IMSMQQueueInfo2Vtbl;
  struct IMSMQQueueInfo2 {
    CONST_VTBL struct IMSMQQueueInfo2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueueInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueueInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueueInfo2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueueInfo2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueueInfo2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueueInfo2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueueInfo2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueueInfo2_get_QueueGuid(This,pbstrGuidQueue) (This)->lpVtbl->get_QueueGuid(This,pbstrGuidQueue)
#define IMSMQQueueInfo2_get_ServiceTypeGuid(This,pbstrGuidServiceType) (This)->lpVtbl->get_ServiceTypeGuid(This,pbstrGuidServiceType)
#define IMSMQQueueInfo2_put_ServiceTypeGuid(This,bstrGuidServiceType) (This)->lpVtbl->put_ServiceTypeGuid(This,bstrGuidServiceType)
#define IMSMQQueueInfo2_get_Label(This,pbstrLabel) (This)->lpVtbl->get_Label(This,pbstrLabel)
#define IMSMQQueueInfo2_put_Label(This,bstrLabel) (This)->lpVtbl->put_Label(This,bstrLabel)
#define IMSMQQueueInfo2_get_PathName(This,pbstrPathName) (This)->lpVtbl->get_PathName(This,pbstrPathName)
#define IMSMQQueueInfo2_put_PathName(This,bstrPathName) (This)->lpVtbl->put_PathName(This,bstrPathName)
#define IMSMQQueueInfo2_get_FormatName(This,pbstrFormatName) (This)->lpVtbl->get_FormatName(This,pbstrFormatName)
#define IMSMQQueueInfo2_put_FormatName(This,bstrFormatName) (This)->lpVtbl->put_FormatName(This,bstrFormatName)
#define IMSMQQueueInfo2_get_IsTransactional(This,pisTransactional) (This)->lpVtbl->get_IsTransactional(This,pisTransactional)
#define IMSMQQueueInfo2_get_PrivLevel(This,plPrivLevel) (This)->lpVtbl->get_PrivLevel(This,plPrivLevel)
#define IMSMQQueueInfo2_put_PrivLevel(This,lPrivLevel) (This)->lpVtbl->put_PrivLevel(This,lPrivLevel)
#define IMSMQQueueInfo2_get_Journal(This,plJournal) (This)->lpVtbl->get_Journal(This,plJournal)
#define IMSMQQueueInfo2_put_Journal(This,lJournal) (This)->lpVtbl->put_Journal(This,lJournal)
#define IMSMQQueueInfo2_get_Quota(This,plQuota) (This)->lpVtbl->get_Quota(This,plQuota)
#define IMSMQQueueInfo2_put_Quota(This,lQuota) (This)->lpVtbl->put_Quota(This,lQuota)
#define IMSMQQueueInfo2_get_BasePriority(This,plBasePriority) (This)->lpVtbl->get_BasePriority(This,plBasePriority)
#define IMSMQQueueInfo2_put_BasePriority(This,lBasePriority) (This)->lpVtbl->put_BasePriority(This,lBasePriority)
#define IMSMQQueueInfo2_get_CreateTime(This,pvarCreateTime) (This)->lpVtbl->get_CreateTime(This,pvarCreateTime)
#define IMSMQQueueInfo2_get_ModifyTime(This,pvarModifyTime) (This)->lpVtbl->get_ModifyTime(This,pvarModifyTime)
#define IMSMQQueueInfo2_get_Authenticate(This,plAuthenticate) (This)->lpVtbl->get_Authenticate(This,plAuthenticate)
#define IMSMQQueueInfo2_put_Authenticate(This,lAuthenticate) (This)->lpVtbl->put_Authenticate(This,lAuthenticate)
#define IMSMQQueueInfo2_get_JournalQuota(This,plJournalQuota) (This)->lpVtbl->get_JournalQuota(This,plJournalQuota)
#define IMSMQQueueInfo2_put_JournalQuota(This,lJournalQuota) (This)->lpVtbl->put_JournalQuota(This,lJournalQuota)
#define IMSMQQueueInfo2_get_IsWorldReadable(This,pisWorldReadable) (This)->lpVtbl->get_IsWorldReadable(This,pisWorldReadable)
#define IMSMQQueueInfo2_Create(This,IsTransactional,IsWorldReadable) (This)->lpVtbl->Create(This,IsTransactional,IsWorldReadable)
#define IMSMQQueueInfo2_Delete(This) (This)->lpVtbl->Delete(This)
#define IMSMQQueueInfo2_Open(This,Access,ShareMode,ppq) (This)->lpVtbl->Open(This,Access,ShareMode,ppq)
#define IMSMQQueueInfo2_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IMSMQQueueInfo2_Update(This) (This)->lpVtbl->Update(This)
#define IMSMQQueueInfo2_get_PathNameDNS(This,pbstrPathNameDNS) (This)->lpVtbl->get_PathNameDNS(This,pbstrPathNameDNS)
#define IMSMQQueueInfo2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#define IMSMQQueueInfo2_get_Security(This,pvarSecurity) (This)->lpVtbl->get_Security(This,pvarSecurity)
#define IMSMQQueueInfo2_put_Security(This,varSecurity) (This)->lpVtbl->put_Security(This,varSecurity)
#endif
#endif
  HRESULT WINAPI IMSMQQueueInfo2_get_QueueGuid_Proxy(IMSMQQueueInfo2 *This,BSTR *pbstrGuidQueue);
  void __RPC_STUB IMSMQQueueInfo2_get_QueueGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_ServiceTypeGuid_Proxy(IMSMQQueueInfo2 *This,BSTR *pbstrGuidServiceType);
  void __RPC_STUB IMSMQQueueInfo2_get_ServiceTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_ServiceTypeGuid_Proxy(IMSMQQueueInfo2 *This,BSTR bstrGuidServiceType);
  void __RPC_STUB IMSMQQueueInfo2_put_ServiceTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_Label_Proxy(IMSMQQueueInfo2 *This,BSTR *pbstrLabel);
  void __RPC_STUB IMSMQQueueInfo2_get_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_Label_Proxy(IMSMQQueueInfo2 *This,BSTR bstrLabel);
  void __RPC_STUB IMSMQQueueInfo2_put_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_PathName_Proxy(IMSMQQueueInfo2 *This,BSTR *pbstrPathName);
  void __RPC_STUB IMSMQQueueInfo2_get_PathName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_PathName_Proxy(IMSMQQueueInfo2 *This,BSTR bstrPathName);
  void __RPC_STUB IMSMQQueueInfo2_put_PathName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_FormatName_Proxy(IMSMQQueueInfo2 *This,BSTR *pbstrFormatName);
  void __RPC_STUB IMSMQQueueInfo2_get_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_FormatName_Proxy(IMSMQQueueInfo2 *This,BSTR bstrFormatName);
  void __RPC_STUB IMSMQQueueInfo2_put_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_IsTransactional_Proxy(IMSMQQueueInfo2 *This,Boolean *pisTransactional);
  void __RPC_STUB IMSMQQueueInfo2_get_IsTransactional_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_PrivLevel_Proxy(IMSMQQueueInfo2 *This,__LONG32 *plPrivLevel);
  void __RPC_STUB IMSMQQueueInfo2_get_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_PrivLevel_Proxy(IMSMQQueueInfo2 *This,__LONG32 lPrivLevel);
  void __RPC_STUB IMSMQQueueInfo2_put_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_Journal_Proxy(IMSMQQueueInfo2 *This,__LONG32 *plJournal);
  void __RPC_STUB IMSMQQueueInfo2_get_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_Journal_Proxy(IMSMQQueueInfo2 *This,__LONG32 lJournal);
  void __RPC_STUB IMSMQQueueInfo2_put_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_Quota_Proxy(IMSMQQueueInfo2 *This,__LONG32 *plQuota);
  void __RPC_STUB IMSMQQueueInfo2_get_Quota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_Quota_Proxy(IMSMQQueueInfo2 *This,__LONG32 lQuota);
  void __RPC_STUB IMSMQQueueInfo2_put_Quota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_BasePriority_Proxy(IMSMQQueueInfo2 *This,__LONG32 *plBasePriority);
  void __RPC_STUB IMSMQQueueInfo2_get_BasePriority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_BasePriority_Proxy(IMSMQQueueInfo2 *This,__LONG32 lBasePriority);
  void __RPC_STUB IMSMQQueueInfo2_put_BasePriority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_CreateTime_Proxy(IMSMQQueueInfo2 *This,VARIANT *pvarCreateTime);
  void __RPC_STUB IMSMQQueueInfo2_get_CreateTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_ModifyTime_Proxy(IMSMQQueueInfo2 *This,VARIANT *pvarModifyTime);
  void __RPC_STUB IMSMQQueueInfo2_get_ModifyTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_Authenticate_Proxy(IMSMQQueueInfo2 *This,__LONG32 *plAuthenticate);
  void __RPC_STUB IMSMQQueueInfo2_get_Authenticate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_Authenticate_Proxy(IMSMQQueueInfo2 *This,__LONG32 lAuthenticate);
  void __RPC_STUB IMSMQQueueInfo2_put_Authenticate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_JournalQuota_Proxy(IMSMQQueueInfo2 *This,__LONG32 *plJournalQuota);
  void __RPC_STUB IMSMQQueueInfo2_get_JournalQuota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_JournalQuota_Proxy(IMSMQQueueInfo2 *This,__LONG32 lJournalQuota);
  void __RPC_STUB IMSMQQueueInfo2_put_JournalQuota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_IsWorldReadable_Proxy(IMSMQQueueInfo2 *This,Boolean *pisWorldReadable);
  void __RPC_STUB IMSMQQueueInfo2_get_IsWorldReadable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_Create_Proxy(IMSMQQueueInfo2 *This,VARIANT *IsTransactional,VARIANT *IsWorldReadable);
  void __RPC_STUB IMSMQQueueInfo2_Create_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_Delete_Proxy(IMSMQQueueInfo2 *This);
  void __RPC_STUB IMSMQQueueInfo2_Delete_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_Open_Proxy(IMSMQQueueInfo2 *This,__LONG32 Access,__LONG32 ShareMode,IMSMQQueue2 **ppq);
  void __RPC_STUB IMSMQQueueInfo2_Open_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_Refresh_Proxy(IMSMQQueueInfo2 *This);
  void __RPC_STUB IMSMQQueueInfo2_Refresh_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_Update_Proxy(IMSMQQueueInfo2 *This);
  void __RPC_STUB IMSMQQueueInfo2_Update_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_PathNameDNS_Proxy(IMSMQQueueInfo2 *This,BSTR *pbstrPathNameDNS);
  void __RPC_STUB IMSMQQueueInfo2_get_PathNameDNS_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_Properties_Proxy(IMSMQQueueInfo2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQQueueInfo2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_get_Security_Proxy(IMSMQQueueInfo2 *This,VARIANT *pvarSecurity);
  void __RPC_STUB IMSMQQueueInfo2_get_Security_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo2_put_Security_Proxy(IMSMQQueueInfo2 *This,VARIANT varSecurity);
  void __RPC_STUB IMSMQQueueInfo2_put_Security_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQueueInfo3_INTERFACE_DEFINED__
#define __IMSMQQueueInfo3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueueInfo3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueueInfo3 : public IDispatch {
  public:
    virtual HRESULT WINAPI get_QueueGuid(BSTR *pbstrGuidQueue) = 0;
    virtual HRESULT WINAPI get_ServiceTypeGuid(BSTR *pbstrGuidServiceType) = 0;
    virtual HRESULT WINAPI put_ServiceTypeGuid(BSTR bstrGuidServiceType) = 0;
    virtual HRESULT WINAPI get_Label(BSTR *pbstrLabel) = 0;
    virtual HRESULT WINAPI put_Label(BSTR bstrLabel) = 0;
    virtual HRESULT WINAPI get_PathName(BSTR *pbstrPathName) = 0;
    virtual HRESULT WINAPI put_PathName(BSTR bstrPathName) = 0;
    virtual HRESULT WINAPI get_FormatName(BSTR *pbstrFormatName) = 0;
    virtual HRESULT WINAPI put_FormatName(BSTR bstrFormatName) = 0;
    virtual HRESULT WINAPI get_IsTransactional(Boolean *pisTransactional) = 0;
    virtual HRESULT WINAPI get_PrivLevel(__LONG32 *plPrivLevel) = 0;
    virtual HRESULT WINAPI put_PrivLevel(__LONG32 lPrivLevel) = 0;
    virtual HRESULT WINAPI get_Journal(__LONG32 *plJournal) = 0;
    virtual HRESULT WINAPI put_Journal(__LONG32 lJournal) = 0;
    virtual HRESULT WINAPI get_Quota(__LONG32 *plQuota) = 0;
    virtual HRESULT WINAPI put_Quota(__LONG32 lQuota) = 0;
    virtual HRESULT WINAPI get_BasePriority(__LONG32 *plBasePriority) = 0;
    virtual HRESULT WINAPI put_BasePriority(__LONG32 lBasePriority) = 0;
    virtual HRESULT WINAPI get_CreateTime(VARIANT *pvarCreateTime) = 0;
    virtual HRESULT WINAPI get_ModifyTime(VARIANT *pvarModifyTime) = 0;
    virtual HRESULT WINAPI get_Authenticate(__LONG32 *plAuthenticate) = 0;
    virtual HRESULT WINAPI put_Authenticate(__LONG32 lAuthenticate) = 0;
    virtual HRESULT WINAPI get_JournalQuota(__LONG32 *plJournalQuota) = 0;
    virtual HRESULT WINAPI put_JournalQuota(__LONG32 lJournalQuota) = 0;
    virtual HRESULT WINAPI get_IsWorldReadable(Boolean *pisWorldReadable) = 0;
    virtual HRESULT WINAPI Create(VARIANT *IsTransactional,VARIANT *IsWorldReadable) = 0;
    virtual HRESULT WINAPI Delete(void) = 0;
    virtual HRESULT WINAPI Open(__LONG32 Access,__LONG32 ShareMode,IMSMQQueue3 **ppq) = 0;
    virtual HRESULT WINAPI Refresh(void) = 0;
    virtual HRESULT WINAPI Update(void) = 0;
    virtual HRESULT WINAPI get_PathNameDNS(BSTR *pbstrPathNameDNS) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
    virtual HRESULT WINAPI get_Security(VARIANT *pvarSecurity) = 0;
    virtual HRESULT WINAPI put_Security(VARIANT varSecurity) = 0;
    virtual HRESULT WINAPI get_IsTransactional2(VARIANT_BOOL *pisTransactional) = 0;
    virtual HRESULT WINAPI get_IsWorldReadable2(VARIANT_BOOL *pisWorldReadable) = 0;
    virtual HRESULT WINAPI get_MulticastAddress(BSTR *pbstrMulticastAddress) = 0;
    virtual HRESULT WINAPI put_MulticastAddress(BSTR bstrMulticastAddress) = 0;
    virtual HRESULT WINAPI get_ADsPath(BSTR *pbstrADsPath) = 0;
  };
#else
  typedef struct IMSMQQueueInfo3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueueInfo3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueueInfo3 *This);
      ULONG (WINAPI *Release)(IMSMQQueueInfo3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueueInfo3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueueInfo3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueueInfo3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueueInfo3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_QueueGuid)(IMSMQQueueInfo3 *This,BSTR *pbstrGuidQueue);
      HRESULT (WINAPI *get_ServiceTypeGuid)(IMSMQQueueInfo3 *This,BSTR *pbstrGuidServiceType);
      HRESULT (WINAPI *put_ServiceTypeGuid)(IMSMQQueueInfo3 *This,BSTR bstrGuidServiceType);
      HRESULT (WINAPI *get_Label)(IMSMQQueueInfo3 *This,BSTR *pbstrLabel);
      HRESULT (WINAPI *put_Label)(IMSMQQueueInfo3 *This,BSTR bstrLabel);
      HRESULT (WINAPI *get_PathName)(IMSMQQueueInfo3 *This,BSTR *pbstrPathName);
      HRESULT (WINAPI *put_PathName)(IMSMQQueueInfo3 *This,BSTR bstrPathName);
      HRESULT (WINAPI *get_FormatName)(IMSMQQueueInfo3 *This,BSTR *pbstrFormatName);
      HRESULT (WINAPI *put_FormatName)(IMSMQQueueInfo3 *This,BSTR bstrFormatName);
      HRESULT (WINAPI *get_IsTransactional)(IMSMQQueueInfo3 *This,Boolean *pisTransactional);
      HRESULT (WINAPI *get_PrivLevel)(IMSMQQueueInfo3 *This,__LONG32 *plPrivLevel);
      HRESULT (WINAPI *put_PrivLevel)(IMSMQQueueInfo3 *This,__LONG32 lPrivLevel);
      HRESULT (WINAPI *get_Journal)(IMSMQQueueInfo3 *This,__LONG32 *plJournal);
      HRESULT (WINAPI *put_Journal)(IMSMQQueueInfo3 *This,__LONG32 lJournal);
      HRESULT (WINAPI *get_Quota)(IMSMQQueueInfo3 *This,__LONG32 *plQuota);
      HRESULT (WINAPI *put_Quota)(IMSMQQueueInfo3 *This,__LONG32 lQuota);
      HRESULT (WINAPI *get_BasePriority)(IMSMQQueueInfo3 *This,__LONG32 *plBasePriority);
      HRESULT (WINAPI *put_BasePriority)(IMSMQQueueInfo3 *This,__LONG32 lBasePriority);
      HRESULT (WINAPI *get_CreateTime)(IMSMQQueueInfo3 *This,VARIANT *pvarCreateTime);
      HRESULT (WINAPI *get_ModifyTime)(IMSMQQueueInfo3 *This,VARIANT *pvarModifyTime);
      HRESULT (WINAPI *get_Authenticate)(IMSMQQueueInfo3 *This,__LONG32 *plAuthenticate);
      HRESULT (WINAPI *put_Authenticate)(IMSMQQueueInfo3 *This,__LONG32 lAuthenticate);
      HRESULT (WINAPI *get_JournalQuota)(IMSMQQueueInfo3 *This,__LONG32 *plJournalQuota);
      HRESULT (WINAPI *put_JournalQuota)(IMSMQQueueInfo3 *This,__LONG32 lJournalQuota);
      HRESULT (WINAPI *get_IsWorldReadable)(IMSMQQueueInfo3 *This,Boolean *pisWorldReadable);
      HRESULT (WINAPI *Create)(IMSMQQueueInfo3 *This,VARIANT *IsTransactional,VARIANT *IsWorldReadable);
      HRESULT (WINAPI *Delete)(IMSMQQueueInfo3 *This);
      HRESULT (WINAPI *Open)(IMSMQQueueInfo3 *This,__LONG32 Access,__LONG32 ShareMode,IMSMQQueue3 **ppq);
      HRESULT (WINAPI *Refresh)(IMSMQQueueInfo3 *This);
      HRESULT (WINAPI *Update)(IMSMQQueueInfo3 *This);
      HRESULT (WINAPI *get_PathNameDNS)(IMSMQQueueInfo3 *This,BSTR *pbstrPathNameDNS);
      HRESULT (WINAPI *get_Properties)(IMSMQQueueInfo3 *This,IDispatch **ppcolProperties);
      HRESULT (WINAPI *get_Security)(IMSMQQueueInfo3 *This,VARIANT *pvarSecurity);
      HRESULT (WINAPI *put_Security)(IMSMQQueueInfo3 *This,VARIANT varSecurity);
      HRESULT (WINAPI *get_IsTransactional2)(IMSMQQueueInfo3 *This,VARIANT_BOOL *pisTransactional);
      HRESULT (WINAPI *get_IsWorldReadable2)(IMSMQQueueInfo3 *This,VARIANT_BOOL *pisWorldReadable);
      HRESULT (WINAPI *get_MulticastAddress)(IMSMQQueueInfo3 *This,BSTR *pbstrMulticastAddress);
      HRESULT (WINAPI *put_MulticastAddress)(IMSMQQueueInfo3 *This,BSTR bstrMulticastAddress);
      HRESULT (WINAPI *get_ADsPath)(IMSMQQueueInfo3 *This,BSTR *pbstrADsPath);
    END_INTERFACE
  } IMSMQQueueInfo3Vtbl;
  struct IMSMQQueueInfo3 {
    CONST_VTBL struct IMSMQQueueInfo3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueueInfo3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueueInfo3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueueInfo3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueueInfo3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueueInfo3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueueInfo3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueueInfo3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueueInfo3_get_QueueGuid(This,pbstrGuidQueue) (This)->lpVtbl->get_QueueGuid(This,pbstrGuidQueue)
#define IMSMQQueueInfo3_get_ServiceTypeGuid(This,pbstrGuidServiceType) (This)->lpVtbl->get_ServiceTypeGuid(This,pbstrGuidServiceType)
#define IMSMQQueueInfo3_put_ServiceTypeGuid(This,bstrGuidServiceType) (This)->lpVtbl->put_ServiceTypeGuid(This,bstrGuidServiceType)
#define IMSMQQueueInfo3_get_Label(This,pbstrLabel) (This)->lpVtbl->get_Label(This,pbstrLabel)
#define IMSMQQueueInfo3_put_Label(This,bstrLabel) (This)->lpVtbl->put_Label(This,bstrLabel)
#define IMSMQQueueInfo3_get_PathName(This,pbstrPathName) (This)->lpVtbl->get_PathName(This,pbstrPathName)
#define IMSMQQueueInfo3_put_PathName(This,bstrPathName) (This)->lpVtbl->put_PathName(This,bstrPathName)
#define IMSMQQueueInfo3_get_FormatName(This,pbstrFormatName) (This)->lpVtbl->get_FormatName(This,pbstrFormatName)
#define IMSMQQueueInfo3_put_FormatName(This,bstrFormatName) (This)->lpVtbl->put_FormatName(This,bstrFormatName)
#define IMSMQQueueInfo3_get_IsTransactional(This,pisTransactional) (This)->lpVtbl->get_IsTransactional(This,pisTransactional)
#define IMSMQQueueInfo3_get_PrivLevel(This,plPrivLevel) (This)->lpVtbl->get_PrivLevel(This,plPrivLevel)
#define IMSMQQueueInfo3_put_PrivLevel(This,lPrivLevel) (This)->lpVtbl->put_PrivLevel(This,lPrivLevel)
#define IMSMQQueueInfo3_get_Journal(This,plJournal) (This)->lpVtbl->get_Journal(This,plJournal)
#define IMSMQQueueInfo3_put_Journal(This,lJournal) (This)->lpVtbl->put_Journal(This,lJournal)
#define IMSMQQueueInfo3_get_Quota(This,plQuota) (This)->lpVtbl->get_Quota(This,plQuota)
#define IMSMQQueueInfo3_put_Quota(This,lQuota) (This)->lpVtbl->put_Quota(This,lQuota)
#define IMSMQQueueInfo3_get_BasePriority(This,plBasePriority) (This)->lpVtbl->get_BasePriority(This,plBasePriority)
#define IMSMQQueueInfo3_put_BasePriority(This,lBasePriority) (This)->lpVtbl->put_BasePriority(This,lBasePriority)
#define IMSMQQueueInfo3_get_CreateTime(This,pvarCreateTime) (This)->lpVtbl->get_CreateTime(This,pvarCreateTime)
#define IMSMQQueueInfo3_get_ModifyTime(This,pvarModifyTime) (This)->lpVtbl->get_ModifyTime(This,pvarModifyTime)
#define IMSMQQueueInfo3_get_Authenticate(This,plAuthenticate) (This)->lpVtbl->get_Authenticate(This,plAuthenticate)
#define IMSMQQueueInfo3_put_Authenticate(This,lAuthenticate) (This)->lpVtbl->put_Authenticate(This,lAuthenticate)
#define IMSMQQueueInfo3_get_JournalQuota(This,plJournalQuota) (This)->lpVtbl->get_JournalQuota(This,plJournalQuota)
#define IMSMQQueueInfo3_put_JournalQuota(This,lJournalQuota) (This)->lpVtbl->put_JournalQuota(This,lJournalQuota)
#define IMSMQQueueInfo3_get_IsWorldReadable(This,pisWorldReadable) (This)->lpVtbl->get_IsWorldReadable(This,pisWorldReadable)
#define IMSMQQueueInfo3_Create(This,IsTransactional,IsWorldReadable) (This)->lpVtbl->Create(This,IsTransactional,IsWorldReadable)
#define IMSMQQueueInfo3_Delete(This) (This)->lpVtbl->Delete(This)
#define IMSMQQueueInfo3_Open(This,Access,ShareMode,ppq) (This)->lpVtbl->Open(This,Access,ShareMode,ppq)
#define IMSMQQueueInfo3_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IMSMQQueueInfo3_Update(This) (This)->lpVtbl->Update(This)
#define IMSMQQueueInfo3_get_PathNameDNS(This,pbstrPathNameDNS) (This)->lpVtbl->get_PathNameDNS(This,pbstrPathNameDNS)
#define IMSMQQueueInfo3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#define IMSMQQueueInfo3_get_Security(This,pvarSecurity) (This)->lpVtbl->get_Security(This,pvarSecurity)
#define IMSMQQueueInfo3_put_Security(This,varSecurity) (This)->lpVtbl->put_Security(This,varSecurity)
#define IMSMQQueueInfo3_get_IsTransactional2(This,pisTransactional) (This)->lpVtbl->get_IsTransactional2(This,pisTransactional)
#define IMSMQQueueInfo3_get_IsWorldReadable2(This,pisWorldReadable) (This)->lpVtbl->get_IsWorldReadable2(This,pisWorldReadable)
#define IMSMQQueueInfo3_get_MulticastAddress(This,pbstrMulticastAddress) (This)->lpVtbl->get_MulticastAddress(This,pbstrMulticastAddress)
#define IMSMQQueueInfo3_put_MulticastAddress(This,bstrMulticastAddress) (This)->lpVtbl->put_MulticastAddress(This,bstrMulticastAddress)
#define IMSMQQueueInfo3_get_ADsPath(This,pbstrADsPath) (This)->lpVtbl->get_ADsPath(This,pbstrADsPath)
#endif
#endif
  HRESULT WINAPI IMSMQQueueInfo3_get_QueueGuid_Proxy(IMSMQQueueInfo3 *This,BSTR *pbstrGuidQueue);
  void __RPC_STUB IMSMQQueueInfo3_get_QueueGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_ServiceTypeGuid_Proxy(IMSMQQueueInfo3 *This,BSTR *pbstrGuidServiceType);
  void __RPC_STUB IMSMQQueueInfo3_get_ServiceTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_ServiceTypeGuid_Proxy(IMSMQQueueInfo3 *This,BSTR bstrGuidServiceType);
  void __RPC_STUB IMSMQQueueInfo3_put_ServiceTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_Label_Proxy(IMSMQQueueInfo3 *This,BSTR *pbstrLabel);
  void __RPC_STUB IMSMQQueueInfo3_get_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_Label_Proxy(IMSMQQueueInfo3 *This,BSTR bstrLabel);
  void __RPC_STUB IMSMQQueueInfo3_put_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_PathName_Proxy(IMSMQQueueInfo3 *This,BSTR *pbstrPathName);
  void __RPC_STUB IMSMQQueueInfo3_get_PathName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_PathName_Proxy(IMSMQQueueInfo3 *This,BSTR bstrPathName);
  void __RPC_STUB IMSMQQueueInfo3_put_PathName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_FormatName_Proxy(IMSMQQueueInfo3 *This,BSTR *pbstrFormatName);
  void __RPC_STUB IMSMQQueueInfo3_get_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_FormatName_Proxy(IMSMQQueueInfo3 *This,BSTR bstrFormatName);
  void __RPC_STUB IMSMQQueueInfo3_put_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_IsTransactional_Proxy(IMSMQQueueInfo3 *This,Boolean *pisTransactional);
  void __RPC_STUB IMSMQQueueInfo3_get_IsTransactional_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_PrivLevel_Proxy(IMSMQQueueInfo3 *This,__LONG32 *plPrivLevel);
  void __RPC_STUB IMSMQQueueInfo3_get_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_PrivLevel_Proxy(IMSMQQueueInfo3 *This,__LONG32 lPrivLevel);
  void __RPC_STUB IMSMQQueueInfo3_put_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_Journal_Proxy(IMSMQQueueInfo3 *This,__LONG32 *plJournal);
  void __RPC_STUB IMSMQQueueInfo3_get_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_Journal_Proxy(IMSMQQueueInfo3 *This,__LONG32 lJournal);
  void __RPC_STUB IMSMQQueueInfo3_put_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_Quota_Proxy(IMSMQQueueInfo3 *This,__LONG32 *plQuota);
  void __RPC_STUB IMSMQQueueInfo3_get_Quota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_Quota_Proxy(IMSMQQueueInfo3 *This,__LONG32 lQuota);
  void __RPC_STUB IMSMQQueueInfo3_put_Quota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_BasePriority_Proxy(IMSMQQueueInfo3 *This,__LONG32 *plBasePriority);
  void __RPC_STUB IMSMQQueueInfo3_get_BasePriority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_BasePriority_Proxy(IMSMQQueueInfo3 *This,__LONG32 lBasePriority);
  void __RPC_STUB IMSMQQueueInfo3_put_BasePriority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_CreateTime_Proxy(IMSMQQueueInfo3 *This,VARIANT *pvarCreateTime);
  void __RPC_STUB IMSMQQueueInfo3_get_CreateTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_ModifyTime_Proxy(IMSMQQueueInfo3 *This,VARIANT *pvarModifyTime);
  void __RPC_STUB IMSMQQueueInfo3_get_ModifyTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_Authenticate_Proxy(IMSMQQueueInfo3 *This,__LONG32 *plAuthenticate);
  void __RPC_STUB IMSMQQueueInfo3_get_Authenticate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_Authenticate_Proxy(IMSMQQueueInfo3 *This,__LONG32 lAuthenticate);
  void __RPC_STUB IMSMQQueueInfo3_put_Authenticate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_JournalQuota_Proxy(IMSMQQueueInfo3 *This,__LONG32 *plJournalQuota);
  void __RPC_STUB IMSMQQueueInfo3_get_JournalQuota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_JournalQuota_Proxy(IMSMQQueueInfo3 *This,__LONG32 lJournalQuota);
  void __RPC_STUB IMSMQQueueInfo3_put_JournalQuota_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_IsWorldReadable_Proxy(IMSMQQueueInfo3 *This,Boolean *pisWorldReadable);
  void __RPC_STUB IMSMQQueueInfo3_get_IsWorldReadable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_Create_Proxy(IMSMQQueueInfo3 *This,VARIANT *IsTransactional,VARIANT *IsWorldReadable);
  void __RPC_STUB IMSMQQueueInfo3_Create_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_Delete_Proxy(IMSMQQueueInfo3 *This);
  void __RPC_STUB IMSMQQueueInfo3_Delete_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_Open_Proxy(IMSMQQueueInfo3 *This,__LONG32 Access,__LONG32 ShareMode,IMSMQQueue3 **ppq);
  void __RPC_STUB IMSMQQueueInfo3_Open_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_Refresh_Proxy(IMSMQQueueInfo3 *This);
  void __RPC_STUB IMSMQQueueInfo3_Refresh_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_Update_Proxy(IMSMQQueueInfo3 *This);
  void __RPC_STUB IMSMQQueueInfo3_Update_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_PathNameDNS_Proxy(IMSMQQueueInfo3 *This,BSTR *pbstrPathNameDNS);
  void __RPC_STUB IMSMQQueueInfo3_get_PathNameDNS_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_Properties_Proxy(IMSMQQueueInfo3 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQQueueInfo3_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_Security_Proxy(IMSMQQueueInfo3 *This,VARIANT *pvarSecurity);
  void __RPC_STUB IMSMQQueueInfo3_get_Security_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_Security_Proxy(IMSMQQueueInfo3 *This,VARIANT varSecurity);
  void __RPC_STUB IMSMQQueueInfo3_put_Security_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_IsTransactional2_Proxy(IMSMQQueueInfo3 *This,VARIANT_BOOL *pisTransactional);
  void __RPC_STUB IMSMQQueueInfo3_get_IsTransactional2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_IsWorldReadable2_Proxy(IMSMQQueueInfo3 *This,VARIANT_BOOL *pisWorldReadable);
  void __RPC_STUB IMSMQQueueInfo3_get_IsWorldReadable2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_MulticastAddress_Proxy(IMSMQQueueInfo3 *This,BSTR *pbstrMulticastAddress);
  void __RPC_STUB IMSMQQueueInfo3_get_MulticastAddress_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_put_MulticastAddress_Proxy(IMSMQQueueInfo3 *This,BSTR bstrMulticastAddress);
  void __RPC_STUB IMSMQQueueInfo3_put_MulticastAddress_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfo3_get_ADsPath_Proxy(IMSMQQueueInfo3 *This,BSTR *pbstrADsPath);
  void __RPC_STUB IMSMQQueueInfo3_get_ADsPath_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQueue_INTERFACE_DEFINED__
#define __IMSMQQueue_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueue;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueue : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Access(__LONG32 *plAccess) = 0;
    virtual HRESULT WINAPI get_ShareMode(__LONG32 *plShareMode) = 0;
    virtual HRESULT WINAPI get_QueueInfo(IMSMQQueueInfo **ppqinfo) = 0;
    virtual HRESULT WINAPI get_Handle(__LONG32 *plHandle) = 0;
    virtual HRESULT WINAPI get_IsOpen(Boolean *pisOpen) = 0;
    virtual HRESULT WINAPI Close(void) = 0;
    virtual HRESULT WINAPI Receive(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI Peek(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI EnableNotification(IMSMQEvent *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout) = 0;
    virtual HRESULT WINAPI Reset(void) = 0;
    virtual HRESULT WINAPI ReceiveCurrent(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI PeekNext(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI PeekCurrent(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
  };
#else
  typedef struct IMSMQQueueVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueue *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueue *This);
      ULONG (WINAPI *Release)(IMSMQQueue *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueue *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueue *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueue *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueue *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Access)(IMSMQQueue *This,__LONG32 *plAccess);
      HRESULT (WINAPI *get_ShareMode)(IMSMQQueue *This,__LONG32 *plShareMode);
      HRESULT (WINAPI *get_QueueInfo)(IMSMQQueue *This,IMSMQQueueInfo **ppqinfo);
      HRESULT (WINAPI *get_Handle)(IMSMQQueue *This,__LONG32 *plHandle);
      HRESULT (WINAPI *get_IsOpen)(IMSMQQueue *This,Boolean *pisOpen);
      HRESULT (WINAPI *Close)(IMSMQQueue *This);
      HRESULT (WINAPI *Receive)(IMSMQQueue *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *Peek)(IMSMQQueue *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *EnableNotification)(IMSMQQueue *This,IMSMQEvent *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout);
      HRESULT (WINAPI *Reset)(IMSMQQueue *This);
      HRESULT (WINAPI *ReceiveCurrent)(IMSMQQueue *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *PeekNext)(IMSMQQueue *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *PeekCurrent)(IMSMQQueue *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
    END_INTERFACE
  } IMSMQQueueVtbl;
  struct IMSMQQueue {
    CONST_VTBL struct IMSMQQueueVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueue_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueue_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueue_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueue_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueue_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueue_get_Access(This,plAccess) (This)->lpVtbl->get_Access(This,plAccess)
#define IMSMQQueue_get_ShareMode(This,plShareMode) (This)->lpVtbl->get_ShareMode(This,plShareMode)
#define IMSMQQueue_get_QueueInfo(This,ppqinfo) (This)->lpVtbl->get_QueueInfo(This,ppqinfo)
#define IMSMQQueue_get_Handle(This,plHandle) (This)->lpVtbl->get_Handle(This,plHandle)
#define IMSMQQueue_get_IsOpen(This,pisOpen) (This)->lpVtbl->get_IsOpen(This,pisOpen)
#define IMSMQQueue_Close(This) (This)->lpVtbl->Close(This)
#define IMSMQQueue_Receive(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->Receive(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue_Peek(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->Peek(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue_EnableNotification(This,Event,Cursor,ReceiveTimeout) (This)->lpVtbl->EnableNotification(This,Event,Cursor,ReceiveTimeout)
#define IMSMQQueue_Reset(This) (This)->lpVtbl->Reset(This)
#define IMSMQQueue_ReceiveCurrent(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->ReceiveCurrent(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue_PeekNext(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->PeekNext(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue_PeekCurrent(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->PeekCurrent(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#endif
#endif
  HRESULT WINAPI IMSMQQueue_get_Access_Proxy(IMSMQQueue *This,__LONG32 *plAccess);
  void __RPC_STUB IMSMQQueue_get_Access_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_get_ShareMode_Proxy(IMSMQQueue *This,__LONG32 *plShareMode);
  void __RPC_STUB IMSMQQueue_get_ShareMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_get_QueueInfo_Proxy(IMSMQQueue *This,IMSMQQueueInfo **ppqinfo);
  void __RPC_STUB IMSMQQueue_get_QueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_get_Handle_Proxy(IMSMQQueue *This,__LONG32 *plHandle);
  void __RPC_STUB IMSMQQueue_get_Handle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_get_IsOpen_Proxy(IMSMQQueue *This,Boolean *pisOpen);
  void __RPC_STUB IMSMQQueue_get_IsOpen_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_Close_Proxy(IMSMQQueue *This);
  void __RPC_STUB IMSMQQueue_Close_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_Receive_Proxy(IMSMQQueue *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue_Receive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_Peek_Proxy(IMSMQQueue *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue_Peek_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_EnableNotification_Proxy(IMSMQQueue *This,IMSMQEvent *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout);
  void __RPC_STUB IMSMQQueue_EnableNotification_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_Reset_Proxy(IMSMQQueue *This);
  void __RPC_STUB IMSMQQueue_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_ReceiveCurrent_Proxy(IMSMQQueue *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue_ReceiveCurrent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_PeekNext_Proxy(IMSMQQueue *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue_PeekNext_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue_PeekCurrent_Proxy(IMSMQQueue *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue_PeekCurrent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQueue2_INTERFACE_DEFINED__
#define __IMSMQQueue2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueue2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueue2 : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Access(__LONG32 *plAccess) = 0;
    virtual HRESULT WINAPI get_ShareMode(__LONG32 *plShareMode) = 0;
    virtual HRESULT WINAPI get_QueueInfo(IMSMQQueueInfo2 **ppqinfo) = 0;
    virtual HRESULT WINAPI get_Handle(__LONG32 *plHandle) = 0;
    virtual HRESULT WINAPI get_IsOpen(Boolean *pisOpen) = 0;
    virtual HRESULT WINAPI Close(void) = 0;
    virtual HRESULT WINAPI Receive_v1(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI Peek_v1(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI EnableNotification(IMSMQEvent2 *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout) = 0;
    virtual HRESULT WINAPI Reset(void) = 0;
    virtual HRESULT WINAPI ReceiveCurrent_v1(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI PeekNext_v1(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI PeekCurrent_v1(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI Receive(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg) = 0;
    virtual HRESULT WINAPI Peek(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg) = 0;
    virtual HRESULT WINAPI ReceiveCurrent(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekNext(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekCurrent(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQQueue2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueue2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueue2 *This);
      ULONG (WINAPI *Release)(IMSMQQueue2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueue2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueue2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueue2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueue2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Access)(IMSMQQueue2 *This,__LONG32 *plAccess);
      HRESULT (WINAPI *get_ShareMode)(IMSMQQueue2 *This,__LONG32 *plShareMode);
      HRESULT (WINAPI *get_QueueInfo)(IMSMQQueue2 *This,IMSMQQueueInfo2 **ppqinfo);
      HRESULT (WINAPI *get_Handle)(IMSMQQueue2 *This,__LONG32 *plHandle);
      HRESULT (WINAPI *get_IsOpen)(IMSMQQueue2 *This,Boolean *pisOpen);
      HRESULT (WINAPI *Close)(IMSMQQueue2 *This);
      HRESULT (WINAPI *Receive_v1)(IMSMQQueue2 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *Peek_v1)(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *EnableNotification)(IMSMQQueue2 *This,IMSMQEvent2 *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout);
      HRESULT (WINAPI *Reset)(IMSMQQueue2 *This);
      HRESULT (WINAPI *ReceiveCurrent_v1)(IMSMQQueue2 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *PeekNext_v1)(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *PeekCurrent_v1)(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *Receive)(IMSMQQueue2 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
      HRESULT (WINAPI *Peek)(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
      HRESULT (WINAPI *ReceiveCurrent)(IMSMQQueue2 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
      HRESULT (WINAPI *PeekNext)(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
      HRESULT (WINAPI *PeekCurrent)(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
      HRESULT (WINAPI *get_Properties)(IMSMQQueue2 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQQueue2Vtbl;
  struct IMSMQQueue2 {
    CONST_VTBL struct IMSMQQueue2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueue2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueue2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueue2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueue2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueue2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueue2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueue2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueue2_get_Access(This,plAccess) (This)->lpVtbl->get_Access(This,plAccess)
#define IMSMQQueue2_get_ShareMode(This,plShareMode) (This)->lpVtbl->get_ShareMode(This,plShareMode)
#define IMSMQQueue2_get_QueueInfo(This,ppqinfo) (This)->lpVtbl->get_QueueInfo(This,ppqinfo)
#define IMSMQQueue2_get_Handle(This,plHandle) (This)->lpVtbl->get_Handle(This,plHandle)
#define IMSMQQueue2_get_IsOpen(This,pisOpen) (This)->lpVtbl->get_IsOpen(This,pisOpen)
#define IMSMQQueue2_Close(This) (This)->lpVtbl->Close(This)
#define IMSMQQueue2_Receive_v1(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->Receive_v1(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue2_Peek_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->Peek_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue2_EnableNotification(This,Event,Cursor,ReceiveTimeout) (This)->lpVtbl->EnableNotification(This,Event,Cursor,ReceiveTimeout)
#define IMSMQQueue2_Reset(This) (This)->lpVtbl->Reset(This)
#define IMSMQQueue2_ReceiveCurrent_v1(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->ReceiveCurrent_v1(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue2_PeekNext_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->PeekNext_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue2_PeekCurrent_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->PeekCurrent_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue2_Receive(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->Receive(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue2_Peek(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->Peek(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue2_ReceiveCurrent(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->ReceiveCurrent(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue2_PeekNext(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->PeekNext(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue2_PeekCurrent(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->PeekCurrent(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQQueue2_get_Access_Proxy(IMSMQQueue2 *This,__LONG32 *plAccess);
  void __RPC_STUB IMSMQQueue2_get_Access_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_get_ShareMode_Proxy(IMSMQQueue2 *This,__LONG32 *plShareMode);
  void __RPC_STUB IMSMQQueue2_get_ShareMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_get_QueueInfo_Proxy(IMSMQQueue2 *This,IMSMQQueueInfo2 **ppqinfo);
  void __RPC_STUB IMSMQQueue2_get_QueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_get_Handle_Proxy(IMSMQQueue2 *This,__LONG32 *plHandle);
  void __RPC_STUB IMSMQQueue2_get_Handle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_get_IsOpen_Proxy(IMSMQQueue2 *This,Boolean *pisOpen);
  void __RPC_STUB IMSMQQueue2_get_IsOpen_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_Close_Proxy(IMSMQQueue2 *This);
  void __RPC_STUB IMSMQQueue2_Close_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_Receive_v1_Proxy(IMSMQQueue2 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue2_Receive_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_Peek_v1_Proxy(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue2_Peek_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_EnableNotification_Proxy(IMSMQQueue2 *This,IMSMQEvent2 *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout);
  void __RPC_STUB IMSMQQueue2_EnableNotification_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_Reset_Proxy(IMSMQQueue2 *This);
  void __RPC_STUB IMSMQQueue2_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_ReceiveCurrent_v1_Proxy(IMSMQQueue2 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue2_ReceiveCurrent_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_PeekNext_v1_Proxy(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue2_PeekNext_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_PeekCurrent_v1_Proxy(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue2_PeekCurrent_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_Receive_Proxy(IMSMQQueue2 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
  void __RPC_STUB IMSMQQueue2_Receive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_Peek_Proxy(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
  void __RPC_STUB IMSMQQueue2_Peek_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_ReceiveCurrent_Proxy(IMSMQQueue2 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
  void __RPC_STUB IMSMQQueue2_ReceiveCurrent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_PeekNext_Proxy(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
  void __RPC_STUB IMSMQQueue2_PeekNext_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_PeekCurrent_Proxy(IMSMQQueue2 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage2 **ppmsg);
  void __RPC_STUB IMSMQQueue2_PeekCurrent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue2_get_Properties_Proxy(IMSMQQueue2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQQueue2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQMessage_INTERFACE_DEFINED__
#define __IMSMQMessage_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQMessage;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQMessage : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Class(__LONG32 *plClass) = 0;
    virtual HRESULT WINAPI get_PrivLevel(__LONG32 *plPrivLevel) = 0;
    virtual HRESULT WINAPI put_PrivLevel(__LONG32 lPrivLevel) = 0;
    virtual HRESULT WINAPI get_AuthLevel(__LONG32 *plAuthLevel) = 0;
    virtual HRESULT WINAPI put_AuthLevel(__LONG32 lAuthLevel) = 0;
    virtual HRESULT WINAPI get_IsAuthenticated(Boolean *pisAuthenticated) = 0;
    virtual HRESULT WINAPI get_Delivery(__LONG32 *plDelivery) = 0;
    virtual HRESULT WINAPI put_Delivery(__LONG32 lDelivery) = 0;
    virtual HRESULT WINAPI get_Trace(__LONG32 *plTrace) = 0;
    virtual HRESULT WINAPI put_Trace(__LONG32 lTrace) = 0;
    virtual HRESULT WINAPI get_Priority(__LONG32 *plPriority) = 0;
    virtual HRESULT WINAPI put_Priority(__LONG32 lPriority) = 0;
    virtual HRESULT WINAPI get_Journal(__LONG32 *plJournal) = 0;
    virtual HRESULT WINAPI put_Journal(__LONG32 lJournal) = 0;
    virtual HRESULT WINAPI get_ResponseQueueInfo(IMSMQQueueInfo **ppqinfoResponse) = 0;
    virtual HRESULT WINAPI putref_ResponseQueueInfo(IMSMQQueueInfo *pqinfoResponse) = 0;
    virtual HRESULT WINAPI get_AppSpecific(__LONG32 *plAppSpecific) = 0;
    virtual HRESULT WINAPI put_AppSpecific(__LONG32 lAppSpecific) = 0;
    virtual HRESULT WINAPI get_SourceMachineGuid(BSTR *pbstrGuidSrcMachine) = 0;
    virtual HRESULT WINAPI get_BodyLength(__LONG32 *pcbBody) = 0;
    virtual HRESULT WINAPI get_Body(VARIANT *pvarBody) = 0;
    virtual HRESULT WINAPI put_Body(VARIANT varBody) = 0;
    virtual HRESULT WINAPI get_AdminQueueInfo(IMSMQQueueInfo **ppqinfoAdmin) = 0;
    virtual HRESULT WINAPI putref_AdminQueueInfo(IMSMQQueueInfo *pqinfoAdmin) = 0;
    virtual HRESULT WINAPI get_Id(VARIANT *pvarMsgId) = 0;
    virtual HRESULT WINAPI get_CorrelationId(VARIANT *pvarMsgId) = 0;
    virtual HRESULT WINAPI put_CorrelationId(VARIANT varMsgId) = 0;
    virtual HRESULT WINAPI get_Ack(__LONG32 *plAck) = 0;
    virtual HRESULT WINAPI put_Ack(__LONG32 lAck) = 0;
    virtual HRESULT WINAPI get_Label(BSTR *pbstrLabel) = 0;
    virtual HRESULT WINAPI put_Label(BSTR bstrLabel) = 0;
    virtual HRESULT WINAPI get_MaxTimeToReachQueue(__LONG32 *plMaxTimeToReachQueue) = 0;
    virtual HRESULT WINAPI put_MaxTimeToReachQueue(__LONG32 lMaxTimeToReachQueue) = 0;
    virtual HRESULT WINAPI get_MaxTimeToReceive(__LONG32 *plMaxTimeToReceive) = 0;
    virtual HRESULT WINAPI put_MaxTimeToReceive(__LONG32 lMaxTimeToReceive) = 0;
    virtual HRESULT WINAPI get_HashAlgorithm(__LONG32 *plHashAlg) = 0;
    virtual HRESULT WINAPI put_HashAlgorithm(__LONG32 lHashAlg) = 0;
    virtual HRESULT WINAPI get_EncryptAlgorithm(__LONG32 *plEncryptAlg) = 0;
    virtual HRESULT WINAPI put_EncryptAlgorithm(__LONG32 lEncryptAlg) = 0;
    virtual HRESULT WINAPI get_SentTime(VARIANT *pvarSentTime) = 0;
    virtual HRESULT WINAPI get_ArrivedTime(VARIANT *plArrivedTime) = 0;
    virtual HRESULT WINAPI get_DestinationQueueInfo(IMSMQQueueInfo **ppqinfoDest) = 0;
    virtual HRESULT WINAPI get_SenderCertificate(VARIANT *pvarSenderCert) = 0;
    virtual HRESULT WINAPI put_SenderCertificate(VARIANT varSenderCert) = 0;
    virtual HRESULT WINAPI get_SenderId(VARIANT *pvarSenderId) = 0;
    virtual HRESULT WINAPI get_SenderIdType(__LONG32 *plSenderIdType) = 0;
    virtual HRESULT WINAPI put_SenderIdType(__LONG32 lSenderIdType) = 0;
    virtual HRESULT WINAPI Send(IMSMQQueue *DestinationQueue,VARIANT *Transaction) = 0;
    virtual HRESULT WINAPI AttachCurrentSecurityContext(void) = 0;
  };
#else
  typedef struct IMSMQMessageVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQMessage *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQMessage *This);
      ULONG (WINAPI *Release)(IMSMQMessage *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQMessage *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQMessage *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQMessage *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQMessage *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Class)(IMSMQMessage *This,__LONG32 *plClass);
      HRESULT (WINAPI *get_PrivLevel)(IMSMQMessage *This,__LONG32 *plPrivLevel);
      HRESULT (WINAPI *put_PrivLevel)(IMSMQMessage *This,__LONG32 lPrivLevel);
      HRESULT (WINAPI *get_AuthLevel)(IMSMQMessage *This,__LONG32 *plAuthLevel);
      HRESULT (WINAPI *put_AuthLevel)(IMSMQMessage *This,__LONG32 lAuthLevel);
      HRESULT (WINAPI *get_IsAuthenticated)(IMSMQMessage *This,Boolean *pisAuthenticated);
      HRESULT (WINAPI *get_Delivery)(IMSMQMessage *This,__LONG32 *plDelivery);
      HRESULT (WINAPI *put_Delivery)(IMSMQMessage *This,__LONG32 lDelivery);
      HRESULT (WINAPI *get_Trace)(IMSMQMessage *This,__LONG32 *plTrace);
      HRESULT (WINAPI *put_Trace)(IMSMQMessage *This,__LONG32 lTrace);
      HRESULT (WINAPI *get_Priority)(IMSMQMessage *This,__LONG32 *plPriority);
      HRESULT (WINAPI *put_Priority)(IMSMQMessage *This,__LONG32 lPriority);
      HRESULT (WINAPI *get_Journal)(IMSMQMessage *This,__LONG32 *plJournal);
      HRESULT (WINAPI *put_Journal)(IMSMQMessage *This,__LONG32 lJournal);
      HRESULT (WINAPI *get_ResponseQueueInfo)(IMSMQMessage *This,IMSMQQueueInfo **ppqinfoResponse);
      HRESULT (WINAPI *putref_ResponseQueueInfo)(IMSMQMessage *This,IMSMQQueueInfo *pqinfoResponse);
      HRESULT (WINAPI *get_AppSpecific)(IMSMQMessage *This,__LONG32 *plAppSpecific);
      HRESULT (WINAPI *put_AppSpecific)(IMSMQMessage *This,__LONG32 lAppSpecific);
      HRESULT (WINAPI *get_SourceMachineGuid)(IMSMQMessage *This,BSTR *pbstrGuidSrcMachine);
      HRESULT (WINAPI *get_BodyLength)(IMSMQMessage *This,__LONG32 *pcbBody);
      HRESULT (WINAPI *get_Body)(IMSMQMessage *This,VARIANT *pvarBody);
      HRESULT (WINAPI *put_Body)(IMSMQMessage *This,VARIANT varBody);
      HRESULT (WINAPI *get_AdminQueueInfo)(IMSMQMessage *This,IMSMQQueueInfo **ppqinfoAdmin);
      HRESULT (WINAPI *putref_AdminQueueInfo)(IMSMQMessage *This,IMSMQQueueInfo *pqinfoAdmin);
      HRESULT (WINAPI *get_Id)(IMSMQMessage *This,VARIANT *pvarMsgId);
      HRESULT (WINAPI *get_CorrelationId)(IMSMQMessage *This,VARIANT *pvarMsgId);
      HRESULT (WINAPI *put_CorrelationId)(IMSMQMessage *This,VARIANT varMsgId);
      HRESULT (WINAPI *get_Ack)(IMSMQMessage *This,__LONG32 *plAck);
      HRESULT (WINAPI *put_Ack)(IMSMQMessage *This,__LONG32 lAck);
      HRESULT (WINAPI *get_Label)(IMSMQMessage *This,BSTR *pbstrLabel);
      HRESULT (WINAPI *put_Label)(IMSMQMessage *This,BSTR bstrLabel);
      HRESULT (WINAPI *get_MaxTimeToReachQueue)(IMSMQMessage *This,__LONG32 *plMaxTimeToReachQueue);
      HRESULT (WINAPI *put_MaxTimeToReachQueue)(IMSMQMessage *This,__LONG32 lMaxTimeToReachQueue);
      HRESULT (WINAPI *get_MaxTimeToReceive)(IMSMQMessage *This,__LONG32 *plMaxTimeToReceive);
      HRESULT (WINAPI *put_MaxTimeToReceive)(IMSMQMessage *This,__LONG32 lMaxTimeToReceive);
      HRESULT (WINAPI *get_HashAlgorithm)(IMSMQMessage *This,__LONG32 *plHashAlg);
      HRESULT (WINAPI *put_HashAlgorithm)(IMSMQMessage *This,__LONG32 lHashAlg);
      HRESULT (WINAPI *get_EncryptAlgorithm)(IMSMQMessage *This,__LONG32 *plEncryptAlg);
      HRESULT (WINAPI *put_EncryptAlgorithm)(IMSMQMessage *This,__LONG32 lEncryptAlg);
      HRESULT (WINAPI *get_SentTime)(IMSMQMessage *This,VARIANT *pvarSentTime);
      HRESULT (WINAPI *get_ArrivedTime)(IMSMQMessage *This,VARIANT *plArrivedTime);
      HRESULT (WINAPI *get_DestinationQueueInfo)(IMSMQMessage *This,IMSMQQueueInfo **ppqinfoDest);
      HRESULT (WINAPI *get_SenderCertificate)(IMSMQMessage *This,VARIANT *pvarSenderCert);
      HRESULT (WINAPI *put_SenderCertificate)(IMSMQMessage *This,VARIANT varSenderCert);
      HRESULT (WINAPI *get_SenderId)(IMSMQMessage *This,VARIANT *pvarSenderId);
      HRESULT (WINAPI *get_SenderIdType)(IMSMQMessage *This,__LONG32 *plSenderIdType);
      HRESULT (WINAPI *put_SenderIdType)(IMSMQMessage *This,__LONG32 lSenderIdType);
      HRESULT (WINAPI *Send)(IMSMQMessage *This,IMSMQQueue *DestinationQueue,VARIANT *Transaction);
      HRESULT (WINAPI *AttachCurrentSecurityContext)(IMSMQMessage *This);
    END_INTERFACE
  } IMSMQMessageVtbl;
  struct IMSMQMessage {
    CONST_VTBL struct IMSMQMessageVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQMessage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQMessage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQMessage_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQMessage_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQMessage_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQMessage_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQMessage_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQMessage_get_Class(This,plClass) (This)->lpVtbl->get_Class(This,plClass)
#define IMSMQMessage_get_PrivLevel(This,plPrivLevel) (This)->lpVtbl->get_PrivLevel(This,plPrivLevel)
#define IMSMQMessage_put_PrivLevel(This,lPrivLevel) (This)->lpVtbl->put_PrivLevel(This,lPrivLevel)
#define IMSMQMessage_get_AuthLevel(This,plAuthLevel) (This)->lpVtbl->get_AuthLevel(This,plAuthLevel)
#define IMSMQMessage_put_AuthLevel(This,lAuthLevel) (This)->lpVtbl->put_AuthLevel(This,lAuthLevel)
#define IMSMQMessage_get_IsAuthenticated(This,pisAuthenticated) (This)->lpVtbl->get_IsAuthenticated(This,pisAuthenticated)
#define IMSMQMessage_get_Delivery(This,plDelivery) (This)->lpVtbl->get_Delivery(This,plDelivery)
#define IMSMQMessage_put_Delivery(This,lDelivery) (This)->lpVtbl->put_Delivery(This,lDelivery)
#define IMSMQMessage_get_Trace(This,plTrace) (This)->lpVtbl->get_Trace(This,plTrace)
#define IMSMQMessage_put_Trace(This,lTrace) (This)->lpVtbl->put_Trace(This,lTrace)
#define IMSMQMessage_get_Priority(This,plPriority) (This)->lpVtbl->get_Priority(This,plPriority)
#define IMSMQMessage_put_Priority(This,lPriority) (This)->lpVtbl->put_Priority(This,lPriority)
#define IMSMQMessage_get_Journal(This,plJournal) (This)->lpVtbl->get_Journal(This,plJournal)
#define IMSMQMessage_put_Journal(This,lJournal) (This)->lpVtbl->put_Journal(This,lJournal)
#define IMSMQMessage_get_ResponseQueueInfo(This,ppqinfoResponse) (This)->lpVtbl->get_ResponseQueueInfo(This,ppqinfoResponse)
#define IMSMQMessage_putref_ResponseQueueInfo(This,pqinfoResponse) (This)->lpVtbl->putref_ResponseQueueInfo(This,pqinfoResponse)
#define IMSMQMessage_get_AppSpecific(This,plAppSpecific) (This)->lpVtbl->get_AppSpecific(This,plAppSpecific)
#define IMSMQMessage_put_AppSpecific(This,lAppSpecific) (This)->lpVtbl->put_AppSpecific(This,lAppSpecific)
#define IMSMQMessage_get_SourceMachineGuid(This,pbstrGuidSrcMachine) (This)->lpVtbl->get_SourceMachineGuid(This,pbstrGuidSrcMachine)
#define IMSMQMessage_get_BodyLength(This,pcbBody) (This)->lpVtbl->get_BodyLength(This,pcbBody)
#define IMSMQMessage_get_Body(This,pvarBody) (This)->lpVtbl->get_Body(This,pvarBody)
#define IMSMQMessage_put_Body(This,varBody) (This)->lpVtbl->put_Body(This,varBody)
#define IMSMQMessage_get_AdminQueueInfo(This,ppqinfoAdmin) (This)->lpVtbl->get_AdminQueueInfo(This,ppqinfoAdmin)
#define IMSMQMessage_putref_AdminQueueInfo(This,pqinfoAdmin) (This)->lpVtbl->putref_AdminQueueInfo(This,pqinfoAdmin)
#define IMSMQMessage_get_Id(This,pvarMsgId) (This)->lpVtbl->get_Id(This,pvarMsgId)
#define IMSMQMessage_get_CorrelationId(This,pvarMsgId) (This)->lpVtbl->get_CorrelationId(This,pvarMsgId)
#define IMSMQMessage_put_CorrelationId(This,varMsgId) (This)->lpVtbl->put_CorrelationId(This,varMsgId)
#define IMSMQMessage_get_Ack(This,plAck) (This)->lpVtbl->get_Ack(This,plAck)
#define IMSMQMessage_put_Ack(This,lAck) (This)->lpVtbl->put_Ack(This,lAck)
#define IMSMQMessage_get_Label(This,pbstrLabel) (This)->lpVtbl->get_Label(This,pbstrLabel)
#define IMSMQMessage_put_Label(This,bstrLabel) (This)->lpVtbl->put_Label(This,bstrLabel)
#define IMSMQMessage_get_MaxTimeToReachQueue(This,plMaxTimeToReachQueue) (This)->lpVtbl->get_MaxTimeToReachQueue(This,plMaxTimeToReachQueue)
#define IMSMQMessage_put_MaxTimeToReachQueue(This,lMaxTimeToReachQueue) (This)->lpVtbl->put_MaxTimeToReachQueue(This,lMaxTimeToReachQueue)
#define IMSMQMessage_get_MaxTimeToReceive(This,plMaxTimeToReceive) (This)->lpVtbl->get_MaxTimeToReceive(This,plMaxTimeToReceive)
#define IMSMQMessage_put_MaxTimeToReceive(This,lMaxTimeToReceive) (This)->lpVtbl->put_MaxTimeToReceive(This,lMaxTimeToReceive)
#define IMSMQMessage_get_HashAlgorithm(This,plHashAlg) (This)->lpVtbl->get_HashAlgorithm(This,plHashAlg)
#define IMSMQMessage_put_HashAlgorithm(This,lHashAlg) (This)->lpVtbl->put_HashAlgorithm(This,lHashAlg)
#define IMSMQMessage_get_EncryptAlgorithm(This,plEncryptAlg) (This)->lpVtbl->get_EncryptAlgorithm(This,plEncryptAlg)
#define IMSMQMessage_put_EncryptAlgorithm(This,lEncryptAlg) (This)->lpVtbl->put_EncryptAlgorithm(This,lEncryptAlg)
#define IMSMQMessage_get_SentTime(This,pvarSentTime) (This)->lpVtbl->get_SentTime(This,pvarSentTime)
#define IMSMQMessage_get_ArrivedTime(This,plArrivedTime) (This)->lpVtbl->get_ArrivedTime(This,plArrivedTime)
#define IMSMQMessage_get_DestinationQueueInfo(This,ppqinfoDest) (This)->lpVtbl->get_DestinationQueueInfo(This,ppqinfoDest)
#define IMSMQMessage_get_SenderCertificate(This,pvarSenderCert) (This)->lpVtbl->get_SenderCertificate(This,pvarSenderCert)
#define IMSMQMessage_put_SenderCertificate(This,varSenderCert) (This)->lpVtbl->put_SenderCertificate(This,varSenderCert)
#define IMSMQMessage_get_SenderId(This,pvarSenderId) (This)->lpVtbl->get_SenderId(This,pvarSenderId)
#define IMSMQMessage_get_SenderIdType(This,plSenderIdType) (This)->lpVtbl->get_SenderIdType(This,plSenderIdType)
#define IMSMQMessage_put_SenderIdType(This,lSenderIdType) (This)->lpVtbl->put_SenderIdType(This,lSenderIdType)
#define IMSMQMessage_Send(This,DestinationQueue,Transaction) (This)->lpVtbl->Send(This,DestinationQueue,Transaction)
#define IMSMQMessage_AttachCurrentSecurityContext(This) (This)->lpVtbl->AttachCurrentSecurityContext(This)
#endif
#endif
  HRESULT WINAPI IMSMQMessage_get_Class_Proxy(IMSMQMessage *This,__LONG32 *plClass);
  void __RPC_STUB IMSMQMessage_get_Class_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_PrivLevel_Proxy(IMSMQMessage *This,__LONG32 *plPrivLevel);
  void __RPC_STUB IMSMQMessage_get_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_PrivLevel_Proxy(IMSMQMessage *This,__LONG32 lPrivLevel);
  void __RPC_STUB IMSMQMessage_put_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_AuthLevel_Proxy(IMSMQMessage *This,__LONG32 *plAuthLevel);
  void __RPC_STUB IMSMQMessage_get_AuthLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_AuthLevel_Proxy(IMSMQMessage *This,__LONG32 lAuthLevel);
  void __RPC_STUB IMSMQMessage_put_AuthLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_IsAuthenticated_Proxy(IMSMQMessage *This,Boolean *pisAuthenticated);
  void __RPC_STUB IMSMQMessage_get_IsAuthenticated_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_Delivery_Proxy(IMSMQMessage *This,__LONG32 *plDelivery);
  void __RPC_STUB IMSMQMessage_get_Delivery_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_Delivery_Proxy(IMSMQMessage *This,__LONG32 lDelivery);
  void __RPC_STUB IMSMQMessage_put_Delivery_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_Trace_Proxy(IMSMQMessage *This,__LONG32 *plTrace);
  void __RPC_STUB IMSMQMessage_get_Trace_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_Trace_Proxy(IMSMQMessage *This,__LONG32 lTrace);
  void __RPC_STUB IMSMQMessage_put_Trace_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_Priority_Proxy(IMSMQMessage *This,__LONG32 *plPriority);
  void __RPC_STUB IMSMQMessage_get_Priority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_Priority_Proxy(IMSMQMessage *This,__LONG32 lPriority);
  void __RPC_STUB IMSMQMessage_put_Priority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_Journal_Proxy(IMSMQMessage *This,__LONG32 *plJournal);
  void __RPC_STUB IMSMQMessage_get_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_Journal_Proxy(IMSMQMessage *This,__LONG32 lJournal);
  void __RPC_STUB IMSMQMessage_put_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_ResponseQueueInfo_Proxy(IMSMQMessage *This,IMSMQQueueInfo **ppqinfoResponse);
  void __RPC_STUB IMSMQMessage_get_ResponseQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_putref_ResponseQueueInfo_Proxy(IMSMQMessage *This,IMSMQQueueInfo *pqinfoResponse);
  void __RPC_STUB IMSMQMessage_putref_ResponseQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_AppSpecific_Proxy(IMSMQMessage *This,__LONG32 *plAppSpecific);
  void __RPC_STUB IMSMQMessage_get_AppSpecific_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_AppSpecific_Proxy(IMSMQMessage *This,__LONG32 lAppSpecific);
  void __RPC_STUB IMSMQMessage_put_AppSpecific_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_SourceMachineGuid_Proxy(IMSMQMessage *This,BSTR *pbstrGuidSrcMachine);
  void __RPC_STUB IMSMQMessage_get_SourceMachineGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_BodyLength_Proxy(IMSMQMessage *This,__LONG32 *pcbBody);
  void __RPC_STUB IMSMQMessage_get_BodyLength_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_Body_Proxy(IMSMQMessage *This,VARIANT *pvarBody);
  void __RPC_STUB IMSMQMessage_get_Body_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_Body_Proxy(IMSMQMessage *This,VARIANT varBody);
  void __RPC_STUB IMSMQMessage_put_Body_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_AdminQueueInfo_Proxy(IMSMQMessage *This,IMSMQQueueInfo **ppqinfoAdmin);
  void __RPC_STUB IMSMQMessage_get_AdminQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_putref_AdminQueueInfo_Proxy(IMSMQMessage *This,IMSMQQueueInfo *pqinfoAdmin);
  void __RPC_STUB IMSMQMessage_putref_AdminQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_Id_Proxy(IMSMQMessage *This,VARIANT *pvarMsgId);
  void __RPC_STUB IMSMQMessage_get_Id_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_CorrelationId_Proxy(IMSMQMessage *This,VARIANT *pvarMsgId);
  void __RPC_STUB IMSMQMessage_get_CorrelationId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_CorrelationId_Proxy(IMSMQMessage *This,VARIANT varMsgId);
  void __RPC_STUB IMSMQMessage_put_CorrelationId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_Ack_Proxy(IMSMQMessage *This,__LONG32 *plAck);
  void __RPC_STUB IMSMQMessage_get_Ack_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_Ack_Proxy(IMSMQMessage *This,__LONG32 lAck);
  void __RPC_STUB IMSMQMessage_put_Ack_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_Label_Proxy(IMSMQMessage *This,BSTR *pbstrLabel);
  void __RPC_STUB IMSMQMessage_get_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_Label_Proxy(IMSMQMessage *This,BSTR bstrLabel);
  void __RPC_STUB IMSMQMessage_put_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_MaxTimeToReachQueue_Proxy(IMSMQMessage *This,__LONG32 *plMaxTimeToReachQueue);
  void __RPC_STUB IMSMQMessage_get_MaxTimeToReachQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_MaxTimeToReachQueue_Proxy(IMSMQMessage *This,__LONG32 lMaxTimeToReachQueue);
  void __RPC_STUB IMSMQMessage_put_MaxTimeToReachQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_MaxTimeToReceive_Proxy(IMSMQMessage *This,__LONG32 *plMaxTimeToReceive);
  void __RPC_STUB IMSMQMessage_get_MaxTimeToReceive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_MaxTimeToReceive_Proxy(IMSMQMessage *This,__LONG32 lMaxTimeToReceive);
  void __RPC_STUB IMSMQMessage_put_MaxTimeToReceive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_HashAlgorithm_Proxy(IMSMQMessage *This,__LONG32 *plHashAlg);
  void __RPC_STUB IMSMQMessage_get_HashAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_HashAlgorithm_Proxy(IMSMQMessage *This,__LONG32 lHashAlg);
  void __RPC_STUB IMSMQMessage_put_HashAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_EncryptAlgorithm_Proxy(IMSMQMessage *This,__LONG32 *plEncryptAlg);
  void __RPC_STUB IMSMQMessage_get_EncryptAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_EncryptAlgorithm_Proxy(IMSMQMessage *This,__LONG32 lEncryptAlg);
  void __RPC_STUB IMSMQMessage_put_EncryptAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_SentTime_Proxy(IMSMQMessage *This,VARIANT *pvarSentTime);
  void __RPC_STUB IMSMQMessage_get_SentTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_ArrivedTime_Proxy(IMSMQMessage *This,VARIANT *plArrivedTime);
  void __RPC_STUB IMSMQMessage_get_ArrivedTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_DestinationQueueInfo_Proxy(IMSMQMessage *This,IMSMQQueueInfo **ppqinfoDest);
  void __RPC_STUB IMSMQMessage_get_DestinationQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_SenderCertificate_Proxy(IMSMQMessage *This,VARIANT *pvarSenderCert);
  void __RPC_STUB IMSMQMessage_get_SenderCertificate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_SenderCertificate_Proxy(IMSMQMessage *This,VARIANT varSenderCert);
  void __RPC_STUB IMSMQMessage_put_SenderCertificate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_SenderId_Proxy(IMSMQMessage *This,VARIANT *pvarSenderId);
  void __RPC_STUB IMSMQMessage_get_SenderId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_get_SenderIdType_Proxy(IMSMQMessage *This,__LONG32 *plSenderIdType);
  void __RPC_STUB IMSMQMessage_get_SenderIdType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_put_SenderIdType_Proxy(IMSMQMessage *This,__LONG32 lSenderIdType);
  void __RPC_STUB IMSMQMessage_put_SenderIdType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_Send_Proxy(IMSMQMessage *This,IMSMQQueue *DestinationQueue,VARIANT *Transaction);
  void __RPC_STUB IMSMQMessage_Send_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage_AttachCurrentSecurityContext_Proxy(IMSMQMessage *This);
  void __RPC_STUB IMSMQMessage_AttachCurrentSecurityContext_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQueueInfos_INTERFACE_DEFINED__
#define __IMSMQQueueInfos_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueueInfos;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueueInfos : public IDispatch {
  public:
    virtual HRESULT WINAPI Reset(void) = 0;
    virtual HRESULT WINAPI Next(IMSMQQueueInfo **ppqinfoNext) = 0;
  };
#else
  typedef struct IMSMQQueueInfosVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueueInfos *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueueInfos *This);
      ULONG (WINAPI *Release)(IMSMQQueueInfos *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueueInfos *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueueInfos *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueueInfos *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueueInfos *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *Reset)(IMSMQQueueInfos *This);
      HRESULT (WINAPI *Next)(IMSMQQueueInfos *This,IMSMQQueueInfo **ppqinfoNext);
    END_INTERFACE
  } IMSMQQueueInfosVtbl;
  struct IMSMQQueueInfos {
    CONST_VTBL struct IMSMQQueueInfosVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueueInfos_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueueInfos_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueueInfos_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueueInfos_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueueInfos_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueueInfos_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueueInfos_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueueInfos_Reset(This) (This)->lpVtbl->Reset(This)
#define IMSMQQueueInfos_Next(This,ppqinfoNext) (This)->lpVtbl->Next(This,ppqinfoNext)
#endif
#endif
  HRESULT WINAPI IMSMQQueueInfos_Reset_Proxy(IMSMQQueueInfos *This);
  void __RPC_STUB IMSMQQueueInfos_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfos_Next_Proxy(IMSMQQueueInfos *This,IMSMQQueueInfo **ppqinfoNext);
  void __RPC_STUB IMSMQQueueInfos_Next_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQueueInfos2_INTERFACE_DEFINED__
#define __IMSMQQueueInfos2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueueInfos2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueueInfos2 : public IDispatch {
  public:
    virtual HRESULT WINAPI Reset(void) = 0;
    virtual HRESULT WINAPI Next(IMSMQQueueInfo2 **ppqinfoNext) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQQueueInfos2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueueInfos2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueueInfos2 *This);
      ULONG (WINAPI *Release)(IMSMQQueueInfos2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueueInfos2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueueInfos2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueueInfos2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueueInfos2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *Reset)(IMSMQQueueInfos2 *This);
      HRESULT (WINAPI *Next)(IMSMQQueueInfos2 *This,IMSMQQueueInfo2 **ppqinfoNext);
      HRESULT (WINAPI *get_Properties)(IMSMQQueueInfos2 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQQueueInfos2Vtbl;
  struct IMSMQQueueInfos2 {
    CONST_VTBL struct IMSMQQueueInfos2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueueInfos2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueueInfos2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueueInfos2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueueInfos2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueueInfos2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueueInfos2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueueInfos2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueueInfos2_Reset(This) (This)->lpVtbl->Reset(This)
#define IMSMQQueueInfos2_Next(This,ppqinfoNext) (This)->lpVtbl->Next(This,ppqinfoNext)
#define IMSMQQueueInfos2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQQueueInfos2_Reset_Proxy(IMSMQQueueInfos2 *This);
  void __RPC_STUB IMSMQQueueInfos2_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfos2_Next_Proxy(IMSMQQueueInfos2 *This,IMSMQQueueInfo2 **ppqinfoNext);
  void __RPC_STUB IMSMQQueueInfos2_Next_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfos2_get_Properties_Proxy(IMSMQQueueInfos2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQQueueInfos2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQueueInfos3_INTERFACE_DEFINED__
#define __IMSMQQueueInfos3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueueInfos3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueueInfos3 : public IDispatch {
  public:
    virtual HRESULT WINAPI Reset(void) = 0;
    virtual HRESULT WINAPI Next(IMSMQQueueInfo3 **ppqinfoNext) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQQueueInfos3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueueInfos3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueueInfos3 *This);
      ULONG (WINAPI *Release)(IMSMQQueueInfos3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueueInfos3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueueInfos3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueueInfos3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueueInfos3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *Reset)(IMSMQQueueInfos3 *This);
      HRESULT (WINAPI *Next)(IMSMQQueueInfos3 *This,IMSMQQueueInfo3 **ppqinfoNext);
      HRESULT (WINAPI *get_Properties)(IMSMQQueueInfos3 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQQueueInfos3Vtbl;
  struct IMSMQQueueInfos3 {
    CONST_VTBL struct IMSMQQueueInfos3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueueInfos3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueueInfos3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueueInfos3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueueInfos3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueueInfos3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueueInfos3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueueInfos3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueueInfos3_Reset(This) (This)->lpVtbl->Reset(This)
#define IMSMQQueueInfos3_Next(This,ppqinfoNext) (This)->lpVtbl->Next(This,ppqinfoNext)
#define IMSMQQueueInfos3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQQueueInfos3_Reset_Proxy(IMSMQQueueInfos3 *This);
  void __RPC_STUB IMSMQQueueInfos3_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfos3_Next_Proxy(IMSMQQueueInfos3 *This,IMSMQQueueInfo3 **ppqinfoNext);
  void __RPC_STUB IMSMQQueueInfos3_Next_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueInfos3_get_Properties_Proxy(IMSMQQueueInfos3 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQQueueInfos3_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQEvent_INTERFACE_DEFINED__
#define __IMSMQEvent_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQEvent;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQEvent : public IDispatch {
  };
#else
  typedef struct IMSMQEventVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQEvent *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQEvent *This);
      ULONG (WINAPI *Release)(IMSMQEvent *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQEvent *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQEvent *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQEvent *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQEvent *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
    END_INTERFACE
  } IMSMQEventVtbl;
  struct IMSMQEvent {
    CONST_VTBL struct IMSMQEventVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQEvent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQEvent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQEvent_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQEvent_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQEvent_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQEvent_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQEvent_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#endif
#endif
#endif

#ifndef __IMSMQEvent2_INTERFACE_DEFINED__
#define __IMSMQEvent2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQEvent2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQEvent2 : public IMSMQEvent {
  public:
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQEvent2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQEvent2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQEvent2 *This);
      ULONG (WINAPI *Release)(IMSMQEvent2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQEvent2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQEvent2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQEvent2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQEvent2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Properties)(IMSMQEvent2 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQEvent2Vtbl;
  struct IMSMQEvent2 {
    CONST_VTBL struct IMSMQEvent2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQEvent2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQEvent2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQEvent2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQEvent2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQEvent2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQEvent2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQEvent2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQEvent2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQEvent2_get_Properties_Proxy(IMSMQEvent2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQEvent2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQEvent3_INTERFACE_DEFINED__
#define __IMSMQEvent3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQEvent3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQEvent3 : public IMSMQEvent2 {
  };
#else
  typedef struct IMSMQEvent3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQEvent3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQEvent3 *This);
      ULONG (WINAPI *Release)(IMSMQEvent3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQEvent3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQEvent3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQEvent3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQEvent3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Properties)(IMSMQEvent3 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQEvent3Vtbl;
  struct IMSMQEvent3 {
    CONST_VTBL struct IMSMQEvent3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQEvent3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQEvent3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQEvent3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQEvent3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQEvent3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQEvent3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQEvent3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQEvent3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
#endif

#ifndef __IMSMQTransaction_INTERFACE_DEFINED__
#define __IMSMQTransaction_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQTransaction;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQTransaction : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Transaction(__LONG32 *plTransaction) = 0;
    virtual HRESULT WINAPI Commit(VARIANT *fRetaining,VARIANT *grfTC,VARIANT *grfRM) = 0;
    virtual HRESULT WINAPI Abort(VARIANT *fRetaining,VARIANT *fAsync) = 0;
  };
#else
  typedef struct IMSMQTransactionVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQTransaction *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQTransaction *This);
      ULONG (WINAPI *Release)(IMSMQTransaction *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQTransaction *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQTransaction *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQTransaction *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQTransaction *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Transaction)(IMSMQTransaction *This,__LONG32 *plTransaction);
      HRESULT (WINAPI *Commit)(IMSMQTransaction *This,VARIANT *fRetaining,VARIANT *grfTC,VARIANT *grfRM);
      HRESULT (WINAPI *Abort)(IMSMQTransaction *This,VARIANT *fRetaining,VARIANT *fAsync);
    END_INTERFACE
  } IMSMQTransactionVtbl;
  struct IMSMQTransaction {
    CONST_VTBL struct IMSMQTransactionVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQTransaction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQTransaction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQTransaction_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQTransaction_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQTransaction_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQTransaction_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQTransaction_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQTransaction_get_Transaction(This,plTransaction) (This)->lpVtbl->get_Transaction(This,plTransaction)
#define IMSMQTransaction_Commit(This,fRetaining,grfTC,grfRM) (This)->lpVtbl->Commit(This,fRetaining,grfTC,grfRM)
#define IMSMQTransaction_Abort(This,fRetaining,fAsync) (This)->lpVtbl->Abort(This,fRetaining,fAsync)
#endif
#endif
  HRESULT WINAPI IMSMQTransaction_get_Transaction_Proxy(IMSMQTransaction *This,__LONG32 *plTransaction);
  void __RPC_STUB IMSMQTransaction_get_Transaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQTransaction_Commit_Proxy(IMSMQTransaction *This,VARIANT *fRetaining,VARIANT *grfTC,VARIANT *grfRM);
  void __RPC_STUB IMSMQTransaction_Commit_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQTransaction_Abort_Proxy(IMSMQTransaction *This,VARIANT *fRetaining,VARIANT *fAsync);
  void __RPC_STUB IMSMQTransaction_Abort_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQCoordinatedTransactionDispenser_INTERFACE_DEFINED__
#define __IMSMQCoordinatedTransactionDispenser_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQCoordinatedTransactionDispenser;
#if defined(__cplusplus) && !defined(CINTERFACE)

  struct IMSMQCoordinatedTransactionDispenser : public IDispatch {
  public:
    virtual HRESULT WINAPI BeginTransaction(IMSMQTransaction **ptransaction) = 0;
  };
#else
  typedef struct IMSMQCoordinatedTransactionDispenserVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQCoordinatedTransactionDispenser *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQCoordinatedTransactionDispenser *This);
      ULONG (WINAPI *Release)(IMSMQCoordinatedTransactionDispenser *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQCoordinatedTransactionDispenser *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQCoordinatedTransactionDispenser *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQCoordinatedTransactionDispenser *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQCoordinatedTransactionDispenser *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *BeginTransaction)(IMSMQCoordinatedTransactionDispenser *This,IMSMQTransaction **ptransaction);
    END_INTERFACE
  } IMSMQCoordinatedTransactionDispenserVtbl;
  struct IMSMQCoordinatedTransactionDispenser {
    CONST_VTBL struct IMSMQCoordinatedTransactionDispenserVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQCoordinatedTransactionDispenser_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQCoordinatedTransactionDispenser_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQCoordinatedTransactionDispenser_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQCoordinatedTransactionDispenser_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQCoordinatedTransactionDispenser_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQCoordinatedTransactionDispenser_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQCoordinatedTransactionDispenser_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQCoordinatedTransactionDispenser_BeginTransaction(This,ptransaction) (This)->lpVtbl->BeginTransaction(This,ptransaction)
#endif
#endif
  HRESULT WINAPI IMSMQCoordinatedTransactionDispenser_BeginTransaction_Proxy(IMSMQCoordinatedTransactionDispenser *This,IMSMQTransaction **ptransaction);
  void __RPC_STUB IMSMQCoordinatedTransactionDispenser_BeginTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQTransactionDispenser_INTERFACE_DEFINED__
#define __IMSMQTransactionDispenser_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQTransactionDispenser;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQTransactionDispenser : public IDispatch {
  public:
    virtual HRESULT WINAPI BeginTransaction(IMSMQTransaction **ptransaction) = 0;
  };
#else
  typedef struct IMSMQTransactionDispenserVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQTransactionDispenser *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQTransactionDispenser *This);
      ULONG (WINAPI *Release)(IMSMQTransactionDispenser *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQTransactionDispenser *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQTransactionDispenser *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQTransactionDispenser *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQTransactionDispenser *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *BeginTransaction)(IMSMQTransactionDispenser *This,IMSMQTransaction **ptransaction);
    END_INTERFACE
  } IMSMQTransactionDispenserVtbl;
  struct IMSMQTransactionDispenser {
    CONST_VTBL struct IMSMQTransactionDispenserVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQTransactionDispenser_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQTransactionDispenser_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQTransactionDispenser_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQTransactionDispenser_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQTransactionDispenser_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQTransactionDispenser_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQTransactionDispenser_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQTransactionDispenser_BeginTransaction(This,ptransaction) (This)->lpVtbl->BeginTransaction(This,ptransaction)
#endif
#endif
  HRESULT WINAPI IMSMQTransactionDispenser_BeginTransaction_Proxy(IMSMQTransactionDispenser *This,IMSMQTransaction **ptransaction);
  void __RPC_STUB IMSMQTransactionDispenser_BeginTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQuery2_INTERFACE_DEFINED__
#define __IMSMQQuery2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQuery2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQuery2 : public IDispatch {
  public:
    virtual HRESULT WINAPI LookupQueue(VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos2 **ppqinfos) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQQuery2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQuery2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQuery2 *This);
      ULONG (WINAPI *Release)(IMSMQQuery2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQuery2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQuery2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQuery2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQuery2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *LookupQueue)(IMSMQQuery2 *This,VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos2 **ppqinfos);
      HRESULT (WINAPI *get_Properties)(IMSMQQuery2 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQQuery2Vtbl;
  struct IMSMQQuery2 {
    CONST_VTBL struct IMSMQQuery2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQuery2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQuery2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQuery2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQuery2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQuery2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQuery2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQuery2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQuery2_LookupQueue(This,QueueGuid,ServiceTypeGuid,Label,CreateTime,ModifyTime,RelServiceType,RelLabel,RelCreateTime,RelModifyTime,ppqinfos) (This)->lpVtbl->LookupQueue(This,QueueGuid,ServiceTypeGuid,Label,CreateTime,ModifyTime,RelServiceType,RelLabel,RelCreateTime,RelModifyTime,ppqinfos)
#define IMSMQQuery2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQQuery2_LookupQueue_Proxy(IMSMQQuery2 *This,VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos2 **ppqinfos);
  void __RPC_STUB IMSMQQuery2_LookupQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQuery2_get_Properties_Proxy(IMSMQQuery2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQQuery2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQQuery3_INTERFACE_DEFINED__
#define __IMSMQQuery3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQuery3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQuery3 : public IDispatch {
  public:
    virtual HRESULT WINAPI LookupQueue_v2(VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos3 **ppqinfos) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
    virtual HRESULT WINAPI LookupQueue(VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,VARIANT *MulticastAddress,VARIANT *RelMulticastAddress,IMSMQQueueInfos3 **ppqinfos) = 0;
  };
#else
  typedef struct IMSMQQuery3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQuery3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQuery3 *This);
      ULONG (WINAPI *Release)(IMSMQQuery3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQuery3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQuery3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQuery3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQuery3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *LookupQueue_v2)(IMSMQQuery3 *This,VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos3 **ppqinfos);
      HRESULT (WINAPI *get_Properties)(IMSMQQuery3 *This,IDispatch **ppcolProperties);
      HRESULT (WINAPI *LookupQueue)(IMSMQQuery3 *This,VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,VARIANT *MulticastAddress,VARIANT *RelMulticastAddress,IMSMQQueueInfos3 **ppqinfos);
    END_INTERFACE
  } IMSMQQuery3Vtbl;
  struct IMSMQQuery3 {
    CONST_VTBL struct IMSMQQuery3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQuery3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQuery3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQuery3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQuery3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQuery3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQuery3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQuery3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQuery3_LookupQueue_v2(This,QueueGuid,ServiceTypeGuid,Label,CreateTime,ModifyTime,RelServiceType,RelLabel,RelCreateTime,RelModifyTime,ppqinfos) (This)->lpVtbl->LookupQueue_v2(This,QueueGuid,ServiceTypeGuid,Label,CreateTime,ModifyTime,RelServiceType,RelLabel,RelCreateTime,RelModifyTime,ppqinfos)
#define IMSMQQuery3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#define IMSMQQuery3_LookupQueue(This,QueueGuid,ServiceTypeGuid,Label,CreateTime,ModifyTime,RelServiceType,RelLabel,RelCreateTime,RelModifyTime,MulticastAddress,RelMulticastAddress,ppqinfos) (This)->lpVtbl->LookupQueue(This,QueueGuid,ServiceTypeGuid,Label,CreateTime,ModifyTime,RelServiceType,RelLabel,RelCreateTime,RelModifyTime,MulticastAddress,RelMulticastAddress,ppqinfos)
#endif
#endif
  HRESULT WINAPI IMSMQQuery3_LookupQueue_v2_Proxy(IMSMQQuery3 *This,VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,IMSMQQueueInfos3 **ppqinfos);
  void __RPC_STUB IMSMQQuery3_LookupQueue_v2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQuery3_get_Properties_Proxy(IMSMQQuery3 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQQuery3_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQuery3_LookupQueue_Proxy(IMSMQQuery3 *This,VARIANT *QueueGuid,VARIANT *ServiceTypeGuid,VARIANT *Label,VARIANT *CreateTime,VARIANT *ModifyTime,VARIANT *RelServiceType,VARIANT *RelLabel,VARIANT *RelCreateTime,VARIANT *RelModifyTime,VARIANT *MulticastAddress,VARIANT *RelMulticastAddress,IMSMQQueueInfos3 **ppqinfos);
  void __RPC_STUB IMSMQQuery3_LookupQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQQuery;
#ifdef __cplusplus
  class MSMQQuery;
#endif

#ifndef __IMSMQMessage2_INTERFACE_DEFINED__
#define __IMSMQMessage2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQMessage2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQMessage2 : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Class(__LONG32 *plClass) = 0;
    virtual HRESULT WINAPI get_PrivLevel(__LONG32 *plPrivLevel) = 0;
    virtual HRESULT WINAPI put_PrivLevel(__LONG32 lPrivLevel) = 0;
    virtual HRESULT WINAPI get_AuthLevel(__LONG32 *plAuthLevel) = 0;
    virtual HRESULT WINAPI put_AuthLevel(__LONG32 lAuthLevel) = 0;
    virtual HRESULT WINAPI get_IsAuthenticated(Boolean *pisAuthenticated) = 0;
    virtual HRESULT WINAPI get_Delivery(__LONG32 *plDelivery) = 0;
    virtual HRESULT WINAPI put_Delivery(__LONG32 lDelivery) = 0;
    virtual HRESULT WINAPI get_Trace(__LONG32 *plTrace) = 0;
    virtual HRESULT WINAPI put_Trace(__LONG32 lTrace) = 0;
    virtual HRESULT WINAPI get_Priority(__LONG32 *plPriority) = 0;
    virtual HRESULT WINAPI put_Priority(__LONG32 lPriority) = 0;
    virtual HRESULT WINAPI get_Journal(__LONG32 *plJournal) = 0;
    virtual HRESULT WINAPI put_Journal(__LONG32 lJournal) = 0;
    virtual HRESULT WINAPI get_ResponseQueueInfo_v1(IMSMQQueueInfo **ppqinfoResponse) = 0;
    virtual HRESULT WINAPI putref_ResponseQueueInfo_v1(IMSMQQueueInfo *pqinfoResponse) = 0;
    virtual HRESULT WINAPI get_AppSpecific(__LONG32 *plAppSpecific) = 0;
    virtual HRESULT WINAPI put_AppSpecific(__LONG32 lAppSpecific) = 0;
    virtual HRESULT WINAPI get_SourceMachineGuid(BSTR *pbstrGuidSrcMachine) = 0;
    virtual HRESULT WINAPI get_BodyLength(__LONG32 *pcbBody) = 0;
    virtual HRESULT WINAPI get_Body(VARIANT *pvarBody) = 0;
    virtual HRESULT WINAPI put_Body(VARIANT varBody) = 0;
    virtual HRESULT WINAPI get_AdminQueueInfo_v1(IMSMQQueueInfo **ppqinfoAdmin) = 0;
    virtual HRESULT WINAPI putref_AdminQueueInfo_v1(IMSMQQueueInfo *pqinfoAdmin) = 0;
    virtual HRESULT WINAPI get_Id(VARIANT *pvarMsgId) = 0;
    virtual HRESULT WINAPI get_CorrelationId(VARIANT *pvarMsgId) = 0;
    virtual HRESULT WINAPI put_CorrelationId(VARIANT varMsgId) = 0;
    virtual HRESULT WINAPI get_Ack(__LONG32 *plAck) = 0;
    virtual HRESULT WINAPI put_Ack(__LONG32 lAck) = 0;
    virtual HRESULT WINAPI get_Label(BSTR *pbstrLabel) = 0;
    virtual HRESULT WINAPI put_Label(BSTR bstrLabel) = 0;
    virtual HRESULT WINAPI get_MaxTimeToReachQueue(__LONG32 *plMaxTimeToReachQueue) = 0;
    virtual HRESULT WINAPI put_MaxTimeToReachQueue(__LONG32 lMaxTimeToReachQueue) = 0;
    virtual HRESULT WINAPI get_MaxTimeToReceive(__LONG32 *plMaxTimeToReceive) = 0;
    virtual HRESULT WINAPI put_MaxTimeToReceive(__LONG32 lMaxTimeToReceive) = 0;
    virtual HRESULT WINAPI get_HashAlgorithm(__LONG32 *plHashAlg) = 0;
    virtual HRESULT WINAPI put_HashAlgorithm(__LONG32 lHashAlg) = 0;
    virtual HRESULT WINAPI get_EncryptAlgorithm(__LONG32 *plEncryptAlg) = 0;
    virtual HRESULT WINAPI put_EncryptAlgorithm(__LONG32 lEncryptAlg) = 0;
    virtual HRESULT WINAPI get_SentTime(VARIANT *pvarSentTime) = 0;
    virtual HRESULT WINAPI get_ArrivedTime(VARIANT *plArrivedTime) = 0;
    virtual HRESULT WINAPI get_DestinationQueueInfo(IMSMQQueueInfo2 **ppqinfoDest) = 0;
    virtual HRESULT WINAPI get_SenderCertificate(VARIANT *pvarSenderCert) = 0;
    virtual HRESULT WINAPI put_SenderCertificate(VARIANT varSenderCert) = 0;
    virtual HRESULT WINAPI get_SenderId(VARIANT *pvarSenderId) = 0;
    virtual HRESULT WINAPI get_SenderIdType(__LONG32 *plSenderIdType) = 0;
    virtual HRESULT WINAPI put_SenderIdType(__LONG32 lSenderIdType) = 0;
    virtual HRESULT WINAPI Send(IMSMQQueue2 *DestinationQueue,VARIANT *Transaction) = 0;
    virtual HRESULT WINAPI AttachCurrentSecurityContext(void) = 0;
    virtual HRESULT WINAPI get_SenderVersion(__LONG32 *plSenderVersion) = 0;
    virtual HRESULT WINAPI get_Extension(VARIANT *pvarExtension) = 0;
    virtual HRESULT WINAPI put_Extension(VARIANT varExtension) = 0;
    virtual HRESULT WINAPI get_ConnectorTypeGuid(BSTR *pbstrGuidConnectorType) = 0;
    virtual HRESULT WINAPI put_ConnectorTypeGuid(BSTR bstrGuidConnectorType) = 0;
    virtual HRESULT WINAPI get_TransactionStatusQueueInfo(IMSMQQueueInfo2 **ppqinfoXactStatus) = 0;
    virtual HRESULT WINAPI get_DestinationSymmetricKey(VARIANT *pvarDestSymmKey) = 0;
    virtual HRESULT WINAPI put_DestinationSymmetricKey(VARIANT varDestSymmKey) = 0;
    virtual HRESULT WINAPI get_Signature(VARIANT *pvarSignature) = 0;
    virtual HRESULT WINAPI put_Signature(VARIANT varSignature) = 0;
    virtual HRESULT WINAPI get_AuthenticationProviderType(__LONG32 *plAuthProvType) = 0;
    virtual HRESULT WINAPI put_AuthenticationProviderType(__LONG32 lAuthProvType) = 0;
    virtual HRESULT WINAPI get_AuthenticationProviderName(BSTR *pbstrAuthProvName) = 0;
    virtual HRESULT WINAPI put_AuthenticationProviderName(BSTR bstrAuthProvName) = 0;
    virtual HRESULT WINAPI put_SenderId(VARIANT varSenderId) = 0;
    virtual HRESULT WINAPI get_MsgClass(__LONG32 *plMsgClass) = 0;
    virtual HRESULT WINAPI put_MsgClass(__LONG32 lMsgClass) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
    virtual HRESULT WINAPI get_TransactionId(VARIANT *pvarXactId) = 0;
    virtual HRESULT WINAPI get_IsFirstInTransaction(Boolean *pisFirstInXact) = 0;
    virtual HRESULT WINAPI get_IsLastInTransaction(Boolean *pisLastInXact) = 0;
    virtual HRESULT WINAPI get_ResponseQueueInfo(IMSMQQueueInfo2 **ppqinfoResponse) = 0;
    virtual HRESULT WINAPI putref_ResponseQueueInfo(IMSMQQueueInfo2 *pqinfoResponse) = 0;
    virtual HRESULT WINAPI get_AdminQueueInfo(IMSMQQueueInfo2 **ppqinfoAdmin) = 0;
    virtual HRESULT WINAPI putref_AdminQueueInfo(IMSMQQueueInfo2 *pqinfoAdmin) = 0;
    virtual HRESULT WINAPI get_ReceivedAuthenticationLevel(short *psReceivedAuthenticationLevel) = 0;
  };
#else
  typedef struct IMSMQMessage2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQMessage2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQMessage2 *This);
      ULONG (WINAPI *Release)(IMSMQMessage2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQMessage2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQMessage2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQMessage2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQMessage2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Class)(IMSMQMessage2 *This,__LONG32 *plClass);
      HRESULT (WINAPI *get_PrivLevel)(IMSMQMessage2 *This,__LONG32 *plPrivLevel);
      HRESULT (WINAPI *put_PrivLevel)(IMSMQMessage2 *This,__LONG32 lPrivLevel);
      HRESULT (WINAPI *get_AuthLevel)(IMSMQMessage2 *This,__LONG32 *plAuthLevel);
      HRESULT (WINAPI *put_AuthLevel)(IMSMQMessage2 *This,__LONG32 lAuthLevel);
      HRESULT (WINAPI *get_IsAuthenticated)(IMSMQMessage2 *This,Boolean *pisAuthenticated);
      HRESULT (WINAPI *get_Delivery)(IMSMQMessage2 *This,__LONG32 *plDelivery);
      HRESULT (WINAPI *put_Delivery)(IMSMQMessage2 *This,__LONG32 lDelivery);
      HRESULT (WINAPI *get_Trace)(IMSMQMessage2 *This,__LONG32 *plTrace);
      HRESULT (WINAPI *put_Trace)(IMSMQMessage2 *This,__LONG32 lTrace);
      HRESULT (WINAPI *get_Priority)(IMSMQMessage2 *This,__LONG32 *plPriority);
      HRESULT (WINAPI *put_Priority)(IMSMQMessage2 *This,__LONG32 lPriority);
      HRESULT (WINAPI *get_Journal)(IMSMQMessage2 *This,__LONG32 *plJournal);
      HRESULT (WINAPI *put_Journal)(IMSMQMessage2 *This,__LONG32 lJournal);
      HRESULT (WINAPI *get_ResponseQueueInfo_v1)(IMSMQMessage2 *This,IMSMQQueueInfo **ppqinfoResponse);
      HRESULT (WINAPI *putref_ResponseQueueInfo_v1)(IMSMQMessage2 *This,IMSMQQueueInfo *pqinfoResponse);
      HRESULT (WINAPI *get_AppSpecific)(IMSMQMessage2 *This,__LONG32 *plAppSpecific);
      HRESULT (WINAPI *put_AppSpecific)(IMSMQMessage2 *This,__LONG32 lAppSpecific);
      HRESULT (WINAPI *get_SourceMachineGuid)(IMSMQMessage2 *This,BSTR *pbstrGuidSrcMachine);
      HRESULT (WINAPI *get_BodyLength)(IMSMQMessage2 *This,__LONG32 *pcbBody);
      HRESULT (WINAPI *get_Body)(IMSMQMessage2 *This,VARIANT *pvarBody);
      HRESULT (WINAPI *put_Body)(IMSMQMessage2 *This,VARIANT varBody);
      HRESULT (WINAPI *get_AdminQueueInfo_v1)(IMSMQMessage2 *This,IMSMQQueueInfo **ppqinfoAdmin);
      HRESULT (WINAPI *putref_AdminQueueInfo_v1)(IMSMQMessage2 *This,IMSMQQueueInfo *pqinfoAdmin);
      HRESULT (WINAPI *get_Id)(IMSMQMessage2 *This,VARIANT *pvarMsgId);
      HRESULT (WINAPI *get_CorrelationId)(IMSMQMessage2 *This,VARIANT *pvarMsgId);
      HRESULT (WINAPI *put_CorrelationId)(IMSMQMessage2 *This,VARIANT varMsgId);
      HRESULT (WINAPI *get_Ack)(IMSMQMessage2 *This,__LONG32 *plAck);
      HRESULT (WINAPI *put_Ack)(IMSMQMessage2 *This,__LONG32 lAck);
      HRESULT (WINAPI *get_Label)(IMSMQMessage2 *This,BSTR *pbstrLabel);
      HRESULT (WINAPI *put_Label)(IMSMQMessage2 *This,BSTR bstrLabel);
      HRESULT (WINAPI *get_MaxTimeToReachQueue)(IMSMQMessage2 *This,__LONG32 *plMaxTimeToReachQueue);
      HRESULT (WINAPI *put_MaxTimeToReachQueue)(IMSMQMessage2 *This,__LONG32 lMaxTimeToReachQueue);
      HRESULT (WINAPI *get_MaxTimeToReceive)(IMSMQMessage2 *This,__LONG32 *plMaxTimeToReceive);
      HRESULT (WINAPI *put_MaxTimeToReceive)(IMSMQMessage2 *This,__LONG32 lMaxTimeToReceive);
      HRESULT (WINAPI *get_HashAlgorithm)(IMSMQMessage2 *This,__LONG32 *plHashAlg);
      HRESULT (WINAPI *put_HashAlgorithm)(IMSMQMessage2 *This,__LONG32 lHashAlg);
      HRESULT (WINAPI *get_EncryptAlgorithm)(IMSMQMessage2 *This,__LONG32 *plEncryptAlg);
      HRESULT (WINAPI *put_EncryptAlgorithm)(IMSMQMessage2 *This,__LONG32 lEncryptAlg);
      HRESULT (WINAPI *get_SentTime)(IMSMQMessage2 *This,VARIANT *pvarSentTime);
      HRESULT (WINAPI *get_ArrivedTime)(IMSMQMessage2 *This,VARIANT *plArrivedTime);
      HRESULT (WINAPI *get_DestinationQueueInfo)(IMSMQMessage2 *This,IMSMQQueueInfo2 **ppqinfoDest);
      HRESULT (WINAPI *get_SenderCertificate)(IMSMQMessage2 *This,VARIANT *pvarSenderCert);
      HRESULT (WINAPI *put_SenderCertificate)(IMSMQMessage2 *This,VARIANT varSenderCert);
      HRESULT (WINAPI *get_SenderId)(IMSMQMessage2 *This,VARIANT *pvarSenderId);
      HRESULT (WINAPI *get_SenderIdType)(IMSMQMessage2 *This,__LONG32 *plSenderIdType);
      HRESULT (WINAPI *put_SenderIdType)(IMSMQMessage2 *This,__LONG32 lSenderIdType);
      HRESULT (WINAPI *Send)(IMSMQMessage2 *This,IMSMQQueue2 *DestinationQueue,VARIANT *Transaction);
      HRESULT (WINAPI *AttachCurrentSecurityContext)(IMSMQMessage2 *This);
      HRESULT (WINAPI *get_SenderVersion)(IMSMQMessage2 *This,__LONG32 *plSenderVersion);
      HRESULT (WINAPI *get_Extension)(IMSMQMessage2 *This,VARIANT *pvarExtension);
      HRESULT (WINAPI *put_Extension)(IMSMQMessage2 *This,VARIANT varExtension);
      HRESULT (WINAPI *get_ConnectorTypeGuid)(IMSMQMessage2 *This,BSTR *pbstrGuidConnectorType);
      HRESULT (WINAPI *put_ConnectorTypeGuid)(IMSMQMessage2 *This,BSTR bstrGuidConnectorType);
      HRESULT (WINAPI *get_TransactionStatusQueueInfo)(IMSMQMessage2 *This,IMSMQQueueInfo2 **ppqinfoXactStatus);
      HRESULT (WINAPI *get_DestinationSymmetricKey)(IMSMQMessage2 *This,VARIANT *pvarDestSymmKey);
      HRESULT (WINAPI *put_DestinationSymmetricKey)(IMSMQMessage2 *This,VARIANT varDestSymmKey);
      HRESULT (WINAPI *get_Signature)(IMSMQMessage2 *This,VARIANT *pvarSignature);
      HRESULT (WINAPI *put_Signature)(IMSMQMessage2 *This,VARIANT varSignature);
      HRESULT (WINAPI *get_AuthenticationProviderType)(IMSMQMessage2 *This,__LONG32 *plAuthProvType);
      HRESULT (WINAPI *put_AuthenticationProviderType)(IMSMQMessage2 *This,__LONG32 lAuthProvType);
      HRESULT (WINAPI *get_AuthenticationProviderName)(IMSMQMessage2 *This,BSTR *pbstrAuthProvName);
      HRESULT (WINAPI *put_AuthenticationProviderName)(IMSMQMessage2 *This,BSTR bstrAuthProvName);
      HRESULT (WINAPI *put_SenderId)(IMSMQMessage2 *This,VARIANT varSenderId);
      HRESULT (WINAPI *get_MsgClass)(IMSMQMessage2 *This,__LONG32 *plMsgClass);
      HRESULT (WINAPI *put_MsgClass)(IMSMQMessage2 *This,__LONG32 lMsgClass);
      HRESULT (WINAPI *get_Properties)(IMSMQMessage2 *This,IDispatch **ppcolProperties);
      HRESULT (WINAPI *get_TransactionId)(IMSMQMessage2 *This,VARIANT *pvarXactId);
      HRESULT (WINAPI *get_IsFirstInTransaction)(IMSMQMessage2 *This,Boolean *pisFirstInXact);
      HRESULT (WINAPI *get_IsLastInTransaction)(IMSMQMessage2 *This,Boolean *pisLastInXact);
      HRESULT (WINAPI *get_ResponseQueueInfo)(IMSMQMessage2 *This,IMSMQQueueInfo2 **ppqinfoResponse);
      HRESULT (WINAPI *putref_ResponseQueueInfo)(IMSMQMessage2 *This,IMSMQQueueInfo2 *pqinfoResponse);
      HRESULT (WINAPI *get_AdminQueueInfo)(IMSMQMessage2 *This,IMSMQQueueInfo2 **ppqinfoAdmin);
      HRESULT (WINAPI *putref_AdminQueueInfo)(IMSMQMessage2 *This,IMSMQQueueInfo2 *pqinfoAdmin);
      HRESULT (WINAPI *get_ReceivedAuthenticationLevel)(IMSMQMessage2 *This,short *psReceivedAuthenticationLevel);
    END_INTERFACE
  } IMSMQMessage2Vtbl;
  struct IMSMQMessage2 {
    CONST_VTBL struct IMSMQMessage2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQMessage2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQMessage2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQMessage2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQMessage2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQMessage2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQMessage2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQMessage2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQMessage2_get_Class(This,plClass) (This)->lpVtbl->get_Class(This,plClass)
#define IMSMQMessage2_get_PrivLevel(This,plPrivLevel) (This)->lpVtbl->get_PrivLevel(This,plPrivLevel)
#define IMSMQMessage2_put_PrivLevel(This,lPrivLevel) (This)->lpVtbl->put_PrivLevel(This,lPrivLevel)
#define IMSMQMessage2_get_AuthLevel(This,plAuthLevel) (This)->lpVtbl->get_AuthLevel(This,plAuthLevel)
#define IMSMQMessage2_put_AuthLevel(This,lAuthLevel) (This)->lpVtbl->put_AuthLevel(This,lAuthLevel)
#define IMSMQMessage2_get_IsAuthenticated(This,pisAuthenticated) (This)->lpVtbl->get_IsAuthenticated(This,pisAuthenticated)
#define IMSMQMessage2_get_Delivery(This,plDelivery) (This)->lpVtbl->get_Delivery(This,plDelivery)
#define IMSMQMessage2_put_Delivery(This,lDelivery) (This)->lpVtbl->put_Delivery(This,lDelivery)
#define IMSMQMessage2_get_Trace(This,plTrace) (This)->lpVtbl->get_Trace(This,plTrace)
#define IMSMQMessage2_put_Trace(This,lTrace) (This)->lpVtbl->put_Trace(This,lTrace)
#define IMSMQMessage2_get_Priority(This,plPriority) (This)->lpVtbl->get_Priority(This,plPriority)
#define IMSMQMessage2_put_Priority(This,lPriority) (This)->lpVtbl->put_Priority(This,lPriority)
#define IMSMQMessage2_get_Journal(This,plJournal) (This)->lpVtbl->get_Journal(This,plJournal)
#define IMSMQMessage2_put_Journal(This,lJournal) (This)->lpVtbl->put_Journal(This,lJournal)
#define IMSMQMessage2_get_ResponseQueueInfo_v1(This,ppqinfoResponse) (This)->lpVtbl->get_ResponseQueueInfo_v1(This,ppqinfoResponse)
#define IMSMQMessage2_putref_ResponseQueueInfo_v1(This,pqinfoResponse) (This)->lpVtbl->putref_ResponseQueueInfo_v1(This,pqinfoResponse)
#define IMSMQMessage2_get_AppSpecific(This,plAppSpecific) (This)->lpVtbl->get_AppSpecific(This,plAppSpecific)
#define IMSMQMessage2_put_AppSpecific(This,lAppSpecific) (This)->lpVtbl->put_AppSpecific(This,lAppSpecific)
#define IMSMQMessage2_get_SourceMachineGuid(This,pbstrGuidSrcMachine) (This)->lpVtbl->get_SourceMachineGuid(This,pbstrGuidSrcMachine)
#define IMSMQMessage2_get_BodyLength(This,pcbBody) (This)->lpVtbl->get_BodyLength(This,pcbBody)
#define IMSMQMessage2_get_Body(This,pvarBody) (This)->lpVtbl->get_Body(This,pvarBody)
#define IMSMQMessage2_put_Body(This,varBody) (This)->lpVtbl->put_Body(This,varBody)
#define IMSMQMessage2_get_AdminQueueInfo_v1(This,ppqinfoAdmin) (This)->lpVtbl->get_AdminQueueInfo_v1(This,ppqinfoAdmin)
#define IMSMQMessage2_putref_AdminQueueInfo_v1(This,pqinfoAdmin) (This)->lpVtbl->putref_AdminQueueInfo_v1(This,pqinfoAdmin)
#define IMSMQMessage2_get_Id(This,pvarMsgId) (This)->lpVtbl->get_Id(This,pvarMsgId)
#define IMSMQMessage2_get_CorrelationId(This,pvarMsgId) (This)->lpVtbl->get_CorrelationId(This,pvarMsgId)
#define IMSMQMessage2_put_CorrelationId(This,varMsgId) (This)->lpVtbl->put_CorrelationId(This,varMsgId)
#define IMSMQMessage2_get_Ack(This,plAck) (This)->lpVtbl->get_Ack(This,plAck)
#define IMSMQMessage2_put_Ack(This,lAck) (This)->lpVtbl->put_Ack(This,lAck)
#define IMSMQMessage2_get_Label(This,pbstrLabel) (This)->lpVtbl->get_Label(This,pbstrLabel)
#define IMSMQMessage2_put_Label(This,bstrLabel) (This)->lpVtbl->put_Label(This,bstrLabel)
#define IMSMQMessage2_get_MaxTimeToReachQueue(This,plMaxTimeToReachQueue) (This)->lpVtbl->get_MaxTimeToReachQueue(This,plMaxTimeToReachQueue)
#define IMSMQMessage2_put_MaxTimeToReachQueue(This,lMaxTimeToReachQueue) (This)->lpVtbl->put_MaxTimeToReachQueue(This,lMaxTimeToReachQueue)
#define IMSMQMessage2_get_MaxTimeToReceive(This,plMaxTimeToReceive) (This)->lpVtbl->get_MaxTimeToReceive(This,plMaxTimeToReceive)
#define IMSMQMessage2_put_MaxTimeToReceive(This,lMaxTimeToReceive) (This)->lpVtbl->put_MaxTimeToReceive(This,lMaxTimeToReceive)
#define IMSMQMessage2_get_HashAlgorithm(This,plHashAlg) (This)->lpVtbl->get_HashAlgorithm(This,plHashAlg)
#define IMSMQMessage2_put_HashAlgorithm(This,lHashAlg) (This)->lpVtbl->put_HashAlgorithm(This,lHashAlg)
#define IMSMQMessage2_get_EncryptAlgorithm(This,plEncryptAlg) (This)->lpVtbl->get_EncryptAlgorithm(This,plEncryptAlg)
#define IMSMQMessage2_put_EncryptAlgorithm(This,lEncryptAlg) (This)->lpVtbl->put_EncryptAlgorithm(This,lEncryptAlg)
#define IMSMQMessage2_get_SentTime(This,pvarSentTime) (This)->lpVtbl->get_SentTime(This,pvarSentTime)
#define IMSMQMessage2_get_ArrivedTime(This,plArrivedTime) (This)->lpVtbl->get_ArrivedTime(This,plArrivedTime)
#define IMSMQMessage2_get_DestinationQueueInfo(This,ppqinfoDest) (This)->lpVtbl->get_DestinationQueueInfo(This,ppqinfoDest)
#define IMSMQMessage2_get_SenderCertificate(This,pvarSenderCert) (This)->lpVtbl->get_SenderCertificate(This,pvarSenderCert)
#define IMSMQMessage2_put_SenderCertificate(This,varSenderCert) (This)->lpVtbl->put_SenderCertificate(This,varSenderCert)
#define IMSMQMessage2_get_SenderId(This,pvarSenderId) (This)->lpVtbl->get_SenderId(This,pvarSenderId)
#define IMSMQMessage2_get_SenderIdType(This,plSenderIdType) (This)->lpVtbl->get_SenderIdType(This,plSenderIdType)
#define IMSMQMessage2_put_SenderIdType(This,lSenderIdType) (This)->lpVtbl->put_SenderIdType(This,lSenderIdType)
#define IMSMQMessage2_Send(This,DestinationQueue,Transaction) (This)->lpVtbl->Send(This,DestinationQueue,Transaction)
#define IMSMQMessage2_AttachCurrentSecurityContext(This) (This)->lpVtbl->AttachCurrentSecurityContext(This)
#define IMSMQMessage2_get_SenderVersion(This,plSenderVersion) (This)->lpVtbl->get_SenderVersion(This,plSenderVersion)
#define IMSMQMessage2_get_Extension(This,pvarExtension) (This)->lpVtbl->get_Extension(This,pvarExtension)
#define IMSMQMessage2_put_Extension(This,varExtension) (This)->lpVtbl->put_Extension(This,varExtension)
#define IMSMQMessage2_get_ConnectorTypeGuid(This,pbstrGuidConnectorType) (This)->lpVtbl->get_ConnectorTypeGuid(This,pbstrGuidConnectorType)
#define IMSMQMessage2_put_ConnectorTypeGuid(This,bstrGuidConnectorType) (This)->lpVtbl->put_ConnectorTypeGuid(This,bstrGuidConnectorType)
#define IMSMQMessage2_get_TransactionStatusQueueInfo(This,ppqinfoXactStatus) (This)->lpVtbl->get_TransactionStatusQueueInfo(This,ppqinfoXactStatus)
#define IMSMQMessage2_get_DestinationSymmetricKey(This,pvarDestSymmKey) (This)->lpVtbl->get_DestinationSymmetricKey(This,pvarDestSymmKey)
#define IMSMQMessage2_put_DestinationSymmetricKey(This,varDestSymmKey) (This)->lpVtbl->put_DestinationSymmetricKey(This,varDestSymmKey)
#define IMSMQMessage2_get_Signature(This,pvarSignature) (This)->lpVtbl->get_Signature(This,pvarSignature)
#define IMSMQMessage2_put_Signature(This,varSignature) (This)->lpVtbl->put_Signature(This,varSignature)
#define IMSMQMessage2_get_AuthenticationProviderType(This,plAuthProvType) (This)->lpVtbl->get_AuthenticationProviderType(This,plAuthProvType)
#define IMSMQMessage2_put_AuthenticationProviderType(This,lAuthProvType) (This)->lpVtbl->put_AuthenticationProviderType(This,lAuthProvType)
#define IMSMQMessage2_get_AuthenticationProviderName(This,pbstrAuthProvName) (This)->lpVtbl->get_AuthenticationProviderName(This,pbstrAuthProvName)
#define IMSMQMessage2_put_AuthenticationProviderName(This,bstrAuthProvName) (This)->lpVtbl->put_AuthenticationProviderName(This,bstrAuthProvName)
#define IMSMQMessage2_put_SenderId(This,varSenderId) (This)->lpVtbl->put_SenderId(This,varSenderId)
#define IMSMQMessage2_get_MsgClass(This,plMsgClass) (This)->lpVtbl->get_MsgClass(This,plMsgClass)
#define IMSMQMessage2_put_MsgClass(This,lMsgClass) (This)->lpVtbl->put_MsgClass(This,lMsgClass)
#define IMSMQMessage2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#define IMSMQMessage2_get_TransactionId(This,pvarXactId) (This)->lpVtbl->get_TransactionId(This,pvarXactId)
#define IMSMQMessage2_get_IsFirstInTransaction(This,pisFirstInXact) (This)->lpVtbl->get_IsFirstInTransaction(This,pisFirstInXact)
#define IMSMQMessage2_get_IsLastInTransaction(This,pisLastInXact) (This)->lpVtbl->get_IsLastInTransaction(This,pisLastInXact)
#define IMSMQMessage2_get_ResponseQueueInfo(This,ppqinfoResponse) (This)->lpVtbl->get_ResponseQueueInfo(This,ppqinfoResponse)
#define IMSMQMessage2_putref_ResponseQueueInfo(This,pqinfoResponse) (This)->lpVtbl->putref_ResponseQueueInfo(This,pqinfoResponse)
#define IMSMQMessage2_get_AdminQueueInfo(This,ppqinfoAdmin) (This)->lpVtbl->get_AdminQueueInfo(This,ppqinfoAdmin)
#define IMSMQMessage2_putref_AdminQueueInfo(This,pqinfoAdmin) (This)->lpVtbl->putref_AdminQueueInfo(This,pqinfoAdmin)
#define IMSMQMessage2_get_ReceivedAuthenticationLevel(This,psReceivedAuthenticationLevel) (This)->lpVtbl->get_ReceivedAuthenticationLevel(This,psReceivedAuthenticationLevel)
#endif
#endif
  HRESULT WINAPI IMSMQMessage2_get_Class_Proxy(IMSMQMessage2 *This,__LONG32 *plClass);
  void __RPC_STUB IMSMQMessage2_get_Class_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_PrivLevel_Proxy(IMSMQMessage2 *This,__LONG32 *plPrivLevel);
  void __RPC_STUB IMSMQMessage2_get_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_PrivLevel_Proxy(IMSMQMessage2 *This,__LONG32 lPrivLevel);
  void __RPC_STUB IMSMQMessage2_put_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_AuthLevel_Proxy(IMSMQMessage2 *This,__LONG32 *plAuthLevel);
  void __RPC_STUB IMSMQMessage2_get_AuthLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_AuthLevel_Proxy(IMSMQMessage2 *This,__LONG32 lAuthLevel);
  void __RPC_STUB IMSMQMessage2_put_AuthLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_IsAuthenticated_Proxy(IMSMQMessage2 *This,Boolean *pisAuthenticated);
  void __RPC_STUB IMSMQMessage2_get_IsAuthenticated_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Delivery_Proxy(IMSMQMessage2 *This,__LONG32 *plDelivery);
  void __RPC_STUB IMSMQMessage2_get_Delivery_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Delivery_Proxy(IMSMQMessage2 *This,__LONG32 lDelivery);
  void __RPC_STUB IMSMQMessage2_put_Delivery_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Trace_Proxy(IMSMQMessage2 *This,__LONG32 *plTrace);
  void __RPC_STUB IMSMQMessage2_get_Trace_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Trace_Proxy(IMSMQMessage2 *This,__LONG32 lTrace);
  void __RPC_STUB IMSMQMessage2_put_Trace_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Priority_Proxy(IMSMQMessage2 *This,__LONG32 *plPriority);
  void __RPC_STUB IMSMQMessage2_get_Priority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Priority_Proxy(IMSMQMessage2 *This,__LONG32 lPriority);
  void __RPC_STUB IMSMQMessage2_put_Priority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Journal_Proxy(IMSMQMessage2 *This,__LONG32 *plJournal);
  void __RPC_STUB IMSMQMessage2_get_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Journal_Proxy(IMSMQMessage2 *This,__LONG32 lJournal);
  void __RPC_STUB IMSMQMessage2_put_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_ResponseQueueInfo_v1_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo **ppqinfoResponse);
  void __RPC_STUB IMSMQMessage2_get_ResponseQueueInfo_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_putref_ResponseQueueInfo_v1_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo *pqinfoResponse);
  void __RPC_STUB IMSMQMessage2_putref_ResponseQueueInfo_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_AppSpecific_Proxy(IMSMQMessage2 *This,__LONG32 *plAppSpecific);
  void __RPC_STUB IMSMQMessage2_get_AppSpecific_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_AppSpecific_Proxy(IMSMQMessage2 *This,__LONG32 lAppSpecific);
  void __RPC_STUB IMSMQMessage2_put_AppSpecific_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_SourceMachineGuid_Proxy(IMSMQMessage2 *This,BSTR *pbstrGuidSrcMachine);
  void __RPC_STUB IMSMQMessage2_get_SourceMachineGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_BodyLength_Proxy(IMSMQMessage2 *This,__LONG32 *pcbBody);
  void __RPC_STUB IMSMQMessage2_get_BodyLength_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Body_Proxy(IMSMQMessage2 *This,VARIANT *pvarBody);
  void __RPC_STUB IMSMQMessage2_get_Body_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Body_Proxy(IMSMQMessage2 *This,VARIANT varBody);
  void __RPC_STUB IMSMQMessage2_put_Body_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_AdminQueueInfo_v1_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo **ppqinfoAdmin);
  void __RPC_STUB IMSMQMessage2_get_AdminQueueInfo_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_putref_AdminQueueInfo_v1_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo *pqinfoAdmin);
  void __RPC_STUB IMSMQMessage2_putref_AdminQueueInfo_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Id_Proxy(IMSMQMessage2 *This,VARIANT *pvarMsgId);
  void __RPC_STUB IMSMQMessage2_get_Id_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_CorrelationId_Proxy(IMSMQMessage2 *This,VARIANT *pvarMsgId);
  void __RPC_STUB IMSMQMessage2_get_CorrelationId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_CorrelationId_Proxy(IMSMQMessage2 *This,VARIANT varMsgId);
  void __RPC_STUB IMSMQMessage2_put_CorrelationId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Ack_Proxy(IMSMQMessage2 *This,__LONG32 *plAck);
  void __RPC_STUB IMSMQMessage2_get_Ack_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Ack_Proxy(IMSMQMessage2 *This,__LONG32 lAck);
  void __RPC_STUB IMSMQMessage2_put_Ack_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Label_Proxy(IMSMQMessage2 *This,BSTR *pbstrLabel);
  void __RPC_STUB IMSMQMessage2_get_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Label_Proxy(IMSMQMessage2 *This,BSTR bstrLabel);
  void __RPC_STUB IMSMQMessage2_put_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_MaxTimeToReachQueue_Proxy(IMSMQMessage2 *This,__LONG32 *plMaxTimeToReachQueue);
  void __RPC_STUB IMSMQMessage2_get_MaxTimeToReachQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_MaxTimeToReachQueue_Proxy(IMSMQMessage2 *This,__LONG32 lMaxTimeToReachQueue);
  void __RPC_STUB IMSMQMessage2_put_MaxTimeToReachQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_MaxTimeToReceive_Proxy(IMSMQMessage2 *This,__LONG32 *plMaxTimeToReceive);
  void __RPC_STUB IMSMQMessage2_get_MaxTimeToReceive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_MaxTimeToReceive_Proxy(IMSMQMessage2 *This,__LONG32 lMaxTimeToReceive);
  void __RPC_STUB IMSMQMessage2_put_MaxTimeToReceive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_HashAlgorithm_Proxy(IMSMQMessage2 *This,__LONG32 *plHashAlg);
  void __RPC_STUB IMSMQMessage2_get_HashAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_HashAlgorithm_Proxy(IMSMQMessage2 *This,__LONG32 lHashAlg);
  void __RPC_STUB IMSMQMessage2_put_HashAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_EncryptAlgorithm_Proxy(IMSMQMessage2 *This,__LONG32 *plEncryptAlg);
  void __RPC_STUB IMSMQMessage2_get_EncryptAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_EncryptAlgorithm_Proxy(IMSMQMessage2 *This,__LONG32 lEncryptAlg);
  void __RPC_STUB IMSMQMessage2_put_EncryptAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_SentTime_Proxy(IMSMQMessage2 *This,VARIANT *pvarSentTime);
  void __RPC_STUB IMSMQMessage2_get_SentTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_ArrivedTime_Proxy(IMSMQMessage2 *This,VARIANT *plArrivedTime);
  void __RPC_STUB IMSMQMessage2_get_ArrivedTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_DestinationQueueInfo_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo2 **ppqinfoDest);
  void __RPC_STUB IMSMQMessage2_get_DestinationQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_SenderCertificate_Proxy(IMSMQMessage2 *This,VARIANT *pvarSenderCert);
  void __RPC_STUB IMSMQMessage2_get_SenderCertificate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_SenderCertificate_Proxy(IMSMQMessage2 *This,VARIANT varSenderCert);
  void __RPC_STUB IMSMQMessage2_put_SenderCertificate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_SenderId_Proxy(IMSMQMessage2 *This,VARIANT *pvarSenderId);
  void __RPC_STUB IMSMQMessage2_get_SenderId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_SenderIdType_Proxy(IMSMQMessage2 *This,__LONG32 *plSenderIdType);
  void __RPC_STUB IMSMQMessage2_get_SenderIdType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_SenderIdType_Proxy(IMSMQMessage2 *This,__LONG32 lSenderIdType);
  void __RPC_STUB IMSMQMessage2_put_SenderIdType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_Send_Proxy(IMSMQMessage2 *This,IMSMQQueue2 *DestinationQueue,VARIANT *Transaction);
  void __RPC_STUB IMSMQMessage2_Send_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_AttachCurrentSecurityContext_Proxy(IMSMQMessage2 *This);
  void __RPC_STUB IMSMQMessage2_AttachCurrentSecurityContext_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_SenderVersion_Proxy(IMSMQMessage2 *This,__LONG32 *plSenderVersion);
  void __RPC_STUB IMSMQMessage2_get_SenderVersion_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Extension_Proxy(IMSMQMessage2 *This,VARIANT *pvarExtension);
  void __RPC_STUB IMSMQMessage2_get_Extension_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Extension_Proxy(IMSMQMessage2 *This,VARIANT varExtension);
  void __RPC_STUB IMSMQMessage2_put_Extension_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_ConnectorTypeGuid_Proxy(IMSMQMessage2 *This,BSTR *pbstrGuidConnectorType);
  void __RPC_STUB IMSMQMessage2_get_ConnectorTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_ConnectorTypeGuid_Proxy(IMSMQMessage2 *This,BSTR bstrGuidConnectorType);
  void __RPC_STUB IMSMQMessage2_put_ConnectorTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_TransactionStatusQueueInfo_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo2 **ppqinfoXactStatus);
  void __RPC_STUB IMSMQMessage2_get_TransactionStatusQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_DestinationSymmetricKey_Proxy(IMSMQMessage2 *This,VARIANT *pvarDestSymmKey);
  void __RPC_STUB IMSMQMessage2_get_DestinationSymmetricKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_DestinationSymmetricKey_Proxy(IMSMQMessage2 *This,VARIANT varDestSymmKey);
  void __RPC_STUB IMSMQMessage2_put_DestinationSymmetricKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Signature_Proxy(IMSMQMessage2 *This,VARIANT *pvarSignature);
  void __RPC_STUB IMSMQMessage2_get_Signature_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_Signature_Proxy(IMSMQMessage2 *This,VARIANT varSignature);
  void __RPC_STUB IMSMQMessage2_put_Signature_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_AuthenticationProviderType_Proxy(IMSMQMessage2 *This,__LONG32 *plAuthProvType);
  void __RPC_STUB IMSMQMessage2_get_AuthenticationProviderType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_AuthenticationProviderType_Proxy(IMSMQMessage2 *This,__LONG32 lAuthProvType);
  void __RPC_STUB IMSMQMessage2_put_AuthenticationProviderType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_AuthenticationProviderName_Proxy(IMSMQMessage2 *This,BSTR *pbstrAuthProvName);
  void __RPC_STUB IMSMQMessage2_get_AuthenticationProviderName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_AuthenticationProviderName_Proxy(IMSMQMessage2 *This,BSTR bstrAuthProvName);
  void __RPC_STUB IMSMQMessage2_put_AuthenticationProviderName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_SenderId_Proxy(IMSMQMessage2 *This,VARIANT varSenderId);
  void __RPC_STUB IMSMQMessage2_put_SenderId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_MsgClass_Proxy(IMSMQMessage2 *This,__LONG32 *plMsgClass);
  void __RPC_STUB IMSMQMessage2_get_MsgClass_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_put_MsgClass_Proxy(IMSMQMessage2 *This,__LONG32 lMsgClass);
  void __RPC_STUB IMSMQMessage2_put_MsgClass_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_Properties_Proxy(IMSMQMessage2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQMessage2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_TransactionId_Proxy(IMSMQMessage2 *This,VARIANT *pvarXactId);
  void __RPC_STUB IMSMQMessage2_get_TransactionId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_IsFirstInTransaction_Proxy(IMSMQMessage2 *This,Boolean *pisFirstInXact);
  void __RPC_STUB IMSMQMessage2_get_IsFirstInTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_IsLastInTransaction_Proxy(IMSMQMessage2 *This,Boolean *pisLastInXact);
  void __RPC_STUB IMSMQMessage2_get_IsLastInTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_ResponseQueueInfo_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo2 **ppqinfoResponse);
  void __RPC_STUB IMSMQMessage2_get_ResponseQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_putref_ResponseQueueInfo_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo2 *pqinfoResponse);
  void __RPC_STUB IMSMQMessage2_putref_ResponseQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_AdminQueueInfo_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo2 **ppqinfoAdmin);
  void __RPC_STUB IMSMQMessage2_get_AdminQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_putref_AdminQueueInfo_Proxy(IMSMQMessage2 *This,IMSMQQueueInfo2 *pqinfoAdmin);
  void __RPC_STUB IMSMQMessage2_putref_AdminQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage2_get_ReceivedAuthenticationLevel_Proxy(IMSMQMessage2 *This,short *psReceivedAuthenticationLevel);
  void __RPC_STUB IMSMQMessage2_get_ReceivedAuthenticationLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQMessage3_INTERFACE_DEFINED__
#define __IMSMQMessage3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQMessage3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQMessage3 : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Class(__LONG32 *plClass) = 0;
    virtual HRESULT WINAPI get_PrivLevel(__LONG32 *plPrivLevel) = 0;
    virtual HRESULT WINAPI put_PrivLevel(__LONG32 lPrivLevel) = 0;
    virtual HRESULT WINAPI get_AuthLevel(__LONG32 *plAuthLevel) = 0;
    virtual HRESULT WINAPI put_AuthLevel(__LONG32 lAuthLevel) = 0;
    virtual HRESULT WINAPI get_IsAuthenticated(Boolean *pisAuthenticated) = 0;
    virtual HRESULT WINAPI get_Delivery(__LONG32 *plDelivery) = 0;
    virtual HRESULT WINAPI put_Delivery(__LONG32 lDelivery) = 0;
    virtual HRESULT WINAPI get_Trace(__LONG32 *plTrace) = 0;
    virtual HRESULT WINAPI put_Trace(__LONG32 lTrace) = 0;
    virtual HRESULT WINAPI get_Priority(__LONG32 *plPriority) = 0;
    virtual HRESULT WINAPI put_Priority(__LONG32 lPriority) = 0;
    virtual HRESULT WINAPI get_Journal(__LONG32 *plJournal) = 0;
    virtual HRESULT WINAPI put_Journal(__LONG32 lJournal) = 0;
    virtual HRESULT WINAPI get_ResponseQueueInfo_v1(IMSMQQueueInfo **ppqinfoResponse) = 0;
    virtual HRESULT WINAPI putref_ResponseQueueInfo_v1(IMSMQQueueInfo *pqinfoResponse) = 0;
    virtual HRESULT WINAPI get_AppSpecific(__LONG32 *plAppSpecific) = 0;
    virtual HRESULT WINAPI put_AppSpecific(__LONG32 lAppSpecific) = 0;
    virtual HRESULT WINAPI get_SourceMachineGuid(BSTR *pbstrGuidSrcMachine) = 0;
    virtual HRESULT WINAPI get_BodyLength(__LONG32 *pcbBody) = 0;
    virtual HRESULT WINAPI get_Body(VARIANT *pvarBody) = 0;
    virtual HRESULT WINAPI put_Body(VARIANT varBody) = 0;
    virtual HRESULT WINAPI get_AdminQueueInfo_v1(IMSMQQueueInfo **ppqinfoAdmin) = 0;
    virtual HRESULT WINAPI putref_AdminQueueInfo_v1(IMSMQQueueInfo *pqinfoAdmin) = 0;
    virtual HRESULT WINAPI get_Id(VARIANT *pvarMsgId) = 0;
    virtual HRESULT WINAPI get_CorrelationId(VARIANT *pvarMsgId) = 0;
    virtual HRESULT WINAPI put_CorrelationId(VARIANT varMsgId) = 0;
    virtual HRESULT WINAPI get_Ack(__LONG32 *plAck) = 0;
    virtual HRESULT WINAPI put_Ack(__LONG32 lAck) = 0;
    virtual HRESULT WINAPI get_Label(BSTR *pbstrLabel) = 0;
    virtual HRESULT WINAPI put_Label(BSTR bstrLabel) = 0;
    virtual HRESULT WINAPI get_MaxTimeToReachQueue(__LONG32 *plMaxTimeToReachQueue) = 0;
    virtual HRESULT WINAPI put_MaxTimeToReachQueue(__LONG32 lMaxTimeToReachQueue) = 0;
    virtual HRESULT WINAPI get_MaxTimeToReceive(__LONG32 *plMaxTimeToReceive) = 0;
    virtual HRESULT WINAPI put_MaxTimeToReceive(__LONG32 lMaxTimeToReceive) = 0;
    virtual HRESULT WINAPI get_HashAlgorithm(__LONG32 *plHashAlg) = 0;
    virtual HRESULT WINAPI put_HashAlgorithm(__LONG32 lHashAlg) = 0;
    virtual HRESULT WINAPI get_EncryptAlgorithm(__LONG32 *plEncryptAlg) = 0;
    virtual HRESULT WINAPI put_EncryptAlgorithm(__LONG32 lEncryptAlg) = 0;
    virtual HRESULT WINAPI get_SentTime(VARIANT *pvarSentTime) = 0;
    virtual HRESULT WINAPI get_ArrivedTime(VARIANT *plArrivedTime) = 0;
    virtual HRESULT WINAPI get_DestinationQueueInfo(IMSMQQueueInfo3 **ppqinfoDest) = 0;
    virtual HRESULT WINAPI get_SenderCertificate(VARIANT *pvarSenderCert) = 0;
    virtual HRESULT WINAPI put_SenderCertificate(VARIANT varSenderCert) = 0;
    virtual HRESULT WINAPI get_SenderId(VARIANT *pvarSenderId) = 0;
    virtual HRESULT WINAPI get_SenderIdType(__LONG32 *plSenderIdType) = 0;
    virtual HRESULT WINAPI put_SenderIdType(__LONG32 lSenderIdType) = 0;
    virtual HRESULT WINAPI Send(IDispatch *DestinationQueue,VARIANT *Transaction) = 0;
    virtual HRESULT WINAPI AttachCurrentSecurityContext(void) = 0;
    virtual HRESULT WINAPI get_SenderVersion(__LONG32 *plSenderVersion) = 0;
    virtual HRESULT WINAPI get_Extension(VARIANT *pvarExtension) = 0;
    virtual HRESULT WINAPI put_Extension(VARIANT varExtension) = 0;
    virtual HRESULT WINAPI get_ConnectorTypeGuid(BSTR *pbstrGuidConnectorType) = 0;
    virtual HRESULT WINAPI put_ConnectorTypeGuid(BSTR bstrGuidConnectorType) = 0;
    virtual HRESULT WINAPI get_TransactionStatusQueueInfo(IMSMQQueueInfo3 **ppqinfoXactStatus) = 0;
    virtual HRESULT WINAPI get_DestinationSymmetricKey(VARIANT *pvarDestSymmKey) = 0;
    virtual HRESULT WINAPI put_DestinationSymmetricKey(VARIANT varDestSymmKey) = 0;
    virtual HRESULT WINAPI get_Signature(VARIANT *pvarSignature) = 0;
    virtual HRESULT WINAPI put_Signature(VARIANT varSignature) = 0;
    virtual HRESULT WINAPI get_AuthenticationProviderType(__LONG32 *plAuthProvType) = 0;
    virtual HRESULT WINAPI put_AuthenticationProviderType(__LONG32 lAuthProvType) = 0;
    virtual HRESULT WINAPI get_AuthenticationProviderName(BSTR *pbstrAuthProvName) = 0;
    virtual HRESULT WINAPI put_AuthenticationProviderName(BSTR bstrAuthProvName) = 0;
    virtual HRESULT WINAPI put_SenderId(VARIANT varSenderId) = 0;
    virtual HRESULT WINAPI get_MsgClass(__LONG32 *plMsgClass) = 0;
    virtual HRESULT WINAPI put_MsgClass(__LONG32 lMsgClass) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
    virtual HRESULT WINAPI get_TransactionId(VARIANT *pvarXactId) = 0;
    virtual HRESULT WINAPI get_IsFirstInTransaction(Boolean *pisFirstInXact) = 0;
    virtual HRESULT WINAPI get_IsLastInTransaction(Boolean *pisLastInXact) = 0;
    virtual HRESULT WINAPI get_ResponseQueueInfo_v2(IMSMQQueueInfo2 **ppqinfoResponse) = 0;
    virtual HRESULT WINAPI putref_ResponseQueueInfo_v2(IMSMQQueueInfo2 *pqinfoResponse) = 0;
    virtual HRESULT WINAPI get_AdminQueueInfo_v2(IMSMQQueueInfo2 **ppqinfoAdmin) = 0;
    virtual HRESULT WINAPI putref_AdminQueueInfo_v2(IMSMQQueueInfo2 *pqinfoAdmin) = 0;
    virtual HRESULT WINAPI get_ReceivedAuthenticationLevel(short *psReceivedAuthenticationLevel) = 0;
    virtual HRESULT WINAPI get_ResponseQueueInfo(IMSMQQueueInfo3 **ppqinfoResponse) = 0;
    virtual HRESULT WINAPI putref_ResponseQueueInfo(IMSMQQueueInfo3 *pqinfoResponse) = 0;
    virtual HRESULT WINAPI get_AdminQueueInfo(IMSMQQueueInfo3 **ppqinfoAdmin) = 0;
    virtual HRESULT WINAPI putref_AdminQueueInfo(IMSMQQueueInfo3 *pqinfoAdmin) = 0;
    virtual HRESULT WINAPI get_ResponseDestination(IDispatch **ppdestResponse) = 0;
    virtual HRESULT WINAPI putref_ResponseDestination(IDispatch *pdestResponse) = 0;
    virtual HRESULT WINAPI get_Destination(IDispatch **ppdestDestination) = 0;
    virtual HRESULT WINAPI get_LookupId(VARIANT *pvarLookupId) = 0;
    virtual HRESULT WINAPI get_IsAuthenticated2(VARIANT_BOOL *pisAuthenticated) = 0;
    virtual HRESULT WINAPI get_IsFirstInTransaction2(VARIANT_BOOL *pisFirstInXact) = 0;
    virtual HRESULT WINAPI get_IsLastInTransaction2(VARIANT_BOOL *pisLastInXact) = 0;
    virtual HRESULT WINAPI AttachCurrentSecurityContext2(void) = 0;
    virtual HRESULT WINAPI get_SoapEnvelope(BSTR *pbstrSoapEnvelope) = 0;
    virtual HRESULT WINAPI get_CompoundMessage(VARIANT *pvarCompoundMessage) = 0;
    virtual HRESULT WINAPI put_SoapHeader(BSTR bstrSoapHeader) = 0;
    virtual HRESULT WINAPI put_SoapBody(BSTR bstrSoapBody) = 0;
  };
#else
  typedef struct IMSMQMessage3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQMessage3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQMessage3 *This);
      ULONG (WINAPI *Release)(IMSMQMessage3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQMessage3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQMessage3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQMessage3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQMessage3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Class)(IMSMQMessage3 *This,__LONG32 *plClass);
      HRESULT (WINAPI *get_PrivLevel)(IMSMQMessage3 *This,__LONG32 *plPrivLevel);
      HRESULT (WINAPI *put_PrivLevel)(IMSMQMessage3 *This,__LONG32 lPrivLevel);
      HRESULT (WINAPI *get_AuthLevel)(IMSMQMessage3 *This,__LONG32 *plAuthLevel);
      HRESULT (WINAPI *put_AuthLevel)(IMSMQMessage3 *This,__LONG32 lAuthLevel);
      HRESULT (WINAPI *get_IsAuthenticated)(IMSMQMessage3 *This,Boolean *pisAuthenticated);
      HRESULT (WINAPI *get_Delivery)(IMSMQMessage3 *This,__LONG32 *plDelivery);
      HRESULT (WINAPI *put_Delivery)(IMSMQMessage3 *This,__LONG32 lDelivery);
      HRESULT (WINAPI *get_Trace)(IMSMQMessage3 *This,__LONG32 *plTrace);
      HRESULT (WINAPI *put_Trace)(IMSMQMessage3 *This,__LONG32 lTrace);
      HRESULT (WINAPI *get_Priority)(IMSMQMessage3 *This,__LONG32 *plPriority);
      HRESULT (WINAPI *put_Priority)(IMSMQMessage3 *This,__LONG32 lPriority);
      HRESULT (WINAPI *get_Journal)(IMSMQMessage3 *This,__LONG32 *plJournal);
      HRESULT (WINAPI *put_Journal)(IMSMQMessage3 *This,__LONG32 lJournal);
      HRESULT (WINAPI *get_ResponseQueueInfo_v1)(IMSMQMessage3 *This,IMSMQQueueInfo **ppqinfoResponse);
      HRESULT (WINAPI *putref_ResponseQueueInfo_v1)(IMSMQMessage3 *This,IMSMQQueueInfo *pqinfoResponse);
      HRESULT (WINAPI *get_AppSpecific)(IMSMQMessage3 *This,__LONG32 *plAppSpecific);
      HRESULT (WINAPI *put_AppSpecific)(IMSMQMessage3 *This,__LONG32 lAppSpecific);
      HRESULT (WINAPI *get_SourceMachineGuid)(IMSMQMessage3 *This,BSTR *pbstrGuidSrcMachine);
      HRESULT (WINAPI *get_BodyLength)(IMSMQMessage3 *This,__LONG32 *pcbBody);
      HRESULT (WINAPI *get_Body)(IMSMQMessage3 *This,VARIANT *pvarBody);
      HRESULT (WINAPI *put_Body)(IMSMQMessage3 *This,VARIANT varBody);
      HRESULT (WINAPI *get_AdminQueueInfo_v1)(IMSMQMessage3 *This,IMSMQQueueInfo **ppqinfoAdmin);
      HRESULT (WINAPI *putref_AdminQueueInfo_v1)(IMSMQMessage3 *This,IMSMQQueueInfo *pqinfoAdmin);
      HRESULT (WINAPI *get_Id)(IMSMQMessage3 *This,VARIANT *pvarMsgId);
      HRESULT (WINAPI *get_CorrelationId)(IMSMQMessage3 *This,VARIANT *pvarMsgId);
      HRESULT (WINAPI *put_CorrelationId)(IMSMQMessage3 *This,VARIANT varMsgId);
      HRESULT (WINAPI *get_Ack)(IMSMQMessage3 *This,__LONG32 *plAck);
      HRESULT (WINAPI *put_Ack)(IMSMQMessage3 *This,__LONG32 lAck);
      HRESULT (WINAPI *get_Label)(IMSMQMessage3 *This,BSTR *pbstrLabel);
      HRESULT (WINAPI *put_Label)(IMSMQMessage3 *This,BSTR bstrLabel);
      HRESULT (WINAPI *get_MaxTimeToReachQueue)(IMSMQMessage3 *This,__LONG32 *plMaxTimeToReachQueue);
      HRESULT (WINAPI *put_MaxTimeToReachQueue)(IMSMQMessage3 *This,__LONG32 lMaxTimeToReachQueue);
      HRESULT (WINAPI *get_MaxTimeToReceive)(IMSMQMessage3 *This,__LONG32 *plMaxTimeToReceive);
      HRESULT (WINAPI *put_MaxTimeToReceive)(IMSMQMessage3 *This,__LONG32 lMaxTimeToReceive);
      HRESULT (WINAPI *get_HashAlgorithm)(IMSMQMessage3 *This,__LONG32 *plHashAlg);
      HRESULT (WINAPI *put_HashAlgorithm)(IMSMQMessage3 *This,__LONG32 lHashAlg);
      HRESULT (WINAPI *get_EncryptAlgorithm)(IMSMQMessage3 *This,__LONG32 *plEncryptAlg);
      HRESULT (WINAPI *put_EncryptAlgorithm)(IMSMQMessage3 *This,__LONG32 lEncryptAlg);
      HRESULT (WINAPI *get_SentTime)(IMSMQMessage3 *This,VARIANT *pvarSentTime);
      HRESULT (WINAPI *get_ArrivedTime)(IMSMQMessage3 *This,VARIANT *plArrivedTime);
      HRESULT (WINAPI *get_DestinationQueueInfo)(IMSMQMessage3 *This,IMSMQQueueInfo3 **ppqinfoDest);
      HRESULT (WINAPI *get_SenderCertificate)(IMSMQMessage3 *This,VARIANT *pvarSenderCert);
      HRESULT (WINAPI *put_SenderCertificate)(IMSMQMessage3 *This,VARIANT varSenderCert);
      HRESULT (WINAPI *get_SenderId)(IMSMQMessage3 *This,VARIANT *pvarSenderId);
      HRESULT (WINAPI *get_SenderIdType)(IMSMQMessage3 *This,__LONG32 *plSenderIdType);
      HRESULT (WINAPI *put_SenderIdType)(IMSMQMessage3 *This,__LONG32 lSenderIdType);
      HRESULT (WINAPI *Send)(IMSMQMessage3 *This,IDispatch *DestinationQueue,VARIANT *Transaction);
      HRESULT (WINAPI *AttachCurrentSecurityContext)(IMSMQMessage3 *This);
      HRESULT (WINAPI *get_SenderVersion)(IMSMQMessage3 *This,__LONG32 *plSenderVersion);
      HRESULT (WINAPI *get_Extension)(IMSMQMessage3 *This,VARIANT *pvarExtension);
      HRESULT (WINAPI *put_Extension)(IMSMQMessage3 *This,VARIANT varExtension);
      HRESULT (WINAPI *get_ConnectorTypeGuid)(IMSMQMessage3 *This,BSTR *pbstrGuidConnectorType);
      HRESULT (WINAPI *put_ConnectorTypeGuid)(IMSMQMessage3 *This,BSTR bstrGuidConnectorType);
      HRESULT (WINAPI *get_TransactionStatusQueueInfo)(IMSMQMessage3 *This,IMSMQQueueInfo3 **ppqinfoXactStatus);
      HRESULT (WINAPI *get_DestinationSymmetricKey)(IMSMQMessage3 *This,VARIANT *pvarDestSymmKey);
      HRESULT (WINAPI *put_DestinationSymmetricKey)(IMSMQMessage3 *This,VARIANT varDestSymmKey);
      HRESULT (WINAPI *get_Signature)(IMSMQMessage3 *This,VARIANT *pvarSignature);
      HRESULT (WINAPI *put_Signature)(IMSMQMessage3 *This,VARIANT varSignature);
      HRESULT (WINAPI *get_AuthenticationProviderType)(IMSMQMessage3 *This,__LONG32 *plAuthProvType);
      HRESULT (WINAPI *put_AuthenticationProviderType)(IMSMQMessage3 *This,__LONG32 lAuthProvType);
      HRESULT (WINAPI *get_AuthenticationProviderName)(IMSMQMessage3 *This,BSTR *pbstrAuthProvName);
      HRESULT (WINAPI *put_AuthenticationProviderName)(IMSMQMessage3 *This,BSTR bstrAuthProvName);
      HRESULT (WINAPI *put_SenderId)(IMSMQMessage3 *This,VARIANT varSenderId);
      HRESULT (WINAPI *get_MsgClass)(IMSMQMessage3 *This,__LONG32 *plMsgClass);
      HRESULT (WINAPI *put_MsgClass)(IMSMQMessage3 *This,__LONG32 lMsgClass);
      HRESULT (WINAPI *get_Properties)(IMSMQMessage3 *This,IDispatch **ppcolProperties);
      HRESULT (WINAPI *get_TransactionId)(IMSMQMessage3 *This,VARIANT *pvarXactId);
      HRESULT (WINAPI *get_IsFirstInTransaction)(IMSMQMessage3 *This,Boolean *pisFirstInXact);
      HRESULT (WINAPI *get_IsLastInTransaction)(IMSMQMessage3 *This,Boolean *pisLastInXact);
      HRESULT (WINAPI *get_ResponseQueueInfo_v2)(IMSMQMessage3 *This,IMSMQQueueInfo2 **ppqinfoResponse);
      HRESULT (WINAPI *putref_ResponseQueueInfo_v2)(IMSMQMessage3 *This,IMSMQQueueInfo2 *pqinfoResponse);
      HRESULT (WINAPI *get_AdminQueueInfo_v2)(IMSMQMessage3 *This,IMSMQQueueInfo2 **ppqinfoAdmin);
      HRESULT (WINAPI *putref_AdminQueueInfo_v2)(IMSMQMessage3 *This,IMSMQQueueInfo2 *pqinfoAdmin);
      HRESULT (WINAPI *get_ReceivedAuthenticationLevel)(IMSMQMessage3 *This,short *psReceivedAuthenticationLevel);
      HRESULT (WINAPI *get_ResponseQueueInfo)(IMSMQMessage3 *This,IMSMQQueueInfo3 **ppqinfoResponse);
      HRESULT (WINAPI *putref_ResponseQueueInfo)(IMSMQMessage3 *This,IMSMQQueueInfo3 *pqinfoResponse);
      HRESULT (WINAPI *get_AdminQueueInfo)(IMSMQMessage3 *This,IMSMQQueueInfo3 **ppqinfoAdmin);
      HRESULT (WINAPI *putref_AdminQueueInfo)(IMSMQMessage3 *This,IMSMQQueueInfo3 *pqinfoAdmin);
      HRESULT (WINAPI *get_ResponseDestination)(IMSMQMessage3 *This,IDispatch **ppdestResponse);
      HRESULT (WINAPI *putref_ResponseDestination)(IMSMQMessage3 *This,IDispatch *pdestResponse);
      HRESULT (WINAPI *get_Destination)(IMSMQMessage3 *This,IDispatch **ppdestDestination);
      HRESULT (WINAPI *get_LookupId)(IMSMQMessage3 *This,VARIANT *pvarLookupId);
      HRESULT (WINAPI *get_IsAuthenticated2)(IMSMQMessage3 *This,VARIANT_BOOL *pisAuthenticated);
      HRESULT (WINAPI *get_IsFirstInTransaction2)(IMSMQMessage3 *This,VARIANT_BOOL *pisFirstInXact);
      HRESULT (WINAPI *get_IsLastInTransaction2)(IMSMQMessage3 *This,VARIANT_BOOL *pisLastInXact);
      HRESULT (WINAPI *AttachCurrentSecurityContext2)(IMSMQMessage3 *This);
      HRESULT (WINAPI *get_SoapEnvelope)(IMSMQMessage3 *This,BSTR *pbstrSoapEnvelope);
      HRESULT (WINAPI *get_CompoundMessage)(IMSMQMessage3 *This,VARIANT *pvarCompoundMessage);
      HRESULT (WINAPI *put_SoapHeader)(IMSMQMessage3 *This,BSTR bstrSoapHeader);
      HRESULT (WINAPI *put_SoapBody)(IMSMQMessage3 *This,BSTR bstrSoapBody);
    END_INTERFACE
  } IMSMQMessage3Vtbl;
  struct IMSMQMessage3 {
    CONST_VTBL struct IMSMQMessage3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQMessage3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQMessage3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQMessage3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQMessage3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQMessage3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQMessage3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQMessage3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQMessage3_get_Class(This,plClass) (This)->lpVtbl->get_Class(This,plClass)
#define IMSMQMessage3_get_PrivLevel(This,plPrivLevel) (This)->lpVtbl->get_PrivLevel(This,plPrivLevel)
#define IMSMQMessage3_put_PrivLevel(This,lPrivLevel) (This)->lpVtbl->put_PrivLevel(This,lPrivLevel)
#define IMSMQMessage3_get_AuthLevel(This,plAuthLevel) (This)->lpVtbl->get_AuthLevel(This,plAuthLevel)
#define IMSMQMessage3_put_AuthLevel(This,lAuthLevel) (This)->lpVtbl->put_AuthLevel(This,lAuthLevel)
#define IMSMQMessage3_get_IsAuthenticated(This,pisAuthenticated) (This)->lpVtbl->get_IsAuthenticated(This,pisAuthenticated)
#define IMSMQMessage3_get_Delivery(This,plDelivery) (This)->lpVtbl->get_Delivery(This,plDelivery)
#define IMSMQMessage3_put_Delivery(This,lDelivery) (This)->lpVtbl->put_Delivery(This,lDelivery)
#define IMSMQMessage3_get_Trace(This,plTrace) (This)->lpVtbl->get_Trace(This,plTrace)
#define IMSMQMessage3_put_Trace(This,lTrace) (This)->lpVtbl->put_Trace(This,lTrace)
#define IMSMQMessage3_get_Priority(This,plPriority) (This)->lpVtbl->get_Priority(This,plPriority)
#define IMSMQMessage3_put_Priority(This,lPriority) (This)->lpVtbl->put_Priority(This,lPriority)
#define IMSMQMessage3_get_Journal(This,plJournal) (This)->lpVtbl->get_Journal(This,plJournal)
#define IMSMQMessage3_put_Journal(This,lJournal) (This)->lpVtbl->put_Journal(This,lJournal)
#define IMSMQMessage3_get_ResponseQueueInfo_v1(This,ppqinfoResponse) (This)->lpVtbl->get_ResponseQueueInfo_v1(This,ppqinfoResponse)
#define IMSMQMessage3_putref_ResponseQueueInfo_v1(This,pqinfoResponse) (This)->lpVtbl->putref_ResponseQueueInfo_v1(This,pqinfoResponse)
#define IMSMQMessage3_get_AppSpecific(This,plAppSpecific) (This)->lpVtbl->get_AppSpecific(This,plAppSpecific)
#define IMSMQMessage3_put_AppSpecific(This,lAppSpecific) (This)->lpVtbl->put_AppSpecific(This,lAppSpecific)
#define IMSMQMessage3_get_SourceMachineGuid(This,pbstrGuidSrcMachine) (This)->lpVtbl->get_SourceMachineGuid(This,pbstrGuidSrcMachine)
#define IMSMQMessage3_get_BodyLength(This,pcbBody) (This)->lpVtbl->get_BodyLength(This,pcbBody)
#define IMSMQMessage3_get_Body(This,pvarBody) (This)->lpVtbl->get_Body(This,pvarBody)
#define IMSMQMessage3_put_Body(This,varBody) (This)->lpVtbl->put_Body(This,varBody)
#define IMSMQMessage3_get_AdminQueueInfo_v1(This,ppqinfoAdmin) (This)->lpVtbl->get_AdminQueueInfo_v1(This,ppqinfoAdmin)
#define IMSMQMessage3_putref_AdminQueueInfo_v1(This,pqinfoAdmin) (This)->lpVtbl->putref_AdminQueueInfo_v1(This,pqinfoAdmin)
#define IMSMQMessage3_get_Id(This,pvarMsgId) (This)->lpVtbl->get_Id(This,pvarMsgId)
#define IMSMQMessage3_get_CorrelationId(This,pvarMsgId) (This)->lpVtbl->get_CorrelationId(This,pvarMsgId)
#define IMSMQMessage3_put_CorrelationId(This,varMsgId) (This)->lpVtbl->put_CorrelationId(This,varMsgId)
#define IMSMQMessage3_get_Ack(This,plAck) (This)->lpVtbl->get_Ack(This,plAck)
#define IMSMQMessage3_put_Ack(This,lAck) (This)->lpVtbl->put_Ack(This,lAck)
#define IMSMQMessage3_get_Label(This,pbstrLabel) (This)->lpVtbl->get_Label(This,pbstrLabel)
#define IMSMQMessage3_put_Label(This,bstrLabel) (This)->lpVtbl->put_Label(This,bstrLabel)
#define IMSMQMessage3_get_MaxTimeToReachQueue(This,plMaxTimeToReachQueue) (This)->lpVtbl->get_MaxTimeToReachQueue(This,plMaxTimeToReachQueue)
#define IMSMQMessage3_put_MaxTimeToReachQueue(This,lMaxTimeToReachQueue) (This)->lpVtbl->put_MaxTimeToReachQueue(This,lMaxTimeToReachQueue)
#define IMSMQMessage3_get_MaxTimeToReceive(This,plMaxTimeToReceive) (This)->lpVtbl->get_MaxTimeToReceive(This,plMaxTimeToReceive)
#define IMSMQMessage3_put_MaxTimeToReceive(This,lMaxTimeToReceive) (This)->lpVtbl->put_MaxTimeToReceive(This,lMaxTimeToReceive)
#define IMSMQMessage3_get_HashAlgorithm(This,plHashAlg) (This)->lpVtbl->get_HashAlgorithm(This,plHashAlg)
#define IMSMQMessage3_put_HashAlgorithm(This,lHashAlg) (This)->lpVtbl->put_HashAlgorithm(This,lHashAlg)
#define IMSMQMessage3_get_EncryptAlgorithm(This,plEncryptAlg) (This)->lpVtbl->get_EncryptAlgorithm(This,plEncryptAlg)
#define IMSMQMessage3_put_EncryptAlgorithm(This,lEncryptAlg) (This)->lpVtbl->put_EncryptAlgorithm(This,lEncryptAlg)
#define IMSMQMessage3_get_SentTime(This,pvarSentTime) (This)->lpVtbl->get_SentTime(This,pvarSentTime)
#define IMSMQMessage3_get_ArrivedTime(This,plArrivedTime) (This)->lpVtbl->get_ArrivedTime(This,plArrivedTime)
#define IMSMQMessage3_get_DestinationQueueInfo(This,ppqinfoDest) (This)->lpVtbl->get_DestinationQueueInfo(This,ppqinfoDest)
#define IMSMQMessage3_get_SenderCertificate(This,pvarSenderCert) (This)->lpVtbl->get_SenderCertificate(This,pvarSenderCert)
#define IMSMQMessage3_put_SenderCertificate(This,varSenderCert) (This)->lpVtbl->put_SenderCertificate(This,varSenderCert)
#define IMSMQMessage3_get_SenderId(This,pvarSenderId) (This)->lpVtbl->get_SenderId(This,pvarSenderId)
#define IMSMQMessage3_get_SenderIdType(This,plSenderIdType) (This)->lpVtbl->get_SenderIdType(This,plSenderIdType)
#define IMSMQMessage3_put_SenderIdType(This,lSenderIdType) (This)->lpVtbl->put_SenderIdType(This,lSenderIdType)
#define IMSMQMessage3_Send(This,DestinationQueue,Transaction) (This)->lpVtbl->Send(This,DestinationQueue,Transaction)
#define IMSMQMessage3_AttachCurrentSecurityContext(This) (This)->lpVtbl->AttachCurrentSecurityContext(This)
#define IMSMQMessage3_get_SenderVersion(This,plSenderVersion) (This)->lpVtbl->get_SenderVersion(This,plSenderVersion)
#define IMSMQMessage3_get_Extension(This,pvarExtension) (This)->lpVtbl->get_Extension(This,pvarExtension)
#define IMSMQMessage3_put_Extension(This,varExtension) (This)->lpVtbl->put_Extension(This,varExtension)
#define IMSMQMessage3_get_ConnectorTypeGuid(This,pbstrGuidConnectorType) (This)->lpVtbl->get_ConnectorTypeGuid(This,pbstrGuidConnectorType)
#define IMSMQMessage3_put_ConnectorTypeGuid(This,bstrGuidConnectorType) (This)->lpVtbl->put_ConnectorTypeGuid(This,bstrGuidConnectorType)
#define IMSMQMessage3_get_TransactionStatusQueueInfo(This,ppqinfoXactStatus) (This)->lpVtbl->get_TransactionStatusQueueInfo(This,ppqinfoXactStatus)
#define IMSMQMessage3_get_DestinationSymmetricKey(This,pvarDestSymmKey) (This)->lpVtbl->get_DestinationSymmetricKey(This,pvarDestSymmKey)
#define IMSMQMessage3_put_DestinationSymmetricKey(This,varDestSymmKey) (This)->lpVtbl->put_DestinationSymmetricKey(This,varDestSymmKey)
#define IMSMQMessage3_get_Signature(This,pvarSignature) (This)->lpVtbl->get_Signature(This,pvarSignature)
#define IMSMQMessage3_put_Signature(This,varSignature) (This)->lpVtbl->put_Signature(This,varSignature)
#define IMSMQMessage3_get_AuthenticationProviderType(This,plAuthProvType) (This)->lpVtbl->get_AuthenticationProviderType(This,plAuthProvType)
#define IMSMQMessage3_put_AuthenticationProviderType(This,lAuthProvType) (This)->lpVtbl->put_AuthenticationProviderType(This,lAuthProvType)
#define IMSMQMessage3_get_AuthenticationProviderName(This,pbstrAuthProvName) (This)->lpVtbl->get_AuthenticationProviderName(This,pbstrAuthProvName)
#define IMSMQMessage3_put_AuthenticationProviderName(This,bstrAuthProvName) (This)->lpVtbl->put_AuthenticationProviderName(This,bstrAuthProvName)
#define IMSMQMessage3_put_SenderId(This,varSenderId) (This)->lpVtbl->put_SenderId(This,varSenderId)
#define IMSMQMessage3_get_MsgClass(This,plMsgClass) (This)->lpVtbl->get_MsgClass(This,plMsgClass)
#define IMSMQMessage3_put_MsgClass(This,lMsgClass) (This)->lpVtbl->put_MsgClass(This,lMsgClass)
#define IMSMQMessage3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#define IMSMQMessage3_get_TransactionId(This,pvarXactId) (This)->lpVtbl->get_TransactionId(This,pvarXactId)
#define IMSMQMessage3_get_IsFirstInTransaction(This,pisFirstInXact) (This)->lpVtbl->get_IsFirstInTransaction(This,pisFirstInXact)
#define IMSMQMessage3_get_IsLastInTransaction(This,pisLastInXact) (This)->lpVtbl->get_IsLastInTransaction(This,pisLastInXact)
#define IMSMQMessage3_get_ResponseQueueInfo_v2(This,ppqinfoResponse) (This)->lpVtbl->get_ResponseQueueInfo_v2(This,ppqinfoResponse)
#define IMSMQMessage3_putref_ResponseQueueInfo_v2(This,pqinfoResponse) (This)->lpVtbl->putref_ResponseQueueInfo_v2(This,pqinfoResponse)
#define IMSMQMessage3_get_AdminQueueInfo_v2(This,ppqinfoAdmin) (This)->lpVtbl->get_AdminQueueInfo_v2(This,ppqinfoAdmin)
#define IMSMQMessage3_putref_AdminQueueInfo_v2(This,pqinfoAdmin) (This)->lpVtbl->putref_AdminQueueInfo_v2(This,pqinfoAdmin)
#define IMSMQMessage3_get_ReceivedAuthenticationLevel(This,psReceivedAuthenticationLevel) (This)->lpVtbl->get_ReceivedAuthenticationLevel(This,psReceivedAuthenticationLevel)
#define IMSMQMessage3_get_ResponseQueueInfo(This,ppqinfoResponse) (This)->lpVtbl->get_ResponseQueueInfo(This,ppqinfoResponse)
#define IMSMQMessage3_putref_ResponseQueueInfo(This,pqinfoResponse) (This)->lpVtbl->putref_ResponseQueueInfo(This,pqinfoResponse)
#define IMSMQMessage3_get_AdminQueueInfo(This,ppqinfoAdmin) (This)->lpVtbl->get_AdminQueueInfo(This,ppqinfoAdmin)
#define IMSMQMessage3_putref_AdminQueueInfo(This,pqinfoAdmin) (This)->lpVtbl->putref_AdminQueueInfo(This,pqinfoAdmin)
#define IMSMQMessage3_get_ResponseDestination(This,ppdestResponse) (This)->lpVtbl->get_ResponseDestination(This,ppdestResponse)
#define IMSMQMessage3_putref_ResponseDestination(This,pdestResponse) (This)->lpVtbl->putref_ResponseDestination(This,pdestResponse)
#define IMSMQMessage3_get_Destination(This,ppdestDestination) (This)->lpVtbl->get_Destination(This,ppdestDestination)
#define IMSMQMessage3_get_LookupId(This,pvarLookupId) (This)->lpVtbl->get_LookupId(This,pvarLookupId)
#define IMSMQMessage3_get_IsAuthenticated2(This,pisAuthenticated) (This)->lpVtbl->get_IsAuthenticated2(This,pisAuthenticated)
#define IMSMQMessage3_get_IsFirstInTransaction2(This,pisFirstInXact) (This)->lpVtbl->get_IsFirstInTransaction2(This,pisFirstInXact)
#define IMSMQMessage3_get_IsLastInTransaction2(This,pisLastInXact) (This)->lpVtbl->get_IsLastInTransaction2(This,pisLastInXact)
#define IMSMQMessage3_AttachCurrentSecurityContext2(This) (This)->lpVtbl->AttachCurrentSecurityContext2(This)
#define IMSMQMessage3_get_SoapEnvelope(This,pbstrSoapEnvelope) (This)->lpVtbl->get_SoapEnvelope(This,pbstrSoapEnvelope)
#define IMSMQMessage3_get_CompoundMessage(This,pvarCompoundMessage) (This)->lpVtbl->get_CompoundMessage(This,pvarCompoundMessage)
#define IMSMQMessage3_put_SoapHeader(This,bstrSoapHeader) (This)->lpVtbl->put_SoapHeader(This,bstrSoapHeader)
#define IMSMQMessage3_put_SoapBody(This,bstrSoapBody) (This)->lpVtbl->put_SoapBody(This,bstrSoapBody)
#endif
#endif
  HRESULT WINAPI IMSMQMessage3_get_Class_Proxy(IMSMQMessage3 *This,__LONG32 *plClass);
  void __RPC_STUB IMSMQMessage3_get_Class_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_PrivLevel_Proxy(IMSMQMessage3 *This,__LONG32 *plPrivLevel);
  void __RPC_STUB IMSMQMessage3_get_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_PrivLevel_Proxy(IMSMQMessage3 *This,__LONG32 lPrivLevel);
  void __RPC_STUB IMSMQMessage3_put_PrivLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_AuthLevel_Proxy(IMSMQMessage3 *This,__LONG32 *plAuthLevel);
  void __RPC_STUB IMSMQMessage3_get_AuthLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_AuthLevel_Proxy(IMSMQMessage3 *This,__LONG32 lAuthLevel);
  void __RPC_STUB IMSMQMessage3_put_AuthLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_IsAuthenticated_Proxy(IMSMQMessage3 *This,Boolean *pisAuthenticated);
  void __RPC_STUB IMSMQMessage3_get_IsAuthenticated_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Delivery_Proxy(IMSMQMessage3 *This,__LONG32 *plDelivery);
  void __RPC_STUB IMSMQMessage3_get_Delivery_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Delivery_Proxy(IMSMQMessage3 *This,__LONG32 lDelivery);
  void __RPC_STUB IMSMQMessage3_put_Delivery_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Trace_Proxy(IMSMQMessage3 *This,__LONG32 *plTrace);
  void __RPC_STUB IMSMQMessage3_get_Trace_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Trace_Proxy(IMSMQMessage3 *This,__LONG32 lTrace);
  void __RPC_STUB IMSMQMessage3_put_Trace_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Priority_Proxy(IMSMQMessage3 *This,__LONG32 *plPriority);
  void __RPC_STUB IMSMQMessage3_get_Priority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Priority_Proxy(IMSMQMessage3 *This,__LONG32 lPriority);
  void __RPC_STUB IMSMQMessage3_put_Priority_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Journal_Proxy(IMSMQMessage3 *This,__LONG32 *plJournal);
  void __RPC_STUB IMSMQMessage3_get_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Journal_Proxy(IMSMQMessage3 *This,__LONG32 lJournal);
  void __RPC_STUB IMSMQMessage3_put_Journal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_ResponseQueueInfo_v1_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo **ppqinfoResponse);
  void __RPC_STUB IMSMQMessage3_get_ResponseQueueInfo_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_putref_ResponseQueueInfo_v1_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo *pqinfoResponse);
  void __RPC_STUB IMSMQMessage3_putref_ResponseQueueInfo_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_AppSpecific_Proxy(IMSMQMessage3 *This,__LONG32 *plAppSpecific);
  void __RPC_STUB IMSMQMessage3_get_AppSpecific_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_AppSpecific_Proxy(IMSMQMessage3 *This,__LONG32 lAppSpecific);
  void __RPC_STUB IMSMQMessage3_put_AppSpecific_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_SourceMachineGuid_Proxy(IMSMQMessage3 *This,BSTR *pbstrGuidSrcMachine);
  void __RPC_STUB IMSMQMessage3_get_SourceMachineGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_BodyLength_Proxy(IMSMQMessage3 *This,__LONG32 *pcbBody);
  void __RPC_STUB IMSMQMessage3_get_BodyLength_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Body_Proxy(IMSMQMessage3 *This,VARIANT *pvarBody);
  void __RPC_STUB IMSMQMessage3_get_Body_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Body_Proxy(IMSMQMessage3 *This,VARIANT varBody);
  void __RPC_STUB IMSMQMessage3_put_Body_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_AdminQueueInfo_v1_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo **ppqinfoAdmin);
  void __RPC_STUB IMSMQMessage3_get_AdminQueueInfo_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_putref_AdminQueueInfo_v1_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo *pqinfoAdmin);
  void __RPC_STUB IMSMQMessage3_putref_AdminQueueInfo_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Id_Proxy(IMSMQMessage3 *This,VARIANT *pvarMsgId);
  void __RPC_STUB IMSMQMessage3_get_Id_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_CorrelationId_Proxy(IMSMQMessage3 *This,VARIANT *pvarMsgId);
  void __RPC_STUB IMSMQMessage3_get_CorrelationId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_CorrelationId_Proxy(IMSMQMessage3 *This,VARIANT varMsgId);
  void __RPC_STUB IMSMQMessage3_put_CorrelationId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Ack_Proxy(IMSMQMessage3 *This,__LONG32 *plAck);
  void __RPC_STUB IMSMQMessage3_get_Ack_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Ack_Proxy(IMSMQMessage3 *This,__LONG32 lAck);
  void __RPC_STUB IMSMQMessage3_put_Ack_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Label_Proxy(IMSMQMessage3 *This,BSTR *pbstrLabel);
  void __RPC_STUB IMSMQMessage3_get_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Label_Proxy(IMSMQMessage3 *This,BSTR bstrLabel);
  void __RPC_STUB IMSMQMessage3_put_Label_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_MaxTimeToReachQueue_Proxy(IMSMQMessage3 *This,__LONG32 *plMaxTimeToReachQueue);
  void __RPC_STUB IMSMQMessage3_get_MaxTimeToReachQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_MaxTimeToReachQueue_Proxy(IMSMQMessage3 *This,__LONG32 lMaxTimeToReachQueue);
  void __RPC_STUB IMSMQMessage3_put_MaxTimeToReachQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_MaxTimeToReceive_Proxy(IMSMQMessage3 *This,__LONG32 *plMaxTimeToReceive);
  void __RPC_STUB IMSMQMessage3_get_MaxTimeToReceive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_MaxTimeToReceive_Proxy(IMSMQMessage3 *This,__LONG32 lMaxTimeToReceive);
  void __RPC_STUB IMSMQMessage3_put_MaxTimeToReceive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_HashAlgorithm_Proxy(IMSMQMessage3 *This,__LONG32 *plHashAlg);
  void __RPC_STUB IMSMQMessage3_get_HashAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_HashAlgorithm_Proxy(IMSMQMessage3 *This,__LONG32 lHashAlg);
  void __RPC_STUB IMSMQMessage3_put_HashAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_EncryptAlgorithm_Proxy(IMSMQMessage3 *This,__LONG32 *plEncryptAlg);
  void __RPC_STUB IMSMQMessage3_get_EncryptAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_EncryptAlgorithm_Proxy(IMSMQMessage3 *This,__LONG32 lEncryptAlg);
  void __RPC_STUB IMSMQMessage3_put_EncryptAlgorithm_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_SentTime_Proxy(IMSMQMessage3 *This,VARIANT *pvarSentTime);
  void __RPC_STUB IMSMQMessage3_get_SentTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_ArrivedTime_Proxy(IMSMQMessage3 *This,VARIANT *plArrivedTime);
  void __RPC_STUB IMSMQMessage3_get_ArrivedTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_DestinationQueueInfo_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo3 **ppqinfoDest);
  void __RPC_STUB IMSMQMessage3_get_DestinationQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_SenderCertificate_Proxy(IMSMQMessage3 *This,VARIANT *pvarSenderCert);
  void __RPC_STUB IMSMQMessage3_get_SenderCertificate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_SenderCertificate_Proxy(IMSMQMessage3 *This,VARIANT varSenderCert);
  void __RPC_STUB IMSMQMessage3_put_SenderCertificate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_SenderId_Proxy(IMSMQMessage3 *This,VARIANT *pvarSenderId);
  void __RPC_STUB IMSMQMessage3_get_SenderId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_SenderIdType_Proxy(IMSMQMessage3 *This,__LONG32 *plSenderIdType);
  void __RPC_STUB IMSMQMessage3_get_SenderIdType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_SenderIdType_Proxy(IMSMQMessage3 *This,__LONG32 lSenderIdType);
  void __RPC_STUB IMSMQMessage3_put_SenderIdType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_Send_Proxy(IMSMQMessage3 *This,IDispatch *DestinationQueue,VARIANT *Transaction);
  void __RPC_STUB IMSMQMessage3_Send_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_AttachCurrentSecurityContext_Proxy(IMSMQMessage3 *This);
  void __RPC_STUB IMSMQMessage3_AttachCurrentSecurityContext_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_SenderVersion_Proxy(IMSMQMessage3 *This,__LONG32 *plSenderVersion);
  void __RPC_STUB IMSMQMessage3_get_SenderVersion_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Extension_Proxy(IMSMQMessage3 *This,VARIANT *pvarExtension);
  void __RPC_STUB IMSMQMessage3_get_Extension_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Extension_Proxy(IMSMQMessage3 *This,VARIANT varExtension);
  void __RPC_STUB IMSMQMessage3_put_Extension_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_ConnectorTypeGuid_Proxy(IMSMQMessage3 *This,BSTR *pbstrGuidConnectorType);
  void __RPC_STUB IMSMQMessage3_get_ConnectorTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_ConnectorTypeGuid_Proxy(IMSMQMessage3 *This,BSTR bstrGuidConnectorType);
  void __RPC_STUB IMSMQMessage3_put_ConnectorTypeGuid_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_TransactionStatusQueueInfo_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo3 **ppqinfoXactStatus);
  void __RPC_STUB IMSMQMessage3_get_TransactionStatusQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_DestinationSymmetricKey_Proxy(IMSMQMessage3 *This,VARIANT *pvarDestSymmKey);
  void __RPC_STUB IMSMQMessage3_get_DestinationSymmetricKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_DestinationSymmetricKey_Proxy(IMSMQMessage3 *This,VARIANT varDestSymmKey);
  void __RPC_STUB IMSMQMessage3_put_DestinationSymmetricKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Signature_Proxy(IMSMQMessage3 *This,VARIANT *pvarSignature);
  void __RPC_STUB IMSMQMessage3_get_Signature_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_Signature_Proxy(IMSMQMessage3 *This,VARIANT varSignature);
  void __RPC_STUB IMSMQMessage3_put_Signature_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_AuthenticationProviderType_Proxy(IMSMQMessage3 *This,__LONG32 *plAuthProvType);
  void __RPC_STUB IMSMQMessage3_get_AuthenticationProviderType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_AuthenticationProviderType_Proxy(IMSMQMessage3 *This,__LONG32 lAuthProvType);
  void __RPC_STUB IMSMQMessage3_put_AuthenticationProviderType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_AuthenticationProviderName_Proxy(IMSMQMessage3 *This,BSTR *pbstrAuthProvName);
  void __RPC_STUB IMSMQMessage3_get_AuthenticationProviderName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_AuthenticationProviderName_Proxy(IMSMQMessage3 *This,BSTR bstrAuthProvName);
  void __RPC_STUB IMSMQMessage3_put_AuthenticationProviderName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_SenderId_Proxy(IMSMQMessage3 *This,VARIANT varSenderId);
  void __RPC_STUB IMSMQMessage3_put_SenderId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_MsgClass_Proxy(IMSMQMessage3 *This,__LONG32 *plMsgClass);
  void __RPC_STUB IMSMQMessage3_get_MsgClass_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_MsgClass_Proxy(IMSMQMessage3 *This,__LONG32 lMsgClass);
  void __RPC_STUB IMSMQMessage3_put_MsgClass_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Properties_Proxy(IMSMQMessage3 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQMessage3_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_TransactionId_Proxy(IMSMQMessage3 *This,VARIANT *pvarXactId);
  void __RPC_STUB IMSMQMessage3_get_TransactionId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_IsFirstInTransaction_Proxy(IMSMQMessage3 *This,Boolean *pisFirstInXact);
  void __RPC_STUB IMSMQMessage3_get_IsFirstInTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_IsLastInTransaction_Proxy(IMSMQMessage3 *This,Boolean *pisLastInXact);
  void __RPC_STUB IMSMQMessage3_get_IsLastInTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_ResponseQueueInfo_v2_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo2 **ppqinfoResponse);
  void __RPC_STUB IMSMQMessage3_get_ResponseQueueInfo_v2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_putref_ResponseQueueInfo_v2_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo2 *pqinfoResponse);
  void __RPC_STUB IMSMQMessage3_putref_ResponseQueueInfo_v2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_AdminQueueInfo_v2_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo2 **ppqinfoAdmin);
  void __RPC_STUB IMSMQMessage3_get_AdminQueueInfo_v2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_putref_AdminQueueInfo_v2_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo2 *pqinfoAdmin);
  void __RPC_STUB IMSMQMessage3_putref_AdminQueueInfo_v2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_ReceivedAuthenticationLevel_Proxy(IMSMQMessage3 *This,short *psReceivedAuthenticationLevel);
  void __RPC_STUB IMSMQMessage3_get_ReceivedAuthenticationLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_ResponseQueueInfo_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo3 **ppqinfoResponse);
  void __RPC_STUB IMSMQMessage3_get_ResponseQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_putref_ResponseQueueInfo_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo3 *pqinfoResponse);
  void __RPC_STUB IMSMQMessage3_putref_ResponseQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_AdminQueueInfo_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo3 **ppqinfoAdmin);
  void __RPC_STUB IMSMQMessage3_get_AdminQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_putref_AdminQueueInfo_Proxy(IMSMQMessage3 *This,IMSMQQueueInfo3 *pqinfoAdmin);
  void __RPC_STUB IMSMQMessage3_putref_AdminQueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_ResponseDestination_Proxy(IMSMQMessage3 *This,IDispatch **ppdestResponse);
  void __RPC_STUB IMSMQMessage3_get_ResponseDestination_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_putref_ResponseDestination_Proxy(IMSMQMessage3 *This,IDispatch *pdestResponse);
  void __RPC_STUB IMSMQMessage3_putref_ResponseDestination_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_Destination_Proxy(IMSMQMessage3 *This,IDispatch **ppdestDestination);
  void __RPC_STUB IMSMQMessage3_get_Destination_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_LookupId_Proxy(IMSMQMessage3 *This,VARIANT *pvarLookupId);
  void __RPC_STUB IMSMQMessage3_get_LookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_IsAuthenticated2_Proxy(IMSMQMessage3 *This,VARIANT_BOOL *pisAuthenticated);
  void __RPC_STUB IMSMQMessage3_get_IsAuthenticated2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_IsFirstInTransaction2_Proxy(IMSMQMessage3 *This,VARIANT_BOOL *pisFirstInXact);
  void __RPC_STUB IMSMQMessage3_get_IsFirstInTransaction2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_IsLastInTransaction2_Proxy(IMSMQMessage3 *This,VARIANT_BOOL *pisLastInXact);
  void __RPC_STUB IMSMQMessage3_get_IsLastInTransaction2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_AttachCurrentSecurityContext2_Proxy(IMSMQMessage3 *This);
  void __RPC_STUB IMSMQMessage3_AttachCurrentSecurityContext2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_SoapEnvelope_Proxy(IMSMQMessage3 *This,BSTR *pbstrSoapEnvelope);
  void __RPC_STUB IMSMQMessage3_get_SoapEnvelope_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_get_CompoundMessage_Proxy(IMSMQMessage3 *This,VARIANT *pvarCompoundMessage);
  void __RPC_STUB IMSMQMessage3_get_CompoundMessage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_SoapHeader_Proxy(IMSMQMessage3 *This,BSTR bstrSoapHeader);
  void __RPC_STUB IMSMQMessage3_put_SoapHeader_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQMessage3_put_SoapBody_Proxy(IMSMQMessage3 *This,BSTR bstrSoapBody);
  void __RPC_STUB IMSMQMessage3_put_SoapBody_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQMessage;
#ifdef __cplusplus
  class MSMQMessage;
#endif

#ifndef __IMSMQQueue3_INTERFACE_DEFINED__
#define __IMSMQQueue3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueue3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueue3 : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Access(__LONG32 *plAccess) = 0;
    virtual HRESULT WINAPI get_ShareMode(__LONG32 *plShareMode) = 0;
    virtual HRESULT WINAPI get_QueueInfo(IMSMQQueueInfo3 **ppqinfo) = 0;
    virtual HRESULT WINAPI get_Handle(__LONG32 *plHandle) = 0;
    virtual HRESULT WINAPI get_IsOpen(Boolean *pisOpen) = 0;
    virtual HRESULT WINAPI Close(void) = 0;
    virtual HRESULT WINAPI Receive_v1(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI Peek_v1(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI EnableNotification(IMSMQEvent3 *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout) = 0;
    virtual HRESULT WINAPI Reset(void) = 0;
    virtual HRESULT WINAPI ReceiveCurrent_v1(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI PeekNext_v1(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI PeekCurrent_v1(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg) = 0;
    virtual HRESULT WINAPI Receive(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI Peek(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI ReceiveCurrent(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekNext(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekCurrent(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
    virtual HRESULT WINAPI get_Handle2(VARIANT *pvarHandle) = 0;
    virtual HRESULT WINAPI ReceiveByLookupId(VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI ReceiveNextByLookupId(VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI ReceivePreviousByLookupId(VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI ReceiveFirstByLookupId(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI ReceiveLastByLookupId(VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekByLookupId(VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekNextByLookupId(VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekPreviousByLookupId(VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekFirstByLookupId(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI PeekLastByLookupId(VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg) = 0;
    virtual HRESULT WINAPI Purge(void) = 0;
    virtual HRESULT WINAPI get_IsOpen2(VARIANT_BOOL *pisOpen) = 0;
  };
#else
  typedef struct IMSMQQueue3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueue3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueue3 *This);
      ULONG (WINAPI *Release)(IMSMQQueue3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueue3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueue3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueue3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueue3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Access)(IMSMQQueue3 *This,__LONG32 *plAccess);
      HRESULT (WINAPI *get_ShareMode)(IMSMQQueue3 *This,__LONG32 *plShareMode);
      HRESULT (WINAPI *get_QueueInfo)(IMSMQQueue3 *This,IMSMQQueueInfo3 **ppqinfo);
      HRESULT (WINAPI *get_Handle)(IMSMQQueue3 *This,__LONG32 *plHandle);
      HRESULT (WINAPI *get_IsOpen)(IMSMQQueue3 *This,Boolean *pisOpen);
      HRESULT (WINAPI *Close)(IMSMQQueue3 *This);
      HRESULT (WINAPI *Receive_v1)(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *Peek_v1)(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *EnableNotification)(IMSMQQueue3 *This,IMSMQEvent3 *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout);
      HRESULT (WINAPI *Reset)(IMSMQQueue3 *This);
      HRESULT (WINAPI *ReceiveCurrent_v1)(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *PeekNext_v1)(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *PeekCurrent_v1)(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
      HRESULT (WINAPI *Receive)(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *Peek)(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *ReceiveCurrent)(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *PeekNext)(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *PeekCurrent)(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *get_Properties)(IMSMQQueue3 *This,IDispatch **ppcolProperties);
      HRESULT (WINAPI *get_Handle2)(IMSMQQueue3 *This,VARIANT *pvarHandle);
      HRESULT (WINAPI *ReceiveByLookupId)(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *ReceiveNextByLookupId)(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *ReceivePreviousByLookupId)(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *ReceiveFirstByLookupId)(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *ReceiveLastByLookupId)(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *PeekByLookupId)(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *PeekNextByLookupId)(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *PeekPreviousByLookupId)(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *PeekFirstByLookupId)(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *PeekLastByLookupId)(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
      HRESULT (WINAPI *Purge)(IMSMQQueue3 *This);
      HRESULT (WINAPI *get_IsOpen2)(IMSMQQueue3 *This,VARIANT_BOOL *pisOpen);
    END_INTERFACE
  } IMSMQQueue3Vtbl;
  struct IMSMQQueue3 {
    CONST_VTBL struct IMSMQQueue3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueue3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueue3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueue3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueue3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueue3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueue3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueue3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueue3_get_Access(This,plAccess) (This)->lpVtbl->get_Access(This,plAccess)
#define IMSMQQueue3_get_ShareMode(This,plShareMode) (This)->lpVtbl->get_ShareMode(This,plShareMode)
#define IMSMQQueue3_get_QueueInfo(This,ppqinfo) (This)->lpVtbl->get_QueueInfo(This,ppqinfo)
#define IMSMQQueue3_get_Handle(This,plHandle) (This)->lpVtbl->get_Handle(This,plHandle)
#define IMSMQQueue3_get_IsOpen(This,pisOpen) (This)->lpVtbl->get_IsOpen(This,pisOpen)
#define IMSMQQueue3_Close(This) (This)->lpVtbl->Close(This)
#define IMSMQQueue3_Receive_v1(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->Receive_v1(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue3_Peek_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->Peek_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue3_EnableNotification(This,Event,Cursor,ReceiveTimeout) (This)->lpVtbl->EnableNotification(This,Event,Cursor,ReceiveTimeout)
#define IMSMQQueue3_Reset(This) (This)->lpVtbl->Reset(This)
#define IMSMQQueue3_ReceiveCurrent_v1(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->ReceiveCurrent_v1(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue3_PeekNext_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->PeekNext_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue3_PeekCurrent_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg) (This)->lpVtbl->PeekCurrent_v1(This,WantDestinationQueue,WantBody,ReceiveTimeout,ppmsg)
#define IMSMQQueue3_Receive(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->Receive(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue3_Peek(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->Peek(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue3_ReceiveCurrent(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->ReceiveCurrent(This,Transaction,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue3_PeekNext(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->PeekNext(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue3_PeekCurrent(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg) (This)->lpVtbl->PeekCurrent(This,WantDestinationQueue,WantBody,ReceiveTimeout,WantConnectorType,ppmsg)
#define IMSMQQueue3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#define IMSMQQueue3_get_Handle2(This,pvarHandle) (This)->lpVtbl->get_Handle2(This,pvarHandle)
#define IMSMQQueue3_ReceiveByLookupId(This,LookupId,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->ReceiveByLookupId(This,LookupId,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_ReceiveNextByLookupId(This,LookupId,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->ReceiveNextByLookupId(This,LookupId,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_ReceivePreviousByLookupId(This,LookupId,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->ReceivePreviousByLookupId(This,LookupId,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_ReceiveFirstByLookupId(This,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->ReceiveFirstByLookupId(This,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_ReceiveLastByLookupId(This,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->ReceiveLastByLookupId(This,Transaction,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_PeekByLookupId(This,LookupId,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->PeekByLookupId(This,LookupId,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_PeekNextByLookupId(This,LookupId,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->PeekNextByLookupId(This,LookupId,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_PeekPreviousByLookupId(This,LookupId,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->PeekPreviousByLookupId(This,LookupId,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_PeekFirstByLookupId(This,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->PeekFirstByLookupId(This,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_PeekLastByLookupId(This,WantDestinationQueue,WantBody,WantConnectorType,ppmsg) (This)->lpVtbl->PeekLastByLookupId(This,WantDestinationQueue,WantBody,WantConnectorType,ppmsg)
#define IMSMQQueue3_Purge(This) (This)->lpVtbl->Purge(This)
#define IMSMQQueue3_get_IsOpen2(This,pisOpen) (This)->lpVtbl->get_IsOpen2(This,pisOpen)
#endif
#endif
  HRESULT WINAPI IMSMQQueue3_get_Access_Proxy(IMSMQQueue3 *This,__LONG32 *plAccess);
  void __RPC_STUB IMSMQQueue3_get_Access_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_get_ShareMode_Proxy(IMSMQQueue3 *This,__LONG32 *plShareMode);
  void __RPC_STUB IMSMQQueue3_get_ShareMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_get_QueueInfo_Proxy(IMSMQQueue3 *This,IMSMQQueueInfo3 **ppqinfo);
  void __RPC_STUB IMSMQQueue3_get_QueueInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_get_Handle_Proxy(IMSMQQueue3 *This,__LONG32 *plHandle);
  void __RPC_STUB IMSMQQueue3_get_Handle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_get_IsOpen_Proxy(IMSMQQueue3 *This,Boolean *pisOpen);
  void __RPC_STUB IMSMQQueue3_get_IsOpen_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_Close_Proxy(IMSMQQueue3 *This);
  void __RPC_STUB IMSMQQueue3_Close_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_Receive_v1_Proxy(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue3_Receive_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_Peek_v1_Proxy(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue3_Peek_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_EnableNotification_Proxy(IMSMQQueue3 *This,IMSMQEvent3 *Event,VARIANT *Cursor,VARIANT *ReceiveTimeout);
  void __RPC_STUB IMSMQQueue3_EnableNotification_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_Reset_Proxy(IMSMQQueue3 *This);
  void __RPC_STUB IMSMQQueue3_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_ReceiveCurrent_v1_Proxy(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue3_ReceiveCurrent_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekNext_v1_Proxy(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekNext_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekCurrent_v1_Proxy(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,IMSMQMessage **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekCurrent_v1_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_Receive_Proxy(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_Receive_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_Peek_Proxy(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_Peek_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_ReceiveCurrent_Proxy(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_ReceiveCurrent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekNext_Proxy(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekNext_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekCurrent_Proxy(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *ReceiveTimeout,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekCurrent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_get_Properties_Proxy(IMSMQQueue3 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQQueue3_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_get_Handle2_Proxy(IMSMQQueue3 *This,VARIANT *pvarHandle);
  void __RPC_STUB IMSMQQueue3_get_Handle2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_ReceiveByLookupId_Proxy(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_ReceiveByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_ReceiveNextByLookupId_Proxy(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_ReceiveNextByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_ReceivePreviousByLookupId_Proxy(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_ReceivePreviousByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_ReceiveFirstByLookupId_Proxy(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_ReceiveFirstByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_ReceiveLastByLookupId_Proxy(IMSMQQueue3 *This,VARIANT *Transaction,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_ReceiveLastByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekByLookupId_Proxy(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekNextByLookupId_Proxy(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekNextByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekPreviousByLookupId_Proxy(IMSMQQueue3 *This,VARIANT LookupId,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekPreviousByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekFirstByLookupId_Proxy(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekFirstByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_PeekLastByLookupId_Proxy(IMSMQQueue3 *This,VARIANT *WantDestinationQueue,VARIANT *WantBody,VARIANT *WantConnectorType,IMSMQMessage3 **ppmsg);
  void __RPC_STUB IMSMQQueue3_PeekLastByLookupId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_Purge_Proxy(IMSMQQueue3 *This);
  void __RPC_STUB IMSMQQueue3_Purge_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueue3_get_IsOpen2_Proxy(IMSMQQueue3 *This,VARIANT_BOOL *pisOpen);
  void __RPC_STUB IMSMQQueue3_get_IsOpen2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQQueue;
#ifdef __cplusplus
  class MSMQQueue;
#endif

#ifndef __IMSMQPrivateEvent_INTERFACE_DEFINED__
#define __IMSMQPrivateEvent_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQPrivateEvent;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQPrivateEvent : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Hwnd(__LONG32 *phwnd) = 0;
    virtual HRESULT WINAPI FireArrivedEvent(IMSMQQueue *pq,__LONG32 msgcursor) = 0;
    virtual HRESULT WINAPI FireArrivedErrorEvent(IMSMQQueue *pq,HRESULT hrStatus,__LONG32 msgcursor) = 0;
  };
#else
  typedef struct IMSMQPrivateEventVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQPrivateEvent *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQPrivateEvent *This);
      ULONG (WINAPI *Release)(IMSMQPrivateEvent *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQPrivateEvent *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQPrivateEvent *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQPrivateEvent *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQPrivateEvent *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Hwnd)(IMSMQPrivateEvent *This,__LONG32 *phwnd);
      HRESULT (WINAPI *FireArrivedEvent)(IMSMQPrivateEvent *This,IMSMQQueue *pq,__LONG32 msgcursor);
      HRESULT (WINAPI *FireArrivedErrorEvent)(IMSMQPrivateEvent *This,IMSMQQueue *pq,HRESULT hrStatus,__LONG32 msgcursor);
    END_INTERFACE
  } IMSMQPrivateEventVtbl;
  struct IMSMQPrivateEvent {
    CONST_VTBL struct IMSMQPrivateEventVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQPrivateEvent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQPrivateEvent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQPrivateEvent_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQPrivateEvent_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQPrivateEvent_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQPrivateEvent_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQPrivateEvent_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQPrivateEvent_get_Hwnd(This,phwnd) (This)->lpVtbl->get_Hwnd(This,phwnd)
#define IMSMQPrivateEvent_FireArrivedEvent(This,pq,msgcursor) (This)->lpVtbl->FireArrivedEvent(This,pq,msgcursor)
#define IMSMQPrivateEvent_FireArrivedErrorEvent(This,pq,hrStatus,msgcursor) (This)->lpVtbl->FireArrivedErrorEvent(This,pq,hrStatus,msgcursor)
#endif
#endif
  HRESULT WINAPI IMSMQPrivateEvent_get_Hwnd_Proxy(IMSMQPrivateEvent *This,__LONG32 *phwnd);
  void __RPC_STUB IMSMQPrivateEvent_get_Hwnd_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQPrivateEvent_FireArrivedEvent_Proxy(IMSMQPrivateEvent *This,IMSMQQueue *pq,__LONG32 msgcursor);
  void __RPC_STUB IMSMQPrivateEvent_FireArrivedEvent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQPrivateEvent_FireArrivedErrorEvent_Proxy(IMSMQPrivateEvent *This,IMSMQQueue *pq,HRESULT hrStatus,__LONG32 msgcursor);
  void __RPC_STUB IMSMQPrivateEvent_FireArrivedErrorEvent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef ___DMSMQEventEvents_DISPINTERFACE_DEFINED__
#define ___DMSMQEventEvents_DISPINTERFACE_DEFINED__
  EXTERN_C const IID DIID__DMSMQEventEvents;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct _DMSMQEventEvents : public IDispatch {
  };
#else
  typedef struct _DMSMQEventEventsVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(_DMSMQEventEvents *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(_DMSMQEventEvents *This);
      ULONG (WINAPI *Release)(_DMSMQEventEvents *This);
      HRESULT (WINAPI *GetTypeInfoCount)(_DMSMQEventEvents *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(_DMSMQEventEvents *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(_DMSMQEventEvents *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(_DMSMQEventEvents *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
    END_INTERFACE
  } _DMSMQEventEventsVtbl;
  struct _DMSMQEventEvents {
    CONST_VTBL struct _DMSMQEventEventsVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define _DMSMQEventEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define _DMSMQEventEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define _DMSMQEventEvents_Release(This) (This)->lpVtbl->Release(This)
#define _DMSMQEventEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define _DMSMQEventEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define _DMSMQEventEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define _DMSMQEventEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#endif
#endif
#endif

  EXTERN_C const CLSID CLSID_MSMQEvent;
#ifdef __cplusplus
  class MSMQEvent;
#endif
  EXTERN_C const CLSID CLSID_MSMQQueueInfo;
#ifdef __cplusplus
  class MSMQQueueInfo;
#endif
  EXTERN_C const CLSID CLSID_MSMQQueueInfos;
#ifdef __cplusplus
  class MSMQQueueInfos;
#endif

#ifndef __IMSMQTransaction2_INTERFACE_DEFINED__
#define __IMSMQTransaction2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQTransaction2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQTransaction2 : public IMSMQTransaction {
  public:
    virtual HRESULT WINAPI InitNew(VARIANT varTransaction) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQTransaction2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQTransaction2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQTransaction2 *This);
      ULONG (WINAPI *Release)(IMSMQTransaction2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQTransaction2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQTransaction2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQTransaction2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQTransaction2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Transaction)(IMSMQTransaction2 *This,__LONG32 *plTransaction);
      HRESULT (WINAPI *Commit)(IMSMQTransaction2 *This,VARIANT *fRetaining,VARIANT *grfTC,VARIANT *grfRM);
      HRESULT (WINAPI *Abort)(IMSMQTransaction2 *This,VARIANT *fRetaining,VARIANT *fAsync);
      HRESULT (WINAPI *InitNew)(IMSMQTransaction2 *This,VARIANT varTransaction);
      HRESULT (WINAPI *get_Properties)(IMSMQTransaction2 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQTransaction2Vtbl;
  struct IMSMQTransaction2 {
    CONST_VTBL struct IMSMQTransaction2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQTransaction2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQTransaction2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQTransaction2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQTransaction2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQTransaction2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQTransaction2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQTransaction2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQTransaction2_get_Transaction(This,plTransaction) (This)->lpVtbl->get_Transaction(This,plTransaction)
#define IMSMQTransaction2_Commit(This,fRetaining,grfTC,grfRM) (This)->lpVtbl->Commit(This,fRetaining,grfTC,grfRM)
#define IMSMQTransaction2_Abort(This,fRetaining,fAsync) (This)->lpVtbl->Abort(This,fRetaining,fAsync)
#define IMSMQTransaction2_InitNew(This,varTransaction) (This)->lpVtbl->InitNew(This,varTransaction)
#define IMSMQTransaction2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQTransaction2_InitNew_Proxy(IMSMQTransaction2 *This,VARIANT varTransaction);
  void __RPC_STUB IMSMQTransaction2_InitNew_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQTransaction2_get_Properties_Proxy(IMSMQTransaction2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQTransaction2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQTransaction3_INTERFACE_DEFINED__
#define __IMSMQTransaction3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQTransaction3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQTransaction3 : public IMSMQTransaction2 {
  public:
    virtual HRESULT WINAPI get_ITransaction(VARIANT *pvarITransaction) = 0;
  };
#else
  typedef struct IMSMQTransaction3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQTransaction3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQTransaction3 *This);
      ULONG (WINAPI *Release)(IMSMQTransaction3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQTransaction3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQTransaction3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQTransaction3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQTransaction3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Transaction)(IMSMQTransaction3 *This,__LONG32 *plTransaction);
      HRESULT (WINAPI *Commit)(IMSMQTransaction3 *This,VARIANT *fRetaining,VARIANT *grfTC,VARIANT *grfRM);
      HRESULT (WINAPI *Abort)(IMSMQTransaction3 *This,VARIANT *fRetaining,VARIANT *fAsync);
      HRESULT (WINAPI *InitNew)(IMSMQTransaction3 *This,VARIANT varTransaction);
      HRESULT (WINAPI *get_Properties)(IMSMQTransaction3 *This,IDispatch **ppcolProperties);
      HRESULT (WINAPI *get_ITransaction)(IMSMQTransaction3 *This,VARIANT *pvarITransaction);
    END_INTERFACE
  } IMSMQTransaction3Vtbl;
  struct IMSMQTransaction3 {
    CONST_VTBL struct IMSMQTransaction3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQTransaction3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQTransaction3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQTransaction3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQTransaction3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQTransaction3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQTransaction3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQTransaction3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQTransaction3_get_Transaction(This,plTransaction) (This)->lpVtbl->get_Transaction(This,plTransaction)
#define IMSMQTransaction3_Commit(This,fRetaining,grfTC,grfRM) (This)->lpVtbl->Commit(This,fRetaining,grfTC,grfRM)
#define IMSMQTransaction3_Abort(This,fRetaining,fAsync) (This)->lpVtbl->Abort(This,fRetaining,fAsync)
#define IMSMQTransaction3_InitNew(This,varTransaction) (This)->lpVtbl->InitNew(This,varTransaction)
#define IMSMQTransaction3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#define IMSMQTransaction3_get_ITransaction(This,pvarITransaction) (This)->lpVtbl->get_ITransaction(This,pvarITransaction)
#endif
#endif
  HRESULT WINAPI IMSMQTransaction3_get_ITransaction_Proxy(IMSMQTransaction3 *This,VARIANT *pvarITransaction);
  void __RPC_STUB IMSMQTransaction3_get_ITransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQTransaction;
#ifdef __cplusplus
  class MSMQTransaction;
#endif

#ifndef __IMSMQCoordinatedTransactionDispenser2_INTERFACE_DEFINED__
#define __IMSMQCoordinatedTransactionDispenser2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQCoordinatedTransactionDispenser2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQCoordinatedTransactionDispenser2 : public IDispatch {
  public:
    virtual HRESULT WINAPI BeginTransaction(IMSMQTransaction2 **ptransaction) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQCoordinatedTransactionDispenser2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQCoordinatedTransactionDispenser2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQCoordinatedTransactionDispenser2 *This);
      ULONG (WINAPI *Release)(IMSMQCoordinatedTransactionDispenser2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQCoordinatedTransactionDispenser2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQCoordinatedTransactionDispenser2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQCoordinatedTransactionDispenser2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQCoordinatedTransactionDispenser2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *BeginTransaction)(IMSMQCoordinatedTransactionDispenser2 *This,IMSMQTransaction2 **ptransaction);
      HRESULT (WINAPI *get_Properties)(IMSMQCoordinatedTransactionDispenser2 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQCoordinatedTransactionDispenser2Vtbl;
  struct IMSMQCoordinatedTransactionDispenser2 {
    CONST_VTBL struct IMSMQCoordinatedTransactionDispenser2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQCoordinatedTransactionDispenser2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQCoordinatedTransactionDispenser2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQCoordinatedTransactionDispenser2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQCoordinatedTransactionDispenser2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQCoordinatedTransactionDispenser2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQCoordinatedTransactionDispenser2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQCoordinatedTransactionDispenser2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQCoordinatedTransactionDispenser2_BeginTransaction(This,ptransaction) (This)->lpVtbl->BeginTransaction(This,ptransaction)
#define IMSMQCoordinatedTransactionDispenser2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQCoordinatedTransactionDispenser2_BeginTransaction_Proxy(IMSMQCoordinatedTransactionDispenser2 *This,IMSMQTransaction2 **ptransaction);
  void __RPC_STUB IMSMQCoordinatedTransactionDispenser2_BeginTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQCoordinatedTransactionDispenser2_get_Properties_Proxy(IMSMQCoordinatedTransactionDispenser2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQCoordinatedTransactionDispenser2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQCoordinatedTransactionDispenser3_INTERFACE_DEFINED__
#define __IMSMQCoordinatedTransactionDispenser3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQCoordinatedTransactionDispenser3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQCoordinatedTransactionDispenser3 : public IDispatch {
  public:
    virtual HRESULT WINAPI BeginTransaction(IMSMQTransaction3 **ptransaction) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQCoordinatedTransactionDispenser3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQCoordinatedTransactionDispenser3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQCoordinatedTransactionDispenser3 *This);
      ULONG (WINAPI *Release)(IMSMQCoordinatedTransactionDispenser3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQCoordinatedTransactionDispenser3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQCoordinatedTransactionDispenser3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQCoordinatedTransactionDispenser3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQCoordinatedTransactionDispenser3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *BeginTransaction)(IMSMQCoordinatedTransactionDispenser3 *This,IMSMQTransaction3 **ptransaction);
      HRESULT (WINAPI *get_Properties)(IMSMQCoordinatedTransactionDispenser3 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQCoordinatedTransactionDispenser3Vtbl;
  struct IMSMQCoordinatedTransactionDispenser3 {
    CONST_VTBL struct IMSMQCoordinatedTransactionDispenser3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQCoordinatedTransactionDispenser3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQCoordinatedTransactionDispenser3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQCoordinatedTransactionDispenser3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQCoordinatedTransactionDispenser3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQCoordinatedTransactionDispenser3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQCoordinatedTransactionDispenser3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQCoordinatedTransactionDispenser3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQCoordinatedTransactionDispenser3_BeginTransaction(This,ptransaction) (This)->lpVtbl->BeginTransaction(This,ptransaction)
#define IMSMQCoordinatedTransactionDispenser3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQCoordinatedTransactionDispenser3_BeginTransaction_Proxy(IMSMQCoordinatedTransactionDispenser3 *This,IMSMQTransaction3 **ptransaction);
  void __RPC_STUB IMSMQCoordinatedTransactionDispenser3_BeginTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQCoordinatedTransactionDispenser3_get_Properties_Proxy(IMSMQCoordinatedTransactionDispenser3 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQCoordinatedTransactionDispenser3_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQCoordinatedTransactionDispenser;
#ifdef __cplusplus
  class MSMQCoordinatedTransactionDispenser;
#endif

#ifndef __IMSMQTransactionDispenser2_INTERFACE_DEFINED__
#define __IMSMQTransactionDispenser2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQTransactionDispenser2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQTransactionDispenser2 : public IDispatch {
  public:
    virtual HRESULT WINAPI BeginTransaction(IMSMQTransaction2 **ptransaction) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQTransactionDispenser2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQTransactionDispenser2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQTransactionDispenser2 *This);
      ULONG (WINAPI *Release)(IMSMQTransactionDispenser2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQTransactionDispenser2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQTransactionDispenser2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQTransactionDispenser2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQTransactionDispenser2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *BeginTransaction)(IMSMQTransactionDispenser2 *This,IMSMQTransaction2 **ptransaction);
      HRESULT (WINAPI *get_Properties)(IMSMQTransactionDispenser2 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQTransactionDispenser2Vtbl;
  struct IMSMQTransactionDispenser2 {
    CONST_VTBL struct IMSMQTransactionDispenser2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQTransactionDispenser2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQTransactionDispenser2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQTransactionDispenser2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQTransactionDispenser2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQTransactionDispenser2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQTransactionDispenser2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQTransactionDispenser2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQTransactionDispenser2_BeginTransaction(This,ptransaction) (This)->lpVtbl->BeginTransaction(This,ptransaction)
#define IMSMQTransactionDispenser2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQTransactionDispenser2_BeginTransaction_Proxy(IMSMQTransactionDispenser2 *This,IMSMQTransaction2 **ptransaction);
  void __RPC_STUB IMSMQTransactionDispenser2_BeginTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQTransactionDispenser2_get_Properties_Proxy(IMSMQTransactionDispenser2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQTransactionDispenser2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQTransactionDispenser3_INTERFACE_DEFINED__
#define __IMSMQTransactionDispenser3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQTransactionDispenser3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQTransactionDispenser3 : public IDispatch {
  public:
    virtual HRESULT WINAPI BeginTransaction(IMSMQTransaction3 **ptransaction) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQTransactionDispenser3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQTransactionDispenser3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQTransactionDispenser3 *This);
      ULONG (WINAPI *Release)(IMSMQTransactionDispenser3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQTransactionDispenser3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQTransactionDispenser3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQTransactionDispenser3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQTransactionDispenser3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *BeginTransaction)(IMSMQTransactionDispenser3 *This,IMSMQTransaction3 **ptransaction);
      HRESULT (WINAPI *get_Properties)(IMSMQTransactionDispenser3 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQTransactionDispenser3Vtbl;
  struct IMSMQTransactionDispenser3 {
    CONST_VTBL struct IMSMQTransactionDispenser3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQTransactionDispenser3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQTransactionDispenser3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQTransactionDispenser3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQTransactionDispenser3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQTransactionDispenser3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQTransactionDispenser3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQTransactionDispenser3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQTransactionDispenser3_BeginTransaction(This,ptransaction) (This)->lpVtbl->BeginTransaction(This,ptransaction)
#define IMSMQTransactionDispenser3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQTransactionDispenser3_BeginTransaction_Proxy(IMSMQTransactionDispenser3 *This,IMSMQTransaction3 **ptransaction);
  void __RPC_STUB IMSMQTransactionDispenser3_BeginTransaction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQTransactionDispenser3_get_Properties_Proxy(IMSMQTransactionDispenser3 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQTransactionDispenser3_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQTransactionDispenser;
#ifdef __cplusplus
  class MSMQTransactionDispenser;
#endif

#ifndef __IMSMQApplication_INTERFACE_DEFINED__
#define __IMSMQApplication_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQApplication;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQApplication : public IDispatch {
  public:
    virtual HRESULT WINAPI MachineIdOfMachineName(BSTR MachineName,BSTR *pbstrGuid) = 0;
  };
#else
  typedef struct IMSMQApplicationVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQApplication *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQApplication *This);
      ULONG (WINAPI *Release)(IMSMQApplication *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQApplication *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQApplication *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQApplication *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQApplication *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *MachineIdOfMachineName)(IMSMQApplication *This,BSTR MachineName,BSTR *pbstrGuid);
    END_INTERFACE
  } IMSMQApplicationVtbl;
  struct IMSMQApplication {
    CONST_VTBL struct IMSMQApplicationVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQApplication_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQApplication_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQApplication_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQApplication_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQApplication_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQApplication_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQApplication_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQApplication_MachineIdOfMachineName(This,MachineName,pbstrGuid) (This)->lpVtbl->MachineIdOfMachineName(This,MachineName,pbstrGuid)
#endif
#endif
  HRESULT WINAPI IMSMQApplication_MachineIdOfMachineName_Proxy(IMSMQApplication *This,BSTR MachineName,BSTR *pbstrGuid);
  void __RPC_STUB IMSMQApplication_MachineIdOfMachineName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQApplication2_INTERFACE_DEFINED__
#define __IMSMQApplication2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQApplication2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQApplication2 : public IMSMQApplication {
  public:
    virtual HRESULT WINAPI RegisterCertificate(VARIANT *Flags,VARIANT *ExternalCertificate) = 0;
    virtual HRESULT WINAPI MachineNameOfMachineId(BSTR bstrGuid,BSTR *pbstrMachineName) = 0;
    virtual HRESULT WINAPI get_MSMQVersionMajor(short *psMSMQVersionMajor) = 0;
    virtual HRESULT WINAPI get_MSMQVersionMinor(short *psMSMQVersionMinor) = 0;
    virtual HRESULT WINAPI get_MSMQVersionBuild(short *psMSMQVersionBuild) = 0;
    virtual HRESULT WINAPI get_IsDsEnabled(VARIANT_BOOL *pfIsDsEnabled) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQApplication2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQApplication2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQApplication2 *This);
      ULONG (WINAPI *Release)(IMSMQApplication2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQApplication2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQApplication2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQApplication2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQApplication2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *MachineIdOfMachineName)(IMSMQApplication2 *This,BSTR MachineName,BSTR *pbstrGuid);
      HRESULT (WINAPI *RegisterCertificate)(IMSMQApplication2 *This,VARIANT *Flags,VARIANT *ExternalCertificate);
      HRESULT (WINAPI *MachineNameOfMachineId)(IMSMQApplication2 *This,BSTR bstrGuid,BSTR *pbstrMachineName);
      HRESULT (WINAPI *get_MSMQVersionMajor)(IMSMQApplication2 *This,short *psMSMQVersionMajor);
      HRESULT (WINAPI *get_MSMQVersionMinor)(IMSMQApplication2 *This,short *psMSMQVersionMinor);
      HRESULT (WINAPI *get_MSMQVersionBuild)(IMSMQApplication2 *This,short *psMSMQVersionBuild);
      HRESULT (WINAPI *get_IsDsEnabled)(IMSMQApplication2 *This,VARIANT_BOOL *pfIsDsEnabled);
      HRESULT (WINAPI *get_Properties)(IMSMQApplication2 *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQApplication2Vtbl;
  struct IMSMQApplication2 {
    CONST_VTBL struct IMSMQApplication2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQApplication2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQApplication2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQApplication2_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQApplication2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQApplication2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQApplication2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQApplication2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQApplication2_MachineIdOfMachineName(This,MachineName,pbstrGuid) (This)->lpVtbl->MachineIdOfMachineName(This,MachineName,pbstrGuid)
#define IMSMQApplication2_RegisterCertificate(This,Flags,ExternalCertificate) (This)->lpVtbl->RegisterCertificate(This,Flags,ExternalCertificate)
#define IMSMQApplication2_MachineNameOfMachineId(This,bstrGuid,pbstrMachineName) (This)->lpVtbl->MachineNameOfMachineId(This,bstrGuid,pbstrMachineName)
#define IMSMQApplication2_get_MSMQVersionMajor(This,psMSMQVersionMajor) (This)->lpVtbl->get_MSMQVersionMajor(This,psMSMQVersionMajor)
#define IMSMQApplication2_get_MSMQVersionMinor(This,psMSMQVersionMinor) (This)->lpVtbl->get_MSMQVersionMinor(This,psMSMQVersionMinor)
#define IMSMQApplication2_get_MSMQVersionBuild(This,psMSMQVersionBuild) (This)->lpVtbl->get_MSMQVersionBuild(This,psMSMQVersionBuild)
#define IMSMQApplication2_get_IsDsEnabled(This,pfIsDsEnabled) (This)->lpVtbl->get_IsDsEnabled(This,pfIsDsEnabled)
#define IMSMQApplication2_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQApplication2_RegisterCertificate_Proxy(IMSMQApplication2 *This,VARIANT *Flags,VARIANT *ExternalCertificate);
  void __RPC_STUB IMSMQApplication2_RegisterCertificate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication2_MachineNameOfMachineId_Proxy(IMSMQApplication2 *This,BSTR bstrGuid,BSTR *pbstrMachineName);
  void __RPC_STUB IMSMQApplication2_MachineNameOfMachineId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication2_get_MSMQVersionMajor_Proxy(IMSMQApplication2 *This,short *psMSMQVersionMajor);
  void __RPC_STUB IMSMQApplication2_get_MSMQVersionMajor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication2_get_MSMQVersionMinor_Proxy(IMSMQApplication2 *This,short *psMSMQVersionMinor);
  void __RPC_STUB IMSMQApplication2_get_MSMQVersionMinor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication2_get_MSMQVersionBuild_Proxy(IMSMQApplication2 *This,short *psMSMQVersionBuild);
  void __RPC_STUB IMSMQApplication2_get_MSMQVersionBuild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication2_get_IsDsEnabled_Proxy(IMSMQApplication2 *This,VARIANT_BOOL *pfIsDsEnabled);
  void __RPC_STUB IMSMQApplication2_get_IsDsEnabled_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication2_get_Properties_Proxy(IMSMQApplication2 *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQApplication2_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQApplication3_INTERFACE_DEFINED__
#define __IMSMQApplication3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQApplication3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQApplication3 : public IMSMQApplication2 {
  public:
    virtual HRESULT WINAPI get_ActiveQueues(VARIANT *pvActiveQueues) = 0;
    virtual HRESULT WINAPI get_PrivateQueues(VARIANT *pvPrivateQueues) = 0;
    virtual HRESULT WINAPI get_DirectoryServiceServer(BSTR *pbstrDirectoryServiceServer) = 0;
    virtual HRESULT WINAPI get_IsConnected(VARIANT_BOOL *pfIsConnected) = 0;
    virtual HRESULT WINAPI get_BytesInAllQueues(VARIANT *pvBytesInAllQueues) = 0;
    virtual HRESULT WINAPI put_Machine(BSTR bstrMachine) = 0;
    virtual HRESULT WINAPI get_Machine(BSTR *pbstrMachine) = 0;
    virtual HRESULT WINAPI Connect(void) = 0;
    virtual HRESULT WINAPI Disconnect(void) = 0;
    virtual HRESULT WINAPI Tidy(void) = 0;
  };
#else
  typedef struct IMSMQApplication3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQApplication3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQApplication3 *This);
      ULONG (WINAPI *Release)(IMSMQApplication3 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQApplication3 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQApplication3 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQApplication3 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQApplication3 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *MachineIdOfMachineName)(IMSMQApplication3 *This,BSTR MachineName,BSTR *pbstrGuid);
      HRESULT (WINAPI *RegisterCertificate)(IMSMQApplication3 *This,VARIANT *Flags,VARIANT *ExternalCertificate);
      HRESULT (WINAPI *MachineNameOfMachineId)(IMSMQApplication3 *This,BSTR bstrGuid,BSTR *pbstrMachineName);
      HRESULT (WINAPI *get_MSMQVersionMajor)(IMSMQApplication3 *This,short *psMSMQVersionMajor);
      HRESULT (WINAPI *get_MSMQVersionMinor)(IMSMQApplication3 *This,short *psMSMQVersionMinor);
      HRESULT (WINAPI *get_MSMQVersionBuild)(IMSMQApplication3 *This,short *psMSMQVersionBuild);
      HRESULT (WINAPI *get_IsDsEnabled)(IMSMQApplication3 *This,VARIANT_BOOL *pfIsDsEnabled);
      HRESULT (WINAPI *get_Properties)(IMSMQApplication3 *This,IDispatch **ppcolProperties);
      HRESULT (WINAPI *get_ActiveQueues)(IMSMQApplication3 *This,VARIANT *pvActiveQueues);
      HRESULT (WINAPI *get_PrivateQueues)(IMSMQApplication3 *This,VARIANT *pvPrivateQueues);
      HRESULT (WINAPI *get_DirectoryServiceServer)(IMSMQApplication3 *This,BSTR *pbstrDirectoryServiceServer);
      HRESULT (WINAPI *get_IsConnected)(IMSMQApplication3 *This,VARIANT_BOOL *pfIsConnected);
      HRESULT (WINAPI *get_BytesInAllQueues)(IMSMQApplication3 *This,VARIANT *pvBytesInAllQueues);
      HRESULT (WINAPI *put_Machine)(IMSMQApplication3 *This,BSTR bstrMachine);
      HRESULT (WINAPI *get_Machine)(IMSMQApplication3 *This,BSTR *pbstrMachine);
      HRESULT (WINAPI *Connect)(IMSMQApplication3 *This);
      HRESULT (WINAPI *Disconnect)(IMSMQApplication3 *This);
      HRESULT (WINAPI *Tidy)(IMSMQApplication3 *This);
    END_INTERFACE
  } IMSMQApplication3Vtbl;
  struct IMSMQApplication3 {
    CONST_VTBL struct IMSMQApplication3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQApplication3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQApplication3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQApplication3_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQApplication3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQApplication3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQApplication3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQApplication3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQApplication3_MachineIdOfMachineName(This,MachineName,pbstrGuid) (This)->lpVtbl->MachineIdOfMachineName(This,MachineName,pbstrGuid)
#define IMSMQApplication3_RegisterCertificate(This,Flags,ExternalCertificate) (This)->lpVtbl->RegisterCertificate(This,Flags,ExternalCertificate)
#define IMSMQApplication3_MachineNameOfMachineId(This,bstrGuid,pbstrMachineName) (This)->lpVtbl->MachineNameOfMachineId(This,bstrGuid,pbstrMachineName)
#define IMSMQApplication3_get_MSMQVersionMajor(This,psMSMQVersionMajor) (This)->lpVtbl->get_MSMQVersionMajor(This,psMSMQVersionMajor)
#define IMSMQApplication3_get_MSMQVersionMinor(This,psMSMQVersionMinor) (This)->lpVtbl->get_MSMQVersionMinor(This,psMSMQVersionMinor)
#define IMSMQApplication3_get_MSMQVersionBuild(This,psMSMQVersionBuild) (This)->lpVtbl->get_MSMQVersionBuild(This,psMSMQVersionBuild)
#define IMSMQApplication3_get_IsDsEnabled(This,pfIsDsEnabled) (This)->lpVtbl->get_IsDsEnabled(This,pfIsDsEnabled)
#define IMSMQApplication3_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#define IMSMQApplication3_get_ActiveQueues(This,pvActiveQueues) (This)->lpVtbl->get_ActiveQueues(This,pvActiveQueues)
#define IMSMQApplication3_get_PrivateQueues(This,pvPrivateQueues) (This)->lpVtbl->get_PrivateQueues(This,pvPrivateQueues)
#define IMSMQApplication3_get_DirectoryServiceServer(This,pbstrDirectoryServiceServer) (This)->lpVtbl->get_DirectoryServiceServer(This,pbstrDirectoryServiceServer)
#define IMSMQApplication3_get_IsConnected(This,pfIsConnected) (This)->lpVtbl->get_IsConnected(This,pfIsConnected)
#define IMSMQApplication3_get_BytesInAllQueues(This,pvBytesInAllQueues) (This)->lpVtbl->get_BytesInAllQueues(This,pvBytesInAllQueues)
#define IMSMQApplication3_put_Machine(This,bstrMachine) (This)->lpVtbl->put_Machine(This,bstrMachine)
#define IMSMQApplication3_get_Machine(This,pbstrMachine) (This)->lpVtbl->get_Machine(This,pbstrMachine)
#define IMSMQApplication3_Connect(This) (This)->lpVtbl->Connect(This)
#define IMSMQApplication3_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#define IMSMQApplication3_Tidy(This) (This)->lpVtbl->Tidy(This)
#endif
#endif
  HRESULT WINAPI IMSMQApplication3_get_ActiveQueues_Proxy(IMSMQApplication3 *This,VARIANT *pvActiveQueues);
  void __RPC_STUB IMSMQApplication3_get_ActiveQueues_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_get_PrivateQueues_Proxy(IMSMQApplication3 *This,VARIANT *pvPrivateQueues);
  void __RPC_STUB IMSMQApplication3_get_PrivateQueues_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_get_DirectoryServiceServer_Proxy(IMSMQApplication3 *This,BSTR *pbstrDirectoryServiceServer);
  void __RPC_STUB IMSMQApplication3_get_DirectoryServiceServer_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_get_IsConnected_Proxy(IMSMQApplication3 *This,VARIANT_BOOL *pfIsConnected);
  void __RPC_STUB IMSMQApplication3_get_IsConnected_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_get_BytesInAllQueues_Proxy(IMSMQApplication3 *This,VARIANT *pvBytesInAllQueues);
  void __RPC_STUB IMSMQApplication3_get_BytesInAllQueues_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_put_Machine_Proxy(IMSMQApplication3 *This,BSTR bstrMachine);
  void __RPC_STUB IMSMQApplication3_put_Machine_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_get_Machine_Proxy(IMSMQApplication3 *This,BSTR *pbstrMachine);
  void __RPC_STUB IMSMQApplication3_get_Machine_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_Connect_Proxy(IMSMQApplication3 *This);
  void __RPC_STUB IMSMQApplication3_Connect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_Disconnect_Proxy(IMSMQApplication3 *This);
  void __RPC_STUB IMSMQApplication3_Disconnect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQApplication3_Tidy_Proxy(IMSMQApplication3 *This);
  void __RPC_STUB IMSMQApplication3_Tidy_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQApplication;
#ifdef __cplusplus
  class MSMQApplication;
#endif

#ifndef __IMSMQDestination_INTERFACE_DEFINED__
#define __IMSMQDestination_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQDestination;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQDestination : public IDispatch {
  public:
    virtual HRESULT WINAPI Open(void) = 0;
    virtual HRESULT WINAPI Close(void) = 0;
    virtual HRESULT WINAPI get_IsOpen(VARIANT_BOOL *pfIsOpen) = 0;
    virtual HRESULT WINAPI get_IADs(IDispatch **ppIADs) = 0;
    virtual HRESULT WINAPI putref_IADs(IDispatch *pIADs) = 0;
    virtual HRESULT WINAPI get_ADsPath(BSTR *pbstrADsPath) = 0;
    virtual HRESULT WINAPI put_ADsPath(BSTR bstrADsPath) = 0;
    virtual HRESULT WINAPI get_PathName(BSTR *pbstrPathName) = 0;
    virtual HRESULT WINAPI put_PathName(BSTR bstrPathName) = 0;
    virtual HRESULT WINAPI get_FormatName(BSTR *pbstrFormatName) = 0;
    virtual HRESULT WINAPI put_FormatName(BSTR bstrFormatName) = 0;
    virtual HRESULT WINAPI get_Destinations(IDispatch **ppDestinations) = 0;
    virtual HRESULT WINAPI putref_Destinations(IDispatch *pDestinations) = 0;
    virtual HRESULT WINAPI get_Properties(IDispatch **ppcolProperties) = 0;
  };
#else
  typedef struct IMSMQDestinationVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQDestination *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQDestination *This);
      ULONG (WINAPI *Release)(IMSMQDestination *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQDestination *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQDestination *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQDestination *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQDestination *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *Open)(IMSMQDestination *This);
      HRESULT (WINAPI *Close)(IMSMQDestination *This);
      HRESULT (WINAPI *get_IsOpen)(IMSMQDestination *This,VARIANT_BOOL *pfIsOpen);
      HRESULT (WINAPI *get_IADs)(IMSMQDestination *This,IDispatch **ppIADs);
      HRESULT (WINAPI *putref_IADs)(IMSMQDestination *This,IDispatch *pIADs);
      HRESULT (WINAPI *get_ADsPath)(IMSMQDestination *This,BSTR *pbstrADsPath);
      HRESULT (WINAPI *put_ADsPath)(IMSMQDestination *This,BSTR bstrADsPath);
      HRESULT (WINAPI *get_PathName)(IMSMQDestination *This,BSTR *pbstrPathName);
      HRESULT (WINAPI *put_PathName)(IMSMQDestination *This,BSTR bstrPathName);
      HRESULT (WINAPI *get_FormatName)(IMSMQDestination *This,BSTR *pbstrFormatName);
      HRESULT (WINAPI *put_FormatName)(IMSMQDestination *This,BSTR bstrFormatName);
      HRESULT (WINAPI *get_Destinations)(IMSMQDestination *This,IDispatch **ppDestinations);
      HRESULT (WINAPI *putref_Destinations)(IMSMQDestination *This,IDispatch *pDestinations);
      HRESULT (WINAPI *get_Properties)(IMSMQDestination *This,IDispatch **ppcolProperties);
    END_INTERFACE
  } IMSMQDestinationVtbl;
  struct IMSMQDestination {
    CONST_VTBL struct IMSMQDestinationVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQDestination_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQDestination_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQDestination_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQDestination_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQDestination_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQDestination_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQDestination_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQDestination_Open(This) (This)->lpVtbl->Open(This)
#define IMSMQDestination_Close(This) (This)->lpVtbl->Close(This)
#define IMSMQDestination_get_IsOpen(This,pfIsOpen) (This)->lpVtbl->get_IsOpen(This,pfIsOpen)
#define IMSMQDestination_get_IADs(This,ppIADs) (This)->lpVtbl->get_IADs(This,ppIADs)
#define IMSMQDestination_putref_IADs(This,pIADs) (This)->lpVtbl->putref_IADs(This,pIADs)
#define IMSMQDestination_get_ADsPath(This,pbstrADsPath) (This)->lpVtbl->get_ADsPath(This,pbstrADsPath)
#define IMSMQDestination_put_ADsPath(This,bstrADsPath) (This)->lpVtbl->put_ADsPath(This,bstrADsPath)
#define IMSMQDestination_get_PathName(This,pbstrPathName) (This)->lpVtbl->get_PathName(This,pbstrPathName)
#define IMSMQDestination_put_PathName(This,bstrPathName) (This)->lpVtbl->put_PathName(This,bstrPathName)
#define IMSMQDestination_get_FormatName(This,pbstrFormatName) (This)->lpVtbl->get_FormatName(This,pbstrFormatName)
#define IMSMQDestination_put_FormatName(This,bstrFormatName) (This)->lpVtbl->put_FormatName(This,bstrFormatName)
#define IMSMQDestination_get_Destinations(This,ppDestinations) (This)->lpVtbl->get_Destinations(This,ppDestinations)
#define IMSMQDestination_putref_Destinations(This,pDestinations) (This)->lpVtbl->putref_Destinations(This,pDestinations)
#define IMSMQDestination_get_Properties(This,ppcolProperties) (This)->lpVtbl->get_Properties(This,ppcolProperties)
#endif
#endif
  HRESULT WINAPI IMSMQDestination_Open_Proxy(IMSMQDestination *This);
  void __RPC_STUB IMSMQDestination_Open_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_Close_Proxy(IMSMQDestination *This);
  void __RPC_STUB IMSMQDestination_Close_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_get_IsOpen_Proxy(IMSMQDestination *This,VARIANT_BOOL *pfIsOpen);
  void __RPC_STUB IMSMQDestination_get_IsOpen_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_get_IADs_Proxy(IMSMQDestination *This,IDispatch **ppIADs);
  void __RPC_STUB IMSMQDestination_get_IADs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_putref_IADs_Proxy(IMSMQDestination *This,IDispatch *pIADs);
  void __RPC_STUB IMSMQDestination_putref_IADs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_get_ADsPath_Proxy(IMSMQDestination *This,BSTR *pbstrADsPath);
  void __RPC_STUB IMSMQDestination_get_ADsPath_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_put_ADsPath_Proxy(IMSMQDestination *This,BSTR bstrADsPath);
  void __RPC_STUB IMSMQDestination_put_ADsPath_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_get_PathName_Proxy(IMSMQDestination *This,BSTR *pbstrPathName);
  void __RPC_STUB IMSMQDestination_get_PathName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_put_PathName_Proxy(IMSMQDestination *This,BSTR bstrPathName);
  void __RPC_STUB IMSMQDestination_put_PathName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_get_FormatName_Proxy(IMSMQDestination *This,BSTR *pbstrFormatName);
  void __RPC_STUB IMSMQDestination_get_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_put_FormatName_Proxy(IMSMQDestination *This,BSTR bstrFormatName);
  void __RPC_STUB IMSMQDestination_put_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_get_Destinations_Proxy(IMSMQDestination *This,IDispatch **ppDestinations);
  void __RPC_STUB IMSMQDestination_get_Destinations_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_putref_Destinations_Proxy(IMSMQDestination *This,IDispatch *pDestinations);
  void __RPC_STUB IMSMQDestination_putref_Destinations_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQDestination_get_Properties_Proxy(IMSMQDestination *This,IDispatch **ppcolProperties);
  void __RPC_STUB IMSMQDestination_get_Properties_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMSMQPrivateDestination_INTERFACE_DEFINED__
#define __IMSMQPrivateDestination_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQPrivateDestination;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQPrivateDestination : public IDispatch {
  public:
    virtual HRESULT WINAPI get_Handle(VARIANT *pvarHandle) = 0;
    virtual HRESULT WINAPI put_Handle(VARIANT varHandle) = 0;
  };
#else
  typedef struct IMSMQPrivateDestinationVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQPrivateDestination *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQPrivateDestination *This);
      ULONG (WINAPI *Release)(IMSMQPrivateDestination *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQPrivateDestination *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQPrivateDestination *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQPrivateDestination *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQPrivateDestination *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_Handle)(IMSMQPrivateDestination *This,VARIANT *pvarHandle);
      HRESULT (WINAPI *put_Handle)(IMSMQPrivateDestination *This,VARIANT varHandle);
    END_INTERFACE
  } IMSMQPrivateDestinationVtbl;
  struct IMSMQPrivateDestination {
    CONST_VTBL struct IMSMQPrivateDestinationVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQPrivateDestination_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQPrivateDestination_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQPrivateDestination_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQPrivateDestination_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQPrivateDestination_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQPrivateDestination_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQPrivateDestination_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQPrivateDestination_get_Handle(This,pvarHandle) (This)->lpVtbl->get_Handle(This,pvarHandle)
#define IMSMQPrivateDestination_put_Handle(This,varHandle) (This)->lpVtbl->put_Handle(This,varHandle)
#endif
#endif
  HRESULT WINAPI IMSMQPrivateDestination_get_Handle_Proxy(IMSMQPrivateDestination *This,VARIANT *pvarHandle);
  void __RPC_STUB IMSMQPrivateDestination_get_Handle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQPrivateDestination_put_Handle_Proxy(IMSMQPrivateDestination *This,VARIANT varHandle);
  void __RPC_STUB IMSMQPrivateDestination_put_Handle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQDestination;
#ifdef __cplusplus
  class MSMQDestination;
#endif

#ifndef __IMSMQCollection_INTERFACE_DEFINED__
#define __IMSMQCollection_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQCollection;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQCollection : public IDispatch {
  public:
    virtual HRESULT WINAPI Item(VARIANT *Index,VARIANT *pvarRet) = 0;
    virtual HRESULT WINAPI get_Count(__LONG32 *pCount) = 0;
    virtual HRESULT WINAPI _NewEnum(IUnknown **ppunk) = 0;
  };
#else
  typedef struct IMSMQCollectionVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQCollection *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQCollection *This);
      ULONG (WINAPI *Release)(IMSMQCollection *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQCollection *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQCollection *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQCollection *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQCollection *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *Item)(IMSMQCollection *This,VARIANT *Index,VARIANT *pvarRet);
      HRESULT (WINAPI *get_Count)(IMSMQCollection *This,__LONG32 *pCount);
      HRESULT (WINAPI *_NewEnum)(IMSMQCollection *This,IUnknown **ppunk);
    END_INTERFACE
  } IMSMQCollectionVtbl;
  struct IMSMQCollection {
    CONST_VTBL struct IMSMQCollectionVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQCollection_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQCollection_Item(This,Index,pvarRet) (This)->lpVtbl->Item(This,Index,pvarRet)
#define IMSMQCollection_get_Count(This,pCount) (This)->lpVtbl->get_Count(This,pCount)
#define IMSMQCollection__NewEnum(This,ppunk) (This)->lpVtbl->_NewEnum(This,ppunk)
#endif
#endif
  HRESULT WINAPI IMSMQCollection_Item_Proxy(IMSMQCollection *This,VARIANT *Index,VARIANT *pvarRet);
  void __RPC_STUB IMSMQCollection_Item_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQCollection_get_Count_Proxy(IMSMQCollection *This,__LONG32 *pCount);
  void __RPC_STUB IMSMQCollection_get_Count_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQCollection__NewEnum_Proxy(IMSMQCollection *This,IUnknown **ppunk);
  void __RPC_STUB IMSMQCollection__NewEnum_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQCollection;
#ifdef __cplusplus
  class MSMQCollection;
#endif

#ifndef __IMSMQManagement_INTERFACE_DEFINED__
#define __IMSMQManagement_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQManagement;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQManagement : public IDispatch {
  public:
    virtual HRESULT WINAPI Init(VARIANT *Machine,VARIANT *Pathname,VARIANT *FormatName) = 0;
    virtual HRESULT WINAPI get_FormatName(BSTR *pbstrFormatName) = 0;
    virtual HRESULT WINAPI get_Machine(BSTR *pbstrMachine) = 0;
    virtual HRESULT WINAPI get_MessageCount(__LONG32 *plMessageCount) = 0;
    virtual HRESULT WINAPI get_ForeignStatus(__LONG32 *plForeignStatus) = 0;
    virtual HRESULT WINAPI get_QueueType(__LONG32 *plQueueType) = 0;
    virtual HRESULT WINAPI get_IsLocal(VARIANT_BOOL *pfIsLocal) = 0;
    virtual HRESULT WINAPI get_TransactionalStatus(__LONG32 *plTransactionalStatus) = 0;
    virtual HRESULT WINAPI get_BytesInQueue(VARIANT *pvBytesInQueue) = 0;
  };
#else
  typedef struct IMSMQManagementVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQManagement *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQManagement *This);
      ULONG (WINAPI *Release)(IMSMQManagement *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQManagement *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQManagement *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQManagement *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQManagement *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *Init)(IMSMQManagement *This,VARIANT *Machine,VARIANT *Pathname,VARIANT *FormatName);
      HRESULT (WINAPI *get_FormatName)(IMSMQManagement *This,BSTR *pbstrFormatName);
      HRESULT (WINAPI *get_Machine)(IMSMQManagement *This,BSTR *pbstrMachine);
      HRESULT (WINAPI *get_MessageCount)(IMSMQManagement *This,__LONG32 *plMessageCount);
      HRESULT (WINAPI *get_ForeignStatus)(IMSMQManagement *This,__LONG32 *plForeignStatus);
      HRESULT (WINAPI *get_QueueType)(IMSMQManagement *This,__LONG32 *plQueueType);
      HRESULT (WINAPI *get_IsLocal)(IMSMQManagement *This,VARIANT_BOOL *pfIsLocal);
      HRESULT (WINAPI *get_TransactionalStatus)(IMSMQManagement *This,__LONG32 *plTransactionalStatus);
      HRESULT (WINAPI *get_BytesInQueue)(IMSMQManagement *This,VARIANT *pvBytesInQueue);
    END_INTERFACE
  } IMSMQManagementVtbl;
  struct IMSMQManagement {
    CONST_VTBL struct IMSMQManagementVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQManagement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQManagement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQManagement_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQManagement_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQManagement_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQManagement_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQManagement_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQManagement_Init(This,Machine,Pathname,FormatName) (This)->lpVtbl->Init(This,Machine,Pathname,FormatName)
#define IMSMQManagement_get_FormatName(This,pbstrFormatName) (This)->lpVtbl->get_FormatName(This,pbstrFormatName)
#define IMSMQManagement_get_Machine(This,pbstrMachine) (This)->lpVtbl->get_Machine(This,pbstrMachine)
#define IMSMQManagement_get_MessageCount(This,plMessageCount) (This)->lpVtbl->get_MessageCount(This,plMessageCount)
#define IMSMQManagement_get_ForeignStatus(This,plForeignStatus) (This)->lpVtbl->get_ForeignStatus(This,plForeignStatus)
#define IMSMQManagement_get_QueueType(This,plQueueType) (This)->lpVtbl->get_QueueType(This,plQueueType)
#define IMSMQManagement_get_IsLocal(This,pfIsLocal) (This)->lpVtbl->get_IsLocal(This,pfIsLocal)
#define IMSMQManagement_get_TransactionalStatus(This,plTransactionalStatus) (This)->lpVtbl->get_TransactionalStatus(This,plTransactionalStatus)
#define IMSMQManagement_get_BytesInQueue(This,pvBytesInQueue) (This)->lpVtbl->get_BytesInQueue(This,pvBytesInQueue)
#endif
#endif
  HRESULT WINAPI IMSMQManagement_Init_Proxy(IMSMQManagement *This,VARIANT *Machine,VARIANT *Pathname,VARIANT *FormatName);
  void __RPC_STUB IMSMQManagement_Init_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQManagement_get_FormatName_Proxy(IMSMQManagement *This,BSTR *pbstrFormatName);
  void __RPC_STUB IMSMQManagement_get_FormatName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQManagement_get_Machine_Proxy(IMSMQManagement *This,BSTR *pbstrMachine);
  void __RPC_STUB IMSMQManagement_get_Machine_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQManagement_get_MessageCount_Proxy(IMSMQManagement *This,__LONG32 *plMessageCount);
  void __RPC_STUB IMSMQManagement_get_MessageCount_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQManagement_get_ForeignStatus_Proxy(IMSMQManagement *This,__LONG32 *plForeignStatus);
  void __RPC_STUB IMSMQManagement_get_ForeignStatus_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQManagement_get_QueueType_Proxy(IMSMQManagement *This,__LONG32 *plQueueType);
  void __RPC_STUB IMSMQManagement_get_QueueType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQManagement_get_IsLocal_Proxy(IMSMQManagement *This,VARIANT_BOOL *pfIsLocal);
  void __RPC_STUB IMSMQManagement_get_IsLocal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQManagement_get_TransactionalStatus_Proxy(IMSMQManagement *This,__LONG32 *plTransactionalStatus);
  void __RPC_STUB IMSMQManagement_get_TransactionalStatus_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQManagement_get_BytesInQueue_Proxy(IMSMQManagement *This,VARIANT *pvBytesInQueue);
  void __RPC_STUB IMSMQManagement_get_BytesInQueue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQManagement;
#ifdef __cplusplus
  class MSMQManagement;
#endif

#ifndef __IMSMQOutgoingQueueManagement_INTERFACE_DEFINED__
#define __IMSMQOutgoingQueueManagement_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQOutgoingQueueManagement;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQOutgoingQueueManagement : public IMSMQManagement {
  public:
    virtual HRESULT WINAPI get_State(__LONG32 *plState) = 0;
    virtual HRESULT WINAPI get_NextHops(VARIANT *pvNextHops) = 0;
    virtual HRESULT WINAPI EodGetSendInfo(IMSMQCollection **ppCollection) = 0;
    virtual HRESULT WINAPI Resume(void) = 0;
    virtual HRESULT WINAPI Pause(void) = 0;
    virtual HRESULT WINAPI EodResend(void) = 0;
  };
#else
  typedef struct IMSMQOutgoingQueueManagementVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQOutgoingQueueManagement *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQOutgoingQueueManagement *This);
      ULONG (WINAPI *Release)(IMSMQOutgoingQueueManagement *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQOutgoingQueueManagement *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQOutgoingQueueManagement *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQOutgoingQueueManagement *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQOutgoingQueueManagement *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *Init)(IMSMQOutgoingQueueManagement *This,VARIANT *Machine,VARIANT *Pathname,VARIANT *FormatName);
      HRESULT (WINAPI *get_FormatName)(IMSMQOutgoingQueueManagement *This,BSTR *pbstrFormatName);
      HRESULT (WINAPI *get_Machine)(IMSMQOutgoingQueueManagement *This,BSTR *pbstrMachine);
      HRESULT (WINAPI *get_MessageCount)(IMSMQOutgoingQueueManagement *This,__LONG32 *plMessageCount);
      HRESULT (WINAPI *get_ForeignStatus)(IMSMQOutgoingQueueManagement *This,__LONG32 *plForeignStatus);
      HRESULT (WINAPI *get_QueueType)(IMSMQOutgoingQueueManagement *This,__LONG32 *plQueueType);
      HRESULT (WINAPI *get_IsLocal)(IMSMQOutgoingQueueManagement *This,VARIANT_BOOL *pfIsLocal);
      HRESULT (WINAPI *get_TransactionalStatus)(IMSMQOutgoingQueueManagement *This,__LONG32 *plTransactionalStatus);
      HRESULT (WINAPI *get_BytesInQueue)(IMSMQOutgoingQueueManagement *This,VARIANT *pvBytesInQueue);
      HRESULT (WINAPI *get_State)(IMSMQOutgoingQueueManagement *This,__LONG32 *plState);
      HRESULT (WINAPI *get_NextHops)(IMSMQOutgoingQueueManagement *This,VARIANT *pvNextHops);
      HRESULT (WINAPI *EodGetSendInfo)(IMSMQOutgoingQueueManagement *This,IMSMQCollection **ppCollection);
      HRESULT (WINAPI *Resume)(IMSMQOutgoingQueueManagement *This);
      HRESULT (WINAPI *Pause)(IMSMQOutgoingQueueManagement *This);
      HRESULT (WINAPI *EodResend)(IMSMQOutgoingQueueManagement *This);
    END_INTERFACE
  } IMSMQOutgoingQueueManagementVtbl;
  struct IMSMQOutgoingQueueManagement {
    CONST_VTBL struct IMSMQOutgoingQueueManagementVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQOutgoingQueueManagement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQOutgoingQueueManagement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQOutgoingQueueManagement_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQOutgoingQueueManagement_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQOutgoingQueueManagement_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQOutgoingQueueManagement_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQOutgoingQueueManagement_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQOutgoingQueueManagement_Init(This,Machine,Pathname,FormatName) (This)->lpVtbl->Init(This,Machine,Pathname,FormatName)
#define IMSMQOutgoingQueueManagement_get_FormatName(This,pbstrFormatName) (This)->lpVtbl->get_FormatName(This,pbstrFormatName)
#define IMSMQOutgoingQueueManagement_get_Machine(This,pbstrMachine) (This)->lpVtbl->get_Machine(This,pbstrMachine)
#define IMSMQOutgoingQueueManagement_get_MessageCount(This,plMessageCount) (This)->lpVtbl->get_MessageCount(This,plMessageCount)
#define IMSMQOutgoingQueueManagement_get_ForeignStatus(This,plForeignStatus) (This)->lpVtbl->get_ForeignStatus(This,plForeignStatus)
#define IMSMQOutgoingQueueManagement_get_QueueType(This,plQueueType) (This)->lpVtbl->get_QueueType(This,plQueueType)
#define IMSMQOutgoingQueueManagement_get_IsLocal(This,pfIsLocal) (This)->lpVtbl->get_IsLocal(This,pfIsLocal)
#define IMSMQOutgoingQueueManagement_get_TransactionalStatus(This,plTransactionalStatus) (This)->lpVtbl->get_TransactionalStatus(This,plTransactionalStatus)
#define IMSMQOutgoingQueueManagement_get_BytesInQueue(This,pvBytesInQueue) (This)->lpVtbl->get_BytesInQueue(This,pvBytesInQueue)
#define IMSMQOutgoingQueueManagement_get_State(This,plState) (This)->lpVtbl->get_State(This,plState)
#define IMSMQOutgoingQueueManagement_get_NextHops(This,pvNextHops) (This)->lpVtbl->get_NextHops(This,pvNextHops)
#define IMSMQOutgoingQueueManagement_EodGetSendInfo(This,ppCollection) (This)->lpVtbl->EodGetSendInfo(This,ppCollection)
#define IMSMQOutgoingQueueManagement_Resume(This) (This)->lpVtbl->Resume(This)
#define IMSMQOutgoingQueueManagement_Pause(This) (This)->lpVtbl->Pause(This)
#define IMSMQOutgoingQueueManagement_EodResend(This) (This)->lpVtbl->EodResend(This)
#endif
#endif
  HRESULT WINAPI IMSMQOutgoingQueueManagement_get_State_Proxy(IMSMQOutgoingQueueManagement *This,__LONG32 *plState);
  void __RPC_STUB IMSMQOutgoingQueueManagement_get_State_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQOutgoingQueueManagement_get_NextHops_Proxy(IMSMQOutgoingQueueManagement *This,VARIANT *pvNextHops);
  void __RPC_STUB IMSMQOutgoingQueueManagement_get_NextHops_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQOutgoingQueueManagement_EodGetSendInfo_Proxy(IMSMQOutgoingQueueManagement *This,IMSMQCollection **ppCollection);
  void __RPC_STUB IMSMQOutgoingQueueManagement_EodGetSendInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQOutgoingQueueManagement_Resume_Proxy(IMSMQOutgoingQueueManagement *This);
  void __RPC_STUB IMSMQOutgoingQueueManagement_Resume_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQOutgoingQueueManagement_Pause_Proxy(IMSMQOutgoingQueueManagement *This);
  void __RPC_STUB IMSMQOutgoingQueueManagement_Pause_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQOutgoingQueueManagement_EodResend_Proxy(IMSMQOutgoingQueueManagement *This);
  void __RPC_STUB IMSMQOutgoingQueueManagement_EodResend_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQOutgoingQueueManagement;
#ifdef __cplusplus
  class MSMQOutgoingQueueManagement;
#endif

#ifndef __IMSMQQueueManagement_INTERFACE_DEFINED__
#define __IMSMQQueueManagement_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMSMQQueueManagement;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMSMQQueueManagement : public IMSMQManagement {
  public:
    virtual HRESULT WINAPI get_JournalMessageCount(__LONG32 *plJournalMessageCount) = 0;
    virtual HRESULT WINAPI get_BytesInJournal(VARIANT *pvBytesInJournal) = 0;
    virtual HRESULT WINAPI EodGetReceiveInfo(VARIANT *pvCollection) = 0;
  };
#else
  typedef struct IMSMQQueueManagementVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMSMQQueueManagement *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMSMQQueueManagement *This);
      ULONG (WINAPI *Release)(IMSMQQueueManagement *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IMSMQQueueManagement *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IMSMQQueueManagement *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IMSMQQueueManagement *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IMSMQQueueManagement *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *Init)(IMSMQQueueManagement *This,VARIANT *Machine,VARIANT *Pathname,VARIANT *FormatName);
      HRESULT (WINAPI *get_FormatName)(IMSMQQueueManagement *This,BSTR *pbstrFormatName);
      HRESULT (WINAPI *get_Machine)(IMSMQQueueManagement *This,BSTR *pbstrMachine);
      HRESULT (WINAPI *get_MessageCount)(IMSMQQueueManagement *This,__LONG32 *plMessageCount);
      HRESULT (WINAPI *get_ForeignStatus)(IMSMQQueueManagement *This,__LONG32 *plForeignStatus);
      HRESULT (WINAPI *get_QueueType)(IMSMQQueueManagement *This,__LONG32 *plQueueType);
      HRESULT (WINAPI *get_IsLocal)(IMSMQQueueManagement *This,VARIANT_BOOL *pfIsLocal);
      HRESULT (WINAPI *get_TransactionalStatus)(IMSMQQueueManagement *This,__LONG32 *plTransactionalStatus);
      HRESULT (WINAPI *get_BytesInQueue)(IMSMQQueueManagement *This,VARIANT *pvBytesInQueue);
      HRESULT (WINAPI *get_JournalMessageCount)(IMSMQQueueManagement *This,__LONG32 *plJournalMessageCount);
      HRESULT (WINAPI *get_BytesInJournal)(IMSMQQueueManagement *This,VARIANT *pvBytesInJournal);
      HRESULT (WINAPI *EodGetReceiveInfo)(IMSMQQueueManagement *This,VARIANT *pvCollection);
    END_INTERFACE
  } IMSMQQueueManagementVtbl;
  struct IMSMQQueueManagement {
    CONST_VTBL struct IMSMQQueueManagementVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMSMQQueueManagement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMSMQQueueManagement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMSMQQueueManagement_Release(This) (This)->lpVtbl->Release(This)
#define IMSMQQueueManagement_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMSMQQueueManagement_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMSMQQueueManagement_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMSMQQueueManagement_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IMSMQQueueManagement_Init(This,Machine,Pathname,FormatName) (This)->lpVtbl->Init(This,Machine,Pathname,FormatName)
#define IMSMQQueueManagement_get_FormatName(This,pbstrFormatName) (This)->lpVtbl->get_FormatName(This,pbstrFormatName)
#define IMSMQQueueManagement_get_Machine(This,pbstrMachine) (This)->lpVtbl->get_Machine(This,pbstrMachine)
#define IMSMQQueueManagement_get_MessageCount(This,plMessageCount) (This)->lpVtbl->get_MessageCount(This,plMessageCount)
#define IMSMQQueueManagement_get_ForeignStatus(This,plForeignStatus) (This)->lpVtbl->get_ForeignStatus(This,plForeignStatus)
#define IMSMQQueueManagement_get_QueueType(This,plQueueType) (This)->lpVtbl->get_QueueType(This,plQueueType)
#define IMSMQQueueManagement_get_IsLocal(This,pfIsLocal) (This)->lpVtbl->get_IsLocal(This,pfIsLocal)
#define IMSMQQueueManagement_get_TransactionalStatus(This,plTransactionalStatus) (This)->lpVtbl->get_TransactionalStatus(This,plTransactionalStatus)
#define IMSMQQueueManagement_get_BytesInQueue(This,pvBytesInQueue) (This)->lpVtbl->get_BytesInQueue(This,pvBytesInQueue)
#define IMSMQQueueManagement_get_JournalMessageCount(This,plJournalMessageCount) (This)->lpVtbl->get_JournalMessageCount(This,plJournalMessageCount)
#define IMSMQQueueManagement_get_BytesInJournal(This,pvBytesInJournal) (This)->lpVtbl->get_BytesInJournal(This,pvBytesInJournal)
#define IMSMQQueueManagement_EodGetReceiveInfo(This,pvCollection) (This)->lpVtbl->EodGetReceiveInfo(This,pvCollection)
#endif
#endif
  HRESULT WINAPI IMSMQQueueManagement_get_JournalMessageCount_Proxy(IMSMQQueueManagement *This,__LONG32 *plJournalMessageCount);
  void __RPC_STUB IMSMQQueueManagement_get_JournalMessageCount_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueManagement_get_BytesInJournal_Proxy(IMSMQQueueManagement *This,VARIANT *pvBytesInJournal);
  void __RPC_STUB IMSMQQueueManagement_get_BytesInJournal_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMSMQQueueManagement_EodGetReceiveInfo_Proxy(IMSMQQueueManagement *This,VARIANT *pvCollection);
  void __RPC_STUB IMSMQQueueManagement_EodGetReceiveInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_MSMQQueueManagement;
#ifdef __cplusplus
  class MSMQQueueManagement;
#endif
#endif

#ifdef __cplusplus
}
#endif
#endif
