/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define IPP_E_FIRST __MSABI_LONG(0x40048000)
#define IPP_E_SYNCCONFLICT __MSABI_LONG(0xC0048003)
#define IPP_E_FILENOTDIRTY __MSABI_LONG(0xC0048004)
#define IPP_E_MARKFOROFFLINE_FAILED __MSABI_LONG(0xC0048006)
#define IPP_E_OFFLINE __MSABI_LONG(0xC0048007)
#define IPP_E_UNSYNCHRONIZED __MSABI_LONG(0xC0048008)
#define IPP_E_SERVERTYPE_NOT_SUPPORTED __MSABI_LONG(0xC004800A)
#define IPP_E_MDAC_VERSION __MSABI_LONG(0xC004800D)
#define IPP_E_COLLECTIONEXISTS __MSABI_LONG(0xC004800E)
#define IPP_E_CANNOTCREATEOFFLINE __MSABI_LONG(0xC004800F)
#define IPP_E_STATUS_CANNOTCOMPLETE __MSABI_LONG(0xC0048101)
#define IPP_E_RESELECTPROVIDER __MSABI_LONG(0xC0048102)
#define IPP_E_CLIENTMUSTEMULATE __MSABI_LONG(0xC0048103)
#define IPP_S_WEAKRESERVE __MSABI_LONG(0x00048104)
#define IPP_S_TRUNCATED __MSABI_LONG(0x00048105)
#define IPP_E_LAST __MSABI_LONG(0x40048106)
