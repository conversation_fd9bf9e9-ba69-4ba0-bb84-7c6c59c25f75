/*** Autogenerated by WIDL 1.6 from include/mfidl.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mfidl_h__
#define __mfidl_h__

/* Forward declarations */

#ifndef __IMFTopologyNode_FWD_DEFINED__
#define __IMFTopologyNode_FWD_DEFINED__
typedef interface IMFTopologyNode IMFTopologyNode;
#endif

#ifndef __IMFTopology_FWD_DEFINED__
#define __IMFTopology_FWD_DEFINED__
typedef interface IMFTopology IMFTopology;
#endif

#ifndef __IMFClock_FWD_DEFINED__
#define __IMFClock_FWD_DEFINED__
typedef interface IMFClock IMFClock;
#endif

#ifndef __IMFMediaSession_FWD_DEFINED__
#define __IMFMediaSession_FWD_DEFINED__
typedef interface IMFMediaSession IMFMediaSession;
#endif

#ifndef __IMFMediaTypeHandler_FWD_DEFINED__
#define __IMFMediaTypeHandler_FWD_DEFINED__
typedef interface IMFMediaTypeHandler IMFMediaTypeHandler;
#endif

#ifndef __IMFStreamDescriptor_FWD_DEFINED__
#define __IMFStreamDescriptor_FWD_DEFINED__
typedef interface IMFStreamDescriptor IMFStreamDescriptor;
#endif

#ifndef __IMFPresentationDescriptor_FWD_DEFINED__
#define __IMFPresentationDescriptor_FWD_DEFINED__
typedef interface IMFPresentationDescriptor IMFPresentationDescriptor;
#endif

#ifndef __IMFMediaSource_FWD_DEFINED__
#define __IMFMediaSource_FWD_DEFINED__
typedef interface IMFMediaSource IMFMediaSource;
#endif

#ifndef __IMFByteStreamBuffering_FWD_DEFINED__
#define __IMFByteStreamBuffering_FWD_DEFINED__
typedef interface IMFByteStreamBuffering IMFByteStreamBuffering;
#endif

/* Headers for imported files */

#include <mfobjects.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <mftransform.h>
#include <windef.h>
typedef enum MFSESSION_SETTOPOLOGY_FLAGS {
    MFSESSION_SETTOPOLOGY_IMMEDIATE = 0x1,
    MFSESSION_SETTOPOLOGY_NORESOLUTION = 0x2,
    MFSESSION_SETTOPOLOGY_CLEAR_CURRENT = 0x4
} MFSESSION_SETTOPOLOGY_FLAGS;
typedef enum MFSESSION_GETFULLTOPOLOGY_FLAGS {
    MFSESSION_GETFULLTOPOLOGY_CURRENT = 0
} MFSESSION_GETFULLTOPOLOGY_FLAGS;
typedef enum MFPMPSESSION_CREATION_FLAGS {
    MFPMPSESSION_UNPROTECTED_PROCESS = 0x1
} MFPMPSESSION_CREATION_FLAGS;
typedef UINT64 TOPOID;
typedef enum MF_TOPOLOGY_TYPE {
    MF_TOPOLOGY_OUTPUT_NODE = 0,
    MF_TOPOLOGY_SOURCESTREAM_NODE = 1,
    MF_TOPOLOGY_TRANSFORM_NODE = 2,
    MF_TOPOLOGY_TEE_NODE = 3,
    MF_TOPOLOGY_MAX = 0xffffffff
} MF_TOPOLOGY_TYPE;
/*****************************************************************************
 * IMFTopologyNode interface
 */
#ifndef __IMFTopologyNode_INTERFACE_DEFINED__
#define __IMFTopologyNode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTopologyNode, 0x83cf873a, 0xf6da, 0x4bc8, 0x82,0x3f, 0xba,0xcf,0xd5,0x5d,0xc4,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("83cf873a-f6da-4bc8-823f-bacfd55dc430")
IMFTopologyNode : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE SetObject(
        IUnknown *pObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObject(
        IUnknown **ppObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNodeType(
        MF_TOPOLOGY_TYPE *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTopoNodeID(
        TOPOID *pID) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTopoNodeID(
        TOPOID ullTopoID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputCount(
        DWORD *pcInputs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputCount(
        DWORD *pcOutputs) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConnectOutput(
        DWORD dwOutputIndex,
        IMFTopologyNode *pDownstreamNode,
        DWORD dwInputIndexOnDownstreamNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisconnectOutput(
        DWORD dwOutputIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInput(
        DWORD dwInputIndex,
        IMFTopologyNode **ppUpstreamNode,
        DWORD *pdwOutputIndexOnUpstreamNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutput(
        DWORD dwOutputIndex,
        IMFTopologyNode **ppDownstreamNode,
        DWORD *pdwInputIndexOnDownstreamNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputPrefType(
        DWORD dwOutputIndex,
        IMFMediaType *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputPrefType(
        DWORD dwOutputIndex,
        IMFMediaType **ppType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInputPrefType(
        DWORD dwInputIndex,
        IMFMediaType *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputPrefType(
        DWORD dwInputIndex,
        IMFMediaType **ppType) = 0;

    virtual HRESULT STDMETHODCALLTYPE CloneFrom(
        IMFTopologyNode *pNode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTopologyNode, 0x83cf873a, 0xf6da, 0x4bc8, 0x82,0x3f, 0xba,0xcf,0xd5,0x5d,0xc4,0x30)
#endif
#else
typedef struct IMFTopologyNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTopologyNode* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTopologyNode* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTopologyNode* This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFTopologyNode* This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFTopologyNode* This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFTopologyNode* This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFTopologyNode* This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFTopologyNode* This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFTopologyNode* This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFTopologyNode* This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFTopologyNode* This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFTopologyNode* This,
        IMFAttributes *pDest);

    /*** IMFTopologyNode methods ***/
    HRESULT (STDMETHODCALLTYPE *SetObject)(
        IMFTopologyNode* This,
        IUnknown *pObject);

    HRESULT (STDMETHODCALLTYPE *GetObject)(
        IMFTopologyNode* This,
        IUnknown **ppObject);

    HRESULT (STDMETHODCALLTYPE *GetNodeType)(
        IMFTopologyNode* This,
        MF_TOPOLOGY_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *GetTopoNodeID)(
        IMFTopologyNode* This,
        TOPOID *pID);

    HRESULT (STDMETHODCALLTYPE *SetTopoNodeID)(
        IMFTopologyNode* This,
        TOPOID ullTopoID);

    HRESULT (STDMETHODCALLTYPE *GetInputCount)(
        IMFTopologyNode* This,
        DWORD *pcInputs);

    HRESULT (STDMETHODCALLTYPE *GetOutputCount)(
        IMFTopologyNode* This,
        DWORD *pcOutputs);

    HRESULT (STDMETHODCALLTYPE *ConnectOutput)(
        IMFTopologyNode* This,
        DWORD dwOutputIndex,
        IMFTopologyNode *pDownstreamNode,
        DWORD dwInputIndexOnDownstreamNode);

    HRESULT (STDMETHODCALLTYPE *DisconnectOutput)(
        IMFTopologyNode* This,
        DWORD dwOutputIndex);

    HRESULT (STDMETHODCALLTYPE *GetInput)(
        IMFTopologyNode* This,
        DWORD dwInputIndex,
        IMFTopologyNode **ppUpstreamNode,
        DWORD *pdwOutputIndexOnUpstreamNode);

    HRESULT (STDMETHODCALLTYPE *GetOutput)(
        IMFTopologyNode* This,
        DWORD dwOutputIndex,
        IMFTopologyNode **ppDownstreamNode,
        DWORD *pdwInputIndexOnDownstreamNode);

    HRESULT (STDMETHODCALLTYPE *SetOutputPrefType)(
        IMFTopologyNode* This,
        DWORD dwOutputIndex,
        IMFMediaType *pType);

    HRESULT (STDMETHODCALLTYPE *GetOutputPrefType)(
        IMFTopologyNode* This,
        DWORD dwOutputIndex,
        IMFMediaType **ppType);

    HRESULT (STDMETHODCALLTYPE *SetInputPrefType)(
        IMFTopologyNode* This,
        DWORD dwInputIndex,
        IMFMediaType *pType);

    HRESULT (STDMETHODCALLTYPE *GetInputPrefType)(
        IMFTopologyNode* This,
        DWORD dwInputIndex,
        IMFMediaType **ppType);

    HRESULT (STDMETHODCALLTYPE *CloneFrom)(
        IMFTopologyNode* This,
        IMFTopologyNode *pNode);

    END_INTERFACE
} IMFTopologyNodeVtbl;
interface IMFTopologyNode {
    CONST_VTBL IMFTopologyNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTopologyNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTopologyNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTopologyNode_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFTopologyNode_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFTopologyNode_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFTopologyNode_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFTopologyNode_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFTopologyNode_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFTopologyNode_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFTopologyNode_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFTopologyNode_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFTopologyNode_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFTopologyNode_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFTopologyNode_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFTopologyNode_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFTopologyNode_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFTopologyNode_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFTopologyNode_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFTopologyNode_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFTopologyNode_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFTopologyNode_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFTopologyNode_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFTopologyNode_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFTopologyNode_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFTopologyNode_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFTopologyNode_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFTopologyNode_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFTopologyNode_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFTopologyNode_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFTopologyNode_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFTopologyNode_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFTopologyNode_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFTopologyNode_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFTopologyNode methods ***/
#define IMFTopologyNode_SetObject(This,pObject) (This)->lpVtbl->SetObject(This,pObject)
#define IMFTopologyNode_GetObject(This,ppObject) (This)->lpVtbl->GetObject(This,ppObject)
#define IMFTopologyNode_GetNodeType(This,pType) (This)->lpVtbl->GetNodeType(This,pType)
#define IMFTopologyNode_GetTopoNodeID(This,pID) (This)->lpVtbl->GetTopoNodeID(This,pID)
#define IMFTopologyNode_SetTopoNodeID(This,ullTopoID) (This)->lpVtbl->SetTopoNodeID(This,ullTopoID)
#define IMFTopologyNode_GetInputCount(This,pcInputs) (This)->lpVtbl->GetInputCount(This,pcInputs)
#define IMFTopologyNode_GetOutputCount(This,pcOutputs) (This)->lpVtbl->GetOutputCount(This,pcOutputs)
#define IMFTopologyNode_ConnectOutput(This,dwOutputIndex,pDownstreamNode,dwInputIndexOnDownstreamNode) (This)->lpVtbl->ConnectOutput(This,dwOutputIndex,pDownstreamNode,dwInputIndexOnDownstreamNode)
#define IMFTopologyNode_DisconnectOutput(This,dwOutputIndex) (This)->lpVtbl->DisconnectOutput(This,dwOutputIndex)
#define IMFTopologyNode_GetInput(This,dwInputIndex,ppUpstreamNode,pdwOutputIndexOnUpstreamNode) (This)->lpVtbl->GetInput(This,dwInputIndex,ppUpstreamNode,pdwOutputIndexOnUpstreamNode)
#define IMFTopologyNode_GetOutput(This,dwOutputIndex,ppDownstreamNode,pdwInputIndexOnDownstreamNode) (This)->lpVtbl->GetOutput(This,dwOutputIndex,ppDownstreamNode,pdwInputIndexOnDownstreamNode)
#define IMFTopologyNode_SetOutputPrefType(This,dwOutputIndex,pType) (This)->lpVtbl->SetOutputPrefType(This,dwOutputIndex,pType)
#define IMFTopologyNode_GetOutputPrefType(This,dwOutputIndex,ppType) (This)->lpVtbl->GetOutputPrefType(This,dwOutputIndex,ppType)
#define IMFTopologyNode_SetInputPrefType(This,dwInputIndex,pType) (This)->lpVtbl->SetInputPrefType(This,dwInputIndex,pType)
#define IMFTopologyNode_GetInputPrefType(This,dwInputIndex,ppType) (This)->lpVtbl->GetInputPrefType(This,dwInputIndex,ppType)
#define IMFTopologyNode_CloneFrom(This,pNode) (This)->lpVtbl->CloneFrom(This,pNode)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFTopologyNode_QueryInterface(IMFTopologyNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFTopologyNode_AddRef(IMFTopologyNode* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFTopologyNode_Release(IMFTopologyNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static FORCEINLINE HRESULT IMFTopologyNode_GetItem(IMFTopologyNode* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetItemType(IMFTopologyNode* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static FORCEINLINE HRESULT IMFTopologyNode_CompareItem(IMFTopologyNode* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static FORCEINLINE HRESULT IMFTopologyNode_Compare(IMFTopologyNode* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetUINT32(IMFTopologyNode* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetUINT64(IMFTopologyNode* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetDouble(IMFTopologyNode* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetGUID(IMFTopologyNode* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetStringLength(IMFTopologyNode* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetString(IMFTopologyNode* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetAllocatedString(IMFTopologyNode* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetBlobSize(IMFTopologyNode* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetBlob(IMFTopologyNode* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetAllocatedBlob(IMFTopologyNode* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetUnknown(IMFTopologyNode* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetItem(IMFTopologyNode* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static FORCEINLINE HRESULT IMFTopologyNode_DeleteItem(IMFTopologyNode* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static FORCEINLINE HRESULT IMFTopologyNode_DeleteAllItems(IMFTopologyNode* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetUINT32(IMFTopologyNode* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetUINT64(IMFTopologyNode* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetDouble(IMFTopologyNode* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetGUID(IMFTopologyNode* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetString(IMFTopologyNode* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetBlob(IMFTopologyNode* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetUnknown(IMFTopologyNode* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static FORCEINLINE HRESULT IMFTopologyNode_LockStore(IMFTopologyNode* This) {
    return This->lpVtbl->LockStore(This);
}
static FORCEINLINE HRESULT IMFTopologyNode_UnlockStore(IMFTopologyNode* This) {
    return This->lpVtbl->UnlockStore(This);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetCount(IMFTopologyNode* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetItemByIndex(IMFTopologyNode* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static FORCEINLINE HRESULT IMFTopologyNode_CopyAllItems(IMFTopologyNode* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFTopologyNode methods ***/
static FORCEINLINE HRESULT IMFTopologyNode_SetObject(IMFTopologyNode* This,IUnknown *pObject) {
    return This->lpVtbl->SetObject(This,pObject);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetObject(IMFTopologyNode* This,IUnknown **ppObject) {
    return This->lpVtbl->GetObject(This,ppObject);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetNodeType(IMFTopologyNode* This,MF_TOPOLOGY_TYPE *pType) {
    return This->lpVtbl->GetNodeType(This,pType);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetTopoNodeID(IMFTopologyNode* This,TOPOID *pID) {
    return This->lpVtbl->GetTopoNodeID(This,pID);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetTopoNodeID(IMFTopologyNode* This,TOPOID ullTopoID) {
    return This->lpVtbl->SetTopoNodeID(This,ullTopoID);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetInputCount(IMFTopologyNode* This,DWORD *pcInputs) {
    return This->lpVtbl->GetInputCount(This,pcInputs);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetOutputCount(IMFTopologyNode* This,DWORD *pcOutputs) {
    return This->lpVtbl->GetOutputCount(This,pcOutputs);
}
static FORCEINLINE HRESULT IMFTopologyNode_ConnectOutput(IMFTopologyNode* This,DWORD dwOutputIndex,IMFTopologyNode *pDownstreamNode,DWORD dwInputIndexOnDownstreamNode) {
    return This->lpVtbl->ConnectOutput(This,dwOutputIndex,pDownstreamNode,dwInputIndexOnDownstreamNode);
}
static FORCEINLINE HRESULT IMFTopologyNode_DisconnectOutput(IMFTopologyNode* This,DWORD dwOutputIndex) {
    return This->lpVtbl->DisconnectOutput(This,dwOutputIndex);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetInput(IMFTopologyNode* This,DWORD dwInputIndex,IMFTopologyNode **ppUpstreamNode,DWORD *pdwOutputIndexOnUpstreamNode) {
    return This->lpVtbl->GetInput(This,dwInputIndex,ppUpstreamNode,pdwOutputIndexOnUpstreamNode);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetOutput(IMFTopologyNode* This,DWORD dwOutputIndex,IMFTopologyNode **ppDownstreamNode,DWORD *pdwInputIndexOnDownstreamNode) {
    return This->lpVtbl->GetOutput(This,dwOutputIndex,ppDownstreamNode,pdwInputIndexOnDownstreamNode);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetOutputPrefType(IMFTopologyNode* This,DWORD dwOutputIndex,IMFMediaType *pType) {
    return This->lpVtbl->SetOutputPrefType(This,dwOutputIndex,pType);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetOutputPrefType(IMFTopologyNode* This,DWORD dwOutputIndex,IMFMediaType **ppType) {
    return This->lpVtbl->GetOutputPrefType(This,dwOutputIndex,ppType);
}
static FORCEINLINE HRESULT IMFTopologyNode_SetInputPrefType(IMFTopologyNode* This,DWORD dwInputIndex,IMFMediaType *pType) {
    return This->lpVtbl->SetInputPrefType(This,dwInputIndex,pType);
}
static FORCEINLINE HRESULT IMFTopologyNode_GetInputPrefType(IMFTopologyNode* This,DWORD dwInputIndex,IMFMediaType **ppType) {
    return This->lpVtbl->GetInputPrefType(This,dwInputIndex,ppType);
}
static FORCEINLINE HRESULT IMFTopologyNode_CloneFrom(IMFTopologyNode* This,IMFTopologyNode *pNode) {
    return This->lpVtbl->CloneFrom(This,pNode);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFTopologyNode_SetObject_Proxy(
    IMFTopologyNode* This,
    IUnknown *pObject);
void __RPC_STUB IMFTopologyNode_SetObject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_GetObject_Proxy(
    IMFTopologyNode* This,
    IUnknown **ppObject);
void __RPC_STUB IMFTopologyNode_GetObject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_GetNodeType_Proxy(
    IMFTopologyNode* This,
    MF_TOPOLOGY_TYPE *pType);
void __RPC_STUB IMFTopologyNode_GetNodeType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_GetTopoNodeID_Proxy(
    IMFTopologyNode* This,
    TOPOID *pID);
void __RPC_STUB IMFTopologyNode_GetTopoNodeID_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_SetTopoNodeID_Proxy(
    IMFTopologyNode* This,
    TOPOID ullTopoID);
void __RPC_STUB IMFTopologyNode_SetTopoNodeID_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_GetInputCount_Proxy(
    IMFTopologyNode* This,
    DWORD *pcInputs);
void __RPC_STUB IMFTopologyNode_GetInputCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_GetOutputCount_Proxy(
    IMFTopologyNode* This,
    DWORD *pcOutputs);
void __RPC_STUB IMFTopologyNode_GetOutputCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_GetInput_Proxy(
    IMFTopologyNode* This,
    DWORD dwInputIndex,
    IMFTopologyNode **ppUpstreamNode,
    DWORD *pdwOutputIndexOnUpstreamNode);
void __RPC_STUB IMFTopologyNode_GetInput_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_GetOutput_Proxy(
    IMFTopologyNode* This,
    DWORD dwOutputIndex,
    IMFTopologyNode **ppDownstreamNode,
    DWORD *pdwInputIndexOnDownstreamNode);
void __RPC_STUB IMFTopologyNode_GetOutput_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_RemoteGetOutputPrefType_Proxy(
    IMFTopologyNode* This,
    DWORD dwOutputIndex,
    DWORD *pcbData,
    BYTE **ppbData);
void __RPC_STUB IMFTopologyNode_RemoteGetOutputPrefType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_RemoteGetInputPrefType_Proxy(
    IMFTopologyNode* This,
    DWORD dwInputIndex,
    DWORD *pcbData,
    BYTE **ppbData);
void __RPC_STUB IMFTopologyNode_RemoteGetInputPrefType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_CloneFrom_Proxy(
    IMFTopologyNode* This,
    IMFTopologyNode *pNode);
void __RPC_STUB IMFTopologyNode_CloneFrom_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFTopologyNode_GetOutputPrefType_Proxy(
    IMFTopologyNode* This,
    DWORD dwOutputIndex,
    IMFMediaType **ppType);
HRESULT __RPC_STUB IMFTopologyNode_GetOutputPrefType_Stub(
    IMFTopologyNode* This,
    DWORD dwOutputIndex,
    DWORD *pcbData,
    BYTE **ppbData);
HRESULT CALLBACK IMFTopologyNode_GetInputPrefType_Proxy(
    IMFTopologyNode* This,
    DWORD dwInputIndex,
    IMFMediaType **ppType);
HRESULT __RPC_STUB IMFTopologyNode_GetInputPrefType_Stub(
    IMFTopologyNode* This,
    DWORD dwInputIndex,
    DWORD *pcbData,
    BYTE **ppbData);

#endif  /* __IMFTopologyNode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFTopology interface
 */
#ifndef __IMFTopology_INTERFACE_DEFINED__
#define __IMFTopology_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTopology, 0x83cf873a, 0xf6da, 0x4bc8, 0x82,0x3f, 0xba,0xcf,0xd5,0x5d,0xc4,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("83cf873a-f6da-4bc8-823f-bacfd55dc433")
IMFTopology : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetTopologyID(
        TOPOID *pID) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddNode(
        IMFTopologyNode *pNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveNode(
        IMFTopologyNode *pNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNodeCount(
        WORD *pwNodes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNode(
        WORD wIndex,
        IMFTopologyNode **ppNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CloneFrom(
        IMFTopology *pTopology) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNodeByID(
        TOPOID qwTopoNodeID,
        IMFTopologyNode **ppNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceNodeCollection(
        IMFCollection **ppCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputNodeCollection(
        IMFCollection **ppCollection) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTopology, 0x83cf873a, 0xf6da, 0x4bc8, 0x82,0x3f, 0xba,0xcf,0xd5,0x5d,0xc4,0x33)
#endif
#else
typedef struct IMFTopologyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTopology* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTopology* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTopology* This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFTopology* This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFTopology* This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFTopology* This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFTopology* This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFTopology* This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFTopology* This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFTopology* This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFTopology* This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFTopology* This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFTopology* This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFTopology* This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFTopology* This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFTopology* This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFTopology* This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFTopology* This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFTopology* This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFTopology* This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFTopology* This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFTopology* This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFTopology* This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFTopology* This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFTopology* This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFTopology* This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFTopology* This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFTopology* This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFTopology* This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFTopology* This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFTopology* This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFTopology* This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFTopology* This,
        IMFAttributes *pDest);

    /*** IMFTopology methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTopologyID)(
        IMFTopology* This,
        TOPOID *pID);

    HRESULT (STDMETHODCALLTYPE *AddNode)(
        IMFTopology* This,
        IMFTopologyNode *pNode);

    HRESULT (STDMETHODCALLTYPE *RemoveNode)(
        IMFTopology* This,
        IMFTopologyNode *pNode);

    HRESULT (STDMETHODCALLTYPE *GetNodeCount)(
        IMFTopology* This,
        WORD *pwNodes);

    HRESULT (STDMETHODCALLTYPE *GetNode)(
        IMFTopology* This,
        WORD wIndex,
        IMFTopologyNode **ppNode);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IMFTopology* This);

    HRESULT (STDMETHODCALLTYPE *CloneFrom)(
        IMFTopology* This,
        IMFTopology *pTopology);

    HRESULT (STDMETHODCALLTYPE *GetNodeByID)(
        IMFTopology* This,
        TOPOID qwTopoNodeID,
        IMFTopologyNode **ppNode);

    HRESULT (STDMETHODCALLTYPE *GetSourceNodeCollection)(
        IMFTopology* This,
        IMFCollection **ppCollection);

    HRESULT (STDMETHODCALLTYPE *GetOutputNodeCollection)(
        IMFTopology* This,
        IMFCollection **ppCollection);

    END_INTERFACE
} IMFTopologyVtbl;
interface IMFTopology {
    CONST_VTBL IMFTopologyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTopology_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTopology_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTopology_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFTopology_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFTopology_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFTopology_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFTopology_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFTopology_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFTopology_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFTopology_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFTopology_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFTopology_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFTopology_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFTopology_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFTopology_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFTopology_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFTopology_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFTopology_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFTopology_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFTopology_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFTopology_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFTopology_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFTopology_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFTopology_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFTopology_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFTopology_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFTopology_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFTopology_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFTopology_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFTopology_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFTopology_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFTopology_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFTopology_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFTopology methods ***/
#define IMFTopology_GetTopologyID(This,pID) (This)->lpVtbl->GetTopologyID(This,pID)
#define IMFTopology_AddNode(This,pNode) (This)->lpVtbl->AddNode(This,pNode)
#define IMFTopology_RemoveNode(This,pNode) (This)->lpVtbl->RemoveNode(This,pNode)
#define IMFTopology_GetNodeCount(This,pwNodes) (This)->lpVtbl->GetNodeCount(This,pwNodes)
#define IMFTopology_GetNode(This,wIndex,ppNode) (This)->lpVtbl->GetNode(This,wIndex,ppNode)
#define IMFTopology_Clear(This) (This)->lpVtbl->Clear(This)
#define IMFTopology_CloneFrom(This,pTopology) (This)->lpVtbl->CloneFrom(This,pTopology)
#define IMFTopology_GetNodeByID(This,qwTopoNodeID,ppNode) (This)->lpVtbl->GetNodeByID(This,qwTopoNodeID,ppNode)
#define IMFTopology_GetSourceNodeCollection(This,ppCollection) (This)->lpVtbl->GetSourceNodeCollection(This,ppCollection)
#define IMFTopology_GetOutputNodeCollection(This,ppCollection) (This)->lpVtbl->GetOutputNodeCollection(This,ppCollection)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFTopology_QueryInterface(IMFTopology* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFTopology_AddRef(IMFTopology* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFTopology_Release(IMFTopology* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static FORCEINLINE HRESULT IMFTopology_GetItem(IMFTopology* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static FORCEINLINE HRESULT IMFTopology_GetItemType(IMFTopology* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static FORCEINLINE HRESULT IMFTopology_CompareItem(IMFTopology* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static FORCEINLINE HRESULT IMFTopology_Compare(IMFTopology* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static FORCEINLINE HRESULT IMFTopology_GetUINT32(IMFTopology* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static FORCEINLINE HRESULT IMFTopology_GetUINT64(IMFTopology* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static FORCEINLINE HRESULT IMFTopology_GetDouble(IMFTopology* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static FORCEINLINE HRESULT IMFTopology_GetGUID(IMFTopology* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static FORCEINLINE HRESULT IMFTopology_GetStringLength(IMFTopology* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static FORCEINLINE HRESULT IMFTopology_GetString(IMFTopology* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static FORCEINLINE HRESULT IMFTopology_GetAllocatedString(IMFTopology* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static FORCEINLINE HRESULT IMFTopology_GetBlobSize(IMFTopology* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static FORCEINLINE HRESULT IMFTopology_GetBlob(IMFTopology* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static FORCEINLINE HRESULT IMFTopology_GetAllocatedBlob(IMFTopology* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static FORCEINLINE HRESULT IMFTopology_GetUnknown(IMFTopology* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static FORCEINLINE HRESULT IMFTopology_SetItem(IMFTopology* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static FORCEINLINE HRESULT IMFTopology_DeleteItem(IMFTopology* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static FORCEINLINE HRESULT IMFTopology_DeleteAllItems(IMFTopology* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static FORCEINLINE HRESULT IMFTopology_SetUINT32(IMFTopology* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static FORCEINLINE HRESULT IMFTopology_SetUINT64(IMFTopology* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static FORCEINLINE HRESULT IMFTopology_SetDouble(IMFTopology* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static FORCEINLINE HRESULT IMFTopology_SetGUID(IMFTopology* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static FORCEINLINE HRESULT IMFTopology_SetString(IMFTopology* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static FORCEINLINE HRESULT IMFTopology_SetBlob(IMFTopology* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static FORCEINLINE HRESULT IMFTopology_SetUnknown(IMFTopology* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static FORCEINLINE HRESULT IMFTopology_LockStore(IMFTopology* This) {
    return This->lpVtbl->LockStore(This);
}
static FORCEINLINE HRESULT IMFTopology_UnlockStore(IMFTopology* This) {
    return This->lpVtbl->UnlockStore(This);
}
static FORCEINLINE HRESULT IMFTopology_GetCount(IMFTopology* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static FORCEINLINE HRESULT IMFTopology_GetItemByIndex(IMFTopology* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static FORCEINLINE HRESULT IMFTopology_CopyAllItems(IMFTopology* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFTopology methods ***/
static FORCEINLINE HRESULT IMFTopology_GetTopologyID(IMFTopology* This,TOPOID *pID) {
    return This->lpVtbl->GetTopologyID(This,pID);
}
static FORCEINLINE HRESULT IMFTopology_AddNode(IMFTopology* This,IMFTopologyNode *pNode) {
    return This->lpVtbl->AddNode(This,pNode);
}
static FORCEINLINE HRESULT IMFTopology_RemoveNode(IMFTopology* This,IMFTopologyNode *pNode) {
    return This->lpVtbl->RemoveNode(This,pNode);
}
static FORCEINLINE HRESULT IMFTopology_GetNodeCount(IMFTopology* This,WORD *pwNodes) {
    return This->lpVtbl->GetNodeCount(This,pwNodes);
}
static FORCEINLINE HRESULT IMFTopology_GetNode(IMFTopology* This,WORD wIndex,IMFTopologyNode **ppNode) {
    return This->lpVtbl->GetNode(This,wIndex,ppNode);
}
static FORCEINLINE HRESULT IMFTopology_Clear(IMFTopology* This) {
    return This->lpVtbl->Clear(This);
}
static FORCEINLINE HRESULT IMFTopology_CloneFrom(IMFTopology* This,IMFTopology *pTopology) {
    return This->lpVtbl->CloneFrom(This,pTopology);
}
static FORCEINLINE HRESULT IMFTopology_GetNodeByID(IMFTopology* This,TOPOID qwTopoNodeID,IMFTopologyNode **ppNode) {
    return This->lpVtbl->GetNodeByID(This,qwTopoNodeID,ppNode);
}
static FORCEINLINE HRESULT IMFTopology_GetSourceNodeCollection(IMFTopology* This,IMFCollection **ppCollection) {
    return This->lpVtbl->GetSourceNodeCollection(This,ppCollection);
}
static FORCEINLINE HRESULT IMFTopology_GetOutputNodeCollection(IMFTopology* This,IMFCollection **ppCollection) {
    return This->lpVtbl->GetOutputNodeCollection(This,ppCollection);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFTopology_GetTopologyID_Proxy(
    IMFTopology* This,
    TOPOID *pID);
void __RPC_STUB IMFTopology_GetTopologyID_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopology_GetNodeCount_Proxy(
    IMFTopology* This,
    WORD *pwNodes);
void __RPC_STUB IMFTopology_GetNodeCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopology_GetNode_Proxy(
    IMFTopology* This,
    WORD wIndex,
    IMFTopologyNode **ppNode);
void __RPC_STUB IMFTopology_GetNode_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopology_CloneFrom_Proxy(
    IMFTopology* This,
    IMFTopology *pTopology);
void __RPC_STUB IMFTopology_CloneFrom_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopology_GetNodeByID_Proxy(
    IMFTopology* This,
    TOPOID qwTopoNodeID,
    IMFTopologyNode **ppNode);
void __RPC_STUB IMFTopology_GetNodeByID_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopology_GetSourceNodeCollection_Proxy(
    IMFTopology* This,
    IMFCollection **ppCollection);
void __RPC_STUB IMFTopology_GetSourceNodeCollection_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopology_GetOutputNodeCollection_Proxy(
    IMFTopology* This,
    IMFCollection **ppCollection);
void __RPC_STUB IMFTopology_GetOutputNodeCollection_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMFTopology_INTERFACE_DEFINED__ */

typedef LONGLONG MFTIME;
typedef enum _MF_CLOCK_STATE {
    MFCLOCK_STATE_INVALID = 0,
    MFCLOCK_STATE_RUNNING = 1,
    MFCLOCK_STATE_STOPPED = 2,
    MFCLOCK_STATE_PAUSED = 3
} MF_CLOCK_STATE;
typedef enum _MF_CLOCK_STATE MFCLOCK_STATE;
typedef struct _MFCLOCK_PROPERTIES {
    UINT64 qwCorrelationRate;
    GUID guidClockId;
    DWORD dwClockFlags;
    UINT64 qwClockFrequency;
    DWORD dwClockTolerance;
    DWORD dwClockJitter;
} MFCLOCK_PROPERTIES;
/*****************************************************************************
 * IMFClock interface
 */
#ifndef __IMFClock_INTERFACE_DEFINED__
#define __IMFClock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFClock, 0x2eb1e945, 0x18b8, 0x4139, 0x9b,0x1a, 0xd5,0xd5,0x84,0x81,0x85,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2eb1e945-18b8-4139-9b1a-d5d584818530")
IMFClock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClockCharacteristics(
        DWORD *pdwCharacteristics) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCorrelatedTime(
        DWORD dwReserved,
        LONGLONG *pllClockTime,
        MFTIME *phnsSystemTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContinuityKey(
        DWORD *pdwContinuityKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        DWORD dwReserved,
        MFCLOCK_STATE *peClockState) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperties(
        MFCLOCK_PROPERTIES *pClockProperties) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFClock, 0x2eb1e945, 0x18b8, 0x4139, 0x9b,0x1a, 0xd5,0xd5,0x84,0x81,0x85,0x30)
#endif
#else
typedef struct IMFClockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFClock* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFClock* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFClock* This);

    /*** IMFClock methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClockCharacteristics)(
        IMFClock* This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *GetCorrelatedTime)(
        IMFClock* This,
        DWORD dwReserved,
        LONGLONG *pllClockTime,
        MFTIME *phnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *GetContinuityKey)(
        IMFClock* This,
        DWORD *pdwContinuityKey);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IMFClock* This,
        DWORD dwReserved,
        MFCLOCK_STATE *peClockState);

    HRESULT (STDMETHODCALLTYPE *GetProperties)(
        IMFClock* This,
        MFCLOCK_PROPERTIES *pClockProperties);

    END_INTERFACE
} IMFClockVtbl;
interface IMFClock {
    CONST_VTBL IMFClockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFClock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFClock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFClock_Release(This) (This)->lpVtbl->Release(This)
/*** IMFClock methods ***/
#define IMFClock_GetClockCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetClockCharacteristics(This,pdwCharacteristics)
#define IMFClock_GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime) (This)->lpVtbl->GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime)
#define IMFClock_GetContinuityKey(This,pdwContinuityKey) (This)->lpVtbl->GetContinuityKey(This,pdwContinuityKey)
#define IMFClock_GetState(This,dwReserved,peClockState) (This)->lpVtbl->GetState(This,dwReserved,peClockState)
#define IMFClock_GetProperties(This,pClockProperties) (This)->lpVtbl->GetProperties(This,pClockProperties)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFClock_QueryInterface(IMFClock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFClock_AddRef(IMFClock* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFClock_Release(IMFClock* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFClock methods ***/
static FORCEINLINE HRESULT IMFClock_GetClockCharacteristics(IMFClock* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetClockCharacteristics(This,pdwCharacteristics);
}
static FORCEINLINE HRESULT IMFClock_GetCorrelatedTime(IMFClock* This,DWORD dwReserved,LONGLONG *pllClockTime,MFTIME *phnsSystemTime) {
    return This->lpVtbl->GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime);
}
static FORCEINLINE HRESULT IMFClock_GetContinuityKey(IMFClock* This,DWORD *pdwContinuityKey) {
    return This->lpVtbl->GetContinuityKey(This,pdwContinuityKey);
}
static FORCEINLINE HRESULT IMFClock_GetState(IMFClock* This,DWORD dwReserved,MFCLOCK_STATE *peClockState) {
    return This->lpVtbl->GetState(This,dwReserved,peClockState);
}
static FORCEINLINE HRESULT IMFClock_GetProperties(IMFClock* This,MFCLOCK_PROPERTIES *pClockProperties) {
    return This->lpVtbl->GetProperties(This,pClockProperties);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFClock_GetClockCharacteristics_Proxy(
    IMFClock* This,
    DWORD *pdwCharacteristics);
void __RPC_STUB IMFClock_GetClockCharacteristics_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFClock_GetCorrelatedTime_Proxy(
    IMFClock* This,
    DWORD dwReserved,
    LONGLONG *pllClockTime,
    MFTIME *phnsSystemTime);
void __RPC_STUB IMFClock_GetCorrelatedTime_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFClock_GetContinuityKey_Proxy(
    IMFClock* This,
    DWORD *pdwContinuityKey);
void __RPC_STUB IMFClock_GetContinuityKey_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFClock_GetState_Proxy(
    IMFClock* This,
    DWORD dwReserved,
    MFCLOCK_STATE *peClockState);
void __RPC_STUB IMFClock_GetState_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFClock_GetProperties_Proxy(
    IMFClock* This,
    MFCLOCK_PROPERTIES *pClockProperties);
void __RPC_STUB IMFClock_GetProperties_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMFClock_INTERFACE_DEFINED__ */

#define SHA_HASH_LEN 20
#define STR_HASH_LEN (SHA_HASH_LEN*2+3)
typedef struct _MFRR_COMPONENT_HASH_INFO {
  DWORD ulReason;
  WCHAR rgHeaderHash[STR_HASH_LEN];
  WCHAR rgPublicKeyHash[STR_HASH_LEN];
  WCHAR wszName[MAX_PATH];
} MFRR_COMPONENT_HASH_INFO, *PMFRR_COMPONENT_HASH_INFO;
EXTERN_GUID(MF_PD_DURATION, 0x6c990d33,0xbb8e,0x477a,0x85,0x98,0xd,0x5d,0x96,0xfc,0xd8,0x8a);
typedef enum _MF_CONNECT_METHOD {
    MF_CONNECT_DIRECT = 0x0,
    MF_CONNECT_ALLOW_CONVERTER = 0x1,
    MF_CONNECT_ALLOW_DECODER = 0x3,
    MF_CONNECT_RESOLVE_INDEPENDENT_OUTPUTTYPES = 0x4,
    MF_CONNECT_AS_OPTIONAL = 0x10000,
    MF_CONNECT_AS_OPTIONAL_BRANCH = 0x20000
} MF_CONNECT_METHOD;
/*****************************************************************************
 * IMFMediaSession interface
 */
#ifndef __IMFMediaSession_INTERFACE_DEFINED__
#define __IMFMediaSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaSession, 0x90377834, 0x21d0, 0x4dee, 0x82,0x14, 0xba,0x2e,0x3e,0x6c,0x11,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("90377834-21d0-4dee-8214-ba2e3e6c1127")
IMFMediaSession : public IMFMediaEventGenerator
{
    virtual HRESULT STDMETHODCALLTYPE SetTopology(
        DWORD dwSetTopologyFlags,
        IMFTopology *pTopology) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearTopologies(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClock(
        IMFClock **ppClock) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSessionCapabilities(
        DWORD *pdwCaps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFullTopology(
        DWORD dwGetFullTopologyFlags,
        TOPOID TopoId,
        IMFTopology **ppFullTopology) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaSession, 0x90377834, 0x21d0, 0x4dee, 0x82,0x14, 0xba,0x2e,0x3e,0x6c,0x11,0x27)
#endif
#else
typedef struct IMFMediaSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaSession* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaSession* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaSession* This);

    /*** IMFMediaEventGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFMediaSession* This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFMediaSession* This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFMediaSession* This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFMediaSession* This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    /*** IMFMediaSession methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTopology)(
        IMFMediaSession* This,
        DWORD dwSetTopologyFlags,
        IMFTopology *pTopology);

    HRESULT (STDMETHODCALLTYPE *ClearTopologies)(
        IMFMediaSession* This);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IMFMediaSession* This,
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFMediaSession* This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IMFMediaSession* This);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IMFMediaSession* This);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaSession* This);

    HRESULT (STDMETHODCALLTYPE *GetClock)(
        IMFMediaSession* This,
        IMFClock **ppClock);

    HRESULT (STDMETHODCALLTYPE *GetSessionCapabilities)(
        IMFMediaSession* This,
        DWORD *pdwCaps);

    HRESULT (STDMETHODCALLTYPE *GetFullTopology)(
        IMFMediaSession* This,
        DWORD dwGetFullTopologyFlags,
        TOPOID TopoId,
        IMFTopology **ppFullTopology);

    END_INTERFACE
} IMFMediaSessionVtbl;
interface IMFMediaSession {
    CONST_VTBL IMFMediaSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaSession_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventGenerator methods ***/
#define IMFMediaSession_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFMediaSession_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFMediaSession_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFMediaSession_QueueEvent(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue)
/*** IMFMediaSession methods ***/
#define IMFMediaSession_SetTopology(This,dwSetTopologyFlags,pTopology) (This)->lpVtbl->SetTopology(This,dwSetTopologyFlags,pTopology)
#define IMFMediaSession_ClearTopologies(This) (This)->lpVtbl->ClearTopologies(This)
#define IMFMediaSession_Start(This,pguidTimeFormat,pvarStartPosition) (This)->lpVtbl->Start(This,pguidTimeFormat,pvarStartPosition)
#define IMFMediaSession_Pause(This) (This)->lpVtbl->Pause(This)
#define IMFMediaSession_Stop(This) (This)->lpVtbl->Stop(This)
#define IMFMediaSession_Close(This) (This)->lpVtbl->Close(This)
#define IMFMediaSession_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#define IMFMediaSession_GetClock(This,ppClock) (This)->lpVtbl->GetClock(This,ppClock)
#define IMFMediaSession_GetSessionCapabilities(This,pdwCaps) (This)->lpVtbl->GetSessionCapabilities(This,pdwCaps)
#define IMFMediaSession_GetFullTopology(This,dwGetFullTopologyFlags,TopoId,ppFullTopology) (This)->lpVtbl->GetFullTopology(This,dwGetFullTopologyFlags,TopoId,ppFullTopology)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFMediaSession_QueryInterface(IMFMediaSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFMediaSession_AddRef(IMFMediaSession* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFMediaSession_Release(IMFMediaSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventGenerator methods ***/
static FORCEINLINE HRESULT IMFMediaSession_GetEvent(IMFMediaSession* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static FORCEINLINE HRESULT IMFMediaSession_BeginGetEvent(IMFMediaSession* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static FORCEINLINE HRESULT IMFMediaSession_EndGetEvent(IMFMediaSession* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static FORCEINLINE HRESULT IMFMediaSession_QueueEvent(IMFMediaSession* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue);
}
/*** IMFMediaSession methods ***/
static FORCEINLINE HRESULT IMFMediaSession_SetTopology(IMFMediaSession* This,DWORD dwSetTopologyFlags,IMFTopology *pTopology) {
    return This->lpVtbl->SetTopology(This,dwSetTopologyFlags,pTopology);
}
static FORCEINLINE HRESULT IMFMediaSession_ClearTopologies(IMFMediaSession* This) {
    return This->lpVtbl->ClearTopologies(This);
}
static FORCEINLINE HRESULT IMFMediaSession_Start(IMFMediaSession* This,const GUID *pguidTimeFormat,const PROPVARIANT *pvarStartPosition) {
    return This->lpVtbl->Start(This,pguidTimeFormat,pvarStartPosition);
}
static FORCEINLINE HRESULT IMFMediaSession_Pause(IMFMediaSession* This) {
    return This->lpVtbl->Pause(This);
}
static FORCEINLINE HRESULT IMFMediaSession_Stop(IMFMediaSession* This) {
    return This->lpVtbl->Stop(This);
}
static FORCEINLINE HRESULT IMFMediaSession_Close(IMFMediaSession* This) {
    return This->lpVtbl->Close(This);
}
static FORCEINLINE HRESULT IMFMediaSession_Shutdown(IMFMediaSession* This) {
    return This->lpVtbl->Shutdown(This);
}
static FORCEINLINE HRESULT IMFMediaSession_GetClock(IMFMediaSession* This,IMFClock **ppClock) {
    return This->lpVtbl->GetClock(This,ppClock);
}
static FORCEINLINE HRESULT IMFMediaSession_GetSessionCapabilities(IMFMediaSession* This,DWORD *pdwCaps) {
    return This->lpVtbl->GetSessionCapabilities(This,pdwCaps);
}
static FORCEINLINE HRESULT IMFMediaSession_GetFullTopology(IMFMediaSession* This,DWORD dwGetFullTopologyFlags,TOPOID TopoId,IMFTopology **ppFullTopology) {
    return This->lpVtbl->GetFullTopology(This,dwGetFullTopologyFlags,TopoId,ppFullTopology);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFMediaSession_SetTopology_Proxy(
    IMFMediaSession* This,
    DWORD dwSetTopologyFlags,
    IMFTopology *pTopology);
void __RPC_STUB IMFMediaSession_SetTopology_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_ClearTopologies_Proxy(
    IMFMediaSession* This);
void __RPC_STUB IMFMediaSession_ClearTopologies_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_Start_Proxy(
    IMFMediaSession* This,
    const GUID *pguidTimeFormat,
    const PROPVARIANT *pvarStartPosition);
void __RPC_STUB IMFMediaSession_Start_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_Pause_Proxy(
    IMFMediaSession* This);
void __RPC_STUB IMFMediaSession_Pause_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_Stop_Proxy(
    IMFMediaSession* This);
void __RPC_STUB IMFMediaSession_Stop_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_Close_Proxy(
    IMFMediaSession* This);
void __RPC_STUB IMFMediaSession_Close_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_Shutdown_Proxy(
    IMFMediaSession* This);
void __RPC_STUB IMFMediaSession_Shutdown_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_GetClock_Proxy(
    IMFMediaSession* This,
    IMFClock **ppClock);
void __RPC_STUB IMFMediaSession_GetClock_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_GetSessionCapabilities_Proxy(
    IMFMediaSession* This,
    DWORD *pdwCaps);
void __RPC_STUB IMFMediaSession_GetSessionCapabilities_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSession_GetFullTopology_Proxy(
    IMFMediaSession* This,
    DWORD dwGetFullTopologyFlags,
    TOPOID TopoId,
    IMFTopology **ppFullTopology);
void __RPC_STUB IMFMediaSession_GetFullTopology_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMFMediaSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaTypeHandler interface
 */
#ifndef __IMFMediaTypeHandler_INTERFACE_DEFINED__
#define __IMFMediaTypeHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaTypeHandler, 0xe93dcf6c, 0x4b07, 0x4e1e, 0x81,0x23, 0xaa,0x16,0xed,0x6e,0xad,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e93dcf6c-4b07-4e1e-8123-aa16ed6eadf5")
IMFMediaTypeHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsMediaTypeSupported(
        IMFMediaType *pMediaType,
        IMFMediaType **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTypeCount(
        DWORD *pdwTypeCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTypeByIndex(
        DWORD dwIndex,
        IMFMediaType **ppType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentMediaType(
        IMFMediaType *pMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentMediaType(
        IMFMediaType **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMajorType(
        GUID *pguidMajorType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaTypeHandler, 0xe93dcf6c, 0x4b07, 0x4e1e, 0x81,0x23, 0xaa,0x16,0xed,0x6e,0xad,0xf5)
#endif
#else
typedef struct IMFMediaTypeHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaTypeHandler* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaTypeHandler* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaTypeHandler* This);

    /*** IMFMediaTypeHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *IsMediaTypeSupported)(
        IMFMediaTypeHandler* This,
        IMFMediaType *pMediaType,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetMediaTypeCount)(
        IMFMediaTypeHandler* This,
        DWORD *pdwTypeCount);

    HRESULT (STDMETHODCALLTYPE *GetMediaTypeByIndex)(
        IMFMediaTypeHandler* This,
        DWORD dwIndex,
        IMFMediaType **ppType);

    HRESULT (STDMETHODCALLTYPE *SetCurrentMediaType)(
        IMFMediaTypeHandler* This,
        IMFMediaType *pMediaType);

    HRESULT (STDMETHODCALLTYPE *GetCurrentMediaType)(
        IMFMediaTypeHandler* This,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetMajorType)(
        IMFMediaTypeHandler* This,
        GUID *pguidMajorType);

    END_INTERFACE
} IMFMediaTypeHandlerVtbl;
interface IMFMediaTypeHandler {
    CONST_VTBL IMFMediaTypeHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaTypeHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaTypeHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaTypeHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaTypeHandler methods ***/
#define IMFMediaTypeHandler_IsMediaTypeSupported(This,pMediaType,ppMediaType) (This)->lpVtbl->IsMediaTypeSupported(This,pMediaType,ppMediaType)
#define IMFMediaTypeHandler_GetMediaTypeCount(This,pdwTypeCount) (This)->lpVtbl->GetMediaTypeCount(This,pdwTypeCount)
#define IMFMediaTypeHandler_GetMediaTypeByIndex(This,dwIndex,ppType) (This)->lpVtbl->GetMediaTypeByIndex(This,dwIndex,ppType)
#define IMFMediaTypeHandler_SetCurrentMediaType(This,pMediaType) (This)->lpVtbl->SetCurrentMediaType(This,pMediaType)
#define IMFMediaTypeHandler_GetCurrentMediaType(This,ppMediaType) (This)->lpVtbl->GetCurrentMediaType(This,ppMediaType)
#define IMFMediaTypeHandler_GetMajorType(This,pguidMajorType) (This)->lpVtbl->GetMajorType(This,pguidMajorType)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFMediaTypeHandler_QueryInterface(IMFMediaTypeHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFMediaTypeHandler_AddRef(IMFMediaTypeHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFMediaTypeHandler_Release(IMFMediaTypeHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaTypeHandler methods ***/
static FORCEINLINE HRESULT IMFMediaTypeHandler_IsMediaTypeSupported(IMFMediaTypeHandler* This,IMFMediaType *pMediaType,IMFMediaType **ppMediaType) {
    return This->lpVtbl->IsMediaTypeSupported(This,pMediaType,ppMediaType);
}
static FORCEINLINE HRESULT IMFMediaTypeHandler_GetMediaTypeCount(IMFMediaTypeHandler* This,DWORD *pdwTypeCount) {
    return This->lpVtbl->GetMediaTypeCount(This,pdwTypeCount);
}
static FORCEINLINE HRESULT IMFMediaTypeHandler_GetMediaTypeByIndex(IMFMediaTypeHandler* This,DWORD dwIndex,IMFMediaType **ppType) {
    return This->lpVtbl->GetMediaTypeByIndex(This,dwIndex,ppType);
}
static FORCEINLINE HRESULT IMFMediaTypeHandler_SetCurrentMediaType(IMFMediaTypeHandler* This,IMFMediaType *pMediaType) {
    return This->lpVtbl->SetCurrentMediaType(This,pMediaType);
}
static FORCEINLINE HRESULT IMFMediaTypeHandler_GetCurrentMediaType(IMFMediaTypeHandler* This,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetCurrentMediaType(This,ppMediaType);
}
static FORCEINLINE HRESULT IMFMediaTypeHandler_GetMajorType(IMFMediaTypeHandler* This,GUID *pguidMajorType) {
    return This->lpVtbl->GetMajorType(This,pguidMajorType);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFMediaTypeHandler_RemoteGetCurrentMediaType_Proxy(
    IMFMediaTypeHandler* This,
    BYTE **ppbData,
    DWORD *pcbData);
void __RPC_STUB IMFMediaTypeHandler_RemoteGetCurrentMediaType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaTypeHandler_GetMajorType_Proxy(
    IMFMediaTypeHandler* This,
    GUID *pguidMajorType);
void __RPC_STUB IMFMediaTypeHandler_GetMajorType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFMediaTypeHandler_GetCurrentMediaType_Proxy(
    IMFMediaTypeHandler* This,
    IMFMediaType **ppMediaType);
HRESULT __RPC_STUB IMFMediaTypeHandler_GetCurrentMediaType_Stub(
    IMFMediaTypeHandler* This,
    BYTE **ppbData,
    DWORD *pcbData);

#endif  /* __IMFMediaTypeHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFStreamDescriptor interface
 */
#ifndef __IMFStreamDescriptor_INTERFACE_DEFINED__
#define __IMFStreamDescriptor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFStreamDescriptor, 0x56c03d9c, 0x9dbb, 0x45f5, 0xab,0x4b, 0xd8,0x0f,0x47,0xc0,0x59,0x38);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56c03d9c-9dbb-45f5-ab4b-d80f47c05938")
IMFStreamDescriptor : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamIdentifier(
        DWORD *pdwStreamIdentifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTypeHandler(
        IMFMediaTypeHandler **ppMediaTypeHandler) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFStreamDescriptor, 0x56c03d9c, 0x9dbb, 0x45f5, 0xab,0x4b, 0xd8,0x0f,0x47,0xc0,0x59,0x38)
#endif
#else
typedef struct IMFStreamDescriptorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFStreamDescriptor* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFStreamDescriptor* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFStreamDescriptor* This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFStreamDescriptor* This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFStreamDescriptor* This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFStreamDescriptor* This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFStreamDescriptor* This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFStreamDescriptor* This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFStreamDescriptor* This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFStreamDescriptor* This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFStreamDescriptor* This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFStreamDescriptor* This,
        IMFAttributes *pDest);

    /*** IMFStreamDescriptor methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamIdentifier)(
        IMFStreamDescriptor* This,
        DWORD *pdwStreamIdentifier);

    HRESULT (STDMETHODCALLTYPE *GetMediaTypeHandler)(
        IMFStreamDescriptor* This,
        IMFMediaTypeHandler **ppMediaTypeHandler);

    END_INTERFACE
} IMFStreamDescriptorVtbl;
interface IMFStreamDescriptor {
    CONST_VTBL IMFStreamDescriptorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFStreamDescriptor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFStreamDescriptor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFStreamDescriptor_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFStreamDescriptor_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFStreamDescriptor_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFStreamDescriptor_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFStreamDescriptor_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFStreamDescriptor_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFStreamDescriptor_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFStreamDescriptor_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFStreamDescriptor_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFStreamDescriptor_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFStreamDescriptor_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFStreamDescriptor_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFStreamDescriptor_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFStreamDescriptor_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFStreamDescriptor_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFStreamDescriptor_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFStreamDescriptor_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFStreamDescriptor_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFStreamDescriptor_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFStreamDescriptor_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFStreamDescriptor_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFStreamDescriptor_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFStreamDescriptor_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFStreamDescriptor_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFStreamDescriptor_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFStreamDescriptor_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFStreamDescriptor_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFStreamDescriptor_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFStreamDescriptor_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFStreamDescriptor_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFStreamDescriptor_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFStreamDescriptor methods ***/
#define IMFStreamDescriptor_GetStreamIdentifier(This,pdwStreamIdentifier) (This)->lpVtbl->GetStreamIdentifier(This,pdwStreamIdentifier)
#define IMFStreamDescriptor_GetMediaTypeHandler(This,ppMediaTypeHandler) (This)->lpVtbl->GetMediaTypeHandler(This,ppMediaTypeHandler)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFStreamDescriptor_QueryInterface(IMFStreamDescriptor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFStreamDescriptor_AddRef(IMFStreamDescriptor* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFStreamDescriptor_Release(IMFStreamDescriptor* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static FORCEINLINE HRESULT IMFStreamDescriptor_GetItem(IMFStreamDescriptor* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetItemType(IMFStreamDescriptor* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_CompareItem(IMFStreamDescriptor* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_Compare(IMFStreamDescriptor* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetUINT32(IMFStreamDescriptor* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetUINT64(IMFStreamDescriptor* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetDouble(IMFStreamDescriptor* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetGUID(IMFStreamDescriptor* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetStringLength(IMFStreamDescriptor* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetString(IMFStreamDescriptor* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetAllocatedString(IMFStreamDescriptor* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetBlobSize(IMFStreamDescriptor* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetBlob(IMFStreamDescriptor* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetAllocatedBlob(IMFStreamDescriptor* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetUnknown(IMFStreamDescriptor* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_SetItem(IMFStreamDescriptor* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_DeleteItem(IMFStreamDescriptor* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_DeleteAllItems(IMFStreamDescriptor* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_SetUINT32(IMFStreamDescriptor* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_SetUINT64(IMFStreamDescriptor* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_SetDouble(IMFStreamDescriptor* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_SetGUID(IMFStreamDescriptor* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_SetString(IMFStreamDescriptor* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_SetBlob(IMFStreamDescriptor* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_SetUnknown(IMFStreamDescriptor* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_LockStore(IMFStreamDescriptor* This) {
    return This->lpVtbl->LockStore(This);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_UnlockStore(IMFStreamDescriptor* This) {
    return This->lpVtbl->UnlockStore(This);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetCount(IMFStreamDescriptor* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetItemByIndex(IMFStreamDescriptor* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_CopyAllItems(IMFStreamDescriptor* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFStreamDescriptor methods ***/
static FORCEINLINE HRESULT IMFStreamDescriptor_GetStreamIdentifier(IMFStreamDescriptor* This,DWORD *pdwStreamIdentifier) {
    return This->lpVtbl->GetStreamIdentifier(This,pdwStreamIdentifier);
}
static FORCEINLINE HRESULT IMFStreamDescriptor_GetMediaTypeHandler(IMFStreamDescriptor* This,IMFMediaTypeHandler **ppMediaTypeHandler) {
    return This->lpVtbl->GetMediaTypeHandler(This,ppMediaTypeHandler);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFStreamDescriptor_GetStreamIdentifier_Proxy(
    IMFStreamDescriptor* This,
    DWORD *pdwStreamIdentifier);
void __RPC_STUB IMFStreamDescriptor_GetStreamIdentifier_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFStreamDescriptor_GetMediaTypeHandler_Proxy(
    IMFStreamDescriptor* This,
    IMFMediaTypeHandler **ppMediaTypeHandler);
void __RPC_STUB IMFStreamDescriptor_GetMediaTypeHandler_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMFStreamDescriptor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFPresentationDescriptor interface
 */
#ifndef __IMFPresentationDescriptor_INTERFACE_DEFINED__
#define __IMFPresentationDescriptor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPresentationDescriptor, 0x03cb2711, 0x24d7, 0x4db6, 0xa1,0x7f, 0xf3,0xa7,0xa4,0x79,0xa5,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("03cb2711-24d7-4db6-a17f-f3a7a479a536")
IMFPresentationDescriptor : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamDescriptorCount(
        DWORD *pdwDescriptorCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamDescriptorByIndex(
        DWORD dwIndex,
        WINBOOL *pfSelected,
        IMFStreamDescriptor **ppDescriptor) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectStream(
        DWORD dwDescriptorIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeselectStream(
        DWORD dwDescriptorIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IMFPresentationDescriptor **ppPresentationDescriptor) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPresentationDescriptor, 0x03cb2711, 0x24d7, 0x4db6, 0xa1,0x7f, 0xf3,0xa7,0xa4,0x79,0xa5,0x36)
#endif
#else
typedef struct IMFPresentationDescriptorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPresentationDescriptor* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPresentationDescriptor* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPresentationDescriptor* This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFPresentationDescriptor* This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFPresentationDescriptor* This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFPresentationDescriptor* This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFPresentationDescriptor* This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFPresentationDescriptor* This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFPresentationDescriptor* This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFPresentationDescriptor* This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFPresentationDescriptor* This,
        IMFAttributes *pDest);

    /*** IMFPresentationDescriptor methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamDescriptorCount)(
        IMFPresentationDescriptor* This,
        DWORD *pdwDescriptorCount);

    HRESULT (STDMETHODCALLTYPE *GetStreamDescriptorByIndex)(
        IMFPresentationDescriptor* This,
        DWORD dwIndex,
        WINBOOL *pfSelected,
        IMFStreamDescriptor **ppDescriptor);

    HRESULT (STDMETHODCALLTYPE *SelectStream)(
        IMFPresentationDescriptor* This,
        DWORD dwDescriptorIndex);

    HRESULT (STDMETHODCALLTYPE *DeselectStream)(
        IMFPresentationDescriptor* This,
        DWORD dwDescriptorIndex);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IMFPresentationDescriptor* This,
        IMFPresentationDescriptor **ppPresentationDescriptor);

    END_INTERFACE
} IMFPresentationDescriptorVtbl;
interface IMFPresentationDescriptor {
    CONST_VTBL IMFPresentationDescriptorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPresentationDescriptor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPresentationDescriptor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPresentationDescriptor_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFPresentationDescriptor_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFPresentationDescriptor_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFPresentationDescriptor_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFPresentationDescriptor_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFPresentationDescriptor_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFPresentationDescriptor_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFPresentationDescriptor_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFPresentationDescriptor_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFPresentationDescriptor_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFPresentationDescriptor_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFPresentationDescriptor_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFPresentationDescriptor_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFPresentationDescriptor_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFPresentationDescriptor_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFPresentationDescriptor_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFPresentationDescriptor_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFPresentationDescriptor_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFPresentationDescriptor_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFPresentationDescriptor_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFPresentationDescriptor_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFPresentationDescriptor_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFPresentationDescriptor_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFPresentationDescriptor_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFPresentationDescriptor_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFPresentationDescriptor_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFPresentationDescriptor_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFPresentationDescriptor_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFPresentationDescriptor_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFPresentationDescriptor_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFPresentationDescriptor_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFPresentationDescriptor methods ***/
#define IMFPresentationDescriptor_GetStreamDescriptorCount(This,pdwDescriptorCount) (This)->lpVtbl->GetStreamDescriptorCount(This,pdwDescriptorCount)
#define IMFPresentationDescriptor_GetStreamDescriptorByIndex(This,dwIndex,pfSelected,ppDescriptor) (This)->lpVtbl->GetStreamDescriptorByIndex(This,dwIndex,pfSelected,ppDescriptor)
#define IMFPresentationDescriptor_SelectStream(This,dwDescriptorIndex) (This)->lpVtbl->SelectStream(This,dwDescriptorIndex)
#define IMFPresentationDescriptor_DeselectStream(This,dwDescriptorIndex) (This)->lpVtbl->DeselectStream(This,dwDescriptorIndex)
#define IMFPresentationDescriptor_Clone(This,ppPresentationDescriptor) (This)->lpVtbl->Clone(This,ppPresentationDescriptor)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFPresentationDescriptor_QueryInterface(IMFPresentationDescriptor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFPresentationDescriptor_AddRef(IMFPresentationDescriptor* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFPresentationDescriptor_Release(IMFPresentationDescriptor* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetItem(IMFPresentationDescriptor* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetItemType(IMFPresentationDescriptor* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_CompareItem(IMFPresentationDescriptor* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_Compare(IMFPresentationDescriptor* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetUINT32(IMFPresentationDescriptor* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetUINT64(IMFPresentationDescriptor* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetDouble(IMFPresentationDescriptor* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetGUID(IMFPresentationDescriptor* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetStringLength(IMFPresentationDescriptor* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetString(IMFPresentationDescriptor* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetAllocatedString(IMFPresentationDescriptor* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetBlobSize(IMFPresentationDescriptor* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetBlob(IMFPresentationDescriptor* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetAllocatedBlob(IMFPresentationDescriptor* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetUnknown(IMFPresentationDescriptor* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SetItem(IMFPresentationDescriptor* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_DeleteItem(IMFPresentationDescriptor* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_DeleteAllItems(IMFPresentationDescriptor* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SetUINT32(IMFPresentationDescriptor* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SetUINT64(IMFPresentationDescriptor* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SetDouble(IMFPresentationDescriptor* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SetGUID(IMFPresentationDescriptor* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SetString(IMFPresentationDescriptor* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SetBlob(IMFPresentationDescriptor* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SetUnknown(IMFPresentationDescriptor* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_LockStore(IMFPresentationDescriptor* This) {
    return This->lpVtbl->LockStore(This);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_UnlockStore(IMFPresentationDescriptor* This) {
    return This->lpVtbl->UnlockStore(This);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetCount(IMFPresentationDescriptor* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetItemByIndex(IMFPresentationDescriptor* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_CopyAllItems(IMFPresentationDescriptor* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFPresentationDescriptor methods ***/
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetStreamDescriptorCount(IMFPresentationDescriptor* This,DWORD *pdwDescriptorCount) {
    return This->lpVtbl->GetStreamDescriptorCount(This,pdwDescriptorCount);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_GetStreamDescriptorByIndex(IMFPresentationDescriptor* This,DWORD dwIndex,WINBOOL *pfSelected,IMFStreamDescriptor **ppDescriptor) {
    return This->lpVtbl->GetStreamDescriptorByIndex(This,dwIndex,pfSelected,ppDescriptor);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_SelectStream(IMFPresentationDescriptor* This,DWORD dwDescriptorIndex) {
    return This->lpVtbl->SelectStream(This,dwDescriptorIndex);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_DeselectStream(IMFPresentationDescriptor* This,DWORD dwDescriptorIndex) {
    return This->lpVtbl->DeselectStream(This,dwDescriptorIndex);
}
static FORCEINLINE HRESULT IMFPresentationDescriptor_Clone(IMFPresentationDescriptor* This,IMFPresentationDescriptor **ppPresentationDescriptor) {
    return This->lpVtbl->Clone(This,ppPresentationDescriptor);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFPresentationDescriptor_GetStreamDescriptorCount_Proxy(
    IMFPresentationDescriptor* This,
    DWORD *pdwDescriptorCount);
void __RPC_STUB IMFPresentationDescriptor_GetStreamDescriptorCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFPresentationDescriptor_GetStreamDescriptorByIndex_Proxy(
    IMFPresentationDescriptor* This,
    DWORD dwIndex,
    WINBOOL *pfSelected,
    IMFStreamDescriptor **ppDescriptor);
void __RPC_STUB IMFPresentationDescriptor_GetStreamDescriptorByIndex_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFPresentationDescriptor_SelectStream_Proxy(
    IMFPresentationDescriptor* This,
    DWORD dwDescriptorIndex);
void __RPC_STUB IMFPresentationDescriptor_SelectStream_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFPresentationDescriptor_DeselectStream_Proxy(
    IMFPresentationDescriptor* This,
    DWORD dwDescriptorIndex);
void __RPC_STUB IMFPresentationDescriptor_DeselectStream_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFPresentationDescriptor_Clone_Proxy(
    IMFPresentationDescriptor* This,
    IMFPresentationDescriptor **ppPresentationDescriptor);
void __RPC_STUB IMFPresentationDescriptor_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMFPresentationDescriptor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaSource interface
 */
#ifndef __IMFMediaSource_INTERFACE_DEFINED__
#define __IMFMediaSource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaSource, 0x279a808d, 0xaec7, 0x40c8, 0x9c,0x6b, 0xa6,0xb4,0x92,0xc7,0x8a,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("279a808d-aec7-40c8-9c6b-a6b492c78a66")
IMFMediaSource : public IMFMediaEventGenerator
{
    virtual HRESULT STDMETHODCALLTYPE GetCharacteristics(
        DWORD *pdwCharacteristics) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePresentationDescriptor(
        IMFPresentationDescriptor **ppPresentationDescriptor) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        IMFPresentationDescriptor *pPresentationDescriptor,
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaSource, 0x279a808d, 0xaec7, 0x40c8, 0x9c,0x6b, 0xa6,0xb4,0x92,0xc7,0x8a,0x66)
#endif
#else
typedef struct IMFMediaSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaSource* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaSource* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaSource* This);

    /*** IMFMediaEventGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFMediaSource* This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFMediaSource* This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFMediaSource* This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFMediaSource* This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    /*** IMFMediaSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCharacteristics)(
        IMFMediaSource* This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *CreatePresentationDescriptor)(
        IMFMediaSource* This,
        IMFPresentationDescriptor **ppPresentationDescriptor);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IMFMediaSource* This,
        IMFPresentationDescriptor *pPresentationDescriptor,
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IMFMediaSource* This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFMediaSource* This);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaSource* This);

    END_INTERFACE
} IMFMediaSourceVtbl;
interface IMFMediaSource {
    CONST_VTBL IMFMediaSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaSource_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventGenerator methods ***/
#define IMFMediaSource_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFMediaSource_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFMediaSource_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFMediaSource_QueueEvent(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue)
/*** IMFMediaSource methods ***/
#define IMFMediaSource_GetCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetCharacteristics(This,pdwCharacteristics)
#define IMFMediaSource_CreatePresentationDescriptor(This,ppPresentationDescriptor) (This)->lpVtbl->CreatePresentationDescriptor(This,ppPresentationDescriptor)
#define IMFMediaSource_Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition) (This)->lpVtbl->Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition)
#define IMFMediaSource_Stop(This) (This)->lpVtbl->Stop(This)
#define IMFMediaSource_Pause(This) (This)->lpVtbl->Pause(This)
#define IMFMediaSource_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFMediaSource_QueryInterface(IMFMediaSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFMediaSource_AddRef(IMFMediaSource* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFMediaSource_Release(IMFMediaSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventGenerator methods ***/
static FORCEINLINE HRESULT IMFMediaSource_GetEvent(IMFMediaSource* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static FORCEINLINE HRESULT IMFMediaSource_BeginGetEvent(IMFMediaSource* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static FORCEINLINE HRESULT IMFMediaSource_EndGetEvent(IMFMediaSource* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static FORCEINLINE HRESULT IMFMediaSource_QueueEvent(IMFMediaSource* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue);
}
/*** IMFMediaSource methods ***/
static FORCEINLINE HRESULT IMFMediaSource_GetCharacteristics(IMFMediaSource* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetCharacteristics(This,pdwCharacteristics);
}
static FORCEINLINE HRESULT IMFMediaSource_CreatePresentationDescriptor(IMFMediaSource* This,IMFPresentationDescriptor **ppPresentationDescriptor) {
    return This->lpVtbl->CreatePresentationDescriptor(This,ppPresentationDescriptor);
}
static FORCEINLINE HRESULT IMFMediaSource_Start(IMFMediaSource* This,IMFPresentationDescriptor *pPresentationDescriptor,const GUID *pguidTimeFormat,const PROPVARIANT *pvarStartPosition) {
    return This->lpVtbl->Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition);
}
static FORCEINLINE HRESULT IMFMediaSource_Stop(IMFMediaSource* This) {
    return This->lpVtbl->Stop(This);
}
static FORCEINLINE HRESULT IMFMediaSource_Pause(IMFMediaSource* This) {
    return This->lpVtbl->Pause(This);
}
static FORCEINLINE HRESULT IMFMediaSource_Shutdown(IMFMediaSource* This) {
    return This->lpVtbl->Shutdown(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFMediaSource_GetCharacteristics_Proxy(
    IMFMediaSource* This,
    DWORD *pdwCharacteristics);
void __RPC_STUB IMFMediaSource_GetCharacteristics_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSource_RemoteCreatePresentationDescriptor_Proxy(
    IMFMediaSource* This,
    DWORD *pcbPD,
    BYTE **pbPD,
    IMFPresentationDescriptor **ppRemotePD);
void __RPC_STUB IMFMediaSource_RemoteCreatePresentationDescriptor_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSource_Start_Proxy(
    IMFMediaSource* This,
    IMFPresentationDescriptor *pPresentationDescriptor,
    const GUID *pguidTimeFormat,
    const PROPVARIANT *pvarStartPosition);
void __RPC_STUB IMFMediaSource_Start_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSource_Stop_Proxy(
    IMFMediaSource* This);
void __RPC_STUB IMFMediaSource_Stop_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSource_Pause_Proxy(
    IMFMediaSource* This);
void __RPC_STUB IMFMediaSource_Pause_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaSource_Shutdown_Proxy(
    IMFMediaSource* This);
void __RPC_STUB IMFMediaSource_Shutdown_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFMediaSource_CreatePresentationDescriptor_Proxy(
    IMFMediaSource* This,
    IMFPresentationDescriptor **ppPresentationDescriptor);
HRESULT __RPC_STUB IMFMediaSource_CreatePresentationDescriptor_Stub(
    IMFMediaSource* This,
    DWORD *pcbPD,
    BYTE **pbPD,
    IMFPresentationDescriptor **ppRemotePD);

#endif  /* __IMFMediaSource_INTERFACE_DEFINED__ */

typedef struct _MF_LEAKY_BUCKET_PAIR {
    DWORD dwBitrate;
    DWORD msBufferWindow;
} MF_LEAKY_BUCKET_PAIR;
typedef struct _MFBYTESTREAM_BUFFERING_PARAMS {
    QWORD cbTotalFileSize;
    QWORD cbPlayableDataSize;
    MF_LEAKY_BUCKET_PAIR *prgBuckets;
    DWORD cBuckets;
    QWORD qwNetBufferingTime;
    QWORD qwExtraBufferingTimeDuringSeek;
    QWORD qwPlayDuration;
    float dRate;
} MFBYTESTREAM_BUFFERING_PARAMS;
/*****************************************************************************
 * IMFByteStreamBuffering interface
 */
#ifndef __IMFByteStreamBuffering_INTERFACE_DEFINED__
#define __IMFByteStreamBuffering_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFByteStreamBuffering, 0x6d66d782, 0x1d4f, 0x4db7, 0x8c,0x63, 0xcb,0x8c,0x77,0xf1,0xef,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6d66d782-1d4f-4db7-8c63-cb8c77f1ef5e")
IMFByteStreamBuffering : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetBufferingParams(
        MFBYTESTREAM_BUFFERING_PARAMS *pParams) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableBuffering(
        WINBOOL fEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopBuffering(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFByteStreamBuffering, 0x6d66d782, 0x1d4f, 0x4db7, 0x8c,0x63, 0xcb,0x8c,0x77,0xf1,0xef,0x5e)
#endif
#else
typedef struct IMFByteStreamBufferingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFByteStreamBuffering* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFByteStreamBuffering* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFByteStreamBuffering* This);

    /*** IMFByteStreamBuffering methods ***/
    HRESULT (STDMETHODCALLTYPE *SetBufferingParams)(
        IMFByteStreamBuffering* This,
        MFBYTESTREAM_BUFFERING_PARAMS *pParams);

    HRESULT (STDMETHODCALLTYPE *EnableBuffering)(
        IMFByteStreamBuffering* This,
        WINBOOL fEnable);

    HRESULT (STDMETHODCALLTYPE *StopBuffering)(
        IMFByteStreamBuffering* This);

    END_INTERFACE
} IMFByteStreamBufferingVtbl;
interface IMFByteStreamBuffering {
    CONST_VTBL IMFByteStreamBufferingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFByteStreamBuffering_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFByteStreamBuffering_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFByteStreamBuffering_Release(This) (This)->lpVtbl->Release(This)
/*** IMFByteStreamBuffering methods ***/
#define IMFByteStreamBuffering_SetBufferingParams(This,pParams) (This)->lpVtbl->SetBufferingParams(This,pParams)
#define IMFByteStreamBuffering_EnableBuffering(This,fEnable) (This)->lpVtbl->EnableBuffering(This,fEnable)
#define IMFByteStreamBuffering_StopBuffering(This) (This)->lpVtbl->StopBuffering(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMFByteStreamBuffering_QueryInterface(IMFByteStreamBuffering* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMFByteStreamBuffering_AddRef(IMFByteStreamBuffering* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMFByteStreamBuffering_Release(IMFByteStreamBuffering* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFByteStreamBuffering methods ***/
static FORCEINLINE HRESULT IMFByteStreamBuffering_SetBufferingParams(IMFByteStreamBuffering* This,MFBYTESTREAM_BUFFERING_PARAMS *pParams) {
    return This->lpVtbl->SetBufferingParams(This,pParams);
}
static FORCEINLINE HRESULT IMFByteStreamBuffering_EnableBuffering(IMFByteStreamBuffering* This,WINBOOL fEnable) {
    return This->lpVtbl->EnableBuffering(This,fEnable);
}
static FORCEINLINE HRESULT IMFByteStreamBuffering_StopBuffering(IMFByteStreamBuffering* This) {
    return This->lpVtbl->StopBuffering(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFByteStreamBuffering_SetBufferingParams_Proxy(
    IMFByteStreamBuffering* This,
    MFBYTESTREAM_BUFFERING_PARAMS *pParams);
void __RPC_STUB IMFByteStreamBuffering_SetBufferingParams_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFByteStreamBuffering_EnableBuffering_Proxy(
    IMFByteStreamBuffering* This,
    WINBOOL fEnable);
void __RPC_STUB IMFByteStreamBuffering_EnableBuffering_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFByteStreamBuffering_StopBuffering_Proxy(
    IMFByteStreamBuffering* This);
void __RPC_STUB IMFByteStreamBuffering_StopBuffering_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMFByteStreamBuffering_INTERFACE_DEFINED__ */

HRESULT WINAPI MFRequireProtectedEnvironment(IMFPresentationDescriptor *pPresentationDescriptor);
HRESULT WINAPI MFSerializePresentationDescriptor(IMFPresentationDescriptor *pPD,DWORD *pcbData,BYTE **ppbData);
typedef DWORD MFSequencerElementId; /*Unknown type*/

typedef enum MFCLOCK_CHARACTERISTICS_FLAGS {
  MFCLOCK_CHARACTERISTICS_FLAG_FREQUENCY_10MHZ   = 0x2,
  MFCLOCK_CHARACTERISTICS_FLAG_ALWAYS_RUNNING    = 0x4,
  MFCLOCK_CHARACTERISTICS_FLAG_IS_SYSTEM_CLOCK   = 0x8
} MFCLOCK_CHARACTERISTICS_FLAGS;

#if (WINVER >= 0x0601)

typedef enum _MF_QUALITY_ADVISE_FLAGS {
  MF_QUALITY_CANNOT_KEEP_UP   = 0x1
} MF_QUALITY_ADVISE_FLAGS;

#endif /*(WINVER >= 0x0601)*/

typedef enum _MF_QUALITY_DROP_MODE {
  MF_DROP_MODE_NONE   = 0x0,
  MF_DROP_MODE_1      = 0x1,
  MF_DROP_MODE_2      = 0x2,
  MF_DROP_MODE_3      = 0x3,
  MF_DROP_MODE_4      = 0x4,
  MF_DROP_MODE_5      = 0x5,
  MF_NUM_DROP_MODES   = 0x6
} MF_QUALITY_DROP_MODE;

typedef enum _MF_QUALITY_LEVEL {
  MF_QUALITY_NORMAL           = 0x0,
  MF_QUALITY_NORMAL_MINUS_1   = 0x1,
  MF_QUALITY_NORMAL_MINUS_2   = 0x2,
  MF_QUALITY_NORMAL_MINUS_3   = 0x3,
  MF_QUALITY_NORMAL_MINUS_4   = 0x4,
  MF_QUALITY_NORMAL_MINUS_5   = 0x5,
  MF_NUM_QUALITY_LEVELS       = 0x6
} MF_QUALITY_LEVEL;

typedef enum MF_SOURCE_READER_CONTROL_FLAG {
  MF_SOURCE_READER_CONTROLF_DRAIN   = 0x00000001
} MF_SOURCE_READER_CONTROL_FLAG;

typedef enum _MF_TOPOLOGY_RESOLUTION_STATUS_FLAGS {
  MF_TOPOLOGY_RESOLUTION_SUCCEEDED              = 0x00000000,
  MF_OPTIONAL_NODE_REJECTED_MEDIA_TYPE          = 0x00000001,
  MF_OPTIONAL_NODE_REJECTED_PROTECTED_PROCESS   = 0x00000002
} MF_TOPOLOGY_RESOLUTION_STATUS_FLAGS;

typedef enum _MF_TOPONODE_DRAIN_MODE {
  MF_TOPONODE_DRAIN_DEFAULT,
  MF_TOPONODE_DRAIN_ALWAYS,
  MF_TOPONODE_DRAIN_NEVER
} MF_TOPONODE_DRAIN_MODE;

typedef enum _MF_TOPONODE_FLUSH_MODE {
  MF_TOPONODE_FLUSH_ALWAYS,
  MF_TOPONODE_FLUSH_SEEK,
  MF_TOPONODE_FLUSH_NEVER
} MF_TOPONODE_FLUSH_MODE;

#if (WINVER >= 0x0601)

typedef enum _MF_TRANSCODE_TOPOLOGY_MODE_FLAGS {
  MF_TRANSCODE_TOPOLOGYMODE_SOFTWARE_ONLY      = 0,
  MF_TRANSCODE_TOPOLOGYMODE_HARDWARE_ALLOWED   = 1
} MF_TRANSCODE_TOPOLOGYMODE_FLAGS;

#endif

typedef enum  {
  MF_LICENSE_URL_UNTRUSTED,
  MF_LICENSE_URL_TRUSTED,
  MF_LICENSE_URL_TAMPERED
} MF_URL_TRUST_STATUS;

typedef enum MFASF_INDEXERFLAGS {
  MFASF_INDEXER_WRITE_NEW_INDEX            = 0x00000001,
  MFASF_INDEXER_READ_FOR_REVERSEPLAYBACK   = 0x00000004,
  MFASF_INDEXER_WRITE_FOR_LIVEREAD         = 0x00000008
} MFASF_INDEXERFLAGS;

typedef enum _MFCLOCK_RELATIONAL_FLAGS {
  MFCLOCK_RELATIONAL_FLAG_JITTER_NEVER_AHEAD   = 0x1
} MFCLOCK_RELATIONAL_FLAGS;

typedef enum _MFMEDIASOURCE_CHARACTERISTICS {
  MFMEDIASOURCE_IS_LIVE                      = 0x1,
  MFMEDIASOURCE_CAN_SEEK                     = 0x2,
  MFMEDIASOURCE_CAN_PAUSE                    = 0x4,
  MFMEDIASOURCE_HAS_SLOW_SEEK                = 0x8,
  MFMEDIASOURCE_HAS_MULTIPLE_PRESENTATIONS   = 0x10,
  MFMEDIASOURCE_CAN_SKIPFORWARD              = 0x20,
  MFMEDIASOURCE_CAN_SKIPBACKWARD             = 0x40
} MFMEDIASOURCE_CHARACTERISTICS;

typedef enum _MFNET_PROXYSETTINGS {
  MFNET_PROXYSETTING_NONE      = 0,
  MFNET_PROXYSETTING_MANUAL    = 1,
  MFNET_PROXYSETTING_AUTO      = 2,
  MFNET_PROXYSETTING_BROWSER   = 3
} MFNET_PROXYSETTINGS;

typedef enum _MFNetAuthenticationFlags {
  MFNET_AUTHENTICATION_PROXY            = 0x00000001,
  MFNET_AUTHENTICATION_CLEAR_TEXT       = 0x00000002,
  MFNET_AUTHENTICATION_LOGGED_ON_USER
} MFNetAuthenticationFlags;

typedef enum _MFNetCredentialOptions {
  MFNET_CREDENTIAL_SAVE               = 0x00000001,
  MFNET_CREDENTIAL_DONT_CACHE         = 0x00000002,
  MFNET_CREDENTIAL_ALLOW_CLEAR_TEXT   = 0x00000004
} MFNetCredentialOptions;

typedef enum _MFNetCredentialRequirements {
  REQUIRE_PROMPT          = 0x00000001,
  REQUIRE_SAVE_SELECTED   = 0x00000002
} MFNetCredentialRequirements;

typedef enum _MFNETSOURCE_CACHE_STATE {
  MFNETSOURCE_CACHE_UNAVAILABLE,
  MFNETSOURCE_CACHE_ACTIVE_WRITING,
  MFNETSOURCE_CACHE_ACTIVE_COMPLETE
} MFNETSOURCE_CACHE_STATE;

typedef enum _MFNETSOURCE_PROTOCOL_TYPE {
  MFNETSOURCE_UNDEFINED   = 0x0,
  MFNETSOURCE_HTTP        = 0x1,
  MFNETSOURCE_RTSP        = 0x2,
  MFNETSOURCE_FILE        = 0x3,
  MFNETSOURCE_MULTICAST   = 0x4
} MFNETSOURCE_PROTOCOL_TYPE;

typedef enum _MFNETSOURCE_STATISTICS_IDS {
  MFNETSOURCE_RECVPACKETS_ID                = 0,
  MFNETSOURCE_LOSTPACKETS_ID,
  MFNETSOURCE_RESENDSREQUESTED_ID,
  MFNETSOURCE_RESENDSRECEIVED_ID,
  MFNETSOURCE_RECOVEREDBYECCPACKETS_ID,
  MFNETSOURCE_RECOVEREDBYRTXPACKETS_ID,
  MFNETSOURCE_OUTPACKETS_ID,
  MFNETSOURCE_RECVRATE_ID,
  MFNETSOURCE_AVGBANDWIDTHBPS_ID,
  MFNETSOURCE_BYTESRECEIVED_ID,
  MFNETSOURCE_PROTOCOL_ID,
  MFNETSOURCE_TRANSPORT_ID,
  MFNETSOURCE_CACHE_STATE_ID,
  MFNETSOURCE_LINKBANDWIDTH_ID,
  MFNETSOURCE_CONTENTBITRATE_ID,
  MFNETSOURCE_SPEEDFACTOR_ID,
  MFNETSOURCE_BUFFERSIZE_ID,
  MFNETSOURCE_BUFFERPROGRESS_ID,
  MFNETSOURCE_LASTBWSWITCHTS_ID,
  MFNETSOURCE_SEEKRANGESTART_ID,
  MFNETSOURCE_SEEKRANGEEND_ID,
  MFNETSOURCE_BUFFERINGCOUNT_ID,
  MFNETSOURCE_INCORRECTLYSIGNEDPACKETS_ID,
  MFNETSOURCE_SIGNEDSESSION_ID,
  MFNETSOURCE_MAXBITRATE_ID,
  MFNETSOURCE_RECEPTION_QUALITY_ID,
  MFNETSOURCE_RECOVEREDPACKETS_ID,
  MFNETSOURCE_VBR_ID,
  MFNETSOURCE_DOWNLOADPROGRESS_ID
} MFNETSOURCE_STATISTICS_IDS;

typedef enum _MFNETSOURCE_TRANSPORT_TYPE {
  MFNETSOURCE_UDP,
  MFNETSOURCE_TCP
} MFNETSOURCE_TRANSPORT_TYPE;

typedef enum MF_OBJECT_TYPE {
  MF_OBJECT_MEDIASOURCE,
  MF_OBJECT_BYTESTREAM,
  MF_OBJECT_INVALID
} MF_OBJECT_TYPE;

typedef enum _MFPOLICYMANAGER_ACTION {
  PEACTION_NO          = 0,
  PEACTION_PLAY        = 1,
  PEACTION_COPY        = 2,
  PEACTION_EXPORT      = 3,
  PEACTION_EXTRACT     = 4,
  PEACTION_RESERVED1   = 5,
  PEACTION_RESERVED2   = 6,
  PEACTION_RESERVED3   = 7,
  PEACTION_LAST        = 7
} MFPOLICYMANAGER_ACTION;

typedef enum _MFRATE_DIRECTION {
  MFRATE_FORWARD,
  MFRATE_REVERSE
} MFRATE_DIRECTION;

typedef enum _MFSequencerTopologyFlags {
  SequencerTopologyFlags_Last   = 0x00000001
} MFSequencerTopologyFlags;

typedef enum _MFSHUTDOWN_STATUS {
  MFSHUTDOWN_INITIATED,
  MFSHUTDOWN_COMPLETED
} MFSHUTDOWN_STATUS;

typedef enum _MFSINK_WMDRMACTION {
  MFSINK_WMDRMACTION_UNDEFINED    = 0,
  MFSINK_WMDRMACTION_ENCODE       = 1,
  MFSINK_WMDRMACTION_TRANSCODE    = 2,
  MFSINK_WMDRMACTION_TRANSCRYPT   = 3,
  MFSINK_WMDRMACTION_LAST         = 3
} MFSINK_WMDRMACTION;

typedef enum MFSTREAMSINK_MARKER_TYPE {
  MFSTREAMSINK_MARKER_DEFAULT,
  MFSTREAMSINK_MARKER_ENDOFSEGMENT,
  MFSTREAMSINK_MARKER_TICK,
  MFSTREAMSINK_MARKER_EVENT
} MFSTREAMSINK_MARKER_TYPE;

typedef enum MFTIMER_FLAGS {
  MFTIMER_RELATIVE   = 0x00000001
} MFTIMER_FLAGS;

#if (WINVER >= 0x0601)

typedef enum MFTOPLOGY_DXVA_MODE {
  MFTOPOLOGY_DXVA_DEFAULT   = 0,
  MFTOPOLOGY_DXVA_NONE      = 1,
  MFTOPOLOGY_DXVA_FULL      = 2
} MFTOPOLOGY_DXVA_MODE;

typedef enum MFTOPOLOGY_HARDWARE_MODE {
  MFTOPOLOGY_HWMODE_SOFTWARE_ONLY   = 0,
  MFTOPOLOGY_HWMODE_USE_HARDWARE    = 1
} MFTOPOLOGY_HARDWARE_MODE;

typedef struct _MFT_REGISTRATION_INFO {
  CLSID                  clsid;
  GUID                   guidCategory;
  UINT32                 uiFlags;
  LPCWSTR                pszName;
  DWORD                  cInTypes;
  MFT_REGISTER_TYPE_INFO *pInTypes;
  DWORD                  cOutTypes;
  MFT_REGISTER_TYPE_INFO *pOutTypes;
} MFT_REGISTRATION_INFO;

#endif /*(WINVER >= 0x0601)*/

typedef struct _ASFFlatPicture {
  BYTE  bPictureType;
  DWORD dwDataLen;
} ASF_FLAT_PICTURE;

typedef struct _ASFFlatSynchronisedLyrics {
  BYTE  bTimeStampFormat;
  BYTE  bContentType;
  DWORD dwLyricsLen;
} ASF_FLAT_SYNCHRONISED_LYRICS;

typedef enum SAMPLE_PROTECTION_VERSION {
  SAMPLE_PROTECTION_VERSION_NO           = 0,
  SAMPLE_PROTECTION_VERSION_BASIC_LOKI   = 1,
  SAMPLE_PROTECTION_VERSION_SCATTER      = 2,
  SAMPLE_PROTECTION_VERSION_RC4          = 3
} SAMPLE_PROTECTION_VERSION;

typedef struct _MFINPUTTRUSTAUTHORITY_ACTION {
  MFPOLICYMANAGER_ACTION Action;
  BYTE                   *pbTicket;
  DWORD                  cbTicket;
} MFINPUTTRUSTAUTHORITY_ACCESS_ACTION;

typedef struct _MFINPUTTRUSTAUTHORITY_ACCESS_PARAMS {
  DWORD                               dwSize;
  DWORD                               dwVer;
  DWORD                               cbSignatureOffset;
  DWORD                               cbSignatureSize;
  DWORD                               cbExtensionOffset;
  DWORD                               cbExtensionSize;
  DWORD                               cActions;
  MFINPUTTRUSTAUTHORITY_ACCESS_ACTION rgOutputActions[1];
} MFINPUTTRUSTAUTHORITY_ACCESS_PARAMS;

typedef struct _MFNetCredentialManagerGetParam {
  HRESULT hrOp;
  WINBOOL    fAllowLoggedOnUser;
  WINBOOL    fClearTextPackage;
  LPCWSTR pszUrl;
  LPCWSTR pszSite;
  LPCWSTR pszRealm;
  LPCWSTR pszPackage;
  LONG    nRetries;
} MFNetCredentialManagerGetParam;

#define MF_1_BYTE_ALIGNMENT     0x00000000
#define MF_2_BYTE_ALIGNMENT     0x00000001
#define MF_4_BYTE_ALIGNMENT     0x00000003
#define MF_8_BYTE_ALIGNMENT     0x00000007
#define MF_16_BYTE_ALIGNMENT    0x0000000F
#define MF_32_BYTE_ALIGNMENT    0x0000001F
#define MF_64_BYTE_ALIGNMENT    0x0000003F
#define MF_128_BYTE_ALIGNMENT   0x0000007F
#define MF_256_BYTE_ALIGNMENT   0x000000FF
#define MF_512_BYTE_ALIGNMENT   0x000001FF

#define MEDIASINK_FIXED_STREAMS                 0x00000001
#define MEDIASINK_CANNOT_MATCH_CLOCK            0x00000002
#define MEDIASINK_RATELESS                      0x00000004
#define MEDIASINK_CLOCK_REQUIRED                0x00000008
#define MEDIASINK_CAN_PREROLL                   0x00000010
#define MEDIASINK_REQUIRE_REFERENCE_MEDIATYPE   0x00000020

#define MF_RESOLUTION_MEDIASOURCE                                           0x00000001
#define MF_RESOLUTION_BYTESTREAM                                            0x00000002
#define MF_RESOLUTION_CONTENT_DOES_NOT_HAVE_TO_MATCH_EXTENSION_OR_MIME_TYPE 0x00000010
#define MF_RESOLUTION_KEEP_BYTE_STREAM_ALIVE_ON_FAIL                        0x00000020
#define MF_RESOLUTION_READ                                                  0x00010000
#define MF_RESOLUTION_WRITE                                                 0x00020000
#ifndef __IMFMediaSink_FWD_DEFINED__
#define __IMFMediaSink_FWD_DEFINED__
typedef interface IMFMediaSink IMFMediaSink;
#endif

#ifndef __IMFASFContentInfo_FWD_DEFINED__
#define __IMFASFContentInfo_FWD_DEFINED__
typedef interface IMFASFContentInfo IMFASFContentInfo;
#endif

#ifndef __IMFASFIndexer_FWD_DEFINED__
#define __IMFASFIndexer_FWD_DEFINED__
typedef interface IMFASFIndexer IMFASFIndexer;
#endif

#ifndef __IMFASFMultiplexer_FWD_DEFINED__
#define __IMFASFMultiplexer_FWD_DEFINED__
typedef interface IMFASFMultiplexer IMFASFMultiplexer;
#endif

#ifndef __IMFASFProfile_FWD_DEFINED__
#define __IMFASFProfile_FWD_DEFINED__
typedef interface IMFASFProfile IMFASFProfile;
#endif

#ifndef __IMFASFSplitter_FWD_DEFINED__
#define __IMFASFSplitter_FWD_DEFINED__
typedef interface IMFASFSplitter IMFASFSplitter;
#endif

#ifndef __IMFPMPServer_FWD_DEFINED__
#define __IMFPMPServer_FWD_DEFINED__
typedef interface IMFPMPServer IMFPMPServer;
#endif

#ifndef __IMFPresentationClock_FWD_DEFINED__
#define __IMFPresentationClock_FWD_DEFINED__
typedef interface IMFPresentationClock IMFPresentationClock;
#endif

#ifndef __IMFNetProxyLocator_FWD_DEFINED__
#define __IMFNetProxyLocator_FWD_DEFINED__
typedef interface IMFNetProxyLocator IMFNetProxyLocator;
#endif

#ifndef __IMFRemoteDesktopPlugin_FWD_DEFINED__
#define __IMFRemoteDesktopPlugin_FWD_DEFINED__
typedef interface IMFRemoteDesktopPlugin IMFRemoteDesktopPlugin;
#endif

#ifndef __IMFTransform_FWD_DEFINED__
#define __IMFTransform_FWD_DEFINED__
typedef interface IMFTransform IMFTransform;
#endif

#ifndef __IMFSampleGrabberSinkCallback_FWD_DEFINED__
#define __IMFSampleGrabberSinkCallback_FWD_DEFINED__
typedef interface IMFSampleGrabberSinkCallback IMFSampleGrabberSinkCallback;
#endif

#ifndef __IMFSequencerSource_FWD_DEFINED__
#define __IMFSequencerSource_FWD_DEFINED__
typedef interface IMFSequencerSource IMFSequencerSource;
#endif

#ifndef __IMFQualityManager_FWD_DEFINED__
#define __IMFQualityManager_FWD_DEFINED__
typedef interface IMFQualityManager IMFQualityManager;
#endif

#ifndef __IMFSourceResolver_FWD_DEFINED__
#define __IMFSourceResolver_FWD_DEFINED__
typedef interface IMFSourceResolver IMFSourceResolver;
#endif

#ifndef __IMFPresentationTimeSource_FWD_DEFINED__
#define __IMFPresentationTimeSource_FWD_DEFINED__
typedef interface IMFPresentationTimeSource IMFPresentationTimeSource;
#endif

#ifndef __IMFTopoLoader_FWD_DEFINED__
#define __IMFTopoLoader_FWD_DEFINED__
typedef interface IMFTopoLoader IMFTopoLoader;
#endif

#ifndef __IMFTranscodeProfile_FWD_DEFINED__
#define __IMFTranscodeProfile_FWD_DEFINED__
typedef interface IMFTranscodeProfile IMFTranscodeProfile;
#endif

HRESULT MFCreate3GPMediaSink(IMFByteStream *pIByteStream,IMFMediaType *pVideoMediaType,IMFMediaType *pAudioMediaType,IMFMediaSink **ppIMediaSink);
HRESULT MFCreateAggregateSource(IMFCollection *pSourceCollection,IMFMediaSource **ppAggSource);

HRESULT WINAPI MFCreateAlignedMemoryBuffer(DWORD cbMaxLength,DWORD fAlignmentFlags,IMFMediaBuffer **ppBuffer);
HRESULT WINAPI MFCreateASFContentInfo(IMFASFContentInfo **ppIContentInfo);
HRESULT WINAPI MFCreateASFIndexer(IMFASFIndexer **ppIIndexer);
HRESULT WINAPI MFCreateASFIndexerByteStream(IMFByteStream *pIContentByteStream,QWORD cbIndexStartOffset,IMFByteStream **pIIndexByteStream);
HRESULT WINAPI MFCreateASFMediaSink(IMFByteStream *pIByteStream,IMFMediaSink **ppIMediaSink);
HRESULT WINAPI MFCreateASFMediaSinkActivate(LPCWSTR pwszFileName,IMFASFContentInfo *pContentInfo,IMFActivate **ppIActivate);
HRESULT WINAPI MFCreateASFMultiplexer(IMFASFMultiplexer **ppIMultiplexer);
HRESULT WINAPI MFCreateASFProfile(IMFASFProfile **ppIProfile);
HRESULT WINAPI MFCreateASFProfileFromPresentationDescriptor(IMFPresentationDescriptor *pIPD,IMFASFProfile **ppIProfile);
HRESULT WINAPI MFCreateASFSplitter(IMFASFSplitter **ppISplitter);
HRESULT WINAPI MFCreateMediaSession(IMFAttributes *pConfiguration,IMFMediaSession **ppMS);
HRESULT WINAPI MFCreateMP3MediaSink(IMFByteStream *pTargetByteStream,IMFMediaSink **ppMediaSink);
HRESULT WINAPI MFCreateMPEG4MediaSink(IMFByteStream *pIByteStream,IMFMediaType *pVideoMediaType,IMFMediaType *pAudioMediaType,IMFMediaSink **ppIMediaSink);
HRESULT WINAPI MFCreateNetSchemePlugin(REFIID riid,LPVOID *ppvHandler);
HRESULT WINAPI MFCreatePMPServer(DWORD dwCreationFlags,IMFPMPServer **ppPMPServer);
HRESULT WINAPI MFCreatePMPMediaSession(DWORD dwCreationFlags,IMFAttributes *pConfiguration,IMFMediaSession **ppMediaSession,IMFActivate **ppEnablerActivate);
HRESULT WINAPI MFCreatePathFromURL(LPCWSTR pwszFileURL,LPWSTR *ppwszFilePath);
HRESULT WINAPI MFCreatePresentationClock(IMFPresentationClock **ppPresentationClock);
HRESULT WINAPI MFCreatePresentationDescriptor(DWORD cStreamDescriptors,IMFStreamDescriptor **apStreamDescriptors,IMFPresentationDescriptor **ppPresentationDescriptor);
HRESULT WINAPI MFCreatePresentationDescriptorFromASFProfile(IMFASFProfile *pIProfile,IMFPresentationDescriptor **ppIPD);
HRESULT WINAPI MFCreateProxyLocator(LPCWSTR pszProtocol,IPropertyStore *pProxyConfig,IMFNetProxyLocator **ppProxyLocator);
HRESULT WINAPI MFCreateRemoteDesktopPlugin(IMFRemoteDesktopPlugin **ppPlugin);
HRESULT WINAPI MFCreateSample(IMFSample **ppIMFSample);
HRESULT WINAPI MFCreateSampleCopierMFT(IMFTransform **ppCopierMFT);
HRESULT WINAPI MFCreateSampleGrabberSinkActivate(IMFMediaType *pIMFMediaType,IMFSampleGrabberSinkCallback *pIMFSampleGrabberSinkCallback,IMFActivate **ppIActivate);
HRESULT WINAPI MFCreateSequencerSource(IUnknown *pReserved,IMFSequencerSource **ppSequencerSource);
HRESULT WINAPI MFCreateSimpleTypeHandler(IMFMediaTypeHandler **ppHandler);
HRESULT WINAPI MFCreateStandardQualityManager(IMFQualityManager **ppQualityManager);
HRESULT WINAPI MFCreateStreamDescriptor(DWORD dwStreamIdentifier,DWORD cMediaTypes,IMFMediaType **apMediaTypes,IMFStreamDescriptor **ppDescriptor);
HRESULT WINAPI MFCreateSourceResolver(IMFSourceResolver **ppISourceResolver);
HRESULT WINAPI MFCreateSystemTimeSource(IMFPresentationTimeSource **ppSystemTimeSource);
HRESULT WINAPI MFCreateTopoLoader(IMFTopoLoader **ppObj);
HRESULT WINAPI MFCreateTopology(IMFTopology **ppTopo);
HRESULT WINAPI MFCreateTopologyNode(MF_TOPOLOGY_TYPE NodeType,IMFTopologyNode **ppNode);
HRESULT WINAPI MFCreateVideoRenderer(REFIID riidRenderer,void **ppVideoRenderer);
HRESULT WINAPI MFCreateVideoRendererActivate(HWND hwndVideo,IMFActivate **ppActivate);
HRESULT WINAPI MFGetService(IUnknown *punkObject,REFGUID guidService,REFIID riid,LPVOID *ppvObject);
HRESULT WINAPI MFGetSupportedMimeTypes(PROPVARIANT *pPropVarMimeTypeArray);
HRESULT WINAPI MFGetSupportedSchemes(PROPVARIANT *pPropVarSchemeArray);
MFTIME WINAPI MFGetSystemTime(void);
HRESULT WINAPI MFShutdownObject(IUnknown *pUnk);
HRESULT WINAPI CreateNamedPropertyStore(INamedPropertyStore **ppStore);
HRESULT WINAPI CreatePropertyStore(IPropertyStore **ppStore);
#if (WINVER >= 0x0601)

HRESULT WINAPI MFCreateTranscodeProfile(IMFTranscodeProfile **ppTranscodeProfile);
HRESULT WINAPI MFCreateTranscodeSinkActivate(IMFActivate **ppActivate);
HRESULT WINAPI MFCreateTranscodeTopology(IMFMediaSource *pSrc,LPCWSTR pwszOutputFilePath,IMFTranscodeProfile *pProfile,IMFTopology **ppTranscodeTopo);
HRESULT WINAPI MFEnumDeviceSources(IMFAttributes *pAttributes,IMFActivate ***pppSourceActivate,UINT32 *pcSourceActivate);
HRESULT WINAPI MFGetTopoNodeCurrentType(IMFTopologyNode *pNode,DWORD dwStreamIndex,WINBOOL fOutput,IMFMediaType **ppType);
HRESULT WINAPI MFTranscodeGetAudioOutputAvailableTypes(REFGUID guidSubType,DWORD dwMFTFlags,IMFAttributes *pCodecConfig,IMFCollection **ppAvailableTypes);
#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mfidl_h__ */
