/*** Autogenerated by WIDL 1.6 from include/fsrm.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __fsrm_h__
#define __fsrm_h__

/* Forward declarations */

#ifndef __IFsrmCommittableCollection_FWD_DEFINED__
#define __IFsrmCommittableCollection_FWD_DEFINED__
typedef interface IFsrmCommittableCollection IFsrmCommittableCollection;
#endif

#ifndef __IFsrmAccessDeniedRemediationClient_FWD_DEFINED__
#define __IFsrmAccessDeniedRemediationClient_FWD_DEFINED__
typedef interface IFsrmAccessDeniedRemediationClient IFsrmAccessDeniedRemediationClient;
#endif

#ifndef __IFsrmAction_FWD_DEFINED__
#define __IFsrmAction_FWD_DEFINED__
typedef interface IFsrmAction IFsrmAction;
#endif

#ifndef __IFsrmActionCommand_FWD_DEFINED__
#define __IFsrmActionCommand_FWD_DEFINED__
typedef interface IFsrmActionCommand IFsrmActionCommand;
#endif

#ifndef __IFsrmActionEventLog_FWD_DEFINED__
#define __IFsrmActionEventLog_FWD_DEFINED__
typedef interface IFsrmActionEventLog IFsrmActionEventLog;
#endif

#ifndef __IFsrmActionReport_FWD_DEFINED__
#define __IFsrmActionReport_FWD_DEFINED__
typedef interface IFsrmActionReport IFsrmActionReport;
#endif

#ifndef __IFsrmCollection_FWD_DEFINED__
#define __IFsrmCollection_FWD_DEFINED__
typedef interface IFsrmCollection IFsrmCollection;
#endif

#ifndef __IFsrmDerivedObjectsResult_FWD_DEFINED__
#define __IFsrmDerivedObjectsResult_FWD_DEFINED__
typedef interface IFsrmDerivedObjectsResult IFsrmDerivedObjectsResult;
#endif

#ifndef __IFsrmExportImport_FWD_DEFINED__
#define __IFsrmExportImport_FWD_DEFINED__
typedef interface IFsrmExportImport IFsrmExportImport;
#endif

#ifndef __IFsrmObject_FWD_DEFINED__
#define __IFsrmObject_FWD_DEFINED__
typedef interface IFsrmObject IFsrmObject;
#endif

#ifndef __IFsrmPathMapper_FWD_DEFINED__
#define __IFsrmPathMapper_FWD_DEFINED__
typedef interface IFsrmPathMapper IFsrmPathMapper;
#endif

#ifndef __IFsrmSetting_FWD_DEFINED__
#define __IFsrmSetting_FWD_DEFINED__
typedef interface IFsrmSetting IFsrmSetting;
#endif

#ifndef __IFsrmActionEmail_FWD_DEFINED__
#define __IFsrmActionEmail_FWD_DEFINED__
typedef interface IFsrmActionEmail IFsrmActionEmail;
#endif

#ifndef __IFsrmActionEmail2_FWD_DEFINED__
#define __IFsrmActionEmail2_FWD_DEFINED__
typedef interface IFsrmActionEmail2 IFsrmActionEmail2;
#endif

#ifndef __IFsrmMutableCollection_FWD_DEFINED__
#define __IFsrmMutableCollection_FWD_DEFINED__
typedef interface IFsrmMutableCollection IFsrmMutableCollection;
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <fsrmenums.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IFsrmObject_FWD_DEFINED__
#define __IFsrmObject_FWD_DEFINED__
typedef interface IFsrmObject IFsrmObject;
#endif

#ifndef __IFsrmCollection_FWD_DEFINED__
#define __IFsrmCollection_FWD_DEFINED__
typedef interface IFsrmCollection IFsrmCollection;
#endif

#ifndef __IFsrmMutableCollection_FWD_DEFINED__
#define __IFsrmMutableCollection_FWD_DEFINED__
typedef interface IFsrmMutableCollection IFsrmMutableCollection;
#endif

#ifndef __IFsrmCommittableCollection_FWD_DEFINED__
#define __IFsrmCommittableCollection_FWD_DEFINED__
typedef interface IFsrmCommittableCollection IFsrmCommittableCollection;
#endif

#ifndef __IFsrmAction_FWD_DEFINED__
#define __IFsrmAction_FWD_DEFINED__
typedef interface IFsrmAction IFsrmAction;
#endif

#ifndef __IFsrmActionEmail_FWD_DEFINED__
#define __IFsrmActionEmail_FWD_DEFINED__
typedef interface IFsrmActionEmail IFsrmActionEmail;
#endif

#ifndef __IFsrmActionReport_FWD_DEFINED__
#define __IFsrmActionReport_FWD_DEFINED__
typedef interface IFsrmActionReport IFsrmActionReport;
#endif

#ifndef __IFsrmActionEventLog_FWD_DEFINED__
#define __IFsrmActionEventLog_FWD_DEFINED__
typedef interface IFsrmActionEventLog IFsrmActionEventLog;
#endif

#ifndef __IFsrmActionCommand_FWD_DEFINED__
#define __IFsrmActionCommand_FWD_DEFINED__
typedef interface IFsrmActionCommand IFsrmActionCommand;
#endif

#ifndef __IFsrmSetting_FWD_DEFINED__
#define __IFsrmSetting_FWD_DEFINED__
typedef interface IFsrmSetting IFsrmSetting;
#endif

#ifndef __IFsrmPathMapper_FWD_DEFINED__
#define __IFsrmPathMapper_FWD_DEFINED__
typedef interface IFsrmPathMapper IFsrmPathMapper;
#endif

#ifndef __IFsrmExportImport_FWD_DEFINED__
#define __IFsrmExportImport_FWD_DEFINED__
typedef interface IFsrmExportImport IFsrmExportImport;
#endif

#define FSRM_DISPID_FEATURE_MASK (0xf000000)

#define FSRM_DISPID_INTERFACE_A_MASK (0xf00000)

#define FSRM_DISPID_INTERFACE_B_MASK (0xf0000)

#define FSRM_DISPID_INTERFACE_C_MASK (0xf000)

#define FSRM_DISPID_INTERFACE_D_MASK (0xf00)

#define FSRM_DISPID_INTERFACE_MASK (0xffff00)

#define FSRM_DISPID_IS_PROPERTY (0x80)

#define FSRM_DISPID_METHOD_NUM_MASK (0x7f)

#define FSRM_DISPID_METHOD_MASK (0xff)

#define FSRM_DISPID_FEATURE_GENERAL (0x1000000)

#define FSRM_DISPID_FEATURE_QUOTA (0x2000000)

#define FSRM_DISPID_FEATURE_FILESCREEN (0x3000000)

#define FSRM_DISPID_FEATURE_REPORTS (0x4000000)

#define FSRM_DISPID_FEATURE_CLASSIFICATION (0x5000000)

#define FSRM_DISPID_FEATURE_PIPELINE (0x6000000)

#define FSRM_DISPID_OBJECT (FSRM_DISPID_FEATURE_GENERAL | 0x100000)

#define FSRM_DISPID_COLLECTION (FSRM_DISPID_FEATURE_GENERAL | 0x200000)

#define FSRM_DISPID_COLLECTION_MUTABLE (FSRM_DISPID_COLLECTION | 0x10000)

#define FSRM_DISPID_COLLECTION_COMMITTABLE (FSRM_DISPID_COLLECTION_MUTABLE | 0x1000)

#define FSRM_DISPID_ACTION (FSRM_DISPID_FEATURE_GENERAL | 0x300000)

#define FSRM_DISPID_ACTION_EMAIL (FSRM_DISPID_ACTION | 0x10000)

#define FSRM_DISPID_ACTION_REPORT (FSRM_DISPID_ACTION | 0x20000)

#define FSRM_DISPID_ACTION_EVENTLOG (FSRM_DISPID_ACTION | 0x30000)

#define FSRM_DISPID_ACTION_COMMAND (FSRM_DISPID_ACTION | 0x40000)

#define FSRM_DISPID_ACTION_EMAIL2 (FSRM_DISPID_ACTION | 0x50000)

#define FSRM_DISPID_SETTING (FSRM_DISPID_FEATURE_GENERAL | 0x400000)

#define FSRM_DISPID_PATHMAPPER (FSRM_DISPID_FEATURE_GENERAL | 0x500000)

#define FSRM_DISPID_EXPORTIMPORT (FSRM_DISPID_FEATURE_GENERAL | 0x600000)

#define FSRM_DISPID_DERIVEDOBJECTSRESULT (FSRM_DISPID_FEATURE_GENERAL | 0x700000)

#define FSRM_DISPID_ADR (FSRM_DISPID_FEATURE_GENERAL | 0x800000)

/*****************************************************************************
 * IFsrmCommittableCollection interface
 */
#ifndef __IFsrmCommittableCollection_INTERFACE_DEFINED__
#define __IFsrmCommittableCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmCommittableCollection, 0x96deb3b5, 0x8b91, 0x4a2a, 0x9d,0x93, 0x80,0xa3,0x5d,0x8a,0xa8,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96deb3b5-8b91-4a2a-9d93-80a35d8aa847")
IFsrmCommittableCollection : public IFsrmMutableCollection
{
    virtual HRESULT STDMETHODCALLTYPE Commit(
        FsrmCommitOptions options,
        IFsrmCollection **results) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmCommittableCollection, 0x96deb3b5, 0x8b91, 0x4a2a, 0x9d,0x93, 0x80,0xa3,0x5d,0x8a,0xa8,0x47)
#endif
#else
typedef struct IFsrmCommittableCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmCommittableCollection* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmCommittableCollection* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmCommittableCollection* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmCommittableCollection* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmCommittableCollection* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmCommittableCollection* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmCommittableCollection* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IFsrmCommittableCollection* This,
        IUnknown **unknown);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IFsrmCommittableCollection* This,
        LONG index,
        VARIANT *item);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IFsrmCommittableCollection* This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        IFsrmCommittableCollection* This,
        FsrmCollectionState *state);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IFsrmCommittableCollection* This);

    HRESULT (STDMETHODCALLTYPE *WaitForCompletion)(
        IFsrmCommittableCollection* This,
        LONG waitSeconds,
        VARIANT_BOOL *completed);

    HRESULT (STDMETHODCALLTYPE *GetById)(
        IFsrmCommittableCollection* This,
        FSRM_OBJECT_ID id,
        VARIANT *entry);

    /*** IFsrmMutableCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *Add)(
        IFsrmCommittableCollection* This,
        VARIANT item);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IFsrmCommittableCollection* This,
        LONG index);

    HRESULT (STDMETHODCALLTYPE *RemoveById)(
        IFsrmCommittableCollection* This,
        FSRM_OBJECT_ID id);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IFsrmCommittableCollection* This,
        IFsrmMutableCollection **collection);

    /*** IFsrmCommittableCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmCommittableCollection* This,
        FsrmCommitOptions options,
        IFsrmCollection **results);

    END_INTERFACE
} IFsrmCommittableCollectionVtbl;
interface IFsrmCommittableCollection {
    CONST_VTBL IFsrmCommittableCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmCommittableCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmCommittableCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmCommittableCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmCommittableCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmCommittableCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmCommittableCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmCommittableCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmCollection methods ***/
#define IFsrmCommittableCollection_get__NewEnum(This,unknown) (This)->lpVtbl->get__NewEnum(This,unknown)
#define IFsrmCommittableCollection_get_Item(This,index,item) (This)->lpVtbl->get_Item(This,index,item)
#define IFsrmCommittableCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define IFsrmCommittableCollection_get_State(This,state) (This)->lpVtbl->get_State(This,state)
#define IFsrmCommittableCollection_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IFsrmCommittableCollection_WaitForCompletion(This,waitSeconds,completed) (This)->lpVtbl->WaitForCompletion(This,waitSeconds,completed)
#define IFsrmCommittableCollection_GetById(This,id,entry) (This)->lpVtbl->GetById(This,id,entry)
/*** IFsrmMutableCollection methods ***/
#define IFsrmCommittableCollection_Add(This,item) (This)->lpVtbl->Add(This,item)
#define IFsrmCommittableCollection_Remove(This,index) (This)->lpVtbl->Remove(This,index)
#define IFsrmCommittableCollection_RemoveById(This,id) (This)->lpVtbl->RemoveById(This,id)
#define IFsrmCommittableCollection_Clone(This,collection) (This)->lpVtbl->Clone(This,collection)
/*** IFsrmCommittableCollection methods ***/
#define IFsrmCommittableCollection_Commit(This,options,results) (This)->lpVtbl->Commit(This,options,results)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmCommittableCollection_QueryInterface(IFsrmCommittableCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmCommittableCollection_AddRef(IFsrmCommittableCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmCommittableCollection_Release(IFsrmCommittableCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmCommittableCollection_GetTypeInfoCount(IFsrmCommittableCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_GetTypeInfo(IFsrmCommittableCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_GetIDsOfNames(IFsrmCommittableCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_Invoke(IFsrmCommittableCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmCollection methods ***/
static FORCEINLINE HRESULT IFsrmCommittableCollection_get__NewEnum(IFsrmCommittableCollection* This,IUnknown **unknown) {
    return This->lpVtbl->get__NewEnum(This,unknown);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_get_Item(IFsrmCommittableCollection* This,LONG index,VARIANT *item) {
    return This->lpVtbl->get_Item(This,index,item);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_get_Count(IFsrmCommittableCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_get_State(IFsrmCommittableCollection* This,FsrmCollectionState *state) {
    return This->lpVtbl->get_State(This,state);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_Cancel(IFsrmCommittableCollection* This) {
    return This->lpVtbl->Cancel(This);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_WaitForCompletion(IFsrmCommittableCollection* This,LONG waitSeconds,VARIANT_BOOL *completed) {
    return This->lpVtbl->WaitForCompletion(This,waitSeconds,completed);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_GetById(IFsrmCommittableCollection* This,FSRM_OBJECT_ID id,VARIANT *entry) {
    return This->lpVtbl->GetById(This,id,entry);
}
/*** IFsrmMutableCollection methods ***/
static FORCEINLINE HRESULT IFsrmCommittableCollection_Add(IFsrmCommittableCollection* This,VARIANT item) {
    return This->lpVtbl->Add(This,item);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_Remove(IFsrmCommittableCollection* This,LONG index) {
    return This->lpVtbl->Remove(This,index);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_RemoveById(IFsrmCommittableCollection* This,FSRM_OBJECT_ID id) {
    return This->lpVtbl->RemoveById(This,id);
}
static FORCEINLINE HRESULT IFsrmCommittableCollection_Clone(IFsrmCommittableCollection* This,IFsrmMutableCollection **collection) {
    return This->lpVtbl->Clone(This,collection);
}
/*** IFsrmCommittableCollection methods ***/
static FORCEINLINE HRESULT IFsrmCommittableCollection_Commit(IFsrmCommittableCollection* This,FsrmCommitOptions options,IFsrmCollection **results) {
    return This->lpVtbl->Commit(This,options,results);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmCommittableCollection_Commit_Proxy(
    IFsrmCommittableCollection* This,
    FsrmCommitOptions options,
    IFsrmCollection **results);
void __RPC_STUB IFsrmCommittableCollection_Commit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmCommittableCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmAccessDeniedRemediationClient interface
 */
#ifndef __IFsrmAccessDeniedRemediationClient_INTERFACE_DEFINED__
#define __IFsrmAccessDeniedRemediationClient_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmAccessDeniedRemediationClient, 0x40002314, 0x590b, 0x45a5, 0x8e,0x1b, 0x8c,0x05,0xda,0x52,0x7e,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("40002314-590b-45a5-8e1b-8c05da527e52")
IFsrmAccessDeniedRemediationClient : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Show(
        ULONG_PTR parentWnd,
        BSTR accessPath,
        AdrClientErrorType errorType,
        LONG flags,
        BSTR windowTitle,
        BSTR windowMessage,
        LONG *result) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmAccessDeniedRemediationClient, 0x40002314, 0x590b, 0x45a5, 0x8e,0x1b, 0x8c,0x05,0xda,0x52,0x7e,0x52)
#endif
#else
typedef struct IFsrmAccessDeniedRemediationClientVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmAccessDeniedRemediationClient* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmAccessDeniedRemediationClient* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmAccessDeniedRemediationClient* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmAccessDeniedRemediationClient* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmAccessDeniedRemediationClient* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmAccessDeniedRemediationClient* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmAccessDeniedRemediationClient* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmAccessDeniedRemediationClient methods ***/
    HRESULT (STDMETHODCALLTYPE *Show)(
        IFsrmAccessDeniedRemediationClient* This,
        ULONG_PTR parentWnd,
        BSTR accessPath,
        AdrClientErrorType errorType,
        LONG flags,
        BSTR windowTitle,
        BSTR windowMessage,
        LONG *result);

    END_INTERFACE
} IFsrmAccessDeniedRemediationClientVtbl;
interface IFsrmAccessDeniedRemediationClient {
    CONST_VTBL IFsrmAccessDeniedRemediationClientVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmAccessDeniedRemediationClient_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmAccessDeniedRemediationClient_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmAccessDeniedRemediationClient_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmAccessDeniedRemediationClient_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmAccessDeniedRemediationClient_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmAccessDeniedRemediationClient_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmAccessDeniedRemediationClient_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmAccessDeniedRemediationClient methods ***/
#define IFsrmAccessDeniedRemediationClient_Show(This,parentWnd,accessPath,errorType,flags,windowTitle,windowMessage,result) (This)->lpVtbl->Show(This,parentWnd,accessPath,errorType,flags,windowTitle,windowMessage,result)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmAccessDeniedRemediationClient_QueryInterface(IFsrmAccessDeniedRemediationClient* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmAccessDeniedRemediationClient_AddRef(IFsrmAccessDeniedRemediationClient* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmAccessDeniedRemediationClient_Release(IFsrmAccessDeniedRemediationClient* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmAccessDeniedRemediationClient_GetTypeInfoCount(IFsrmAccessDeniedRemediationClient* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmAccessDeniedRemediationClient_GetTypeInfo(IFsrmAccessDeniedRemediationClient* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmAccessDeniedRemediationClient_GetIDsOfNames(IFsrmAccessDeniedRemediationClient* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmAccessDeniedRemediationClient_Invoke(IFsrmAccessDeniedRemediationClient* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmAccessDeniedRemediationClient methods ***/
static FORCEINLINE HRESULT IFsrmAccessDeniedRemediationClient_Show(IFsrmAccessDeniedRemediationClient* This,ULONG_PTR parentWnd,BSTR accessPath,AdrClientErrorType errorType,LONG flags,BSTR windowTitle,BSTR windowMessage,LONG *result) {
    return This->lpVtbl->Show(This,parentWnd,accessPath,errorType,flags,windowTitle,windowMessage,result);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmAccessDeniedRemediationClient_Show_Proxy(
    IFsrmAccessDeniedRemediationClient* This,
    ULONG_PTR parentWnd,
    BSTR accessPath,
    AdrClientErrorType errorType,
    LONG flags,
    BSTR windowTitle,
    BSTR windowMessage,
    LONG *result);
void __RPC_STUB IFsrmAccessDeniedRemediationClient_Show_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmAccessDeniedRemediationClient_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmAction interface
 */
#ifndef __IFsrmAction_INTERFACE_DEFINED__
#define __IFsrmAction_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmAction, 0x6cd6408a, 0xae60, 0x463b, 0x9e,0xf1, 0xe1,0x17,0x53,0x4d,0x69,0xdc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6cd6408a-ae60-463b-9ef1-e117534d69dc")
IFsrmAction : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Id(
        FSRM_OBJECT_ID *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ActionType(
        FsrmActionType *actionType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RunLimitInterval(
        LONG *minutes) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RunLimitInterval(
        LONG minutes) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmAction, 0x6cd6408a, 0xae60, 0x463b, 0x9e,0xf1, 0xe1,0x17,0x53,0x4d,0x69,0xdc)
#endif
#else
typedef struct IFsrmActionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmAction* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmAction* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmAction* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmAction* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmAction* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmAction* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmAction* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmAction* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_ActionType)(
        IFsrmAction* This,
        FsrmActionType *actionType);

    HRESULT (STDMETHODCALLTYPE *get_RunLimitInterval)(
        IFsrmAction* This,
        LONG *minutes);

    HRESULT (STDMETHODCALLTYPE *put_RunLimitInterval)(
        IFsrmAction* This,
        LONG minutes);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmAction* This);

    END_INTERFACE
} IFsrmActionVtbl;
interface IFsrmAction {
    CONST_VTBL IFsrmActionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmAction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmAction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmAction_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmAction_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmAction_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmAction_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmAction_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmAction methods ***/
#define IFsrmAction_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmAction_get_ActionType(This,actionType) (This)->lpVtbl->get_ActionType(This,actionType)
#define IFsrmAction_get_RunLimitInterval(This,minutes) (This)->lpVtbl->get_RunLimitInterval(This,minutes)
#define IFsrmAction_put_RunLimitInterval(This,minutes) (This)->lpVtbl->put_RunLimitInterval(This,minutes)
#define IFsrmAction_Delete(This) (This)->lpVtbl->Delete(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmAction_QueryInterface(IFsrmAction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmAction_AddRef(IFsrmAction* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmAction_Release(IFsrmAction* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmAction_GetTypeInfoCount(IFsrmAction* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmAction_GetTypeInfo(IFsrmAction* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmAction_GetIDsOfNames(IFsrmAction* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmAction_Invoke(IFsrmAction* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmAction methods ***/
static FORCEINLINE HRESULT IFsrmAction_get_Id(IFsrmAction* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmAction_get_ActionType(IFsrmAction* This,FsrmActionType *actionType) {
    return This->lpVtbl->get_ActionType(This,actionType);
}
static FORCEINLINE HRESULT IFsrmAction_get_RunLimitInterval(IFsrmAction* This,LONG *minutes) {
    return This->lpVtbl->get_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmAction_put_RunLimitInterval(IFsrmAction* This,LONG minutes) {
    return This->lpVtbl->put_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmAction_Delete(IFsrmAction* This) {
    return This->lpVtbl->Delete(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmAction_get_Id_Proxy(
    IFsrmAction* This,
    FSRM_OBJECT_ID *id);
void __RPC_STUB IFsrmAction_get_Id_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmAction_get_ActionType_Proxy(
    IFsrmAction* This,
    FsrmActionType *actionType);
void __RPC_STUB IFsrmAction_get_ActionType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmAction_get_RunLimitInterval_Proxy(
    IFsrmAction* This,
    LONG *minutes);
void __RPC_STUB IFsrmAction_get_RunLimitInterval_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmAction_put_RunLimitInterval_Proxy(
    IFsrmAction* This,
    LONG minutes);
void __RPC_STUB IFsrmAction_put_RunLimitInterval_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmAction_Delete_Proxy(
    IFsrmAction* This);
void __RPC_STUB IFsrmAction_Delete_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmAction_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmActionCommand interface
 */
#ifndef __IFsrmActionCommand_INTERFACE_DEFINED__
#define __IFsrmActionCommand_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmActionCommand, 0x********, 0xe247, 0x4917, 0x9c,0x20, 0xf3,0xee,0x9c,0x7e,0xe7,0x83);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("********-e247-4917-9c20-f3ee9c7ee783")
IFsrmActionCommand : public IFsrmAction
{
    virtual HRESULT STDMETHODCALLTYPE get_ExecutablePath(
        BSTR *executablePath) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ExecutablePath(
        BSTR executablePath) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Arguments(
        BSTR *arguments) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Arguments(
        BSTR arguments) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Account(
        FsrmAccountType *account) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Account(
        FsrmAccountType account) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WorkingDirectory(
        BSTR *workingDirectory) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WorkingDirectory(
        BSTR workingDirectory) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MonitorCommand(
        VARIANT_BOOL *monitorCommand) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MonitorCommand(
        VARIANT_BOOL monitorCommand) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_KillTimeOut(
        LONG *minutes) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_KillTimeOut(
        LONG minutes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LogResult(
        VARIANT_BOOL *logResults) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_LogResult(
        VARIANT_BOOL logResults) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmActionCommand, 0x********, 0xe247, 0x4917, 0x9c,0x20, 0xf3,0xee,0x9c,0x7e,0xe7,0x83)
#endif
#else
typedef struct IFsrmActionCommandVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmActionCommand* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmActionCommand* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmActionCommand* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmActionCommand* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmActionCommand* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmActionCommand* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmActionCommand* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmActionCommand* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_ActionType)(
        IFsrmActionCommand* This,
        FsrmActionType *actionType);

    HRESULT (STDMETHODCALLTYPE *get_RunLimitInterval)(
        IFsrmActionCommand* This,
        LONG *minutes);

    HRESULT (STDMETHODCALLTYPE *put_RunLimitInterval)(
        IFsrmActionCommand* This,
        LONG minutes);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmActionCommand* This);

    /*** IFsrmActionCommand methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ExecutablePath)(
        IFsrmActionCommand* This,
        BSTR *executablePath);

    HRESULT (STDMETHODCALLTYPE *put_ExecutablePath)(
        IFsrmActionCommand* This,
        BSTR executablePath);

    HRESULT (STDMETHODCALLTYPE *get_Arguments)(
        IFsrmActionCommand* This,
        BSTR *arguments);

    HRESULT (STDMETHODCALLTYPE *put_Arguments)(
        IFsrmActionCommand* This,
        BSTR arguments);

    HRESULT (STDMETHODCALLTYPE *get_Account)(
        IFsrmActionCommand* This,
        FsrmAccountType *account);

    HRESULT (STDMETHODCALLTYPE *put_Account)(
        IFsrmActionCommand* This,
        FsrmAccountType account);

    HRESULT (STDMETHODCALLTYPE *get_WorkingDirectory)(
        IFsrmActionCommand* This,
        BSTR *workingDirectory);

    HRESULT (STDMETHODCALLTYPE *put_WorkingDirectory)(
        IFsrmActionCommand* This,
        BSTR workingDirectory);

    HRESULT (STDMETHODCALLTYPE *get_MonitorCommand)(
        IFsrmActionCommand* This,
        VARIANT_BOOL *monitorCommand);

    HRESULT (STDMETHODCALLTYPE *put_MonitorCommand)(
        IFsrmActionCommand* This,
        VARIANT_BOOL monitorCommand);

    HRESULT (STDMETHODCALLTYPE *get_KillTimeOut)(
        IFsrmActionCommand* This,
        LONG *minutes);

    HRESULT (STDMETHODCALLTYPE *put_KillTimeOut)(
        IFsrmActionCommand* This,
        LONG minutes);

    HRESULT (STDMETHODCALLTYPE *get_LogResult)(
        IFsrmActionCommand* This,
        VARIANT_BOOL *logResults);

    HRESULT (STDMETHODCALLTYPE *put_LogResult)(
        IFsrmActionCommand* This,
        VARIANT_BOOL logResults);

    END_INTERFACE
} IFsrmActionCommandVtbl;
interface IFsrmActionCommand {
    CONST_VTBL IFsrmActionCommandVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmActionCommand_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmActionCommand_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmActionCommand_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmActionCommand_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmActionCommand_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmActionCommand_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmActionCommand_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmAction methods ***/
#define IFsrmActionCommand_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmActionCommand_get_ActionType(This,actionType) (This)->lpVtbl->get_ActionType(This,actionType)
#define IFsrmActionCommand_get_RunLimitInterval(This,minutes) (This)->lpVtbl->get_RunLimitInterval(This,minutes)
#define IFsrmActionCommand_put_RunLimitInterval(This,minutes) (This)->lpVtbl->put_RunLimitInterval(This,minutes)
#define IFsrmActionCommand_Delete(This) (This)->lpVtbl->Delete(This)
/*** IFsrmActionCommand methods ***/
#define IFsrmActionCommand_get_ExecutablePath(This,executablePath) (This)->lpVtbl->get_ExecutablePath(This,executablePath)
#define IFsrmActionCommand_put_ExecutablePath(This,executablePath) (This)->lpVtbl->put_ExecutablePath(This,executablePath)
#define IFsrmActionCommand_get_Arguments(This,arguments) (This)->lpVtbl->get_Arguments(This,arguments)
#define IFsrmActionCommand_put_Arguments(This,arguments) (This)->lpVtbl->put_Arguments(This,arguments)
#define IFsrmActionCommand_get_Account(This,account) (This)->lpVtbl->get_Account(This,account)
#define IFsrmActionCommand_put_Account(This,account) (This)->lpVtbl->put_Account(This,account)
#define IFsrmActionCommand_get_WorkingDirectory(This,workingDirectory) (This)->lpVtbl->get_WorkingDirectory(This,workingDirectory)
#define IFsrmActionCommand_put_WorkingDirectory(This,workingDirectory) (This)->lpVtbl->put_WorkingDirectory(This,workingDirectory)
#define IFsrmActionCommand_get_MonitorCommand(This,monitorCommand) (This)->lpVtbl->get_MonitorCommand(This,monitorCommand)
#define IFsrmActionCommand_put_MonitorCommand(This,monitorCommand) (This)->lpVtbl->put_MonitorCommand(This,monitorCommand)
#define IFsrmActionCommand_get_KillTimeOut(This,minutes) (This)->lpVtbl->get_KillTimeOut(This,minutes)
#define IFsrmActionCommand_put_KillTimeOut(This,minutes) (This)->lpVtbl->put_KillTimeOut(This,minutes)
#define IFsrmActionCommand_get_LogResult(This,logResults) (This)->lpVtbl->get_LogResult(This,logResults)
#define IFsrmActionCommand_put_LogResult(This,logResults) (This)->lpVtbl->put_LogResult(This,logResults)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmActionCommand_QueryInterface(IFsrmActionCommand* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmActionCommand_AddRef(IFsrmActionCommand* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmActionCommand_Release(IFsrmActionCommand* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmActionCommand_GetTypeInfoCount(IFsrmActionCommand* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmActionCommand_GetTypeInfo(IFsrmActionCommand* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmActionCommand_GetIDsOfNames(IFsrmActionCommand* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmActionCommand_Invoke(IFsrmActionCommand* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmAction methods ***/
static FORCEINLINE HRESULT IFsrmActionCommand_get_Id(IFsrmActionCommand* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmActionCommand_get_ActionType(IFsrmActionCommand* This,FsrmActionType *actionType) {
    return This->lpVtbl->get_ActionType(This,actionType);
}
static FORCEINLINE HRESULT IFsrmActionCommand_get_RunLimitInterval(IFsrmActionCommand* This,LONG *minutes) {
    return This->lpVtbl->get_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionCommand_put_RunLimitInterval(IFsrmActionCommand* This,LONG minutes) {
    return This->lpVtbl->put_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionCommand_Delete(IFsrmActionCommand* This) {
    return This->lpVtbl->Delete(This);
}
/*** IFsrmActionCommand methods ***/
static FORCEINLINE HRESULT IFsrmActionCommand_get_ExecutablePath(IFsrmActionCommand* This,BSTR *executablePath) {
    return This->lpVtbl->get_ExecutablePath(This,executablePath);
}
static FORCEINLINE HRESULT IFsrmActionCommand_put_ExecutablePath(IFsrmActionCommand* This,BSTR executablePath) {
    return This->lpVtbl->put_ExecutablePath(This,executablePath);
}
static FORCEINLINE HRESULT IFsrmActionCommand_get_Arguments(IFsrmActionCommand* This,BSTR *arguments) {
    return This->lpVtbl->get_Arguments(This,arguments);
}
static FORCEINLINE HRESULT IFsrmActionCommand_put_Arguments(IFsrmActionCommand* This,BSTR arguments) {
    return This->lpVtbl->put_Arguments(This,arguments);
}
static FORCEINLINE HRESULT IFsrmActionCommand_get_Account(IFsrmActionCommand* This,FsrmAccountType *account) {
    return This->lpVtbl->get_Account(This,account);
}
static FORCEINLINE HRESULT IFsrmActionCommand_put_Account(IFsrmActionCommand* This,FsrmAccountType account) {
    return This->lpVtbl->put_Account(This,account);
}
static FORCEINLINE HRESULT IFsrmActionCommand_get_WorkingDirectory(IFsrmActionCommand* This,BSTR *workingDirectory) {
    return This->lpVtbl->get_WorkingDirectory(This,workingDirectory);
}
static FORCEINLINE HRESULT IFsrmActionCommand_put_WorkingDirectory(IFsrmActionCommand* This,BSTR workingDirectory) {
    return This->lpVtbl->put_WorkingDirectory(This,workingDirectory);
}
static FORCEINLINE HRESULT IFsrmActionCommand_get_MonitorCommand(IFsrmActionCommand* This,VARIANT_BOOL *monitorCommand) {
    return This->lpVtbl->get_MonitorCommand(This,monitorCommand);
}
static FORCEINLINE HRESULT IFsrmActionCommand_put_MonitorCommand(IFsrmActionCommand* This,VARIANT_BOOL monitorCommand) {
    return This->lpVtbl->put_MonitorCommand(This,monitorCommand);
}
static FORCEINLINE HRESULT IFsrmActionCommand_get_KillTimeOut(IFsrmActionCommand* This,LONG *minutes) {
    return This->lpVtbl->get_KillTimeOut(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionCommand_put_KillTimeOut(IFsrmActionCommand* This,LONG minutes) {
    return This->lpVtbl->put_KillTimeOut(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionCommand_get_LogResult(IFsrmActionCommand* This,VARIANT_BOOL *logResults) {
    return This->lpVtbl->get_LogResult(This,logResults);
}
static FORCEINLINE HRESULT IFsrmActionCommand_put_LogResult(IFsrmActionCommand* This,VARIANT_BOOL logResults) {
    return This->lpVtbl->put_LogResult(This,logResults);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmActionCommand_get_ExecutablePath_Proxy(
    IFsrmActionCommand* This,
    BSTR *executablePath);
void __RPC_STUB IFsrmActionCommand_get_ExecutablePath_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_put_ExecutablePath_Proxy(
    IFsrmActionCommand* This,
    BSTR executablePath);
void __RPC_STUB IFsrmActionCommand_put_ExecutablePath_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_get_Arguments_Proxy(
    IFsrmActionCommand* This,
    BSTR *arguments);
void __RPC_STUB IFsrmActionCommand_get_Arguments_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_put_Arguments_Proxy(
    IFsrmActionCommand* This,
    BSTR arguments);
void __RPC_STUB IFsrmActionCommand_put_Arguments_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_get_Account_Proxy(
    IFsrmActionCommand* This,
    FsrmAccountType *account);
void __RPC_STUB IFsrmActionCommand_get_Account_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_put_Account_Proxy(
    IFsrmActionCommand* This,
    FsrmAccountType account);
void __RPC_STUB IFsrmActionCommand_put_Account_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_get_WorkingDirectory_Proxy(
    IFsrmActionCommand* This,
    BSTR *workingDirectory);
void __RPC_STUB IFsrmActionCommand_get_WorkingDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_put_WorkingDirectory_Proxy(
    IFsrmActionCommand* This,
    BSTR workingDirectory);
void __RPC_STUB IFsrmActionCommand_put_WorkingDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_get_MonitorCommand_Proxy(
    IFsrmActionCommand* This,
    VARIANT_BOOL *monitorCommand);
void __RPC_STUB IFsrmActionCommand_get_MonitorCommand_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_put_MonitorCommand_Proxy(
    IFsrmActionCommand* This,
    VARIANT_BOOL monitorCommand);
void __RPC_STUB IFsrmActionCommand_put_MonitorCommand_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_get_KillTimeOut_Proxy(
    IFsrmActionCommand* This,
    LONG *minutes);
void __RPC_STUB IFsrmActionCommand_get_KillTimeOut_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_put_KillTimeOut_Proxy(
    IFsrmActionCommand* This,
    LONG minutes);
void __RPC_STUB IFsrmActionCommand_put_KillTimeOut_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_get_LogResult_Proxy(
    IFsrmActionCommand* This,
    VARIANT_BOOL *logResults);
void __RPC_STUB IFsrmActionCommand_get_LogResult_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionCommand_put_LogResult_Proxy(
    IFsrmActionCommand* This,
    VARIANT_BOOL logResults);
void __RPC_STUB IFsrmActionCommand_put_LogResult_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmActionCommand_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmActionEventLog interface
 */
#ifndef __IFsrmActionEventLog_INTERFACE_DEFINED__
#define __IFsrmActionEventLog_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmActionEventLog, 0x4c8f96c3, 0x5d94, 0x4f37, 0xa4,0xf4, 0xf5,0x6a,0xb4,0x63,0x54,0x6f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4c8f96c3-5d94-4f37-a4f4-f56ab463546f")
IFsrmActionEventLog : public IFsrmAction
{
    virtual HRESULT STDMETHODCALLTYPE get_EventType(
        FsrmEventType *eventType) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EventType(
        FsrmEventType eventType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MessageText(
        BSTR *messageText) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MessageText(
        BSTR messageText) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmActionEventLog, 0x4c8f96c3, 0x5d94, 0x4f37, 0xa4,0xf4, 0xf5,0x6a,0xb4,0x63,0x54,0x6f)
#endif
#else
typedef struct IFsrmActionEventLogVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmActionEventLog* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmActionEventLog* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmActionEventLog* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmActionEventLog* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmActionEventLog* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmActionEventLog* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmActionEventLog* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmActionEventLog* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_ActionType)(
        IFsrmActionEventLog* This,
        FsrmActionType *actionType);

    HRESULT (STDMETHODCALLTYPE *get_RunLimitInterval)(
        IFsrmActionEventLog* This,
        LONG *minutes);

    HRESULT (STDMETHODCALLTYPE *put_RunLimitInterval)(
        IFsrmActionEventLog* This,
        LONG minutes);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmActionEventLog* This);

    /*** IFsrmActionEventLog methods ***/
    HRESULT (STDMETHODCALLTYPE *get_EventType)(
        IFsrmActionEventLog* This,
        FsrmEventType *eventType);

    HRESULT (STDMETHODCALLTYPE *put_EventType)(
        IFsrmActionEventLog* This,
        FsrmEventType eventType);

    HRESULT (STDMETHODCALLTYPE *get_MessageText)(
        IFsrmActionEventLog* This,
        BSTR *messageText);

    HRESULT (STDMETHODCALLTYPE *put_MessageText)(
        IFsrmActionEventLog* This,
        BSTR messageText);

    END_INTERFACE
} IFsrmActionEventLogVtbl;
interface IFsrmActionEventLog {
    CONST_VTBL IFsrmActionEventLogVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmActionEventLog_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmActionEventLog_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmActionEventLog_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmActionEventLog_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmActionEventLog_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmActionEventLog_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmActionEventLog_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmAction methods ***/
#define IFsrmActionEventLog_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmActionEventLog_get_ActionType(This,actionType) (This)->lpVtbl->get_ActionType(This,actionType)
#define IFsrmActionEventLog_get_RunLimitInterval(This,minutes) (This)->lpVtbl->get_RunLimitInterval(This,minutes)
#define IFsrmActionEventLog_put_RunLimitInterval(This,minutes) (This)->lpVtbl->put_RunLimitInterval(This,minutes)
#define IFsrmActionEventLog_Delete(This) (This)->lpVtbl->Delete(This)
/*** IFsrmActionEventLog methods ***/
#define IFsrmActionEventLog_get_EventType(This,eventType) (This)->lpVtbl->get_EventType(This,eventType)
#define IFsrmActionEventLog_put_EventType(This,eventType) (This)->lpVtbl->put_EventType(This,eventType)
#define IFsrmActionEventLog_get_MessageText(This,messageText) (This)->lpVtbl->get_MessageText(This,messageText)
#define IFsrmActionEventLog_put_MessageText(This,messageText) (This)->lpVtbl->put_MessageText(This,messageText)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmActionEventLog_QueryInterface(IFsrmActionEventLog* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmActionEventLog_AddRef(IFsrmActionEventLog* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmActionEventLog_Release(IFsrmActionEventLog* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmActionEventLog_GetTypeInfoCount(IFsrmActionEventLog* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_GetTypeInfo(IFsrmActionEventLog* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_GetIDsOfNames(IFsrmActionEventLog* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_Invoke(IFsrmActionEventLog* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmAction methods ***/
static FORCEINLINE HRESULT IFsrmActionEventLog_get_Id(IFsrmActionEventLog* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_get_ActionType(IFsrmActionEventLog* This,FsrmActionType *actionType) {
    return This->lpVtbl->get_ActionType(This,actionType);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_get_RunLimitInterval(IFsrmActionEventLog* This,LONG *minutes) {
    return This->lpVtbl->get_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_put_RunLimitInterval(IFsrmActionEventLog* This,LONG minutes) {
    return This->lpVtbl->put_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_Delete(IFsrmActionEventLog* This) {
    return This->lpVtbl->Delete(This);
}
/*** IFsrmActionEventLog methods ***/
static FORCEINLINE HRESULT IFsrmActionEventLog_get_EventType(IFsrmActionEventLog* This,FsrmEventType *eventType) {
    return This->lpVtbl->get_EventType(This,eventType);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_put_EventType(IFsrmActionEventLog* This,FsrmEventType eventType) {
    return This->lpVtbl->put_EventType(This,eventType);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_get_MessageText(IFsrmActionEventLog* This,BSTR *messageText) {
    return This->lpVtbl->get_MessageText(This,messageText);
}
static FORCEINLINE HRESULT IFsrmActionEventLog_put_MessageText(IFsrmActionEventLog* This,BSTR messageText) {
    return This->lpVtbl->put_MessageText(This,messageText);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmActionEventLog_get_EventType_Proxy(
    IFsrmActionEventLog* This,
    FsrmEventType *eventType);
void __RPC_STUB IFsrmActionEventLog_get_EventType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEventLog_put_EventType_Proxy(
    IFsrmActionEventLog* This,
    FsrmEventType eventType);
void __RPC_STUB IFsrmActionEventLog_put_EventType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEventLog_get_MessageText_Proxy(
    IFsrmActionEventLog* This,
    BSTR *messageText);
void __RPC_STUB IFsrmActionEventLog_get_MessageText_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEventLog_put_MessageText_Proxy(
    IFsrmActionEventLog* This,
    BSTR messageText);
void __RPC_STUB IFsrmActionEventLog_put_MessageText_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmActionEventLog_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmActionReport interface
 */
#ifndef __IFsrmActionReport_INTERFACE_DEFINED__
#define __IFsrmActionReport_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmActionReport, 0x2dbe63c4, 0xb340, 0x48a0, 0xa5,0xb0, 0x15,0x8e,0x07,0xfc,0x56,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2dbe63c4-b340-48a0-a5b0-158e07fc567e")
IFsrmActionReport : public IFsrmAction
{
    virtual HRESULT STDMETHODCALLTYPE get_ReportTypes(
        SAFEARRAY **reportTypes) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ReportTypes(
        SAFEARRAY *reportTypes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailTo(
        BSTR *mailTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailTo(
        BSTR mailTo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmActionReport, 0x2dbe63c4, 0xb340, 0x48a0, 0xa5,0xb0, 0x15,0x8e,0x07,0xfc,0x56,0x7e)
#endif
#else
typedef struct IFsrmActionReportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmActionReport* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmActionReport* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmActionReport* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmActionReport* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmActionReport* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmActionReport* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmActionReport* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmActionReport* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_ActionType)(
        IFsrmActionReport* This,
        FsrmActionType *actionType);

    HRESULT (STDMETHODCALLTYPE *get_RunLimitInterval)(
        IFsrmActionReport* This,
        LONG *minutes);

    HRESULT (STDMETHODCALLTYPE *put_RunLimitInterval)(
        IFsrmActionReport* This,
        LONG minutes);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmActionReport* This);

    /*** IFsrmActionReport methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ReportTypes)(
        IFsrmActionReport* This,
        SAFEARRAY **reportTypes);

    HRESULT (STDMETHODCALLTYPE *put_ReportTypes)(
        IFsrmActionReport* This,
        SAFEARRAY *reportTypes);

    HRESULT (STDMETHODCALLTYPE *get_MailTo)(
        IFsrmActionReport* This,
        BSTR *mailTo);

    HRESULT (STDMETHODCALLTYPE *put_MailTo)(
        IFsrmActionReport* This,
        BSTR mailTo);

    END_INTERFACE
} IFsrmActionReportVtbl;
interface IFsrmActionReport {
    CONST_VTBL IFsrmActionReportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmActionReport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmActionReport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmActionReport_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmActionReport_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmActionReport_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmActionReport_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmActionReport_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmAction methods ***/
#define IFsrmActionReport_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmActionReport_get_ActionType(This,actionType) (This)->lpVtbl->get_ActionType(This,actionType)
#define IFsrmActionReport_get_RunLimitInterval(This,minutes) (This)->lpVtbl->get_RunLimitInterval(This,minutes)
#define IFsrmActionReport_put_RunLimitInterval(This,minutes) (This)->lpVtbl->put_RunLimitInterval(This,minutes)
#define IFsrmActionReport_Delete(This) (This)->lpVtbl->Delete(This)
/*** IFsrmActionReport methods ***/
#define IFsrmActionReport_get_ReportTypes(This,reportTypes) (This)->lpVtbl->get_ReportTypes(This,reportTypes)
#define IFsrmActionReport_put_ReportTypes(This,reportTypes) (This)->lpVtbl->put_ReportTypes(This,reportTypes)
#define IFsrmActionReport_get_MailTo(This,mailTo) (This)->lpVtbl->get_MailTo(This,mailTo)
#define IFsrmActionReport_put_MailTo(This,mailTo) (This)->lpVtbl->put_MailTo(This,mailTo)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmActionReport_QueryInterface(IFsrmActionReport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmActionReport_AddRef(IFsrmActionReport* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmActionReport_Release(IFsrmActionReport* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmActionReport_GetTypeInfoCount(IFsrmActionReport* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmActionReport_GetTypeInfo(IFsrmActionReport* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmActionReport_GetIDsOfNames(IFsrmActionReport* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmActionReport_Invoke(IFsrmActionReport* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmAction methods ***/
static FORCEINLINE HRESULT IFsrmActionReport_get_Id(IFsrmActionReport* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmActionReport_get_ActionType(IFsrmActionReport* This,FsrmActionType *actionType) {
    return This->lpVtbl->get_ActionType(This,actionType);
}
static FORCEINLINE HRESULT IFsrmActionReport_get_RunLimitInterval(IFsrmActionReport* This,LONG *minutes) {
    return This->lpVtbl->get_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionReport_put_RunLimitInterval(IFsrmActionReport* This,LONG minutes) {
    return This->lpVtbl->put_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionReport_Delete(IFsrmActionReport* This) {
    return This->lpVtbl->Delete(This);
}
/*** IFsrmActionReport methods ***/
static FORCEINLINE HRESULT IFsrmActionReport_get_ReportTypes(IFsrmActionReport* This,SAFEARRAY **reportTypes) {
    return This->lpVtbl->get_ReportTypes(This,reportTypes);
}
static FORCEINLINE HRESULT IFsrmActionReport_put_ReportTypes(IFsrmActionReport* This,SAFEARRAY *reportTypes) {
    return This->lpVtbl->put_ReportTypes(This,reportTypes);
}
static FORCEINLINE HRESULT IFsrmActionReport_get_MailTo(IFsrmActionReport* This,BSTR *mailTo) {
    return This->lpVtbl->get_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmActionReport_put_MailTo(IFsrmActionReport* This,BSTR mailTo) {
    return This->lpVtbl->put_MailTo(This,mailTo);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmActionReport_get_ReportTypes_Proxy(
    IFsrmActionReport* This,
    SAFEARRAY **reportTypes);
void __RPC_STUB IFsrmActionReport_get_ReportTypes_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionReport_put_ReportTypes_Proxy(
    IFsrmActionReport* This,
    SAFEARRAY *reportTypes);
void __RPC_STUB IFsrmActionReport_put_ReportTypes_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionReport_get_MailTo_Proxy(
    IFsrmActionReport* This,
    BSTR *mailTo);
void __RPC_STUB IFsrmActionReport_get_MailTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionReport_put_MailTo_Proxy(
    IFsrmActionReport* This,
    BSTR mailTo);
void __RPC_STUB IFsrmActionReport_put_MailTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmActionReport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmCollection interface
 */
#ifndef __IFsrmCollection_INTERFACE_DEFINED__
#define __IFsrmCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmCollection, 0xf76fbf3b, 0x8ddd, 0x4b42, 0xb0,0x5a, 0xcb,0x1c,0x3f,0xf1,0xfe,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f76fbf3b-8ddd-4b42-b05a-cb1c3ff1fee8")
IFsrmCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **unknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        VARIANT *item) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_State(
        FsrmCollectionState *state) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitForCompletion(
        LONG waitSeconds,
        VARIANT_BOOL *completed) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetById(
        FSRM_OBJECT_ID id,
        VARIANT *entry) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmCollection, 0xf76fbf3b, 0x8ddd, 0x4b42, 0xb0,0x5a, 0xcb,0x1c,0x3f,0xf1,0xfe,0xe8)
#endif
#else
typedef struct IFsrmCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmCollection* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmCollection* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmCollection* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmCollection* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmCollection* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmCollection* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmCollection* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IFsrmCollection* This,
        IUnknown **unknown);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IFsrmCollection* This,
        LONG index,
        VARIANT *item);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IFsrmCollection* This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        IFsrmCollection* This,
        FsrmCollectionState *state);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IFsrmCollection* This);

    HRESULT (STDMETHODCALLTYPE *WaitForCompletion)(
        IFsrmCollection* This,
        LONG waitSeconds,
        VARIANT_BOOL *completed);

    HRESULT (STDMETHODCALLTYPE *GetById)(
        IFsrmCollection* This,
        FSRM_OBJECT_ID id,
        VARIANT *entry);

    END_INTERFACE
} IFsrmCollectionVtbl;
interface IFsrmCollection {
    CONST_VTBL IFsrmCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmCollection methods ***/
#define IFsrmCollection_get__NewEnum(This,unknown) (This)->lpVtbl->get__NewEnum(This,unknown)
#define IFsrmCollection_get_Item(This,index,item) (This)->lpVtbl->get_Item(This,index,item)
#define IFsrmCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define IFsrmCollection_get_State(This,state) (This)->lpVtbl->get_State(This,state)
#define IFsrmCollection_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IFsrmCollection_WaitForCompletion(This,waitSeconds,completed) (This)->lpVtbl->WaitForCompletion(This,waitSeconds,completed)
#define IFsrmCollection_GetById(This,id,entry) (This)->lpVtbl->GetById(This,id,entry)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmCollection_QueryInterface(IFsrmCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmCollection_AddRef(IFsrmCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmCollection_Release(IFsrmCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmCollection_GetTypeInfoCount(IFsrmCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmCollection_GetTypeInfo(IFsrmCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmCollection_GetIDsOfNames(IFsrmCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmCollection_Invoke(IFsrmCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmCollection methods ***/
static FORCEINLINE HRESULT IFsrmCollection_get__NewEnum(IFsrmCollection* This,IUnknown **unknown) {
    return This->lpVtbl->get__NewEnum(This,unknown);
}
static FORCEINLINE HRESULT IFsrmCollection_get_Item(IFsrmCollection* This,LONG index,VARIANT *item) {
    return This->lpVtbl->get_Item(This,index,item);
}
static FORCEINLINE HRESULT IFsrmCollection_get_Count(IFsrmCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static FORCEINLINE HRESULT IFsrmCollection_get_State(IFsrmCollection* This,FsrmCollectionState *state) {
    return This->lpVtbl->get_State(This,state);
}
static FORCEINLINE HRESULT IFsrmCollection_Cancel(IFsrmCollection* This) {
    return This->lpVtbl->Cancel(This);
}
static FORCEINLINE HRESULT IFsrmCollection_WaitForCompletion(IFsrmCollection* This,LONG waitSeconds,VARIANT_BOOL *completed) {
    return This->lpVtbl->WaitForCompletion(This,waitSeconds,completed);
}
static FORCEINLINE HRESULT IFsrmCollection_GetById(IFsrmCollection* This,FSRM_OBJECT_ID id,VARIANT *entry) {
    return This->lpVtbl->GetById(This,id,entry);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmCollection_get__NewEnum_Proxy(
    IFsrmCollection* This,
    IUnknown **unknown);
void __RPC_STUB IFsrmCollection_get__NewEnum_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmCollection_get_Item_Proxy(
    IFsrmCollection* This,
    LONG index,
    VARIANT *item);
void __RPC_STUB IFsrmCollection_get_Item_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmCollection_get_Count_Proxy(
    IFsrmCollection* This,
    LONG *count);
void __RPC_STUB IFsrmCollection_get_Count_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmCollection_get_State_Proxy(
    IFsrmCollection* This,
    FsrmCollectionState *state);
void __RPC_STUB IFsrmCollection_get_State_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmCollection_Cancel_Proxy(
    IFsrmCollection* This);
void __RPC_STUB IFsrmCollection_Cancel_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmCollection_WaitForCompletion_Proxy(
    IFsrmCollection* This,
    LONG waitSeconds,
    VARIANT_BOOL *completed);
void __RPC_STUB IFsrmCollection_WaitForCompletion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmCollection_GetById_Proxy(
    IFsrmCollection* This,
    FSRM_OBJECT_ID id,
    VARIANT *entry);
void __RPC_STUB IFsrmCollection_GetById_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmDerivedObjectsResult interface
 */
#ifndef __IFsrmDerivedObjectsResult_INTERFACE_DEFINED__
#define __IFsrmDerivedObjectsResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmDerivedObjectsResult, 0x39322a2d, 0x38ee, 0x4d0d, 0x80,0x95, 0x42,0x1a,0x80,0x84,0x9a,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("39322a2d-38ee-4d0d-8095-421a80849a82")
IFsrmDerivedObjectsResult : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_DerivedObjects(
        IFsrmCollection **derivedObjects) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Results(
        IFsrmCollection **results) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmDerivedObjectsResult, 0x39322a2d, 0x38ee, 0x4d0d, 0x80,0x95, 0x42,0x1a,0x80,0x84,0x9a,0x82)
#endif
#else
typedef struct IFsrmDerivedObjectsResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmDerivedObjectsResult* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmDerivedObjectsResult* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmDerivedObjectsResult* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmDerivedObjectsResult* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmDerivedObjectsResult* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmDerivedObjectsResult* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmDerivedObjectsResult* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmDerivedObjectsResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DerivedObjects)(
        IFsrmDerivedObjectsResult* This,
        IFsrmCollection **derivedObjects);

    HRESULT (STDMETHODCALLTYPE *get_Results)(
        IFsrmDerivedObjectsResult* This,
        IFsrmCollection **results);

    END_INTERFACE
} IFsrmDerivedObjectsResultVtbl;
interface IFsrmDerivedObjectsResult {
    CONST_VTBL IFsrmDerivedObjectsResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmDerivedObjectsResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmDerivedObjectsResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmDerivedObjectsResult_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmDerivedObjectsResult_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmDerivedObjectsResult_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmDerivedObjectsResult_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmDerivedObjectsResult_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmDerivedObjectsResult methods ***/
#define IFsrmDerivedObjectsResult_get_DerivedObjects(This,derivedObjects) (This)->lpVtbl->get_DerivedObjects(This,derivedObjects)
#define IFsrmDerivedObjectsResult_get_Results(This,results) (This)->lpVtbl->get_Results(This,results)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmDerivedObjectsResult_QueryInterface(IFsrmDerivedObjectsResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmDerivedObjectsResult_AddRef(IFsrmDerivedObjectsResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmDerivedObjectsResult_Release(IFsrmDerivedObjectsResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmDerivedObjectsResult_GetTypeInfoCount(IFsrmDerivedObjectsResult* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmDerivedObjectsResult_GetTypeInfo(IFsrmDerivedObjectsResult* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmDerivedObjectsResult_GetIDsOfNames(IFsrmDerivedObjectsResult* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmDerivedObjectsResult_Invoke(IFsrmDerivedObjectsResult* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmDerivedObjectsResult methods ***/
static FORCEINLINE HRESULT IFsrmDerivedObjectsResult_get_DerivedObjects(IFsrmDerivedObjectsResult* This,IFsrmCollection **derivedObjects) {
    return This->lpVtbl->get_DerivedObjects(This,derivedObjects);
}
static FORCEINLINE HRESULT IFsrmDerivedObjectsResult_get_Results(IFsrmDerivedObjectsResult* This,IFsrmCollection **results) {
    return This->lpVtbl->get_Results(This,results);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmDerivedObjectsResult_get_DerivedObjects_Proxy(
    IFsrmDerivedObjectsResult* This,
    IFsrmCollection **derivedObjects);
void __RPC_STUB IFsrmDerivedObjectsResult_get_DerivedObjects_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmDerivedObjectsResult_get_Results_Proxy(
    IFsrmDerivedObjectsResult* This,
    IFsrmCollection **results);
void __RPC_STUB IFsrmDerivedObjectsResult_get_Results_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmDerivedObjectsResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmExportImport interface
 */
#ifndef __IFsrmExportImport_INTERFACE_DEFINED__
#define __IFsrmExportImport_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmExportImport, 0xefcb0ab1, 0x16c4, 0x4a79, 0x81,0x2c, 0x72,0x56,0x14,0xc3,0x30,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("efcb0ab1-16c4-4a79-812c-725614c3306b")
IFsrmExportImport : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE ExportFileGroups(
        BSTR filePath,
        VARIANT *fileGroupNamesSafeArray = 0,
        BSTR remoteHost = L"") = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportFileGroups(
        BSTR filePath,
        VARIANT *fileGroupNamesSafeArray,
        BSTR remoteHost,
        IFsrmCommittableCollection **fileGroups) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExportFileScreenTemplates(
        BSTR filePath,
        VARIANT *templateNamesSafeArray = 0,
        BSTR remoteHost = L"") = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportFileScreenTemplates(
        BSTR filePath,
        VARIANT *templateNamesSafeArray,
        BSTR remoteHost,
        IFsrmCommittableCollection **templates) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExportQuotaTemplates(
        BSTR filePath,
        VARIANT *templateNamesSafeArray = 0,
        BSTR remoteHost = L"") = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportQuotaTemplates(
        BSTR filePath,
        VARIANT *templateNamesSafeArray,
        BSTR remoteHost,
        IFsrmCommittableCollection **templates) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmExportImport, 0xefcb0ab1, 0x16c4, 0x4a79, 0x81,0x2c, 0x72,0x56,0x14,0xc3,0x30,0x6b)
#endif
#else
typedef struct IFsrmExportImportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmExportImport* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmExportImport* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmExportImport* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmExportImport* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmExportImport* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmExportImport* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmExportImport* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmExportImport methods ***/
    HRESULT (STDMETHODCALLTYPE *ExportFileGroups)(
        IFsrmExportImport* This,
        BSTR filePath,
        VARIANT *fileGroupNamesSafeArray,
        BSTR remoteHost);

    HRESULT (STDMETHODCALLTYPE *ImportFileGroups)(
        IFsrmExportImport* This,
        BSTR filePath,
        VARIANT *fileGroupNamesSafeArray,
        BSTR remoteHost,
        IFsrmCommittableCollection **fileGroups);

    HRESULT (STDMETHODCALLTYPE *ExportFileScreenTemplates)(
        IFsrmExportImport* This,
        BSTR filePath,
        VARIANT *templateNamesSafeArray,
        BSTR remoteHost);

    HRESULT (STDMETHODCALLTYPE *ImportFileScreenTemplates)(
        IFsrmExportImport* This,
        BSTR filePath,
        VARIANT *templateNamesSafeArray,
        BSTR remoteHost,
        IFsrmCommittableCollection **templates);

    HRESULT (STDMETHODCALLTYPE *ExportQuotaTemplates)(
        IFsrmExportImport* This,
        BSTR filePath,
        VARIANT *templateNamesSafeArray,
        BSTR remoteHost);

    HRESULT (STDMETHODCALLTYPE *ImportQuotaTemplates)(
        IFsrmExportImport* This,
        BSTR filePath,
        VARIANT *templateNamesSafeArray,
        BSTR remoteHost,
        IFsrmCommittableCollection **templates);

    END_INTERFACE
} IFsrmExportImportVtbl;
interface IFsrmExportImport {
    CONST_VTBL IFsrmExportImportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmExportImport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmExportImport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmExportImport_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmExportImport_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmExportImport_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmExportImport_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmExportImport_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmExportImport methods ***/
#define IFsrmExportImport_ExportFileGroups(This,filePath,fileGroupNamesSafeArray,remoteHost) (This)->lpVtbl->ExportFileGroups(This,filePath,fileGroupNamesSafeArray,remoteHost)
#define IFsrmExportImport_ImportFileGroups(This,filePath,fileGroupNamesSafeArray,remoteHost,fileGroups) (This)->lpVtbl->ImportFileGroups(This,filePath,fileGroupNamesSafeArray,remoteHost,fileGroups)
#define IFsrmExportImport_ExportFileScreenTemplates(This,filePath,templateNamesSafeArray,remoteHost) (This)->lpVtbl->ExportFileScreenTemplates(This,filePath,templateNamesSafeArray,remoteHost)
#define IFsrmExportImport_ImportFileScreenTemplates(This,filePath,templateNamesSafeArray,remoteHost,templates) (This)->lpVtbl->ImportFileScreenTemplates(This,filePath,templateNamesSafeArray,remoteHost,templates)
#define IFsrmExportImport_ExportQuotaTemplates(This,filePath,templateNamesSafeArray,remoteHost) (This)->lpVtbl->ExportQuotaTemplates(This,filePath,templateNamesSafeArray,remoteHost)
#define IFsrmExportImport_ImportQuotaTemplates(This,filePath,templateNamesSafeArray,remoteHost,templates) (This)->lpVtbl->ImportQuotaTemplates(This,filePath,templateNamesSafeArray,remoteHost,templates)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmExportImport_QueryInterface(IFsrmExportImport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmExportImport_AddRef(IFsrmExportImport* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmExportImport_Release(IFsrmExportImport* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmExportImport_GetTypeInfoCount(IFsrmExportImport* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmExportImport_GetTypeInfo(IFsrmExportImport* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmExportImport_GetIDsOfNames(IFsrmExportImport* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmExportImport_Invoke(IFsrmExportImport* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmExportImport methods ***/
static FORCEINLINE HRESULT IFsrmExportImport_ExportFileGroups(IFsrmExportImport* This,BSTR filePath,VARIANT *fileGroupNamesSafeArray,BSTR remoteHost) {
    return This->lpVtbl->ExportFileGroups(This,filePath,fileGroupNamesSafeArray,remoteHost);
}
static FORCEINLINE HRESULT IFsrmExportImport_ImportFileGroups(IFsrmExportImport* This,BSTR filePath,VARIANT *fileGroupNamesSafeArray,BSTR remoteHost,IFsrmCommittableCollection **fileGroups) {
    return This->lpVtbl->ImportFileGroups(This,filePath,fileGroupNamesSafeArray,remoteHost,fileGroups);
}
static FORCEINLINE HRESULT IFsrmExportImport_ExportFileScreenTemplates(IFsrmExportImport* This,BSTR filePath,VARIANT *templateNamesSafeArray,BSTR remoteHost) {
    return This->lpVtbl->ExportFileScreenTemplates(This,filePath,templateNamesSafeArray,remoteHost);
}
static FORCEINLINE HRESULT IFsrmExportImport_ImportFileScreenTemplates(IFsrmExportImport* This,BSTR filePath,VARIANT *templateNamesSafeArray,BSTR remoteHost,IFsrmCommittableCollection **templates) {
    return This->lpVtbl->ImportFileScreenTemplates(This,filePath,templateNamesSafeArray,remoteHost,templates);
}
static FORCEINLINE HRESULT IFsrmExportImport_ExportQuotaTemplates(IFsrmExportImport* This,BSTR filePath,VARIANT *templateNamesSafeArray,BSTR remoteHost) {
    return This->lpVtbl->ExportQuotaTemplates(This,filePath,templateNamesSafeArray,remoteHost);
}
static FORCEINLINE HRESULT IFsrmExportImport_ImportQuotaTemplates(IFsrmExportImport* This,BSTR filePath,VARIANT *templateNamesSafeArray,BSTR remoteHost,IFsrmCommittableCollection **templates) {
    return This->lpVtbl->ImportQuotaTemplates(This,filePath,templateNamesSafeArray,remoteHost,templates);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmExportImport_ExportFileGroups_Proxy(
    IFsrmExportImport* This,
    BSTR filePath,
    VARIANT *fileGroupNamesSafeArray,
    BSTR remoteHost);
void __RPC_STUB IFsrmExportImport_ExportFileGroups_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmExportImport_ImportFileGroups_Proxy(
    IFsrmExportImport* This,
    BSTR filePath,
    VARIANT *fileGroupNamesSafeArray,
    BSTR remoteHost,
    IFsrmCommittableCollection **fileGroups);
void __RPC_STUB IFsrmExportImport_ImportFileGroups_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmExportImport_ExportFileScreenTemplates_Proxy(
    IFsrmExportImport* This,
    BSTR filePath,
    VARIANT *templateNamesSafeArray,
    BSTR remoteHost);
void __RPC_STUB IFsrmExportImport_ExportFileScreenTemplates_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmExportImport_ImportFileScreenTemplates_Proxy(
    IFsrmExportImport* This,
    BSTR filePath,
    VARIANT *templateNamesSafeArray,
    BSTR remoteHost,
    IFsrmCommittableCollection **templates);
void __RPC_STUB IFsrmExportImport_ImportFileScreenTemplates_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmExportImport_ExportQuotaTemplates_Proxy(
    IFsrmExportImport* This,
    BSTR filePath,
    VARIANT *templateNamesSafeArray,
    BSTR remoteHost);
void __RPC_STUB IFsrmExportImport_ExportQuotaTemplates_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmExportImport_ImportQuotaTemplates_Proxy(
    IFsrmExportImport* This,
    BSTR filePath,
    VARIANT *templateNamesSafeArray,
    BSTR remoteHost,
    IFsrmCommittableCollection **templates);
void __RPC_STUB IFsrmExportImport_ImportQuotaTemplates_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmExportImport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmObject interface
 */
#ifndef __IFsrmObject_INTERFACE_DEFINED__
#define __IFsrmObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmObject, 0x22bcef93, 0x4a3f, 0x4183, 0x89,0xf9, 0x2f,0x8b,0x8a,0x62,0x8a,0xee);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("22bcef93-4a3f-4183-89f9-2f8b8a628aee")
IFsrmObject : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Id(
        FSRM_OBJECT_ID *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *description) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Description(
        BSTR description) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Commit(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmObject, 0x22bcef93, 0x4a3f, 0x4183, 0x89,0xf9, 0x2f,0x8b,0x8a,0x62,0x8a,0xee)
#endif
#else
typedef struct IFsrmObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmObject* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmObject* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmObject* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmObject* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmObject* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmObject* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmObject* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmObject* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmObject* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmObject* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmObject* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmObject* This);

    END_INTERFACE
} IFsrmObjectVtbl;
interface IFsrmObject {
    CONST_VTBL IFsrmObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmObject_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmObject_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmObject_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmObject_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmObject_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmObject_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmObject_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmObject_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmObject_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmObject_Commit(This) (This)->lpVtbl->Commit(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmObject_QueryInterface(IFsrmObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmObject_AddRef(IFsrmObject* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmObject_Release(IFsrmObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmObject_GetTypeInfoCount(IFsrmObject* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmObject_GetTypeInfo(IFsrmObject* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmObject_GetIDsOfNames(IFsrmObject* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmObject_Invoke(IFsrmObject* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmObject_get_Id(IFsrmObject* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmObject_get_Description(IFsrmObject* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmObject_put_Description(IFsrmObject* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmObject_Delete(IFsrmObject* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmObject_Commit(IFsrmObject* This) {
    return This->lpVtbl->Commit(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmObject_get_Id_Proxy(
    IFsrmObject* This,
    FSRM_OBJECT_ID *id);
void __RPC_STUB IFsrmObject_get_Id_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmObject_get_Description_Proxy(
    IFsrmObject* This,
    BSTR *description);
void __RPC_STUB IFsrmObject_get_Description_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmObject_put_Description_Proxy(
    IFsrmObject* This,
    BSTR description);
void __RPC_STUB IFsrmObject_put_Description_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmObject_Delete_Proxy(
    IFsrmObject* This);
void __RPC_STUB IFsrmObject_Delete_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmObject_Commit_Proxy(
    IFsrmObject* This);
void __RPC_STUB IFsrmObject_Commit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmPathMapper interface
 */
#ifndef __IFsrmPathMapper_INTERFACE_DEFINED__
#define __IFsrmPathMapper_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmPathMapper, 0x6f4dbfff, 0x6920, 0x4821, 0xa6,0xc3, 0xb7,0xe9,0x4c,0x1f,0xd6,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6f4dbfff-6920-4821-a6c3-b7e94c1fd60c")
IFsrmPathMapper : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetSharePathsForLocalPath(
        BSTR localPath,
        SAFEARRAY **sharePaths) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmPathMapper, 0x6f4dbfff, 0x6920, 0x4821, 0xa6,0xc3, 0xb7,0xe9,0x4c,0x1f,0xd6,0x0c)
#endif
#else
typedef struct IFsrmPathMapperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmPathMapper* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmPathMapper* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmPathMapper* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmPathMapper* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmPathMapper* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmPathMapper* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmPathMapper* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmPathMapper methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSharePathsForLocalPath)(
        IFsrmPathMapper* This,
        BSTR localPath,
        SAFEARRAY **sharePaths);

    END_INTERFACE
} IFsrmPathMapperVtbl;
interface IFsrmPathMapper {
    CONST_VTBL IFsrmPathMapperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmPathMapper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmPathMapper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmPathMapper_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmPathMapper_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmPathMapper_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmPathMapper_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmPathMapper_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmPathMapper methods ***/
#define IFsrmPathMapper_GetSharePathsForLocalPath(This,localPath,sharePaths) (This)->lpVtbl->GetSharePathsForLocalPath(This,localPath,sharePaths)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmPathMapper_QueryInterface(IFsrmPathMapper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmPathMapper_AddRef(IFsrmPathMapper* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmPathMapper_Release(IFsrmPathMapper* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmPathMapper_GetTypeInfoCount(IFsrmPathMapper* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmPathMapper_GetTypeInfo(IFsrmPathMapper* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmPathMapper_GetIDsOfNames(IFsrmPathMapper* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmPathMapper_Invoke(IFsrmPathMapper* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmPathMapper methods ***/
static FORCEINLINE HRESULT IFsrmPathMapper_GetSharePathsForLocalPath(IFsrmPathMapper* This,BSTR localPath,SAFEARRAY **sharePaths) {
    return This->lpVtbl->GetSharePathsForLocalPath(This,localPath,sharePaths);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmPathMapper_GetSharePathsForLocalPath_Proxy(
    IFsrmPathMapper* This,
    BSTR localPath,
    SAFEARRAY **sharePaths);
void __RPC_STUB IFsrmPathMapper_GetSharePathsForLocalPath_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmPathMapper_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmSetting interface
 */
#ifndef __IFsrmSetting_INTERFACE_DEFINED__
#define __IFsrmSetting_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmSetting, 0xf411d4fd, 0x14be, 0x4260, 0x8c,0x40, 0x03,0xb7,0xc9,0x5e,0x60,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f411d4fd-14be-4260-8c40-03b7c95e608a")
IFsrmSetting : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_SmtpServer(
        BSTR *smtpServer) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SmtpServer(
        BSTR smtpServer) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailFrom(
        BSTR *mailFrom) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailFrom(
        BSTR mailFrom) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AdminEmail(
        BSTR *adminEmail) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AdminEmail(
        BSTR adminEmail) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DisableCommandLine(
        VARIANT_BOOL *disableCommandLine) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DisableCommandLine(
        VARIANT_BOOL disableCommandLine) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EnableScreeningAudit(
        VARIANT_BOOL *enableScreeningAudit) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EnableScreeningAudit(
        VARIANT_BOOL enableScreeningAudit) = 0;

    virtual HRESULT STDMETHODCALLTYPE EmailTest(
        BSTR mailTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetActionRunLimitInterval(
        FsrmActionType actionType,
        LONG delayTimeMinutes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetActionRunLimitInterval(
        FsrmActionType actionType,
        LONG *delayTimeMinutes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmSetting, 0xf411d4fd, 0x14be, 0x4260, 0x8c,0x40, 0x03,0xb7,0xc9,0x5e,0x60,0x8a)
#endif
#else
typedef struct IFsrmSettingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmSetting* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmSetting* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmSetting* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmSetting* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmSetting* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmSetting* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmSetting* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmSetting methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SmtpServer)(
        IFsrmSetting* This,
        BSTR *smtpServer);

    HRESULT (STDMETHODCALLTYPE *put_SmtpServer)(
        IFsrmSetting* This,
        BSTR smtpServer);

    HRESULT (STDMETHODCALLTYPE *get_MailFrom)(
        IFsrmSetting* This,
        BSTR *mailFrom);

    HRESULT (STDMETHODCALLTYPE *put_MailFrom)(
        IFsrmSetting* This,
        BSTR mailFrom);

    HRESULT (STDMETHODCALLTYPE *get_AdminEmail)(
        IFsrmSetting* This,
        BSTR *adminEmail);

    HRESULT (STDMETHODCALLTYPE *put_AdminEmail)(
        IFsrmSetting* This,
        BSTR adminEmail);

    HRESULT (STDMETHODCALLTYPE *get_DisableCommandLine)(
        IFsrmSetting* This,
        VARIANT_BOOL *disableCommandLine);

    HRESULT (STDMETHODCALLTYPE *put_DisableCommandLine)(
        IFsrmSetting* This,
        VARIANT_BOOL disableCommandLine);

    HRESULT (STDMETHODCALLTYPE *get_EnableScreeningAudit)(
        IFsrmSetting* This,
        VARIANT_BOOL *enableScreeningAudit);

    HRESULT (STDMETHODCALLTYPE *put_EnableScreeningAudit)(
        IFsrmSetting* This,
        VARIANT_BOOL enableScreeningAudit);

    HRESULT (STDMETHODCALLTYPE *EmailTest)(
        IFsrmSetting* This,
        BSTR mailTo);

    HRESULT (STDMETHODCALLTYPE *SetActionRunLimitInterval)(
        IFsrmSetting* This,
        FsrmActionType actionType,
        LONG delayTimeMinutes);

    HRESULT (STDMETHODCALLTYPE *GetActionRunLimitInterval)(
        IFsrmSetting* This,
        FsrmActionType actionType,
        LONG *delayTimeMinutes);

    END_INTERFACE
} IFsrmSettingVtbl;
interface IFsrmSetting {
    CONST_VTBL IFsrmSettingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmSetting_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmSetting_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmSetting_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmSetting_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmSetting_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmSetting_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmSetting_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmSetting methods ***/
#define IFsrmSetting_get_SmtpServer(This,smtpServer) (This)->lpVtbl->get_SmtpServer(This,smtpServer)
#define IFsrmSetting_put_SmtpServer(This,smtpServer) (This)->lpVtbl->put_SmtpServer(This,smtpServer)
#define IFsrmSetting_get_MailFrom(This,mailFrom) (This)->lpVtbl->get_MailFrom(This,mailFrom)
#define IFsrmSetting_put_MailFrom(This,mailFrom) (This)->lpVtbl->put_MailFrom(This,mailFrom)
#define IFsrmSetting_get_AdminEmail(This,adminEmail) (This)->lpVtbl->get_AdminEmail(This,adminEmail)
#define IFsrmSetting_put_AdminEmail(This,adminEmail) (This)->lpVtbl->put_AdminEmail(This,adminEmail)
#define IFsrmSetting_get_DisableCommandLine(This,disableCommandLine) (This)->lpVtbl->get_DisableCommandLine(This,disableCommandLine)
#define IFsrmSetting_put_DisableCommandLine(This,disableCommandLine) (This)->lpVtbl->put_DisableCommandLine(This,disableCommandLine)
#define IFsrmSetting_get_EnableScreeningAudit(This,enableScreeningAudit) (This)->lpVtbl->get_EnableScreeningAudit(This,enableScreeningAudit)
#define IFsrmSetting_put_EnableScreeningAudit(This,enableScreeningAudit) (This)->lpVtbl->put_EnableScreeningAudit(This,enableScreeningAudit)
#define IFsrmSetting_EmailTest(This,mailTo) (This)->lpVtbl->EmailTest(This,mailTo)
#define IFsrmSetting_SetActionRunLimitInterval(This,actionType,delayTimeMinutes) (This)->lpVtbl->SetActionRunLimitInterval(This,actionType,delayTimeMinutes)
#define IFsrmSetting_GetActionRunLimitInterval(This,actionType,delayTimeMinutes) (This)->lpVtbl->GetActionRunLimitInterval(This,actionType,delayTimeMinutes)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmSetting_QueryInterface(IFsrmSetting* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmSetting_AddRef(IFsrmSetting* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmSetting_Release(IFsrmSetting* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmSetting_GetTypeInfoCount(IFsrmSetting* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmSetting_GetTypeInfo(IFsrmSetting* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmSetting_GetIDsOfNames(IFsrmSetting* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmSetting_Invoke(IFsrmSetting* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmSetting methods ***/
static FORCEINLINE HRESULT IFsrmSetting_get_SmtpServer(IFsrmSetting* This,BSTR *smtpServer) {
    return This->lpVtbl->get_SmtpServer(This,smtpServer);
}
static FORCEINLINE HRESULT IFsrmSetting_put_SmtpServer(IFsrmSetting* This,BSTR smtpServer) {
    return This->lpVtbl->put_SmtpServer(This,smtpServer);
}
static FORCEINLINE HRESULT IFsrmSetting_get_MailFrom(IFsrmSetting* This,BSTR *mailFrom) {
    return This->lpVtbl->get_MailFrom(This,mailFrom);
}
static FORCEINLINE HRESULT IFsrmSetting_put_MailFrom(IFsrmSetting* This,BSTR mailFrom) {
    return This->lpVtbl->put_MailFrom(This,mailFrom);
}
static FORCEINLINE HRESULT IFsrmSetting_get_AdminEmail(IFsrmSetting* This,BSTR *adminEmail) {
    return This->lpVtbl->get_AdminEmail(This,adminEmail);
}
static FORCEINLINE HRESULT IFsrmSetting_put_AdminEmail(IFsrmSetting* This,BSTR adminEmail) {
    return This->lpVtbl->put_AdminEmail(This,adminEmail);
}
static FORCEINLINE HRESULT IFsrmSetting_get_DisableCommandLine(IFsrmSetting* This,VARIANT_BOOL *disableCommandLine) {
    return This->lpVtbl->get_DisableCommandLine(This,disableCommandLine);
}
static FORCEINLINE HRESULT IFsrmSetting_put_DisableCommandLine(IFsrmSetting* This,VARIANT_BOOL disableCommandLine) {
    return This->lpVtbl->put_DisableCommandLine(This,disableCommandLine);
}
static FORCEINLINE HRESULT IFsrmSetting_get_EnableScreeningAudit(IFsrmSetting* This,VARIANT_BOOL *enableScreeningAudit) {
    return This->lpVtbl->get_EnableScreeningAudit(This,enableScreeningAudit);
}
static FORCEINLINE HRESULT IFsrmSetting_put_EnableScreeningAudit(IFsrmSetting* This,VARIANT_BOOL enableScreeningAudit) {
    return This->lpVtbl->put_EnableScreeningAudit(This,enableScreeningAudit);
}
static FORCEINLINE HRESULT IFsrmSetting_EmailTest(IFsrmSetting* This,BSTR mailTo) {
    return This->lpVtbl->EmailTest(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmSetting_SetActionRunLimitInterval(IFsrmSetting* This,FsrmActionType actionType,LONG delayTimeMinutes) {
    return This->lpVtbl->SetActionRunLimitInterval(This,actionType,delayTimeMinutes);
}
static FORCEINLINE HRESULT IFsrmSetting_GetActionRunLimitInterval(IFsrmSetting* This,FsrmActionType actionType,LONG *delayTimeMinutes) {
    return This->lpVtbl->GetActionRunLimitInterval(This,actionType,delayTimeMinutes);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmSetting_get_SmtpServer_Proxy(
    IFsrmSetting* This,
    BSTR *smtpServer);
void __RPC_STUB IFsrmSetting_get_SmtpServer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_put_SmtpServer_Proxy(
    IFsrmSetting* This,
    BSTR smtpServer);
void __RPC_STUB IFsrmSetting_put_SmtpServer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_get_MailFrom_Proxy(
    IFsrmSetting* This,
    BSTR *mailFrom);
void __RPC_STUB IFsrmSetting_get_MailFrom_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_put_MailFrom_Proxy(
    IFsrmSetting* This,
    BSTR mailFrom);
void __RPC_STUB IFsrmSetting_put_MailFrom_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_get_AdminEmail_Proxy(
    IFsrmSetting* This,
    BSTR *adminEmail);
void __RPC_STUB IFsrmSetting_get_AdminEmail_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_put_AdminEmail_Proxy(
    IFsrmSetting* This,
    BSTR adminEmail);
void __RPC_STUB IFsrmSetting_put_AdminEmail_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_get_DisableCommandLine_Proxy(
    IFsrmSetting* This,
    VARIANT_BOOL *disableCommandLine);
void __RPC_STUB IFsrmSetting_get_DisableCommandLine_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_put_DisableCommandLine_Proxy(
    IFsrmSetting* This,
    VARIANT_BOOL disableCommandLine);
void __RPC_STUB IFsrmSetting_put_DisableCommandLine_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_get_EnableScreeningAudit_Proxy(
    IFsrmSetting* This,
    VARIANT_BOOL *enableScreeningAudit);
void __RPC_STUB IFsrmSetting_get_EnableScreeningAudit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_put_EnableScreeningAudit_Proxy(
    IFsrmSetting* This,
    VARIANT_BOOL enableScreeningAudit);
void __RPC_STUB IFsrmSetting_put_EnableScreeningAudit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_EmailTest_Proxy(
    IFsrmSetting* This,
    BSTR mailTo);
void __RPC_STUB IFsrmSetting_EmailTest_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_SetActionRunLimitInterval_Proxy(
    IFsrmSetting* This,
    FsrmActionType actionType,
    LONG delayTimeMinutes);
void __RPC_STUB IFsrmSetting_SetActionRunLimitInterval_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmSetting_GetActionRunLimitInterval_Proxy(
    IFsrmSetting* This,
    FsrmActionType actionType,
    LONG *delayTimeMinutes);
void __RPC_STUB IFsrmSetting_GetActionRunLimitInterval_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmSetting_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmActionEmail interface
 */
#ifndef __IFsrmActionEmail_INTERFACE_DEFINED__
#define __IFsrmActionEmail_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmActionEmail, 0xd646567d, 0x26ae, 0x4caa, 0x9f,0x84, 0x4e,0x0a,0xad,0x20,0x7f,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d646567d-26ae-4caa-9f84-4e0aad207fca")
IFsrmActionEmail : public IFsrmAction
{
    virtual HRESULT STDMETHODCALLTYPE get_MailFrom(
        BSTR *mailFrom) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailFrom(
        BSTR mailFrom) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailReplyTo(
        BSTR *mailReplyTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailReplyTo(
        BSTR mailReplyTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailTo(
        BSTR *mailTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailTo(
        BSTR mailTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailCc(
        BSTR *mailCc) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailCc(
        BSTR mailCc) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailBcc(
        BSTR *mailBcc) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailBcc(
        BSTR mailBcc) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailSubject(
        BSTR *mailSubject) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailSubject(
        BSTR mailSubject) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MessageText(
        BSTR *messageText) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MessageText(
        BSTR messageText) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmActionEmail, 0xd646567d, 0x26ae, 0x4caa, 0x9f,0x84, 0x4e,0x0a,0xad,0x20,0x7f,0xca)
#endif
#else
typedef struct IFsrmActionEmailVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmActionEmail* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmActionEmail* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmActionEmail* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmActionEmail* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmActionEmail* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmActionEmail* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmActionEmail* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmActionEmail* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_ActionType)(
        IFsrmActionEmail* This,
        FsrmActionType *actionType);

    HRESULT (STDMETHODCALLTYPE *get_RunLimitInterval)(
        IFsrmActionEmail* This,
        LONG *minutes);

    HRESULT (STDMETHODCALLTYPE *put_RunLimitInterval)(
        IFsrmActionEmail* This,
        LONG minutes);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmActionEmail* This);

    /*** IFsrmActionEmail methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MailFrom)(
        IFsrmActionEmail* This,
        BSTR *mailFrom);

    HRESULT (STDMETHODCALLTYPE *put_MailFrom)(
        IFsrmActionEmail* This,
        BSTR mailFrom);

    HRESULT (STDMETHODCALLTYPE *get_MailReplyTo)(
        IFsrmActionEmail* This,
        BSTR *mailReplyTo);

    HRESULT (STDMETHODCALLTYPE *put_MailReplyTo)(
        IFsrmActionEmail* This,
        BSTR mailReplyTo);

    HRESULT (STDMETHODCALLTYPE *get_MailTo)(
        IFsrmActionEmail* This,
        BSTR *mailTo);

    HRESULT (STDMETHODCALLTYPE *put_MailTo)(
        IFsrmActionEmail* This,
        BSTR mailTo);

    HRESULT (STDMETHODCALLTYPE *get_MailCc)(
        IFsrmActionEmail* This,
        BSTR *mailCc);

    HRESULT (STDMETHODCALLTYPE *put_MailCc)(
        IFsrmActionEmail* This,
        BSTR mailCc);

    HRESULT (STDMETHODCALLTYPE *get_MailBcc)(
        IFsrmActionEmail* This,
        BSTR *mailBcc);

    HRESULT (STDMETHODCALLTYPE *put_MailBcc)(
        IFsrmActionEmail* This,
        BSTR mailBcc);

    HRESULT (STDMETHODCALLTYPE *get_MailSubject)(
        IFsrmActionEmail* This,
        BSTR *mailSubject);

    HRESULT (STDMETHODCALLTYPE *put_MailSubject)(
        IFsrmActionEmail* This,
        BSTR mailSubject);

    HRESULT (STDMETHODCALLTYPE *get_MessageText)(
        IFsrmActionEmail* This,
        BSTR *messageText);

    HRESULT (STDMETHODCALLTYPE *put_MessageText)(
        IFsrmActionEmail* This,
        BSTR messageText);

    END_INTERFACE
} IFsrmActionEmailVtbl;
interface IFsrmActionEmail {
    CONST_VTBL IFsrmActionEmailVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmActionEmail_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmActionEmail_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmActionEmail_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmActionEmail_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmActionEmail_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmActionEmail_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmActionEmail_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmAction methods ***/
#define IFsrmActionEmail_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmActionEmail_get_ActionType(This,actionType) (This)->lpVtbl->get_ActionType(This,actionType)
#define IFsrmActionEmail_get_RunLimitInterval(This,minutes) (This)->lpVtbl->get_RunLimitInterval(This,minutes)
#define IFsrmActionEmail_put_RunLimitInterval(This,minutes) (This)->lpVtbl->put_RunLimitInterval(This,minutes)
#define IFsrmActionEmail_Delete(This) (This)->lpVtbl->Delete(This)
/*** IFsrmActionEmail methods ***/
#define IFsrmActionEmail_get_MailFrom(This,mailFrom) (This)->lpVtbl->get_MailFrom(This,mailFrom)
#define IFsrmActionEmail_put_MailFrom(This,mailFrom) (This)->lpVtbl->put_MailFrom(This,mailFrom)
#define IFsrmActionEmail_get_MailReplyTo(This,mailReplyTo) (This)->lpVtbl->get_MailReplyTo(This,mailReplyTo)
#define IFsrmActionEmail_put_MailReplyTo(This,mailReplyTo) (This)->lpVtbl->put_MailReplyTo(This,mailReplyTo)
#define IFsrmActionEmail_get_MailTo(This,mailTo) (This)->lpVtbl->get_MailTo(This,mailTo)
#define IFsrmActionEmail_put_MailTo(This,mailTo) (This)->lpVtbl->put_MailTo(This,mailTo)
#define IFsrmActionEmail_get_MailCc(This,mailCc) (This)->lpVtbl->get_MailCc(This,mailCc)
#define IFsrmActionEmail_put_MailCc(This,mailCc) (This)->lpVtbl->put_MailCc(This,mailCc)
#define IFsrmActionEmail_get_MailBcc(This,mailBcc) (This)->lpVtbl->get_MailBcc(This,mailBcc)
#define IFsrmActionEmail_put_MailBcc(This,mailBcc) (This)->lpVtbl->put_MailBcc(This,mailBcc)
#define IFsrmActionEmail_get_MailSubject(This,mailSubject) (This)->lpVtbl->get_MailSubject(This,mailSubject)
#define IFsrmActionEmail_put_MailSubject(This,mailSubject) (This)->lpVtbl->put_MailSubject(This,mailSubject)
#define IFsrmActionEmail_get_MessageText(This,messageText) (This)->lpVtbl->get_MessageText(This,messageText)
#define IFsrmActionEmail_put_MessageText(This,messageText) (This)->lpVtbl->put_MessageText(This,messageText)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail_QueryInterface(IFsrmActionEmail* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmActionEmail_AddRef(IFsrmActionEmail* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmActionEmail_Release(IFsrmActionEmail* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail_GetTypeInfoCount(IFsrmActionEmail* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmActionEmail_GetTypeInfo(IFsrmActionEmail* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmActionEmail_GetIDsOfNames(IFsrmActionEmail* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmActionEmail_Invoke(IFsrmActionEmail* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmAction methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail_get_Id(IFsrmActionEmail* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmActionEmail_get_ActionType(IFsrmActionEmail* This,FsrmActionType *actionType) {
    return This->lpVtbl->get_ActionType(This,actionType);
}
static FORCEINLINE HRESULT IFsrmActionEmail_get_RunLimitInterval(IFsrmActionEmail* This,LONG *minutes) {
    return This->lpVtbl->get_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionEmail_put_RunLimitInterval(IFsrmActionEmail* This,LONG minutes) {
    return This->lpVtbl->put_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionEmail_Delete(IFsrmActionEmail* This) {
    return This->lpVtbl->Delete(This);
}
/*** IFsrmActionEmail methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail_get_MailFrom(IFsrmActionEmail* This,BSTR *mailFrom) {
    return This->lpVtbl->get_MailFrom(This,mailFrom);
}
static FORCEINLINE HRESULT IFsrmActionEmail_put_MailFrom(IFsrmActionEmail* This,BSTR mailFrom) {
    return This->lpVtbl->put_MailFrom(This,mailFrom);
}
static FORCEINLINE HRESULT IFsrmActionEmail_get_MailReplyTo(IFsrmActionEmail* This,BSTR *mailReplyTo) {
    return This->lpVtbl->get_MailReplyTo(This,mailReplyTo);
}
static FORCEINLINE HRESULT IFsrmActionEmail_put_MailReplyTo(IFsrmActionEmail* This,BSTR mailReplyTo) {
    return This->lpVtbl->put_MailReplyTo(This,mailReplyTo);
}
static FORCEINLINE HRESULT IFsrmActionEmail_get_MailTo(IFsrmActionEmail* This,BSTR *mailTo) {
    return This->lpVtbl->get_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmActionEmail_put_MailTo(IFsrmActionEmail* This,BSTR mailTo) {
    return This->lpVtbl->put_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmActionEmail_get_MailCc(IFsrmActionEmail* This,BSTR *mailCc) {
    return This->lpVtbl->get_MailCc(This,mailCc);
}
static FORCEINLINE HRESULT IFsrmActionEmail_put_MailCc(IFsrmActionEmail* This,BSTR mailCc) {
    return This->lpVtbl->put_MailCc(This,mailCc);
}
static FORCEINLINE HRESULT IFsrmActionEmail_get_MailBcc(IFsrmActionEmail* This,BSTR *mailBcc) {
    return This->lpVtbl->get_MailBcc(This,mailBcc);
}
static FORCEINLINE HRESULT IFsrmActionEmail_put_MailBcc(IFsrmActionEmail* This,BSTR mailBcc) {
    return This->lpVtbl->put_MailBcc(This,mailBcc);
}
static FORCEINLINE HRESULT IFsrmActionEmail_get_MailSubject(IFsrmActionEmail* This,BSTR *mailSubject) {
    return This->lpVtbl->get_MailSubject(This,mailSubject);
}
static FORCEINLINE HRESULT IFsrmActionEmail_put_MailSubject(IFsrmActionEmail* This,BSTR mailSubject) {
    return This->lpVtbl->put_MailSubject(This,mailSubject);
}
static FORCEINLINE HRESULT IFsrmActionEmail_get_MessageText(IFsrmActionEmail* This,BSTR *messageText) {
    return This->lpVtbl->get_MessageText(This,messageText);
}
static FORCEINLINE HRESULT IFsrmActionEmail_put_MessageText(IFsrmActionEmail* This,BSTR messageText) {
    return This->lpVtbl->put_MessageText(This,messageText);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmActionEmail_get_MailFrom_Proxy(
    IFsrmActionEmail* This,
    BSTR *mailFrom);
void __RPC_STUB IFsrmActionEmail_get_MailFrom_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_put_MailFrom_Proxy(
    IFsrmActionEmail* This,
    BSTR mailFrom);
void __RPC_STUB IFsrmActionEmail_put_MailFrom_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_get_MailReplyTo_Proxy(
    IFsrmActionEmail* This,
    BSTR *mailReplyTo);
void __RPC_STUB IFsrmActionEmail_get_MailReplyTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_put_MailReplyTo_Proxy(
    IFsrmActionEmail* This,
    BSTR mailReplyTo);
void __RPC_STUB IFsrmActionEmail_put_MailReplyTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_get_MailTo_Proxy(
    IFsrmActionEmail* This,
    BSTR *mailTo);
void __RPC_STUB IFsrmActionEmail_get_MailTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_put_MailTo_Proxy(
    IFsrmActionEmail* This,
    BSTR mailTo);
void __RPC_STUB IFsrmActionEmail_put_MailTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_get_MailCc_Proxy(
    IFsrmActionEmail* This,
    BSTR *mailCc);
void __RPC_STUB IFsrmActionEmail_get_MailCc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_put_MailCc_Proxy(
    IFsrmActionEmail* This,
    BSTR mailCc);
void __RPC_STUB IFsrmActionEmail_put_MailCc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_get_MailBcc_Proxy(
    IFsrmActionEmail* This,
    BSTR *mailBcc);
void __RPC_STUB IFsrmActionEmail_get_MailBcc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_put_MailBcc_Proxy(
    IFsrmActionEmail* This,
    BSTR mailBcc);
void __RPC_STUB IFsrmActionEmail_put_MailBcc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_get_MailSubject_Proxy(
    IFsrmActionEmail* This,
    BSTR *mailSubject);
void __RPC_STUB IFsrmActionEmail_get_MailSubject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_put_MailSubject_Proxy(
    IFsrmActionEmail* This,
    BSTR mailSubject);
void __RPC_STUB IFsrmActionEmail_put_MailSubject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_get_MessageText_Proxy(
    IFsrmActionEmail* This,
    BSTR *messageText);
void __RPC_STUB IFsrmActionEmail_get_MessageText_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail_put_MessageText_Proxy(
    IFsrmActionEmail* This,
    BSTR messageText);
void __RPC_STUB IFsrmActionEmail_put_MessageText_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmActionEmail_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmActionEmail2 interface
 */
#ifndef __IFsrmActionEmail2_INTERFACE_DEFINED__
#define __IFsrmActionEmail2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmActionEmail2, 0x8276702f, 0x2532, 0x4839, 0x89,0xbf, 0x48,0x72,0x60,0x9a,0x2e,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8276702f-2532-4839-89bf-4872609a2ea4")
IFsrmActionEmail2 : public IFsrmActionEmail
{
    virtual HRESULT STDMETHODCALLTYPE get_AttachmentFileListSize(
        LONG *attachmentFileListSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AttachmentFileListSize(
        LONG attachmentFileListSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmActionEmail2, 0x8276702f, 0x2532, 0x4839, 0x89,0xbf, 0x48,0x72,0x60,0x9a,0x2e,0xa4)
#endif
#else
typedef struct IFsrmActionEmail2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmActionEmail2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmActionEmail2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmActionEmail2* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmActionEmail2* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmActionEmail2* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmActionEmail2* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmActionEmail2* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmActionEmail2* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_ActionType)(
        IFsrmActionEmail2* This,
        FsrmActionType *actionType);

    HRESULT (STDMETHODCALLTYPE *get_RunLimitInterval)(
        IFsrmActionEmail2* This,
        LONG *minutes);

    HRESULT (STDMETHODCALLTYPE *put_RunLimitInterval)(
        IFsrmActionEmail2* This,
        LONG minutes);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmActionEmail2* This);

    /*** IFsrmActionEmail methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MailFrom)(
        IFsrmActionEmail2* This,
        BSTR *mailFrom);

    HRESULT (STDMETHODCALLTYPE *put_MailFrom)(
        IFsrmActionEmail2* This,
        BSTR mailFrom);

    HRESULT (STDMETHODCALLTYPE *get_MailReplyTo)(
        IFsrmActionEmail2* This,
        BSTR *mailReplyTo);

    HRESULT (STDMETHODCALLTYPE *put_MailReplyTo)(
        IFsrmActionEmail2* This,
        BSTR mailReplyTo);

    HRESULT (STDMETHODCALLTYPE *get_MailTo)(
        IFsrmActionEmail2* This,
        BSTR *mailTo);

    HRESULT (STDMETHODCALLTYPE *put_MailTo)(
        IFsrmActionEmail2* This,
        BSTR mailTo);

    HRESULT (STDMETHODCALLTYPE *get_MailCc)(
        IFsrmActionEmail2* This,
        BSTR *mailCc);

    HRESULT (STDMETHODCALLTYPE *put_MailCc)(
        IFsrmActionEmail2* This,
        BSTR mailCc);

    HRESULT (STDMETHODCALLTYPE *get_MailBcc)(
        IFsrmActionEmail2* This,
        BSTR *mailBcc);

    HRESULT (STDMETHODCALLTYPE *put_MailBcc)(
        IFsrmActionEmail2* This,
        BSTR mailBcc);

    HRESULT (STDMETHODCALLTYPE *get_MailSubject)(
        IFsrmActionEmail2* This,
        BSTR *mailSubject);

    HRESULT (STDMETHODCALLTYPE *put_MailSubject)(
        IFsrmActionEmail2* This,
        BSTR mailSubject);

    HRESULT (STDMETHODCALLTYPE *get_MessageText)(
        IFsrmActionEmail2* This,
        BSTR *messageText);

    HRESULT (STDMETHODCALLTYPE *put_MessageText)(
        IFsrmActionEmail2* This,
        BSTR messageText);

    /*** IFsrmActionEmail2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AttachmentFileListSize)(
        IFsrmActionEmail2* This,
        LONG *attachmentFileListSize);

    HRESULT (STDMETHODCALLTYPE *put_AttachmentFileListSize)(
        IFsrmActionEmail2* This,
        LONG attachmentFileListSize);

    END_INTERFACE
} IFsrmActionEmail2Vtbl;
interface IFsrmActionEmail2 {
    CONST_VTBL IFsrmActionEmail2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmActionEmail2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmActionEmail2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmActionEmail2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmActionEmail2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmActionEmail2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmActionEmail2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmActionEmail2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmAction methods ***/
#define IFsrmActionEmail2_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmActionEmail2_get_ActionType(This,actionType) (This)->lpVtbl->get_ActionType(This,actionType)
#define IFsrmActionEmail2_get_RunLimitInterval(This,minutes) (This)->lpVtbl->get_RunLimitInterval(This,minutes)
#define IFsrmActionEmail2_put_RunLimitInterval(This,minutes) (This)->lpVtbl->put_RunLimitInterval(This,minutes)
#define IFsrmActionEmail2_Delete(This) (This)->lpVtbl->Delete(This)
/*** IFsrmActionEmail methods ***/
#define IFsrmActionEmail2_get_MailFrom(This,mailFrom) (This)->lpVtbl->get_MailFrom(This,mailFrom)
#define IFsrmActionEmail2_put_MailFrom(This,mailFrom) (This)->lpVtbl->put_MailFrom(This,mailFrom)
#define IFsrmActionEmail2_get_MailReplyTo(This,mailReplyTo) (This)->lpVtbl->get_MailReplyTo(This,mailReplyTo)
#define IFsrmActionEmail2_put_MailReplyTo(This,mailReplyTo) (This)->lpVtbl->put_MailReplyTo(This,mailReplyTo)
#define IFsrmActionEmail2_get_MailTo(This,mailTo) (This)->lpVtbl->get_MailTo(This,mailTo)
#define IFsrmActionEmail2_put_MailTo(This,mailTo) (This)->lpVtbl->put_MailTo(This,mailTo)
#define IFsrmActionEmail2_get_MailCc(This,mailCc) (This)->lpVtbl->get_MailCc(This,mailCc)
#define IFsrmActionEmail2_put_MailCc(This,mailCc) (This)->lpVtbl->put_MailCc(This,mailCc)
#define IFsrmActionEmail2_get_MailBcc(This,mailBcc) (This)->lpVtbl->get_MailBcc(This,mailBcc)
#define IFsrmActionEmail2_put_MailBcc(This,mailBcc) (This)->lpVtbl->put_MailBcc(This,mailBcc)
#define IFsrmActionEmail2_get_MailSubject(This,mailSubject) (This)->lpVtbl->get_MailSubject(This,mailSubject)
#define IFsrmActionEmail2_put_MailSubject(This,mailSubject) (This)->lpVtbl->put_MailSubject(This,mailSubject)
#define IFsrmActionEmail2_get_MessageText(This,messageText) (This)->lpVtbl->get_MessageText(This,messageText)
#define IFsrmActionEmail2_put_MessageText(This,messageText) (This)->lpVtbl->put_MessageText(This,messageText)
/*** IFsrmActionEmail2 methods ***/
#define IFsrmActionEmail2_get_AttachmentFileListSize(This,attachmentFileListSize) (This)->lpVtbl->get_AttachmentFileListSize(This,attachmentFileListSize)
#define IFsrmActionEmail2_put_AttachmentFileListSize(This,attachmentFileListSize) (This)->lpVtbl->put_AttachmentFileListSize(This,attachmentFileListSize)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail2_QueryInterface(IFsrmActionEmail2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmActionEmail2_AddRef(IFsrmActionEmail2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmActionEmail2_Release(IFsrmActionEmail2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail2_GetTypeInfoCount(IFsrmActionEmail2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_GetTypeInfo(IFsrmActionEmail2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_GetIDsOfNames(IFsrmActionEmail2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_Invoke(IFsrmActionEmail2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmAction methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail2_get_Id(IFsrmActionEmail2* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_get_ActionType(IFsrmActionEmail2* This,FsrmActionType *actionType) {
    return This->lpVtbl->get_ActionType(This,actionType);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_get_RunLimitInterval(IFsrmActionEmail2* This,LONG *minutes) {
    return This->lpVtbl->get_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_RunLimitInterval(IFsrmActionEmail2* This,LONG minutes) {
    return This->lpVtbl->put_RunLimitInterval(This,minutes);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_Delete(IFsrmActionEmail2* This) {
    return This->lpVtbl->Delete(This);
}
/*** IFsrmActionEmail methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail2_get_MailFrom(IFsrmActionEmail2* This,BSTR *mailFrom) {
    return This->lpVtbl->get_MailFrom(This,mailFrom);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_MailFrom(IFsrmActionEmail2* This,BSTR mailFrom) {
    return This->lpVtbl->put_MailFrom(This,mailFrom);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_get_MailReplyTo(IFsrmActionEmail2* This,BSTR *mailReplyTo) {
    return This->lpVtbl->get_MailReplyTo(This,mailReplyTo);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_MailReplyTo(IFsrmActionEmail2* This,BSTR mailReplyTo) {
    return This->lpVtbl->put_MailReplyTo(This,mailReplyTo);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_get_MailTo(IFsrmActionEmail2* This,BSTR *mailTo) {
    return This->lpVtbl->get_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_MailTo(IFsrmActionEmail2* This,BSTR mailTo) {
    return This->lpVtbl->put_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_get_MailCc(IFsrmActionEmail2* This,BSTR *mailCc) {
    return This->lpVtbl->get_MailCc(This,mailCc);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_MailCc(IFsrmActionEmail2* This,BSTR mailCc) {
    return This->lpVtbl->put_MailCc(This,mailCc);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_get_MailBcc(IFsrmActionEmail2* This,BSTR *mailBcc) {
    return This->lpVtbl->get_MailBcc(This,mailBcc);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_MailBcc(IFsrmActionEmail2* This,BSTR mailBcc) {
    return This->lpVtbl->put_MailBcc(This,mailBcc);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_get_MailSubject(IFsrmActionEmail2* This,BSTR *mailSubject) {
    return This->lpVtbl->get_MailSubject(This,mailSubject);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_MailSubject(IFsrmActionEmail2* This,BSTR mailSubject) {
    return This->lpVtbl->put_MailSubject(This,mailSubject);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_get_MessageText(IFsrmActionEmail2* This,BSTR *messageText) {
    return This->lpVtbl->get_MessageText(This,messageText);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_MessageText(IFsrmActionEmail2* This,BSTR messageText) {
    return This->lpVtbl->put_MessageText(This,messageText);
}
/*** IFsrmActionEmail2 methods ***/
static FORCEINLINE HRESULT IFsrmActionEmail2_get_AttachmentFileListSize(IFsrmActionEmail2* This,LONG *attachmentFileListSize) {
    return This->lpVtbl->get_AttachmentFileListSize(This,attachmentFileListSize);
}
static FORCEINLINE HRESULT IFsrmActionEmail2_put_AttachmentFileListSize(IFsrmActionEmail2* This,LONG attachmentFileListSize) {
    return This->lpVtbl->put_AttachmentFileListSize(This,attachmentFileListSize);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmActionEmail2_get_AttachmentFileListSize_Proxy(
    IFsrmActionEmail2* This,
    LONG *attachmentFileListSize);
void __RPC_STUB IFsrmActionEmail2_get_AttachmentFileListSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmActionEmail2_put_AttachmentFileListSize_Proxy(
    IFsrmActionEmail2* This,
    LONG attachmentFileListSize);
void __RPC_STUB IFsrmActionEmail2_put_AttachmentFileListSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmActionEmail2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmMutableCollection interface
 */
#ifndef __IFsrmMutableCollection_INTERFACE_DEFINED__
#define __IFsrmMutableCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmMutableCollection, 0x1bb617b8, 0x3886, 0x49dc, 0xaf,0x82, 0xa6,0xc9,0x0f,0xa3,0x5d,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1bb617b8-3886-49dc-af82-a6c90fa35dda")
IFsrmMutableCollection : public IFsrmCollection
{
    virtual HRESULT STDMETHODCALLTYPE Add(
        VARIANT item) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        LONG index) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveById(
        FSRM_OBJECT_ID id) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IFsrmMutableCollection **collection) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmMutableCollection, 0x1bb617b8, 0x3886, 0x49dc, 0xaf,0x82, 0xa6,0xc9,0x0f,0xa3,0x5d,0xda)
#endif
#else
typedef struct IFsrmMutableCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmMutableCollection* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmMutableCollection* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmMutableCollection* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmMutableCollection* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmMutableCollection* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmMutableCollection* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmMutableCollection* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IFsrmMutableCollection* This,
        IUnknown **unknown);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IFsrmMutableCollection* This,
        LONG index,
        VARIANT *item);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IFsrmMutableCollection* This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        IFsrmMutableCollection* This,
        FsrmCollectionState *state);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IFsrmMutableCollection* This);

    HRESULT (STDMETHODCALLTYPE *WaitForCompletion)(
        IFsrmMutableCollection* This,
        LONG waitSeconds,
        VARIANT_BOOL *completed);

    HRESULT (STDMETHODCALLTYPE *GetById)(
        IFsrmMutableCollection* This,
        FSRM_OBJECT_ID id,
        VARIANT *entry);

    /*** IFsrmMutableCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *Add)(
        IFsrmMutableCollection* This,
        VARIANT item);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IFsrmMutableCollection* This,
        LONG index);

    HRESULT (STDMETHODCALLTYPE *RemoveById)(
        IFsrmMutableCollection* This,
        FSRM_OBJECT_ID id);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IFsrmMutableCollection* This,
        IFsrmMutableCollection **collection);

    END_INTERFACE
} IFsrmMutableCollectionVtbl;
interface IFsrmMutableCollection {
    CONST_VTBL IFsrmMutableCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmMutableCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmMutableCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmMutableCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmMutableCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmMutableCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmMutableCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmMutableCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmCollection methods ***/
#define IFsrmMutableCollection_get__NewEnum(This,unknown) (This)->lpVtbl->get__NewEnum(This,unknown)
#define IFsrmMutableCollection_get_Item(This,index,item) (This)->lpVtbl->get_Item(This,index,item)
#define IFsrmMutableCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define IFsrmMutableCollection_get_State(This,state) (This)->lpVtbl->get_State(This,state)
#define IFsrmMutableCollection_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IFsrmMutableCollection_WaitForCompletion(This,waitSeconds,completed) (This)->lpVtbl->WaitForCompletion(This,waitSeconds,completed)
#define IFsrmMutableCollection_GetById(This,id,entry) (This)->lpVtbl->GetById(This,id,entry)
/*** IFsrmMutableCollection methods ***/
#define IFsrmMutableCollection_Add(This,item) (This)->lpVtbl->Add(This,item)
#define IFsrmMutableCollection_Remove(This,index) (This)->lpVtbl->Remove(This,index)
#define IFsrmMutableCollection_RemoveById(This,id) (This)->lpVtbl->RemoveById(This,id)
#define IFsrmMutableCollection_Clone(This,collection) (This)->lpVtbl->Clone(This,collection)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmMutableCollection_QueryInterface(IFsrmMutableCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmMutableCollection_AddRef(IFsrmMutableCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmMutableCollection_Release(IFsrmMutableCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmMutableCollection_GetTypeInfoCount(IFsrmMutableCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_GetTypeInfo(IFsrmMutableCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_GetIDsOfNames(IFsrmMutableCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_Invoke(IFsrmMutableCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmCollection methods ***/
static FORCEINLINE HRESULT IFsrmMutableCollection_get__NewEnum(IFsrmMutableCollection* This,IUnknown **unknown) {
    return This->lpVtbl->get__NewEnum(This,unknown);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_get_Item(IFsrmMutableCollection* This,LONG index,VARIANT *item) {
    return This->lpVtbl->get_Item(This,index,item);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_get_Count(IFsrmMutableCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_get_State(IFsrmMutableCollection* This,FsrmCollectionState *state) {
    return This->lpVtbl->get_State(This,state);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_Cancel(IFsrmMutableCollection* This) {
    return This->lpVtbl->Cancel(This);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_WaitForCompletion(IFsrmMutableCollection* This,LONG waitSeconds,VARIANT_BOOL *completed) {
    return This->lpVtbl->WaitForCompletion(This,waitSeconds,completed);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_GetById(IFsrmMutableCollection* This,FSRM_OBJECT_ID id,VARIANT *entry) {
    return This->lpVtbl->GetById(This,id,entry);
}
/*** IFsrmMutableCollection methods ***/
static FORCEINLINE HRESULT IFsrmMutableCollection_Add(IFsrmMutableCollection* This,VARIANT item) {
    return This->lpVtbl->Add(This,item);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_Remove(IFsrmMutableCollection* This,LONG index) {
    return This->lpVtbl->Remove(This,index);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_RemoveById(IFsrmMutableCollection* This,FSRM_OBJECT_ID id) {
    return This->lpVtbl->RemoveById(This,id);
}
static FORCEINLINE HRESULT IFsrmMutableCollection_Clone(IFsrmMutableCollection* This,IFsrmMutableCollection **collection) {
    return This->lpVtbl->Clone(This,collection);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmMutableCollection_Add_Proxy(
    IFsrmMutableCollection* This,
    VARIANT item);
void __RPC_STUB IFsrmMutableCollection_Add_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmMutableCollection_Remove_Proxy(
    IFsrmMutableCollection* This,
    LONG index);
void __RPC_STUB IFsrmMutableCollection_Remove_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmMutableCollection_RemoveById_Proxy(
    IFsrmMutableCollection* This,
    FSRM_OBJECT_ID id);
void __RPC_STUB IFsrmMutableCollection_RemoveById_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmMutableCollection_Clone_Proxy(
    IFsrmMutableCollection* This,
    IFsrmMutableCollection **collection);
void __RPC_STUB IFsrmMutableCollection_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmMutableCollection_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __fsrm_h__ */
