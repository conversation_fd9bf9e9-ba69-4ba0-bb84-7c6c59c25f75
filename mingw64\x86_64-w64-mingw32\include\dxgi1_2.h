/*** Autogenerated by WIDL 3.1 from direct-x/include/dxgi1_2.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dxgi1_2_h__
#define __dxgi1_2_h__

/* Forward declarations */

#ifndef __IDXGIOutputDuplication_FWD_DEFINED__
#define __IDXGIOutputDuplication_FWD_DEFINED__
typedef interface IDXGIOutputDuplication IDXGIOutputDuplication;
#ifdef __cplusplus
interface IDXGIOutputDuplication;
#endif /* __cplusplus */
#endif

#ifndef __IDXGISurface2_FWD_DEFINED__
#define __IDXGISurface2_FWD_DEFINED__
typedef interface IDXGISurface2 IDXGISurface2;
#ifdef __cplusplus
interface IDXGISurface2;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIResource1_FWD_DEFINED__
#define __IDXGIResource1_FWD_DEFINED__
typedef interface IDXGIResource1 IDXGIResource1;
#ifdef __cplusplus
interface IDXGIResource1;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIDisplayControl_FWD_DEFINED__
#define __IDXGIDisplayControl_FWD_DEFINED__
typedef interface IDXGIDisplayControl IDXGIDisplayControl;
#ifdef __cplusplus
interface IDXGIDisplayControl;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIDevice2_FWD_DEFINED__
#define __IDXGIDevice2_FWD_DEFINED__
typedef interface IDXGIDevice2 IDXGIDevice2;
#ifdef __cplusplus
interface IDXGIDevice2;
#endif /* __cplusplus */
#endif

#ifndef __IDXGISwapChain1_FWD_DEFINED__
#define __IDXGISwapChain1_FWD_DEFINED__
typedef interface IDXGISwapChain1 IDXGISwapChain1;
#ifdef __cplusplus
interface IDXGISwapChain1;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactory2_FWD_DEFINED__
#define __IDXGIFactory2_FWD_DEFINED__
typedef interface IDXGIFactory2 IDXGIFactory2;
#ifdef __cplusplus
interface IDXGIFactory2;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIAdapter2_FWD_DEFINED__
#define __IDXGIAdapter2_FWD_DEFINED__
typedef interface IDXGIAdapter2 IDXGIAdapter2;
#ifdef __cplusplus
interface IDXGIAdapter2;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIOutput1_FWD_DEFINED__
#define __IDXGIOutput1_FWD_DEFINED__
typedef interface IDXGIOutput1 IDXGIOutput1;
#ifdef __cplusplus
interface IDXGIOutput1;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <dxgi.h>

#ifdef __cplusplus
extern "C" {
#endif

#define DXGI_ENUM_MODES_STEREO (0x4)

#define DXGI_ENUM_MODES_DISABLED_STEREO (0x8)

#define DXGI_SHARED_RESOURCE_READ (0x80000000)

#define DXGI_SHARED_RESOURCE_WRITE (0x1)

typedef enum _DXGI_OFFER_RESOURCE_PRIORITY {
    DXGI_OFFER_RESOURCE_PRIORITY_LOW = 1,
    DXGI_OFFER_RESOURCE_PRIORITY_NORMAL = 2,
    DXGI_OFFER_RESOURCE_PRIORITY_HIGH = 3
} DXGI_OFFER_RESOURCE_PRIORITY;
typedef enum DXGI_ALPHA_MODE {
    DXGI_ALPHA_MODE_UNSPECIFIED = 0,
    DXGI_ALPHA_MODE_PREMULTIPLIED = 1,
    DXGI_ALPHA_MODE_STRAIGHT = 2,
    DXGI_ALPHA_MODE_IGNORE = 3,
    DXGI_ALPHA_MODE_FORCE_DWORD = 0xffffffff
} DXGI_ALPHA_MODE;
typedef struct DXGI_OUTDUPL_MOVE_RECT {
    POINT SourcePoint;
    RECT DestinationRect;
} DXGI_OUTDUPL_MOVE_RECT;
typedef struct DXGI_OUTDUPL_DESC {
    DXGI_MODE_DESC ModeDesc;
    DXGI_MODE_ROTATION Rotation;
    WINBOOL DesktopImageInSystemMemory;
} DXGI_OUTDUPL_DESC;
typedef struct DXGI_OUTDUPL_POINTER_POSITION {
    POINT Position;
    WINBOOL Visible;
} DXGI_OUTDUPL_POINTER_POSITION;
typedef enum DXGI_OUTDUPL_POINTER_SHAPE_TYPE {
    DXGI_OUTDUPL_POINTER_SHAPE_TYPE_MONOCHROME = 0x1,
    DXGI_OUTDUPL_POINTER_SHAPE_TYPE_COLOR = 0x2,
    DXGI_OUTDUPL_POINTER_SHAPE_TYPE_MASKED_COLOR = 0x4
} DXGI_OUTDUPL_POINTER_SHAPE_TYPE;
typedef struct DXGI_OUTDUPL_POINTER_SHAPE_INFO {
    UINT Type;
    UINT Width;
    UINT Height;
    UINT Pitch;
    POINT HotSpot;
} DXGI_OUTDUPL_POINTER_SHAPE_INFO;
typedef struct DXGI_OUTDUPL_FRAME_INFO {
    LARGE_INTEGER LastPresentTime;
    LARGE_INTEGER LastMouseUpdateTime;
    UINT AccumulatedFrames;
    WINBOOL RectsCoalesced;
    WINBOOL ProtectedContentMaskedOut;
    DXGI_OUTDUPL_POINTER_POSITION PointerPosition;
    UINT TotalMetadataBufferSize;
    UINT PointerShapeBufferSize;
} DXGI_OUTDUPL_FRAME_INFO;
typedef struct DXGI_MODE_DESC1 {
    UINT Width;
    UINT Height;
    DXGI_RATIONAL RefreshRate;
    DXGI_FORMAT Format;
    DXGI_MODE_SCANLINE_ORDER ScanlineOrdering;
    DXGI_MODE_SCALING Scaling;
    WINBOOL Stereo;
} DXGI_MODE_DESC1;
/*****************************************************************************
 * IDXGIOutputDuplication interface
 */
#ifndef __IDXGIOutputDuplication_INTERFACE_DEFINED__
#define __IDXGIOutputDuplication_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIOutputDuplication, 0x191cfac3, 0xa341, 0x470d, 0xb2,0x6e, 0xa8,0x64,0xf4,0x28,0x31,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("191cfac3-a341-470d-b26e-a864f428319c")
IDXGIOutputDuplication : public IDXGIObject
{
    virtual void STDMETHODCALLTYPE GetDesc(
        DXGI_OUTDUPL_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE AcquireNextFrame(
        UINT timeout_in_milliseconds,
        DXGI_OUTDUPL_FRAME_INFO *frame_info,
        IDXGIResource **desktop_resource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFrameDirtyRects(
        UINT dirty_rects_buffer_size,
        RECT *dirty_rects_buffer,
        UINT *dirty_rects_buffer_size_required) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFrameMoveRects(
        UINT move_rects_buffer_size,
        DXGI_OUTDUPL_MOVE_RECT *move_rect_buffer,
        UINT *move_rects_buffer_size_required) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFramePointerShape(
        UINT pointer_shape_buffer_size,
        void *pointer_shape_buffer,
        UINT *pointer_shape_buffer_size_required,
        DXGI_OUTDUPL_POINTER_SHAPE_INFO *pointer_shape_info) = 0;

    virtual HRESULT STDMETHODCALLTYPE MapDesktopSurface(
        DXGI_MAPPED_RECT *locked_rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnMapDesktopSurface(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseFrame(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIOutputDuplication, 0x191cfac3, 0xa341, 0x470d, 0xb2,0x6e, 0xa8,0x64,0xf4,0x28,0x31,0x9c)
#endif
#else
typedef struct IDXGIOutputDuplicationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIOutputDuplication *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIOutputDuplication *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIOutputDuplication *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIOutputDuplication *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIOutputDuplication *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIOutputDuplication *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIOutputDuplication *This,
        REFIID riid,
        void **parent);

    /*** IDXGIOutputDuplication methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        IDXGIOutputDuplication *This,
        DXGI_OUTDUPL_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *AcquireNextFrame)(
        IDXGIOutputDuplication *This,
        UINT timeout_in_milliseconds,
        DXGI_OUTDUPL_FRAME_INFO *frame_info,
        IDXGIResource **desktop_resource);

    HRESULT (STDMETHODCALLTYPE *GetFrameDirtyRects)(
        IDXGIOutputDuplication *This,
        UINT dirty_rects_buffer_size,
        RECT *dirty_rects_buffer,
        UINT *dirty_rects_buffer_size_required);

    HRESULT (STDMETHODCALLTYPE *GetFrameMoveRects)(
        IDXGIOutputDuplication *This,
        UINT move_rects_buffer_size,
        DXGI_OUTDUPL_MOVE_RECT *move_rect_buffer,
        UINT *move_rects_buffer_size_required);

    HRESULT (STDMETHODCALLTYPE *GetFramePointerShape)(
        IDXGIOutputDuplication *This,
        UINT pointer_shape_buffer_size,
        void *pointer_shape_buffer,
        UINT *pointer_shape_buffer_size_required,
        DXGI_OUTDUPL_POINTER_SHAPE_INFO *pointer_shape_info);

    HRESULT (STDMETHODCALLTYPE *MapDesktopSurface)(
        IDXGIOutputDuplication *This,
        DXGI_MAPPED_RECT *locked_rect);

    HRESULT (STDMETHODCALLTYPE *UnMapDesktopSurface)(
        IDXGIOutputDuplication *This);

    HRESULT (STDMETHODCALLTYPE *ReleaseFrame)(
        IDXGIOutputDuplication *This);

    END_INTERFACE
} IDXGIOutputDuplicationVtbl;

interface IDXGIOutputDuplication {
    CONST_VTBL IDXGIOutputDuplicationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIOutputDuplication_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIOutputDuplication_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIOutputDuplication_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIOutputDuplication_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIOutputDuplication_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIOutputDuplication_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIOutputDuplication_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIOutputDuplication methods ***/
#define IDXGIOutputDuplication_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIOutputDuplication_AcquireNextFrame(This,timeout_in_milliseconds,frame_info,desktop_resource) (This)->lpVtbl->AcquireNextFrame(This,timeout_in_milliseconds,frame_info,desktop_resource)
#define IDXGIOutputDuplication_GetFrameDirtyRects(This,dirty_rects_buffer_size,dirty_rects_buffer,dirty_rects_buffer_size_required) (This)->lpVtbl->GetFrameDirtyRects(This,dirty_rects_buffer_size,dirty_rects_buffer,dirty_rects_buffer_size_required)
#define IDXGIOutputDuplication_GetFrameMoveRects(This,move_rects_buffer_size,move_rect_buffer,move_rects_buffer_size_required) (This)->lpVtbl->GetFrameMoveRects(This,move_rects_buffer_size,move_rect_buffer,move_rects_buffer_size_required)
#define IDXGIOutputDuplication_GetFramePointerShape(This,pointer_shape_buffer_size,pointer_shape_buffer,pointer_shape_buffer_size_required,pointer_shape_info) (This)->lpVtbl->GetFramePointerShape(This,pointer_shape_buffer_size,pointer_shape_buffer,pointer_shape_buffer_size_required,pointer_shape_info)
#define IDXGIOutputDuplication_MapDesktopSurface(This,locked_rect) (This)->lpVtbl->MapDesktopSurface(This,locked_rect)
#define IDXGIOutputDuplication_UnMapDesktopSurface(This) (This)->lpVtbl->UnMapDesktopSurface(This)
#define IDXGIOutputDuplication_ReleaseFrame(This) (This)->lpVtbl->ReleaseFrame(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGIOutputDuplication_QueryInterface(IDXGIOutputDuplication* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGIOutputDuplication_AddRef(IDXGIOutputDuplication* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGIOutputDuplication_Release(IDXGIOutputDuplication* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static FORCEINLINE HRESULT IDXGIOutputDuplication_SetPrivateData(IDXGIOutputDuplication* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_SetPrivateDataInterface(IDXGIOutputDuplication* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_GetPrivateData(IDXGIOutputDuplication* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_GetParent(IDXGIOutputDuplication* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIOutputDuplication methods ***/
static FORCEINLINE void IDXGIOutputDuplication_GetDesc(IDXGIOutputDuplication* This,DXGI_OUTDUPL_DESC *desc) {
    This->lpVtbl->GetDesc(This,desc);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_AcquireNextFrame(IDXGIOutputDuplication* This,UINT timeout_in_milliseconds,DXGI_OUTDUPL_FRAME_INFO *frame_info,IDXGIResource **desktop_resource) {
    return This->lpVtbl->AcquireNextFrame(This,timeout_in_milliseconds,frame_info,desktop_resource);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_GetFrameDirtyRects(IDXGIOutputDuplication* This,UINT dirty_rects_buffer_size,RECT *dirty_rects_buffer,UINT *dirty_rects_buffer_size_required) {
    return This->lpVtbl->GetFrameDirtyRects(This,dirty_rects_buffer_size,dirty_rects_buffer,dirty_rects_buffer_size_required);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_GetFrameMoveRects(IDXGIOutputDuplication* This,UINT move_rects_buffer_size,DXGI_OUTDUPL_MOVE_RECT *move_rect_buffer,UINT *move_rects_buffer_size_required) {
    return This->lpVtbl->GetFrameMoveRects(This,move_rects_buffer_size,move_rect_buffer,move_rects_buffer_size_required);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_GetFramePointerShape(IDXGIOutputDuplication* This,UINT pointer_shape_buffer_size,void *pointer_shape_buffer,UINT *pointer_shape_buffer_size_required,DXGI_OUTDUPL_POINTER_SHAPE_INFO *pointer_shape_info) {
    return This->lpVtbl->GetFramePointerShape(This,pointer_shape_buffer_size,pointer_shape_buffer,pointer_shape_buffer_size_required,pointer_shape_info);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_MapDesktopSurface(IDXGIOutputDuplication* This,DXGI_MAPPED_RECT *locked_rect) {
    return This->lpVtbl->MapDesktopSurface(This,locked_rect);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_UnMapDesktopSurface(IDXGIOutputDuplication* This) {
    return This->lpVtbl->UnMapDesktopSurface(This);
}
static FORCEINLINE HRESULT IDXGIOutputDuplication_ReleaseFrame(IDXGIOutputDuplication* This) {
    return This->lpVtbl->ReleaseFrame(This);
}
#endif
#endif

#endif


#endif  /* __IDXGIOutputDuplication_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGISurface2 interface
 */
#ifndef __IDXGISurface2_INTERFACE_DEFINED__
#define __IDXGISurface2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISurface2, 0xaba496dd, 0xb617, 0x4cb8, 0xa8,0x66, 0xbc,0x44,0xd7,0xeb,0x1f,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aba496dd-b617-4cb8-a866-bc44d7eb1fa2")
IDXGISurface2 : public IDXGISurface1
{
    virtual HRESULT STDMETHODCALLTYPE GetResource(
        REFIID iid,
        void **parent_resource,
        UINT *subresource_idx) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISurface2, 0xaba496dd, 0xb617, 0x4cb8, 0xa8,0x66, 0xbc,0x44,0xd7,0xeb,0x1f,0xa2)
#endif
#else
typedef struct IDXGISurface2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISurface2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISurface2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISurface2 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGISurface2 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGISurface2 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGISurface2 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGISurface2 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGISurface2 *This,
        REFIID riid,
        void **device);

    /*** IDXGISurface methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGISurface2 *This,
        DXGI_SURFACE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *Map)(
        IDXGISurface2 *This,
        DXGI_MAPPED_RECT *mapped_rect,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *Unmap)(
        IDXGISurface2 *This);

    /*** IDXGISurface1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDC)(
        IDXGISurface2 *This,
        WINBOOL discard,
        HDC *hdc);

    HRESULT (STDMETHODCALLTYPE *ReleaseDC)(
        IDXGISurface2 *This,
        RECT *dirty_rect);

    /*** IDXGISurface2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetResource)(
        IDXGISurface2 *This,
        REFIID iid,
        void **parent_resource,
        UINT *subresource_idx);

    END_INTERFACE
} IDXGISurface2Vtbl;

interface IDXGISurface2 {
    CONST_VTBL IDXGISurface2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISurface2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISurface2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISurface2_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGISurface2_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGISurface2_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGISurface2_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGISurface2_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGISurface2_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGISurface methods ***/
#define IDXGISurface2_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGISurface2_Map(This,mapped_rect,flags) (This)->lpVtbl->Map(This,mapped_rect,flags)
#define IDXGISurface2_Unmap(This) (This)->lpVtbl->Unmap(This)
/*** IDXGISurface1 methods ***/
#define IDXGISurface2_GetDC(This,discard,hdc) (This)->lpVtbl->GetDC(This,discard,hdc)
#define IDXGISurface2_ReleaseDC(This,dirty_rect) (This)->lpVtbl->ReleaseDC(This,dirty_rect)
/*** IDXGISurface2 methods ***/
#define IDXGISurface2_GetResource(This,iid,parent_resource,subresource_idx) (This)->lpVtbl->GetResource(This,iid,parent_resource,subresource_idx)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGISurface2_QueryInterface(IDXGISurface2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGISurface2_AddRef(IDXGISurface2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGISurface2_Release(IDXGISurface2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static FORCEINLINE HRESULT IDXGISurface2_SetPrivateData(IDXGISurface2* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGISurface2_SetPrivateDataInterface(IDXGISurface2* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static FORCEINLINE HRESULT IDXGISurface2_GetPrivateData(IDXGISurface2* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGISurface2_GetParent(IDXGISurface2* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static FORCEINLINE HRESULT IDXGISurface2_GetDevice(IDXGISurface2* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGISurface methods ***/
static FORCEINLINE HRESULT IDXGISurface2_GetDesc(IDXGISurface2* This,DXGI_SURFACE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static FORCEINLINE HRESULT IDXGISurface2_Map(IDXGISurface2* This,DXGI_MAPPED_RECT *mapped_rect,UINT flags) {
    return This->lpVtbl->Map(This,mapped_rect,flags);
}
static FORCEINLINE HRESULT IDXGISurface2_Unmap(IDXGISurface2* This) {
    return This->lpVtbl->Unmap(This);
}
/*** IDXGISurface1 methods ***/
static FORCEINLINE HRESULT IDXGISurface2_GetDC(IDXGISurface2* This,WINBOOL discard,HDC *hdc) {
    return This->lpVtbl->GetDC(This,discard,hdc);
}
static FORCEINLINE HRESULT IDXGISurface2_ReleaseDC(IDXGISurface2* This,RECT *dirty_rect) {
    return This->lpVtbl->ReleaseDC(This,dirty_rect);
}
/*** IDXGISurface2 methods ***/
static FORCEINLINE HRESULT IDXGISurface2_GetResource(IDXGISurface2* This,REFIID iid,void **parent_resource,UINT *subresource_idx) {
    return This->lpVtbl->GetResource(This,iid,parent_resource,subresource_idx);
}
#endif
#endif

#endif


#endif  /* __IDXGISurface2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIResource1 interface
 */
#ifndef __IDXGIResource1_INTERFACE_DEFINED__
#define __IDXGIResource1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIResource1, 0x30961379, 0x4609, 0x4a41, 0x99,0x8e, 0x54,0xfe,0x56,0x7e,0xe0,0xc1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("*************-4a41-998e-54fe567ee0c1")
IDXGIResource1 : public IDXGIResource
{
    virtual HRESULT STDMETHODCALLTYPE CreateSubresourceSurface(
        UINT index,
        IDXGISurface2 **surface) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSharedHandle(
        const SECURITY_ATTRIBUTES *attributes,
        DWORD access,
        const WCHAR *name,
        HANDLE *handle) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIResource1, 0x30961379, 0x4609, 0x4a41, 0x99,0x8e, 0x54,0xfe,0x56,0x7e,0xe0,0xc1)
#endif
#else
typedef struct IDXGIResource1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIResource1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIResource1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIResource1 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIResource1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIResource1 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIResource1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIResource1 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGIResource1 *This,
        REFIID riid,
        void **device);

    /*** IDXGIResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSharedHandle)(
        IDXGIResource1 *This,
        HANDLE *pSharedHandle);

    HRESULT (STDMETHODCALLTYPE *GetUsage)(
        IDXGIResource1 *This,
        DXGI_USAGE *pUsage);

    HRESULT (STDMETHODCALLTYPE *SetEvictionPriority)(
        IDXGIResource1 *This,
        UINT EvictionPriority);

    HRESULT (STDMETHODCALLTYPE *GetEvictionPriority)(
        IDXGIResource1 *This,
        UINT *pEvictionPriority);

    /*** IDXGIResource1 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateSubresourceSurface)(
        IDXGIResource1 *This,
        UINT index,
        IDXGISurface2 **surface);

    HRESULT (STDMETHODCALLTYPE *CreateSharedHandle)(
        IDXGIResource1 *This,
        const SECURITY_ATTRIBUTES *attributes,
        DWORD access,
        const WCHAR *name,
        HANDLE *handle);

    END_INTERFACE
} IDXGIResource1Vtbl;

interface IDXGIResource1 {
    CONST_VTBL IDXGIResource1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIResource1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIResource1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIResource1_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIResource1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIResource1_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIResource1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIResource1_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGIResource1_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGIResource methods ***/
#define IDXGIResource1_GetSharedHandle(This,pSharedHandle) (This)->lpVtbl->GetSharedHandle(This,pSharedHandle)
#define IDXGIResource1_GetUsage(This,pUsage) (This)->lpVtbl->GetUsage(This,pUsage)
#define IDXGIResource1_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define IDXGIResource1_GetEvictionPriority(This,pEvictionPriority) (This)->lpVtbl->GetEvictionPriority(This,pEvictionPriority)
/*** IDXGIResource1 methods ***/
#define IDXGIResource1_CreateSubresourceSurface(This,index,surface) (This)->lpVtbl->CreateSubresourceSurface(This,index,surface)
#define IDXGIResource1_CreateSharedHandle(This,attributes,access,name,handle) (This)->lpVtbl->CreateSharedHandle(This,attributes,access,name,handle)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGIResource1_QueryInterface(IDXGIResource1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGIResource1_AddRef(IDXGIResource1* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGIResource1_Release(IDXGIResource1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static FORCEINLINE HRESULT IDXGIResource1_SetPrivateData(IDXGIResource1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIResource1_SetPrivateDataInterface(IDXGIResource1* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static FORCEINLINE HRESULT IDXGIResource1_GetPrivateData(IDXGIResource1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIResource1_GetParent(IDXGIResource1* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static FORCEINLINE HRESULT IDXGIResource1_GetDevice(IDXGIResource1* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGIResource methods ***/
static FORCEINLINE HRESULT IDXGIResource1_GetSharedHandle(IDXGIResource1* This,HANDLE *pSharedHandle) {
    return This->lpVtbl->GetSharedHandle(This,pSharedHandle);
}
static FORCEINLINE HRESULT IDXGIResource1_GetUsage(IDXGIResource1* This,DXGI_USAGE *pUsage) {
    return This->lpVtbl->GetUsage(This,pUsage);
}
static FORCEINLINE HRESULT IDXGIResource1_SetEvictionPriority(IDXGIResource1* This,UINT EvictionPriority) {
    return This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static FORCEINLINE HRESULT IDXGIResource1_GetEvictionPriority(IDXGIResource1* This,UINT *pEvictionPriority) {
    return This->lpVtbl->GetEvictionPriority(This,pEvictionPriority);
}
/*** IDXGIResource1 methods ***/
static FORCEINLINE HRESULT IDXGIResource1_CreateSubresourceSurface(IDXGIResource1* This,UINT index,IDXGISurface2 **surface) {
    return This->lpVtbl->CreateSubresourceSurface(This,index,surface);
}
static FORCEINLINE HRESULT IDXGIResource1_CreateSharedHandle(IDXGIResource1* This,const SECURITY_ATTRIBUTES *attributes,DWORD access,const WCHAR *name,HANDLE *handle) {
    return This->lpVtbl->CreateSharedHandle(This,attributes,access,name,handle);
}
#endif
#endif

#endif


#endif  /* __IDXGIResource1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIDisplayControl interface
 */
#ifndef __IDXGIDisplayControl_INTERFACE_DEFINED__
#define __IDXGIDisplayControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIDisplayControl, 0xea9dbf1a, 0xc88e, 0x4486, 0x85,0x4a, 0x98,0xaa,0x01,0x38,0xf3,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ea9dbf1a-c88e-4486-854a-98aa0138f30c")
IDXGIDisplayControl : public IUnknown
{
    virtual WINBOOL STDMETHODCALLTYPE IsStereoEnabled(
        ) = 0;

    virtual void STDMETHODCALLTYPE SetStereoEnabled(
        WINBOOL enabled) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIDisplayControl, 0xea9dbf1a, 0xc88e, 0x4486, 0x85,0x4a, 0x98,0xaa,0x01,0x38,0xf3,0x0c)
#endif
#else
typedef struct IDXGIDisplayControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIDisplayControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIDisplayControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIDisplayControl *This);

    /*** IDXGIDisplayControl methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsStereoEnabled)(
        IDXGIDisplayControl *This);

    void (STDMETHODCALLTYPE *SetStereoEnabled)(
        IDXGIDisplayControl *This,
        WINBOOL enabled);

    END_INTERFACE
} IDXGIDisplayControlVtbl;

interface IDXGIDisplayControl {
    CONST_VTBL IDXGIDisplayControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIDisplayControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIDisplayControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIDisplayControl_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIDisplayControl methods ***/
#define IDXGIDisplayControl_IsStereoEnabled(This) (This)->lpVtbl->IsStereoEnabled(This)
#define IDXGIDisplayControl_SetStereoEnabled(This,enabled) (This)->lpVtbl->SetStereoEnabled(This,enabled)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGIDisplayControl_QueryInterface(IDXGIDisplayControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGIDisplayControl_AddRef(IDXGIDisplayControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGIDisplayControl_Release(IDXGIDisplayControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIDisplayControl methods ***/
static FORCEINLINE WINBOOL IDXGIDisplayControl_IsStereoEnabled(IDXGIDisplayControl* This) {
    return This->lpVtbl->IsStereoEnabled(This);
}
static FORCEINLINE void IDXGIDisplayControl_SetStereoEnabled(IDXGIDisplayControl* This,WINBOOL enabled) {
    This->lpVtbl->SetStereoEnabled(This,enabled);
}
#endif
#endif

#endif


#endif  /* __IDXGIDisplayControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIDevice2 interface
 */
#ifndef __IDXGIDevice2_INTERFACE_DEFINED__
#define __IDXGIDevice2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIDevice2, 0x05008617, 0xfbfd, 0x4051, 0xa7,0x90, 0x14,0x48,0x84,0xb4,0xf6,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("05008617-fbfd-4051-a790-144884b4f6a9")
IDXGIDevice2 : public IDXGIDevice1
{
    virtual HRESULT STDMETHODCALLTYPE OfferResources(
        UINT NumResources,
        IDXGIResource *const *ppResources,
        DXGI_OFFER_RESOURCE_PRIORITY Priority) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReclaimResources(
        UINT NumResources,
        IDXGIResource *const *ppResources,
        WINBOOL *pDiscarded) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnqueueSetEvent(
        HANDLE hEvent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIDevice2, 0x05008617, 0xfbfd, 0x4051, 0xa7,0x90, 0x14,0x48,0x84,0xb4,0xf6,0xa9)
#endif
#else
typedef struct IDXGIDevice2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIDevice2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIDevice2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIDevice2 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIDevice2 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIDevice2 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIDevice2 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIDevice2 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAdapter)(
        IDXGIDevice2 *This,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *CreateSurface)(
        IDXGIDevice2 *This,
        const DXGI_SURFACE_DESC *desc,
        UINT surface_count,
        DXGI_USAGE usage,
        const DXGI_SHARED_RESOURCE *shared_resource,
        IDXGISurface **surface);

    HRESULT (STDMETHODCALLTYPE *QueryResourceResidency)(
        IDXGIDevice2 *This,
        IUnknown *const *resources,
        DXGI_RESIDENCY *residency,
        UINT resource_count);

    HRESULT (STDMETHODCALLTYPE *SetGPUThreadPriority)(
        IDXGIDevice2 *This,
        INT priority);

    HRESULT (STDMETHODCALLTYPE *GetGPUThreadPriority)(
        IDXGIDevice2 *This,
        INT *priority);

    /*** IDXGIDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMaximumFrameLatency)(
        IDXGIDevice2 *This,
        UINT MaxLatency);

    HRESULT (STDMETHODCALLTYPE *GetMaximumFrameLatency)(
        IDXGIDevice2 *This,
        UINT *pMaxLatency);

    /*** IDXGIDevice2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OfferResources)(
        IDXGIDevice2 *This,
        UINT NumResources,
        IDXGIResource *const *ppResources,
        DXGI_OFFER_RESOURCE_PRIORITY Priority);

    HRESULT (STDMETHODCALLTYPE *ReclaimResources)(
        IDXGIDevice2 *This,
        UINT NumResources,
        IDXGIResource *const *ppResources,
        WINBOOL *pDiscarded);

    HRESULT (STDMETHODCALLTYPE *EnqueueSetEvent)(
        IDXGIDevice2 *This,
        HANDLE hEvent);

    END_INTERFACE
} IDXGIDevice2Vtbl;

interface IDXGIDevice2 {
    CONST_VTBL IDXGIDevice2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIDevice2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIDevice2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIDevice2_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIDevice2_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIDevice2_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIDevice2_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIDevice2_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDevice methods ***/
#define IDXGIDevice2_GetAdapter(This,adapter) (This)->lpVtbl->GetAdapter(This,adapter)
#define IDXGIDevice2_CreateSurface(This,desc,surface_count,usage,shared_resource,surface) (This)->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface)
#define IDXGIDevice2_QueryResourceResidency(This,resources,residency,resource_count) (This)->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count)
#define IDXGIDevice2_SetGPUThreadPriority(This,priority) (This)->lpVtbl->SetGPUThreadPriority(This,priority)
#define IDXGIDevice2_GetGPUThreadPriority(This,priority) (This)->lpVtbl->GetGPUThreadPriority(This,priority)
/*** IDXGIDevice1 methods ***/
#define IDXGIDevice2_SetMaximumFrameLatency(This,MaxLatency) (This)->lpVtbl->SetMaximumFrameLatency(This,MaxLatency)
#define IDXGIDevice2_GetMaximumFrameLatency(This,pMaxLatency) (This)->lpVtbl->GetMaximumFrameLatency(This,pMaxLatency)
/*** IDXGIDevice2 methods ***/
#define IDXGIDevice2_OfferResources(This,NumResources,ppResources,Priority) (This)->lpVtbl->OfferResources(This,NumResources,ppResources,Priority)
#define IDXGIDevice2_ReclaimResources(This,NumResources,ppResources,pDiscarded) (This)->lpVtbl->ReclaimResources(This,NumResources,ppResources,pDiscarded)
#define IDXGIDevice2_EnqueueSetEvent(This,hEvent) (This)->lpVtbl->EnqueueSetEvent(This,hEvent)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGIDevice2_QueryInterface(IDXGIDevice2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGIDevice2_AddRef(IDXGIDevice2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGIDevice2_Release(IDXGIDevice2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static FORCEINLINE HRESULT IDXGIDevice2_SetPrivateData(IDXGIDevice2* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIDevice2_SetPrivateDataInterface(IDXGIDevice2* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static FORCEINLINE HRESULT IDXGIDevice2_GetPrivateData(IDXGIDevice2* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIDevice2_GetParent(IDXGIDevice2* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDevice methods ***/
static FORCEINLINE HRESULT IDXGIDevice2_GetAdapter(IDXGIDevice2* This,IDXGIAdapter **adapter) {
    return This->lpVtbl->GetAdapter(This,adapter);
}
static FORCEINLINE HRESULT IDXGIDevice2_CreateSurface(IDXGIDevice2* This,const DXGI_SURFACE_DESC *desc,UINT surface_count,DXGI_USAGE usage,const DXGI_SHARED_RESOURCE *shared_resource,IDXGISurface **surface) {
    return This->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface);
}
static FORCEINLINE HRESULT IDXGIDevice2_QueryResourceResidency(IDXGIDevice2* This,IUnknown *const *resources,DXGI_RESIDENCY *residency,UINT resource_count) {
    return This->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count);
}
static FORCEINLINE HRESULT IDXGIDevice2_SetGPUThreadPriority(IDXGIDevice2* This,INT priority) {
    return This->lpVtbl->SetGPUThreadPriority(This,priority);
}
static FORCEINLINE HRESULT IDXGIDevice2_GetGPUThreadPriority(IDXGIDevice2* This,INT *priority) {
    return This->lpVtbl->GetGPUThreadPriority(This,priority);
}
/*** IDXGIDevice1 methods ***/
static FORCEINLINE HRESULT IDXGIDevice2_SetMaximumFrameLatency(IDXGIDevice2* This,UINT MaxLatency) {
    return This->lpVtbl->SetMaximumFrameLatency(This,MaxLatency);
}
static FORCEINLINE HRESULT IDXGIDevice2_GetMaximumFrameLatency(IDXGIDevice2* This,UINT *pMaxLatency) {
    return This->lpVtbl->GetMaximumFrameLatency(This,pMaxLatency);
}
/*** IDXGIDevice2 methods ***/
static FORCEINLINE HRESULT IDXGIDevice2_OfferResources(IDXGIDevice2* This,UINT NumResources,IDXGIResource *const *ppResources,DXGI_OFFER_RESOURCE_PRIORITY Priority) {
    return This->lpVtbl->OfferResources(This,NumResources,ppResources,Priority);
}
static FORCEINLINE HRESULT IDXGIDevice2_ReclaimResources(IDXGIDevice2* This,UINT NumResources,IDXGIResource *const *ppResources,WINBOOL *pDiscarded) {
    return This->lpVtbl->ReclaimResources(This,NumResources,ppResources,pDiscarded);
}
static FORCEINLINE HRESULT IDXGIDevice2_EnqueueSetEvent(IDXGIDevice2* This,HANDLE hEvent) {
    return This->lpVtbl->EnqueueSetEvent(This,hEvent);
}
#endif
#endif

#endif


#endif  /* __IDXGIDevice2_INTERFACE_DEFINED__ */

typedef enum DXGI_SCALING {
    DXGI_SCALING_STRETCH = 0,
    DXGI_SCALING_NONE = 1
} DXGI_SCALING;
typedef struct DXGI_SWAP_CHAIN_DESC1 {
    UINT Width;
    UINT Height;
    DXGI_FORMAT Format;
    WINBOOL Stereo;
    DXGI_SAMPLE_DESC SampleDesc;
    DXGI_USAGE BufferUsage;
    UINT BufferCount;
    DXGI_SCALING Scaling;
    DXGI_SWAP_EFFECT SwapEffect;
    DXGI_ALPHA_MODE AlphaMode;
    UINT Flags;
} DXGI_SWAP_CHAIN_DESC1;
typedef struct DXGI_SWAP_CHAIN_FULLSCREEN_DESC {
    DXGI_RATIONAL RefreshRate;
    DXGI_MODE_SCANLINE_ORDER ScanlineOrdering;
    DXGI_MODE_SCALING Scaling;
    WINBOOL Windowed;
} DXGI_SWAP_CHAIN_FULLSCREEN_DESC;
typedef struct DXGI_PRESENT_PARAMETERS {
    UINT DirtyRectsCount;
    RECT *pDirtyRects;
    RECT *pScrollRect;
    POINT *pScrollOffset;
} DXGI_PRESENT_PARAMETERS;
/*****************************************************************************
 * IDXGISwapChain1 interface
 */
#ifndef __IDXGISwapChain1_INTERFACE_DEFINED__
#define __IDXGISwapChain1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISwapChain1, 0x790a45f7, 0x0d42, 0x4876, 0x98,0x3a, 0x0a,0x55,0xcf,0xe6,0xf4,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("790a45f7-0d42-4876-983a-0a55cfe6f4aa")
IDXGISwapChain1 : public IDXGISwapChain
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc1(
        DXGI_SWAP_CHAIN_DESC1 *pDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFullscreenDesc(
        DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHwnd(
        HWND *pHwnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCoreWindow(
        REFIID refiid,
        void **ppUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Present1(
        UINT SyncInterval,
        UINT PresentFlags,
        const DXGI_PRESENT_PARAMETERS *pPresentParameters) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsTemporaryMonoSupported(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestrictToOutput(
        IDXGIOutput **ppRestrictToOutput) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackgroundColor(
        const DXGI_RGBA *pColor) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackgroundColor(
        DXGI_RGBA *pColor) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRotation(
        DXGI_MODE_ROTATION Rotation) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRotation(
        DXGI_MODE_ROTATION *pRotation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISwapChain1, 0x790a45f7, 0x0d42, 0x4876, 0x98,0x3a, 0x0a,0x55,0xcf,0xe6,0xf4,0xaa)
#endif
#else
typedef struct IDXGISwapChain1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISwapChain1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISwapChain1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISwapChain1 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGISwapChain1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGISwapChain1 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGISwapChain1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGISwapChain1 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGISwapChain1 *This,
        REFIID riid,
        void **device);

    /*** IDXGISwapChain methods ***/
    HRESULT (STDMETHODCALLTYPE *Present)(
        IDXGISwapChain1 *This,
        UINT sync_interval,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IDXGISwapChain1 *This,
        UINT buffer_idx,
        REFIID riid,
        void **surface);

    HRESULT (STDMETHODCALLTYPE *SetFullscreenState)(
        IDXGISwapChain1 *This,
        WINBOOL fullscreen,
        IDXGIOutput *target);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenState)(
        IDXGISwapChain1 *This,
        WINBOOL *fullscreen,
        IDXGIOutput **target);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGISwapChain1 *This,
        DXGI_SWAP_CHAIN_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *ResizeBuffers)(
        IDXGISwapChain1 *This,
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ResizeTarget)(
        IDXGISwapChain1 *This,
        const DXGI_MODE_DESC *target_mode_desc);

    HRESULT (STDMETHODCALLTYPE *GetContainingOutput)(
        IDXGISwapChain1 *This,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGISwapChain1 *This,
        DXGI_FRAME_STATISTICS *stats);

    HRESULT (STDMETHODCALLTYPE *GetLastPresentCount)(
        IDXGISwapChain1 *This,
        UINT *last_present_count);

    /*** IDXGISwapChain1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGISwapChain1 *This,
        DXGI_SWAP_CHAIN_DESC1 *pDesc);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenDesc)(
        IDXGISwapChain1 *This,
        DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc);

    HRESULT (STDMETHODCALLTYPE *GetHwnd)(
        IDXGISwapChain1 *This,
        HWND *pHwnd);

    HRESULT (STDMETHODCALLTYPE *GetCoreWindow)(
        IDXGISwapChain1 *This,
        REFIID refiid,
        void **ppUnk);

    HRESULT (STDMETHODCALLTYPE *Present1)(
        IDXGISwapChain1 *This,
        UINT SyncInterval,
        UINT PresentFlags,
        const DXGI_PRESENT_PARAMETERS *pPresentParameters);

    WINBOOL (STDMETHODCALLTYPE *IsTemporaryMonoSupported)(
        IDXGISwapChain1 *This);

    HRESULT (STDMETHODCALLTYPE *GetRestrictToOutput)(
        IDXGISwapChain1 *This,
        IDXGIOutput **ppRestrictToOutput);

    HRESULT (STDMETHODCALLTYPE *SetBackgroundColor)(
        IDXGISwapChain1 *This,
        const DXGI_RGBA *pColor);

    HRESULT (STDMETHODCALLTYPE *GetBackgroundColor)(
        IDXGISwapChain1 *This,
        DXGI_RGBA *pColor);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IDXGISwapChain1 *This,
        DXGI_MODE_ROTATION Rotation);

    HRESULT (STDMETHODCALLTYPE *GetRotation)(
        IDXGISwapChain1 *This,
        DXGI_MODE_ROTATION *pRotation);

    END_INTERFACE
} IDXGISwapChain1Vtbl;

interface IDXGISwapChain1 {
    CONST_VTBL IDXGISwapChain1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISwapChain1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISwapChain1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISwapChain1_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGISwapChain1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain1_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGISwapChain1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain1_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGISwapChain1_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGISwapChain methods ***/
#define IDXGISwapChain1_Present(This,sync_interval,flags) (This)->lpVtbl->Present(This,sync_interval,flags)
#define IDXGISwapChain1_GetBuffer(This,buffer_idx,riid,surface) (This)->lpVtbl->GetBuffer(This,buffer_idx,riid,surface)
#define IDXGISwapChain1_SetFullscreenState(This,fullscreen,target) (This)->lpVtbl->SetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain1_GetFullscreenState(This,fullscreen,target) (This)->lpVtbl->GetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain1_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGISwapChain1_ResizeBuffers(This,buffer_count,width,height,format,flags) (This)->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags)
#define IDXGISwapChain1_ResizeTarget(This,target_mode_desc) (This)->lpVtbl->ResizeTarget(This,target_mode_desc)
#define IDXGISwapChain1_GetContainingOutput(This,output) (This)->lpVtbl->GetContainingOutput(This,output)
#define IDXGISwapChain1_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
#define IDXGISwapChain1_GetLastPresentCount(This,last_present_count) (This)->lpVtbl->GetLastPresentCount(This,last_present_count)
/*** IDXGISwapChain1 methods ***/
#define IDXGISwapChain1_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#define IDXGISwapChain1_GetFullscreenDesc(This,pDesc) (This)->lpVtbl->GetFullscreenDesc(This,pDesc)
#define IDXGISwapChain1_GetHwnd(This,pHwnd) (This)->lpVtbl->GetHwnd(This,pHwnd)
#define IDXGISwapChain1_GetCoreWindow(This,refiid,ppUnk) (This)->lpVtbl->GetCoreWindow(This,refiid,ppUnk)
#define IDXGISwapChain1_Present1(This,SyncInterval,PresentFlags,pPresentParameters) (This)->lpVtbl->Present1(This,SyncInterval,PresentFlags,pPresentParameters)
#define IDXGISwapChain1_IsTemporaryMonoSupported(This) (This)->lpVtbl->IsTemporaryMonoSupported(This)
#define IDXGISwapChain1_GetRestrictToOutput(This,ppRestrictToOutput) (This)->lpVtbl->GetRestrictToOutput(This,ppRestrictToOutput)
#define IDXGISwapChain1_SetBackgroundColor(This,pColor) (This)->lpVtbl->SetBackgroundColor(This,pColor)
#define IDXGISwapChain1_GetBackgroundColor(This,pColor) (This)->lpVtbl->GetBackgroundColor(This,pColor)
#define IDXGISwapChain1_SetRotation(This,Rotation) (This)->lpVtbl->SetRotation(This,Rotation)
#define IDXGISwapChain1_GetRotation(This,pRotation) (This)->lpVtbl->GetRotation(This,pRotation)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGISwapChain1_QueryInterface(IDXGISwapChain1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGISwapChain1_AddRef(IDXGISwapChain1* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGISwapChain1_Release(IDXGISwapChain1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static FORCEINLINE HRESULT IDXGISwapChain1_SetPrivateData(IDXGISwapChain1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGISwapChain1_SetPrivateDataInterface(IDXGISwapChain1* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetPrivateData(IDXGISwapChain1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetParent(IDXGISwapChain1* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static FORCEINLINE HRESULT IDXGISwapChain1_GetDevice(IDXGISwapChain1* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGISwapChain methods ***/
static FORCEINLINE HRESULT IDXGISwapChain1_Present(IDXGISwapChain1* This,UINT sync_interval,UINT flags) {
    return This->lpVtbl->Present(This,sync_interval,flags);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetBuffer(IDXGISwapChain1* This,UINT buffer_idx,REFIID riid,void **surface) {
    return This->lpVtbl->GetBuffer(This,buffer_idx,riid,surface);
}
static FORCEINLINE HRESULT IDXGISwapChain1_SetFullscreenState(IDXGISwapChain1* This,WINBOOL fullscreen,IDXGIOutput *target) {
    return This->lpVtbl->SetFullscreenState(This,fullscreen,target);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetFullscreenState(IDXGISwapChain1* This,WINBOOL *fullscreen,IDXGIOutput **target) {
    return This->lpVtbl->GetFullscreenState(This,fullscreen,target);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetDesc(IDXGISwapChain1* This,DXGI_SWAP_CHAIN_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static FORCEINLINE HRESULT IDXGISwapChain1_ResizeBuffers(IDXGISwapChain1* This,UINT buffer_count,UINT width,UINT height,DXGI_FORMAT format,UINT flags) {
    return This->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags);
}
static FORCEINLINE HRESULT IDXGISwapChain1_ResizeTarget(IDXGISwapChain1* This,const DXGI_MODE_DESC *target_mode_desc) {
    return This->lpVtbl->ResizeTarget(This,target_mode_desc);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetContainingOutput(IDXGISwapChain1* This,IDXGIOutput **output) {
    return This->lpVtbl->GetContainingOutput(This,output);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetFrameStatistics(IDXGISwapChain1* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetLastPresentCount(IDXGISwapChain1* This,UINT *last_present_count) {
    return This->lpVtbl->GetLastPresentCount(This,last_present_count);
}
/*** IDXGISwapChain1 methods ***/
static FORCEINLINE HRESULT IDXGISwapChain1_GetDesc1(IDXGISwapChain1* This,DXGI_SWAP_CHAIN_DESC1 *pDesc) {
    return This->lpVtbl->GetDesc1(This,pDesc);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetFullscreenDesc(IDXGISwapChain1* This,DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc) {
    return This->lpVtbl->GetFullscreenDesc(This,pDesc);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetHwnd(IDXGISwapChain1* This,HWND *pHwnd) {
    return This->lpVtbl->GetHwnd(This,pHwnd);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetCoreWindow(IDXGISwapChain1* This,REFIID refiid,void **ppUnk) {
    return This->lpVtbl->GetCoreWindow(This,refiid,ppUnk);
}
static FORCEINLINE HRESULT IDXGISwapChain1_Present1(IDXGISwapChain1* This,UINT SyncInterval,UINT PresentFlags,const DXGI_PRESENT_PARAMETERS *pPresentParameters) {
    return This->lpVtbl->Present1(This,SyncInterval,PresentFlags,pPresentParameters);
}
static FORCEINLINE WINBOOL IDXGISwapChain1_IsTemporaryMonoSupported(IDXGISwapChain1* This) {
    return This->lpVtbl->IsTemporaryMonoSupported(This);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetRestrictToOutput(IDXGISwapChain1* This,IDXGIOutput **ppRestrictToOutput) {
    return This->lpVtbl->GetRestrictToOutput(This,ppRestrictToOutput);
}
static FORCEINLINE HRESULT IDXGISwapChain1_SetBackgroundColor(IDXGISwapChain1* This,const DXGI_RGBA *pColor) {
    return This->lpVtbl->SetBackgroundColor(This,pColor);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetBackgroundColor(IDXGISwapChain1* This,DXGI_RGBA *pColor) {
    return This->lpVtbl->GetBackgroundColor(This,pColor);
}
static FORCEINLINE HRESULT IDXGISwapChain1_SetRotation(IDXGISwapChain1* This,DXGI_MODE_ROTATION Rotation) {
    return This->lpVtbl->SetRotation(This,Rotation);
}
static FORCEINLINE HRESULT IDXGISwapChain1_GetRotation(IDXGISwapChain1* This,DXGI_MODE_ROTATION *pRotation) {
    return This->lpVtbl->GetRotation(This,pRotation);
}
#endif
#endif

#endif


#endif  /* __IDXGISwapChain1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIFactory2 interface
 */
#ifndef __IDXGIFactory2_INTERFACE_DEFINED__
#define __IDXGIFactory2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactory2, 0x50c83a1c, 0xe072, 0x4c48, 0x87,0xb0, 0x36,0x30,0xfa,0x36,0xa6,0xd0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("50c83a1c-e072-4c48-87b0-3630fa36a6d0")
IDXGIFactory2 : public IDXGIFactory1
{
    virtual WINBOOL STDMETHODCALLTYPE IsWindowedStereoEnabled(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSwapChainForHwnd(
        IUnknown *pDevice,
        HWND hWnd,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSwapChainForCoreWindow(
        IUnknown *pDevice,
        IUnknown *pWindow,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSharedResourceAdapterLuid(
        HANDLE hResource,
        LUID *pLuid) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterOcclusionStatusWindow(
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterStereoStatusEvent(
        HANDLE hEvent,
        DWORD *pdwCookie) = 0;

    virtual void STDMETHODCALLTYPE UnregisterStereoStatus(
        DWORD dwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterStereoStatusWindow(
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterOcclusionStatusEvent(
        HANDLE hEvent,
        DWORD *pdwCookie) = 0;

    virtual void STDMETHODCALLTYPE UnregisterOcclusionStatus(
        DWORD dwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSwapChainForComposition(
        IUnknown *pDevice,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactory2, 0x50c83a1c, 0xe072, 0x4c48, 0x87,0xb0, 0x36,0x30,0xfa,0x36,0xa6,0xd0)
#endif
#else
typedef struct IDXGIFactory2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactory2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactory2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactory2 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIFactory2 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIFactory2 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIFactory2 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIFactory2 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters)(
        IDXGIFactory2 *This,
        UINT adapter_idx,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *MakeWindowAssociation)(
        IDXGIFactory2 *This,
        HWND window,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetWindowAssociation)(
        IDXGIFactory2 *This,
        HWND *window);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChain)(
        IDXGIFactory2 *This,
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateSoftwareAdapter)(
        IDXGIFactory2 *This,
        HMODULE swrast,
        IDXGIAdapter **adapter);

    /*** IDXGIFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters1)(
        IDXGIFactory2 *This,
        UINT Adapter,
        IDXGIAdapter1 **ppAdapter);

    WINBOOL (STDMETHODCALLTYPE *IsCurrent)(
        IDXGIFactory2 *This);

    /*** IDXGIFactory2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsWindowedStereoEnabled)(
        IDXGIFactory2 *This);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForHwnd)(
        IDXGIFactory2 *This,
        IUnknown *pDevice,
        HWND hWnd,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForCoreWindow)(
        IDXGIFactory2 *This,
        IUnknown *pDevice,
        IUnknown *pWindow,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *GetSharedResourceAdapterLuid)(
        IDXGIFactory2 *This,
        HANDLE hResource,
        LUID *pLuid);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusWindow)(
        IDXGIFactory2 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusEvent)(
        IDXGIFactory2 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterStereoStatus)(
        IDXGIFactory2 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusWindow)(
        IDXGIFactory2 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusEvent)(
        IDXGIFactory2 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterOcclusionStatus)(
        IDXGIFactory2 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForComposition)(
        IDXGIFactory2 *This,
        IUnknown *pDevice,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    END_INTERFACE
} IDXGIFactory2Vtbl;

interface IDXGIFactory2 {
    CONST_VTBL IDXGIFactory2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactory2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactory2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactory2_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIFactory2_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIFactory2_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIFactory2_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIFactory2_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIFactory methods ***/
#define IDXGIFactory2_EnumAdapters(This,adapter_idx,adapter) (This)->lpVtbl->EnumAdapters(This,adapter_idx,adapter)
#define IDXGIFactory2_MakeWindowAssociation(This,window,flags) (This)->lpVtbl->MakeWindowAssociation(This,window,flags)
#define IDXGIFactory2_GetWindowAssociation(This,window) (This)->lpVtbl->GetWindowAssociation(This,window)
#define IDXGIFactory2_CreateSwapChain(This,device,desc,swapchain) (This)->lpVtbl->CreateSwapChain(This,device,desc,swapchain)
#define IDXGIFactory2_CreateSoftwareAdapter(This,swrast,adapter) (This)->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter)
/*** IDXGIFactory1 methods ***/
#define IDXGIFactory2_EnumAdapters1(This,Adapter,ppAdapter) (This)->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter)
#define IDXGIFactory2_IsCurrent(This) (This)->lpVtbl->IsCurrent(This)
/*** IDXGIFactory2 methods ***/
#define IDXGIFactory2_IsWindowedStereoEnabled(This) (This)->lpVtbl->IsWindowedStereoEnabled(This)
#define IDXGIFactory2_CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory2_CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory2_GetSharedResourceAdapterLuid(This,hResource,pLuid) (This)->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid)
#define IDXGIFactory2_RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory2_RegisterStereoStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory2_UnregisterStereoStatus(This,dwCookie) (This)->lpVtbl->UnregisterStereoStatus(This,dwCookie)
#define IDXGIFactory2_RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory2_RegisterOcclusionStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory2_UnregisterOcclusionStatus(This,dwCookie) (This)->lpVtbl->UnregisterOcclusionStatus(This,dwCookie)
#define IDXGIFactory2_CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGIFactory2_QueryInterface(IDXGIFactory2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGIFactory2_AddRef(IDXGIFactory2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGIFactory2_Release(IDXGIFactory2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static FORCEINLINE HRESULT IDXGIFactory2_SetPrivateData(IDXGIFactory2* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIFactory2_SetPrivateDataInterface(IDXGIFactory2* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static FORCEINLINE HRESULT IDXGIFactory2_GetPrivateData(IDXGIFactory2* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIFactory2_GetParent(IDXGIFactory2* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIFactory methods ***/
static FORCEINLINE HRESULT IDXGIFactory2_EnumAdapters(IDXGIFactory2* This,UINT adapter_idx,IDXGIAdapter **adapter) {
    return This->lpVtbl->EnumAdapters(This,adapter_idx,adapter);
}
static FORCEINLINE HRESULT IDXGIFactory2_MakeWindowAssociation(IDXGIFactory2* This,HWND window,UINT flags) {
    return This->lpVtbl->MakeWindowAssociation(This,window,flags);
}
static FORCEINLINE HRESULT IDXGIFactory2_GetWindowAssociation(IDXGIFactory2* This,HWND *window) {
    return This->lpVtbl->GetWindowAssociation(This,window);
}
static FORCEINLINE HRESULT IDXGIFactory2_CreateSwapChain(IDXGIFactory2* This,IUnknown *device,DXGI_SWAP_CHAIN_DESC *desc,IDXGISwapChain **swapchain) {
    return This->lpVtbl->CreateSwapChain(This,device,desc,swapchain);
}
static FORCEINLINE HRESULT IDXGIFactory2_CreateSoftwareAdapter(IDXGIFactory2* This,HMODULE swrast,IDXGIAdapter **adapter) {
    return This->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter);
}
/*** IDXGIFactory1 methods ***/
static FORCEINLINE HRESULT IDXGIFactory2_EnumAdapters1(IDXGIFactory2* This,UINT Adapter,IDXGIAdapter1 **ppAdapter) {
    return This->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter);
}
static FORCEINLINE WINBOOL IDXGIFactory2_IsCurrent(IDXGIFactory2* This) {
    return This->lpVtbl->IsCurrent(This);
}
/*** IDXGIFactory2 methods ***/
static FORCEINLINE WINBOOL IDXGIFactory2_IsWindowedStereoEnabled(IDXGIFactory2* This) {
    return This->lpVtbl->IsWindowedStereoEnabled(This);
}
static FORCEINLINE HRESULT IDXGIFactory2_CreateSwapChainForHwnd(IDXGIFactory2* This,IUnknown *pDevice,HWND hWnd,const DXGI_SWAP_CHAIN_DESC1 *pDesc,const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain);
}
static FORCEINLINE HRESULT IDXGIFactory2_CreateSwapChainForCoreWindow(IDXGIFactory2* This,IUnknown *pDevice,IUnknown *pWindow,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain);
}
static FORCEINLINE HRESULT IDXGIFactory2_GetSharedResourceAdapterLuid(IDXGIFactory2* This,HANDLE hResource,LUID *pLuid) {
    return This->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid);
}
static FORCEINLINE HRESULT IDXGIFactory2_RegisterOcclusionStatusWindow(IDXGIFactory2* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static FORCEINLINE HRESULT IDXGIFactory2_RegisterStereoStatusEvent(IDXGIFactory2* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie);
}
static FORCEINLINE void IDXGIFactory2_UnregisterStereoStatus(IDXGIFactory2* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterStereoStatus(This,dwCookie);
}
static FORCEINLINE HRESULT IDXGIFactory2_RegisterStereoStatusWindow(IDXGIFactory2* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static FORCEINLINE HRESULT IDXGIFactory2_RegisterOcclusionStatusEvent(IDXGIFactory2* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie);
}
static FORCEINLINE void IDXGIFactory2_UnregisterOcclusionStatus(IDXGIFactory2* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterOcclusionStatus(This,dwCookie);
}
static FORCEINLINE HRESULT IDXGIFactory2_CreateSwapChainForComposition(IDXGIFactory2* This,IUnknown *pDevice,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactory2_INTERFACE_DEFINED__ */

typedef enum DXGI_GRAPHICS_PREEMPTION_GRANULARITY {
    DXGI_GRAPHICS_PREEMPTION_DMA_BUFFER_BOUNDARY = 0,
    DXGI_GRAPHICS_PREEMPTION_PRIMITIVE_BOUNDARY = 1,
    DXGI_GRAPHICS_PREEMPTION_TRIANGLE_BOUNDARY = 2,
    DXGI_GRAPHICS_PREEMPTION_PIXEL_BOUNDARY = 3,
    DXGI_GRAPHICS_PREEMPTION_INSTRUCTION_BOUNDARY = 4
} DXGI_GRAPHICS_PREEMPTION_GRANULARITY;
typedef enum DXGI_COMPUTE_PREEMPTION_GRANULARITY {
    DXGI_COMPUTE_PREEMPTION_DMA_BUFFER_BOUNDARY = 0,
    DXGI_COMPUTE_PREEMPTION_DISPATCH_BOUNDARY = 1,
    DXGI_COMPUTE_PREEMPTION_THREAD_GROUP_BOUNDARY = 2,
    DXGI_COMPUTE_PREEMPTION_THREAD_BOUNDARY = 3,
    DXGI_COMPUTE_PREEMPTION_INSTRUCTION_BOUNDARY = 4
} DXGI_COMPUTE_PREEMPTION_GRANULARITY;
typedef struct DXGI_ADAPTER_DESC2 {
    WCHAR Description[128];
    UINT VendorId;
    UINT DeviceId;
    UINT SubSysId;
    UINT Revision;
    SIZE_T DedicatedVideoMemory;
    SIZE_T DedicatedSystemMemory;
    SIZE_T SharedSystemMemory;
    LUID AdapterLuid;
    UINT Flags;
    DXGI_GRAPHICS_PREEMPTION_GRANULARITY GraphicsPreemptionGranularity;
    DXGI_COMPUTE_PREEMPTION_GRANULARITY ComputePreemptionGranularity;
} DXGI_ADAPTER_DESC2;
/*****************************************************************************
 * IDXGIAdapter2 interface
 */
#ifndef __IDXGIAdapter2_INTERFACE_DEFINED__
#define __IDXGIAdapter2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIAdapter2, 0x0aa1ae0a, 0xfa0e, 0x4b84, 0x86,0x44, 0xe0,0x5f,0xf8,0xe5,0xac,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0aa1ae0a-fa0e-4b84-8644-e05ff8e5acb5")
IDXGIAdapter2 : public IDXGIAdapter1
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc2(
        DXGI_ADAPTER_DESC2 *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIAdapter2, 0x0aa1ae0a, 0xfa0e, 0x4b84, 0x86,0x44, 0xe0,0x5f,0xf8,0xe5,0xac,0xb5)
#endif
#else
typedef struct IDXGIAdapter2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIAdapter2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIAdapter2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIAdapter2 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIAdapter2 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIAdapter2 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIAdapter2 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIAdapter2 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIAdapter methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumOutputs)(
        IDXGIAdapter2 *This,
        UINT output_idx,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIAdapter2 *This,
        DXGI_ADAPTER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *CheckInterfaceSupport)(
        IDXGIAdapter2 *This,
        REFGUID guid,
        LARGE_INTEGER *umd_version);

    /*** IDXGIAdapter1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGIAdapter2 *This,
        DXGI_ADAPTER_DESC1 *pDesc);

    /*** IDXGIAdapter2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc2)(
        IDXGIAdapter2 *This,
        DXGI_ADAPTER_DESC2 *pDesc);

    END_INTERFACE
} IDXGIAdapter2Vtbl;

interface IDXGIAdapter2 {
    CONST_VTBL IDXGIAdapter2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIAdapter2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIAdapter2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIAdapter2_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIAdapter2_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter2_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIAdapter2_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter2_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIAdapter methods ***/
#define IDXGIAdapter2_EnumOutputs(This,output_idx,output) (This)->lpVtbl->EnumOutputs(This,output_idx,output)
#define IDXGIAdapter2_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIAdapter2_CheckInterfaceSupport(This,guid,umd_version) (This)->lpVtbl->CheckInterfaceSupport(This,guid,umd_version)
/*** IDXGIAdapter1 methods ***/
#define IDXGIAdapter2_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
/*** IDXGIAdapter2 methods ***/
#define IDXGIAdapter2_GetDesc2(This,pDesc) (This)->lpVtbl->GetDesc2(This,pDesc)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGIAdapter2_QueryInterface(IDXGIAdapter2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGIAdapter2_AddRef(IDXGIAdapter2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGIAdapter2_Release(IDXGIAdapter2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static FORCEINLINE HRESULT IDXGIAdapter2_SetPrivateData(IDXGIAdapter2* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIAdapter2_SetPrivateDataInterface(IDXGIAdapter2* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static FORCEINLINE HRESULT IDXGIAdapter2_GetPrivateData(IDXGIAdapter2* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIAdapter2_GetParent(IDXGIAdapter2* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIAdapter methods ***/
static FORCEINLINE HRESULT IDXGIAdapter2_EnumOutputs(IDXGIAdapter2* This,UINT output_idx,IDXGIOutput **output) {
    return This->lpVtbl->EnumOutputs(This,output_idx,output);
}
static FORCEINLINE HRESULT IDXGIAdapter2_GetDesc(IDXGIAdapter2* This,DXGI_ADAPTER_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static FORCEINLINE HRESULT IDXGIAdapter2_CheckInterfaceSupport(IDXGIAdapter2* This,REFGUID guid,LARGE_INTEGER *umd_version) {
    return This->lpVtbl->CheckInterfaceSupport(This,guid,umd_version);
}
/*** IDXGIAdapter1 methods ***/
static FORCEINLINE HRESULT IDXGIAdapter2_GetDesc1(IDXGIAdapter2* This,DXGI_ADAPTER_DESC1 *pDesc) {
    return This->lpVtbl->GetDesc1(This,pDesc);
}
/*** IDXGIAdapter2 methods ***/
static FORCEINLINE HRESULT IDXGIAdapter2_GetDesc2(IDXGIAdapter2* This,DXGI_ADAPTER_DESC2 *pDesc) {
    return This->lpVtbl->GetDesc2(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __IDXGIAdapter2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIOutput1 interface
 */
#ifndef __IDXGIOutput1_INTERFACE_DEFINED__
#define __IDXGIOutput1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIOutput1, 0x00cddea8, 0x939b, 0x4b83, 0xa3,0x40, 0xa6,0x85,0x22,0x66,0x66,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00cddea8-939b-4b83-a340-a685226666cc")
IDXGIOutput1 : public IDXGIOutput
{
    virtual HRESULT STDMETHODCALLTYPE GetDisplayModeList1(
        DXGI_FORMAT enum_format,
        UINT flags,
        UINT *num_modes,
        DXGI_MODE_DESC1 *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindClosestMatchingMode1(
        const DXGI_MODE_DESC1 *mode_to_match,
        DXGI_MODE_DESC1 *closest_match,
        IUnknown *concerned_device) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplaySurfaceData1(
        IDXGIResource *destination) = 0;

    virtual HRESULT STDMETHODCALLTYPE DuplicateOutput(
        IUnknown *device,
        IDXGIOutputDuplication **output_duplication) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIOutput1, 0x00cddea8, 0x939b, 0x4b83, 0xa3,0x40, 0xa6,0x85,0x22,0x66,0x66,0xcc)
#endif
#else
typedef struct IDXGIOutput1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIOutput1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIOutput1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIOutput1 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIOutput1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIOutput1 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIOutput1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIOutput1 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIOutput methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIOutput1 *This,
        DXGI_OUTPUT_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList)(
        IDXGIOutput1 *This,
        DXGI_FORMAT format,
        UINT flags,
        UINT *mode_count,
        DXGI_MODE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode)(
        IDXGIOutput1 *This,
        const DXGI_MODE_DESC *mode,
        DXGI_MODE_DESC *closest_match,
        IUnknown *device);

    HRESULT (STDMETHODCALLTYPE *WaitForVBlank)(
        IDXGIOutput1 *This);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        IDXGIOutput1 *This,
        IUnknown *device,
        WINBOOL exclusive);

    void (STDMETHODCALLTYPE *ReleaseOwnership)(
        IDXGIOutput1 *This);

    HRESULT (STDMETHODCALLTYPE *GetGammaControlCapabilities)(
        IDXGIOutput1 *This,
        DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps);

    HRESULT (STDMETHODCALLTYPE *SetGammaControl)(
        IDXGIOutput1 *This,
        const DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *GetGammaControl)(
        IDXGIOutput1 *This,
        DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *SetDisplaySurface)(
        IDXGIOutput1 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData)(
        IDXGIOutput1 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGIOutput1 *This,
        DXGI_FRAME_STATISTICS *stats);

    /*** IDXGIOutput1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList1)(
        IDXGIOutput1 *This,
        DXGI_FORMAT enum_format,
        UINT flags,
        UINT *num_modes,
        DXGI_MODE_DESC1 *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode1)(
        IDXGIOutput1 *This,
        const DXGI_MODE_DESC1 *mode_to_match,
        DXGI_MODE_DESC1 *closest_match,
        IUnknown *concerned_device);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData1)(
        IDXGIOutput1 *This,
        IDXGIResource *destination);

    HRESULT (STDMETHODCALLTYPE *DuplicateOutput)(
        IDXGIOutput1 *This,
        IUnknown *device,
        IDXGIOutputDuplication **output_duplication);

    END_INTERFACE
} IDXGIOutput1Vtbl;

interface IDXGIOutput1 {
    CONST_VTBL IDXGIOutput1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIOutput1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIOutput1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIOutput1_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIOutput1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIOutput1_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIOutput1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIOutput1_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIOutput methods ***/
#define IDXGIOutput1_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIOutput1_GetDisplayModeList(This,format,flags,mode_count,desc) (This)->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc)
#define IDXGIOutput1_FindClosestMatchingMode(This,mode,closest_match,device) (This)->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device)
#define IDXGIOutput1_WaitForVBlank(This) (This)->lpVtbl->WaitForVBlank(This)
#define IDXGIOutput1_TakeOwnership(This,device,exclusive) (This)->lpVtbl->TakeOwnership(This,device,exclusive)
#define IDXGIOutput1_ReleaseOwnership(This) (This)->lpVtbl->ReleaseOwnership(This)
#define IDXGIOutput1_GetGammaControlCapabilities(This,gamma_caps) (This)->lpVtbl->GetGammaControlCapabilities(This,gamma_caps)
#define IDXGIOutput1_SetGammaControl(This,gamma_control) (This)->lpVtbl->SetGammaControl(This,gamma_control)
#define IDXGIOutput1_GetGammaControl(This,gamma_control) (This)->lpVtbl->GetGammaControl(This,gamma_control)
#define IDXGIOutput1_SetDisplaySurface(This,surface) (This)->lpVtbl->SetDisplaySurface(This,surface)
#define IDXGIOutput1_GetDisplaySurfaceData(This,surface) (This)->lpVtbl->GetDisplaySurfaceData(This,surface)
#define IDXGIOutput1_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
/*** IDXGIOutput1 methods ***/
#define IDXGIOutput1_GetDisplayModeList1(This,enum_format,flags,num_modes,desc) (This)->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc)
#define IDXGIOutput1_FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device) (This)->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device)
#define IDXGIOutput1_GetDisplaySurfaceData1(This,destination) (This)->lpVtbl->GetDisplaySurfaceData1(This,destination)
#define IDXGIOutput1_DuplicateOutput(This,device,output_duplication) (This)->lpVtbl->DuplicateOutput(This,device,output_duplication)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDXGIOutput1_QueryInterface(IDXGIOutput1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDXGIOutput1_AddRef(IDXGIOutput1* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDXGIOutput1_Release(IDXGIOutput1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static FORCEINLINE HRESULT IDXGIOutput1_SetPrivateData(IDXGIOutput1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIOutput1_SetPrivateDataInterface(IDXGIOutput1* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static FORCEINLINE HRESULT IDXGIOutput1_GetPrivateData(IDXGIOutput1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static FORCEINLINE HRESULT IDXGIOutput1_GetParent(IDXGIOutput1* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIOutput methods ***/
static FORCEINLINE HRESULT IDXGIOutput1_GetDesc(IDXGIOutput1* This,DXGI_OUTPUT_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static FORCEINLINE HRESULT IDXGIOutput1_GetDisplayModeList(IDXGIOutput1* This,DXGI_FORMAT format,UINT flags,UINT *mode_count,DXGI_MODE_DESC *desc) {
    return This->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc);
}
static FORCEINLINE HRESULT IDXGIOutput1_FindClosestMatchingMode(IDXGIOutput1* This,const DXGI_MODE_DESC *mode,DXGI_MODE_DESC *closest_match,IUnknown *device) {
    return This->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device);
}
static FORCEINLINE HRESULT IDXGIOutput1_WaitForVBlank(IDXGIOutput1* This) {
    return This->lpVtbl->WaitForVBlank(This);
}
static FORCEINLINE HRESULT IDXGIOutput1_TakeOwnership(IDXGIOutput1* This,IUnknown *device,WINBOOL exclusive) {
    return This->lpVtbl->TakeOwnership(This,device,exclusive);
}
static FORCEINLINE void IDXGIOutput1_ReleaseOwnership(IDXGIOutput1* This) {
    This->lpVtbl->ReleaseOwnership(This);
}
static FORCEINLINE HRESULT IDXGIOutput1_GetGammaControlCapabilities(IDXGIOutput1* This,DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps) {
    return This->lpVtbl->GetGammaControlCapabilities(This,gamma_caps);
}
static FORCEINLINE HRESULT IDXGIOutput1_SetGammaControl(IDXGIOutput1* This,const DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->SetGammaControl(This,gamma_control);
}
static FORCEINLINE HRESULT IDXGIOutput1_GetGammaControl(IDXGIOutput1* This,DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->GetGammaControl(This,gamma_control);
}
static FORCEINLINE HRESULT IDXGIOutput1_SetDisplaySurface(IDXGIOutput1* This,IDXGISurface *surface) {
    return This->lpVtbl->SetDisplaySurface(This,surface);
}
static FORCEINLINE HRESULT IDXGIOutput1_GetDisplaySurfaceData(IDXGIOutput1* This,IDXGISurface *surface) {
    return This->lpVtbl->GetDisplaySurfaceData(This,surface);
}
static FORCEINLINE HRESULT IDXGIOutput1_GetFrameStatistics(IDXGIOutput1* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
/*** IDXGIOutput1 methods ***/
static FORCEINLINE HRESULT IDXGIOutput1_GetDisplayModeList1(IDXGIOutput1* This,DXGI_FORMAT enum_format,UINT flags,UINT *num_modes,DXGI_MODE_DESC1 *desc) {
    return This->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc);
}
static FORCEINLINE HRESULT IDXGIOutput1_FindClosestMatchingMode1(IDXGIOutput1* This,const DXGI_MODE_DESC1 *mode_to_match,DXGI_MODE_DESC1 *closest_match,IUnknown *concerned_device) {
    return This->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device);
}
static FORCEINLINE HRESULT IDXGIOutput1_GetDisplaySurfaceData1(IDXGIOutput1* This,IDXGIResource *destination) {
    return This->lpVtbl->GetDisplaySurfaceData1(This,destination);
}
static FORCEINLINE HRESULT IDXGIOutput1_DuplicateOutput(IDXGIOutput1* This,IUnknown *device,IDXGIOutputDuplication **output_duplication) {
    return This->lpVtbl->DuplicateOutput(This,device,output_duplication);
}
#endif
#endif

#endif


#endif  /* __IDXGIOutput1_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dxgi1_2_h__ */
