/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include "rpc.h"
#include "rpcndr.h"

#ifndef __RPCNDR_H_VERSION__
#error This stub requires an updated version of <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include "windows.h"
#include "ole2.h"
#endif

#ifndef __mmc_h__
#define __mmc_h__

#ifndef __IComponentData_FWD_DEFINED__
#define __IComponentData_FWD_DEFINED__
typedef struct IComponentData IComponentData;
#endif

#ifndef __IComponent_FWD_DEFINED__
#define __IComponent_FWD_DEFINED__
typedef struct IComponent IComponent;
#endif

#ifndef __IResultDataCompare_FWD_DEFINED__
#define __IResultDataCompare_FWD_DEFINED__
typedef struct IResultDataCompare IResultDataCompare;
#endif

#ifndef __IResultOwnerData_FWD_DEFINED__
#define __IResultOwnerData_FWD_DEFINED__
typedef struct IResultOwnerData IResultOwnerData;
#endif

#ifndef __IConsole_FWD_DEFINED__
#define __IConsole_FWD_DEFINED__
typedef struct IConsole IConsole;
#endif

#ifndef __IHeaderCtrl_FWD_DEFINED__
#define __IHeaderCtrl_FWD_DEFINED__
typedef struct IHeaderCtrl IHeaderCtrl;
#endif

#ifndef __IContextMenuCallback_FWD_DEFINED__
#define __IContextMenuCallback_FWD_DEFINED__
typedef struct IContextMenuCallback IContextMenuCallback;
#endif

#ifndef __IContextMenuProvider_FWD_DEFINED__
#define __IContextMenuProvider_FWD_DEFINED__
typedef struct IContextMenuProvider IContextMenuProvider;
#endif

#ifndef __IExtendContextMenu_FWD_DEFINED__
#define __IExtendContextMenu_FWD_DEFINED__
typedef struct IExtendContextMenu IExtendContextMenu;
#endif

#ifndef __IImageList_FWD_DEFINED__
#define __IImageList_FWD_DEFINED__
typedef struct IImageList IImageList;
#endif

#ifndef __IResultData_FWD_DEFINED__
#define __IResultData_FWD_DEFINED__
typedef struct IResultData IResultData;
#endif

#ifndef __IConsoleNameSpace_FWD_DEFINED__
#define __IConsoleNameSpace_FWD_DEFINED__
typedef struct IConsoleNameSpace IConsoleNameSpace;
#endif

#ifndef __IConsoleNameSpace2_FWD_DEFINED__
#define __IConsoleNameSpace2_FWD_DEFINED__
typedef struct IConsoleNameSpace2 IConsoleNameSpace2;
#endif

#ifndef __IPropertySheetCallback_FWD_DEFINED__
#define __IPropertySheetCallback_FWD_DEFINED__
typedef struct IPropertySheetCallback IPropertySheetCallback;
#endif

#ifndef __IPropertySheetProvider_FWD_DEFINED__
#define __IPropertySheetProvider_FWD_DEFINED__
typedef struct IPropertySheetProvider IPropertySheetProvider;
#endif

#ifndef __IExtendPropertySheet_FWD_DEFINED__
#define __IExtendPropertySheet_FWD_DEFINED__
typedef struct IExtendPropertySheet IExtendPropertySheet;
#endif

#ifndef __IControlbar_FWD_DEFINED__
#define __IControlbar_FWD_DEFINED__
typedef struct IControlbar IControlbar;
#endif

#ifndef __IExtendControlbar_FWD_DEFINED__
#define __IExtendControlbar_FWD_DEFINED__
typedef struct IExtendControlbar IExtendControlbar;
#endif

#ifndef __IToolbar_FWD_DEFINED__
#define __IToolbar_FWD_DEFINED__
typedef struct IToolbar IToolbar;
#endif

#ifndef __IConsoleVerb_FWD_DEFINED__
#define __IConsoleVerb_FWD_DEFINED__
typedef struct IConsoleVerb IConsoleVerb;
#endif

#ifndef __ISnapinAbout_FWD_DEFINED__
#define __ISnapinAbout_FWD_DEFINED__
typedef struct ISnapinAbout ISnapinAbout;
#endif

#ifndef __IMenuButton_FWD_DEFINED__
#define __IMenuButton_FWD_DEFINED__
typedef struct IMenuButton IMenuButton;
#endif

#ifndef __ISnapinHelp_FWD_DEFINED__
#define __ISnapinHelp_FWD_DEFINED__
typedef struct ISnapinHelp ISnapinHelp;
#endif

#ifndef __IExtendPropertySheet2_FWD_DEFINED__
#define __IExtendPropertySheet2_FWD_DEFINED__
typedef struct IExtendPropertySheet2 IExtendPropertySheet2;
#endif

#ifndef __IHeaderCtrl2_FWD_DEFINED__
#define __IHeaderCtrl2_FWD_DEFINED__
typedef struct IHeaderCtrl2 IHeaderCtrl2;
#endif

#ifndef __ISnapinHelp2_FWD_DEFINED__
#define __ISnapinHelp2_FWD_DEFINED__
typedef struct ISnapinHelp2 ISnapinHelp2;
#endif

#ifndef __IEnumTASK_FWD_DEFINED__
#define __IEnumTASK_FWD_DEFINED__
typedef struct IEnumTASK IEnumTASK;
#endif

#ifndef __IExtendTaskPad_FWD_DEFINED__
#define __IExtendTaskPad_FWD_DEFINED__
typedef struct IExtendTaskPad IExtendTaskPad;
#endif

#ifndef __IConsole2_FWD_DEFINED__
#define __IConsole2_FWD_DEFINED__
typedef struct IConsole2 IConsole2;
#endif

#ifndef __IDisplayHelp_FWD_DEFINED__
#define __IDisplayHelp_FWD_DEFINED__
typedef struct IDisplayHelp IDisplayHelp;
#endif

#ifndef __IRequiredExtensions_FWD_DEFINED__
#define __IRequiredExtensions_FWD_DEFINED__
typedef struct IRequiredExtensions IRequiredExtensions;
#endif

#ifndef __IStringTable_FWD_DEFINED__
#define __IStringTable_FWD_DEFINED__
typedef struct IStringTable IStringTable;
#endif

#ifndef __IColumnData_FWD_DEFINED__
#define __IColumnData_FWD_DEFINED__
typedef struct IColumnData IColumnData;
#endif

#ifndef __IMessageView_FWD_DEFINED__
#define __IMessageView_FWD_DEFINED__
typedef struct IMessageView IMessageView;
#endif

#ifndef __IResultDataCompareEx_FWD_DEFINED__
#define __IResultDataCompareEx_FWD_DEFINED__
typedef struct IResultDataCompareEx IResultDataCompareEx;
#endif

#ifndef __IComponentData2_FWD_DEFINED__
#define __IComponentData2_FWD_DEFINED__
typedef struct IComponentData2 IComponentData2;
#endif

#ifndef __IComponent2_FWD_DEFINED__
#define __IComponent2_FWD_DEFINED__
typedef struct IComponent2 IComponent2;
#endif

#ifndef __IContextMenuCallback2_FWD_DEFINED__
#define __IContextMenuCallback2_FWD_DEFINED__
typedef struct IContextMenuCallback2 IContextMenuCallback2;
#endif

#ifndef __IMMCVersionInfo_FWD_DEFINED__
#define __IMMCVersionInfo_FWD_DEFINED__
typedef struct IMMCVersionInfo IMMCVersionInfo;
#endif

#ifndef __MMCVersionInfo_FWD_DEFINED__
#define __MMCVersionInfo_FWD_DEFINED__
#ifdef __cplusplus
typedef class MMCVersionInfo MMCVersionInfo;
#else
typedef struct MMCVersionInfo MMCVersionInfo;
#endif
#endif

#ifndef __ConsolePower_FWD_DEFINED__
#define __ConsolePower_FWD_DEFINED__
#ifdef __cplusplus
typedef class ConsolePower ConsolePower;
#else
typedef struct ConsolePower ConsolePower;
#endif
#endif

#ifndef __IExtendView_FWD_DEFINED__
#define __IExtendView_FWD_DEFINED__
typedef struct IExtendView IExtendView;
#endif

#ifndef __IViewExtensionCallback_FWD_DEFINED__
#define __IViewExtensionCallback_FWD_DEFINED__
typedef struct IViewExtensionCallback IViewExtensionCallback;
#endif

#ifndef __IConsolePower_FWD_DEFINED__
#define __IConsolePower_FWD_DEFINED__
typedef struct IConsolePower IConsolePower;
#endif

#ifndef __IConsolePowerSink_FWD_DEFINED__
#define __IConsolePowerSink_FWD_DEFINED__
typedef struct IConsolePowerSink IConsolePowerSink;
#endif

#ifndef __INodeProperties_FWD_DEFINED__
#define __INodeProperties_FWD_DEFINED__
typedef struct INodeProperties INodeProperties;
#endif

#ifndef __IConsole3_FWD_DEFINED__
#define __IConsole3_FWD_DEFINED__
typedef struct IConsole3 IConsole3;
#endif

#ifndef __IResultData2_FWD_DEFINED__
#define __IResultData2_FWD_DEFINED__
typedef struct IResultData2 IResultData2;
#endif

#include "basetsd.h"
#include "oaidl.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __MIDL_user_allocate_free_DEFINED__
#define __MIDL_user_allocate_free_DEFINED__
  void *__RPC_API MIDL_user_allocate(size_t);
  void __RPC_API MIDL_user_free(void *);
#endif

#ifndef MMC_VER
#define MMC_VER 0x0200
#endif

  typedef IConsole *LPCONSOLE;
  typedef IHeaderCtrl *LPHEADERCTRL;
  typedef IToolbar *LPTOOLBAR;
  typedef IImageList *LPIMAGELIST;
  typedef IResultData *LPRESULTDATA;
  typedef IConsoleNameSpace *LPCONSOLENAMESPACE;
  typedef IPropertySheetProvider *LPPROPERTYSHEETPROVIDER;
  typedef IPropertySheetCallback *LPPROPERTYSHEETCALLBACK;
  typedef IContextMenuProvider *LPCONTEXTMENUPROVIDER;
  typedef IContextMenuCallback *LPCONTEXTMENUCALLBACK;
  typedef IControlbar *LPCONTROLBAR;
  typedef IConsoleVerb *LPCONSOLEVERB;
  typedef IMenuButton *LPMENUBUTTON;

#if (MMC_VER >= 0x0110)
  typedef IConsole2 *LPCONSOLE2;
  typedef IHeaderCtrl2 *LPHEADERCTRL2;
  typedef IConsoleNameSpace2 *LPCONSOLENAMESPACE2;
  typedef IDisplayHelp *LPDISPLAYHELP;
  typedef IStringTable *LPSTRINGTABLE;
#endif
#if (MMC_VER >= 0x0120)
  typedef IColumnData *LPCOLUMNDATA;
  typedef IResultDataCompareEx *LPRESULTDATACOMPAREEX;
#endif

  typedef IComponent *LPCOMPONENT;
  typedef IComponentData *LPCOMPONENTDATA;
  typedef IExtendPropertySheet *LPEXTENDPROPERTYSHEET;
  typedef IExtendContextMenu *LPEXTENDCONTEXTMENU;
  typedef IExtendControlbar *LPEXTENDCONTROLBAR;
  typedef IResultDataCompare *LPRESULTDATACOMPARE;
  typedef IResultOwnerData *LPRESULTOWNERDATA;
  typedef ISnapinAbout *LPSNAPABOUT;
  typedef ISnapinAbout *LPSNAPINABOUT;
  typedef ISnapinHelp *LPSNAPHELP;
  typedef ISnapinHelp *LPSNAPINHELP;

#if (MMC_VER >= 0x0110)
  typedef IEnumTASK *LPENUMTASK;
  typedef IExtendPropertySheet2 *LPEXTENDPROPERTYSHEET2;
  typedef ISnapinHelp2 *LPSNAPINHELP2;
  typedef IExtendTaskPad *LPEXTENDTASKPAD;
  typedef IRequiredExtensions *LPREQUIREDEXTENSIONS;
#endif
#if (MMC_VER >= 0x0200)
  typedef IComponent2 *LPCOMPONENT2;
  typedef IComponentData2 *LPCOMPONENTDATA2;
  typedef IExtendView *LPEXTENDVIEW;
  typedef IViewExtensionCallback *LPVIEWEXTENSIONCALLBACK;
  typedef IConsolePower *LPCONSOLEPOWER;
  typedef IConsolePowerSink *LPCONSOLEPOWERSINK;
  typedef IConsole3 *LPCONSOLE3;
  typedef INodeProperties *LPNODEPROPERTIES;
  typedef IResultData2 *LPRESULTDATA2;
#endif

  typedef BSTR *PBSTR;

#define MMCLV_AUTO (-1)
#define MMCLV_NOPARAM (-2)
#define MMCLV_NOICON (-1)
#define MMCLV_VIEWSTYLE_ICON (0)
#define MMCLV_VIEWSTYLE_SMALLICON (0x2)
#define MMCLV_VIEWSTYLE_LIST (0x3)
#define MMCLV_VIEWSTYLE_REPORT (0x1)
#define MMCLV_VIEWSTYLE_FILTERED (0x4)
#define MMCLV_NOPTR (0)
#define MMCLV_UPDATE_NOINVALIDATEALL (0x1)
#define MMCLV_UPDATE_NOSCROLL (0x2)

  static unsigned short *MMC_CALLBACK = (unsigned short *)-1;

#if (MMC_VER >= 0x0120)
#define MMC_IMAGECALLBACK (-1)
#define MMC_TEXTCALLBACK MMC_CALLBACK
#endif

  typedef LONG_PTR HSCOPEITEM;
  typedef __LONG32 COMPONENTID;
  typedef LONG_PTR HRESULTITEM;

#define RDI_STR (0x2)
#define RDI_IMAGE (0x4)
#define RDI_STATE (0x8)
#define RDI_PARAM (0x10)
#define RDI_INDEX (0x20)
#define RDI_INDENT (0x40)

  typedef enum _MMC_RESULT_VIEW_STYLE {
    MMC_SINGLESEL = 0x0001,MMC_SHOWSELALWAYS = 0x0002,MMC_NOSORTHEADER = 0x0004,
#if (MMC_VER >= 0x0120)
    MMC_ENSUREFOCUSVISIBLE = 0x0008
#endif
  } MMC_RESULT_VIEW_STYLE;

#define MMC_VIEW_OPTIONS_NONE (0)
#define MMC_VIEW_OPTIONS_NOLISTVIEWS (0x1)
#define MMC_VIEW_OPTIONS_MULTISELECT (0x2)
#define MMC_VIEW_OPTIONS_OWNERDATALIST (0x4)
#define MMC_VIEW_OPTIONS_FILTERED (0x8)
#define MMC_VIEW_OPTIONS_CREATENEW (0x10)
#if (MMC_VER >= 0x0110)
#define MMC_VIEW_OPTIONS_USEFONTLINKING (0x20)
#endif
#if (MMC_VER >= 0x0120)
#define MMC_VIEW_OPTIONS_EXCLUDE_SCOPE_ITEMS_FROM_LIST (0x40)
#define MMC_VIEW_OPTIONS_LEXICAL_SORT (0x80)
#endif

#define MMC_PSO_NOAPPLYNOW (0x1)
#define MMC_PSO_HASHELP (0x2)
#define MMC_PSO_NEWWIZARDTYPE (0x4)
#define MMC_PSO_NO_PROPTITLE (0x8)

  typedef enum _MMC_CONTROL_TYPE {
    TOOLBAR = 0,MENUBUTTON,COMBOBOXBAR
  } MMC_CONTROL_TYPE;

  typedef enum _MMC_CONSOLE_VERB {
    MMC_VERB_NONE = 0x0000,MMC_VERB_OPEN = 0x8000,MMC_VERB_COPY = 0x8001,MMC_VERB_PASTE = 0x8002,MMC_VERB_DELETE = 0x8003,MMC_VERB_PROPERTIES = 0x8004,
    MMC_VERB_RENAME = 0x8005,MMC_VERB_REFRESH = 0x8006,MMC_VERB_PRINT = 0x8007,
#if (MMC_VER >= 0x0110)
    MMC_VERB_CUT = 0x8008,MMC_VERB_MAX,MMC_VERB_FIRST = MMC_VERB_OPEN,MMC_VERB_LAST = MMC_VERB_MAX - 1
#endif
  } MMC_CONSOLE_VERB;

#include <pshpack8.h>

  typedef struct _MMCButton {
    int nBitmap;
    int idCommand;
    BYTE fsState;
    BYTE fsType;
    LPOLESTR lpButtonText;
    LPOLESTR lpTooltipText;
  } MMCBUTTON;

#include <poppack.h>

  typedef MMCBUTTON *LPMMCBUTTON;

  typedef enum _MMC_BUTTON_STATE {
    ENABLED = 0x1,CHECKED = 0x2,HIDDEN = 0x4,INDETERMINATE = 0x8,BUTTONPRESSED = 0x10
  } MMC_BUTTON_STATE;

  typedef struct _RESULTDATAITEM {
    DWORD mask;
    WINBOOL bScopeItem;
    HRESULTITEM itemID;
    int nIndex;
    int nCol;
    LPOLESTR str;
    int nImage;
    UINT nState;
    LPARAM lParam;
    int iIndent;
  } RESULTDATAITEM;

  typedef RESULTDATAITEM *LPRESULTDATAITEM;

#define RFI_PARTIAL (0x1)
#define RFI_WRAP (0x2)

  typedef struct _RESULTFINDINFO {
    LPOLESTR psz;
    int nStart;
    DWORD dwOptions;
  } RESULTFINDINFO;

  typedef RESULTFINDINFO *LPRESULTFINDINFO;

#define RSI_DESCENDING (0x1)
#define RSI_NOSORTICON (0x2)

#define SDI_STR (0x2)
#define SDI_IMAGE (0x4)
#define SDI_OPENIMAGE (0x8)
#define SDI_STATE (0x10)
#define SDI_PARAM (0x20)
#define SDI_CHILDREN (0x40)
#define SDI_PARENT (0)
#define SDI_PREVIOUS (0x10000000)
#define SDI_NEXT (0x20000000)
#define SDI_FIRST (0x8000000)

  typedef struct _SCOPEDATAITEM {
    DWORD mask;
    LPOLESTR displayname;
    int nImage;
    int nOpenImage;
    UINT nState;
    int cChildren;
    LPARAM lParam;
    HSCOPEITEM relativeID;
    HSCOPEITEM ID;
  } SCOPEDATAITEM;

  typedef SCOPEDATAITEM *LPSCOPEDATAITEM;

  typedef enum _MMC_SCOPE_ITEM_STATE {
    MMC_SCOPE_ITEM_STATE_NORMAL = 0x1,MMC_SCOPE_ITEM_STATE_BOLD = 0x2,MMC_SCOPE_ITEM_STATE_EXPANDEDONCE = 0x3
  } MMC_SCOPE_ITEM_STATE;

  typedef struct _CONTEXTMENUITEM {
    LPWSTR strName;
    LPWSTR strStatusBarText;
    LONG lCommandID;
    LONG lInsertionPointID;
    LONG fFlags;
    LONG fSpecialFlags;
  } CONTEXTMENUITEM;

  typedef CONTEXTMENUITEM *LPCONTEXTMENUITEM;

  typedef enum _MMC_MENU_COMMAND_IDS {
    MMCC_STANDARD_VIEW_SELECT = -1
  } MMC_MENU_COMMAND_IDS;

  typedef struct _MENUBUTTONDATA {
    int idCommand;
    int x;
    int y;
  } MENUBUTTONDATA;

  typedef MENUBUTTONDATA *LPMENUBUTTONDATA;
  typedef LONG_PTR MMC_COOKIE;

#define MMC_MULTI_SELECT_COOKIE (-2)
#define MMC_WINDOW_COOKIE (-3)
#if (MMC_VER >= 0x0110)
#define SPECIAL_COOKIE_MIN (-10)
#define SPECIAL_COOKIE_MAX (-1)

  typedef enum _MMC_FILTER_TYPE {
    MMC_STRING_FILTER = 0,MMC_INT_FILTER = 0x1,MMC_FILTER_NOVALUE = 0x8000
  } MMC_FILTER_TYPE;

  typedef struct _MMC_FILTERDATA {
    LPOLESTR pszText;
    INT cchTextMax;
    LONG lValue;
  } MMC_FILTERDATA;

  typedef enum _MMC_FILTER_CHANGE_CODE {
    MFCC_DISABLE = 0,MFCC_ENABLE = 1,MFCC_VALUE_CHANGE = 2
  } MMC_FILTER_CHANGE_CODE;

  typedef struct _MMC_RESTORE_VIEW {
    DWORD dwSize;
    MMC_COOKIE cookie;
    LPOLESTR pViewType;
    __LONG32 lViewOptions;
  } MMC_RESTORE_VIEW;

  typedef struct _MMC_EXPANDSYNC_STRUCT {
    WINBOOL bHandled;
    WINBOOL bExpanding;
    HSCOPEITEM hItem;
  } MMC_EXPANDSYNC_STRUCT;
#endif
#if (MMC_VER >= 0x0120)
  typedef struct _MMC_VISIBLE_COLUMNS {
    INT nVisibleColumns;
    INT rgVisibleCols[1 ];
  } MMC_VISIBLE_COLUMNS;
#endif
  typedef enum _MMC_NOTIFY_TYPE {
    MMCN_ACTIVATE = 0x8001,MMCN_ADD_IMAGES = 0x8002,MMCN_BTN_CLICK = 0x8003,MMCN_CLICK = 0x8004,MMCN_COLUMN_CLICK = 0x8005,
    MMCN_CONTEXTMENU = 0x8006,MMCN_CUTORMOVE = 0x8007,MMCN_DBLCLICK = 0x8008,MMCN_DELETE = 0x8009,MMCN_DESELECT_ALL = 0x800A,MMCN_EXPAND = 0x800B,
    MMCN_HELP = 0x800C,MMCN_MENU_BTNCLICK = 0x800D,MMCN_MINIMIZED = 0x800E,MMCN_PASTE = 0x800F,MMCN_PROPERTY_CHANGE = 0x8010,MMCN_QUERY_PASTE = 0x8011,
    MMCN_REFRESH = 0x8012,MMCN_REMOVE_CHILDREN = 0x8013,MMCN_RENAME = 0x8014,MMCN_SELECT = 0x8015,MMCN_SHOW = 0x8016,MMCN_VIEW_CHANGE = 0x8017,
    MMCN_SNAPINHELP = 0x8018,MMCN_CONTEXTHELP = 0x8019,MMCN_INITOCX = 0x801A,
#if (MMC_VER >= 0x0110)
    MMCN_FILTER_CHANGE = 0x801B,MMCN_FILTERBTN_CLICK = 0x801C,MMCN_RESTORE_VIEW = 0x801D,MMCN_PRINT = 0x801E,MMCN_PRELOAD = 0x801F,
    MMCN_LISTPAD = 0x8020,MMCN_EXPANDSYNC = 0x8021,
#if (MMC_VER >= 0x0120)
    MMCN_COLUMNS_CHANGED = 0x8022,
#if (MMC_VER >= 0x0200)
    MMCN_CANPASTE_OUTOFPROC = 0x8023,
#endif
#endif
#endif
  } MMC_NOTIFY_TYPE;

  typedef enum _DATA_OBJECT_TYPES {
    CCT_SCOPE = 0x8000,CCT_RESULT = 0x8001,CCT_SNAPIN_MANAGER = 0x8002,CCT_UNINITIALIZED = 0xffff
  } DATA_OBJECT_TYPES;

#define MMC_NW_OPTION_NONE (0)
#define MMC_NW_OPTION_NOSCOPEPANE (0x1)
#define MMC_NW_OPTION_NOTOOLBARS (0x2)
#define MMC_NW_OPTION_SHORTTITLE (0x4)
#define MMC_NW_OPTION_CUSTOMTITLE (0x8)
#define MMC_NW_OPTION_NOPERSIST (0x10)

#define CCF_NODETYPE (L"CCF_NODETYPE")
#define CCF_SZNODETYPE (L"CCF_SZNODETYPE")
#define CCF_DISPLAY_NAME (L"CCF_DISPLAY_NAME")
#define CCF_SNAPIN_CLASSID (L"CCF_SNAPIN_CLASSID")
#define CCF_WINDOW_TITLE (L"CCF_WINDOW_TITLE")
#define CCF_MMC_MULTISELECT_DATAOBJECT (L"CCF_MMC_MULTISELECT_DATAOBJECT")

  typedef struct _SMMCDataObjects {
    DWORD count;
    LPDATAOBJECT lpDataObject[1 ];
  } SMMCDataObjects;

#define CCF_MULTI_SELECT_SNAPINS (L"CCF_MULTI_SELECT_SNAPINS")

  typedef struct _SMMCObjectTypes {
    DWORD count;
    GUID guid[1 ];
  } SMMCObjectTypes;

#define CCF_OBJECT_TYPES_IN_MULTI_SELECT (L"CCF_OBJECT_TYPES_IN_MULTI_SELECT")

#if (MMC_VER >= 0x0110)
  typedef SMMCObjectTypes SMMCDynamicExtensions;

#define CCF_MMC_DYNAMIC_EXTENSIONS (L"CCF_MMC_DYNAMIC_EXTENSIONS")
#define CCF_SNAPIN_PRELOADS (L"CCF_SNAPIN_PRELOADS")

  typedef struct _SNodeID {
    DWORD cBytes;
    BYTE id[1 ];
  } SNodeID;

#if (MMC_VER >= 0x0120)
  typedef struct _SNodeID2
  {
    DWORD dwFlags;
    DWORD cBytes;
    BYTE id[1 ];
  } SNodeID2;

#define MMC_NODEID_SLOW_RETRIEVAL (0x1)
#define CCF_NODEID2 (L"CCF_NODEID2")
#endif
#define CCF_NODEID (L"CCF_NODEID")

#if (MMC_VER >= 0x0120)
  typedef struct _SColumnSetID
  {
    DWORD dwFlags;
    DWORD cBytes;
    BYTE id[1 ];
  } SColumnSetID;

#define CCF_COLUMN_SET_ID (L"CCF_COLUMN_SET_ID")
#endif
#endif
  STDAPI MMCPropertyChangeNotify(LONG_PTR lNotifyHandle,LPARAM param);
#if (MMC_VER >= 0x0110)
  STDAPI MMCPropertyHelp(LPOLESTR pszHelpTopic);
#endif
  STDAPI MMCFreeNotifyHandle(LONG_PTR lNotifyHandle);
  STDAPI MMCPropPageCallback(void *vpsp);
  EXTERN_C const CLSID CLSID_NodeManager;
#if (MMC_VER >= 0x0120)
  EXTERN_C const CLSID CLSID_MessageView;
#endif
#define DOBJ_NULL (LPDATAOBJECT) 0
#define DOBJ_CUSTOMOCX (LPDATAOBJECT) -1
#define DOBJ_CUSTOMWEB (LPDATAOBJECT) -2
#if (MMC_VER >= 0x0110)
#if (MMC_VER >= 0x0120)
#define DOBJ_NOCONSOLE (LPDATAOBJECT) -3
#endif
#define SPECIAL_DOBJ_MIN -10
#define SPECIAL_DOBJ_MAX 0
#endif
#define IS_SPECIAL_DATAOBJECT(d) (((LONG_PTR)(d) >= SPECIAL_DOBJ_MIN) && ((LONG_PTR)(d) <= SPECIAL_DOBJ_MAX))
#define IS_SPECIAL_COOKIE(c) (((c) >= SPECIAL_COOKIE_MIN) && ((c) <= SPECIAL_COOKIE_MAX))

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0000_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0000_v0_0_s_ifspec;

#ifndef __IComponentData_INTERFACE_DEFINED__
#define __IComponentData_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IComponentData;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IComponentData : public IUnknown {
  public:
    virtual HRESULT WINAPI Initialize(LPUNKNOWN pUnknown) = 0;
    virtual HRESULT WINAPI CreateComponent(LPCOMPONENT *ppComponent) = 0;
    virtual HRESULT WINAPI Notify(LPDATAOBJECT lpDataObject,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param) = 0;
    virtual HRESULT WINAPI Destroy(void) = 0;
    virtual HRESULT WINAPI QueryDataObject(MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDATAOBJECT *ppDataObject) = 0;
    virtual HRESULT WINAPI GetDisplayInfo(SCOPEDATAITEM *pScopeDataItem) = 0;
    virtual HRESULT WINAPI CompareObjects(LPDATAOBJECT lpDataObjectA,LPDATAOBJECT lpDataObjectB) = 0;
  };
#else
  typedef struct IComponentDataVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IComponentData *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IComponentData *This);
      ULONG (WINAPI *Release)(IComponentData *This);
      HRESULT (WINAPI *Initialize)(IComponentData *This,LPUNKNOWN pUnknown);
      HRESULT (WINAPI *CreateComponent)(IComponentData *This,LPCOMPONENT *ppComponent);
      HRESULT (WINAPI *Notify)(IComponentData *This,LPDATAOBJECT lpDataObject,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param);
      HRESULT (WINAPI *Destroy)(IComponentData *This);
      HRESULT (WINAPI *QueryDataObject)(IComponentData *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDATAOBJECT *ppDataObject);
      HRESULT (WINAPI *GetDisplayInfo)(IComponentData *This,SCOPEDATAITEM *pScopeDataItem);
      HRESULT (WINAPI *CompareObjects)(IComponentData *This,LPDATAOBJECT lpDataObjectA,LPDATAOBJECT lpDataObjectB);
    END_INTERFACE
  } IComponentDataVtbl;
  struct IComponentData {
    CONST_VTBL struct IComponentDataVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IComponentData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IComponentData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IComponentData_Release(This) (This)->lpVtbl->Release(This)
#define IComponentData_Initialize(This,pUnknown) (This)->lpVtbl->Initialize(This,pUnknown)
#define IComponentData_CreateComponent(This,ppComponent) (This)->lpVtbl->CreateComponent(This,ppComponent)
#define IComponentData_Notify(This,lpDataObject,event,arg,param) (This)->lpVtbl->Notify(This,lpDataObject,event,arg,param)
#define IComponentData_Destroy(This) (This)->lpVtbl->Destroy(This)
#define IComponentData_QueryDataObject(This,cookie,type,ppDataObject) (This)->lpVtbl->QueryDataObject(This,cookie,type,ppDataObject)
#define IComponentData_GetDisplayInfo(This,pScopeDataItem) (This)->lpVtbl->GetDisplayInfo(This,pScopeDataItem)
#define IComponentData_CompareObjects(This,lpDataObjectA,lpDataObjectB) (This)->lpVtbl->CompareObjects(This,lpDataObjectA,lpDataObjectB)
#endif
#endif
  HRESULT WINAPI IComponentData_Initialize_Proxy(IComponentData *This,LPUNKNOWN pUnknown);
  void __RPC_STUB IComponentData_Initialize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponentData_CreateComponent_Proxy(IComponentData *This,LPCOMPONENT *ppComponent);
  void __RPC_STUB IComponentData_CreateComponent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponentData_Notify_Proxy(IComponentData *This,LPDATAOBJECT lpDataObject,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param);
  void __RPC_STUB IComponentData_Notify_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponentData_Destroy_Proxy(IComponentData *This);
  void __RPC_STUB IComponentData_Destroy_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponentData_QueryDataObject_Proxy(IComponentData *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDATAOBJECT *ppDataObject);
  void __RPC_STUB IComponentData_QueryDataObject_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponentData_GetDisplayInfo_Proxy(IComponentData *This,SCOPEDATAITEM *pScopeDataItem);
  void __RPC_STUB IComponentData_GetDisplayInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponentData_CompareObjects_Proxy(IComponentData *This,LPDATAOBJECT lpDataObjectA,LPDATAOBJECT lpDataObjectB);
  void __RPC_STUB IComponentData_CompareObjects_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IComponent_INTERFACE_DEFINED__
#define __IComponent_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IComponent;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IComponent : public IUnknown {
  public:
    virtual HRESULT WINAPI Initialize(LPCONSOLE lpConsole) = 0;
    virtual HRESULT WINAPI Notify(LPDATAOBJECT lpDataObject,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param) = 0;
    virtual HRESULT WINAPI Destroy(MMC_COOKIE cookie) = 0;
    virtual HRESULT WINAPI QueryDataObject(MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDATAOBJECT *ppDataObject) = 0;
    virtual HRESULT WINAPI GetResultViewType(MMC_COOKIE cookie,LPOLESTR *ppViewType,__LONG32 *pViewOptions) = 0;
    virtual HRESULT WINAPI GetDisplayInfo(RESULTDATAITEM *pResultDataItem) = 0;
    virtual HRESULT WINAPI CompareObjects(LPDATAOBJECT lpDataObjectA,LPDATAOBJECT lpDataObjectB) = 0;
  };
#else
  typedef struct IComponentVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IComponent *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IComponent *This);
      ULONG (WINAPI *Release)(IComponent *This);
      HRESULT (WINAPI *Initialize)(IComponent *This,LPCONSOLE lpConsole);
      HRESULT (WINAPI *Notify)(IComponent *This,LPDATAOBJECT lpDataObject,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param);
      HRESULT (WINAPI *Destroy)(IComponent *This,MMC_COOKIE cookie);
      HRESULT (WINAPI *QueryDataObject)(IComponent *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDATAOBJECT *ppDataObject);
      HRESULT (WINAPI *GetResultViewType)(IComponent *This,MMC_COOKIE cookie,LPOLESTR *ppViewType,__LONG32 *pViewOptions);
      HRESULT (WINAPI *GetDisplayInfo)(IComponent *This,RESULTDATAITEM *pResultDataItem);
      HRESULT (WINAPI *CompareObjects)(IComponent *This,LPDATAOBJECT lpDataObjectA,LPDATAOBJECT lpDataObjectB);
    END_INTERFACE
  } IComponentVtbl;
  struct IComponent {
    CONST_VTBL struct IComponentVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IComponent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IComponent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IComponent_Release(This) (This)->lpVtbl->Release(This)
#define IComponent_Initialize(This,lpConsole) (This)->lpVtbl->Initialize(This,lpConsole)
#define IComponent_Notify(This,lpDataObject,event,arg,param) (This)->lpVtbl->Notify(This,lpDataObject,event,arg,param)
#define IComponent_Destroy(This,cookie) (This)->lpVtbl->Destroy(This,cookie)
#define IComponent_QueryDataObject(This,cookie,type,ppDataObject) (This)->lpVtbl->QueryDataObject(This,cookie,type,ppDataObject)
#define IComponent_GetResultViewType(This,cookie,ppViewType,pViewOptions) (This)->lpVtbl->GetResultViewType(This,cookie,ppViewType,pViewOptions)
#define IComponent_GetDisplayInfo(This,pResultDataItem) (This)->lpVtbl->GetDisplayInfo(This,pResultDataItem)
#define IComponent_CompareObjects(This,lpDataObjectA,lpDataObjectB) (This)->lpVtbl->CompareObjects(This,lpDataObjectA,lpDataObjectB)
#endif
#endif
  HRESULT WINAPI IComponent_Initialize_Proxy(IComponent *This,LPCONSOLE lpConsole);
  void __RPC_STUB IComponent_Initialize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponent_Notify_Proxy(IComponent *This,LPDATAOBJECT lpDataObject,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param);
  void __RPC_STUB IComponent_Notify_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponent_Destroy_Proxy(IComponent *This,MMC_COOKIE cookie);
  void __RPC_STUB IComponent_Destroy_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponent_QueryDataObject_Proxy(IComponent *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDATAOBJECT *ppDataObject);
  void __RPC_STUB IComponent_QueryDataObject_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponent_GetResultViewType_Proxy(IComponent *This,MMC_COOKIE cookie,LPOLESTR *ppViewType,__LONG32 *pViewOptions);
  void __RPC_STUB IComponent_GetResultViewType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponent_GetDisplayInfo_Proxy(IComponent *This,RESULTDATAITEM *pResultDataItem);
  void __RPC_STUB IComponent_GetDisplayInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponent_CompareObjects_Proxy(IComponent *This,LPDATAOBJECT lpDataObjectA,LPDATAOBJECT lpDataObjectB);
  void __RPC_STUB IComponent_CompareObjects_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IResultDataCompare_INTERFACE_DEFINED__
#define __IResultDataCompare_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IResultDataCompare;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IResultDataCompare : public IUnknown {
  public:
    virtual HRESULT WINAPI Compare(LPARAM lUserParam,MMC_COOKIE cookieA,MMC_COOKIE cookieB,int *pnResult) = 0;
  };
#else
  typedef struct IResultDataCompareVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IResultDataCompare *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IResultDataCompare *This);
      ULONG (WINAPI *Release)(IResultDataCompare *This);
      HRESULT (WINAPI *Compare)(IResultDataCompare *This,LPARAM lUserParam,MMC_COOKIE cookieA,MMC_COOKIE cookieB,int *pnResult);
    END_INTERFACE
  } IResultDataCompareVtbl;
  struct IResultDataCompare {
    CONST_VTBL struct IResultDataCompareVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IResultDataCompare_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IResultDataCompare_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IResultDataCompare_Release(This) (This)->lpVtbl->Release(This)
#define IResultDataCompare_Compare(This,lUserParam,cookieA,cookieB,pnResult) (This)->lpVtbl->Compare(This,lUserParam,cookieA,cookieB,pnResult)
#endif
#endif
  HRESULT WINAPI IResultDataCompare_Compare_Proxy(IResultDataCompare *This,LPARAM lUserParam,MMC_COOKIE cookieA,MMC_COOKIE cookieB,int *pnResult);
  void __RPC_STUB IResultDataCompare_Compare_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IResultOwnerData_INTERFACE_DEFINED__
#define __IResultOwnerData_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IResultOwnerData;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IResultOwnerData : public IUnknown {
  public:
    virtual HRESULT WINAPI FindItem(LPRESULTFINDINFO pFindInfo,int *pnFoundIndex) = 0;
    virtual HRESULT WINAPI CacheHint(int nStartIndex,int nEndIndex) = 0;
    virtual HRESULT WINAPI SortItems(int nColumn,DWORD dwSortOptions,LPARAM lUserParam) = 0;
  };
#else
  typedef struct IResultOwnerDataVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IResultOwnerData *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IResultOwnerData *This);
      ULONG (WINAPI *Release)(IResultOwnerData *This);
      HRESULT (WINAPI *FindItem)(IResultOwnerData *This,LPRESULTFINDINFO pFindInfo,int *pnFoundIndex);
      HRESULT (WINAPI *CacheHint)(IResultOwnerData *This,int nStartIndex,int nEndIndex);
      HRESULT (WINAPI *SortItems)(IResultOwnerData *This,int nColumn,DWORD dwSortOptions,LPARAM lUserParam);
    END_INTERFACE
  } IResultOwnerDataVtbl;
  struct IResultOwnerData {
    CONST_VTBL struct IResultOwnerDataVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IResultOwnerData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IResultOwnerData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IResultOwnerData_Release(This) (This)->lpVtbl->Release(This)
#define IResultOwnerData_FindItem(This,pFindInfo,pnFoundIndex) (This)->lpVtbl->FindItem(This,pFindInfo,pnFoundIndex)
#define IResultOwnerData_CacheHint(This,nStartIndex,nEndIndex) (This)->lpVtbl->CacheHint(This,nStartIndex,nEndIndex)
#define IResultOwnerData_SortItems(This,nColumn,dwSortOptions,lUserParam) (This)->lpVtbl->SortItems(This,nColumn,dwSortOptions,lUserParam)
#endif
#endif
  HRESULT WINAPI IResultOwnerData_FindItem_Proxy(IResultOwnerData *This,LPRESULTFINDINFO pFindInfo,int *pnFoundIndex);
  void __RPC_STUB IResultOwnerData_FindItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultOwnerData_CacheHint_Proxy(IResultOwnerData *This,int nStartIndex,int nEndIndex);
  void __RPC_STUB IResultOwnerData_CacheHint_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultOwnerData_SortItems_Proxy(IResultOwnerData *This,int nColumn,DWORD dwSortOptions,LPARAM lUserParam);
  void __RPC_STUB IResultOwnerData_SortItems_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IConsole_INTERFACE_DEFINED__
#define __IConsole_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IConsole;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IConsole : public IUnknown {
  public:
    virtual HRESULT WINAPI SetHeader(LPHEADERCTRL pHeader) = 0;
    virtual HRESULT WINAPI SetToolbar(LPTOOLBAR pToolbar) = 0;
    virtual HRESULT WINAPI QueryResultView(LPUNKNOWN *pUnknown) = 0;
    virtual HRESULT WINAPI QueryScopeImageList(LPIMAGELIST *ppImageList) = 0;
    virtual HRESULT WINAPI QueryResultImageList(LPIMAGELIST *ppImageList) = 0;
    virtual HRESULT WINAPI UpdateAllViews(LPDATAOBJECT lpDataObject,LPARAM data,LONG_PTR hint) = 0;
    virtual HRESULT WINAPI MessageBox(LPCWSTR lpszText,LPCWSTR lpszTitle,UINT fuStyle,int *piRetval) = 0;
    virtual HRESULT WINAPI QueryConsoleVerb(LPCONSOLEVERB *ppConsoleVerb) = 0;
    virtual HRESULT WINAPI SelectScopeItem(HSCOPEITEM hScopeItem) = 0;
    virtual HRESULT WINAPI GetMainWindow(HWND *phwnd) = 0;
    virtual HRESULT WINAPI NewWindow(HSCOPEITEM hScopeItem,unsigned __LONG32 lOptions) = 0;
  };
#else
  typedef struct IConsoleVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IConsole *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IConsole *This);
      ULONG (WINAPI *Release)(IConsole *This);
      HRESULT (WINAPI *SetHeader)(IConsole *This,LPHEADERCTRL pHeader);
      HRESULT (WINAPI *SetToolbar)(IConsole *This,LPTOOLBAR pToolbar);
      HRESULT (WINAPI *QueryResultView)(IConsole *This,LPUNKNOWN *pUnknown);
      HRESULT (WINAPI *QueryScopeImageList)(IConsole *This,LPIMAGELIST *ppImageList);
      HRESULT (WINAPI *QueryResultImageList)(IConsole *This,LPIMAGELIST *ppImageList);
      HRESULT (WINAPI *UpdateAllViews)(IConsole *This,LPDATAOBJECT lpDataObject,LPARAM data,LONG_PTR hint);
      HRESULT (WINAPI *MessageBox)(IConsole *This,LPCWSTR lpszText,LPCWSTR lpszTitle,UINT fuStyle,int *piRetval);
      HRESULT (WINAPI *QueryConsoleVerb)(IConsole *This,LPCONSOLEVERB *ppConsoleVerb);
      HRESULT (WINAPI *SelectScopeItem)(IConsole *This,HSCOPEITEM hScopeItem);
      HRESULT (WINAPI *GetMainWindow)(IConsole *This,HWND *phwnd);
      HRESULT (WINAPI *NewWindow)(IConsole *This,HSCOPEITEM hScopeItem,unsigned __LONG32 lOptions);
    END_INTERFACE
  } IConsoleVtbl;
  struct IConsole {
    CONST_VTBL struct IConsoleVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IConsole_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConsole_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConsole_Release(This) (This)->lpVtbl->Release(This)
#define IConsole_SetHeader(This,pHeader) (This)->lpVtbl->SetHeader(This,pHeader)
#define IConsole_SetToolbar(This,pToolbar) (This)->lpVtbl->SetToolbar(This,pToolbar)
#define IConsole_QueryResultView(This,pUnknown) (This)->lpVtbl->QueryResultView(This,pUnknown)
#define IConsole_QueryScopeImageList(This,ppImageList) (This)->lpVtbl->QueryScopeImageList(This,ppImageList)
#define IConsole_QueryResultImageList(This,ppImageList) (This)->lpVtbl->QueryResultImageList(This,ppImageList)
#define IConsole_UpdateAllViews(This,lpDataObject,data,hint) (This)->lpVtbl->UpdateAllViews(This,lpDataObject,data,hint)
#define IConsole_MessageBox(This,lpszText,lpszTitle,fuStyle,piRetval) (This)->lpVtbl->MessageBox(This,lpszText,lpszTitle,fuStyle,piRetval)
#define IConsole_QueryConsoleVerb(This,ppConsoleVerb) (This)->lpVtbl->QueryConsoleVerb(This,ppConsoleVerb)
#define IConsole_SelectScopeItem(This,hScopeItem) (This)->lpVtbl->SelectScopeItem(This,hScopeItem)
#define IConsole_GetMainWindow(This,phwnd) (This)->lpVtbl->GetMainWindow(This,phwnd)
#define IConsole_NewWindow(This,hScopeItem,lOptions) (This)->lpVtbl->NewWindow(This,hScopeItem,lOptions)
#endif
#endif
  HRESULT WINAPI IConsole_SetHeader_Proxy(IConsole *This,LPHEADERCTRL pHeader);
  void __RPC_STUB IConsole_SetHeader_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_SetToolbar_Proxy(IConsole *This,LPTOOLBAR pToolbar);
  void __RPC_STUB IConsole_SetToolbar_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_QueryResultView_Proxy(IConsole *This,LPUNKNOWN *pUnknown);
  void __RPC_STUB IConsole_QueryResultView_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_QueryScopeImageList_Proxy(IConsole *This,LPIMAGELIST *ppImageList);
  void __RPC_STUB IConsole_QueryScopeImageList_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_QueryResultImageList_Proxy(IConsole *This,LPIMAGELIST *ppImageList);
  void __RPC_STUB IConsole_QueryResultImageList_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_UpdateAllViews_Proxy(IConsole *This,LPDATAOBJECT lpDataObject,LPARAM data,LONG_PTR hint);
  void __RPC_STUB IConsole_UpdateAllViews_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_MessageBox_Proxy(IConsole *This,LPCWSTR lpszText,LPCWSTR lpszTitle,UINT fuStyle,int *piRetval);
  void __RPC_STUB IConsole_MessageBox_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_QueryConsoleVerb_Proxy(IConsole *This,LPCONSOLEVERB *ppConsoleVerb);
  void __RPC_STUB IConsole_QueryConsoleVerb_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_SelectScopeItem_Proxy(IConsole *This,HSCOPEITEM hScopeItem);
  void __RPC_STUB IConsole_SelectScopeItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_GetMainWindow_Proxy(IConsole *This,HWND *phwnd);
  void __RPC_STUB IConsole_GetMainWindow_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole_NewWindow_Proxy(IConsole *This,HSCOPEITEM hScopeItem,unsigned __LONG32 lOptions);
  void __RPC_STUB IConsole_NewWindow_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IHeaderCtrl_INTERFACE_DEFINED__
#define __IHeaderCtrl_INTERFACE_DEFINED__
#define AUTO_WIDTH (-1)
#if (MMC_VER >= 0x0120)
#define HIDE_COLUMN (-4)
#endif
  EXTERN_C const IID IID_IHeaderCtrl;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IHeaderCtrl : public IUnknown {
  public:
    virtual HRESULT WINAPI InsertColumn(int nCol,LPCWSTR title,int nFormat,int nWidth) = 0;
    virtual HRESULT WINAPI DeleteColumn(int nCol) = 0;
    virtual HRESULT WINAPI SetColumnText(int nCol,LPCWSTR title) = 0;
    virtual HRESULT WINAPI GetColumnText(int nCol,LPOLESTR *pText) = 0;
    virtual HRESULT WINAPI SetColumnWidth(int nCol,int nWidth) = 0;
    virtual HRESULT WINAPI GetColumnWidth(int nCol,int *pWidth) = 0;
  };
#else
  typedef struct IHeaderCtrlVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IHeaderCtrl *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IHeaderCtrl *This);
      ULONG (WINAPI *Release)(IHeaderCtrl *This);
      HRESULT (WINAPI *InsertColumn)(IHeaderCtrl *This,int nCol,LPCWSTR title,int nFormat,int nWidth);
      HRESULT (WINAPI *DeleteColumn)(IHeaderCtrl *This,int nCol);
      HRESULT (WINAPI *SetColumnText)(IHeaderCtrl *This,int nCol,LPCWSTR title);
      HRESULT (WINAPI *GetColumnText)(IHeaderCtrl *This,int nCol,LPOLESTR *pText);
      HRESULT (WINAPI *SetColumnWidth)(IHeaderCtrl *This,int nCol,int nWidth);
      HRESULT (WINAPI *GetColumnWidth)(IHeaderCtrl *This,int nCol,int *pWidth);
    END_INTERFACE
  } IHeaderCtrlVtbl;
  struct IHeaderCtrl {
    CONST_VTBL struct IHeaderCtrlVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IHeaderCtrl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHeaderCtrl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHeaderCtrl_Release(This) (This)->lpVtbl->Release(This)
#define IHeaderCtrl_InsertColumn(This,nCol,title,nFormat,nWidth) (This)->lpVtbl->InsertColumn(This,nCol,title,nFormat,nWidth)
#define IHeaderCtrl_DeleteColumn(This,nCol) (This)->lpVtbl->DeleteColumn(This,nCol)
#define IHeaderCtrl_SetColumnText(This,nCol,title) (This)->lpVtbl->SetColumnText(This,nCol,title)
#define IHeaderCtrl_GetColumnText(This,nCol,pText) (This)->lpVtbl->GetColumnText(This,nCol,pText)
#define IHeaderCtrl_SetColumnWidth(This,nCol,nWidth) (This)->lpVtbl->SetColumnWidth(This,nCol,nWidth)
#define IHeaderCtrl_GetColumnWidth(This,nCol,pWidth) (This)->lpVtbl->GetColumnWidth(This,nCol,pWidth)
#endif
#endif
  HRESULT WINAPI IHeaderCtrl_InsertColumn_Proxy(IHeaderCtrl *This,int nCol,LPCWSTR title,int nFormat,int nWidth);
  void __RPC_STUB IHeaderCtrl_InsertColumn_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IHeaderCtrl_DeleteColumn_Proxy(IHeaderCtrl *This,int nCol);
  void __RPC_STUB IHeaderCtrl_DeleteColumn_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IHeaderCtrl_SetColumnText_Proxy(IHeaderCtrl *This,int nCol,LPCWSTR title);
  void __RPC_STUB IHeaderCtrl_SetColumnText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IHeaderCtrl_GetColumnText_Proxy(IHeaderCtrl *This,int nCol,LPOLESTR *pText);
  void __RPC_STUB IHeaderCtrl_GetColumnText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IHeaderCtrl_SetColumnWidth_Proxy(IHeaderCtrl *This,int nCol,int nWidth);
  void __RPC_STUB IHeaderCtrl_SetColumnWidth_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IHeaderCtrl_GetColumnWidth_Proxy(IHeaderCtrl *This,int nCol,int *pWidth);
  void __RPC_STUB IHeaderCtrl_GetColumnWidth_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  enum __MIDL___MIDL_itf_mmc_0121_0001 {
    CCM_INSERTIONPOINTID_MASK_SPECIAL = 0xffff0000,CCM_INSERTIONPOINTID_MASK_SHARED = 0x80000000,CCM_INSERTIONPOINTID_MASK_CREATE_PRIMARY = 0x40000000,
    CCM_INSERTIONPOINTID_MASK_ADD_PRIMARY = 0x20000000,CCM_INSERTIONPOINTID_MASK_ADD_3RDPARTY = 0x10000000,
    CCM_INSERTIONPOINTID_MASK_RESERVED = 0xfff0000,CCM_INSERTIONPOINTID_MASK_FLAGINDEX = 0x1f,CCM_INSERTIONPOINTID_PRIMARY_TOP = 0xa0000000,
    CCM_INSERTIONPOINTID_PRIMARY_NEW = 0xa0000001,CCM_INSERTIONPOINTID_PRIMARY_TASK = 0xa0000002,CCM_INSERTIONPOINTID_PRIMARY_VIEW = 0xa0000003,
    CCM_INSERTIONPOINTID_3RDPARTY_NEW = 0x90000001,CCM_INSERTIONPOINTID_3RDPARTY_TASK = 0x90000002,CCM_INSERTIONPOINTID_ROOT_MENU = 0x80000000
  };

  enum __MIDL___MIDL_itf_mmc_0121_0002 {
    CCM_INSERTIONALLOWED_TOP = 1 << (CCM_INSERTIONPOINTID_PRIMARY_TOP & CCM_INSERTIONPOINTID_MASK_FLAGINDEX),
    CCM_INSERTIONALLOWED_NEW = 1 << (CCM_INSERTIONPOINTID_PRIMARY_NEW & CCM_INSERTIONPOINTID_MASK_FLAGINDEX),
    CCM_INSERTIONALLOWED_TASK = 1 << (CCM_INSERTIONPOINTID_PRIMARY_TASK & CCM_INSERTIONPOINTID_MASK_FLAGINDEX),
    CCM_INSERTIONALLOWED_VIEW = 1 << (CCM_INSERTIONPOINTID_PRIMARY_VIEW & CCM_INSERTIONPOINTID_MASK_FLAGINDEX)
  };

  enum __MIDL___MIDL_itf_mmc_0121_0003 {
    CCM_COMMANDID_MASK_RESERVED = 0xffff0000
  };

  enum __MIDL___MIDL_itf_mmc_0121_0004 {
    CCM_SPECIAL_SEPARATOR = 0x1,CCM_SPECIAL_SUBMENU = 0x2,CCM_SPECIAL_DEFAULT_ITEM = 0x4,CCM_SPECIAL_INSERTION_POINT = 0x8,
    CCM_SPECIAL_TESTONLY = 0x10
  };

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0121_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0121_v0_0_s_ifspec;

#ifndef __IContextMenuCallback_INTERFACE_DEFINED__
#define __IContextMenuCallback_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IContextMenuCallback;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IContextMenuCallback : public IUnknown {
  public:
    virtual HRESULT WINAPI AddItem(CONTEXTMENUITEM *pItem) = 0;
  };
#else
  typedef struct IContextMenuCallbackVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IContextMenuCallback *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IContextMenuCallback *This);
      ULONG (WINAPI *Release)(IContextMenuCallback *This);
      HRESULT (WINAPI *AddItem)(IContextMenuCallback *This,CONTEXTMENUITEM *pItem);
    END_INTERFACE
  } IContextMenuCallbackVtbl;
  struct IContextMenuCallback {
    CONST_VTBL struct IContextMenuCallbackVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IContextMenuCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IContextMenuCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IContextMenuCallback_Release(This) (This)->lpVtbl->Release(This)
#define IContextMenuCallback_AddItem(This,pItem) (This)->lpVtbl->AddItem(This,pItem)
#endif
#endif
  HRESULT WINAPI IContextMenuCallback_AddItem_Proxy(IContextMenuCallback *This,CONTEXTMENUITEM *pItem);
  void __RPC_STUB IContextMenuCallback_AddItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IContextMenuProvider_INTERFACE_DEFINED__
#define __IContextMenuProvider_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IContextMenuProvider;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IContextMenuProvider : public IContextMenuCallback {
  public:
    virtual HRESULT WINAPI EmptyMenuList(void) = 0;
    virtual HRESULT WINAPI AddPrimaryExtensionItems(LPUNKNOWN piExtension,LPDATAOBJECT piDataObject) = 0;
    virtual HRESULT WINAPI AddThirdPartyExtensionItems(LPDATAOBJECT piDataObject) = 0;
    virtual HRESULT WINAPI ShowContextMenu(HWND hwndParent,__LONG32 xPos,__LONG32 yPos,__LONG32 *plSelected) = 0;
  };
#else
  typedef struct IContextMenuProviderVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IContextMenuProvider *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IContextMenuProvider *This);
      ULONG (WINAPI *Release)(IContextMenuProvider *This);
      HRESULT (WINAPI *AddItem)(IContextMenuProvider *This,CONTEXTMENUITEM *pItem);
      HRESULT (WINAPI *EmptyMenuList)(IContextMenuProvider *This);
      HRESULT (WINAPI *AddPrimaryExtensionItems)(IContextMenuProvider *This,LPUNKNOWN piExtension,LPDATAOBJECT piDataObject);
      HRESULT (WINAPI *AddThirdPartyExtensionItems)(IContextMenuProvider *This,LPDATAOBJECT piDataObject);
      HRESULT (WINAPI *ShowContextMenu)(IContextMenuProvider *This,HWND hwndParent,__LONG32 xPos,__LONG32 yPos,__LONG32 *plSelected);
    END_INTERFACE
  } IContextMenuProviderVtbl;
  struct IContextMenuProvider {
    CONST_VTBL struct IContextMenuProviderVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IContextMenuProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IContextMenuProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IContextMenuProvider_Release(This) (This)->lpVtbl->Release(This)
#define IContextMenuProvider_AddItem(This,pItem) (This)->lpVtbl->AddItem(This,pItem)
#define IContextMenuProvider_EmptyMenuList(This) (This)->lpVtbl->EmptyMenuList(This)
#define IContextMenuProvider_AddPrimaryExtensionItems(This,piExtension,piDataObject) (This)->lpVtbl->AddPrimaryExtensionItems(This,piExtension,piDataObject)
#define IContextMenuProvider_AddThirdPartyExtensionItems(This,piDataObject) (This)->lpVtbl->AddThirdPartyExtensionItems(This,piDataObject)
#define IContextMenuProvider_ShowContextMenu(This,hwndParent,xPos,yPos,plSelected) (This)->lpVtbl->ShowContextMenu(This,hwndParent,xPos,yPos,plSelected)
#endif
#endif
  HRESULT WINAPI IContextMenuProvider_EmptyMenuList_Proxy(IContextMenuProvider *This);
  void __RPC_STUB IContextMenuProvider_EmptyMenuList_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IContextMenuProvider_AddPrimaryExtensionItems_Proxy(IContextMenuProvider *This,LPUNKNOWN piExtension,LPDATAOBJECT piDataObject);
  void __RPC_STUB IContextMenuProvider_AddPrimaryExtensionItems_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IContextMenuProvider_AddThirdPartyExtensionItems_Proxy(IContextMenuProvider *This,LPDATAOBJECT piDataObject);
  void __RPC_STUB IContextMenuProvider_AddThirdPartyExtensionItems_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IContextMenuProvider_ShowContextMenu_Proxy(IContextMenuProvider *This,HWND hwndParent,__LONG32 xPos,__LONG32 yPos,__LONG32 *plSelected);
  void __RPC_STUB IContextMenuProvider_ShowContextMenu_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IExtendContextMenu_INTERFACE_DEFINED__
#define __IExtendContextMenu_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IExtendContextMenu;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IExtendContextMenu : public IUnknown {
  public:
    virtual HRESULT WINAPI AddMenuItems(LPDATAOBJECT piDataObject,LPCONTEXTMENUCALLBACK piCallback,__LONG32 *pInsertionAllowed) = 0;
    virtual HRESULT WINAPI Command(__LONG32 lCommandID,LPDATAOBJECT piDataObject) = 0;
  };
#else
  typedef struct IExtendContextMenuVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IExtendContextMenu *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IExtendContextMenu *This);
      ULONG (WINAPI *Release)(IExtendContextMenu *This);
      HRESULT (WINAPI *AddMenuItems)(IExtendContextMenu *This,LPDATAOBJECT piDataObject,LPCONTEXTMENUCALLBACK piCallback,__LONG32 *pInsertionAllowed);
      HRESULT (WINAPI *Command)(IExtendContextMenu *This,__LONG32 lCommandID,LPDATAOBJECT piDataObject);
    END_INTERFACE
  } IExtendContextMenuVtbl;
  struct IExtendContextMenu {
    CONST_VTBL struct IExtendContextMenuVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IExtendContextMenu_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExtendContextMenu_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExtendContextMenu_Release(This) (This)->lpVtbl->Release(This)
#define IExtendContextMenu_AddMenuItems(This,piDataObject,piCallback,pInsertionAllowed) (This)->lpVtbl->AddMenuItems(This,piDataObject,piCallback,pInsertionAllowed)
#define IExtendContextMenu_Command(This,lCommandID,piDataObject) (This)->lpVtbl->Command(This,lCommandID,piDataObject)
#endif
#endif
  HRESULT WINAPI IExtendContextMenu_AddMenuItems_Proxy(IExtendContextMenu *This,LPDATAOBJECT piDataObject,LPCONTEXTMENUCALLBACK piCallback,__LONG32 *pInsertionAllowed);
  void __RPC_STUB IExtendContextMenu_AddMenuItems_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IExtendContextMenu_Command_Proxy(IExtendContextMenu *This,__LONG32 lCommandID,LPDATAOBJECT piDataObject);
  void __RPC_STUB IExtendContextMenu_Command_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#if (MMC_VER >= 0x0120)
#define ILSIF_LEAVE_LARGE_ICON 0x40000000
#define ILSIF_LEAVE_SMALL_ICON 0x20000000
#define ILSIF_LEAVE_MASK (ILSIF_LEAVE_LARGE_ICON | ILSIF_LEAVE_SMALL_ICON)
#define ILSI_LARGE_ICON(nLoc) (nLoc | ILSIF_LEAVE_SMALL_ICON)
#define ILSI_SMALL_ICON(nLoc) (nLoc | ILSIF_LEAVE_LARGE_ICON)
#endif

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0124_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0124_v0_0_s_ifspec;
#ifndef __IImageList_INTERFACE_DEFINED__
#define __IImageList_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IImageList;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IImageList : public IUnknown {
  public:
    virtual HRESULT WINAPI ImageListSetIcon(LONG_PTR *pIcon,__LONG32 nLoc) = 0;
    virtual HRESULT WINAPI ImageListSetStrip(LONG_PTR *pBMapSm,LONG_PTR *pBMapLg,__LONG32 nStartLoc,COLORREF cMask) = 0;
  };
#else
  typedef struct IImageListVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IImageList *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IImageList *This);
      ULONG (WINAPI *Release)(IImageList *This);
      HRESULT (WINAPI *ImageListSetIcon)(IImageList *This,LONG_PTR *pIcon,__LONG32 nLoc);
      HRESULT (WINAPI *ImageListSetStrip)(IImageList *This,LONG_PTR *pBMapSm,LONG_PTR *pBMapLg,__LONG32 nStartLoc,COLORREF cMask);
    END_INTERFACE
  } IImageListVtbl;
  struct IImageList {
    CONST_VTBL struct IImageListVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IImageList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IImageList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IImageList_Release(This) (This)->lpVtbl->Release(This)
#define IImageList_ImageListSetIcon(This,pIcon,nLoc) (This)->lpVtbl->ImageListSetIcon(This,pIcon,nLoc)
#define IImageList_ImageListSetStrip(This,pBMapSm,pBMapLg,nStartLoc,cMask) (This)->lpVtbl->ImageListSetStrip(This,pBMapSm,pBMapLg,nStartLoc,cMask)
#endif
#endif
  HRESULT WINAPI IImageList_ImageListSetIcon_Proxy(IImageList *This,LONG_PTR *pIcon,__LONG32 nLoc);
  void __RPC_STUB IImageList_ImageListSetIcon_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IImageList_ImageListSetStrip_Proxy(IImageList *This,LONG_PTR *pBMapSm,LONG_PTR *pBMapLg,__LONG32 nStartLoc,COLORREF cMask);
  void __RPC_STUB IImageList_ImageListSetStrip_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IResultData_INTERFACE_DEFINED__
#define __IResultData_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IResultData;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IResultData : public IUnknown {
  public:
    virtual HRESULT WINAPI InsertItem(LPRESULTDATAITEM item) = 0;
    virtual HRESULT WINAPI DeleteItem(HRESULTITEM itemID,int nCol) = 0;
    virtual HRESULT WINAPI FindItemByLParam(LPARAM lParam,HRESULTITEM *pItemID) = 0;
    virtual HRESULT WINAPI DeleteAllRsltItems(void) = 0;
    virtual HRESULT WINAPI SetItem(LPRESULTDATAITEM item) = 0;
    virtual HRESULT WINAPI GetItem(LPRESULTDATAITEM item) = 0;
    virtual HRESULT WINAPI GetNextItem(LPRESULTDATAITEM item) = 0;
    virtual HRESULT WINAPI ModifyItemState(int nIndex,HRESULTITEM itemID,UINT uAdd,UINT uRemove) = 0;
    virtual HRESULT WINAPI ModifyViewStyle(MMC_RESULT_VIEW_STYLE add,MMC_RESULT_VIEW_STYLE remove) = 0;
    virtual HRESULT WINAPI SetViewMode(__LONG32 lViewMode) = 0;
    virtual HRESULT WINAPI GetViewMode(__LONG32 *lViewMode) = 0;
    virtual HRESULT WINAPI UpdateItem(HRESULTITEM itemID) = 0;
    virtual HRESULT WINAPI Sort(int nColumn,DWORD dwSortOptions,LPARAM lUserParam) = 0;
    virtual HRESULT WINAPI SetDescBarText(LPOLESTR DescText) = 0;
    virtual HRESULT WINAPI SetItemCount(int nItemCount,DWORD dwOptions) = 0;
  };
#else
  typedef struct IResultDataVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IResultData *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IResultData *This);
      ULONG (WINAPI *Release)(IResultData *This);
      HRESULT (WINAPI *InsertItem)(IResultData *This,LPRESULTDATAITEM item);
      HRESULT (WINAPI *DeleteItem)(IResultData *This,HRESULTITEM itemID,int nCol);
      HRESULT (WINAPI *FindItemByLParam)(IResultData *This,LPARAM lParam,HRESULTITEM *pItemID);
      HRESULT (WINAPI *DeleteAllRsltItems)(IResultData *This);
      HRESULT (WINAPI *SetItem)(IResultData *This,LPRESULTDATAITEM item);
      HRESULT (WINAPI *GetItem)(IResultData *This,LPRESULTDATAITEM item);
      HRESULT (WINAPI *GetNextItem)(IResultData *This,LPRESULTDATAITEM item);
      HRESULT (WINAPI *ModifyItemState)(IResultData *This,int nIndex,HRESULTITEM itemID,UINT uAdd,UINT uRemove);
      HRESULT (WINAPI *ModifyViewStyle)(IResultData *This,MMC_RESULT_VIEW_STYLE add,MMC_RESULT_VIEW_STYLE remove);
      HRESULT (WINAPI *SetViewMode)(IResultData *This,__LONG32 lViewMode);
      HRESULT (WINAPI *GetViewMode)(IResultData *This,__LONG32 *lViewMode);
      HRESULT (WINAPI *UpdateItem)(IResultData *This,HRESULTITEM itemID);
      HRESULT (WINAPI *Sort)(IResultData *This,int nColumn,DWORD dwSortOptions,LPARAM lUserParam);
      HRESULT (WINAPI *SetDescBarText)(IResultData *This,LPOLESTR DescText);
      HRESULT (WINAPI *SetItemCount)(IResultData *This,int nItemCount,DWORD dwOptions);
    END_INTERFACE
  } IResultDataVtbl;
  struct IResultData {
    CONST_VTBL struct IResultDataVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IResultData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IResultData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IResultData_Release(This) (This)->lpVtbl->Release(This)
#define IResultData_InsertItem(This,item) (This)->lpVtbl->InsertItem(This,item)
#define IResultData_DeleteItem(This,itemID,nCol) (This)->lpVtbl->DeleteItem(This,itemID,nCol)
#define IResultData_FindItemByLParam(This,lParam,pItemID) (This)->lpVtbl->FindItemByLParam(This,lParam,pItemID)
#define IResultData_DeleteAllRsltItems(This) (This)->lpVtbl->DeleteAllRsltItems(This)
#define IResultData_SetItem(This,item) (This)->lpVtbl->SetItem(This,item)
#define IResultData_GetItem(This,item) (This)->lpVtbl->GetItem(This,item)
#define IResultData_GetNextItem(This,item) (This)->lpVtbl->GetNextItem(This,item)
#define IResultData_ModifyItemState(This,nIndex,itemID,uAdd,uRemove) (This)->lpVtbl->ModifyItemState(This,nIndex,itemID,uAdd,uRemove)
#define IResultData_ModifyViewStyle(This,add,remove) (This)->lpVtbl->ModifyViewStyle(This,add,remove)
#define IResultData_SetViewMode(This,lViewMode) (This)->lpVtbl->SetViewMode(This,lViewMode)
#define IResultData_GetViewMode(This,lViewMode) (This)->lpVtbl->GetViewMode(This,lViewMode)
#define IResultData_UpdateItem(This,itemID) (This)->lpVtbl->UpdateItem(This,itemID)
#define IResultData_Sort(This,nColumn,dwSortOptions,lUserParam) (This)->lpVtbl->Sort(This,nColumn,dwSortOptions,lUserParam)
#define IResultData_SetDescBarText(This,DescText) (This)->lpVtbl->SetDescBarText(This,DescText)
#define IResultData_SetItemCount(This,nItemCount,dwOptions) (This)->lpVtbl->SetItemCount(This,nItemCount,dwOptions)
#endif
#endif
  HRESULT WINAPI IResultData_InsertItem_Proxy(IResultData *This,LPRESULTDATAITEM item);
  void __RPC_STUB IResultData_InsertItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_DeleteItem_Proxy(IResultData *This,HRESULTITEM itemID,int nCol);
  void __RPC_STUB IResultData_DeleteItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_FindItemByLParam_Proxy(IResultData *This,LPARAM lParam,HRESULTITEM *pItemID);
  void __RPC_STUB IResultData_FindItemByLParam_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_DeleteAllRsltItems_Proxy(IResultData *This);
  void __RPC_STUB IResultData_DeleteAllRsltItems_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_SetItem_Proxy(IResultData *This,LPRESULTDATAITEM item);
  void __RPC_STUB IResultData_SetItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_GetItem_Proxy(IResultData *This,LPRESULTDATAITEM item);
  void __RPC_STUB IResultData_GetItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_GetNextItem_Proxy(IResultData *This,LPRESULTDATAITEM item);
  void __RPC_STUB IResultData_GetNextItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_ModifyItemState_Proxy(IResultData *This,int nIndex,HRESULTITEM itemID,UINT uAdd,UINT uRemove);
  void __RPC_STUB IResultData_ModifyItemState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_ModifyViewStyle_Proxy(IResultData *This,MMC_RESULT_VIEW_STYLE add,MMC_RESULT_VIEW_STYLE remove);
  void __RPC_STUB IResultData_ModifyViewStyle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_SetViewMode_Proxy(IResultData *This,__LONG32 lViewMode);
  void __RPC_STUB IResultData_SetViewMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_GetViewMode_Proxy(IResultData *This,__LONG32 *lViewMode);
  void __RPC_STUB IResultData_GetViewMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_UpdateItem_Proxy(IResultData *This,HRESULTITEM itemID);
  void __RPC_STUB IResultData_UpdateItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_Sort_Proxy(IResultData *This,int nColumn,DWORD dwSortOptions,LPARAM lUserParam);
  void __RPC_STUB IResultData_Sort_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_SetDescBarText_Proxy(IResultData *This,LPOLESTR DescText);
  void __RPC_STUB IResultData_SetDescBarText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IResultData_SetItemCount_Proxy(IResultData *This,int nItemCount,DWORD dwOptions);
  void __RPC_STUB IResultData_SetItemCount_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IConsoleNameSpace_INTERFACE_DEFINED__
#define __IConsoleNameSpace_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IConsoleNameSpace;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IConsoleNameSpace : public IUnknown {
  public:
    virtual HRESULT WINAPI InsertItem(LPSCOPEDATAITEM item) = 0;
    virtual HRESULT WINAPI DeleteItem(HSCOPEITEM hItem,__LONG32 fDeleteThis) = 0;
    virtual HRESULT WINAPI SetItem(LPSCOPEDATAITEM item) = 0;
    virtual HRESULT WINAPI GetItem(LPSCOPEDATAITEM item) = 0;
    virtual HRESULT WINAPI GetChildItem(HSCOPEITEM item,HSCOPEITEM *pItemChild,MMC_COOKIE *pCookie) = 0;
    virtual HRESULT WINAPI GetNextItem(HSCOPEITEM item,HSCOPEITEM *pItemNext,MMC_COOKIE *pCookie) = 0;
    virtual HRESULT WINAPI GetParentItem(HSCOPEITEM item,HSCOPEITEM *pItemParent,MMC_COOKIE *pCookie) = 0;
  };
#else
  typedef struct IConsoleNameSpaceVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IConsoleNameSpace *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IConsoleNameSpace *This);
      ULONG (WINAPI *Release)(IConsoleNameSpace *This);
      HRESULT (WINAPI *InsertItem)(IConsoleNameSpace *This,LPSCOPEDATAITEM item);
      HRESULT (WINAPI *DeleteItem)(IConsoleNameSpace *This,HSCOPEITEM hItem,__LONG32 fDeleteThis);
      HRESULT (WINAPI *SetItem)(IConsoleNameSpace *This,LPSCOPEDATAITEM item);
      HRESULT (WINAPI *GetItem)(IConsoleNameSpace *This,LPSCOPEDATAITEM item);
      HRESULT (WINAPI *GetChildItem)(IConsoleNameSpace *This,HSCOPEITEM item,HSCOPEITEM *pItemChild,MMC_COOKIE *pCookie);
      HRESULT (WINAPI *GetNextItem)(IConsoleNameSpace *This,HSCOPEITEM item,HSCOPEITEM *pItemNext,MMC_COOKIE *pCookie);
      HRESULT (WINAPI *GetParentItem)(IConsoleNameSpace *This,HSCOPEITEM item,HSCOPEITEM *pItemParent,MMC_COOKIE *pCookie);
    END_INTERFACE
  } IConsoleNameSpaceVtbl;
  struct IConsoleNameSpace {
    CONST_VTBL struct IConsoleNameSpaceVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IConsoleNameSpace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConsoleNameSpace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConsoleNameSpace_Release(This) (This)->lpVtbl->Release(This)
#define IConsoleNameSpace_InsertItem(This,item) (This)->lpVtbl->InsertItem(This,item)
#define IConsoleNameSpace_DeleteItem(This,hItem,fDeleteThis) (This)->lpVtbl->DeleteItem(This,hItem,fDeleteThis)
#define IConsoleNameSpace_SetItem(This,item) (This)->lpVtbl->SetItem(This,item)
#define IConsoleNameSpace_GetItem(This,item) (This)->lpVtbl->GetItem(This,item)
#define IConsoleNameSpace_GetChildItem(This,item,pItemChild,pCookie) (This)->lpVtbl->GetChildItem(This,item,pItemChild,pCookie)
#define IConsoleNameSpace_GetNextItem(This,item,pItemNext,pCookie) (This)->lpVtbl->GetNextItem(This,item,pItemNext,pCookie)
#define IConsoleNameSpace_GetParentItem(This,item,pItemParent,pCookie) (This)->lpVtbl->GetParentItem(This,item,pItemParent,pCookie)
#endif
#endif
  HRESULT WINAPI IConsoleNameSpace_InsertItem_Proxy(IConsoleNameSpace *This,LPSCOPEDATAITEM item);
  void __RPC_STUB IConsoleNameSpace_InsertItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleNameSpace_DeleteItem_Proxy(IConsoleNameSpace *This,HSCOPEITEM hItem,__LONG32 fDeleteThis);
  void __RPC_STUB IConsoleNameSpace_DeleteItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleNameSpace_SetItem_Proxy(IConsoleNameSpace *This,LPSCOPEDATAITEM item);
  void __RPC_STUB IConsoleNameSpace_SetItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleNameSpace_GetItem_Proxy(IConsoleNameSpace *This,LPSCOPEDATAITEM item);
  void __RPC_STUB IConsoleNameSpace_GetItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleNameSpace_GetChildItem_Proxy(IConsoleNameSpace *This,HSCOPEITEM item,HSCOPEITEM *pItemChild,MMC_COOKIE *pCookie);
  void __RPC_STUB IConsoleNameSpace_GetChildItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleNameSpace_GetNextItem_Proxy(IConsoleNameSpace *This,HSCOPEITEM item,HSCOPEITEM *pItemNext,MMC_COOKIE *pCookie);
  void __RPC_STUB IConsoleNameSpace_GetNextItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleNameSpace_GetParentItem_Proxy(IConsoleNameSpace *This,HSCOPEITEM item,HSCOPEITEM *pItemParent,MMC_COOKIE *pCookie);
  void __RPC_STUB IConsoleNameSpace_GetParentItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IConsoleNameSpace2_INTERFACE_DEFINED__
#define __IConsoleNameSpace2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IConsoleNameSpace2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IConsoleNameSpace2 : public IConsoleNameSpace {
  public:
    virtual HRESULT WINAPI Expand(HSCOPEITEM hItem) = 0;
    virtual HRESULT WINAPI AddExtension(HSCOPEITEM hItem,LPCLSID lpClsid) = 0;
  };
#else
  typedef struct IConsoleNameSpace2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IConsoleNameSpace2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IConsoleNameSpace2 *This);
      ULONG (WINAPI *Release)(IConsoleNameSpace2 *This);
      HRESULT (WINAPI *InsertItem)(IConsoleNameSpace2 *This,LPSCOPEDATAITEM item);
      HRESULT (WINAPI *DeleteItem)(IConsoleNameSpace2 *This,HSCOPEITEM hItem,__LONG32 fDeleteThis);
      HRESULT (WINAPI *SetItem)(IConsoleNameSpace2 *This,LPSCOPEDATAITEM item);
      HRESULT (WINAPI *GetItem)(IConsoleNameSpace2 *This,LPSCOPEDATAITEM item);
      HRESULT (WINAPI *GetChildItem)(IConsoleNameSpace2 *This,HSCOPEITEM item,HSCOPEITEM *pItemChild,MMC_COOKIE *pCookie);
      HRESULT (WINAPI *GetNextItem)(IConsoleNameSpace2 *This,HSCOPEITEM item,HSCOPEITEM *pItemNext,MMC_COOKIE *pCookie);
      HRESULT (WINAPI *GetParentItem)(IConsoleNameSpace2 *This,HSCOPEITEM item,HSCOPEITEM *pItemParent,MMC_COOKIE *pCookie);
      HRESULT (WINAPI *Expand)(IConsoleNameSpace2 *This,HSCOPEITEM hItem);
      HRESULT (WINAPI *AddExtension)(IConsoleNameSpace2 *This,HSCOPEITEM hItem,LPCLSID lpClsid);
    END_INTERFACE
  } IConsoleNameSpace2Vtbl;
  struct IConsoleNameSpace2 {
    CONST_VTBL struct IConsoleNameSpace2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IConsoleNameSpace2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConsoleNameSpace2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConsoleNameSpace2_Release(This) (This)->lpVtbl->Release(This)
#define IConsoleNameSpace2_InsertItem(This,item) (This)->lpVtbl->InsertItem(This,item)
#define IConsoleNameSpace2_DeleteItem(This,hItem,fDeleteThis) (This)->lpVtbl->DeleteItem(This,hItem,fDeleteThis)
#define IConsoleNameSpace2_SetItem(This,item) (This)->lpVtbl->SetItem(This,item)
#define IConsoleNameSpace2_GetItem(This,item) (This)->lpVtbl->GetItem(This,item)
#define IConsoleNameSpace2_GetChildItem(This,item,pItemChild,pCookie) (This)->lpVtbl->GetChildItem(This,item,pItemChild,pCookie)
#define IConsoleNameSpace2_GetNextItem(This,item,pItemNext,pCookie) (This)->lpVtbl->GetNextItem(This,item,pItemNext,pCookie)
#define IConsoleNameSpace2_GetParentItem(This,item,pItemParent,pCookie) (This)->lpVtbl->GetParentItem(This,item,pItemParent,pCookie)
#define IConsoleNameSpace2_Expand(This,hItem) (This)->lpVtbl->Expand(This,hItem)
#define IConsoleNameSpace2_AddExtension(This,hItem,lpClsid) (This)->lpVtbl->AddExtension(This,hItem,lpClsid)
#endif
#endif
  HRESULT WINAPI IConsoleNameSpace2_Expand_Proxy(IConsoleNameSpace2 *This,HSCOPEITEM hItem);
  void __RPC_STUB IConsoleNameSpace2_Expand_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleNameSpace2_AddExtension_Proxy(IConsoleNameSpace2 *This,HSCOPEITEM hItem,LPCLSID lpClsid);
  void __RPC_STUB IConsoleNameSpace2_AddExtension_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  typedef struct _PSP *HPROPSHEETPAGE;

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0129_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0129_v0_0_s_ifspec;
#ifndef __IPropertySheetCallback_INTERFACE_DEFINED__
#define __IPropertySheetCallback_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IPropertySheetCallback;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IPropertySheetCallback : public IUnknown {
  public:
    virtual HRESULT WINAPI AddPage(HPROPSHEETPAGE hPage) = 0;
    virtual HRESULT WINAPI RemovePage(HPROPSHEETPAGE hPage) = 0;
  };
#else
  typedef struct IPropertySheetCallbackVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IPropertySheetCallback *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IPropertySheetCallback *This);
      ULONG (WINAPI *Release)(IPropertySheetCallback *This);
      HRESULT (WINAPI *AddPage)(IPropertySheetCallback *This,HPROPSHEETPAGE hPage);
      HRESULT (WINAPI *RemovePage)(IPropertySheetCallback *This,HPROPSHEETPAGE hPage);
    END_INTERFACE
  } IPropertySheetCallbackVtbl;
  struct IPropertySheetCallback {
    CONST_VTBL struct IPropertySheetCallbackVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IPropertySheetCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertySheetCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertySheetCallback_Release(This) (This)->lpVtbl->Release(This)
#define IPropertySheetCallback_AddPage(This,hPage) (This)->lpVtbl->AddPage(This,hPage)
#define IPropertySheetCallback_RemovePage(This,hPage) (This)->lpVtbl->RemovePage(This,hPage)
#endif
#endif
  HRESULT WINAPI IPropertySheetCallback_AddPage_Proxy(IPropertySheetCallback *This,HPROPSHEETPAGE hPage);
  void __RPC_STUB IPropertySheetCallback_AddPage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IPropertySheetCallback_RemovePage_Proxy(IPropertySheetCallback *This,HPROPSHEETPAGE hPage);
  void __RPC_STUB IPropertySheetCallback_RemovePage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IPropertySheetProvider_INTERFACE_DEFINED__
#define __IPropertySheetProvider_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IPropertySheetProvider;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IPropertySheetProvider : public IUnknown {
  public:
    virtual HRESULT WINAPI CreatePropertySheet(LPCWSTR title,boolean type,MMC_COOKIE cookie,LPDATAOBJECT pIDataObjectm,DWORD dwOptions) = 0;
    virtual HRESULT WINAPI FindPropertySheet(MMC_COOKIE cookie,LPCOMPONENT lpComponent,LPDATAOBJECT lpDataObject) = 0;
    virtual HRESULT WINAPI AddPrimaryPages(LPUNKNOWN lpUnknown,WINBOOL bCreateHandle,HWND hNotifyWindow,WINBOOL bScopePane) = 0;
    virtual HRESULT WINAPI AddExtensionPages(void) = 0;
    virtual HRESULT WINAPI Show(LONG_PTR window,int page) = 0;
  };
#else
  typedef struct IPropertySheetProviderVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IPropertySheetProvider *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IPropertySheetProvider *This);
      ULONG (WINAPI *Release)(IPropertySheetProvider *This);
      HRESULT (WINAPI *CreatePropertySheet)(IPropertySheetProvider *This,LPCWSTR title,boolean type,MMC_COOKIE cookie,LPDATAOBJECT pIDataObjectm,DWORD dwOptions);
      HRESULT (WINAPI *FindPropertySheet)(IPropertySheetProvider *This,MMC_COOKIE cookie,LPCOMPONENT lpComponent,LPDATAOBJECT lpDataObject);
      HRESULT (WINAPI *AddPrimaryPages)(IPropertySheetProvider *This,LPUNKNOWN lpUnknown,WINBOOL bCreateHandle,HWND hNotifyWindow,WINBOOL bScopePane);
      HRESULT (WINAPI *AddExtensionPages)(IPropertySheetProvider *This);
      HRESULT (WINAPI *Show)(IPropertySheetProvider *This,LONG_PTR window,int page);
    END_INTERFACE
  } IPropertySheetProviderVtbl;
  struct IPropertySheetProvider {
    CONST_VTBL struct IPropertySheetProviderVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IPropertySheetProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertySheetProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertySheetProvider_Release(This) (This)->lpVtbl->Release(This)
#define IPropertySheetProvider_CreatePropertySheet(This,title,type,cookie,pIDataObjectm,dwOptions) (This)->lpVtbl->CreatePropertySheet(This,title,type,cookie,pIDataObjectm,dwOptions)
#define IPropertySheetProvider_FindPropertySheet(This,cookie,lpComponent,lpDataObject) (This)->lpVtbl->FindPropertySheet(This,cookie,lpComponent,lpDataObject)
#define IPropertySheetProvider_AddPrimaryPages(This,lpUnknown,bCreateHandle,hNotifyWindow,bScopePane) (This)->lpVtbl->AddPrimaryPages(This,lpUnknown,bCreateHandle,hNotifyWindow,bScopePane)
#define IPropertySheetProvider_AddExtensionPages(This) (This)->lpVtbl->AddExtensionPages(This)
#define IPropertySheetProvider_Show(This,window,page) (This)->lpVtbl->Show(This,window,page)
#endif
#endif
  HRESULT WINAPI IPropertySheetProvider_CreatePropertySheet_Proxy(IPropertySheetProvider *This,LPCWSTR title,boolean type,MMC_COOKIE cookie,LPDATAOBJECT pIDataObjectm,DWORD dwOptions);
  void __RPC_STUB IPropertySheetProvider_CreatePropertySheet_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IPropertySheetProvider_FindPropertySheet_Proxy(IPropertySheetProvider *This,MMC_COOKIE cookie,LPCOMPONENT lpComponent,LPDATAOBJECT lpDataObject);
  void __RPC_STUB IPropertySheetProvider_FindPropertySheet_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IPropertySheetProvider_AddPrimaryPages_Proxy(IPropertySheetProvider *This,LPUNKNOWN lpUnknown,WINBOOL bCreateHandle,HWND hNotifyWindow,WINBOOL bScopePane);
  void __RPC_STUB IPropertySheetProvider_AddPrimaryPages_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IPropertySheetProvider_AddExtensionPages_Proxy(IPropertySheetProvider *This);
  void __RPC_STUB IPropertySheetProvider_AddExtensionPages_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IPropertySheetProvider_Show_Proxy(IPropertySheetProvider *This,LONG_PTR window,int page);
  void __RPC_STUB IPropertySheetProvider_Show_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IExtendPropertySheet_INTERFACE_DEFINED__
#define __IExtendPropertySheet_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IExtendPropertySheet;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IExtendPropertySheet : public IUnknown {
  public:
    virtual HRESULT WINAPI CreatePropertyPages(LPPROPERTYSHEETCALLBACK lpProvider,LONG_PTR handle,LPDATAOBJECT lpIDataObject) = 0;
    virtual HRESULT WINAPI QueryPagesFor(LPDATAOBJECT lpDataObject) = 0;
  };
#else
  typedef struct IExtendPropertySheetVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IExtendPropertySheet *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IExtendPropertySheet *This);
      ULONG (WINAPI *Release)(IExtendPropertySheet *This);
      HRESULT (WINAPI *CreatePropertyPages)(IExtendPropertySheet *This,LPPROPERTYSHEETCALLBACK lpProvider,LONG_PTR handle,LPDATAOBJECT lpIDataObject);
      HRESULT (WINAPI *QueryPagesFor)(IExtendPropertySheet *This,LPDATAOBJECT lpDataObject);
    END_INTERFACE
  } IExtendPropertySheetVtbl;
  struct IExtendPropertySheet {
    CONST_VTBL struct IExtendPropertySheetVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IExtendPropertySheet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExtendPropertySheet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExtendPropertySheet_Release(This) (This)->lpVtbl->Release(This)
#define IExtendPropertySheet_CreatePropertyPages(This,lpProvider,handle,lpIDataObject) (This)->lpVtbl->CreatePropertyPages(This,lpProvider,handle,lpIDataObject)
#define IExtendPropertySheet_QueryPagesFor(This,lpDataObject) (This)->lpVtbl->QueryPagesFor(This,lpDataObject)
#endif
#endif
  HRESULT WINAPI IExtendPropertySheet_CreatePropertyPages_Proxy(IExtendPropertySheet *This,LPPROPERTYSHEETCALLBACK lpProvider,LONG_PTR handle,LPDATAOBJECT lpIDataObject);
  void __RPC_STUB IExtendPropertySheet_CreatePropertyPages_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IExtendPropertySheet_QueryPagesFor_Proxy(IExtendPropertySheet *This,LPDATAOBJECT lpDataObject);
  void __RPC_STUB IExtendPropertySheet_QueryPagesFor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IControlbar_INTERFACE_DEFINED__
#define __IControlbar_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IControlbar;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IControlbar : public IUnknown {
  public:
    virtual HRESULT WINAPI Create(MMC_CONTROL_TYPE nType,LPEXTENDCONTROLBAR pExtendControlbar,LPUNKNOWN *ppUnknown) = 0;
    virtual HRESULT WINAPI Attach(MMC_CONTROL_TYPE nType,LPUNKNOWN lpUnknown) = 0;
    virtual HRESULT WINAPI Detach(LPUNKNOWN lpUnknown) = 0;
  };
#else
  typedef struct IControlbarVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IControlbar *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IControlbar *This);
      ULONG (WINAPI *Release)(IControlbar *This);
      HRESULT (WINAPI *Create)(IControlbar *This,MMC_CONTROL_TYPE nType,LPEXTENDCONTROLBAR pExtendControlbar,LPUNKNOWN *ppUnknown);
      HRESULT (WINAPI *Attach)(IControlbar *This,MMC_CONTROL_TYPE nType,LPUNKNOWN lpUnknown);
      HRESULT (WINAPI *Detach)(IControlbar *This,LPUNKNOWN lpUnknown);
    END_INTERFACE
  } IControlbarVtbl;
  struct IControlbar {
    CONST_VTBL struct IControlbarVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IControlbar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IControlbar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IControlbar_Release(This) (This)->lpVtbl->Release(This)
#define IControlbar_Create(This,nType,pExtendControlbar,ppUnknown) (This)->lpVtbl->Create(This,nType,pExtendControlbar,ppUnknown)
#define IControlbar_Attach(This,nType,lpUnknown) (This)->lpVtbl->Attach(This,nType,lpUnknown)
#define IControlbar_Detach(This,lpUnknown) (This)->lpVtbl->Detach(This,lpUnknown)
#endif
#endif
  HRESULT WINAPI IControlbar_Create_Proxy(IControlbar *This,MMC_CONTROL_TYPE nType,LPEXTENDCONTROLBAR pExtendControlbar,LPUNKNOWN *ppUnknown);
  void __RPC_STUB IControlbar_Create_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IControlbar_Attach_Proxy(IControlbar *This,MMC_CONTROL_TYPE nType,LPUNKNOWN lpUnknown);
  void __RPC_STUB IControlbar_Attach_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IControlbar_Detach_Proxy(IControlbar *This,LPUNKNOWN lpUnknown);
  void __RPC_STUB IControlbar_Detach_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IExtendControlbar_INTERFACE_DEFINED__
#define __IExtendControlbar_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IExtendControlbar;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IExtendControlbar : public IUnknown {
  public:
    virtual HRESULT WINAPI SetControlbar(LPCONTROLBAR pControlbar) = 0;
    virtual HRESULT WINAPI ControlbarNotify(MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param) = 0;
  };
#else
  typedef struct IExtendControlbarVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IExtendControlbar *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IExtendControlbar *This);
      ULONG (WINAPI *Release)(IExtendControlbar *This);
      HRESULT (WINAPI *SetControlbar)(IExtendControlbar *This,LPCONTROLBAR pControlbar);
      HRESULT (WINAPI *ControlbarNotify)(IExtendControlbar *This,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param);
    END_INTERFACE
  } IExtendControlbarVtbl;
  struct IExtendControlbar {
    CONST_VTBL struct IExtendControlbarVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IExtendControlbar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExtendControlbar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExtendControlbar_Release(This) (This)->lpVtbl->Release(This)
#define IExtendControlbar_SetControlbar(This,pControlbar) (This)->lpVtbl->SetControlbar(This,pControlbar)
#define IExtendControlbar_ControlbarNotify(This,event,arg,param) (This)->lpVtbl->ControlbarNotify(This,event,arg,param)
#endif
#endif
  HRESULT WINAPI IExtendControlbar_SetControlbar_Proxy(IExtendControlbar *This,LPCONTROLBAR pControlbar);
  void __RPC_STUB IExtendControlbar_SetControlbar_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IExtendControlbar_ControlbarNotify_Proxy(IExtendControlbar *This,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param);
  void __RPC_STUB IExtendControlbar_ControlbarNotify_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IToolbar_INTERFACE_DEFINED__
#define __IToolbar_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IToolbar;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IToolbar : public IUnknown {
  public:
    virtual HRESULT WINAPI AddBitmap(int nImages,HBITMAP hbmp,int cxSize,int cySize,COLORREF crMask) = 0;
    virtual HRESULT WINAPI AddButtons(int nButtons,LPMMCBUTTON lpButtons) = 0;
    virtual HRESULT WINAPI InsertButton(int nIndex,LPMMCBUTTON lpButton) = 0;
    virtual HRESULT WINAPI DeleteButton(int nIndex) = 0;
    virtual HRESULT WINAPI GetButtonState(int idCommand,MMC_BUTTON_STATE nState,WINBOOL *pState) = 0;
    virtual HRESULT WINAPI SetButtonState(int idCommand,MMC_BUTTON_STATE nState,WINBOOL bState) = 0;
  };
#else
  typedef struct IToolbarVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IToolbar *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IToolbar *This);
      ULONG (WINAPI *Release)(IToolbar *This);
      HRESULT (WINAPI *AddBitmap)(IToolbar *This,int nImages,HBITMAP hbmp,int cxSize,int cySize,COLORREF crMask);
      HRESULT (WINAPI *AddButtons)(IToolbar *This,int nButtons,LPMMCBUTTON lpButtons);
      HRESULT (WINAPI *InsertButton)(IToolbar *This,int nIndex,LPMMCBUTTON lpButton);
      HRESULT (WINAPI *DeleteButton)(IToolbar *This,int nIndex);
      HRESULT (WINAPI *GetButtonState)(IToolbar *This,int idCommand,MMC_BUTTON_STATE nState,WINBOOL *pState);
      HRESULT (WINAPI *SetButtonState)(IToolbar *This,int idCommand,MMC_BUTTON_STATE nState,WINBOOL bState);
    END_INTERFACE
  } IToolbarVtbl;
  struct IToolbar {
    CONST_VTBL struct IToolbarVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IToolbar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IToolbar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IToolbar_Release(This) (This)->lpVtbl->Release(This)
#define IToolbar_AddBitmap(This,nImages,hbmp,cxSize,cySize,crMask) (This)->lpVtbl->AddBitmap(This,nImages,hbmp,cxSize,cySize,crMask)
#define IToolbar_AddButtons(This,nButtons,lpButtons) (This)->lpVtbl->AddButtons(This,nButtons,lpButtons)
#define IToolbar_InsertButton(This,nIndex,lpButton) (This)->lpVtbl->InsertButton(This,nIndex,lpButton)
#define IToolbar_DeleteButton(This,nIndex) (This)->lpVtbl->DeleteButton(This,nIndex)
#define IToolbar_GetButtonState(This,idCommand,nState,pState) (This)->lpVtbl->GetButtonState(This,idCommand,nState,pState)
#define IToolbar_SetButtonState(This,idCommand,nState,bState) (This)->lpVtbl->SetButtonState(This,idCommand,nState,bState)
#endif
#endif
  HRESULT WINAPI IToolbar_AddBitmap_Proxy(IToolbar *This,int nImages,HBITMAP hbmp,int cxSize,int cySize,COLORREF crMask);
  void __RPC_STUB IToolbar_AddBitmap_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IToolbar_AddButtons_Proxy(IToolbar *This,int nButtons,LPMMCBUTTON lpButtons);
  void __RPC_STUB IToolbar_AddButtons_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IToolbar_InsertButton_Proxy(IToolbar *This,int nIndex,LPMMCBUTTON lpButton);
  void __RPC_STUB IToolbar_InsertButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IToolbar_DeleteButton_Proxy(IToolbar *This,int nIndex);
  void __RPC_STUB IToolbar_DeleteButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IToolbar_GetButtonState_Proxy(IToolbar *This,int idCommand,MMC_BUTTON_STATE nState,WINBOOL *pState);
  void __RPC_STUB IToolbar_GetButtonState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IToolbar_SetButtonState_Proxy(IToolbar *This,int idCommand,MMC_BUTTON_STATE nState,WINBOOL bState);
  void __RPC_STUB IToolbar_SetButtonState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IConsoleVerb_INTERFACE_DEFINED__
#define __IConsoleVerb_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IConsoleVerb;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IConsoleVerb : public IUnknown {
  public:
    virtual HRESULT WINAPI GetVerbState(MMC_CONSOLE_VERB eCmdID,MMC_BUTTON_STATE nState,WINBOOL *pState) = 0;
    virtual HRESULT WINAPI SetVerbState(MMC_CONSOLE_VERB eCmdID,MMC_BUTTON_STATE nState,WINBOOL bState) = 0;
    virtual HRESULT WINAPI SetDefaultVerb(MMC_CONSOLE_VERB eCmdID) = 0;
    virtual HRESULT WINAPI GetDefaultVerb(MMC_CONSOLE_VERB *peCmdID) = 0;
  };
#else
  typedef struct IConsoleVerbVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IConsoleVerb *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IConsoleVerb *This);
      ULONG (WINAPI *Release)(IConsoleVerb *This);
      HRESULT (WINAPI *GetVerbState)(IConsoleVerb *This,MMC_CONSOLE_VERB eCmdID,MMC_BUTTON_STATE nState,WINBOOL *pState);
      HRESULT (WINAPI *SetVerbState)(IConsoleVerb *This,MMC_CONSOLE_VERB eCmdID,MMC_BUTTON_STATE nState,WINBOOL bState);
      HRESULT (WINAPI *SetDefaultVerb)(IConsoleVerb *This,MMC_CONSOLE_VERB eCmdID);
      HRESULT (WINAPI *GetDefaultVerb)(IConsoleVerb *This,MMC_CONSOLE_VERB *peCmdID);
    END_INTERFACE
  } IConsoleVerbVtbl;
  struct IConsoleVerb {
    CONST_VTBL struct IConsoleVerbVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IConsoleVerb_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConsoleVerb_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConsoleVerb_Release(This) (This)->lpVtbl->Release(This)
#define IConsoleVerb_GetVerbState(This,eCmdID,nState,pState) (This)->lpVtbl->GetVerbState(This,eCmdID,nState,pState)
#define IConsoleVerb_SetVerbState(This,eCmdID,nState,bState) (This)->lpVtbl->SetVerbState(This,eCmdID,nState,bState)
#define IConsoleVerb_SetDefaultVerb(This,eCmdID) (This)->lpVtbl->SetDefaultVerb(This,eCmdID)
#define IConsoleVerb_GetDefaultVerb(This,peCmdID) (This)->lpVtbl->GetDefaultVerb(This,peCmdID)
#endif
#endif
  HRESULT WINAPI IConsoleVerb_GetVerbState_Proxy(IConsoleVerb *This,MMC_CONSOLE_VERB eCmdID,MMC_BUTTON_STATE nState,WINBOOL *pState);
  void __RPC_STUB IConsoleVerb_GetVerbState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleVerb_SetVerbState_Proxy(IConsoleVerb *This,MMC_CONSOLE_VERB eCmdID,MMC_BUTTON_STATE nState,WINBOOL bState);
  void __RPC_STUB IConsoleVerb_SetVerbState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleVerb_SetDefaultVerb_Proxy(IConsoleVerb *This,MMC_CONSOLE_VERB eCmdID);
  void __RPC_STUB IConsoleVerb_SetDefaultVerb_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsoleVerb_GetDefaultVerb_Proxy(IConsoleVerb *This,MMC_CONSOLE_VERB *peCmdID);
  void __RPC_STUB IConsoleVerb_GetDefaultVerb_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __ISnapinAbout_INTERFACE_DEFINED__
#define __ISnapinAbout_INTERFACE_DEFINED__
  EXTERN_C const IID IID_ISnapinAbout;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct ISnapinAbout : public IUnknown {
  public:
    virtual HRESULT WINAPI GetSnapinDescription(LPOLESTR *lpDescription) = 0;
    virtual HRESULT WINAPI GetProvider(LPOLESTR *lpName) = 0;
    virtual HRESULT WINAPI GetSnapinVersion(LPOLESTR *lpVersion) = 0;
    virtual HRESULT WINAPI GetSnapinImage(HICON *hAppIcon) = 0;
    virtual HRESULT WINAPI GetStaticFolderImage(HBITMAP *hSmallImage,HBITMAP *hSmallImageOpen,HBITMAP *hLargeImage,COLORREF *cMask) = 0;
  };
#else
  typedef struct ISnapinAboutVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(ISnapinAbout *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(ISnapinAbout *This);
      ULONG (WINAPI *Release)(ISnapinAbout *This);
      HRESULT (WINAPI *GetSnapinDescription)(ISnapinAbout *This,LPOLESTR *lpDescription);
      HRESULT (WINAPI *GetProvider)(ISnapinAbout *This,LPOLESTR *lpName);
      HRESULT (WINAPI *GetSnapinVersion)(ISnapinAbout *This,LPOLESTR *lpVersion);
      HRESULT (WINAPI *GetSnapinImage)(ISnapinAbout *This,HICON *hAppIcon);
      HRESULT (WINAPI *GetStaticFolderImage)(ISnapinAbout *This,HBITMAP *hSmallImage,HBITMAP *hSmallImageOpen,HBITMAP *hLargeImage,COLORREF *cMask);
    END_INTERFACE
  } ISnapinAboutVtbl;
  struct ISnapinAbout {
    CONST_VTBL struct ISnapinAboutVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define ISnapinAbout_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISnapinAbout_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISnapinAbout_Release(This) (This)->lpVtbl->Release(This)
#define ISnapinAbout_GetSnapinDescription(This,lpDescription) (This)->lpVtbl->GetSnapinDescription(This,lpDescription)
#define ISnapinAbout_GetProvider(This,lpName) (This)->lpVtbl->GetProvider(This,lpName)
#define ISnapinAbout_GetSnapinVersion(This,lpVersion) (This)->lpVtbl->GetSnapinVersion(This,lpVersion)
#define ISnapinAbout_GetSnapinImage(This,hAppIcon) (This)->lpVtbl->GetSnapinImage(This,hAppIcon)
#define ISnapinAbout_GetStaticFolderImage(This,hSmallImage,hSmallImageOpen,hLargeImage,cMask) (This)->lpVtbl->GetStaticFolderImage(This,hSmallImage,hSmallImageOpen,hLargeImage,cMask)
#endif
#endif
  HRESULT WINAPI ISnapinAbout_GetSnapinDescription_Proxy(ISnapinAbout *This,LPOLESTR *lpDescription);
  void __RPC_STUB ISnapinAbout_GetSnapinDescription_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI ISnapinAbout_GetProvider_Proxy(ISnapinAbout *This,LPOLESTR *lpName);
  void __RPC_STUB ISnapinAbout_GetProvider_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI ISnapinAbout_GetSnapinVersion_Proxy(ISnapinAbout *This,LPOLESTR *lpVersion);
  void __RPC_STUB ISnapinAbout_GetSnapinVersion_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI ISnapinAbout_GetSnapinImage_Proxy(ISnapinAbout *This,HICON *hAppIcon);
  void __RPC_STUB ISnapinAbout_GetSnapinImage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI ISnapinAbout_GetStaticFolderImage_Proxy(ISnapinAbout *This,HBITMAP *hSmallImage,HBITMAP *hSmallImageOpen,HBITMAP *hLargeImage,COLORREF *cMask);
  void __RPC_STUB ISnapinAbout_GetStaticFolderImage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMenuButton_INTERFACE_DEFINED__
#define __IMenuButton_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMenuButton;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMenuButton : public IUnknown {
  public:
    virtual HRESULT WINAPI AddButton(int idCommand,LPOLESTR lpButtonText,LPOLESTR lpTooltipText) = 0;
    virtual HRESULT WINAPI SetButton(int idCommand,LPOLESTR lpButtonText,LPOLESTR lpTooltipText) = 0;
    virtual HRESULT WINAPI SetButtonState(int idCommand,MMC_BUTTON_STATE nState,WINBOOL bState) = 0;
  };
#else
  typedef struct IMenuButtonVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMenuButton *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMenuButton *This);
      ULONG (WINAPI *Release)(IMenuButton *This);
      HRESULT (WINAPI *AddButton)(IMenuButton *This,int idCommand,LPOLESTR lpButtonText,LPOLESTR lpTooltipText);
      HRESULT (WINAPI *SetButton)(IMenuButton *This,int idCommand,LPOLESTR lpButtonText,LPOLESTR lpTooltipText);
      HRESULT (WINAPI *SetButtonState)(IMenuButton *This,int idCommand,MMC_BUTTON_STATE nState,WINBOOL bState);
    END_INTERFACE
  } IMenuButtonVtbl;
  struct IMenuButton {
    CONST_VTBL struct IMenuButtonVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMenuButton_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMenuButton_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMenuButton_Release(This) (This)->lpVtbl->Release(This)
#define IMenuButton_AddButton(This,idCommand,lpButtonText,lpTooltipText) (This)->lpVtbl->AddButton(This,idCommand,lpButtonText,lpTooltipText)
#define IMenuButton_SetButton(This,idCommand,lpButtonText,lpTooltipText) (This)->lpVtbl->SetButton(This,idCommand,lpButtonText,lpTooltipText)
#define IMenuButton_SetButtonState(This,idCommand,nState,bState) (This)->lpVtbl->SetButtonState(This,idCommand,nState,bState)
#endif
#endif
  HRESULT WINAPI IMenuButton_AddButton_Proxy(IMenuButton *This,int idCommand,LPOLESTR lpButtonText,LPOLESTR lpTooltipText);
  void __RPC_STUB IMenuButton_AddButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMenuButton_SetButton_Proxy(IMenuButton *This,int idCommand,LPOLESTR lpButtonText,LPOLESTR lpTooltipText);
  void __RPC_STUB IMenuButton_SetButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMenuButton_SetButtonState_Proxy(IMenuButton *This,int idCommand,MMC_BUTTON_STATE nState,WINBOOL bState);
  void __RPC_STUB IMenuButton_SetButtonState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __ISnapinHelp_INTERFACE_DEFINED__
#define __ISnapinHelp_INTERFACE_DEFINED__
  EXTERN_C const IID IID_ISnapinHelp;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct ISnapinHelp : public IUnknown {
  public:
    virtual HRESULT WINAPI GetHelpTopic(LPOLESTR *lpCompiledHelpFile) = 0;
  };
#else
  typedef struct ISnapinHelpVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(ISnapinHelp *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(ISnapinHelp *This);
      ULONG (WINAPI *Release)(ISnapinHelp *This);
      HRESULT (WINAPI *GetHelpTopic)(ISnapinHelp *This,LPOLESTR *lpCompiledHelpFile);
    END_INTERFACE
  } ISnapinHelpVtbl;
  struct ISnapinHelp {
    CONST_VTBL struct ISnapinHelpVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define ISnapinHelp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISnapinHelp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISnapinHelp_Release(This) (This)->lpVtbl->Release(This)
#define ISnapinHelp_GetHelpTopic(This,lpCompiledHelpFile) (This)->lpVtbl->GetHelpTopic(This,lpCompiledHelpFile)
#endif
#endif
  HRESULT WINAPI ISnapinHelp_GetHelpTopic_Proxy(ISnapinHelp *This,LPOLESTR *lpCompiledHelpFile);
  void __RPC_STUB ISnapinHelp_GetHelpTopic_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#if (MMC_VER >= 0x0110)
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0139_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0139_v0_0_s_ifspec;
#ifndef __IExtendPropertySheet2_INTERFACE_DEFINED__
#define __IExtendPropertySheet2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IExtendPropertySheet2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IExtendPropertySheet2 : public IExtendPropertySheet {
  public:
    virtual HRESULT WINAPI GetWatermarks(LPDATAOBJECT lpIDataObject,HBITMAP *lphWatermark,HBITMAP *lphHeader,HPALETTE *lphPalette,WINBOOL *bStretch) = 0;
  };
#else
  typedef struct IExtendPropertySheet2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IExtendPropertySheet2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IExtendPropertySheet2 *This);
      ULONG (WINAPI *Release)(IExtendPropertySheet2 *This);
      HRESULT (WINAPI *CreatePropertyPages)(IExtendPropertySheet2 *This,LPPROPERTYSHEETCALLBACK lpProvider,LONG_PTR handle,LPDATAOBJECT lpIDataObject);
      HRESULT (WINAPI *QueryPagesFor)(IExtendPropertySheet2 *This,LPDATAOBJECT lpDataObject);
      HRESULT (WINAPI *GetWatermarks)(IExtendPropertySheet2 *This,LPDATAOBJECT lpIDataObject,HBITMAP *lphWatermark,HBITMAP *lphHeader,HPALETTE *lphPalette,WINBOOL *bStretch);
    END_INTERFACE
  } IExtendPropertySheet2Vtbl;
  struct IExtendPropertySheet2 {
    CONST_VTBL struct IExtendPropertySheet2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IExtendPropertySheet2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExtendPropertySheet2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExtendPropertySheet2_Release(This) (This)->lpVtbl->Release(This)
#define IExtendPropertySheet2_CreatePropertyPages(This,lpProvider,handle,lpIDataObject) (This)->lpVtbl->CreatePropertyPages(This,lpProvider,handle,lpIDataObject)
#define IExtendPropertySheet2_QueryPagesFor(This,lpDataObject) (This)->lpVtbl->QueryPagesFor(This,lpDataObject)
#define IExtendPropertySheet2_GetWatermarks(This,lpIDataObject,lphWatermark,lphHeader,lphPalette,bStretch) (This)->lpVtbl->GetWatermarks(This,lpIDataObject,lphWatermark,lphHeader,lphPalette,bStretch)
#endif
#endif
  HRESULT WINAPI IExtendPropertySheet2_GetWatermarks_Proxy(IExtendPropertySheet2 *This,LPDATAOBJECT lpIDataObject,HBITMAP *lphWatermark,HBITMAP *lphHeader,HPALETTE *lphPalette,WINBOOL *bStretch);
  void __RPC_STUB IExtendPropertySheet2_GetWatermarks_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IHeaderCtrl2_INTERFACE_DEFINED__
#define __IHeaderCtrl2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IHeaderCtrl2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IHeaderCtrl2 : public IHeaderCtrl {
  public:
    virtual HRESULT WINAPI SetChangeTimeOut(unsigned __LONG32 uTimeout) = 0;
    virtual HRESULT WINAPI SetColumnFilter(UINT nColumn,DWORD dwType,MMC_FILTERDATA *pFilterData) = 0;
    virtual HRESULT WINAPI GetColumnFilter(UINT nColumn,LPDWORD pdwType,MMC_FILTERDATA *pFilterData) = 0;
  };
#else
  typedef struct IHeaderCtrl2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IHeaderCtrl2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IHeaderCtrl2 *This);
      ULONG (WINAPI *Release)(IHeaderCtrl2 *This);
      HRESULT (WINAPI *InsertColumn)(IHeaderCtrl2 *This,int nCol,LPCWSTR title,int nFormat,int nWidth);
      HRESULT (WINAPI *DeleteColumn)(IHeaderCtrl2 *This,int nCol);
      HRESULT (WINAPI *SetColumnText)(IHeaderCtrl2 *This,int nCol,LPCWSTR title);
      HRESULT (WINAPI *GetColumnText)(IHeaderCtrl2 *This,int nCol,LPOLESTR *pText);
      HRESULT (WINAPI *SetColumnWidth)(IHeaderCtrl2 *This,int nCol,int nWidth);
      HRESULT (WINAPI *GetColumnWidth)(IHeaderCtrl2 *This,int nCol,int *pWidth);
      HRESULT (WINAPI *SetChangeTimeOut)(IHeaderCtrl2 *This,unsigned __LONG32 uTimeout);
      HRESULT (WINAPI *SetColumnFilter)(IHeaderCtrl2 *This,UINT nColumn,DWORD dwType,MMC_FILTERDATA *pFilterData);
      HRESULT (WINAPI *GetColumnFilter)(IHeaderCtrl2 *This,UINT nColumn,LPDWORD pdwType,MMC_FILTERDATA *pFilterData);
    END_INTERFACE
  } IHeaderCtrl2Vtbl;
  struct IHeaderCtrl2 {
    CONST_VTBL struct IHeaderCtrl2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IHeaderCtrl2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHeaderCtrl2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHeaderCtrl2_Release(This) (This)->lpVtbl->Release(This)
#define IHeaderCtrl2_InsertColumn(This,nCol,title,nFormat,nWidth) (This)->lpVtbl->InsertColumn(This,nCol,title,nFormat,nWidth)
#define IHeaderCtrl2_DeleteColumn(This,nCol) (This)->lpVtbl->DeleteColumn(This,nCol)
#define IHeaderCtrl2_SetColumnText(This,nCol,title) (This)->lpVtbl->SetColumnText(This,nCol,title)
#define IHeaderCtrl2_GetColumnText(This,nCol,pText) (This)->lpVtbl->GetColumnText(This,nCol,pText)
#define IHeaderCtrl2_SetColumnWidth(This,nCol,nWidth) (This)->lpVtbl->SetColumnWidth(This,nCol,nWidth)
#define IHeaderCtrl2_GetColumnWidth(This,nCol,pWidth) (This)->lpVtbl->GetColumnWidth(This,nCol,pWidth)
#define IHeaderCtrl2_SetChangeTimeOut(This,uTimeout) (This)->lpVtbl->SetChangeTimeOut(This,uTimeout)
#define IHeaderCtrl2_SetColumnFilter(This,nColumn,dwType,pFilterData) (This)->lpVtbl->SetColumnFilter(This,nColumn,dwType,pFilterData)
#define IHeaderCtrl2_GetColumnFilter(This,nColumn,pdwType,pFilterData) (This)->lpVtbl->GetColumnFilter(This,nColumn,pdwType,pFilterData)
#endif
#endif
  HRESULT WINAPI IHeaderCtrl2_SetChangeTimeOut_Proxy(IHeaderCtrl2 *This,unsigned __LONG32 uTimeout);
  void __RPC_STUB IHeaderCtrl2_SetChangeTimeOut_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IHeaderCtrl2_SetColumnFilter_Proxy(IHeaderCtrl2 *This,UINT nColumn,DWORD dwType,MMC_FILTERDATA *pFilterData);
  void __RPC_STUB IHeaderCtrl2_SetColumnFilter_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IHeaderCtrl2_GetColumnFilter_Proxy(IHeaderCtrl2 *This,UINT nColumn,LPDWORD pdwType,MMC_FILTERDATA *pFilterData);
  void __RPC_STUB IHeaderCtrl2_GetColumnFilter_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __ISnapinHelp2_INTERFACE_DEFINED__
#define __ISnapinHelp2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_ISnapinHelp2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct ISnapinHelp2 : public ISnapinHelp {
  public:
    virtual HRESULT WINAPI GetLinkedTopics(LPOLESTR *lpCompiledHelpFiles) = 0;
  };
#else
  typedef struct ISnapinHelp2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(ISnapinHelp2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(ISnapinHelp2 *This);
      ULONG (WINAPI *Release)(ISnapinHelp2 *This);
      HRESULT (WINAPI *GetHelpTopic)(ISnapinHelp2 *This,LPOLESTR *lpCompiledHelpFile);
      HRESULT (WINAPI *GetLinkedTopics)(ISnapinHelp2 *This,LPOLESTR *lpCompiledHelpFiles);
    END_INTERFACE
  } ISnapinHelp2Vtbl;
  struct ISnapinHelp2 {
    CONST_VTBL struct ISnapinHelp2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define ISnapinHelp2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISnapinHelp2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISnapinHelp2_Release(This) (This)->lpVtbl->Release(This)
#define ISnapinHelp2_GetHelpTopic(This,lpCompiledHelpFile) (This)->lpVtbl->GetHelpTopic(This,lpCompiledHelpFile)
#define ISnapinHelp2_GetLinkedTopics(This,lpCompiledHelpFiles) (This)->lpVtbl->GetLinkedTopics(This,lpCompiledHelpFiles)
#endif
#endif
  HRESULT WINAPI ISnapinHelp2_GetLinkedTopics_Proxy(ISnapinHelp2 *This,LPOLESTR *lpCompiledHelpFiles);
  void __RPC_STUB ISnapinHelp2_GetLinkedTopics_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  typedef enum _MMC_TASK_DISPLAY_TYPE {
    MMC_TASK_DISPLAY_UNINITIALIZED = 0,MMC_TASK_DISPLAY_TYPE_SYMBOL,MMC_TASK_DISPLAY_TYPE_VANILLA_GIF,
    MMC_TASK_DISPLAY_TYPE_CHOCOLATE_GIF,MMC_TASK_DISPLAY_TYPE_BITMAP
  } MMC_TASK_DISPLAY_TYPE;

  typedef struct _MMC_TASK_DISPLAY_SYMBOL {
    LPOLESTR szFontFamilyName;
    LPOLESTR szURLtoEOT;
    LPOLESTR szSymbolString;
  } MMC_TASK_DISPLAY_SYMBOL;

  typedef struct _MMC_TASK_DISPLAY_BITMAP {
    LPOLESTR szMouseOverBitmap;
    LPOLESTR szMouseOffBitmap;
  } MMC_TASK_DISPLAY_BITMAP;

  typedef struct _MMC_TASK_DISPLAY_OBJECT {
    MMC_TASK_DISPLAY_TYPE eDisplayType;
    __C89_NAMELESS union {
      MMC_TASK_DISPLAY_BITMAP uBitmap;
      MMC_TASK_DISPLAY_SYMBOL uSymbol;
    };
  } MMC_TASK_DISPLAY_OBJECT;

  typedef enum _MMC_ACTION_TYPE {
    MMC_ACTION_UNINITIALIZED = -1,MMC_ACTION_ID = 0,MMC_ACTION_LINK,MMC_ACTION_SCRIPT
  } MMC_ACTION_TYPE;

  typedef struct _MMC_TASK {
    MMC_TASK_DISPLAY_OBJECT sDisplayObject;
    LPOLESTR szText;
    LPOLESTR szHelpString;
    MMC_ACTION_TYPE eActionType;
    __C89_NAMELESS union {
      LONG_PTR nCommandID;
      LPOLESTR szActionURL;
      LPOLESTR szScript;
    };
  } MMC_TASK;

  typedef struct _MMC_LISTPAD_INFO {
    LPOLESTR szTitle;
    LPOLESTR szButtonText;
    LONG_PTR nCommandID;
  } MMC_LISTPAD_INFO;

  typedef DWORD MMC_STRING_ID;

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0142_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0142_v0_0_s_ifspec;

#ifndef __IEnumTASK_INTERFACE_DEFINED__
#define __IEnumTASK_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IEnumTASK;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IEnumTASK : public IUnknown {
  public:
    virtual HRESULT WINAPI Next(ULONG celt,MMC_TASK *rgelt,ULONG *pceltFetched) = 0;
    virtual HRESULT WINAPI Skip(ULONG celt) = 0;
    virtual HRESULT WINAPI Reset(void) = 0;
    virtual HRESULT WINAPI Clone(IEnumTASK **ppenum) = 0;
  };
#else
  typedef struct IEnumTASKVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IEnumTASK *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IEnumTASK *This);
      ULONG (WINAPI *Release)(IEnumTASK *This);
      HRESULT (WINAPI *Next)(IEnumTASK *This,ULONG celt,MMC_TASK *rgelt,ULONG *pceltFetched);
      HRESULT (WINAPI *Skip)(IEnumTASK *This,ULONG celt);
      HRESULT (WINAPI *Reset)(IEnumTASK *This);
      HRESULT (WINAPI *Clone)(IEnumTASK *This,IEnumTASK **ppenum);
    END_INTERFACE
  } IEnumTASKVtbl;
  struct IEnumTASK {
    CONST_VTBL struct IEnumTASKVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IEnumTASK_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTASK_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTASK_Release(This) (This)->lpVtbl->Release(This)
#define IEnumTASK_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumTASK_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumTASK_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTASK_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#endif
#endif
  HRESULT WINAPI IEnumTASK_Next_Proxy(IEnumTASK *This,ULONG celt,MMC_TASK *rgelt,ULONG *pceltFetched);
  void __RPC_STUB IEnumTASK_Next_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IEnumTASK_Skip_Proxy(IEnumTASK *This,ULONG celt);
  void __RPC_STUB IEnumTASK_Skip_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IEnumTASK_Reset_Proxy(IEnumTASK *This);
  void __RPC_STUB IEnumTASK_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IEnumTASK_Clone_Proxy(IEnumTASK *This,IEnumTASK **ppenum);
  void __RPC_STUB IEnumTASK_Clone_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IExtendTaskPad_INTERFACE_DEFINED__
#define __IExtendTaskPad_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IExtendTaskPad;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IExtendTaskPad : public IUnknown {
  public:
    virtual HRESULT WINAPI TaskNotify(IDataObject *pdo,VARIANT *arg,VARIANT *param) = 0;
    virtual HRESULT WINAPI EnumTasks(IDataObject *pdo,LPOLESTR szTaskGroup,IEnumTASK **ppEnumTASK) = 0;
    virtual HRESULT WINAPI GetTitle(LPOLESTR pszGroup,LPOLESTR *pszTitle) = 0;
    virtual HRESULT WINAPI GetDescriptiveText(LPOLESTR pszGroup,LPOLESTR *pszDescriptiveText) = 0;
    virtual HRESULT WINAPI GetBackground(LPOLESTR pszGroup,MMC_TASK_DISPLAY_OBJECT *pTDO) = 0;
    virtual HRESULT WINAPI GetListPadInfo(LPOLESTR pszGroup,MMC_LISTPAD_INFO *lpListPadInfo) = 0;
  };
#else
  typedef struct IExtendTaskPadVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IExtendTaskPad *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IExtendTaskPad *This);
      ULONG (WINAPI *Release)(IExtendTaskPad *This);
      HRESULT (WINAPI *TaskNotify)(IExtendTaskPad *This,IDataObject *pdo,VARIANT *arg,VARIANT *param);
      HRESULT (WINAPI *EnumTasks)(IExtendTaskPad *This,IDataObject *pdo,LPOLESTR szTaskGroup,IEnumTASK **ppEnumTASK);
      HRESULT (WINAPI *GetTitle)(IExtendTaskPad *This,LPOLESTR pszGroup,LPOLESTR *pszTitle);
      HRESULT (WINAPI *GetDescriptiveText)(IExtendTaskPad *This,LPOLESTR pszGroup,LPOLESTR *pszDescriptiveText);
      HRESULT (WINAPI *GetBackground)(IExtendTaskPad *This,LPOLESTR pszGroup,MMC_TASK_DISPLAY_OBJECT *pTDO);
      HRESULT (WINAPI *GetListPadInfo)(IExtendTaskPad *This,LPOLESTR pszGroup,MMC_LISTPAD_INFO *lpListPadInfo);
    END_INTERFACE
  } IExtendTaskPadVtbl;
  struct IExtendTaskPad {
    CONST_VTBL struct IExtendTaskPadVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IExtendTaskPad_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExtendTaskPad_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExtendTaskPad_Release(This) (This)->lpVtbl->Release(This)
#define IExtendTaskPad_TaskNotify(This,pdo,arg,param) (This)->lpVtbl->TaskNotify(This,pdo,arg,param)
#define IExtendTaskPad_EnumTasks(This,pdo,szTaskGroup,ppEnumTASK) (This)->lpVtbl->EnumTasks(This,pdo,szTaskGroup,ppEnumTASK)
#define IExtendTaskPad_GetTitle(This,pszGroup,pszTitle) (This)->lpVtbl->GetTitle(This,pszGroup,pszTitle)
#define IExtendTaskPad_GetDescriptiveText(This,pszGroup,pszDescriptiveText) (This)->lpVtbl->GetDescriptiveText(This,pszGroup,pszDescriptiveText)
#define IExtendTaskPad_GetBackground(This,pszGroup,pTDO) (This)->lpVtbl->GetBackground(This,pszGroup,pTDO)
#define IExtendTaskPad_GetListPadInfo(This,pszGroup,lpListPadInfo) (This)->lpVtbl->GetListPadInfo(This,pszGroup,lpListPadInfo)
#endif
#endif
  HRESULT WINAPI IExtendTaskPad_TaskNotify_Proxy(IExtendTaskPad *This,IDataObject *pdo,VARIANT *arg,VARIANT *param);
  void __RPC_STUB IExtendTaskPad_TaskNotify_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IExtendTaskPad_EnumTasks_Proxy(IExtendTaskPad *This,IDataObject *pdo,LPOLESTR szTaskGroup,IEnumTASK **ppEnumTASK);
  void __RPC_STUB IExtendTaskPad_EnumTasks_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IExtendTaskPad_GetTitle_Proxy(IExtendTaskPad *This,LPOLESTR pszGroup,LPOLESTR *pszTitle);
  void __RPC_STUB IExtendTaskPad_GetTitle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IExtendTaskPad_GetDescriptiveText_Proxy(IExtendTaskPad *This,LPOLESTR pszGroup,LPOLESTR *pszDescriptiveText);
  void __RPC_STUB IExtendTaskPad_GetDescriptiveText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IExtendTaskPad_GetBackground_Proxy(IExtendTaskPad *This,LPOLESTR pszGroup,MMC_TASK_DISPLAY_OBJECT *pTDO);
  void __RPC_STUB IExtendTaskPad_GetBackground_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IExtendTaskPad_GetListPadInfo_Proxy(IExtendTaskPad *This,LPOLESTR pszGroup,MMC_LISTPAD_INFO *lpListPadInfo);
  void __RPC_STUB IExtendTaskPad_GetListPadInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IConsole2_INTERFACE_DEFINED__
#define __IConsole2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IConsole2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IConsole2 : public IConsole {
  public:
    virtual HRESULT WINAPI Expand(HSCOPEITEM hItem,WINBOOL bExpand) = 0;
    virtual HRESULT WINAPI IsTaskpadViewPreferred(void) = 0;
    virtual HRESULT WINAPI SetStatusText(LPOLESTR pszStatusText) = 0;
  };
#else
  typedef struct IConsole2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IConsole2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IConsole2 *This);
      ULONG (WINAPI *Release)(IConsole2 *This);
      HRESULT (WINAPI *SetHeader)(IConsole2 *This,LPHEADERCTRL pHeader);
      HRESULT (WINAPI *SetToolbar)(IConsole2 *This,LPTOOLBAR pToolbar);
      HRESULT (WINAPI *QueryResultView)(IConsole2 *This,LPUNKNOWN *pUnknown);
      HRESULT (WINAPI *QueryScopeImageList)(IConsole2 *This,LPIMAGELIST *ppImageList);
      HRESULT (WINAPI *QueryResultImageList)(IConsole2 *This,LPIMAGELIST *ppImageList);
      HRESULT (WINAPI *UpdateAllViews)(IConsole2 *This,LPDATAOBJECT lpDataObject,LPARAM data,LONG_PTR hint);
      HRESULT (WINAPI *MessageBox)(IConsole2 *This,LPCWSTR lpszText,LPCWSTR lpszTitle,UINT fuStyle,int *piRetval);
      HRESULT (WINAPI *QueryConsoleVerb)(IConsole2 *This,LPCONSOLEVERB *ppConsoleVerb);
      HRESULT (WINAPI *SelectScopeItem)(IConsole2 *This,HSCOPEITEM hScopeItem);
      HRESULT (WINAPI *GetMainWindow)(IConsole2 *This,HWND *phwnd);
      HRESULT (WINAPI *NewWindow)(IConsole2 *This,HSCOPEITEM hScopeItem,unsigned __LONG32 lOptions);
      HRESULT (WINAPI *Expand)(IConsole2 *This,HSCOPEITEM hItem,WINBOOL bExpand);
      HRESULT (WINAPI *IsTaskpadViewPreferred)(IConsole2 *This);
      HRESULT (WINAPI *SetStatusText)(IConsole2 *This,LPOLESTR pszStatusText);
    END_INTERFACE
  } IConsole2Vtbl;
  struct IConsole2 {
    CONST_VTBL struct IConsole2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IConsole2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConsole2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConsole2_Release(This) (This)->lpVtbl->Release(This)
#define IConsole2_SetHeader(This,pHeader) (This)->lpVtbl->SetHeader(This,pHeader)
#define IConsole2_SetToolbar(This,pToolbar) (This)->lpVtbl->SetToolbar(This,pToolbar)
#define IConsole2_QueryResultView(This,pUnknown) (This)->lpVtbl->QueryResultView(This,pUnknown)
#define IConsole2_QueryScopeImageList(This,ppImageList) (This)->lpVtbl->QueryScopeImageList(This,ppImageList)
#define IConsole2_QueryResultImageList(This,ppImageList) (This)->lpVtbl->QueryResultImageList(This,ppImageList)
#define IConsole2_UpdateAllViews(This,lpDataObject,data,hint) (This)->lpVtbl->UpdateAllViews(This,lpDataObject,data,hint)
#define IConsole2_MessageBox(This,lpszText,lpszTitle,fuStyle,piRetval) (This)->lpVtbl->MessageBox(This,lpszText,lpszTitle,fuStyle,piRetval)
#define IConsole2_QueryConsoleVerb(This,ppConsoleVerb) (This)->lpVtbl->QueryConsoleVerb(This,ppConsoleVerb)
#define IConsole2_SelectScopeItem(This,hScopeItem) (This)->lpVtbl->SelectScopeItem(This,hScopeItem)
#define IConsole2_GetMainWindow(This,phwnd) (This)->lpVtbl->GetMainWindow(This,phwnd)
#define IConsole2_NewWindow(This,hScopeItem,lOptions) (This)->lpVtbl->NewWindow(This,hScopeItem,lOptions)
#define IConsole2_Expand(This,hItem,bExpand) (This)->lpVtbl->Expand(This,hItem,bExpand)
#define IConsole2_IsTaskpadViewPreferred(This) (This)->lpVtbl->IsTaskpadViewPreferred(This)
#define IConsole2_SetStatusText(This,pszStatusText) (This)->lpVtbl->SetStatusText(This,pszStatusText)
#endif
#endif
  HRESULT WINAPI IConsole2_Expand_Proxy(IConsole2 *This,HSCOPEITEM hItem,WINBOOL bExpand);
  void __RPC_STUB IConsole2_Expand_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole2_IsTaskpadViewPreferred_Proxy(IConsole2 *This);
  void __RPC_STUB IConsole2_IsTaskpadViewPreferred_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsole2_SetStatusText_Proxy(IConsole2 *This,LPOLESTR pszStatusText);
  void __RPC_STUB IConsole2_SetStatusText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IDisplayHelp_INTERFACE_DEFINED__
#define __IDisplayHelp_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IDisplayHelp;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IDisplayHelp : public IUnknown {
  public:
    virtual HRESULT WINAPI ShowTopic(LPOLESTR pszHelpTopic) = 0;
  };
#else
  typedef struct IDisplayHelpVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IDisplayHelp *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IDisplayHelp *This);
      ULONG (WINAPI *Release)(IDisplayHelp *This);
      HRESULT (WINAPI *ShowTopic)(IDisplayHelp *This,LPOLESTR pszHelpTopic);
    END_INTERFACE
  } IDisplayHelpVtbl;
  struct IDisplayHelp {
    CONST_VTBL struct IDisplayHelpVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IDisplayHelp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDisplayHelp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDisplayHelp_Release(This) (This)->lpVtbl->Release(This)
#define IDisplayHelp_ShowTopic(This,pszHelpTopic) (This)->lpVtbl->ShowTopic(This,pszHelpTopic)
#endif
#endif
  HRESULT WINAPI IDisplayHelp_ShowTopic_Proxy(IDisplayHelp *This,LPOLESTR pszHelpTopic);
  void __RPC_STUB IDisplayHelp_ShowTopic_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IRequiredExtensions_INTERFACE_DEFINED__
#define __IRequiredExtensions_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IRequiredExtensions;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IRequiredExtensions : public IUnknown {
  public:
    virtual HRESULT WINAPI EnableAllExtensions(void) = 0;
    virtual HRESULT WINAPI GetFirstExtension(LPCLSID pExtCLSID) = 0;
    virtual HRESULT WINAPI GetNextExtension(LPCLSID pExtCLSID) = 0;
  };
#else
  typedef struct IRequiredExtensionsVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IRequiredExtensions *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IRequiredExtensions *This);
      ULONG (WINAPI *Release)(IRequiredExtensions *This);
      HRESULT (WINAPI *EnableAllExtensions)(IRequiredExtensions *This);
      HRESULT (WINAPI *GetFirstExtension)(IRequiredExtensions *This,LPCLSID pExtCLSID);
      HRESULT (WINAPI *GetNextExtension)(IRequiredExtensions *This,LPCLSID pExtCLSID);
    END_INTERFACE
  } IRequiredExtensionsVtbl;
  struct IRequiredExtensions {
    CONST_VTBL struct IRequiredExtensionsVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IRequiredExtensions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRequiredExtensions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRequiredExtensions_Release(This) (This)->lpVtbl->Release(This)
#define IRequiredExtensions_EnableAllExtensions(This) (This)->lpVtbl->EnableAllExtensions(This)
#define IRequiredExtensions_GetFirstExtension(This,pExtCLSID) (This)->lpVtbl->GetFirstExtension(This,pExtCLSID)
#define IRequiredExtensions_GetNextExtension(This,pExtCLSID) (This)->lpVtbl->GetNextExtension(This,pExtCLSID)
#endif
#endif
  HRESULT WINAPI IRequiredExtensions_EnableAllExtensions_Proxy(IRequiredExtensions *This);
  void __RPC_STUB IRequiredExtensions_EnableAllExtensions_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IRequiredExtensions_GetFirstExtension_Proxy(IRequiredExtensions *This,LPCLSID pExtCLSID);
  void __RPC_STUB IRequiredExtensions_GetFirstExtension_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IRequiredExtensions_GetNextExtension_Proxy(IRequiredExtensions *This,LPCLSID pExtCLSID);
  void __RPC_STUB IRequiredExtensions_GetNextExtension_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IStringTable_INTERFACE_DEFINED__
#define __IStringTable_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IStringTable;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IStringTable : public IUnknown {
  public:
    virtual HRESULT WINAPI AddString(LPCOLESTR pszAdd,MMC_STRING_ID *pStringID) = 0;
    virtual HRESULT WINAPI GetString(MMC_STRING_ID StringID,ULONG cchBuffer,LPOLESTR lpBuffer,ULONG *pcchOut) = 0;
    virtual HRESULT WINAPI GetStringLength(MMC_STRING_ID StringID,ULONG *pcchString) = 0;
    virtual HRESULT WINAPI DeleteString(MMC_STRING_ID StringID) = 0;
    virtual HRESULT WINAPI DeleteAllStrings(void) = 0;
    virtual HRESULT WINAPI FindString(LPCOLESTR pszFind,MMC_STRING_ID *pStringID) = 0;
    virtual HRESULT WINAPI Enumerate(IEnumString **ppEnum) = 0;
  };
#else
  typedef struct IStringTableVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IStringTable *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IStringTable *This);
      ULONG (WINAPI *Release)(IStringTable *This);
      HRESULT (WINAPI *AddString)(IStringTable *This,LPCOLESTR pszAdd,MMC_STRING_ID *pStringID);
      HRESULT (WINAPI *GetString)(IStringTable *This,MMC_STRING_ID StringID,ULONG cchBuffer,LPOLESTR lpBuffer,ULONG *pcchOut);
      HRESULT (WINAPI *GetStringLength)(IStringTable *This,MMC_STRING_ID StringID,ULONG *pcchString);
      HRESULT (WINAPI *DeleteString)(IStringTable *This,MMC_STRING_ID StringID);
      HRESULT (WINAPI *DeleteAllStrings)(IStringTable *This);
      HRESULT (WINAPI *FindString)(IStringTable *This,LPCOLESTR pszFind,MMC_STRING_ID *pStringID);
      HRESULT (WINAPI *Enumerate)(IStringTable *This,IEnumString **ppEnum);
    END_INTERFACE
  } IStringTableVtbl;
  struct IStringTable {
    CONST_VTBL struct IStringTableVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IStringTable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IStringTable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IStringTable_Release(This) (This)->lpVtbl->Release(This)
#define IStringTable_AddString(This,pszAdd,pStringID) (This)->lpVtbl->AddString(This,pszAdd,pStringID)
#define IStringTable_GetString(This,StringID,cchBuffer,lpBuffer,pcchOut) (This)->lpVtbl->GetString(This,StringID,cchBuffer,lpBuffer,pcchOut)
#define IStringTable_GetStringLength(This,StringID,pcchString) (This)->lpVtbl->GetStringLength(This,StringID,pcchString)
#define IStringTable_DeleteString(This,StringID) (This)->lpVtbl->DeleteString(This,StringID)
#define IStringTable_DeleteAllStrings(This) (This)->lpVtbl->DeleteAllStrings(This)
#define IStringTable_FindString(This,pszFind,pStringID) (This)->lpVtbl->FindString(This,pszFind,pStringID)
#define IStringTable_Enumerate(This,ppEnum) (This)->lpVtbl->Enumerate(This,ppEnum)
#endif
#endif
  HRESULT WINAPI IStringTable_AddString_Proxy(IStringTable *This,LPCOLESTR pszAdd,MMC_STRING_ID *pStringID);
  void __RPC_STUB IStringTable_AddString_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IStringTable_GetString_Proxy(IStringTable *This,MMC_STRING_ID StringID,ULONG cchBuffer,LPOLESTR lpBuffer,ULONG *pcchOut);
  void __RPC_STUB IStringTable_GetString_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IStringTable_GetStringLength_Proxy(IStringTable *This,MMC_STRING_ID StringID,ULONG *pcchString);
  void __RPC_STUB IStringTable_GetStringLength_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IStringTable_DeleteString_Proxy(IStringTable *This,MMC_STRING_ID StringID);
  void __RPC_STUB IStringTable_DeleteString_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IStringTable_DeleteAllStrings_Proxy(IStringTable *This);
  void __RPC_STUB IStringTable_DeleteAllStrings_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IStringTable_FindString_Proxy(IStringTable *This,LPCOLESTR pszFind,MMC_STRING_ID *pStringID);
  void __RPC_STUB IStringTable_FindString_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IStringTable_Enumerate_Proxy(IStringTable *This,IEnumString **ppEnum);
  void __RPC_STUB IStringTable_Enumerate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif
#endif

#if (MMC_VER >= 0x0120)
#define HDI_HIDDEN (0x1)
  typedef struct _MMC_COLUMN_DATA {
    int nColIndex;
    DWORD dwFlags;
    int nWidth;
    ULONG_PTR ulReserved;
  } MMC_COLUMN_DATA;

  typedef struct _MMC_COLUMN_SET_DATA {
    int cbSize;
    int nNumCols;
    MMC_COLUMN_DATA *pColData;
  } MMC_COLUMN_SET_DATA;

  typedef struct _MMC_SORT_DATA {
    int nColIndex;
    DWORD dwSortOptions;
    ULONG_PTR ulReserved;
  } MMC_SORT_DATA;

  typedef struct _MMC_SORT_SET_DATA {
    int cbSize;
    int nNumItems;
    MMC_SORT_DATA *pSortData;
  } MMC_SORT_SET_DATA;

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0148_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0148_v0_0_s_ifspec;

#ifndef __IColumnData_INTERFACE_DEFINED__
#define __IColumnData_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IColumnData;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IColumnData : public IUnknown {
  public:
    virtual HRESULT WINAPI SetColumnConfigData(SColumnSetID *pColID,MMC_COLUMN_SET_DATA *pColSetData) = 0;
    virtual HRESULT WINAPI GetColumnConfigData(SColumnSetID *pColID,MMC_COLUMN_SET_DATA **ppColSetData) = 0;
    virtual HRESULT WINAPI SetColumnSortData(SColumnSetID *pColID,MMC_SORT_SET_DATA *pColSortData) = 0;
    virtual HRESULT WINAPI GetColumnSortData(SColumnSetID *pColID,MMC_SORT_SET_DATA **ppColSortData) = 0;
  };
#else
  typedef struct IColumnDataVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IColumnData *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IColumnData *This);
      ULONG (WINAPI *Release)(IColumnData *This);
      HRESULT (WINAPI *SetColumnConfigData)(IColumnData *This,SColumnSetID *pColID,MMC_COLUMN_SET_DATA *pColSetData);
      HRESULT (WINAPI *GetColumnConfigData)(IColumnData *This,SColumnSetID *pColID,MMC_COLUMN_SET_DATA **ppColSetData);
      HRESULT (WINAPI *SetColumnSortData)(IColumnData *This,SColumnSetID *pColID,MMC_SORT_SET_DATA *pColSortData);
      HRESULT (WINAPI *GetColumnSortData)(IColumnData *This,SColumnSetID *pColID,MMC_SORT_SET_DATA **ppColSortData);
    END_INTERFACE
  } IColumnDataVtbl;
  struct IColumnData {
    CONST_VTBL struct IColumnDataVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IColumnData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IColumnData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IColumnData_Release(This) (This)->lpVtbl->Release(This)
#define IColumnData_SetColumnConfigData(This,pColID,pColSetData) (This)->lpVtbl->SetColumnConfigData(This,pColID,pColSetData)
#define IColumnData_GetColumnConfigData(This,pColID,ppColSetData) (This)->lpVtbl->GetColumnConfigData(This,pColID,ppColSetData)
#define IColumnData_SetColumnSortData(This,pColID,pColSortData) (This)->lpVtbl->SetColumnSortData(This,pColID,pColSortData)
#define IColumnData_GetColumnSortData(This,pColID,ppColSortData) (This)->lpVtbl->GetColumnSortData(This,pColID,ppColSortData)
#endif
#endif
  HRESULT WINAPI IColumnData_SetColumnConfigData_Proxy(IColumnData *This,SColumnSetID *pColID,MMC_COLUMN_SET_DATA *pColSetData);
  void __RPC_STUB IColumnData_SetColumnConfigData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IColumnData_GetColumnConfigData_Proxy(IColumnData *This,SColumnSetID *pColID,MMC_COLUMN_SET_DATA **ppColSetData);
  void __RPC_STUB IColumnData_GetColumnConfigData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IColumnData_SetColumnSortData_Proxy(IColumnData *This,SColumnSetID *pColID,MMC_SORT_SET_DATA *pColSortData);
  void __RPC_STUB IColumnData_SetColumnSortData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IColumnData_GetColumnSortData_Proxy(IColumnData *This,SColumnSetID *pColID,MMC_SORT_SET_DATA **ppColSortData);
  void __RPC_STUB IColumnData_GetColumnSortData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMessageView_INTERFACE_DEFINED__
#define __IMessageView_INTERFACE_DEFINED__
  typedef enum tagIconIdentifier {
    Icon_None = 0,Icon_Error = 32513,Icon_Question = 32514,Icon_Warning = 32515,Icon_Information = 32516,Icon_First = Icon_Error,
    Icon_Last = Icon_Information
  } IconIdentifier;

  EXTERN_C const IID IID_IMessageView;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMessageView : public IUnknown {
  public:
    virtual HRESULT WINAPI SetTitleText(LPCOLESTR pszTitleText) = 0;
    virtual HRESULT WINAPI SetBodyText(LPCOLESTR pszBodyText) = 0;
    virtual HRESULT WINAPI SetIcon(IconIdentifier id) = 0;
    virtual HRESULT WINAPI Clear(void) = 0;
  };
#else
  typedef struct IMessageViewVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMessageView *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMessageView *This);
      ULONG (WINAPI *Release)(IMessageView *This);
      HRESULT (WINAPI *SetTitleText)(IMessageView *This,LPCOLESTR pszTitleText);
      HRESULT (WINAPI *SetBodyText)(IMessageView *This,LPCOLESTR pszBodyText);
      HRESULT (WINAPI *SetIcon)(IMessageView *This,IconIdentifier id);
      HRESULT (WINAPI *Clear)(IMessageView *This);
    END_INTERFACE
  } IMessageViewVtbl;
  struct IMessageView {
    CONST_VTBL struct IMessageViewVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMessageView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMessageView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMessageView_Release(This) (This)->lpVtbl->Release(This)
#define IMessageView_SetTitleText(This,pszTitleText) (This)->lpVtbl->SetTitleText(This,pszTitleText)
#define IMessageView_SetBodyText(This,pszBodyText) (This)->lpVtbl->SetBodyText(This,pszBodyText)
#define IMessageView_SetIcon(This,id) (This)->lpVtbl->SetIcon(This,id)
#define IMessageView_Clear(This) (This)->lpVtbl->Clear(This)
#endif
#endif
  HRESULT WINAPI IMessageView_SetTitleText_Proxy(IMessageView *This,LPCOLESTR pszTitleText);
  void __RPC_STUB IMessageView_SetTitleText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMessageView_SetBodyText_Proxy(IMessageView *This,LPCOLESTR pszBodyText);
  void __RPC_STUB IMessageView_SetBodyText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMessageView_SetIcon_Proxy(IMessageView *This,IconIdentifier id);
  void __RPC_STUB IMessageView_SetIcon_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IMessageView_Clear_Proxy(IMessageView *This);
  void __RPC_STUB IMessageView_Clear_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  typedef struct _RDCITEMHDR {
    DWORD dwFlags;
    MMC_COOKIE cookie;
    LPARAM lpReserved;
  } RDITEMHDR;

#define RDCI_ScopeItem (0x80000000)

  typedef struct _RDCOMPARE {
    DWORD cbSize;
    DWORD dwFlags;
    int nColumn;
    LPARAM lUserParam;
    RDITEMHDR *prdch1;
    RDITEMHDR *prdch2;
  } RDCOMPARE;

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0150_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0150_v0_0_s_ifspec;

#ifndef __IResultDataCompareEx_INTERFACE_DEFINED__
#define __IResultDataCompareEx_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IResultDataCompareEx;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IResultDataCompareEx : public IUnknown {
  public:
    virtual HRESULT WINAPI Compare(RDCOMPARE *prdc,int *pnResult) = 0;
  };
#else
  typedef struct IResultDataCompareExVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IResultDataCompareEx *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IResultDataCompareEx *This);
      ULONG (WINAPI *Release)(IResultDataCompareEx *This);
      HRESULT (WINAPI *Compare)(IResultDataCompareEx *This,RDCOMPARE *prdc,int *pnResult);
    END_INTERFACE
  } IResultDataCompareExVtbl;
  struct IResultDataCompareEx {
    CONST_VTBL struct IResultDataCompareExVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IResultDataCompareEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IResultDataCompareEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IResultDataCompareEx_Release(This) (This)->lpVtbl->Release(This)
#define IResultDataCompareEx_Compare(This,prdc,pnResult) (This)->lpVtbl->Compare(This,prdc,pnResult)
#endif
#endif
  HRESULT WINAPI IResultDataCompareEx_Compare_Proxy(IResultDataCompareEx *This,RDCOMPARE *prdc,int *pnResult);
  void __RPC_STUB IResultDataCompareEx_Compare_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif
#endif

#if (MMC_VER >= 0x0200)
  typedef enum _MMC_VIEW_TYPE {
    MMC_VIEW_TYPE_LIST = 0,MMC_VIEW_TYPE_HTML,MMC_VIEW_TYPE_OCX
  } MMC_VIEW_TYPE;

#define RVTI_MISC_OPTIONS_NOLISTVIEWS (0x1)
#define RVTI_LIST_OPTIONS_NONE (0)
#define RVTI_LIST_OPTIONS_OWNERDATALIST (0x2)
#define RVTI_LIST_OPTIONS_MULTISELECT (0x4)
#define RVTI_LIST_OPTIONS_FILTERED (0x8)
#define RVTI_LIST_OPTIONS_USEFONTLINKING (0x20)
#define RVTI_LIST_OPTIONS_EXCLUDE_SCOPE_ITEMS_FROM_LIST (0x40)
#define RVTI_LIST_OPTIONS_LEXICAL_SORT (0x80)
#define RVTI_LIST_OPTIONS_ALLOWPASTE (0x100)
#define RVTI_HTML_OPTIONS_NONE (0)
#define RVTI_HTML_OPTIONS_NOLISTVIEW (0x1)
#define RVTI_OCX_OPTIONS_NONE (0)
#define RVTI_OCX_OPTIONS_NOLISTVIEW (0x1)
#define RVTI_OCX_OPTIONS_CACHE_OCX (0x2)

  typedef struct _RESULT_VIEW_TYPE_INFO {
    LPOLESTR pstrPersistableViewDescription;
    MMC_VIEW_TYPE eViewType;
    DWORD dwMiscOptions;
    __C89_NAMELESS union {
      DWORD dwListOptions;
      __C89_NAMELESS struct {
	DWORD dwHTMLOptions;
	LPOLESTR pstrURL;
      };
      __C89_NAMELESS struct {
	DWORD dwOCXOptions;
	LPUNKNOWN pUnkControl;
      };
    };
  } RESULT_VIEW_TYPE_INFO;

  typedef struct _RESULT_VIEW_TYPE_INFO *PRESULT_VIEW_TYPE_INFO;

#define CCF_DESCRIPTION (L"CCF_DESCRIPTION")
#define CCF_HTML_DETAILS (L"CCF_HTML_DETAILS")

  typedef struct _CONTEXTMENUITEM2 {
    LPWSTR strName;
    LPWSTR strStatusBarText;
    LONG lCommandID;
    LONG lInsertionPointID;
    LONG fFlags;
    LONG fSpecialFlags;
    LPWSTR strLanguageIndependentName;
  } CONTEXTMENUITEM2;

  typedef CONTEXTMENUITEM2 *LPCONTEXTMENUITEM2;

  typedef struct _MMC_EXT_VIEW_DATA {
    GUID viewID;
    LPCOLESTR pszURL;
    LPCOLESTR pszViewTitle;
    LPCOLESTR pszTooltipText;
    WINBOOL bReplacesDefaultView;
  } MMC_EXT_VIEW_DATA;

  typedef struct _MMC_EXT_VIEW_DATA *PMMC_EXT_VIEW_DATA;

#define MMC_DEFAULT_OPERATION_COPY (0x1)

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0151_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0151_v0_0_s_ifspec;

#ifndef __IComponentData2_INTERFACE_DEFINED__
#define __IComponentData2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IComponentData2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IComponentData2 : public IComponentData {
  public:
    virtual HRESULT WINAPI QueryDispatch(MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDISPATCH *ppDispatch) = 0;
  };
#else
  typedef struct IComponentData2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IComponentData2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IComponentData2 *This);
      ULONG (WINAPI *Release)(IComponentData2 *This);
      HRESULT (WINAPI *Initialize)(IComponentData2 *This,LPUNKNOWN pUnknown);
      HRESULT (WINAPI *CreateComponent)(IComponentData2 *This,LPCOMPONENT *ppComponent);
      HRESULT (WINAPI *Notify)(IComponentData2 *This,LPDATAOBJECT lpDataObject,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param);
      HRESULT (WINAPI *Destroy)(IComponentData2 *This);
      HRESULT (WINAPI *QueryDataObject)(IComponentData2 *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDATAOBJECT *ppDataObject);
      HRESULT (WINAPI *GetDisplayInfo)(IComponentData2 *This,SCOPEDATAITEM *pScopeDataItem);
      HRESULT (WINAPI *CompareObjects)(IComponentData2 *This,LPDATAOBJECT lpDataObjectA,LPDATAOBJECT lpDataObjectB);
      HRESULT (WINAPI *QueryDispatch)(IComponentData2 *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDISPATCH *ppDispatch);
    END_INTERFACE
  } IComponentData2Vtbl;
  struct IComponentData2 {
    CONST_VTBL struct IComponentData2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IComponentData2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IComponentData2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IComponentData2_Release(This) (This)->lpVtbl->Release(This)
#define IComponentData2_Initialize(This,pUnknown) (This)->lpVtbl->Initialize(This,pUnknown)
#define IComponentData2_CreateComponent(This,ppComponent) (This)->lpVtbl->CreateComponent(This,ppComponent)
#define IComponentData2_Notify(This,lpDataObject,event,arg,param) (This)->lpVtbl->Notify(This,lpDataObject,event,arg,param)
#define IComponentData2_Destroy(This) (This)->lpVtbl->Destroy(This)
#define IComponentData2_QueryDataObject(This,cookie,type,ppDataObject) (This)->lpVtbl->QueryDataObject(This,cookie,type,ppDataObject)
#define IComponentData2_GetDisplayInfo(This,pScopeDataItem) (This)->lpVtbl->GetDisplayInfo(This,pScopeDataItem)
#define IComponentData2_CompareObjects(This,lpDataObjectA,lpDataObjectB) (This)->lpVtbl->CompareObjects(This,lpDataObjectA,lpDataObjectB)
#define IComponentData2_QueryDispatch(This,cookie,type,ppDispatch) (This)->lpVtbl->QueryDispatch(This,cookie,type,ppDispatch)
#endif
#endif
  HRESULT WINAPI IComponentData2_QueryDispatch_Proxy(IComponentData2 *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDISPATCH *ppDispatch);
  void __RPC_STUB IComponentData2_QueryDispatch_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IComponent2_INTERFACE_DEFINED__
#define __IComponent2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IComponent2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IComponent2 : public IComponent {
  public:
    virtual HRESULT WINAPI QueryDispatch(MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDISPATCH *ppDispatch) = 0;
    virtual HRESULT WINAPI GetResultViewType2(MMC_COOKIE cookie,PRESULT_VIEW_TYPE_INFO pResultViewType) = 0;
    virtual HRESULT WINAPI RestoreResultView(MMC_COOKIE cookie,PRESULT_VIEW_TYPE_INFO pResultViewType) = 0;
  };
#else
  typedef struct IComponent2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IComponent2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IComponent2 *This);
      ULONG (WINAPI *Release)(IComponent2 *This);
      HRESULT (WINAPI *Initialize)(IComponent2 *This,LPCONSOLE lpConsole);
      HRESULT (WINAPI *Notify)(IComponent2 *This,LPDATAOBJECT lpDataObject,MMC_NOTIFY_TYPE event,LPARAM arg,LPARAM param);
      HRESULT (WINAPI *Destroy)(IComponent2 *This,MMC_COOKIE cookie);
      HRESULT (WINAPI *QueryDataObject)(IComponent2 *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDATAOBJECT *ppDataObject);
      HRESULT (WINAPI *GetResultViewType)(IComponent2 *This,MMC_COOKIE cookie,LPOLESTR *ppViewType,__LONG32 *pViewOptions);
      HRESULT (WINAPI *GetDisplayInfo)(IComponent2 *This,RESULTDATAITEM *pResultDataItem);
      HRESULT (WINAPI *CompareObjects)(IComponent2 *This,LPDATAOBJECT lpDataObjectA,LPDATAOBJECT lpDataObjectB);
      HRESULT (WINAPI *QueryDispatch)(IComponent2 *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDISPATCH *ppDispatch);
      HRESULT (WINAPI *GetResultViewType2)(IComponent2 *This,MMC_COOKIE cookie,PRESULT_VIEW_TYPE_INFO pResultViewType);
      HRESULT (WINAPI *RestoreResultView)(IComponent2 *This,MMC_COOKIE cookie,PRESULT_VIEW_TYPE_INFO pResultViewType);
    END_INTERFACE
  } IComponent2Vtbl;
  struct IComponent2 {
    CONST_VTBL struct IComponent2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IComponent2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IComponent2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IComponent2_Release(This) (This)->lpVtbl->Release(This)
#define IComponent2_Initialize(This,lpConsole) (This)->lpVtbl->Initialize(This,lpConsole)
#define IComponent2_Notify(This,lpDataObject,event,arg,param) (This)->lpVtbl->Notify(This,lpDataObject,event,arg,param)
#define IComponent2_Destroy(This,cookie) (This)->lpVtbl->Destroy(This,cookie)
#define IComponent2_QueryDataObject(This,cookie,type,ppDataObject) (This)->lpVtbl->QueryDataObject(This,cookie,type,ppDataObject)
#define IComponent2_GetResultViewType(This,cookie,ppViewType,pViewOptions) (This)->lpVtbl->GetResultViewType(This,cookie,ppViewType,pViewOptions)
#define IComponent2_GetDisplayInfo(This,pResultDataItem) (This)->lpVtbl->GetDisplayInfo(This,pResultDataItem)
#define IComponent2_CompareObjects(This,lpDataObjectA,lpDataObjectB) (This)->lpVtbl->CompareObjects(This,lpDataObjectA,lpDataObjectB)
#define IComponent2_QueryDispatch(This,cookie,type,ppDispatch) (This)->lpVtbl->QueryDispatch(This,cookie,type,ppDispatch)
#define IComponent2_GetResultViewType2(This,cookie,pResultViewType) (This)->lpVtbl->GetResultViewType2(This,cookie,pResultViewType)
#define IComponent2_RestoreResultView(This,cookie,pResultViewType) (This)->lpVtbl->RestoreResultView(This,cookie,pResultViewType)
#endif
#endif
  HRESULT WINAPI IComponent2_QueryDispatch_Proxy(IComponent2 *This,MMC_COOKIE cookie,DATA_OBJECT_TYPES type,LPDISPATCH *ppDispatch);
  void __RPC_STUB IComponent2_QueryDispatch_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponent2_GetResultViewType2_Proxy(IComponent2 *This,MMC_COOKIE cookie,PRESULT_VIEW_TYPE_INFO pResultViewType);
  void __RPC_STUB IComponent2_GetResultViewType2_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IComponent2_RestoreResultView_Proxy(IComponent2 *This,MMC_COOKIE cookie,PRESULT_VIEW_TYPE_INFO pResultViewType);
  void __RPC_STUB IComponent2_RestoreResultView_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IContextMenuCallback2_INTERFACE_DEFINED__
#define __IContextMenuCallback2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IContextMenuCallback2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IContextMenuCallback2 : public IUnknown {
  public:
    virtual HRESULT WINAPI AddItem(CONTEXTMENUITEM2 *pItem) = 0;
  };
#else
  typedef struct IContextMenuCallback2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IContextMenuCallback2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IContextMenuCallback2 *This);
      ULONG (WINAPI *Release)(IContextMenuCallback2 *This);
      HRESULT (WINAPI *AddItem)(IContextMenuCallback2 *This,CONTEXTMENUITEM2 *pItem);
    END_INTERFACE
  } IContextMenuCallback2Vtbl;
  struct IContextMenuCallback2 {
    CONST_VTBL struct IContextMenuCallback2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IContextMenuCallback2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IContextMenuCallback2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IContextMenuCallback2_Release(This) (This)->lpVtbl->Release(This)
#define IContextMenuCallback2_AddItem(This,pItem) (This)->lpVtbl->AddItem(This,pItem)
#endif
#endif
  HRESULT WINAPI IContextMenuCallback2_AddItem_Proxy(IContextMenuCallback2 *This,CONTEXTMENUITEM2 *pItem);
  void __RPC_STUB IContextMenuCallback2_AddItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IMMCVersionInfo_INTERFACE_DEFINED__
#define __IMMCVersionInfo_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IMMCVersionInfo;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IMMCVersionInfo : public IUnknown {
  public:
    virtual HRESULT WINAPI GetMMCVersion(__LONG32 *pVersionMajor,__LONG32 *pVersionMinor) = 0;
  };
#else
  typedef struct IMMCVersionInfoVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IMMCVersionInfo *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IMMCVersionInfo *This);
      ULONG (WINAPI *Release)(IMMCVersionInfo *This);
      HRESULT (WINAPI *GetMMCVersion)(IMMCVersionInfo *This,__LONG32 *pVersionMajor,__LONG32 *pVersionMinor);
    END_INTERFACE
  } IMMCVersionInfoVtbl;
  struct IMMCVersionInfo {
    CONST_VTBL struct IMMCVersionInfoVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IMMCVersionInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMMCVersionInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMMCVersionInfo_Release(This) (This)->lpVtbl->Release(This)
#define IMMCVersionInfo_GetMMCVersion(This,pVersionMajor,pVersionMinor) (This)->lpVtbl->GetMMCVersion(This,pVersionMajor,pVersionMinor)
#endif
#endif
  HRESULT WINAPI IMMCVersionInfo_GetMMCVersion_Proxy(IMMCVersionInfo *This,__LONG32 *pVersionMajor,__LONG32 *pVersionMinor);
  void __RPC_STUB IMMCVersionInfo_GetMMCVersion_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __MMCVersionLib_LIBRARY_DEFINED__
#define __MMCVersionLib_LIBRARY_DEFINED__
  EXTERN_C const IID LIBID_MMCVersionLib;
  EXTERN_C const CLSID CLSID_MMCVersionInfo;
#ifdef __cplusplus
  class MMCVersionInfo;
#endif
  EXTERN_C const CLSID CLSID_ConsolePower;
#ifdef __cplusplus
  class ConsolePower;
#endif
#endif

#ifndef __IExtendView_INTERFACE_DEFINED__
#define __IExtendView_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IExtendView;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IExtendView : public IUnknown {
  public:
    virtual HRESULT WINAPI GetViews(LPDATAOBJECT pDataObject,LPVIEWEXTENSIONCALLBACK pViewExtensionCallback) = 0;
  };
#else
  typedef struct IExtendViewVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IExtendView *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IExtendView *This);
      ULONG (WINAPI *Release)(IExtendView *This);
      HRESULT (WINAPI *GetViews)(IExtendView *This,LPDATAOBJECT pDataObject,LPVIEWEXTENSIONCALLBACK pViewExtensionCallback);
    END_INTERFACE
  } IExtendViewVtbl;
  struct IExtendView {
    CONST_VTBL struct IExtendViewVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IExtendView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExtendView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExtendView_Release(This) (This)->lpVtbl->Release(This)
#define IExtendView_GetViews(This,pDataObject,pViewExtensionCallback) (This)->lpVtbl->GetViews(This,pDataObject,pViewExtensionCallback)
#endif
#endif
  HRESULT WINAPI IExtendView_GetViews_Proxy(IExtendView *This,LPDATAOBJECT pDataObject,LPVIEWEXTENSIONCALLBACK pViewExtensionCallback);
  void __RPC_STUB IExtendView_GetViews_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IViewExtensionCallback_INTERFACE_DEFINED__
#define __IViewExtensionCallback_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IViewExtensionCallback;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IViewExtensionCallback : public IUnknown {
  public:
    virtual HRESULT WINAPI AddView(PMMC_EXT_VIEW_DATA pExtViewData) = 0;
  };
#else
  typedef struct IViewExtensionCallbackVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IViewExtensionCallback *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IViewExtensionCallback *This);
      ULONG (WINAPI *Release)(IViewExtensionCallback *This);
      HRESULT (WINAPI *AddView)(IViewExtensionCallback *This,PMMC_EXT_VIEW_DATA pExtViewData);
    END_INTERFACE
  } IViewExtensionCallbackVtbl;
  struct IViewExtensionCallback {
    CONST_VTBL struct IViewExtensionCallbackVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IViewExtensionCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IViewExtensionCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IViewExtensionCallback_Release(This) (This)->lpVtbl->Release(This)
#define IViewExtensionCallback_AddView(This,pExtViewData) (This)->lpVtbl->AddView(This,pExtViewData)
#endif
#endif
  HRESULT WINAPI IViewExtensionCallback_AddView_Proxy(IViewExtensionCallback *This,PMMC_EXT_VIEW_DATA pExtViewData);
  void __RPC_STUB IViewExtensionCallback_AddView_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IConsolePower_INTERFACE_DEFINED__
#define __IConsolePower_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IConsolePower;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IConsolePower : public IUnknown {
  public:
    virtual HRESULT WINAPI SetExecutionState(DWORD dwAdd,DWORD dwRemove) = 0;
    virtual HRESULT WINAPI ResetIdleTimer(DWORD dwFlags) = 0;
  };
#else
  typedef struct IConsolePowerVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IConsolePower *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IConsolePower *This);
      ULONG (WINAPI *Release)(IConsolePower *This);
      HRESULT (WINAPI *SetExecutionState)(IConsolePower *This,DWORD dwAdd,DWORD dwRemove);
      HRESULT (WINAPI *ResetIdleTimer)(IConsolePower *This,DWORD dwFlags);
    END_INTERFACE
  } IConsolePowerVtbl;
  struct IConsolePower {
    CONST_VTBL struct IConsolePowerVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IConsolePower_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConsolePower_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConsolePower_Release(This) (This)->lpVtbl->Release(This)
#define IConsolePower_SetExecutionState(This,dwAdd,dwRemove) (This)->lpVtbl->SetExecutionState(This,dwAdd,dwRemove)
#define IConsolePower_ResetIdleTimer(This,dwFlags) (This)->lpVtbl->ResetIdleTimer(This,dwFlags)
#endif
#endif
  HRESULT WINAPI IConsolePower_SetExecutionState_Proxy(IConsolePower *This,DWORD dwAdd,DWORD dwRemove);
  void __RPC_STUB IConsolePower_SetExecutionState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IConsolePower_ResetIdleTimer_Proxy(IConsolePower *This,DWORD dwFlags);
  void __RPC_STUB IConsolePower_ResetIdleTimer_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IConsolePowerSink_INTERFACE_DEFINED__
#define __IConsolePowerSink_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IConsolePowerSink;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IConsolePowerSink : public IUnknown {
  public:
    virtual HRESULT WINAPI OnPowerBroadcast(UINT nEvent,LPARAM lParam,LRESULT *plReturn) = 0;
  };
#else
  typedef struct IConsolePowerSinkVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IConsolePowerSink *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IConsolePowerSink *This);
      ULONG (WINAPI *Release)(IConsolePowerSink *This);
      HRESULT (WINAPI *OnPowerBroadcast)(IConsolePowerSink *This,UINT nEvent,LPARAM lParam,LRESULT *plReturn);
    END_INTERFACE
  } IConsolePowerSinkVtbl;
  struct IConsolePowerSink {
    CONST_VTBL struct IConsolePowerSinkVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IConsolePowerSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConsolePowerSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConsolePowerSink_Release(This) (This)->lpVtbl->Release(This)
#define IConsolePowerSink_OnPowerBroadcast(This,nEvent,lParam,plReturn) (This)->lpVtbl->OnPowerBroadcast(This,nEvent,lParam,plReturn)
#endif
#endif
  HRESULT WINAPI IConsolePowerSink_OnPowerBroadcast_Proxy(IConsolePowerSink *This,UINT nEvent,LPARAM lParam,LRESULT *plReturn);
  void __RPC_STUB IConsolePowerSink_OnPowerBroadcast_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __INodeProperties_INTERFACE_DEFINED__
#define __INodeProperties_INTERFACE_DEFINED__
  EXTERN_C const IID IID_INodeProperties;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct INodeProperties : public IUnknown {
  public:
    virtual HRESULT WINAPI GetProperty(LPDATAOBJECT pDataObject,BSTR szPropertyName,PBSTR pbstrProperty) = 0;
  };
#else
  typedef struct INodePropertiesVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(INodeProperties *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(INodeProperties *This);
      ULONG (WINAPI *Release)(INodeProperties *This);
      HRESULT (WINAPI *GetProperty)(INodeProperties *This,LPDATAOBJECT pDataObject,BSTR szPropertyName,PBSTR pbstrProperty);
    END_INTERFACE
  } INodePropertiesVtbl;
  struct INodeProperties {
    CONST_VTBL struct INodePropertiesVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define INodeProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INodeProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INodeProperties_Release(This) (This)->lpVtbl->Release(This)
#define INodeProperties_GetProperty(This,pDataObject,szPropertyName,pbstrProperty) (This)->lpVtbl->GetProperty(This,pDataObject,szPropertyName,pbstrProperty)
#endif
#endif
  HRESULT WINAPI INodeProperties_GetProperty_Proxy(INodeProperties *This,LPDATAOBJECT pDataObject,BSTR szPropertyName,PBSTR pbstrProperty);
  void __RPC_STUB INodeProperties_GetProperty_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IConsole3_INTERFACE_DEFINED__
#define __IConsole3_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IConsole3;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IConsole3 : public IConsole2 {
  public:
    virtual HRESULT WINAPI RenameScopeItem(HSCOPEITEM hScopeItem) = 0;
  };
#else
  typedef struct IConsole3Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IConsole3 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IConsole3 *This);
      ULONG (WINAPI *Release)(IConsole3 *This);
      HRESULT (WINAPI *SetHeader)(IConsole3 *This,LPHEADERCTRL pHeader);
      HRESULT (WINAPI *SetToolbar)(IConsole3 *This,LPTOOLBAR pToolbar);
      HRESULT (WINAPI *QueryResultView)(IConsole3 *This,LPUNKNOWN *pUnknown);
      HRESULT (WINAPI *QueryScopeImageList)(IConsole3 *This,LPIMAGELIST *ppImageList);
      HRESULT (WINAPI *QueryResultImageList)(IConsole3 *This,LPIMAGELIST *ppImageList);
      HRESULT (WINAPI *UpdateAllViews)(IConsole3 *This,LPDATAOBJECT lpDataObject,LPARAM data,LONG_PTR hint);
      HRESULT (WINAPI *MessageBox)(IConsole3 *This,LPCWSTR lpszText,LPCWSTR lpszTitle,UINT fuStyle,int *piRetval);
      HRESULT (WINAPI *QueryConsoleVerb)(IConsole3 *This,LPCONSOLEVERB *ppConsoleVerb);
      HRESULT (WINAPI *SelectScopeItem)(IConsole3 *This,HSCOPEITEM hScopeItem);
      HRESULT (WINAPI *GetMainWindow)(IConsole3 *This,HWND *phwnd);
      HRESULT (WINAPI *NewWindow)(IConsole3 *This,HSCOPEITEM hScopeItem,unsigned __LONG32 lOptions);
      HRESULT (WINAPI *Expand)(IConsole3 *This,HSCOPEITEM hItem,WINBOOL bExpand);
      HRESULT (WINAPI *IsTaskpadViewPreferred)(IConsole3 *This);
      HRESULT (WINAPI *SetStatusText)(IConsole3 *This,LPOLESTR pszStatusText);
      HRESULT (WINAPI *RenameScopeItem)(IConsole3 *This,HSCOPEITEM hScopeItem);
    END_INTERFACE
  } IConsole3Vtbl;
  struct IConsole3 {
    CONST_VTBL struct IConsole3Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IConsole3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConsole3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConsole3_Release(This) (This)->lpVtbl->Release(This)
#define IConsole3_SetHeader(This,pHeader) (This)->lpVtbl->SetHeader(This,pHeader)
#define IConsole3_SetToolbar(This,pToolbar) (This)->lpVtbl->SetToolbar(This,pToolbar)
#define IConsole3_QueryResultView(This,pUnknown) (This)->lpVtbl->QueryResultView(This,pUnknown)
#define IConsole3_QueryScopeImageList(This,ppImageList) (This)->lpVtbl->QueryScopeImageList(This,ppImageList)
#define IConsole3_QueryResultImageList(This,ppImageList) (This)->lpVtbl->QueryResultImageList(This,ppImageList)
#define IConsole3_UpdateAllViews(This,lpDataObject,data,hint) (This)->lpVtbl->UpdateAllViews(This,lpDataObject,data,hint)
#define IConsole3_MessageBox(This,lpszText,lpszTitle,fuStyle,piRetval) (This)->lpVtbl->MessageBox(This,lpszText,lpszTitle,fuStyle,piRetval)
#define IConsole3_QueryConsoleVerb(This,ppConsoleVerb) (This)->lpVtbl->QueryConsoleVerb(This,ppConsoleVerb)
#define IConsole3_SelectScopeItem(This,hScopeItem) (This)->lpVtbl->SelectScopeItem(This,hScopeItem)
#define IConsole3_GetMainWindow(This,phwnd) (This)->lpVtbl->GetMainWindow(This,phwnd)
#define IConsole3_NewWindow(This,hScopeItem,lOptions) (This)->lpVtbl->NewWindow(This,hScopeItem,lOptions)
#define IConsole3_Expand(This,hItem,bExpand) (This)->lpVtbl->Expand(This,hItem,bExpand)
#define IConsole3_IsTaskpadViewPreferred(This) (This)->lpVtbl->IsTaskpadViewPreferred(This)
#define IConsole3_SetStatusText(This,pszStatusText) (This)->lpVtbl->SetStatusText(This,pszStatusText)
#define IConsole3_RenameScopeItem(This,hScopeItem) (This)->lpVtbl->RenameScopeItem(This,hScopeItem)
#endif
#endif
  HRESULT WINAPI IConsole3_RenameScopeItem_Proxy(IConsole3 *This,HSCOPEITEM hScopeItem);
  void __RPC_STUB IConsole3_RenameScopeItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IResultData2_INTERFACE_DEFINED__
#define __IResultData2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IResultData2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IResultData2 : public IResultData {
  public:
    virtual HRESULT WINAPI RenameResultItem(HRESULTITEM itemID) = 0;
  };
#else
  typedef struct IResultData2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IResultData2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IResultData2 *This);
      ULONG (WINAPI *Release)(IResultData2 *This);
      HRESULT (WINAPI *InsertItem)(IResultData2 *This,LPRESULTDATAITEM item);
      HRESULT (WINAPI *DeleteItem)(IResultData2 *This,HRESULTITEM itemID,int nCol);
      HRESULT (WINAPI *FindItemByLParam)(IResultData2 *This,LPARAM lParam,HRESULTITEM *pItemID);
      HRESULT (WINAPI *DeleteAllRsltItems)(IResultData2 *This);
      HRESULT (WINAPI *SetItem)(IResultData2 *This,LPRESULTDATAITEM item);
      HRESULT (WINAPI *GetItem)(IResultData2 *This,LPRESULTDATAITEM item);
      HRESULT (WINAPI *GetNextItem)(IResultData2 *This,LPRESULTDATAITEM item);
      HRESULT (WINAPI *ModifyItemState)(IResultData2 *This,int nIndex,HRESULTITEM itemID,UINT uAdd,UINT uRemove);
      HRESULT (WINAPI *ModifyViewStyle)(IResultData2 *This,MMC_RESULT_VIEW_STYLE add,MMC_RESULT_VIEW_STYLE remove);
      HRESULT (WINAPI *SetViewMode)(IResultData2 *This,__LONG32 lViewMode);
      HRESULT (WINAPI *GetViewMode)(IResultData2 *This,__LONG32 *lViewMode);
      HRESULT (WINAPI *UpdateItem)(IResultData2 *This,HRESULTITEM itemID);
      HRESULT (WINAPI *Sort)(IResultData2 *This,int nColumn,DWORD dwSortOptions,LPARAM lUserParam);
      HRESULT (WINAPI *SetDescBarText)(IResultData2 *This,LPOLESTR DescText);
      HRESULT (WINAPI *SetItemCount)(IResultData2 *This,int nItemCount,DWORD dwOptions);
      HRESULT (WINAPI *RenameResultItem)(IResultData2 *This,HRESULTITEM itemID);
    END_INTERFACE
  } IResultData2Vtbl;
  struct IResultData2 {
    CONST_VTBL struct IResultData2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IResultData2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IResultData2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IResultData2_Release(This) (This)->lpVtbl->Release(This)
#define IResultData2_InsertItem(This,item) (This)->lpVtbl->InsertItem(This,item)
#define IResultData2_DeleteItem(This,itemID,nCol) (This)->lpVtbl->DeleteItem(This,itemID,nCol)
#define IResultData2_FindItemByLParam(This,lParam,pItemID) (This)->lpVtbl->FindItemByLParam(This,lParam,pItemID)
#define IResultData2_DeleteAllRsltItems(This) (This)->lpVtbl->DeleteAllRsltItems(This)
#define IResultData2_SetItem(This,item) (This)->lpVtbl->SetItem(This,item)
#define IResultData2_GetItem(This,item) (This)->lpVtbl->GetItem(This,item)
#define IResultData2_GetNextItem(This,item) (This)->lpVtbl->GetNextItem(This,item)
#define IResultData2_ModifyItemState(This,nIndex,itemID,uAdd,uRemove) (This)->lpVtbl->ModifyItemState(This,nIndex,itemID,uAdd,uRemove)
#define IResultData2_ModifyViewStyle(This,add,remove) (This)->lpVtbl->ModifyViewStyle(This,add,remove)
#define IResultData2_SetViewMode(This,lViewMode) (This)->lpVtbl->SetViewMode(This,lViewMode)
#define IResultData2_GetViewMode(This,lViewMode) (This)->lpVtbl->GetViewMode(This,lViewMode)
#define IResultData2_UpdateItem(This,itemID) (This)->lpVtbl->UpdateItem(This,itemID)
#define IResultData2_Sort(This,nColumn,dwSortOptions,lUserParam) (This)->lpVtbl->Sort(This,nColumn,dwSortOptions,lUserParam)
#define IResultData2_SetDescBarText(This,DescText) (This)->lpVtbl->SetDescBarText(This,DescText)
#define IResultData2_SetItemCount(This,nItemCount,dwOptions) (This)->lpVtbl->SetItemCount(This,nItemCount,dwOptions)
#define IResultData2_RenameResultItem(This,itemID) (This)->lpVtbl->RenameResultItem(This,itemID)
#endif
#endif
  HRESULT WINAPI IResultData2_RenameResultItem_Proxy(IResultData2 *This,HRESULTITEM itemID);
  void __RPC_STUB IResultData2_RenameResultItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif
#endif

  extern RPC_IF_HANDLE __MIDL_itf_mmc_0163_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_mmc_0163_v0_0_s_ifspec;

  ULONG __RPC_API BSTR_UserSize(ULONG *,ULONG,BSTR *);
  unsigned char *__RPC_API BSTR_UserMarshal(ULONG *,unsigned char *,BSTR *);
  unsigned char *__RPC_API BSTR_UserUnmarshal(ULONG *,unsigned char *,BSTR *);
  void __RPC_API BSTR_UserFree(ULONG *,BSTR *);
  ULONG __RPC_API HBITMAP_UserSize(ULONG *,ULONG,HBITMAP *);
  unsigned char *__RPC_API HBITMAP_UserMarshal(ULONG *,unsigned char *,HBITMAP *);
  unsigned char *__RPC_API HBITMAP_UserUnmarshal(ULONG *,unsigned char *,HBITMAP *);
  void __RPC_API HBITMAP_UserFree(ULONG *,HBITMAP *);
  ULONG __RPC_API HICON_UserSize(ULONG *,ULONG,HICON *);
  unsigned char *__RPC_API HICON_UserMarshal(ULONG *,unsigned char *,HICON *);
  unsigned char *__RPC_API HICON_UserUnmarshal(ULONG *,unsigned char *,HICON *);
  void __RPC_API HICON_UserFree(ULONG *,HICON *);
  ULONG __RPC_API HPALETTE_UserSize(ULONG *,ULONG,HPALETTE *);
  unsigned char *__RPC_API HPALETTE_UserMarshal(ULONG *,unsigned char *,HPALETTE *);
  unsigned char *__RPC_API HPALETTE_UserUnmarshal(ULONG *,unsigned char *,HPALETTE *);
  void __RPC_API HPALETTE_UserFree(ULONG *,HPALETTE *);
  ULONG __RPC_API HWND_UserSize(ULONG *,ULONG,HWND *);
  unsigned char *__RPC_API HWND_UserMarshal(ULONG *,unsigned char *,HWND *);
  unsigned char *__RPC_API HWND_UserUnmarshal(ULONG *,unsigned char *,HWND *);
  void __RPC_API HWND_UserFree(ULONG *,HWND *);

#ifdef __cplusplus
}
#endif
#endif
