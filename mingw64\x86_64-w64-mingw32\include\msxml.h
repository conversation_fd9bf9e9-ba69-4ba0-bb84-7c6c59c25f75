/*** Autogenerated by WIDL 1.6 from include/msxml.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __msxml_h__
#define __msxml_h__

/* Forward declarations */

/* Headers for imported files */

#include <unknwn.h>
#include <objidl.h>
#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#pragma push_macro("abort")
#undef abort
#ifndef __IXMLDOMImplementation_FWD_DEFINED__
#define __IXMLDOMImplementation_FWD_DEFINED__
typedef struct IXMLDOMImplementation IXMLDOMImplementation;
#endif

#ifndef __IXMLDOMNode_FWD_DEFINED__
#define __IXMLDOMNode_FWD_DEFINED__
typedef struct IXMLDOMNode IXMLDOMNode;
#endif

#ifndef __IXMLDOMDocumentFragment_FWD_DEFINED__
#define __IXMLDOMDocumentFragment_FWD_DEFINED__
typedef struct IXMLDOMDocumentFragment IXMLDOMDocumentFragment;
#endif

#ifndef __IXMLDOMDocument_FWD_DEFINED__
#define __IXMLDOMDocument_FWD_DEFINED__
typedef struct IXMLDOMDocument IXMLDOMDocument;
#endif

#ifndef __IXMLDOMNodeList_FWD_DEFINED__
#define __IXMLDOMNodeList_FWD_DEFINED__
typedef struct IXMLDOMNodeList IXMLDOMNodeList;
#endif

#ifndef __IXMLDOMNamedNodeMap_FWD_DEFINED__
#define __IXMLDOMNamedNodeMap_FWD_DEFINED__
typedef struct IXMLDOMNamedNodeMap IXMLDOMNamedNodeMap;
#endif

#ifndef __IXMLDOMCharacterData_FWD_DEFINED__
#define __IXMLDOMCharacterData_FWD_DEFINED__
typedef struct IXMLDOMCharacterData IXMLDOMCharacterData;
#endif

#ifndef __IXMLDOMAttribute_FWD_DEFINED__
#define __IXMLDOMAttribute_FWD_DEFINED__
typedef struct IXMLDOMAttribute IXMLDOMAttribute;
#endif

#ifndef __IXMLDOMElement_FWD_DEFINED__
#define __IXMLDOMElement_FWD_DEFINED__
typedef struct IXMLDOMElement IXMLDOMElement;
#endif

#ifndef __IXMLDOMText_FWD_DEFINED__
#define __IXMLDOMText_FWD_DEFINED__
typedef struct IXMLDOMText IXMLDOMText;
#endif

#ifndef __IXMLDOMComment_FWD_DEFINED__
#define __IXMLDOMComment_FWD_DEFINED__
typedef struct IXMLDOMComment IXMLDOMComment;
#endif

#ifndef __IXMLDOMProcessingInstruction_FWD_DEFINED__
#define __IXMLDOMProcessingInstruction_FWD_DEFINED__
typedef struct IXMLDOMProcessingInstruction IXMLDOMProcessingInstruction;
#endif

#ifndef __IXMLDOMCDATASection_FWD_DEFINED__
#define __IXMLDOMCDATASection_FWD_DEFINED__
typedef struct IXMLDOMCDATASection IXMLDOMCDATASection;
#endif

#ifndef __IXMLDOMDocumentType_FWD_DEFINED__
#define __IXMLDOMDocumentType_FWD_DEFINED__
typedef struct IXMLDOMDocumentType IXMLDOMDocumentType;
#endif

#ifndef __IXMLDOMNotation_FWD_DEFINED__
#define __IXMLDOMNotation_FWD_DEFINED__
typedef struct IXMLDOMNotation IXMLDOMNotation;
#endif

#ifndef __IXMLDOMEntity_FWD_DEFINED__
#define __IXMLDOMEntity_FWD_DEFINED__
typedef struct IXMLDOMEntity IXMLDOMEntity;
#endif

#ifndef __IXMLDOMEntityReference_FWD_DEFINED__
#define __IXMLDOMEntityReference_FWD_DEFINED__
typedef struct IXMLDOMEntityReference IXMLDOMEntityReference;
#endif

#ifndef __IXMLDOMParseError_FWD_DEFINED__
#define __IXMLDOMParseError_FWD_DEFINED__
typedef struct IXMLDOMParseError IXMLDOMParseError;
#endif

#ifndef __IXTLRuntime_FWD_DEFINED__
#define __IXTLRuntime_FWD_DEFINED__
typedef struct IXTLRuntime IXTLRuntime;
#endif

#ifndef __XMLDOMDocumentEvents_FWD_DEFINED__
#define __XMLDOMDocumentEvents_FWD_DEFINED__
typedef struct XMLDOMDocumentEvents XMLDOMDocumentEvents;
#endif

#ifndef __DOMDocument_FWD_DEFINED__
#define __DOMDocument_FWD_DEFINED__
#ifdef __cplusplus
typedef class DOMDocument DOMDocument;
#else
typedef struct DOMDocument DOMDocument;
#endif
#endif

#ifndef __DOMFreeThreadedDocument_FWD_DEFINED__
#define __DOMFreeThreadedDocument_FWD_DEFINED__
#ifdef __cplusplus
typedef class DOMFreeThreadedDocument DOMFreeThreadedDocument;
#else
typedef struct DOMFreeThreadedDocument DOMFreeThreadedDocument;
#endif
#endif

#ifndef __IXMLHttpRequest_FWD_DEFINED__
#define __IXMLHttpRequest_FWD_DEFINED__
typedef struct IXMLHttpRequest IXMLHttpRequest;
#endif

#ifndef __XMLHTTPRequest_FWD_DEFINED__
#define __XMLHTTPRequest_FWD_DEFINED__
#ifdef __cplusplus
typedef class XMLHTTPRequest XMLHTTPRequest;
#else
typedef struct XMLHTTPRequest XMLHTTPRequest;
#endif
#endif

#ifndef __IXMLDSOControl_FWD_DEFINED__
#define __IXMLDSOControl_FWD_DEFINED__
typedef struct IXMLDSOControl IXMLDSOControl;
#endif

#ifndef __XMLDSOControl_FWD_DEFINED__
#define __XMLDSOControl_FWD_DEFINED__
#ifdef __cplusplus
typedef class XMLDSOControl XMLDSOControl;
#else
typedef struct XMLDSOControl XMLDSOControl;
#endif
#endif

#ifndef __IXMLElementCollection_FWD_DEFINED__
#define __IXMLElementCollection_FWD_DEFINED__
typedef struct IXMLElementCollection IXMLElementCollection;
#endif

#ifndef __IXMLDocument_FWD_DEFINED__
#define __IXMLDocument_FWD_DEFINED__
typedef struct IXMLDocument IXMLDocument;
#endif

#ifndef __IXMLDocument2_FWD_DEFINED__
#define __IXMLDocument2_FWD_DEFINED__
typedef struct IXMLDocument2 IXMLDocument2;
#endif

#ifndef __IXMLElement_FWD_DEFINED__
#define __IXMLElement_FWD_DEFINED__
typedef struct IXMLElement IXMLElement;
#endif

#ifndef __IXMLElement2_FWD_DEFINED__
#define __IXMLElement2_FWD_DEFINED__
typedef struct IXMLElement2 IXMLElement2;
#endif

#ifndef __IXMLAttribute_FWD_DEFINED__
#define __IXMLAttribute_FWD_DEFINED__
typedef struct IXMLAttribute IXMLAttribute;
#endif

#ifndef __IXMLError_FWD_DEFINED__
#define __IXMLError_FWD_DEFINED__
typedef struct IXMLError IXMLError;
#endif

#ifndef __XMLDocument_FWD_DEFINED__
#define __XMLDocument_FWD_DEFINED__

#ifdef __cplusplus
typedef class XMLDocument XMLDocument;
#else
typedef struct XMLDocument XMLDocument;
#endif
#endif


  typedef struct _xml_error {
    unsigned int _nLine;
    BSTR _pchBuf;
    unsigned int _cchBuf;
    unsigned int _ich;
    BSTR _pszFound;
    BSTR _pszExpected;
    DWORD _reserved1;
    DWORD _reserved2;
  } XML_ERROR;

  extern RPC_IF_HANDLE __MIDL_itf_msxml_0000_v0_0_c_ifspec;
  extern RPC_IF_HANDLE __MIDL_itf_msxml_0000_v0_0_s_ifspec;

#ifndef __MSXML_LIBRARY_DEFINED__
#define __MSXML_LIBRARY_DEFINED__

  typedef enum tagDOMNodeType {
    NODE_INVALID = 0,NODE_ELEMENT,NODE_ATTRIBUTE,NODE_TEXT,NODE_CDATA_SECTION,
    NODE_ENTITY_REFERENCE,NODE_ENTITY,NODE_PROCESSING_INSTRUCTION,NODE_COMMENT,
    NODE_DOCUMENT,NODE_DOCUMENT_TYPE,NODE_DOCUMENT_FRAGMENT,NODE_NOTATION
  } DOMNodeType;

  typedef enum tagXMLEMEM_TYPE {
    XMLELEMTYPE_ELEMENT = 0,XMLELEMTYPE_TEXT,XMLELEMTYPE_COMMENT,XMLELEMTYPE_DOCUMENT,
    XMLELEMTYPE_DTD,XMLELEMTYPE_PI,XMLELEMTYPE_OTHER
  } XMLELEM_TYPE;

  EXTERN_C const IID LIBID_MSXML;

#ifndef __IXMLDOMImplementation_INTERFACE_DEFINED__
#define __IXMLDOMImplementation_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMImplementation;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMImplementation : public IDispatch {
  public:
    virtual HRESULT WINAPI hasFeature(BSTR feature,BSTR version,VARIANT_BOOL *hasFeature) = 0;
  };
#else
  typedef struct IXMLDOMImplementationVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMImplementation *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMImplementation *This);
      ULONG (WINAPI *Release)(IXMLDOMImplementation *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMImplementation *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMImplementation *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMImplementation *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMImplementation *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *hasFeature)(IXMLDOMImplementation *This,BSTR feature,BSTR version,VARIANT_BOOL *hasFeature);
    END_INTERFACE
  } IXMLDOMImplementationVtbl;
  struct IXMLDOMImplementation {
    CONST_VTBL struct IXMLDOMImplementationVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMImplementation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMImplementation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMImplementation_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMImplementation_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMImplementation_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMImplementation_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMImplementation_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMImplementation_hasFeature(This,feature,version,hasFeature) (This)->lpVtbl->hasFeature(This,feature,version,hasFeature)
#endif
#endif
  HRESULT WINAPI IXMLDOMImplementation_hasFeature_Proxy(IXMLDOMImplementation *This,BSTR feature,BSTR version,VARIANT_BOOL *hasFeature);
  void __RPC_STUB IXMLDOMImplementation_hasFeature_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMNode_INTERFACE_DEFINED__
#define __IXMLDOMNode_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMNode;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMNode : public IDispatch {
  public:
    virtual HRESULT WINAPI get_nodeName(BSTR *name) = 0;
    virtual HRESULT WINAPI get_nodeValue(VARIANT *value) = 0;
    virtual HRESULT WINAPI put_nodeValue(VARIANT value) = 0;
    virtual HRESULT WINAPI get_nodeType(DOMNodeType *type) = 0;
    virtual HRESULT WINAPI get_parentNode(IXMLDOMNode **parent) = 0;
    virtual HRESULT WINAPI get_childNodes(IXMLDOMNodeList **childList) = 0;
    virtual HRESULT WINAPI get_firstChild(IXMLDOMNode **firstChild) = 0;
    virtual HRESULT WINAPI get_lastChild(IXMLDOMNode **lastChild) = 0;
    virtual HRESULT WINAPI get_previousSibling(IXMLDOMNode **previousSibling) = 0;
    virtual HRESULT WINAPI get_nextSibling(IXMLDOMNode **nextSibling) = 0;
    virtual HRESULT WINAPI get_attributes(IXMLDOMNamedNodeMap **attributeMap) = 0;
    virtual HRESULT WINAPI insertBefore(IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) = 0;
    virtual HRESULT WINAPI replaceChild(IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) = 0;
    virtual HRESULT WINAPI removeChild(IXMLDOMNode *childNode,IXMLDOMNode **oldChild) = 0;
    virtual HRESULT WINAPI appendChild(IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) = 0;
    virtual HRESULT WINAPI hasChildNodes(VARIANT_BOOL *hasChild) = 0;
    virtual HRESULT WINAPI get_ownerDocument(IXMLDOMDocument **DOMDocument) = 0;
    virtual HRESULT WINAPI cloneNode(VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) = 0;
    virtual HRESULT WINAPI get_nodeTypeString(BSTR *nodeType) = 0;
    virtual HRESULT WINAPI get_text(BSTR *text) = 0;
    virtual HRESULT WINAPI put_text(BSTR text) = 0;
    virtual HRESULT WINAPI get_specified(VARIANT_BOOL *isSpecified) = 0;
    virtual HRESULT WINAPI get_definition(IXMLDOMNode **definitionNode) = 0;
    virtual HRESULT WINAPI get_nodeTypedValue(VARIANT *typedValue) = 0;
    virtual HRESULT WINAPI put_nodeTypedValue(VARIANT typedValue) = 0;
    virtual HRESULT WINAPI get_dataType(VARIANT *dataTypeName) = 0;
    virtual HRESULT WINAPI put_dataType(BSTR dataTypeName) = 0;
    virtual HRESULT WINAPI get_xml(BSTR *xmlString) = 0;
    virtual HRESULT WINAPI transformNode(IXMLDOMNode *stylesheet,BSTR *xmlString) = 0;
    virtual HRESULT WINAPI selectNodes(BSTR queryString,IXMLDOMNodeList **resultList) = 0;
    virtual HRESULT WINAPI selectSingleNode(BSTR queryString,IXMLDOMNode **resultNode) = 0;
    virtual HRESULT WINAPI get_parsed(VARIANT_BOOL *isParsed) = 0;
    virtual HRESULT WINAPI get_namespaceURI(BSTR *namespaceURI) = 0;
    virtual HRESULT WINAPI get_prefix(BSTR *prefixString) = 0;
    virtual HRESULT WINAPI get_baseName(BSTR *nameString) = 0;
    virtual HRESULT WINAPI transformNodeToObject(IXMLDOMNode *stylesheet,VARIANT outputObject) = 0;
  };
#else
  typedef struct IXMLDOMNodeVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMNode *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMNode *This);
      ULONG (WINAPI *Release)(IXMLDOMNode *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMNode *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMNode *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMNode *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMNode *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMNode *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMNode *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMNode *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMNode *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMNode *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMNode *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMNode *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMNode *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMNode *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMNode *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMNode *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMNode *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMNode *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMNode *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMNode *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMNode *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMNode *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMNode *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMNode *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMNode *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMNode *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMNode *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMNode *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMNode *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMNode *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMNode *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMNode *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMNode *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMNode *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMNode *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMNode *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMNode *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMNode *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMNode *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMNode *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMNode *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
    END_INTERFACE
  } IXMLDOMNodeVtbl;
  struct IXMLDOMNode {
    CONST_VTBL struct IXMLDOMNodeVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMNode_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMNode_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMNode_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMNode_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMNode_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMNode_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMNode_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMNode_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMNode_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMNode_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMNode_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMNode_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMNode_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMNode_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMNode_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMNode_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMNode_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMNode_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMNode_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMNode_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMNode_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMNode_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMNode_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMNode_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMNode_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMNode_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMNode_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMNode_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMNode_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMNode_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMNode_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMNode_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMNode_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMNode_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMNode_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMNode_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMNode_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMNode_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMNode_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMNode_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMNode_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#endif
#endif
  HRESULT WINAPI IXMLDOMNode_get_nodeName_Proxy(IXMLDOMNode *This,BSTR *name);
  void __RPC_STUB IXMLDOMNode_get_nodeName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_nodeValue_Proxy(IXMLDOMNode *This,VARIANT *value);
  void __RPC_STUB IXMLDOMNode_get_nodeValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_put_nodeValue_Proxy(IXMLDOMNode *This,VARIANT value);
  void __RPC_STUB IXMLDOMNode_put_nodeValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_nodeType_Proxy(IXMLDOMNode *This,DOMNodeType *type);
  void __RPC_STUB IXMLDOMNode_get_nodeType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_parentNode_Proxy(IXMLDOMNode *This,IXMLDOMNode **parent);
  void __RPC_STUB IXMLDOMNode_get_parentNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_childNodes_Proxy(IXMLDOMNode *This,IXMLDOMNodeList **childList);
  void __RPC_STUB IXMLDOMNode_get_childNodes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_firstChild_Proxy(IXMLDOMNode *This,IXMLDOMNode **firstChild);
  void __RPC_STUB IXMLDOMNode_get_firstChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_lastChild_Proxy(IXMLDOMNode *This,IXMLDOMNode **lastChild);
  void __RPC_STUB IXMLDOMNode_get_lastChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_previousSibling_Proxy(IXMLDOMNode *This,IXMLDOMNode **previousSibling);
  void __RPC_STUB IXMLDOMNode_get_previousSibling_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_nextSibling_Proxy(IXMLDOMNode *This,IXMLDOMNode **nextSibling);
  void __RPC_STUB IXMLDOMNode_get_nextSibling_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_attributes_Proxy(IXMLDOMNode *This,IXMLDOMNamedNodeMap **attributeMap);
  void __RPC_STUB IXMLDOMNode_get_attributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_insertBefore_Proxy(IXMLDOMNode *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
  void __RPC_STUB IXMLDOMNode_insertBefore_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_replaceChild_Proxy(IXMLDOMNode *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
  void __RPC_STUB IXMLDOMNode_replaceChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_removeChild_Proxy(IXMLDOMNode *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
  void __RPC_STUB IXMLDOMNode_removeChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_appendChild_Proxy(IXMLDOMNode *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
  void __RPC_STUB IXMLDOMNode_appendChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_hasChildNodes_Proxy(IXMLDOMNode *This,VARIANT_BOOL *hasChild);
  void __RPC_STUB IXMLDOMNode_hasChildNodes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_ownerDocument_Proxy(IXMLDOMNode *This,IXMLDOMDocument **DOMDocument);
  void __RPC_STUB IXMLDOMNode_get_ownerDocument_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_cloneNode_Proxy(IXMLDOMNode *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
  void __RPC_STUB IXMLDOMNode_cloneNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_nodeTypeString_Proxy(IXMLDOMNode *This,BSTR *nodeType);
  void __RPC_STUB IXMLDOMNode_get_nodeTypeString_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_text_Proxy(IXMLDOMNode *This,BSTR *text);
  void __RPC_STUB IXMLDOMNode_get_text_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_put_text_Proxy(IXMLDOMNode *This,BSTR text);
  void __RPC_STUB IXMLDOMNode_put_text_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_specified_Proxy(IXMLDOMNode *This,VARIANT_BOOL *isSpecified);
  void __RPC_STUB IXMLDOMNode_get_specified_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_definition_Proxy(IXMLDOMNode *This,IXMLDOMNode **definitionNode);
  void __RPC_STUB IXMLDOMNode_get_definition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_nodeTypedValue_Proxy(IXMLDOMNode *This,VARIANT *typedValue);
  void __RPC_STUB IXMLDOMNode_get_nodeTypedValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_put_nodeTypedValue_Proxy(IXMLDOMNode *This,VARIANT typedValue);
  void __RPC_STUB IXMLDOMNode_put_nodeTypedValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_dataType_Proxy(IXMLDOMNode *This,VARIANT *dataTypeName);
  void __RPC_STUB IXMLDOMNode_get_dataType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_put_dataType_Proxy(IXMLDOMNode *This,BSTR dataTypeName);
  void __RPC_STUB IXMLDOMNode_put_dataType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_xml_Proxy(IXMLDOMNode *This,BSTR *xmlString);
  void __RPC_STUB IXMLDOMNode_get_xml_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_transformNode_Proxy(IXMLDOMNode *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
  void __RPC_STUB IXMLDOMNode_transformNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_selectNodes_Proxy(IXMLDOMNode *This,BSTR queryString,IXMLDOMNodeList **resultList);
  void __RPC_STUB IXMLDOMNode_selectNodes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_selectSingleNode_Proxy(IXMLDOMNode *This,BSTR queryString,IXMLDOMNode **resultNode);
  void __RPC_STUB IXMLDOMNode_selectSingleNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_parsed_Proxy(IXMLDOMNode *This,VARIANT_BOOL *isParsed);
  void __RPC_STUB IXMLDOMNode_get_parsed_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_namespaceURI_Proxy(IXMLDOMNode *This,BSTR *namespaceURI);
  void __RPC_STUB IXMLDOMNode_get_namespaceURI_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_prefix_Proxy(IXMLDOMNode *This,BSTR *prefixString);
  void __RPC_STUB IXMLDOMNode_get_prefix_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_get_baseName_Proxy(IXMLDOMNode *This,BSTR *nameString);
  void __RPC_STUB IXMLDOMNode_get_baseName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNode_transformNodeToObject_Proxy(IXMLDOMNode *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
  void __RPC_STUB IXMLDOMNode_transformNodeToObject_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMDocumentFragment_INTERFACE_DEFINED__
#define __IXMLDOMDocumentFragment_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMDocumentFragment;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMDocumentFragment : public IXMLDOMNode {
  };
#else
  typedef struct IXMLDOMDocumentFragmentVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMDocumentFragment *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMDocumentFragment *This);
      ULONG (WINAPI *Release)(IXMLDOMDocumentFragment *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMDocumentFragment *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMDocumentFragment *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMDocumentFragment *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMDocumentFragment *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMDocumentFragment *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMDocumentFragment *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMDocumentFragment *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMDocumentFragment *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMDocumentFragment *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMDocumentFragment *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMDocumentFragment *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMDocumentFragment *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMDocumentFragment *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMDocumentFragment *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMDocumentFragment *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMDocumentFragment *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMDocumentFragment *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMDocumentFragment *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMDocumentFragment *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMDocumentFragment *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMDocumentFragment *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMDocumentFragment *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMDocumentFragment *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMDocumentFragment *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMDocumentFragment *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMDocumentFragment *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMDocumentFragment *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMDocumentFragment *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMDocumentFragment *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMDocumentFragment *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMDocumentFragment *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMDocumentFragment *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMDocumentFragment *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMDocumentFragment *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMDocumentFragment *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMDocumentFragment *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMDocumentFragment *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMDocumentFragment *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMDocumentFragment *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMDocumentFragment *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
    END_INTERFACE
  } IXMLDOMDocumentFragmentVtbl;
  struct IXMLDOMDocumentFragment {
    CONST_VTBL struct IXMLDOMDocumentFragmentVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMDocumentFragment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMDocumentFragment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMDocumentFragment_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMDocumentFragment_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMDocumentFragment_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMDocumentFragment_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMDocumentFragment_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMDocumentFragment_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMDocumentFragment_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMDocumentFragment_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMDocumentFragment_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMDocumentFragment_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMDocumentFragment_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMDocumentFragment_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMDocumentFragment_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMDocumentFragment_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMDocumentFragment_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMDocumentFragment_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMDocumentFragment_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMDocumentFragment_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMDocumentFragment_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMDocumentFragment_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMDocumentFragment_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMDocumentFragment_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMDocumentFragment_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMDocumentFragment_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMDocumentFragment_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMDocumentFragment_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMDocumentFragment_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMDocumentFragment_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMDocumentFragment_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMDocumentFragment_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMDocumentFragment_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMDocumentFragment_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMDocumentFragment_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMDocumentFragment_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMDocumentFragment_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMDocumentFragment_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMDocumentFragment_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMDocumentFragment_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMDocumentFragment_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMDocumentFragment_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMDocumentFragment_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#endif
#endif
#endif

#ifndef __IXMLDOMDocument_INTERFACE_DEFINED__
#define __IXMLDOMDocument_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMDocument;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMDocument : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI get_doctype(IXMLDOMDocumentType **documentType) = 0;
    virtual HRESULT WINAPI get_implementation(IXMLDOMImplementation **impl) = 0;
    virtual HRESULT WINAPI get_documentElement(IXMLDOMElement **DOMElement) = 0;
    virtual HRESULT WINAPI putref_documentElement(IXMLDOMElement *DOMElement) = 0;
    virtual HRESULT WINAPI createElement(BSTR tagName,IXMLDOMElement **element) = 0;
    virtual HRESULT WINAPI createDocumentFragment(IXMLDOMDocumentFragment **docFrag) = 0;
    virtual HRESULT WINAPI createTextNode(BSTR data,IXMLDOMText **text) = 0;
    virtual HRESULT WINAPI createComment(BSTR data,IXMLDOMComment **comment) = 0;
    virtual HRESULT WINAPI createCDATASection(BSTR data,IXMLDOMCDATASection **cdata) = 0;
    virtual HRESULT WINAPI createProcessingInstruction(BSTR target,BSTR data,IXMLDOMProcessingInstruction **pi) = 0;
    virtual HRESULT WINAPI createAttribute(BSTR name,IXMLDOMAttribute **attribute) = 0;
    virtual HRESULT WINAPI createEntityReference(BSTR name,IXMLDOMEntityReference **entityRef) = 0;
    virtual HRESULT WINAPI getElementsByTagName(BSTR tagName,IXMLDOMNodeList **resultList) = 0;
    virtual HRESULT WINAPI createNode(VARIANT Type,BSTR name,BSTR namespaceURI,IXMLDOMNode **node) = 0;
    virtual HRESULT WINAPI nodeFromID(BSTR idString,IXMLDOMNode **node) = 0;
    virtual HRESULT WINAPI load(VARIANT xmlSource,VARIANT_BOOL *isSuccessful) = 0;
    virtual HRESULT WINAPI get_readyState(LONG *value) = 0;
    virtual HRESULT WINAPI get_parseError(IXMLDOMParseError **errorObj) = 0;
    virtual HRESULT WINAPI get_url(BSTR *urlString) = 0;
    virtual HRESULT WINAPI get_async(VARIANT_BOOL *isAsync) = 0;
    virtual HRESULT WINAPI put_async(VARIANT_BOOL isAsync) = 0;
    virtual HRESULT WINAPI abort(void) = 0;
    virtual HRESULT WINAPI loadXML(BSTR bstrXML,VARIANT_BOOL *isSuccessful) = 0;
    virtual HRESULT WINAPI save(VARIANT destination) = 0;
    virtual HRESULT WINAPI get_validateOnParse(VARIANT_BOOL *isValidating) = 0;
    virtual HRESULT WINAPI put_validateOnParse(VARIANT_BOOL isValidating) = 0;
    virtual HRESULT WINAPI get_resolveExternals(VARIANT_BOOL *isResolving) = 0;
    virtual HRESULT WINAPI put_resolveExternals(VARIANT_BOOL isResolving) = 0;
    virtual HRESULT WINAPI get_preserveWhiteSpace(VARIANT_BOOL *isPreserving) = 0;
    virtual HRESULT WINAPI put_preserveWhiteSpace(VARIANT_BOOL isPreserving) = 0;
    virtual HRESULT WINAPI put_onreadystatechange(VARIANT readystatechangeSink) = 0;
    virtual HRESULT WINAPI put_ondataavailable(VARIANT ondataavailableSink) = 0;
    virtual HRESULT WINAPI put_ontransformnode(VARIANT ontransformnodeSink) = 0;
  };
#else
  typedef struct IXMLDOMDocumentVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMDocument *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMDocument *This);
      ULONG (WINAPI *Release)(IXMLDOMDocument *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMDocument *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMDocument *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMDocument *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMDocument *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMDocument *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMDocument *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMDocument *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMDocument *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMDocument *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMDocument *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMDocument *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMDocument *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMDocument *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMDocument *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMDocument *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMDocument *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMDocument *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMDocument *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMDocument *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMDocument *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMDocument *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMDocument *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMDocument *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMDocument *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMDocument *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMDocument *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMDocument *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMDocument *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMDocument *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMDocument *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMDocument *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMDocument *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMDocument *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMDocument *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMDocument *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMDocument *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMDocument *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMDocument *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMDocument *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMDocument *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_doctype)(IXMLDOMDocument *This,IXMLDOMDocumentType **documentType);
      HRESULT (WINAPI *get_implementation)(IXMLDOMDocument *This,IXMLDOMImplementation **impl);
      HRESULT (WINAPI *get_documentElement)(IXMLDOMDocument *This,IXMLDOMElement **DOMElement);
      HRESULT (WINAPI *putref_documentElement)(IXMLDOMDocument *This,IXMLDOMElement *DOMElement);
      HRESULT (WINAPI *createElement)(IXMLDOMDocument *This,BSTR tagName,IXMLDOMElement **element);
      HRESULT (WINAPI *createDocumentFragment)(IXMLDOMDocument *This,IXMLDOMDocumentFragment **docFrag);
      HRESULT (WINAPI *createTextNode)(IXMLDOMDocument *This,BSTR data,IXMLDOMText **text);
      HRESULT (WINAPI *createComment)(IXMLDOMDocument *This,BSTR data,IXMLDOMComment **comment);
      HRESULT (WINAPI *createCDATASection)(IXMLDOMDocument *This,BSTR data,IXMLDOMCDATASection **cdata);
      HRESULT (WINAPI *createProcessingInstruction)(IXMLDOMDocument *This,BSTR target,BSTR data,IXMLDOMProcessingInstruction **pi);
      HRESULT (WINAPI *createAttribute)(IXMLDOMDocument *This,BSTR name,IXMLDOMAttribute **attribute);
      HRESULT (WINAPI *createEntityReference)(IXMLDOMDocument *This,BSTR name,IXMLDOMEntityReference **entityRef);
      HRESULT (WINAPI *getElementsByTagName)(IXMLDOMDocument *This,BSTR tagName,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *createNode)(IXMLDOMDocument *This,VARIANT Type,BSTR name,BSTR namespaceURI,IXMLDOMNode **node);
      HRESULT (WINAPI *nodeFromID)(IXMLDOMDocument *This,BSTR idString,IXMLDOMNode **node);
      HRESULT (WINAPI *load)(IXMLDOMDocument *This,VARIANT xmlSource,VARIANT_BOOL *isSuccessful);
      HRESULT (WINAPI *get_readyState)(IXMLDOMDocument *This,LONG *value);
      HRESULT (WINAPI *get_parseError)(IXMLDOMDocument *This,IXMLDOMParseError **errorObj);
      HRESULT (WINAPI *get_url)(IXMLDOMDocument *This,BSTR *urlString);
      HRESULT (WINAPI *get_async)(IXMLDOMDocument *This,VARIANT_BOOL *isAsync);
      HRESULT (WINAPI *put_async)(IXMLDOMDocument *This,VARIANT_BOOL isAsync);
      HRESULT (WINAPI *abort)(IXMLDOMDocument *This);
      HRESULT (WINAPI *loadXML)(IXMLDOMDocument *This,BSTR bstrXML,VARIANT_BOOL *isSuccessful);
      HRESULT (WINAPI *save)(IXMLDOMDocument *This,VARIANT destination);
      HRESULT (WINAPI *get_validateOnParse)(IXMLDOMDocument *This,VARIANT_BOOL *isValidating);
      HRESULT (WINAPI *put_validateOnParse)(IXMLDOMDocument *This,VARIANT_BOOL isValidating);
      HRESULT (WINAPI *get_resolveExternals)(IXMLDOMDocument *This,VARIANT_BOOL *isResolving);
      HRESULT (WINAPI *put_resolveExternals)(IXMLDOMDocument *This,VARIANT_BOOL isResolving);
      HRESULT (WINAPI *get_preserveWhiteSpace)(IXMLDOMDocument *This,VARIANT_BOOL *isPreserving);
      HRESULT (WINAPI *put_preserveWhiteSpace)(IXMLDOMDocument *This,VARIANT_BOOL isPreserving);
      HRESULT (WINAPI *put_onreadystatechange)(IXMLDOMDocument *This,VARIANT readystatechangeSink);
      HRESULT (WINAPI *put_ondataavailable)(IXMLDOMDocument *This,VARIANT ondataavailableSink);
      HRESULT (WINAPI *put_ontransformnode)(IXMLDOMDocument *This,VARIANT ontransformnodeSink);
    END_INTERFACE
  } IXMLDOMDocumentVtbl;
  struct IXMLDOMDocument {
    CONST_VTBL struct IXMLDOMDocumentVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMDocument_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMDocument_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMDocument_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMDocument_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMDocument_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMDocument_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMDocument_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMDocument_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMDocument_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMDocument_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMDocument_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMDocument_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMDocument_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMDocument_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMDocument_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMDocument_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMDocument_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMDocument_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMDocument_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMDocument_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMDocument_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMDocument_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMDocument_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMDocument_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMDocument_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMDocument_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMDocument_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMDocument_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMDocument_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMDocument_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMDocument_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMDocument_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMDocument_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMDocument_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMDocument_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMDocument_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMDocument_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMDocument_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMDocument_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMDocument_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMDocument_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMDocument_get_doctype(This,documentType) (This)->lpVtbl->get_doctype(This,documentType)
#define IXMLDOMDocument_get_implementation(This,impl) (This)->lpVtbl->get_implementation(This,impl)
#define IXMLDOMDocument_get_documentElement(This,DOMElement) (This)->lpVtbl->get_documentElement(This,DOMElement)
#define IXMLDOMDocument_putref_documentElement(This,DOMElement) (This)->lpVtbl->putref_documentElement(This,DOMElement)
#define IXMLDOMDocument_createElement(This,tagName,element) (This)->lpVtbl->createElement(This,tagName,element)
#define IXMLDOMDocument_createDocumentFragment(This,docFrag) (This)->lpVtbl->createDocumentFragment(This,docFrag)
#define IXMLDOMDocument_createTextNode(This,data,text) (This)->lpVtbl->createTextNode(This,data,text)
#define IXMLDOMDocument_createComment(This,data,comment) (This)->lpVtbl->createComment(This,data,comment)
#define IXMLDOMDocument_createCDATASection(This,data,cdata) (This)->lpVtbl->createCDATASection(This,data,cdata)
#define IXMLDOMDocument_createProcessingInstruction(This,target,data,pi) (This)->lpVtbl->createProcessingInstruction(This,target,data,pi)
#define IXMLDOMDocument_createAttribute(This,name,attribute) (This)->lpVtbl->createAttribute(This,name,attribute)
#define IXMLDOMDocument_createEntityReference(This,name,entityRef) (This)->lpVtbl->createEntityReference(This,name,entityRef)
#define IXMLDOMDocument_getElementsByTagName(This,tagName,resultList) (This)->lpVtbl->getElementsByTagName(This,tagName,resultList)
#define IXMLDOMDocument_createNode(This,Type,name,namespaceURI,node) (This)->lpVtbl->createNode(This,Type,name,namespaceURI,node)
#define IXMLDOMDocument_nodeFromID(This,idString,node) (This)->lpVtbl->nodeFromID(This,idString,node)
#define IXMLDOMDocument_load(This,xmlSource,isSuccessful) (This)->lpVtbl->load(This,xmlSource,isSuccessful)
#define IXMLDOMDocument_get_readyState(This,value) (This)->lpVtbl->get_readyState(This,value)
#define IXMLDOMDocument_get_parseError(This,errorObj) (This)->lpVtbl->get_parseError(This,errorObj)
#define IXMLDOMDocument_get_url(This,urlString) (This)->lpVtbl->get_url(This,urlString)
#define IXMLDOMDocument_get_async(This,isAsync) (This)->lpVtbl->get_async(This,isAsync)
#define IXMLDOMDocument_put_async(This,isAsync) (This)->lpVtbl->put_async(This,isAsync)
#define IXMLDOMDocument_abort(This) (This)->lpVtbl->abort(This)
#define IXMLDOMDocument_loadXML(This,bstrXML,isSuccessful) (This)->lpVtbl->loadXML(This,bstrXML,isSuccessful)
#define IXMLDOMDocument_save(This,destination) (This)->lpVtbl->save(This,destination)
#define IXMLDOMDocument_get_validateOnParse(This,isValidating) (This)->lpVtbl->get_validateOnParse(This,isValidating)
#define IXMLDOMDocument_put_validateOnParse(This,isValidating) (This)->lpVtbl->put_validateOnParse(This,isValidating)
#define IXMLDOMDocument_get_resolveExternals(This,isResolving) (This)->lpVtbl->get_resolveExternals(This,isResolving)
#define IXMLDOMDocument_put_resolveExternals(This,isResolving) (This)->lpVtbl->put_resolveExternals(This,isResolving)
#define IXMLDOMDocument_get_preserveWhiteSpace(This,isPreserving) (This)->lpVtbl->get_preserveWhiteSpace(This,isPreserving)
#define IXMLDOMDocument_put_preserveWhiteSpace(This,isPreserving) (This)->lpVtbl->put_preserveWhiteSpace(This,isPreserving)
#define IXMLDOMDocument_put_onreadystatechange(This,readystatechangeSink) (This)->lpVtbl->put_onreadystatechange(This,readystatechangeSink)
#define IXMLDOMDocument_put_ondataavailable(This,ondataavailableSink) (This)->lpVtbl->put_ondataavailable(This,ondataavailableSink)
#define IXMLDOMDocument_put_ontransformnode(This,ontransformnodeSink) (This)->lpVtbl->put_ontransformnode(This,ontransformnodeSink)
#endif
#endif
  HRESULT WINAPI IXMLDOMDocument_get_doctype_Proxy(IXMLDOMDocument *This,IXMLDOMDocumentType **documentType);
  void __RPC_STUB IXMLDOMDocument_get_doctype_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_implementation_Proxy(IXMLDOMDocument *This,IXMLDOMImplementation **impl);
  void __RPC_STUB IXMLDOMDocument_get_implementation_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_documentElement_Proxy(IXMLDOMDocument *This,IXMLDOMElement **DOMElement);
  void __RPC_STUB IXMLDOMDocument_get_documentElement_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_putref_documentElement_Proxy(IXMLDOMDocument *This,IXMLDOMElement *DOMElement);
  void __RPC_STUB IXMLDOMDocument_putref_documentElement_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createElement_Proxy(IXMLDOMDocument *This,BSTR tagName,IXMLDOMElement **element);
  void __RPC_STUB IXMLDOMDocument_createElement_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createDocumentFragment_Proxy(IXMLDOMDocument *This,IXMLDOMDocumentFragment **docFrag);
  void __RPC_STUB IXMLDOMDocument_createDocumentFragment_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createTextNode_Proxy(IXMLDOMDocument *This,BSTR data,IXMLDOMText **text);
  void __RPC_STUB IXMLDOMDocument_createTextNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createComment_Proxy(IXMLDOMDocument *This,BSTR data,IXMLDOMComment **comment);
  void __RPC_STUB IXMLDOMDocument_createComment_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createCDATASection_Proxy(IXMLDOMDocument *This,BSTR data,IXMLDOMCDATASection **cdata);
  void __RPC_STUB IXMLDOMDocument_createCDATASection_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createProcessingInstruction_Proxy(IXMLDOMDocument *This,BSTR target,BSTR data,IXMLDOMProcessingInstruction **pi);
  void __RPC_STUB IXMLDOMDocument_createProcessingInstruction_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createAttribute_Proxy(IXMLDOMDocument *This,BSTR name,IXMLDOMAttribute **attribute);
  void __RPC_STUB IXMLDOMDocument_createAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createEntityReference_Proxy(IXMLDOMDocument *This,BSTR name,IXMLDOMEntityReference **entityRef);
  void __RPC_STUB IXMLDOMDocument_createEntityReference_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_getElementsByTagName_Proxy(IXMLDOMDocument *This,BSTR tagName,IXMLDOMNodeList **resultList);
  void __RPC_STUB IXMLDOMDocument_getElementsByTagName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_createNode_Proxy(IXMLDOMDocument *This,VARIANT Type,BSTR name,BSTR namespaceURI,IXMLDOMNode **node);
  void __RPC_STUB IXMLDOMDocument_createNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_nodeFromID_Proxy(IXMLDOMDocument *This,BSTR idString,IXMLDOMNode **node);
  void __RPC_STUB IXMLDOMDocument_nodeFromID_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_load_Proxy(IXMLDOMDocument *This,VARIANT xmlSource,VARIANT_BOOL *isSuccessful);
  void __RPC_STUB IXMLDOMDocument_load_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_readyState_Proxy(IXMLDOMDocument *This,LONG *value);
  void __RPC_STUB IXMLDOMDocument_get_readyState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_parseError_Proxy(IXMLDOMDocument *This,IXMLDOMParseError **errorObj);
  void __RPC_STUB IXMLDOMDocument_get_parseError_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_url_Proxy(IXMLDOMDocument *This,BSTR *urlString);
  void __RPC_STUB IXMLDOMDocument_get_url_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_async_Proxy(IXMLDOMDocument *This,VARIANT_BOOL *isAsync);
  void __RPC_STUB IXMLDOMDocument_get_async_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_put_async_Proxy(IXMLDOMDocument *This,VARIANT_BOOL isAsync);
  void __RPC_STUB IXMLDOMDocument_put_async_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_abort_Proxy(IXMLDOMDocument *This);
  void __RPC_STUB IXMLDOMDocument_abort_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_loadXML_Proxy(IXMLDOMDocument *This,BSTR bstrXML,VARIANT_BOOL *isSuccessful);
  void __RPC_STUB IXMLDOMDocument_loadXML_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_save_Proxy(IXMLDOMDocument *This,VARIANT destination);
  void __RPC_STUB IXMLDOMDocument_save_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_validateOnParse_Proxy(IXMLDOMDocument *This,VARIANT_BOOL *isValidating);
  void __RPC_STUB IXMLDOMDocument_get_validateOnParse_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_put_validateOnParse_Proxy(IXMLDOMDocument *This,VARIANT_BOOL isValidating);
  void __RPC_STUB IXMLDOMDocument_put_validateOnParse_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_resolveExternals_Proxy(IXMLDOMDocument *This,VARIANT_BOOL *isResolving);
  void __RPC_STUB IXMLDOMDocument_get_resolveExternals_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_put_resolveExternals_Proxy(IXMLDOMDocument *This,VARIANT_BOOL isResolving);
  void __RPC_STUB IXMLDOMDocument_put_resolveExternals_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_get_preserveWhiteSpace_Proxy(IXMLDOMDocument *This,VARIANT_BOOL *isPreserving);
  void __RPC_STUB IXMLDOMDocument_get_preserveWhiteSpace_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_put_preserveWhiteSpace_Proxy(IXMLDOMDocument *This,VARIANT_BOOL isPreserving);
  void __RPC_STUB IXMLDOMDocument_put_preserveWhiteSpace_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_put_onreadystatechange_Proxy(IXMLDOMDocument *This,VARIANT readystatechangeSink);
  void __RPC_STUB IXMLDOMDocument_put_onreadystatechange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_put_ondataavailable_Proxy(IXMLDOMDocument *This,VARIANT ondataavailableSink);
  void __RPC_STUB IXMLDOMDocument_put_ondataavailable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocument_put_ontransformnode_Proxy(IXMLDOMDocument *This,VARIANT ontransformnodeSink);
  void __RPC_STUB IXMLDOMDocument_put_ontransformnode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMNodeList_INTERFACE_DEFINED__
#define __IXMLDOMNodeList_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMNodeList;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMNodeList : public IDispatch {
  public:
    virtual HRESULT WINAPI get_item(LONG index,IXMLDOMNode **listItem) = 0;
    virtual HRESULT WINAPI get_length(LONG *listLength) = 0;
    virtual HRESULT WINAPI nextNode(IXMLDOMNode **nextItem) = 0;
    virtual HRESULT WINAPI reset(void) = 0;
    virtual HRESULT WINAPI get__newEnum(IUnknown **ppUnk) = 0;
  };
#else
  typedef struct IXMLDOMNodeListVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMNodeList *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMNodeList *This);
      ULONG (WINAPI *Release)(IXMLDOMNodeList *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMNodeList *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMNodeList *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMNodeList *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMNodeList *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_item)(IXMLDOMNodeList *This,LONG index,IXMLDOMNode **listItem);
      HRESULT (WINAPI *get_length)(IXMLDOMNodeList *This,LONG *listLength);
      HRESULT (WINAPI *nextNode)(IXMLDOMNodeList *This,IXMLDOMNode **nextItem);
      HRESULT (WINAPI *reset)(IXMLDOMNodeList *This);
      HRESULT (WINAPI *get__newEnum)(IXMLDOMNodeList *This,IUnknown **ppUnk);
    END_INTERFACE
  } IXMLDOMNodeListVtbl;
  struct IXMLDOMNodeList {
    CONST_VTBL struct IXMLDOMNodeListVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMNodeList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMNodeList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMNodeList_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMNodeList_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMNodeList_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMNodeList_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMNodeList_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMNodeList_get_item(This,index,listItem) (This)->lpVtbl->get_item(This,index,listItem)
#define IXMLDOMNodeList_get_length(This,listLength) (This)->lpVtbl->get_length(This,listLength)
#define IXMLDOMNodeList_nextNode(This,nextItem) (This)->lpVtbl->nextNode(This,nextItem)
#define IXMLDOMNodeList_reset(This) (This)->lpVtbl->reset(This)
#define IXMLDOMNodeList_get__newEnum(This,ppUnk) (This)->lpVtbl->get__newEnum(This,ppUnk)
#endif
#endif
  HRESULT WINAPI IXMLDOMNodeList_get_item_Proxy(IXMLDOMNodeList *This,LONG index,IXMLDOMNode **listItem);
  void __RPC_STUB IXMLDOMNodeList_get_item_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNodeList_get_length_Proxy(IXMLDOMNodeList *This,LONG *listLength);
  void __RPC_STUB IXMLDOMNodeList_get_length_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNodeList_nextNode_Proxy(IXMLDOMNodeList *This,IXMLDOMNode **nextItem);
  void __RPC_STUB IXMLDOMNodeList_nextNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNodeList_reset_Proxy(IXMLDOMNodeList *This);
  void __RPC_STUB IXMLDOMNodeList_reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNodeList_get__newEnum_Proxy(IXMLDOMNodeList *This,IUnknown **ppUnk);
  void __RPC_STUB IXMLDOMNodeList_get__newEnum_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMNamedNodeMap_INTERFACE_DEFINED__
#define __IXMLDOMNamedNodeMap_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMNamedNodeMap;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMNamedNodeMap : public IDispatch {
  public:
    virtual HRESULT WINAPI getNamedItem(BSTR name,IXMLDOMNode **namedItem) = 0;
    virtual HRESULT WINAPI setNamedItem(IXMLDOMNode *newItem,IXMLDOMNode **nameItem) = 0;
    virtual HRESULT WINAPI removeNamedItem(BSTR name,IXMLDOMNode **namedItem) = 0;
    virtual HRESULT WINAPI get_item(LONG index,IXMLDOMNode **listItem) = 0;
    virtual HRESULT WINAPI get_length(LONG *listLength) = 0;
    virtual HRESULT WINAPI getQualifiedItem(BSTR baseName,BSTR namespaceURI,IXMLDOMNode **qualifiedItem) = 0;
    virtual HRESULT WINAPI removeQualifiedItem(BSTR baseName,BSTR namespaceURI,IXMLDOMNode **qualifiedItem) = 0;
    virtual HRESULT WINAPI nextNode(IXMLDOMNode **nextItem) = 0;
    virtual HRESULT WINAPI reset(void) = 0;
    virtual HRESULT WINAPI get__newEnum(IUnknown **ppUnk) = 0;
  };
#else
  typedef struct IXMLDOMNamedNodeMapVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMNamedNodeMap *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMNamedNodeMap *This);
      ULONG (WINAPI *Release)(IXMLDOMNamedNodeMap *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMNamedNodeMap *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMNamedNodeMap *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMNamedNodeMap *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMNamedNodeMap *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *getNamedItem)(IXMLDOMNamedNodeMap *This,BSTR name,IXMLDOMNode **namedItem);
      HRESULT (WINAPI *setNamedItem)(IXMLDOMNamedNodeMap *This,IXMLDOMNode *newItem,IXMLDOMNode **nameItem);
      HRESULT (WINAPI *removeNamedItem)(IXMLDOMNamedNodeMap *This,BSTR name,IXMLDOMNode **namedItem);
      HRESULT (WINAPI *get_item)(IXMLDOMNamedNodeMap *This,LONG index,IXMLDOMNode **listItem);
      HRESULT (WINAPI *get_length)(IXMLDOMNamedNodeMap *This,LONG *listLength);
      HRESULT (WINAPI *getQualifiedItem)(IXMLDOMNamedNodeMap *This,BSTR baseName,BSTR namespaceURI,IXMLDOMNode **qualifiedItem);
      HRESULT (WINAPI *removeQualifiedItem)(IXMLDOMNamedNodeMap *This,BSTR baseName,BSTR namespaceURI,IXMLDOMNode **qualifiedItem);
      HRESULT (WINAPI *nextNode)(IXMLDOMNamedNodeMap *This,IXMLDOMNode **nextItem);
      HRESULT (WINAPI *reset)(IXMLDOMNamedNodeMap *This);
      HRESULT (WINAPI *get__newEnum)(IXMLDOMNamedNodeMap *This,IUnknown **ppUnk);
    END_INTERFACE
  } IXMLDOMNamedNodeMapVtbl;
  struct IXMLDOMNamedNodeMap {
    CONST_VTBL struct IXMLDOMNamedNodeMapVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMNamedNodeMap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMNamedNodeMap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMNamedNodeMap_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMNamedNodeMap_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMNamedNodeMap_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMNamedNodeMap_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMNamedNodeMap_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMNamedNodeMap_getNamedItem(This,name,namedItem) (This)->lpVtbl->getNamedItem(This,name,namedItem)
#define IXMLDOMNamedNodeMap_setNamedItem(This,newItem,nameItem) (This)->lpVtbl->setNamedItem(This,newItem,nameItem)
#define IXMLDOMNamedNodeMap_removeNamedItem(This,name,namedItem) (This)->lpVtbl->removeNamedItem(This,name,namedItem)
#define IXMLDOMNamedNodeMap_get_item(This,index,listItem) (This)->lpVtbl->get_item(This,index,listItem)
#define IXMLDOMNamedNodeMap_get_length(This,listLength) (This)->lpVtbl->get_length(This,listLength)
#define IXMLDOMNamedNodeMap_getQualifiedItem(This,baseName,namespaceURI,qualifiedItem) (This)->lpVtbl->getQualifiedItem(This,baseName,namespaceURI,qualifiedItem)
#define IXMLDOMNamedNodeMap_removeQualifiedItem(This,baseName,namespaceURI,qualifiedItem) (This)->lpVtbl->removeQualifiedItem(This,baseName,namespaceURI,qualifiedItem)
#define IXMLDOMNamedNodeMap_nextNode(This,nextItem) (This)->lpVtbl->nextNode(This,nextItem)
#define IXMLDOMNamedNodeMap_reset(This) (This)->lpVtbl->reset(This)
#define IXMLDOMNamedNodeMap_get__newEnum(This,ppUnk) (This)->lpVtbl->get__newEnum(This,ppUnk)
#endif
#endif
  HRESULT WINAPI IXMLDOMNamedNodeMap_getNamedItem_Proxy(IXMLDOMNamedNodeMap *This,BSTR name,IXMLDOMNode **namedItem);
  void __RPC_STUB IXMLDOMNamedNodeMap_getNamedItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_setNamedItem_Proxy(IXMLDOMNamedNodeMap *This,IXMLDOMNode *newItem,IXMLDOMNode **nameItem);
  void __RPC_STUB IXMLDOMNamedNodeMap_setNamedItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_removeNamedItem_Proxy(IXMLDOMNamedNodeMap *This,BSTR name,IXMLDOMNode **namedItem);
  void __RPC_STUB IXMLDOMNamedNodeMap_removeNamedItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_get_item_Proxy(IXMLDOMNamedNodeMap *This,LONG index,IXMLDOMNode **listItem);
  void __RPC_STUB IXMLDOMNamedNodeMap_get_item_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_get_length_Proxy(IXMLDOMNamedNodeMap *This,LONG *listLength);
  void __RPC_STUB IXMLDOMNamedNodeMap_get_length_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_getQualifiedItem_Proxy(IXMLDOMNamedNodeMap *This,BSTR baseName,BSTR namespaceURI,IXMLDOMNode **qualifiedItem);
  void __RPC_STUB IXMLDOMNamedNodeMap_getQualifiedItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_removeQualifiedItem_Proxy(IXMLDOMNamedNodeMap *This,BSTR baseName,BSTR namespaceURI,IXMLDOMNode **qualifiedItem);
  void __RPC_STUB IXMLDOMNamedNodeMap_removeQualifiedItem_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_nextNode_Proxy(IXMLDOMNamedNodeMap *This,IXMLDOMNode **nextItem);
  void __RPC_STUB IXMLDOMNamedNodeMap_nextNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_reset_Proxy(IXMLDOMNamedNodeMap *This);
  void __RPC_STUB IXMLDOMNamedNodeMap_reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNamedNodeMap_get__newEnum_Proxy(IXMLDOMNamedNodeMap *This,IUnknown **ppUnk);
  void __RPC_STUB IXMLDOMNamedNodeMap_get__newEnum_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMCharacterData_INTERFACE_DEFINED__
#define __IXMLDOMCharacterData_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMCharacterData;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMCharacterData : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI get_data(BSTR *data) = 0;
    virtual HRESULT WINAPI put_data(BSTR data) = 0;
    virtual HRESULT WINAPI get_length(LONG *dataLength) = 0;
    virtual HRESULT WINAPI substringData(LONG offset,LONG count,BSTR *data) = 0;
    virtual HRESULT WINAPI appendData(BSTR data) = 0;
    virtual HRESULT WINAPI insertData(LONG offset,BSTR data) = 0;
    virtual HRESULT WINAPI deleteData(LONG offset,LONG count) = 0;
    virtual HRESULT WINAPI replaceData(LONG offset,LONG count,BSTR data) = 0;
  };
#else
  typedef struct IXMLDOMCharacterDataVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMCharacterData *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMCharacterData *This);
      ULONG (WINAPI *Release)(IXMLDOMCharacterData *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMCharacterData *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMCharacterData *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMCharacterData *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMCharacterData *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMCharacterData *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMCharacterData *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMCharacterData *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMCharacterData *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMCharacterData *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMCharacterData *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMCharacterData *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMCharacterData *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMCharacterData *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMCharacterData *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMCharacterData *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMCharacterData *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMCharacterData *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMCharacterData *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMCharacterData *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMCharacterData *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMCharacterData *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMCharacterData *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMCharacterData *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMCharacterData *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMCharacterData *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMCharacterData *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMCharacterData *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMCharacterData *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMCharacterData *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMCharacterData *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMCharacterData *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMCharacterData *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMCharacterData *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMCharacterData *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMCharacterData *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMCharacterData *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMCharacterData *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMCharacterData *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMCharacterData *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMCharacterData *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_data)(IXMLDOMCharacterData *This,BSTR *data);
      HRESULT (WINAPI *put_data)(IXMLDOMCharacterData *This,BSTR data);
      HRESULT (WINAPI *get_length)(IXMLDOMCharacterData *This,LONG *dataLength);
      HRESULT (WINAPI *substringData)(IXMLDOMCharacterData *This,LONG offset,LONG count,BSTR *data);
      HRESULT (WINAPI *appendData)(IXMLDOMCharacterData *This,BSTR data);
      HRESULT (WINAPI *insertData)(IXMLDOMCharacterData *This,LONG offset,BSTR data);
      HRESULT (WINAPI *deleteData)(IXMLDOMCharacterData *This,LONG offset,LONG count);
      HRESULT (WINAPI *replaceData)(IXMLDOMCharacterData *This,LONG offset,LONG count,BSTR data);
    END_INTERFACE
  } IXMLDOMCharacterDataVtbl;
  struct IXMLDOMCharacterData {
    CONST_VTBL struct IXMLDOMCharacterDataVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMCharacterData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMCharacterData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMCharacterData_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMCharacterData_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMCharacterData_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMCharacterData_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMCharacterData_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMCharacterData_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMCharacterData_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMCharacterData_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMCharacterData_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMCharacterData_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMCharacterData_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMCharacterData_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMCharacterData_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMCharacterData_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMCharacterData_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMCharacterData_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMCharacterData_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMCharacterData_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMCharacterData_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMCharacterData_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMCharacterData_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMCharacterData_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMCharacterData_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMCharacterData_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMCharacterData_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMCharacterData_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMCharacterData_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMCharacterData_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMCharacterData_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMCharacterData_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMCharacterData_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMCharacterData_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMCharacterData_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMCharacterData_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMCharacterData_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMCharacterData_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMCharacterData_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMCharacterData_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMCharacterData_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMCharacterData_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMCharacterData_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMCharacterData_get_data(This,data) (This)->lpVtbl->get_data(This,data)
#define IXMLDOMCharacterData_put_data(This,data) (This)->lpVtbl->put_data(This,data)
#define IXMLDOMCharacterData_get_length(This,dataLength) (This)->lpVtbl->get_length(This,dataLength)
#define IXMLDOMCharacterData_substringData(This,offset,count,data) (This)->lpVtbl->substringData(This,offset,count,data)
#define IXMLDOMCharacterData_appendData(This,data) (This)->lpVtbl->appendData(This,data)
#define IXMLDOMCharacterData_insertData(This,offset,data) (This)->lpVtbl->insertData(This,offset,data)
#define IXMLDOMCharacterData_deleteData(This,offset,count) (This)->lpVtbl->deleteData(This,offset,count)
#define IXMLDOMCharacterData_replaceData(This,offset,count,data) (This)->lpVtbl->replaceData(This,offset,count,data)
#endif
#endif
  HRESULT WINAPI IXMLDOMCharacterData_get_data_Proxy(IXMLDOMCharacterData *This,BSTR *data);
  void __RPC_STUB IXMLDOMCharacterData_get_data_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMCharacterData_put_data_Proxy(IXMLDOMCharacterData *This,BSTR data);
  void __RPC_STUB IXMLDOMCharacterData_put_data_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMCharacterData_get_length_Proxy(IXMLDOMCharacterData *This,LONG *dataLength);
  void __RPC_STUB IXMLDOMCharacterData_get_length_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMCharacterData_substringData_Proxy(IXMLDOMCharacterData *This,LONG offset,LONG count,BSTR *data);
  void __RPC_STUB IXMLDOMCharacterData_substringData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMCharacterData_appendData_Proxy(IXMLDOMCharacterData *This,BSTR data);
  void __RPC_STUB IXMLDOMCharacterData_appendData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMCharacterData_insertData_Proxy(IXMLDOMCharacterData *This,LONG offset,BSTR data);
  void __RPC_STUB IXMLDOMCharacterData_insertData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMCharacterData_deleteData_Proxy(IXMLDOMCharacterData *This,LONG offset,LONG count);
  void __RPC_STUB IXMLDOMCharacterData_deleteData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMCharacterData_replaceData_Proxy(IXMLDOMCharacterData *This,LONG offset,LONG count,BSTR data);
  void __RPC_STUB IXMLDOMCharacterData_replaceData_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMAttribute_INTERFACE_DEFINED__
#define __IXMLDOMAttribute_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMAttribute;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMAttribute : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI get_name(BSTR *attributeName) = 0;
    virtual HRESULT WINAPI get_value(VARIANT *attributeValue) = 0;
    virtual HRESULT WINAPI put_value(VARIANT attributeValue) = 0;
  };
#else
  typedef struct IXMLDOMAttributeVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMAttribute *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMAttribute *This);
      ULONG (WINAPI *Release)(IXMLDOMAttribute *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMAttribute *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMAttribute *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMAttribute *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMAttribute *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMAttribute *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMAttribute *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMAttribute *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMAttribute *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMAttribute *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMAttribute *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMAttribute *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMAttribute *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMAttribute *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMAttribute *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMAttribute *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMAttribute *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMAttribute *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMAttribute *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMAttribute *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMAttribute *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMAttribute *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMAttribute *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMAttribute *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMAttribute *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMAttribute *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMAttribute *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMAttribute *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMAttribute *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMAttribute *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMAttribute *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMAttribute *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMAttribute *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMAttribute *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMAttribute *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMAttribute *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMAttribute *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMAttribute *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMAttribute *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMAttribute *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMAttribute *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_name)(IXMLDOMAttribute *This,BSTR *attributeName);
      HRESULT (WINAPI *get_value)(IXMLDOMAttribute *This,VARIANT *attributeValue);
      HRESULT (WINAPI *put_value)(IXMLDOMAttribute *This,VARIANT attributeValue);
    END_INTERFACE
  } IXMLDOMAttributeVtbl;
  struct IXMLDOMAttribute {
    CONST_VTBL struct IXMLDOMAttributeVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMAttribute_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMAttribute_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMAttribute_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMAttribute_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMAttribute_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMAttribute_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMAttribute_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMAttribute_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMAttribute_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMAttribute_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMAttribute_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMAttribute_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMAttribute_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMAttribute_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMAttribute_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMAttribute_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMAttribute_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMAttribute_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMAttribute_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMAttribute_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMAttribute_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMAttribute_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMAttribute_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMAttribute_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMAttribute_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMAttribute_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMAttribute_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMAttribute_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMAttribute_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMAttribute_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMAttribute_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMAttribute_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMAttribute_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMAttribute_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMAttribute_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMAttribute_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMAttribute_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMAttribute_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMAttribute_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMAttribute_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMAttribute_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMAttribute_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMAttribute_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMAttribute_get_name(This,attributeName) (This)->lpVtbl->get_name(This,attributeName)
#define IXMLDOMAttribute_get_value(This,attributeValue) (This)->lpVtbl->get_value(This,attributeValue)
#define IXMLDOMAttribute_put_value(This,attributeValue) (This)->lpVtbl->put_value(This,attributeValue)
#endif
#endif
  HRESULT WINAPI IXMLDOMAttribute_get_name_Proxy(IXMLDOMAttribute *This,BSTR *attributeName);
  void __RPC_STUB IXMLDOMAttribute_get_name_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMAttribute_get_value_Proxy(IXMLDOMAttribute *This,VARIANT *attributeValue);
  void __RPC_STUB IXMLDOMAttribute_get_value_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMAttribute_put_value_Proxy(IXMLDOMAttribute *This,VARIANT attributeValue);
  void __RPC_STUB IXMLDOMAttribute_put_value_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMElement_INTERFACE_DEFINED__
#define __IXMLDOMElement_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMElement;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMElement : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI get_tagName(BSTR *tagName) = 0;
    virtual HRESULT WINAPI getAttribute(BSTR name,VARIANT *value) = 0;
    virtual HRESULT WINAPI setAttribute(BSTR name,VARIANT value) = 0;
    virtual HRESULT WINAPI removeAttribute(BSTR name) = 0;
    virtual HRESULT WINAPI getAttributeNode(BSTR name,IXMLDOMAttribute **attributeNode) = 0;
    virtual HRESULT WINAPI setAttributeNode(IXMLDOMAttribute *DOMAttribute,IXMLDOMAttribute **attributeNode) = 0;
    virtual HRESULT WINAPI removeAttributeNode(IXMLDOMAttribute *DOMAttribute,IXMLDOMAttribute **attributeNode) = 0;
    virtual HRESULT WINAPI getElementsByTagName(BSTR tagName,IXMLDOMNodeList **resultList) = 0;
    virtual HRESULT WINAPI normalize(void) = 0;
  };
#else
  typedef struct IXMLDOMElementVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMElement *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMElement *This);
      ULONG (WINAPI *Release)(IXMLDOMElement *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMElement *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMElement *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMElement *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMElement *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMElement *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMElement *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMElement *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMElement *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMElement *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMElement *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMElement *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMElement *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMElement *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMElement *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMElement *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMElement *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMElement *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMElement *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMElement *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMElement *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMElement *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMElement *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMElement *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMElement *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMElement *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMElement *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMElement *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMElement *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMElement *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMElement *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMElement *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMElement *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMElement *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMElement *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMElement *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMElement *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMElement *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMElement *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMElement *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMElement *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_tagName)(IXMLDOMElement *This,BSTR *tagName);
      HRESULT (WINAPI *getAttribute)(IXMLDOMElement *This,BSTR name,VARIANT *value);
      HRESULT (WINAPI *setAttribute)(IXMLDOMElement *This,BSTR name,VARIANT value);
      HRESULT (WINAPI *removeAttribute)(IXMLDOMElement *This,BSTR name);
      HRESULT (WINAPI *getAttributeNode)(IXMLDOMElement *This,BSTR name,IXMLDOMAttribute **attributeNode);
      HRESULT (WINAPI *setAttributeNode)(IXMLDOMElement *This,IXMLDOMAttribute *DOMAttribute,IXMLDOMAttribute **attributeNode);
      HRESULT (WINAPI *removeAttributeNode)(IXMLDOMElement *This,IXMLDOMAttribute *DOMAttribute,IXMLDOMAttribute **attributeNode);
      HRESULT (WINAPI *getElementsByTagName)(IXMLDOMElement *This,BSTR tagName,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *normalize)(IXMLDOMElement *This);
    END_INTERFACE
  } IXMLDOMElementVtbl;
  struct IXMLDOMElement {
    CONST_VTBL struct IXMLDOMElementVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMElement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMElement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMElement_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMElement_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMElement_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMElement_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMElement_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMElement_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMElement_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMElement_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMElement_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMElement_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMElement_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMElement_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMElement_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMElement_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMElement_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMElement_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMElement_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMElement_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMElement_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMElement_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMElement_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMElement_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMElement_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMElement_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMElement_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMElement_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMElement_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMElement_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMElement_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMElement_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMElement_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMElement_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMElement_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMElement_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMElement_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMElement_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMElement_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMElement_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMElement_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMElement_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMElement_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMElement_get_tagName(This,tagName) (This)->lpVtbl->get_tagName(This,tagName)
#define IXMLDOMElement_getAttribute(This,name,value) (This)->lpVtbl->getAttribute(This,name,value)
#define IXMLDOMElement_setAttribute(This,name,value) (This)->lpVtbl->setAttribute(This,name,value)
#define IXMLDOMElement_removeAttribute(This,name) (This)->lpVtbl->removeAttribute(This,name)
#define IXMLDOMElement_getAttributeNode(This,name,attributeNode) (This)->lpVtbl->getAttributeNode(This,name,attributeNode)
#define IXMLDOMElement_setAttributeNode(This,DOMAttribute,attributeNode) (This)->lpVtbl->setAttributeNode(This,DOMAttribute,attributeNode)
#define IXMLDOMElement_removeAttributeNode(This,DOMAttribute,attributeNode) (This)->lpVtbl->removeAttributeNode(This,DOMAttribute,attributeNode)
#define IXMLDOMElement_getElementsByTagName(This,tagName,resultList) (This)->lpVtbl->getElementsByTagName(This,tagName,resultList)
#define IXMLDOMElement_normalize(This) (This)->lpVtbl->normalize(This)
#endif
#endif
  HRESULT WINAPI IXMLDOMElement_get_tagName_Proxy(IXMLDOMElement *This,BSTR *tagName);
  void __RPC_STUB IXMLDOMElement_get_tagName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMElement_getAttribute_Proxy(IXMLDOMElement *This,BSTR name,VARIANT *value);
  void __RPC_STUB IXMLDOMElement_getAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMElement_setAttribute_Proxy(IXMLDOMElement *This,BSTR name,VARIANT value);
  void __RPC_STUB IXMLDOMElement_setAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMElement_removeAttribute_Proxy(IXMLDOMElement *This,BSTR name);
  void __RPC_STUB IXMLDOMElement_removeAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMElement_getAttributeNode_Proxy(IXMLDOMElement *This,BSTR name,IXMLDOMAttribute **attributeNode);
  void __RPC_STUB IXMLDOMElement_getAttributeNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMElement_setAttributeNode_Proxy(IXMLDOMElement *This,IXMLDOMAttribute *DOMAttribute,IXMLDOMAttribute **attributeNode);
  void __RPC_STUB IXMLDOMElement_setAttributeNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMElement_removeAttributeNode_Proxy(IXMLDOMElement *This,IXMLDOMAttribute *DOMAttribute,IXMLDOMAttribute **attributeNode);
  void __RPC_STUB IXMLDOMElement_removeAttributeNode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMElement_getElementsByTagName_Proxy(IXMLDOMElement *This,BSTR tagName,IXMLDOMNodeList **resultList);
  void __RPC_STUB IXMLDOMElement_getElementsByTagName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMElement_normalize_Proxy(IXMLDOMElement *This);
  void __RPC_STUB IXMLDOMElement_normalize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMText_INTERFACE_DEFINED__
#define __IXMLDOMText_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMText;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMText : public IXMLDOMCharacterData {
  public:
    virtual HRESULT WINAPI splitText(LONG offset,IXMLDOMText **rightHandTextNode) = 0;
  };
#else
  typedef struct IXMLDOMTextVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMText *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMText *This);
      ULONG (WINAPI *Release)(IXMLDOMText *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMText *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMText *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMText *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMText *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMText *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMText *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMText *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMText *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMText *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMText *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMText *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMText *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMText *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMText *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMText *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMText *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMText *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMText *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMText *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMText *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMText *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMText *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMText *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMText *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMText *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMText *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMText *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMText *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMText *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMText *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMText *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMText *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMText *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMText *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMText *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMText *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMText *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMText *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMText *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMText *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_data)(IXMLDOMText *This,BSTR *data);
      HRESULT (WINAPI *put_data)(IXMLDOMText *This,BSTR data);
      HRESULT (WINAPI *get_length)(IXMLDOMText *This,LONG *dataLength);
      HRESULT (WINAPI *substringData)(IXMLDOMText *This,LONG offset,LONG count,BSTR *data);
      HRESULT (WINAPI *appendData)(IXMLDOMText *This,BSTR data);
      HRESULT (WINAPI *insertData)(IXMLDOMText *This,LONG offset,BSTR data);
      HRESULT (WINAPI *deleteData)(IXMLDOMText *This,LONG offset,LONG count);
      HRESULT (WINAPI *replaceData)(IXMLDOMText *This,LONG offset,LONG count,BSTR data);
      HRESULT (WINAPI *splitText)(IXMLDOMText *This,LONG offset,IXMLDOMText **rightHandTextNode);
    END_INTERFACE
  } IXMLDOMTextVtbl;
  struct IXMLDOMText {
    CONST_VTBL struct IXMLDOMTextVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMText_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMText_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMText_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMText_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMText_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMText_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMText_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMText_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMText_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMText_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMText_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMText_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMText_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMText_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMText_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMText_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMText_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMText_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMText_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMText_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMText_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMText_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMText_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMText_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMText_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMText_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMText_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMText_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMText_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMText_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMText_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMText_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMText_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMText_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMText_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMText_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMText_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMText_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMText_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMText_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMText_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMText_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMText_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMText_get_data(This,data) (This)->lpVtbl->get_data(This,data)
#define IXMLDOMText_put_data(This,data) (This)->lpVtbl->put_data(This,data)
#define IXMLDOMText_get_length(This,dataLength) (This)->lpVtbl->get_length(This,dataLength)
#define IXMLDOMText_substringData(This,offset,count,data) (This)->lpVtbl->substringData(This,offset,count,data)
#define IXMLDOMText_appendData(This,data) (This)->lpVtbl->appendData(This,data)
#define IXMLDOMText_insertData(This,offset,data) (This)->lpVtbl->insertData(This,offset,data)
#define IXMLDOMText_deleteData(This,offset,count) (This)->lpVtbl->deleteData(This,offset,count)
#define IXMLDOMText_replaceData(This,offset,count,data) (This)->lpVtbl->replaceData(This,offset,count,data)
#define IXMLDOMText_splitText(This,offset,rightHandTextNode) (This)->lpVtbl->splitText(This,offset,rightHandTextNode)
#endif
#endif
  HRESULT WINAPI IXMLDOMText_splitText_Proxy(IXMLDOMText *This,LONG offset,IXMLDOMText **rightHandTextNode);
  void __RPC_STUB IXMLDOMText_splitText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMComment_INTERFACE_DEFINED__
#define __IXMLDOMComment_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMComment;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMComment : public IXMLDOMCharacterData {
  };
#else
  typedef struct IXMLDOMCommentVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMComment *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMComment *This);
      ULONG (WINAPI *Release)(IXMLDOMComment *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMComment *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMComment *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMComment *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMComment *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMComment *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMComment *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMComment *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMComment *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMComment *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMComment *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMComment *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMComment *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMComment *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMComment *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMComment *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMComment *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMComment *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMComment *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMComment *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMComment *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMComment *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMComment *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMComment *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMComment *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMComment *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMComment *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMComment *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMComment *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMComment *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMComment *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMComment *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMComment *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMComment *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMComment *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMComment *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMComment *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMComment *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMComment *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMComment *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMComment *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_data)(IXMLDOMComment *This,BSTR *data);
      HRESULT (WINAPI *put_data)(IXMLDOMComment *This,BSTR data);
      HRESULT (WINAPI *get_length)(IXMLDOMComment *This,LONG *dataLength);
      HRESULT (WINAPI *substringData)(IXMLDOMComment *This,LONG offset,LONG count,BSTR *data);
      HRESULT (WINAPI *appendData)(IXMLDOMComment *This,BSTR data);
      HRESULT (WINAPI *insertData)(IXMLDOMComment *This,LONG offset,BSTR data);
      HRESULT (WINAPI *deleteData)(IXMLDOMComment *This,LONG offset,LONG count);
      HRESULT (WINAPI *replaceData)(IXMLDOMComment *This,LONG offset,LONG count,BSTR data);
    END_INTERFACE
  } IXMLDOMCommentVtbl;
  struct IXMLDOMComment {
    CONST_VTBL struct IXMLDOMCommentVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMComment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMComment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMComment_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMComment_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMComment_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMComment_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMComment_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMComment_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMComment_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMComment_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMComment_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMComment_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMComment_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMComment_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMComment_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMComment_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMComment_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMComment_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMComment_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMComment_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMComment_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMComment_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMComment_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMComment_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMComment_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMComment_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMComment_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMComment_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMComment_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMComment_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMComment_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMComment_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMComment_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMComment_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMComment_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMComment_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMComment_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMComment_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMComment_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMComment_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMComment_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMComment_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMComment_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMComment_get_data(This,data) (This)->lpVtbl->get_data(This,data)
#define IXMLDOMComment_put_data(This,data) (This)->lpVtbl->put_data(This,data)
#define IXMLDOMComment_get_length(This,dataLength) (This)->lpVtbl->get_length(This,dataLength)
#define IXMLDOMComment_substringData(This,offset,count,data) (This)->lpVtbl->substringData(This,offset,count,data)
#define IXMLDOMComment_appendData(This,data) (This)->lpVtbl->appendData(This,data)
#define IXMLDOMComment_insertData(This,offset,data) (This)->lpVtbl->insertData(This,offset,data)
#define IXMLDOMComment_deleteData(This,offset,count) (This)->lpVtbl->deleteData(This,offset,count)
#define IXMLDOMComment_replaceData(This,offset,count,data) (This)->lpVtbl->replaceData(This,offset,count,data)
#endif
#endif
#endif

#ifndef __IXMLDOMProcessingInstruction_INTERFACE_DEFINED__
#define __IXMLDOMProcessingInstruction_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMProcessingInstruction;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMProcessingInstruction : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI get_target(BSTR *name) = 0;
    virtual HRESULT WINAPI get_data(BSTR *value) = 0;
    virtual HRESULT WINAPI put_data(BSTR value) = 0;
  };
#else
  typedef struct IXMLDOMProcessingInstructionVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMProcessingInstruction *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMProcessingInstruction *This);
      ULONG (WINAPI *Release)(IXMLDOMProcessingInstruction *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMProcessingInstruction *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMProcessingInstruction *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMProcessingInstruction *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMProcessingInstruction *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMProcessingInstruction *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMProcessingInstruction *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMProcessingInstruction *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMProcessingInstruction *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMProcessingInstruction *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMProcessingInstruction *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMProcessingInstruction *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMProcessingInstruction *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMProcessingInstruction *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMProcessingInstruction *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMProcessingInstruction *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMProcessingInstruction *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMProcessingInstruction *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMProcessingInstruction *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMProcessingInstruction *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMProcessingInstruction *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMProcessingInstruction *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMProcessingInstruction *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMProcessingInstruction *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMProcessingInstruction *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMProcessingInstruction *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMProcessingInstruction *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMProcessingInstruction *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMProcessingInstruction *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMProcessingInstruction *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMProcessingInstruction *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMProcessingInstruction *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMProcessingInstruction *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMProcessingInstruction *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMProcessingInstruction *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMProcessingInstruction *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMProcessingInstruction *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMProcessingInstruction *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMProcessingInstruction *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMProcessingInstruction *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMProcessingInstruction *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_target)(IXMLDOMProcessingInstruction *This,BSTR *name);
      HRESULT (WINAPI *get_data)(IXMLDOMProcessingInstruction *This,BSTR *value);
      HRESULT (WINAPI *put_data)(IXMLDOMProcessingInstruction *This,BSTR value);
    END_INTERFACE
  } IXMLDOMProcessingInstructionVtbl;
  struct IXMLDOMProcessingInstruction {
    CONST_VTBL struct IXMLDOMProcessingInstructionVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMProcessingInstruction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMProcessingInstruction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMProcessingInstruction_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMProcessingInstruction_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMProcessingInstruction_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMProcessingInstruction_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMProcessingInstruction_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMProcessingInstruction_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMProcessingInstruction_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMProcessingInstruction_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMProcessingInstruction_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMProcessingInstruction_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMProcessingInstruction_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMProcessingInstruction_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMProcessingInstruction_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMProcessingInstruction_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMProcessingInstruction_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMProcessingInstruction_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMProcessingInstruction_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMProcessingInstruction_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMProcessingInstruction_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMProcessingInstruction_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMProcessingInstruction_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMProcessingInstruction_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMProcessingInstruction_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMProcessingInstruction_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMProcessingInstruction_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMProcessingInstruction_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMProcessingInstruction_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMProcessingInstruction_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMProcessingInstruction_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMProcessingInstruction_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMProcessingInstruction_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMProcessingInstruction_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMProcessingInstruction_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMProcessingInstruction_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMProcessingInstruction_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMProcessingInstruction_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMProcessingInstruction_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMProcessingInstruction_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMProcessingInstruction_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMProcessingInstruction_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMProcessingInstruction_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMProcessingInstruction_get_target(This,name) (This)->lpVtbl->get_target(This,name)
#define IXMLDOMProcessingInstruction_get_data(This,value) (This)->lpVtbl->get_data(This,value)
#define IXMLDOMProcessingInstruction_put_data(This,value) (This)->lpVtbl->put_data(This,value)
#endif
#endif
  HRESULT WINAPI IXMLDOMProcessingInstruction_get_target_Proxy(IXMLDOMProcessingInstruction *This,BSTR *name);
  void __RPC_STUB IXMLDOMProcessingInstruction_get_target_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMProcessingInstruction_get_data_Proxy(IXMLDOMProcessingInstruction *This,BSTR *value);
  void __RPC_STUB IXMLDOMProcessingInstruction_get_data_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMProcessingInstruction_put_data_Proxy(IXMLDOMProcessingInstruction *This,BSTR value);
  void __RPC_STUB IXMLDOMProcessingInstruction_put_data_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMCDATASection_INTERFACE_DEFINED__
#define __IXMLDOMCDATASection_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMCDATASection;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMCDATASection : public IXMLDOMText {
  };
#else
  typedef struct IXMLDOMCDATASectionVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMCDATASection *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMCDATASection *This);
      ULONG (WINAPI *Release)(IXMLDOMCDATASection *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMCDATASection *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMCDATASection *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMCDATASection *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMCDATASection *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMCDATASection *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMCDATASection *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMCDATASection *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMCDATASection *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMCDATASection *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMCDATASection *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMCDATASection *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMCDATASection *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMCDATASection *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMCDATASection *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMCDATASection *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMCDATASection *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMCDATASection *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMCDATASection *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMCDATASection *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMCDATASection *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMCDATASection *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMCDATASection *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMCDATASection *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMCDATASection *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMCDATASection *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMCDATASection *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMCDATASection *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMCDATASection *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMCDATASection *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMCDATASection *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMCDATASection *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMCDATASection *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMCDATASection *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMCDATASection *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMCDATASection *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMCDATASection *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMCDATASection *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMCDATASection *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMCDATASection *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMCDATASection *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_data)(IXMLDOMCDATASection *This,BSTR *data);
      HRESULT (WINAPI *put_data)(IXMLDOMCDATASection *This,BSTR data);
      HRESULT (WINAPI *get_length)(IXMLDOMCDATASection *This,LONG *dataLength);
      HRESULT (WINAPI *substringData)(IXMLDOMCDATASection *This,LONG offset,LONG count,BSTR *data);
      HRESULT (WINAPI *appendData)(IXMLDOMCDATASection *This,BSTR data);
      HRESULT (WINAPI *insertData)(IXMLDOMCDATASection *This,LONG offset,BSTR data);
      HRESULT (WINAPI *deleteData)(IXMLDOMCDATASection *This,LONG offset,LONG count);
      HRESULT (WINAPI *replaceData)(IXMLDOMCDATASection *This,LONG offset,LONG count,BSTR data);
      HRESULT (WINAPI *splitText)(IXMLDOMCDATASection *This,LONG offset,IXMLDOMText **rightHandTextNode);
    END_INTERFACE
  } IXMLDOMCDATASectionVtbl;
  struct IXMLDOMCDATASection {
    CONST_VTBL struct IXMLDOMCDATASectionVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMCDATASection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMCDATASection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMCDATASection_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMCDATASection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMCDATASection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMCDATASection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMCDATASection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMCDATASection_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMCDATASection_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMCDATASection_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMCDATASection_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMCDATASection_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMCDATASection_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMCDATASection_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMCDATASection_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMCDATASection_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMCDATASection_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMCDATASection_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMCDATASection_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMCDATASection_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMCDATASection_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMCDATASection_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMCDATASection_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMCDATASection_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMCDATASection_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMCDATASection_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMCDATASection_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMCDATASection_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMCDATASection_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMCDATASection_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMCDATASection_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMCDATASection_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMCDATASection_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMCDATASection_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMCDATASection_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMCDATASection_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMCDATASection_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMCDATASection_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMCDATASection_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMCDATASection_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMCDATASection_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMCDATASection_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMCDATASection_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMCDATASection_get_data(This,data) (This)->lpVtbl->get_data(This,data)
#define IXMLDOMCDATASection_put_data(This,data) (This)->lpVtbl->put_data(This,data)
#define IXMLDOMCDATASection_get_length(This,dataLength) (This)->lpVtbl->get_length(This,dataLength)
#define IXMLDOMCDATASection_substringData(This,offset,count,data) (This)->lpVtbl->substringData(This,offset,count,data)
#define IXMLDOMCDATASection_appendData(This,data) (This)->lpVtbl->appendData(This,data)
#define IXMLDOMCDATASection_insertData(This,offset,data) (This)->lpVtbl->insertData(This,offset,data)
#define IXMLDOMCDATASection_deleteData(This,offset,count) (This)->lpVtbl->deleteData(This,offset,count)
#define IXMLDOMCDATASection_replaceData(This,offset,count,data) (This)->lpVtbl->replaceData(This,offset,count,data)
#define IXMLDOMCDATASection_splitText(This,offset,rightHandTextNode) (This)->lpVtbl->splitText(This,offset,rightHandTextNode)
#endif
#endif
#endif

#ifndef __IXMLDOMDocumentType_INTERFACE_DEFINED__
#define __IXMLDOMDocumentType_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMDocumentType;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMDocumentType : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI get_name(BSTR *rootName) = 0;
    virtual HRESULT WINAPI get_entities(IXMLDOMNamedNodeMap **entityMap) = 0;
    virtual HRESULT WINAPI get_notations(IXMLDOMNamedNodeMap **notationMap) = 0;
  };
#else
  typedef struct IXMLDOMDocumentTypeVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMDocumentType *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMDocumentType *This);
      ULONG (WINAPI *Release)(IXMLDOMDocumentType *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMDocumentType *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMDocumentType *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMDocumentType *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMDocumentType *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMDocumentType *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMDocumentType *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMDocumentType *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMDocumentType *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMDocumentType *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMDocumentType *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMDocumentType *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMDocumentType *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMDocumentType *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMDocumentType *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMDocumentType *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMDocumentType *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMDocumentType *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMDocumentType *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMDocumentType *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMDocumentType *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMDocumentType *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMDocumentType *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMDocumentType *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMDocumentType *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMDocumentType *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMDocumentType *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMDocumentType *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMDocumentType *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMDocumentType *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMDocumentType *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMDocumentType *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMDocumentType *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMDocumentType *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMDocumentType *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMDocumentType *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMDocumentType *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMDocumentType *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMDocumentType *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMDocumentType *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMDocumentType *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_name)(IXMLDOMDocumentType *This,BSTR *rootName);
      HRESULT (WINAPI *get_entities)(IXMLDOMDocumentType *This,IXMLDOMNamedNodeMap **entityMap);
      HRESULT (WINAPI *get_notations)(IXMLDOMDocumentType *This,IXMLDOMNamedNodeMap **notationMap);
    END_INTERFACE
  } IXMLDOMDocumentTypeVtbl;
  struct IXMLDOMDocumentType {
    CONST_VTBL struct IXMLDOMDocumentTypeVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMDocumentType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMDocumentType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMDocumentType_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMDocumentType_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMDocumentType_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMDocumentType_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMDocumentType_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMDocumentType_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMDocumentType_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMDocumentType_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMDocumentType_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMDocumentType_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMDocumentType_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMDocumentType_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMDocumentType_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMDocumentType_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMDocumentType_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMDocumentType_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMDocumentType_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMDocumentType_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMDocumentType_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMDocumentType_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMDocumentType_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMDocumentType_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMDocumentType_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMDocumentType_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMDocumentType_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMDocumentType_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMDocumentType_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMDocumentType_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMDocumentType_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMDocumentType_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMDocumentType_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMDocumentType_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMDocumentType_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMDocumentType_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMDocumentType_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMDocumentType_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMDocumentType_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMDocumentType_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMDocumentType_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMDocumentType_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMDocumentType_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMDocumentType_get_name(This,rootName) (This)->lpVtbl->get_name(This,rootName)
#define IXMLDOMDocumentType_get_entities(This,entityMap) (This)->lpVtbl->get_entities(This,entityMap)
#define IXMLDOMDocumentType_get_notations(This,notationMap) (This)->lpVtbl->get_notations(This,notationMap)
#endif
#endif
  HRESULT WINAPI IXMLDOMDocumentType_get_name_Proxy(IXMLDOMDocumentType *This,BSTR *rootName);
  void __RPC_STUB IXMLDOMDocumentType_get_name_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocumentType_get_entities_Proxy(IXMLDOMDocumentType *This,IXMLDOMNamedNodeMap **entityMap);
  void __RPC_STUB IXMLDOMDocumentType_get_entities_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMDocumentType_get_notations_Proxy(IXMLDOMDocumentType *This,IXMLDOMNamedNodeMap **notationMap);
  void __RPC_STUB IXMLDOMDocumentType_get_notations_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMNotation_INTERFACE_DEFINED__
#define __IXMLDOMNotation_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMNotation;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMNotation : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI get_publicId(VARIANT *publicID) = 0;
    virtual HRESULT WINAPI get_systemId(VARIANT *systemID) = 0;
  };
#else
  typedef struct IXMLDOMNotationVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMNotation *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMNotation *This);
      ULONG (WINAPI *Release)(IXMLDOMNotation *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMNotation *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMNotation *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMNotation *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMNotation *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMNotation *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMNotation *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMNotation *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMNotation *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMNotation *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMNotation *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMNotation *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMNotation *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMNotation *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMNotation *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMNotation *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMNotation *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMNotation *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMNotation *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMNotation *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMNotation *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMNotation *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMNotation *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMNotation *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMNotation *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMNotation *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMNotation *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMNotation *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMNotation *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMNotation *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMNotation *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMNotation *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMNotation *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMNotation *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMNotation *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMNotation *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMNotation *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMNotation *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMNotation *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMNotation *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMNotation *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_publicId)(IXMLDOMNotation *This,VARIANT *publicID);
      HRESULT (WINAPI *get_systemId)(IXMLDOMNotation *This,VARIANT *systemID);
    END_INTERFACE
  } IXMLDOMNotationVtbl;
  struct IXMLDOMNotation {
    CONST_VTBL struct IXMLDOMNotationVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMNotation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMNotation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMNotation_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMNotation_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMNotation_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMNotation_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMNotation_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMNotation_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMNotation_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMNotation_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMNotation_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMNotation_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMNotation_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMNotation_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMNotation_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMNotation_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMNotation_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMNotation_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMNotation_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMNotation_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMNotation_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMNotation_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMNotation_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMNotation_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMNotation_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMNotation_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMNotation_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMNotation_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMNotation_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMNotation_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMNotation_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMNotation_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMNotation_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMNotation_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMNotation_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMNotation_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMNotation_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMNotation_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMNotation_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMNotation_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMNotation_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMNotation_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMNotation_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMNotation_get_publicId(This,publicID) (This)->lpVtbl->get_publicId(This,publicID)
#define IXMLDOMNotation_get_systemId(This,systemID) (This)->lpVtbl->get_systemId(This,systemID)
#endif
#endif
  HRESULT WINAPI IXMLDOMNotation_get_publicId_Proxy(IXMLDOMNotation *This,VARIANT *publicID);
  void __RPC_STUB IXMLDOMNotation_get_publicId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMNotation_get_systemId_Proxy(IXMLDOMNotation *This,VARIANT *systemID);
  void __RPC_STUB IXMLDOMNotation_get_systemId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMEntity_INTERFACE_DEFINED__
#define __IXMLDOMEntity_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMEntity;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMEntity : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI get_publicId(VARIANT *publicID) = 0;
    virtual HRESULT WINAPI get_systemId(VARIANT *systemID) = 0;
    virtual HRESULT WINAPI get_notationName(BSTR *name) = 0;
  };
#else
  typedef struct IXMLDOMEntityVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMEntity *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMEntity *This);
      ULONG (WINAPI *Release)(IXMLDOMEntity *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMEntity *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMEntity *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMEntity *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMEntity *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMEntity *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMEntity *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMEntity *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMEntity *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMEntity *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMEntity *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMEntity *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMEntity *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMEntity *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMEntity *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMEntity *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMEntity *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMEntity *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMEntity *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMEntity *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMEntity *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMEntity *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMEntity *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMEntity *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMEntity *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMEntity *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMEntity *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMEntity *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMEntity *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMEntity *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMEntity *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMEntity *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMEntity *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMEntity *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMEntity *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMEntity *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMEntity *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMEntity *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMEntity *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMEntity *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMEntity *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *get_publicId)(IXMLDOMEntity *This,VARIANT *publicID);
      HRESULT (WINAPI *get_systemId)(IXMLDOMEntity *This,VARIANT *systemID);
      HRESULT (WINAPI *get_notationName)(IXMLDOMEntity *This,BSTR *name);
    END_INTERFACE
  } IXMLDOMEntityVtbl;
  struct IXMLDOMEntity {
    CONST_VTBL struct IXMLDOMEntityVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMEntity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMEntity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMEntity_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMEntity_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMEntity_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMEntity_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMEntity_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMEntity_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMEntity_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMEntity_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMEntity_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMEntity_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMEntity_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMEntity_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMEntity_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMEntity_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMEntity_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMEntity_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMEntity_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMEntity_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMEntity_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMEntity_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMEntity_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMEntity_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMEntity_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMEntity_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMEntity_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMEntity_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMEntity_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMEntity_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMEntity_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMEntity_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMEntity_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMEntity_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMEntity_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMEntity_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMEntity_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMEntity_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMEntity_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMEntity_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMEntity_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMEntity_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMEntity_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXMLDOMEntity_get_publicId(This,publicID) (This)->lpVtbl->get_publicId(This,publicID)
#define IXMLDOMEntity_get_systemId(This,systemID) (This)->lpVtbl->get_systemId(This,systemID)
#define IXMLDOMEntity_get_notationName(This,name) (This)->lpVtbl->get_notationName(This,name)
#endif
#endif
  HRESULT WINAPI IXMLDOMEntity_get_publicId_Proxy(IXMLDOMEntity *This,VARIANT *publicID);
  void __RPC_STUB IXMLDOMEntity_get_publicId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMEntity_get_systemId_Proxy(IXMLDOMEntity *This,VARIANT *systemID);
  void __RPC_STUB IXMLDOMEntity_get_systemId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMEntity_get_notationName_Proxy(IXMLDOMEntity *This,BSTR *name);
  void __RPC_STUB IXMLDOMEntity_get_notationName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDOMEntityReference_INTERFACE_DEFINED__
#define __IXMLDOMEntityReference_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMEntityReference;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMEntityReference : public IXMLDOMNode {
  };
#else
  typedef struct IXMLDOMEntityReferenceVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMEntityReference *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMEntityReference *This);
      ULONG (WINAPI *Release)(IXMLDOMEntityReference *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMEntityReference *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMEntityReference *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMEntityReference *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMEntityReference *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXMLDOMEntityReference *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXMLDOMEntityReference *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXMLDOMEntityReference *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXMLDOMEntityReference *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXMLDOMEntityReference *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXMLDOMEntityReference *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXMLDOMEntityReference *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXMLDOMEntityReference *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXMLDOMEntityReference *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXMLDOMEntityReference *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXMLDOMEntityReference *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXMLDOMEntityReference *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXMLDOMEntityReference *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXMLDOMEntityReference *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXMLDOMEntityReference *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXMLDOMEntityReference *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXMLDOMEntityReference *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXMLDOMEntityReference *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXMLDOMEntityReference *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXMLDOMEntityReference *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXMLDOMEntityReference *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXMLDOMEntityReference *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXMLDOMEntityReference *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXMLDOMEntityReference *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXMLDOMEntityReference *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXMLDOMEntityReference *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXMLDOMEntityReference *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXMLDOMEntityReference *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXMLDOMEntityReference *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXMLDOMEntityReference *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXMLDOMEntityReference *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXMLDOMEntityReference *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXMLDOMEntityReference *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXMLDOMEntityReference *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXMLDOMEntityReference *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXMLDOMEntityReference *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
    END_INTERFACE
  } IXMLDOMEntityReferenceVtbl;
  struct IXMLDOMEntityReference {
    CONST_VTBL struct IXMLDOMEntityReferenceVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMEntityReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMEntityReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMEntityReference_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMEntityReference_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMEntityReference_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMEntityReference_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMEntityReference_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMEntityReference_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMEntityReference_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMEntityReference_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMEntityReference_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMEntityReference_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMEntityReference_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMEntityReference_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMEntityReference_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMEntityReference_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMEntityReference_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMEntityReference_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMEntityReference_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMEntityReference_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMEntityReference_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMEntityReference_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMEntityReference_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMEntityReference_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMEntityReference_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMEntityReference_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMEntityReference_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMEntityReference_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMEntityReference_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMEntityReference_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMEntityReference_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMEntityReference_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMEntityReference_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMEntityReference_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMEntityReference_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMEntityReference_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXMLDOMEntityReference_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMEntityReference_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMEntityReference_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMEntityReference_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMEntityReference_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMEntityReference_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMEntityReference_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#endif
#endif
#endif

#ifndef __IXMLDOMParseError_INTERFACE_DEFINED__
#define __IXMLDOMParseError_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDOMParseError;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDOMParseError : public IDispatch {
  public:
    virtual HRESULT WINAPI get_errorCode(LONG *errorCode) = 0;
    virtual HRESULT WINAPI get_url(BSTR *urlString) = 0;
    virtual HRESULT WINAPI get_reason(BSTR *reasonString) = 0;
    virtual HRESULT WINAPI get_srcText(BSTR *sourceString) = 0;
    virtual HRESULT WINAPI get_line(LONG *lineNumber) = 0;
    virtual HRESULT WINAPI get_linepos(LONG *linePosition) = 0;
    virtual HRESULT WINAPI get_filepos(LONG *filePosition) = 0;
  };
#else
  typedef struct IXMLDOMParseErrorVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDOMParseError *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDOMParseError *This);
      ULONG (WINAPI *Release)(IXMLDOMParseError *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDOMParseError *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDOMParseError *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDOMParseError *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDOMParseError *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_errorCode)(IXMLDOMParseError *This,LONG *errorCode);
      HRESULT (WINAPI *get_url)(IXMLDOMParseError *This,BSTR *urlString);
      HRESULT (WINAPI *get_reason)(IXMLDOMParseError *This,BSTR *reasonString);
      HRESULT (WINAPI *get_srcText)(IXMLDOMParseError *This,BSTR *sourceString);
      HRESULT (WINAPI *get_line)(IXMLDOMParseError *This,LONG *lineNumber);
      HRESULT (WINAPI *get_linepos)(IXMLDOMParseError *This,LONG *linePosition);
      HRESULT (WINAPI *get_filepos)(IXMLDOMParseError *This,LONG *filePosition);
    END_INTERFACE
  } IXMLDOMParseErrorVtbl;
  struct IXMLDOMParseError {
    CONST_VTBL struct IXMLDOMParseErrorVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDOMParseError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMParseError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMParseError_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDOMParseError_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMParseError_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMParseError_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMParseError_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDOMParseError_get_errorCode(This,errorCode) (This)->lpVtbl->get_errorCode(This,errorCode)
#define IXMLDOMParseError_get_url(This,urlString) (This)->lpVtbl->get_url(This,urlString)
#define IXMLDOMParseError_get_reason(This,reasonString) (This)->lpVtbl->get_reason(This,reasonString)
#define IXMLDOMParseError_get_srcText(This,sourceString) (This)->lpVtbl->get_srcText(This,sourceString)
#define IXMLDOMParseError_get_line(This,lineNumber) (This)->lpVtbl->get_line(This,lineNumber)
#define IXMLDOMParseError_get_linepos(This,linePosition) (This)->lpVtbl->get_linepos(This,linePosition)
#define IXMLDOMParseError_get_filepos(This,filePosition) (This)->lpVtbl->get_filepos(This,filePosition)
#endif
#endif
  HRESULT WINAPI IXMLDOMParseError_get_errorCode_Proxy(IXMLDOMParseError *This,LONG *errorCode);
  void __RPC_STUB IXMLDOMParseError_get_errorCode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMParseError_get_url_Proxy(IXMLDOMParseError *This,BSTR *urlString);
  void __RPC_STUB IXMLDOMParseError_get_url_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMParseError_get_reason_Proxy(IXMLDOMParseError *This,BSTR *reasonString);
  void __RPC_STUB IXMLDOMParseError_get_reason_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMParseError_get_srcText_Proxy(IXMLDOMParseError *This,BSTR *sourceString);
  void __RPC_STUB IXMLDOMParseError_get_srcText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMParseError_get_line_Proxy(IXMLDOMParseError *This,LONG *lineNumber);
  void __RPC_STUB IXMLDOMParseError_get_line_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMParseError_get_linepos_Proxy(IXMLDOMParseError *This,LONG *linePosition);
  void __RPC_STUB IXMLDOMParseError_get_linepos_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDOMParseError_get_filepos_Proxy(IXMLDOMParseError *This,LONG *filePosition);
  void __RPC_STUB IXMLDOMParseError_get_filepos_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXTLRuntime_INTERFACE_DEFINED__
#define __IXTLRuntime_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXTLRuntime;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXTLRuntime : public IXMLDOMNode {
  public:
    virtual HRESULT WINAPI uniqueID(IXMLDOMNode *pNode,LONG *pID) = 0;
    virtual HRESULT WINAPI depth(IXMLDOMNode *pNode,LONG *pDepth) = 0;
    virtual HRESULT WINAPI childNumber(IXMLDOMNode *pNode,LONG *pNumber) = 0;
    virtual HRESULT WINAPI ancestorChildNumber(BSTR bstrNodeName,IXMLDOMNode *pNode,LONG *pNumber) = 0;
    virtual HRESULT WINAPI absoluteChildNumber(IXMLDOMNode *pNode,LONG *pNumber) = 0;
    virtual HRESULT WINAPI formatIndex(LONG lIndex,BSTR bstrFormat,BSTR *pbstrFormattedString) = 0;
    virtual HRESULT WINAPI formatNumber(double dblNumber,BSTR bstrFormat,BSTR *pbstrFormattedString) = 0;
    virtual HRESULT WINAPI formatDate(VARIANT varDate,BSTR bstrFormat,VARIANT varDestLocale,BSTR *pbstrFormattedString) = 0;
    virtual HRESULT WINAPI formatTime(VARIANT varTime,BSTR bstrFormat,VARIANT varDestLocale,BSTR *pbstrFormattedString) = 0;
  };
#else
  typedef struct IXTLRuntimeVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXTLRuntime *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXTLRuntime *This);
      ULONG (WINAPI *Release)(IXTLRuntime *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXTLRuntime *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXTLRuntime *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXTLRuntime *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXTLRuntime *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_nodeName)(IXTLRuntime *This,BSTR *name);
      HRESULT (WINAPI *get_nodeValue)(IXTLRuntime *This,VARIANT *value);
      HRESULT (WINAPI *put_nodeValue)(IXTLRuntime *This,VARIANT value);
      HRESULT (WINAPI *get_nodeType)(IXTLRuntime *This,DOMNodeType *type);
      HRESULT (WINAPI *get_parentNode)(IXTLRuntime *This,IXMLDOMNode **parent);
      HRESULT (WINAPI *get_childNodes)(IXTLRuntime *This,IXMLDOMNodeList **childList);
      HRESULT (WINAPI *get_firstChild)(IXTLRuntime *This,IXMLDOMNode **firstChild);
      HRESULT (WINAPI *get_lastChild)(IXTLRuntime *This,IXMLDOMNode **lastChild);
      HRESULT (WINAPI *get_previousSibling)(IXTLRuntime *This,IXMLDOMNode **previousSibling);
      HRESULT (WINAPI *get_nextSibling)(IXTLRuntime *This,IXMLDOMNode **nextSibling);
      HRESULT (WINAPI *get_attributes)(IXTLRuntime *This,IXMLDOMNamedNodeMap **attributeMap);
      HRESULT (WINAPI *insertBefore)(IXTLRuntime *This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *replaceChild)(IXTLRuntime *This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild);
      HRESULT (WINAPI *removeChild)(IXTLRuntime *This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild);
      HRESULT (WINAPI *appendChild)(IXTLRuntime *This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild);
      HRESULT (WINAPI *hasChildNodes)(IXTLRuntime *This,VARIANT_BOOL *hasChild);
      HRESULT (WINAPI *get_ownerDocument)(IXTLRuntime *This,IXMLDOMDocument **DOMDocument);
      HRESULT (WINAPI *cloneNode)(IXTLRuntime *This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot);
      HRESULT (WINAPI *get_nodeTypeString)(IXTLRuntime *This,BSTR *nodeType);
      HRESULT (WINAPI *get_text)(IXTLRuntime *This,BSTR *text);
      HRESULT (WINAPI *put_text)(IXTLRuntime *This,BSTR text);
      HRESULT (WINAPI *get_specified)(IXTLRuntime *This,VARIANT_BOOL *isSpecified);
      HRESULT (WINAPI *get_definition)(IXTLRuntime *This,IXMLDOMNode **definitionNode);
      HRESULT (WINAPI *get_nodeTypedValue)(IXTLRuntime *This,VARIANT *typedValue);
      HRESULT (WINAPI *put_nodeTypedValue)(IXTLRuntime *This,VARIANT typedValue);
      HRESULT (WINAPI *get_dataType)(IXTLRuntime *This,VARIANT *dataTypeName);
      HRESULT (WINAPI *put_dataType)(IXTLRuntime *This,BSTR dataTypeName);
      HRESULT (WINAPI *get_xml)(IXTLRuntime *This,BSTR *xmlString);
      HRESULT (WINAPI *transformNode)(IXTLRuntime *This,IXMLDOMNode *stylesheet,BSTR *xmlString);
      HRESULT (WINAPI *selectNodes)(IXTLRuntime *This,BSTR queryString,IXMLDOMNodeList **resultList);
      HRESULT (WINAPI *selectSingleNode)(IXTLRuntime *This,BSTR queryString,IXMLDOMNode **resultNode);
      HRESULT (WINAPI *get_parsed)(IXTLRuntime *This,VARIANT_BOOL *isParsed);
      HRESULT (WINAPI *get_namespaceURI)(IXTLRuntime *This,BSTR *namespaceURI);
      HRESULT (WINAPI *get_prefix)(IXTLRuntime *This,BSTR *prefixString);
      HRESULT (WINAPI *get_baseName)(IXTLRuntime *This,BSTR *nameString);
      HRESULT (WINAPI *transformNodeToObject)(IXTLRuntime *This,IXMLDOMNode *stylesheet,VARIANT outputObject);
      HRESULT (WINAPI *uniqueID)(IXTLRuntime *This,IXMLDOMNode *pNode,LONG *pID);
      HRESULT (WINAPI *depth)(IXTLRuntime *This,IXMLDOMNode *pNode,LONG *pDepth);
      HRESULT (WINAPI *childNumber)(IXTLRuntime *This,IXMLDOMNode *pNode,LONG *pNumber);
      HRESULT (WINAPI *ancestorChildNumber)(IXTLRuntime *This,BSTR bstrNodeName,IXMLDOMNode *pNode,LONG *pNumber);
      HRESULT (WINAPI *absoluteChildNumber)(IXTLRuntime *This,IXMLDOMNode *pNode,LONG *pNumber);
      HRESULT (WINAPI *formatIndex)(IXTLRuntime *This,LONG lIndex,BSTR bstrFormat,BSTR *pbstrFormattedString);
      HRESULT (WINAPI *formatNumber)(IXTLRuntime *This,double dblNumber,BSTR bstrFormat,BSTR *pbstrFormattedString);
      HRESULT (WINAPI *formatDate)(IXTLRuntime *This,VARIANT varDate,BSTR bstrFormat,VARIANT varDestLocale,BSTR *pbstrFormattedString);
      HRESULT (WINAPI *formatTime)(IXTLRuntime *This,VARIANT varTime,BSTR bstrFormat,VARIANT varDestLocale,BSTR *pbstrFormattedString);
    END_INTERFACE
  } IXTLRuntimeVtbl;
  struct IXTLRuntime {
    CONST_VTBL struct IXTLRuntimeVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXTLRuntime_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXTLRuntime_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXTLRuntime_Release(This) (This)->lpVtbl->Release(This)
#define IXTLRuntime_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXTLRuntime_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXTLRuntime_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXTLRuntime_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXTLRuntime_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXTLRuntime_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXTLRuntime_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXTLRuntime_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXTLRuntime_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXTLRuntime_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXTLRuntime_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXTLRuntime_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXTLRuntime_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXTLRuntime_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXTLRuntime_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXTLRuntime_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXTLRuntime_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXTLRuntime_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXTLRuntime_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXTLRuntime_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXTLRuntime_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXTLRuntime_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXTLRuntime_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXTLRuntime_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXTLRuntime_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXTLRuntime_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXTLRuntime_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXTLRuntime_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXTLRuntime_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXTLRuntime_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXTLRuntime_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXTLRuntime_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXTLRuntime_transformNode(This,stylesheet,xmlString) (This)->lpVtbl->transformNode(This,stylesheet,xmlString)
#define IXTLRuntime_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXTLRuntime_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXTLRuntime_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXTLRuntime_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXTLRuntime_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXTLRuntime_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXTLRuntime_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#define IXTLRuntime_uniqueID(This,pNode,pID) (This)->lpVtbl->uniqueID(This,pNode,pID)
#define IXTLRuntime_depth(This,pNode,pDepth) (This)->lpVtbl->depth(This,pNode,pDepth)
#define IXTLRuntime_childNumber(This,pNode,pNumber) (This)->lpVtbl->childNumber(This,pNode,pNumber)
#define IXTLRuntime_ancestorChildNumber(This,bstrNodeName,pNode,pNumber) (This)->lpVtbl->ancestorChildNumber(This,bstrNodeName,pNode,pNumber)
#define IXTLRuntime_absoluteChildNumber(This,pNode,pNumber) (This)->lpVtbl->absoluteChildNumber(This,pNode,pNumber)
#define IXTLRuntime_formatIndex(This,lIndex,bstrFormat,pbstrFormattedString) (This)->lpVtbl->formatIndex(This,lIndex,bstrFormat,pbstrFormattedString)
#define IXTLRuntime_formatNumber(This,dblNumber,bstrFormat,pbstrFormattedString) (This)->lpVtbl->formatNumber(This,dblNumber,bstrFormat,pbstrFormattedString)
#define IXTLRuntime_formatDate(This,varDate,bstrFormat,varDestLocale,pbstrFormattedString) (This)->lpVtbl->formatDate(This,varDate,bstrFormat,varDestLocale,pbstrFormattedString)
#define IXTLRuntime_formatTime(This,varTime,bstrFormat,varDestLocale,pbstrFormattedString) (This)->lpVtbl->formatTime(This,varTime,bstrFormat,varDestLocale,pbstrFormattedString)
#endif
#endif
  HRESULT WINAPI IXTLRuntime_uniqueID_Proxy(IXTLRuntime *This,IXMLDOMNode *pNode,LONG *pID);
  void __RPC_STUB IXTLRuntime_uniqueID_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXTLRuntime_depth_Proxy(IXTLRuntime *This,IXMLDOMNode *pNode,LONG *pDepth);
  void __RPC_STUB IXTLRuntime_depth_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXTLRuntime_childNumber_Proxy(IXTLRuntime *This,IXMLDOMNode *pNode,LONG *pNumber);
  void __RPC_STUB IXTLRuntime_childNumber_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXTLRuntime_ancestorChildNumber_Proxy(IXTLRuntime *This,BSTR bstrNodeName,IXMLDOMNode *pNode,LONG *pNumber);
  void __RPC_STUB IXTLRuntime_ancestorChildNumber_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXTLRuntime_absoluteChildNumber_Proxy(IXTLRuntime *This,IXMLDOMNode *pNode,LONG *pNumber);
  void __RPC_STUB IXTLRuntime_absoluteChildNumber_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXTLRuntime_formatIndex_Proxy(IXTLRuntime *This,LONG lIndex,BSTR bstrFormat,BSTR *pbstrFormattedString);
  void __RPC_STUB IXTLRuntime_formatIndex_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXTLRuntime_formatNumber_Proxy(IXTLRuntime *This,double dblNumber,BSTR bstrFormat,BSTR *pbstrFormattedString);
  void __RPC_STUB IXTLRuntime_formatNumber_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXTLRuntime_formatDate_Proxy(IXTLRuntime *This,VARIANT varDate,BSTR bstrFormat,VARIANT varDestLocale,BSTR *pbstrFormattedString);
  void __RPC_STUB IXTLRuntime_formatDate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXTLRuntime_formatTime_Proxy(IXTLRuntime *This,VARIANT varTime,BSTR bstrFormat,VARIANT varDestLocale,BSTR *pbstrFormattedString);
  void __RPC_STUB IXTLRuntime_formatTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __XMLDOMDocumentEvents_DISPINTERFACE_DEFINED__
#define __XMLDOMDocumentEvents_DISPINTERFACE_DEFINED__
  EXTERN_C const IID DIID_XMLDOMDocumentEvents;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct XMLDOMDocumentEvents : public IDispatch {
  };
#else
  typedef struct XMLDOMDocumentEventsVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(XMLDOMDocumentEvents *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(XMLDOMDocumentEvents *This);
      ULONG (WINAPI *Release)(XMLDOMDocumentEvents *This);
      HRESULT (WINAPI *GetTypeInfoCount)(XMLDOMDocumentEvents *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(XMLDOMDocumentEvents *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(XMLDOMDocumentEvents *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(XMLDOMDocumentEvents *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
    END_INTERFACE
  } XMLDOMDocumentEventsVtbl;
  struct XMLDOMDocumentEvents {
    CONST_VTBL struct XMLDOMDocumentEventsVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define XMLDOMDocumentEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define XMLDOMDocumentEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define XMLDOMDocumentEvents_Release(This) (This)->lpVtbl->Release(This)
#define XMLDOMDocumentEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define XMLDOMDocumentEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define XMLDOMDocumentEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define XMLDOMDocumentEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#endif
#endif
#endif

  EXTERN_C const CLSID CLSID_DOMDocument;
#ifdef __cplusplus
  class DOMDocument;
#endif
  EXTERN_C const CLSID CLSID_DOMFreeThreadedDocument;
#ifdef __cplusplus
  class DOMFreeThreadedDocument;
#endif

#ifndef __IXMLHttpRequest_INTERFACE_DEFINED__
#define __IXMLHttpRequest_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLHttpRequest;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLHttpRequest : public IDispatch {
  public:
    virtual HRESULT WINAPI open(BSTR bstrMethod,BSTR bstrUrl,VARIANT varAsync,VARIANT bstrUser,VARIANT bstrPassword) = 0;
    virtual HRESULT WINAPI setRequestHeader(BSTR bstrHeader,BSTR bstrValue) = 0;
    virtual HRESULT WINAPI getResponseHeader(BSTR bstrHeader,BSTR *pbstrValue) = 0;
    virtual HRESULT WINAPI getAllResponseHeaders(BSTR *pbstrHeaders) = 0;
    virtual HRESULT WINAPI send(VARIANT varBody) = 0;
    virtual HRESULT WINAPI abort(void) = 0;
    virtual HRESULT WINAPI get_status(LONG *plStatus) = 0;
    virtual HRESULT WINAPI get_statusText(BSTR *pbstrStatus) = 0;
    virtual HRESULT WINAPI get_responseXML(IDispatch **ppBody) = 0;
    virtual HRESULT WINAPI get_responseText(BSTR *pbstrBody) = 0;
    virtual HRESULT WINAPI get_responseBody(VARIANT *pvarBody) = 0;
    virtual HRESULT WINAPI get_responseStream(VARIANT *pvarBody) = 0;
    virtual HRESULT WINAPI get_readyState(LONG *plState) = 0;
    virtual HRESULT WINAPI put_onreadystatechange(IDispatch *pReadyStateSink) = 0;
  };
#else
  typedef struct IXMLHttpRequestVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLHttpRequest *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLHttpRequest *This);
      ULONG (WINAPI *Release)(IXMLHttpRequest *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLHttpRequest *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLHttpRequest *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLHttpRequest *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLHttpRequest *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *open)(IXMLHttpRequest *This,BSTR bstrMethod,BSTR bstrUrl,VARIANT varAsync,VARIANT bstrUser,VARIANT bstrPassword);
      HRESULT (WINAPI *setRequestHeader)(IXMLHttpRequest *This,BSTR bstrHeader,BSTR bstrValue);
      HRESULT (WINAPI *getResponseHeader)(IXMLHttpRequest *This,BSTR bstrHeader,BSTR *pbstrValue);
      HRESULT (WINAPI *getAllResponseHeaders)(IXMLHttpRequest *This,BSTR *pbstrHeaders);
      HRESULT (WINAPI *send)(IXMLHttpRequest *This,VARIANT varBody);
      HRESULT (WINAPI *abort)(IXMLHttpRequest *This);
      HRESULT (WINAPI *get_status)(IXMLHttpRequest *This,LONG *plStatus);
      HRESULT (WINAPI *get_statusText)(IXMLHttpRequest *This,BSTR *pbstrStatus);
      HRESULT (WINAPI *get_responseXML)(IXMLHttpRequest *This,IDispatch **ppBody);
      HRESULT (WINAPI *get_responseText)(IXMLHttpRequest *This,BSTR *pbstrBody);
      HRESULT (WINAPI *get_responseBody)(IXMLHttpRequest *This,VARIANT *pvarBody);
      HRESULT (WINAPI *get_responseStream)(IXMLHttpRequest *This,VARIANT *pvarBody);
      HRESULT (WINAPI *get_readyState)(IXMLHttpRequest *This,LONG *plState);
      HRESULT (WINAPI *put_onreadystatechange)(IXMLHttpRequest *This,IDispatch *pReadyStateSink);
    END_INTERFACE
  } IXMLHttpRequestVtbl;
  struct IXMLHttpRequest {
    CONST_VTBL struct IXMLHttpRequestVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLHttpRequest_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLHttpRequest_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLHttpRequest_Release(This) (This)->lpVtbl->Release(This)
#define IXMLHttpRequest_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLHttpRequest_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLHttpRequest_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLHttpRequest_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLHttpRequest_open(This,bstrMethod,bstrUrl,varAsync,bstrUser,bstrPassword) (This)->lpVtbl->open(This,bstrMethod,bstrUrl,varAsync,bstrUser,bstrPassword)
#define IXMLHttpRequest_setRequestHeader(This,bstrHeader,bstrValue) (This)->lpVtbl->setRequestHeader(This,bstrHeader,bstrValue)
#define IXMLHttpRequest_getResponseHeader(This,bstrHeader,pbstrValue) (This)->lpVtbl->getResponseHeader(This,bstrHeader,pbstrValue)
#define IXMLHttpRequest_getAllResponseHeaders(This,pbstrHeaders) (This)->lpVtbl->getAllResponseHeaders(This,pbstrHeaders)
#define IXMLHttpRequest_send(This,varBody) (This)->lpVtbl->send(This,varBody)
#define IXMLHttpRequest_abort(This) (This)->lpVtbl->abort(This)
#define IXMLHttpRequest_get_status(This,plStatus) (This)->lpVtbl->get_status(This,plStatus)
#define IXMLHttpRequest_get_statusText(This,pbstrStatus) (This)->lpVtbl->get_statusText(This,pbstrStatus)
#define IXMLHttpRequest_get_responseXML(This,ppBody) (This)->lpVtbl->get_responseXML(This,ppBody)
#define IXMLHttpRequest_get_responseText(This,pbstrBody) (This)->lpVtbl->get_responseText(This,pbstrBody)
#define IXMLHttpRequest_get_responseBody(This,pvarBody) (This)->lpVtbl->get_responseBody(This,pvarBody)
#define IXMLHttpRequest_get_responseStream(This,pvarBody) (This)->lpVtbl->get_responseStream(This,pvarBody)
#define IXMLHttpRequest_get_readyState(This,plState) (This)->lpVtbl->get_readyState(This,plState)
#define IXMLHttpRequest_put_onreadystatechange(This,pReadyStateSink) (This)->lpVtbl->put_onreadystatechange(This,pReadyStateSink)
#endif
#endif
  HRESULT WINAPI IXMLHttpRequest_open_Proxy(IXMLHttpRequest *This,BSTR bstrMethod,BSTR bstrUrl,VARIANT varAsync,VARIANT bstrUser,VARIANT bstrPassword);
  void __RPC_STUB IXMLHttpRequest_open_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_setRequestHeader_Proxy(IXMLHttpRequest *This,BSTR bstrHeader,BSTR bstrValue);
  void __RPC_STUB IXMLHttpRequest_setRequestHeader_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_getResponseHeader_Proxy(IXMLHttpRequest *This,BSTR bstrHeader,BSTR *pbstrValue);
  void __RPC_STUB IXMLHttpRequest_getResponseHeader_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_getAllResponseHeaders_Proxy(IXMLHttpRequest *This,BSTR *pbstrHeaders);
  void __RPC_STUB IXMLHttpRequest_getAllResponseHeaders_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_send_Proxy(IXMLHttpRequest *This,VARIANT varBody);
  void __RPC_STUB IXMLHttpRequest_send_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_abort_Proxy(IXMLHttpRequest *This);
  void __RPC_STUB IXMLHttpRequest_abort_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_get_status_Proxy(IXMLHttpRequest *This,LONG *plStatus);
  void __RPC_STUB IXMLHttpRequest_get_status_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_get_statusText_Proxy(IXMLHttpRequest *This,BSTR *pbstrStatus);
  void __RPC_STUB IXMLHttpRequest_get_statusText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_get_responseXML_Proxy(IXMLHttpRequest *This,IDispatch **ppBody);
  void __RPC_STUB IXMLHttpRequest_get_responseXML_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_get_responseText_Proxy(IXMLHttpRequest *This,BSTR *pbstrBody);
  void __RPC_STUB IXMLHttpRequest_get_responseText_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_get_responseBody_Proxy(IXMLHttpRequest *This,VARIANT *pvarBody);
  void __RPC_STUB IXMLHttpRequest_get_responseBody_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_get_responseStream_Proxy(IXMLHttpRequest *This,VARIANT *pvarBody);
  void __RPC_STUB IXMLHttpRequest_get_responseStream_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_get_readyState_Proxy(IXMLHttpRequest *This,LONG *plState);
  void __RPC_STUB IXMLHttpRequest_get_readyState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLHttpRequest_put_onreadystatechange_Proxy(IXMLHttpRequest *This,IDispatch *pReadyStateSink);
  void __RPC_STUB IXMLHttpRequest_put_onreadystatechange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_XMLHTTPRequest;
#ifdef __cplusplus
  class XMLHTTPRequest;
#endif

#ifndef __IXMLDSOControl_INTERFACE_DEFINED__
#define __IXMLDSOControl_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDSOControl;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDSOControl : public IDispatch {
  public:
    virtual HRESULT WINAPI get_XMLDocument(IXMLDOMDocument **ppDoc) = 0;
    virtual HRESULT WINAPI put_XMLDocument(IXMLDOMDocument *ppDoc) = 0;
    virtual HRESULT WINAPI get_JavaDSOCompatible(WINBOOL *fJavaDSOCompatible) = 0;
    virtual HRESULT WINAPI put_JavaDSOCompatible(WINBOOL fJavaDSOCompatible) = 0;
    virtual HRESULT WINAPI get_readyState(LONG *state) = 0;
  };
#else
  typedef struct IXMLDSOControlVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDSOControl *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDSOControl *This);
      ULONG (WINAPI *Release)(IXMLDSOControl *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDSOControl *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDSOControl *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDSOControl *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDSOControl *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_XMLDocument)(IXMLDSOControl *This,IXMLDOMDocument **ppDoc);
      HRESULT (WINAPI *put_XMLDocument)(IXMLDSOControl *This,IXMLDOMDocument *ppDoc);
      HRESULT (WINAPI *get_JavaDSOCompatible)(IXMLDSOControl *This,WINBOOL *fJavaDSOCompatible);
      HRESULT (WINAPI *put_JavaDSOCompatible)(IXMLDSOControl *This,WINBOOL fJavaDSOCompatible);
      HRESULT (WINAPI *get_readyState)(IXMLDSOControl *This,LONG *state);
    END_INTERFACE
  } IXMLDSOControlVtbl;
  struct IXMLDSOControl {
    CONST_VTBL struct IXMLDSOControlVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDSOControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDSOControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDSOControl_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDSOControl_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDSOControl_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDSOControl_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDSOControl_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDSOControl_get_XMLDocument(This,ppDoc) (This)->lpVtbl->get_XMLDocument(This,ppDoc)
#define IXMLDSOControl_put_XMLDocument(This,ppDoc) (This)->lpVtbl->put_XMLDocument(This,ppDoc)
#define IXMLDSOControl_get_JavaDSOCompatible(This,fJavaDSOCompatible) (This)->lpVtbl->get_JavaDSOCompatible(This,fJavaDSOCompatible)
#define IXMLDSOControl_put_JavaDSOCompatible(This,fJavaDSOCompatible) (This)->lpVtbl->put_JavaDSOCompatible(This,fJavaDSOCompatible)
#define IXMLDSOControl_get_readyState(This,state) (This)->lpVtbl->get_readyState(This,state)
#endif
#endif
  HRESULT WINAPI IXMLDSOControl_get_XMLDocument_Proxy(IXMLDSOControl *This,IXMLDOMDocument **ppDoc);
  void __RPC_STUB IXMLDSOControl_get_XMLDocument_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDSOControl_put_XMLDocument_Proxy(IXMLDSOControl *This,IXMLDOMDocument *ppDoc);
  void __RPC_STUB IXMLDSOControl_put_XMLDocument_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDSOControl_get_JavaDSOCompatible_Proxy(IXMLDSOControl *This,WINBOOL *fJavaDSOCompatible);
  void __RPC_STUB IXMLDSOControl_get_JavaDSOCompatible_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDSOControl_put_JavaDSOCompatible_Proxy(IXMLDSOControl *This,WINBOOL fJavaDSOCompatible);
  void __RPC_STUB IXMLDSOControl_put_JavaDSOCompatible_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDSOControl_get_readyState_Proxy(IXMLDSOControl *This,LONG *state);
  void __RPC_STUB IXMLDSOControl_get_readyState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_XMLDSOControl;
#ifdef __cplusplus
  class XMLDSOControl;
#endif

#ifndef __IXMLElementCollection_INTERFACE_DEFINED__
#define __IXMLElementCollection_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLElementCollection;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLElementCollection : public IDispatch {
  public:
    virtual HRESULT WINAPI put_length(LONG v) = 0;
    virtual HRESULT WINAPI get_length(LONG *p) = 0;
    virtual HRESULT WINAPI get__newEnum(IUnknown **ppUnk) = 0;
    virtual HRESULT WINAPI item(VARIANT var1,VARIANT var2,IDispatch **ppDisp) = 0;
  };
#else
  typedef struct IXMLElementCollectionVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLElementCollection *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLElementCollection *This);
      ULONG (WINAPI *Release)(IXMLElementCollection *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLElementCollection *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLElementCollection *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLElementCollection *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLElementCollection *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *put_length)(IXMLElementCollection *This,LONG v);
      HRESULT (WINAPI *get_length)(IXMLElementCollection *This,LONG *p);
      HRESULT (WINAPI *get__newEnum)(IXMLElementCollection *This,IUnknown **ppUnk);
      HRESULT (WINAPI *item)(IXMLElementCollection *This,VARIANT var1,VARIANT var2,IDispatch **ppDisp);
    END_INTERFACE
  } IXMLElementCollectionVtbl;
  struct IXMLElementCollection {
    CONST_VTBL struct IXMLElementCollectionVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLElementCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLElementCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLElementCollection_Release(This) (This)->lpVtbl->Release(This)
#define IXMLElementCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLElementCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLElementCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLElementCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLElementCollection_put_length(This,v) (This)->lpVtbl->put_length(This,v)
#define IXMLElementCollection_get_length(This,p) (This)->lpVtbl->get_length(This,p)
#define IXMLElementCollection_get__newEnum(This,ppUnk) (This)->lpVtbl->get__newEnum(This,ppUnk)
#define IXMLElementCollection_item(This,var1,var2,ppDisp) (This)->lpVtbl->item(This,var1,var2,ppDisp)
#endif
#endif
  HRESULT WINAPI IXMLElementCollection_put_length_Proxy(IXMLElementCollection *This,LONG v);
  void __RPC_STUB IXMLElementCollection_put_length_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElementCollection_get_length_Proxy(IXMLElementCollection *This,LONG *p);
  void __RPC_STUB IXMLElementCollection_get_length_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElementCollection_get__newEnum_Proxy(IXMLElementCollection *This,IUnknown **ppUnk);
  void __RPC_STUB IXMLElementCollection_get__newEnum_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElementCollection_item_Proxy(IXMLElementCollection *This,VARIANT var1,VARIANT var2,IDispatch **ppDisp);
  void __RPC_STUB IXMLElementCollection_item_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDocument_INTERFACE_DEFINED__
#define __IXMLDocument_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDocument;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDocument : public IDispatch {
  public:
    virtual HRESULT WINAPI get_root(IXMLElement **p) = 0;
    virtual HRESULT WINAPI get_fileSize(BSTR *p) = 0;
    virtual HRESULT WINAPI get_fileModifiedDate(BSTR *p) = 0;
    virtual HRESULT WINAPI get_fileUpdatedDate(BSTR *p) = 0;
    virtual HRESULT WINAPI get_URL(BSTR *p) = 0;
    virtual HRESULT WINAPI put_URL(BSTR p) = 0;
    virtual HRESULT WINAPI get_mimeType(BSTR *p) = 0;
    virtual HRESULT WINAPI get_readyState(LONG *pl) = 0;
    virtual HRESULT WINAPI get_charset(BSTR *p) = 0;
    virtual HRESULT WINAPI put_charset(BSTR p) = 0;
    virtual HRESULT WINAPI get_version(BSTR *p) = 0;
    virtual HRESULT WINAPI get_doctype(BSTR *p) = 0;
    virtual HRESULT WINAPI get_dtdURL(BSTR *p) = 0;
    virtual HRESULT WINAPI createElement(VARIANT vType,VARIANT var1,IXMLElement **ppElem) = 0;
  };
#else
  typedef struct IXMLDocumentVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDocument *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDocument *This);
      ULONG (WINAPI *Release)(IXMLDocument *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDocument *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDocument *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDocument *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDocument *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_root)(IXMLDocument *This,IXMLElement **p);
      HRESULT (WINAPI *get_fileSize)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *get_fileModifiedDate)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *get_fileUpdatedDate)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *get_URL)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *put_URL)(IXMLDocument *This,BSTR p);
      HRESULT (WINAPI *get_mimeType)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *get_readyState)(IXMLDocument *This,LONG *pl);
      HRESULT (WINAPI *get_charset)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *put_charset)(IXMLDocument *This,BSTR p);
      HRESULT (WINAPI *get_version)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *get_doctype)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *get_dtdURL)(IXMLDocument *This,BSTR *p);
      HRESULT (WINAPI *createElement)(IXMLDocument *This,VARIANT vType,VARIANT var1,IXMLElement **ppElem);
    END_INTERFACE
  } IXMLDocumentVtbl;
  struct IXMLDocument {
    CONST_VTBL struct IXMLDocumentVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDocument_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDocument_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDocument_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDocument_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDocument_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDocument_get_root(This,p) (This)->lpVtbl->get_root(This,p)
#define IXMLDocument_get_fileSize(This,p) (This)->lpVtbl->get_fileSize(This,p)
#define IXMLDocument_get_fileModifiedDate(This,p) (This)->lpVtbl->get_fileModifiedDate(This,p)
#define IXMLDocument_get_fileUpdatedDate(This,p) (This)->lpVtbl->get_fileUpdatedDate(This,p)
#define IXMLDocument_get_URL(This,p) (This)->lpVtbl->get_URL(This,p)
#define IXMLDocument_put_URL(This,p) (This)->lpVtbl->put_URL(This,p)
#define IXMLDocument_get_mimeType(This,p) (This)->lpVtbl->get_mimeType(This,p)
#define IXMLDocument_get_readyState(This,pl) (This)->lpVtbl->get_readyState(This,pl)
#define IXMLDocument_get_charset(This,p) (This)->lpVtbl->get_charset(This,p)
#define IXMLDocument_put_charset(This,p) (This)->lpVtbl->put_charset(This,p)
#define IXMLDocument_get_version(This,p) (This)->lpVtbl->get_version(This,p)
#define IXMLDocument_get_doctype(This,p) (This)->lpVtbl->get_doctype(This,p)
#define IXMLDocument_get_dtdURL(This,p) (This)->lpVtbl->get_dtdURL(This,p)
#define IXMLDocument_createElement(This,vType,var1,ppElem) (This)->lpVtbl->createElement(This,vType,var1,ppElem)
#endif
#endif
  HRESULT WINAPI IXMLDocument_get_root_Proxy(IXMLDocument *This,IXMLElement **p);
  void __RPC_STUB IXMLDocument_get_root_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_fileSize_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_fileSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_fileModifiedDate_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_fileModifiedDate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_fileUpdatedDate_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_fileUpdatedDate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_URL_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_URL_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_put_URL_Proxy(IXMLDocument *This,BSTR p);
  void __RPC_STUB IXMLDocument_put_URL_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_mimeType_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_mimeType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_readyState_Proxy(IXMLDocument *This,LONG *pl);
  void __RPC_STUB IXMLDocument_get_readyState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_charset_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_charset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_put_charset_Proxy(IXMLDocument *This,BSTR p);
  void __RPC_STUB IXMLDocument_put_charset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_version_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_version_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_doctype_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_doctype_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_get_dtdURL_Proxy(IXMLDocument *This,BSTR *p);
  void __RPC_STUB IXMLDocument_get_dtdURL_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument_createElement_Proxy(IXMLDocument *This,VARIANT vType,VARIANT var1,IXMLElement **ppElem);
  void __RPC_STUB IXMLDocument_createElement_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLDocument2_INTERFACE_DEFINED__
#define __IXMLDocument2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLDocument2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLDocument2 : public IDispatch {
  public:
    virtual HRESULT WINAPI get_root(IXMLElement2 **p) = 0;
    virtual HRESULT WINAPI get_fileSize(BSTR *p) = 0;
    virtual HRESULT WINAPI get_fileModifiedDate(BSTR *p) = 0;
    virtual HRESULT WINAPI get_fileUpdatedDate(BSTR *p) = 0;
    virtual HRESULT WINAPI get_URL(BSTR *p) = 0;
    virtual HRESULT WINAPI put_URL(BSTR p) = 0;
    virtual HRESULT WINAPI get_mimeType(BSTR *p) = 0;
    virtual HRESULT WINAPI get_readyState(LONG *pl) = 0;
    virtual HRESULT WINAPI get_charset(BSTR *p) = 0;
    virtual HRESULT WINAPI put_charset(BSTR p) = 0;
    virtual HRESULT WINAPI get_version(BSTR *p) = 0;
    virtual HRESULT WINAPI get_doctype(BSTR *p) = 0;
    virtual HRESULT WINAPI get_dtdURL(BSTR *p) = 0;
    virtual HRESULT WINAPI createElement(VARIANT vType,VARIANT var1,IXMLElement2 **ppElem) = 0;
    virtual HRESULT WINAPI get_async(VARIANT_BOOL *pf) = 0;
    virtual HRESULT WINAPI put_async(VARIANT_BOOL f) = 0;
  };
#else
  typedef struct IXMLDocument2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLDocument2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLDocument2 *This);
      ULONG (WINAPI *Release)(IXMLDocument2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLDocument2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLDocument2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLDocument2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLDocument2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_root)(IXMLDocument2 *This,IXMLElement2 **p);
      HRESULT (WINAPI *get_fileSize)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *get_fileModifiedDate)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *get_fileUpdatedDate)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *get_URL)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *put_URL)(IXMLDocument2 *This,BSTR p);
      HRESULT (WINAPI *get_mimeType)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *get_readyState)(IXMLDocument2 *This,LONG *pl);
      HRESULT (WINAPI *get_charset)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *put_charset)(IXMLDocument2 *This,BSTR p);
      HRESULT (WINAPI *get_version)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *get_doctype)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *get_dtdURL)(IXMLDocument2 *This,BSTR *p);
      HRESULT (WINAPI *createElement)(IXMLDocument2 *This,VARIANT vType,VARIANT var1,IXMLElement2 **ppElem);
      HRESULT (WINAPI *get_async)(IXMLDocument2 *This,VARIANT_BOOL *pf);
      HRESULT (WINAPI *put_async)(IXMLDocument2 *This,VARIANT_BOOL f);
    END_INTERFACE
  } IXMLDocument2Vtbl;
  struct IXMLDocument2 {
    CONST_VTBL struct IXMLDocument2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLDocument2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDocument2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDocument2_Release(This) (This)->lpVtbl->Release(This)
#define IXMLDocument2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDocument2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDocument2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDocument2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLDocument2_get_root(This,p) (This)->lpVtbl->get_root(This,p)
#define IXMLDocument2_get_fileSize(This,p) (This)->lpVtbl->get_fileSize(This,p)
#define IXMLDocument2_get_fileModifiedDate(This,p) (This)->lpVtbl->get_fileModifiedDate(This,p)
#define IXMLDocument2_get_fileUpdatedDate(This,p) (This)->lpVtbl->get_fileUpdatedDate(This,p)
#define IXMLDocument2_get_URL(This,p) (This)->lpVtbl->get_URL(This,p)
#define IXMLDocument2_put_URL(This,p) (This)->lpVtbl->put_URL(This,p)
#define IXMLDocument2_get_mimeType(This,p) (This)->lpVtbl->get_mimeType(This,p)
#define IXMLDocument2_get_readyState(This,pl) (This)->lpVtbl->get_readyState(This,pl)
#define IXMLDocument2_get_charset(This,p) (This)->lpVtbl->get_charset(This,p)
#define IXMLDocument2_put_charset(This,p) (This)->lpVtbl->put_charset(This,p)
#define IXMLDocument2_get_version(This,p) (This)->lpVtbl->get_version(This,p)
#define IXMLDocument2_get_doctype(This,p) (This)->lpVtbl->get_doctype(This,p)
#define IXMLDocument2_get_dtdURL(This,p) (This)->lpVtbl->get_dtdURL(This,p)
#define IXMLDocument2_createElement(This,vType,var1,ppElem) (This)->lpVtbl->createElement(This,vType,var1,ppElem)
#define IXMLDocument2_get_async(This,pf) (This)->lpVtbl->get_async(This,pf)
#define IXMLDocument2_put_async(This,f) (This)->lpVtbl->put_async(This,f)
#endif
#endif
  HRESULT WINAPI IXMLDocument2_get_root_Proxy(IXMLDocument2 *This,IXMLElement2 **p);
  void __RPC_STUB IXMLDocument2_get_root_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_fileSize_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_fileSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_fileModifiedDate_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_fileModifiedDate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_fileUpdatedDate_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_fileUpdatedDate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_URL_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_URL_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_put_URL_Proxy(IXMLDocument2 *This,BSTR p);
  void __RPC_STUB IXMLDocument2_put_URL_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_mimeType_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_mimeType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_readyState_Proxy(IXMLDocument2 *This,LONG *pl);
  void __RPC_STUB IXMLDocument2_get_readyState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_charset_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_charset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_put_charset_Proxy(IXMLDocument2 *This,BSTR p);
  void __RPC_STUB IXMLDocument2_put_charset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_version_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_version_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_doctype_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_doctype_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_dtdURL_Proxy(IXMLDocument2 *This,BSTR *p);
  void __RPC_STUB IXMLDocument2_get_dtdURL_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_createElement_Proxy(IXMLDocument2 *This,VARIANT vType,VARIANT var1,IXMLElement2 **ppElem);
  void __RPC_STUB IXMLDocument2_createElement_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_get_async_Proxy(IXMLDocument2 *This,VARIANT_BOOL *pf);
  void __RPC_STUB IXMLDocument2_get_async_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLDocument2_put_async_Proxy(IXMLDocument2 *This,VARIANT_BOOL f);
  void __RPC_STUB IXMLDocument2_put_async_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLElement_INTERFACE_DEFINED__
#define __IXMLElement_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLElement;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLElement : public IDispatch {
  public:
    virtual HRESULT WINAPI get_tagName(BSTR *p) = 0;
    virtual HRESULT WINAPI put_tagName(BSTR p) = 0;
    virtual HRESULT WINAPI get_parent(IXMLElement **ppParent) = 0;
    virtual HRESULT WINAPI setAttribute(BSTR strPropertyName,VARIANT PropertyValue) = 0;
    virtual HRESULT WINAPI getAttribute(BSTR strPropertyName,VARIANT *PropertyValue) = 0;
    virtual HRESULT WINAPI removeAttribute(BSTR strPropertyName) = 0;
    virtual HRESULT WINAPI get_children(IXMLElementCollection **pp) = 0;
    virtual HRESULT WINAPI get_type(LONG *plType) = 0;
    virtual HRESULT WINAPI get_text(BSTR *p) = 0;
    virtual HRESULT WINAPI put_text(BSTR p) = 0;
    virtual HRESULT WINAPI addChild(IXMLElement *pChildElem,LONG lIndex,LONG lReserved) = 0;
    virtual HRESULT WINAPI removeChild(IXMLElement *pChildElem) = 0;
  };
#else
  typedef struct IXMLElementVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLElement *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLElement *This);
      ULONG (WINAPI *Release)(IXMLElement *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLElement *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLElement *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLElement *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLElement *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_tagName)(IXMLElement *This,BSTR *p);
      HRESULT (WINAPI *put_tagName)(IXMLElement *This,BSTR p);
      HRESULT (WINAPI *get_parent)(IXMLElement *This,IXMLElement **ppParent);
      HRESULT (WINAPI *setAttribute)(IXMLElement *This,BSTR strPropertyName,VARIANT PropertyValue);
      HRESULT (WINAPI *getAttribute)(IXMLElement *This,BSTR strPropertyName,VARIANT *PropertyValue);
      HRESULT (WINAPI *removeAttribute)(IXMLElement *This,BSTR strPropertyName);
      HRESULT (WINAPI *get_children)(IXMLElement *This,IXMLElementCollection **pp);
      HRESULT (WINAPI *get_type)(IXMLElement *This,LONG *plType);
      HRESULT (WINAPI *get_text)(IXMLElement *This,BSTR *p);
      HRESULT (WINAPI *put_text)(IXMLElement *This,BSTR p);
      HRESULT (WINAPI *addChild)(IXMLElement *This,IXMLElement *pChildElem,LONG lIndex,LONG lReserved);
      HRESULT (WINAPI *removeChild)(IXMLElement *This,IXMLElement *pChildElem);
    END_INTERFACE
  } IXMLElementVtbl;
  struct IXMLElement {
    CONST_VTBL struct IXMLElementVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLElement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLElement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLElement_Release(This) (This)->lpVtbl->Release(This)
#define IXMLElement_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLElement_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLElement_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLElement_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLElement_get_tagName(This,p) (This)->lpVtbl->get_tagName(This,p)
#define IXMLElement_put_tagName(This,p) (This)->lpVtbl->put_tagName(This,p)
#define IXMLElement_get_parent(This,ppParent) (This)->lpVtbl->get_parent(This,ppParent)
#define IXMLElement_setAttribute(This,strPropertyName,PropertyValue) (This)->lpVtbl->setAttribute(This,strPropertyName,PropertyValue)
#define IXMLElement_getAttribute(This,strPropertyName,PropertyValue) (This)->lpVtbl->getAttribute(This,strPropertyName,PropertyValue)
#define IXMLElement_removeAttribute(This,strPropertyName) (This)->lpVtbl->removeAttribute(This,strPropertyName)
#define IXMLElement_get_children(This,pp) (This)->lpVtbl->get_children(This,pp)
#define IXMLElement_get_type(This,plType) (This)->lpVtbl->get_type(This,plType)
#define IXMLElement_get_text(This,p) (This)->lpVtbl->get_text(This,p)
#define IXMLElement_put_text(This,p) (This)->lpVtbl->put_text(This,p)
#define IXMLElement_addChild(This,pChildElem,lIndex,lReserved) (This)->lpVtbl->addChild(This,pChildElem,lIndex,lReserved)
#define IXMLElement_removeChild(This,pChildElem) (This)->lpVtbl->removeChild(This,pChildElem)
#endif
#endif
  HRESULT WINAPI IXMLElement_get_tagName_Proxy(IXMLElement *This,BSTR *p);
  void __RPC_STUB IXMLElement_get_tagName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_put_tagName_Proxy(IXMLElement *This,BSTR p);
  void __RPC_STUB IXMLElement_put_tagName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_get_parent_Proxy(IXMLElement *This,IXMLElement **ppParent);
  void __RPC_STUB IXMLElement_get_parent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_setAttribute_Proxy(IXMLElement *This,BSTR strPropertyName,VARIANT PropertyValue);
  void __RPC_STUB IXMLElement_setAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_getAttribute_Proxy(IXMLElement *This,BSTR strPropertyName,VARIANT *PropertyValue);
  void __RPC_STUB IXMLElement_getAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_removeAttribute_Proxy(IXMLElement *This,BSTR strPropertyName);
  void __RPC_STUB IXMLElement_removeAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_get_children_Proxy(IXMLElement *This,IXMLElementCollection **pp);
  void __RPC_STUB IXMLElement_get_children_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_get_type_Proxy(IXMLElement *This,LONG *plType);
  void __RPC_STUB IXMLElement_get_type_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_get_text_Proxy(IXMLElement *This,BSTR *p);
  void __RPC_STUB IXMLElement_get_text_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_put_text_Proxy(IXMLElement *This,BSTR p);
  void __RPC_STUB IXMLElement_put_text_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_addChild_Proxy(IXMLElement *This,IXMLElement *pChildElem,LONG lIndex,LONG lReserved);
  void __RPC_STUB IXMLElement_addChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement_removeChild_Proxy(IXMLElement *This,IXMLElement *pChildElem);
  void __RPC_STUB IXMLElement_removeChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLElement2_INTERFACE_DEFINED__
#define __IXMLElement2_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLElement2;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLElement2 : public IDispatch {
  public:
    virtual HRESULT WINAPI get_tagName(BSTR *p) = 0;
    virtual HRESULT WINAPI put_tagName(BSTR p) = 0;
    virtual HRESULT WINAPI get_parent(IXMLElement2 **ppParent) = 0;
    virtual HRESULT WINAPI setAttribute(BSTR strPropertyName,VARIANT PropertyValue) = 0;
    virtual HRESULT WINAPI getAttribute(BSTR strPropertyName,VARIANT *PropertyValue) = 0;
    virtual HRESULT WINAPI removeAttribute(BSTR strPropertyName) = 0;
    virtual HRESULT WINAPI get_children(IXMLElementCollection **pp) = 0;
    virtual HRESULT WINAPI get_type(LONG *plType) = 0;
    virtual HRESULT WINAPI get_text(BSTR *p) = 0;
    virtual HRESULT WINAPI put_text(BSTR p) = 0;
    virtual HRESULT WINAPI addChild(IXMLElement2 *pChildElem,LONG lIndex,LONG lReserved) = 0;
    virtual HRESULT WINAPI removeChild(IXMLElement2 *pChildElem) = 0;
    virtual HRESULT WINAPI get_attributes(IXMLElementCollection **pp) = 0;
  };
#else
  typedef struct IXMLElement2Vtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLElement2 *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLElement2 *This);
      ULONG (WINAPI *Release)(IXMLElement2 *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLElement2 *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLElement2 *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLElement2 *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLElement2 *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_tagName)(IXMLElement2 *This,BSTR *p);
      HRESULT (WINAPI *put_tagName)(IXMLElement2 *This,BSTR p);
      HRESULT (WINAPI *get_parent)(IXMLElement2 *This,IXMLElement2 **ppParent);
      HRESULT (WINAPI *setAttribute)(IXMLElement2 *This,BSTR strPropertyName,VARIANT PropertyValue);
      HRESULT (WINAPI *getAttribute)(IXMLElement2 *This,BSTR strPropertyName,VARIANT *PropertyValue);
      HRESULT (WINAPI *removeAttribute)(IXMLElement2 *This,BSTR strPropertyName);
      HRESULT (WINAPI *get_children)(IXMLElement2 *This,IXMLElementCollection **pp);
      HRESULT (WINAPI *get_type)(IXMLElement2 *This,LONG *plType);
      HRESULT (WINAPI *get_text)(IXMLElement2 *This,BSTR *p);
      HRESULT (WINAPI *put_text)(IXMLElement2 *This,BSTR p);
      HRESULT (WINAPI *addChild)(IXMLElement2 *This,IXMLElement2 *pChildElem,LONG lIndex,LONG lReserved);
      HRESULT (WINAPI *removeChild)(IXMLElement2 *This,IXMLElement2 *pChildElem);
      HRESULT (WINAPI *get_attributes)(IXMLElement2 *This,IXMLElementCollection **pp);
    END_INTERFACE
  } IXMLElement2Vtbl;
  struct IXMLElement2 {
    CONST_VTBL struct IXMLElement2Vtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLElement2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLElement2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLElement2_Release(This) (This)->lpVtbl->Release(This)
#define IXMLElement2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLElement2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLElement2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLElement2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLElement2_get_tagName(This,p) (This)->lpVtbl->get_tagName(This,p)
#define IXMLElement2_put_tagName(This,p) (This)->lpVtbl->put_tagName(This,p)
#define IXMLElement2_get_parent(This,ppParent) (This)->lpVtbl->get_parent(This,ppParent)
#define IXMLElement2_setAttribute(This,strPropertyName,PropertyValue) (This)->lpVtbl->setAttribute(This,strPropertyName,PropertyValue)
#define IXMLElement2_getAttribute(This,strPropertyName,PropertyValue) (This)->lpVtbl->getAttribute(This,strPropertyName,PropertyValue)
#define IXMLElement2_removeAttribute(This,strPropertyName) (This)->lpVtbl->removeAttribute(This,strPropertyName)
#define IXMLElement2_get_children(This,pp) (This)->lpVtbl->get_children(This,pp)
#define IXMLElement2_get_type(This,plType) (This)->lpVtbl->get_type(This,plType)
#define IXMLElement2_get_text(This,p) (This)->lpVtbl->get_text(This,p)
#define IXMLElement2_put_text(This,p) (This)->lpVtbl->put_text(This,p)
#define IXMLElement2_addChild(This,pChildElem,lIndex,lReserved) (This)->lpVtbl->addChild(This,pChildElem,lIndex,lReserved)
#define IXMLElement2_removeChild(This,pChildElem) (This)->lpVtbl->removeChild(This,pChildElem)
#define IXMLElement2_get_attributes(This,pp) (This)->lpVtbl->get_attributes(This,pp)
#endif
#endif
  HRESULT WINAPI IXMLElement2_get_tagName_Proxy(IXMLElement2 *This,BSTR *p);
  void __RPC_STUB IXMLElement2_get_tagName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_put_tagName_Proxy(IXMLElement2 *This,BSTR p);
  void __RPC_STUB IXMLElement2_put_tagName_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_get_parent_Proxy(IXMLElement2 *This,IXMLElement2 **ppParent);
  void __RPC_STUB IXMLElement2_get_parent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_setAttribute_Proxy(IXMLElement2 *This,BSTR strPropertyName,VARIANT PropertyValue);
  void __RPC_STUB IXMLElement2_setAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_getAttribute_Proxy(IXMLElement2 *This,BSTR strPropertyName,VARIANT *PropertyValue);
  void __RPC_STUB IXMLElement2_getAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_removeAttribute_Proxy(IXMLElement2 *This,BSTR strPropertyName);
  void __RPC_STUB IXMLElement2_removeAttribute_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_get_children_Proxy(IXMLElement2 *This,IXMLElementCollection **pp);
  void __RPC_STUB IXMLElement2_get_children_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_get_type_Proxy(IXMLElement2 *This,LONG *plType);
  void __RPC_STUB IXMLElement2_get_type_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_get_text_Proxy(IXMLElement2 *This,BSTR *p);
  void __RPC_STUB IXMLElement2_get_text_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_put_text_Proxy(IXMLElement2 *This,BSTR p);
  void __RPC_STUB IXMLElement2_put_text_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_addChild_Proxy(IXMLElement2 *This,IXMLElement2 *pChildElem,LONG lIndex,LONG lReserved);
  void __RPC_STUB IXMLElement2_addChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_removeChild_Proxy(IXMLElement2 *This,IXMLElement2 *pChildElem);
  void __RPC_STUB IXMLElement2_removeChild_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLElement2_get_attributes_Proxy(IXMLElement2 *This,IXMLElementCollection **pp);
  void __RPC_STUB IXMLElement2_get_attributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLAttribute_INTERFACE_DEFINED__
#define __IXMLAttribute_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLAttribute;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLAttribute : public IDispatch {
  public:
    virtual HRESULT WINAPI get_name(BSTR *n) = 0;
    virtual HRESULT WINAPI get_value(BSTR *v) = 0;
  };
#else
  typedef struct IXMLAttributeVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLAttribute *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLAttribute *This);
      ULONG (WINAPI *Release)(IXMLAttribute *This);
      HRESULT (WINAPI *GetTypeInfoCount)(IXMLAttribute *This,UINT *pctinfo);
      HRESULT (WINAPI *GetTypeInfo)(IXMLAttribute *This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo);
      HRESULT (WINAPI *GetIDsOfNames)(IXMLAttribute *This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId);
      HRESULT (WINAPI *Invoke)(IXMLAttribute *This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr);
      HRESULT (WINAPI *get_name)(IXMLAttribute *This,BSTR *n);
      HRESULT (WINAPI *get_value)(IXMLAttribute *This,BSTR *v);
    END_INTERFACE
  } IXMLAttributeVtbl;
  struct IXMLAttribute {
    CONST_VTBL struct IXMLAttributeVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLAttribute_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLAttribute_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLAttribute_Release(This) (This)->lpVtbl->Release(This)
#define IXMLAttribute_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLAttribute_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLAttribute_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLAttribute_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#define IXMLAttribute_get_name(This,n) (This)->lpVtbl->get_name(This,n)
#define IXMLAttribute_get_value(This,v) (This)->lpVtbl->get_value(This,v)
#endif
#endif
  HRESULT WINAPI IXMLAttribute_get_name_Proxy(IXMLAttribute *This,BSTR *n);
  void __RPC_STUB IXMLAttribute_get_name_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
  HRESULT WINAPI IXMLAttribute_get_value_Proxy(IXMLAttribute *This,BSTR *v);
  void __RPC_STUB IXMLAttribute_get_value_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

#ifndef __IXMLError_INTERFACE_DEFINED__
#define __IXMLError_INTERFACE_DEFINED__
  EXTERN_C const IID IID_IXMLError;
#if defined(__cplusplus) && !defined(CINTERFACE)
  struct IXMLError : public IUnknown {
  public:
    virtual HRESULT WINAPI GetErrorInfo(XML_ERROR *pErrorReturn) = 0;
  };
#else
  typedef struct IXMLErrorVtbl {
    BEGIN_INTERFACE
      HRESULT (WINAPI *QueryInterface)(IXMLError *This,REFIID riid,void **ppvObject);
      ULONG (WINAPI *AddRef)(IXMLError *This);
      ULONG (WINAPI *Release)(IXMLError *This);
      HRESULT (WINAPI *GetErrorInfo)(IXMLError *This,XML_ERROR *pErrorReturn);
    END_INTERFACE
  } IXMLErrorVtbl;
  struct IXMLError {
    CONST_VTBL struct IXMLErrorVtbl *lpVtbl;
  };
#ifdef COBJMACROS
#define IXMLError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLError_Release(This) (This)->lpVtbl->Release(This)
#define IXMLError_GetErrorInfo(This,pErrorReturn) (This)->lpVtbl->GetErrorInfo(This,pErrorReturn)
#endif
#endif
  HRESULT WINAPI IXMLError_GetErrorInfo_Proxy(IXMLError *This,XML_ERROR *pErrorReturn);
  void __RPC_STUB IXMLError_GetErrorInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);
#endif

  EXTERN_C const CLSID CLSID_XMLDocument;
#ifdef __cplusplus
  class XMLDocument;
#endif
#endif
#pragma pop_macro("abort")
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __msxml_h__ */
