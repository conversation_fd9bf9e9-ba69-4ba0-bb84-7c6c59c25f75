/*** Autogenerated by WIDL 1.6 from include/fsrmreports.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __fsrmreports_h__
#define __fsrmreports_h__

/* Forward declarations */

#ifndef __IFsrmReport_FWD_DEFINED__
#define __IFsrmReport_FWD_DEFINED__
typedef interface IFsrmReport IFsrmReport;
#endif

#ifndef __IFsrmReportScheduler_FWD_DEFINED__
#define __IFsrmReportScheduler_FWD_DEFINED__
typedef interface IFsrmReportScheduler IFsrmReportScheduler;
#endif

#ifndef __IFsrmFileManagementJobManager_FWD_DEFINED__
#define __IFsrmFileManagementJobManager_FWD_DEFINED__
typedef interface IFsrmFileManagementJobManager IFsrmFileManagementJobManager;
#endif

#ifndef __IFsrmPropertyCondition_FWD_DEFINED__
#define __IFsrmPropertyCondition_FWD_DEFINED__
typedef interface IFsrmPropertyCondition IFsrmPropertyCondition;
#endif

#ifndef __IFsrmReportManager_FWD_DEFINED__
#define __IFsrmReportManager_FWD_DEFINED__
typedef interface IFsrmReportManager IFsrmReportManager;
#endif

#ifndef __IFsrmReportJob_FWD_DEFINED__
#define __IFsrmReportJob_FWD_DEFINED__
typedef interface IFsrmReportJob IFsrmReportJob;
#endif

#ifndef __IFsrmFileManagementJob_FWD_DEFINED__
#define __IFsrmFileManagementJob_FWD_DEFINED__
typedef interface IFsrmFileManagementJob IFsrmFileManagementJob;
#endif

#ifndef __IFsrmFileCondition_FWD_DEFINED__
#define __IFsrmFileCondition_FWD_DEFINED__
typedef interface IFsrmFileCondition IFsrmFileCondition;
#endif

#ifndef __IFsrmFileConditionProperty_FWD_DEFINED__
#define __IFsrmFileConditionProperty_FWD_DEFINED__
typedef interface IFsrmFileConditionProperty IFsrmFileConditionProperty;
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <fsrmenums.h>
#include <fsrm.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IFsrmFileManagementJob_FWD_DEFINED__
#define __IFsrmFileManagementJob_FWD_DEFINED__
typedef interface IFsrmFileManagementJob IFsrmFileManagementJob;
#endif

#ifndef __IFsrmFileManagementJobManager_FWD_DEFINED__
#define __IFsrmFileManagementJobManager_FWD_DEFINED__
typedef interface IFsrmFileManagementJobManager IFsrmFileManagementJobManager;
#endif

#ifndef __IFsrmPropertyCondition_FWD_DEFINED__
#define __IFsrmPropertyCondition_FWD_DEFINED__
typedef interface IFsrmPropertyCondition IFsrmPropertyCondition;
#endif

#ifndef __IFsrmReport_FWD_DEFINED__
#define __IFsrmReport_FWD_DEFINED__
typedef interface IFsrmReport IFsrmReport;
#endif

#ifndef __IFsrmReportJob_FWD_DEFINED__
#define __IFsrmReportJob_FWD_DEFINED__
typedef interface IFsrmReportJob IFsrmReportJob;
#endif

#ifndef __IFsrmReportManager_FWD_DEFINED__
#define __IFsrmReportManager_FWD_DEFINED__
typedef interface IFsrmReportManager IFsrmReportManager;
#endif

#define FSRM_DISPID_REPORT_MANAGER (FSRM_DISPID_FEATURE_REPORTS | 0x100000)

#define FSRM_DISPID_REPORT_JOB (FSRM_DISPID_FEATURE_REPORTS | 0x200000)

#define FSRM_DISPID_REPORT (FSRM_DISPID_FEATURE_REPORTS | 0x300000)

#define FSRM_DISPID_REPORT_SCHEDULER (FSRM_DISPID_FEATURE_REPORTS | 0x400000)

#define FSRM_DISPID_FILE_MANAGEMENT_JOB_MANAGER (FSRM_DISPID_FEATURE_REPORTS | 0x500000)

#define FSRM_DISPID_FILE_MANAGEMENT_JOB (FSRM_DISPID_FEATURE_REPORTS | 0x600000)

#define FSRM_DISPID_FILE_MANAGEMENT_NOTIFICATION (FSRM_DISPID_FEATURE_REPORTS | 0x700000)

#define FSRM_DISPID_PROPERTY_CONDITION (FSRM_DISPID_FEATURE_REPORTS | 0x800000)

#define FSRM_DISPID_FILE_CONDITION (FSRM_DISPID_FEATURE_REPORTS | 0x900000)

#define FSRM_DISPID_FILE_CONDITION_PROPERTY (FSRM_DISPID_FEATURE_REPORTS | 0xa00000)

#define FSRM_DISPID_FILE_MANAGEMENT_JOB_2 (FSRM_DISPID_FEATURE_REPORTS | 0xb00000)

/*****************************************************************************
 * IFsrmReport interface
 */
#ifndef __IFsrmReport_INTERFACE_DEFINED__
#define __IFsrmReport_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmReport, 0xd8cc81d9, 0x46b8, 0x4fa4, 0xbf,0xa5, 0x4a,0xa9,0xde,0xc9,0xb6,0x38);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d8cc81d9-46b8-4fa4-bfa5-4aa9dec9b638")
IFsrmReport : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Type(
        FsrmReportType *reportType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *description) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Description(
        BSTR description) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastGeneratedFileNamePrefix(
        BSTR *prefix) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFilter(
        FsrmReportFilter filter,
        VARIANT *filterValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFilter(
        FsrmReportFilter filter,
        VARIANT filterValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmReport, 0xd8cc81d9, 0x46b8, 0x4fa4, 0xbf,0xa5, 0x4a,0xa9,0xde,0xc9,0xb6,0x38)
#endif
#else
typedef struct IFsrmReportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmReport* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmReport* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmReport* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmReport* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmReport* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmReport* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmReport* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmReport methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IFsrmReport* This,
        FsrmReportType *reportType);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmReport* This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmReport* This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmReport* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmReport* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *get_LastGeneratedFileNamePrefix)(
        IFsrmReport* This,
        BSTR *prefix);

    HRESULT (STDMETHODCALLTYPE *GetFilter)(
        IFsrmReport* This,
        FsrmReportFilter filter,
        VARIANT *filterValue);

    HRESULT (STDMETHODCALLTYPE *SetFilter)(
        IFsrmReport* This,
        FsrmReportFilter filter,
        VARIANT filterValue);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmReport* This);

    END_INTERFACE
} IFsrmReportVtbl;
interface IFsrmReport {
    CONST_VTBL IFsrmReportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmReport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmReport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmReport_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmReport_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmReport_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmReport_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmReport_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmReport methods ***/
#define IFsrmReport_get_Type(This,reportType) (This)->lpVtbl->get_Type(This,reportType)
#define IFsrmReport_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmReport_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmReport_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmReport_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmReport_get_LastGeneratedFileNamePrefix(This,prefix) (This)->lpVtbl->get_LastGeneratedFileNamePrefix(This,prefix)
#define IFsrmReport_GetFilter(This,filter,filterValue) (This)->lpVtbl->GetFilter(This,filter,filterValue)
#define IFsrmReport_SetFilter(This,filter,filterValue) (This)->lpVtbl->SetFilter(This,filter,filterValue)
#define IFsrmReport_Delete(This) (This)->lpVtbl->Delete(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmReport_QueryInterface(IFsrmReport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmReport_AddRef(IFsrmReport* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmReport_Release(IFsrmReport* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmReport_GetTypeInfoCount(IFsrmReport* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmReport_GetTypeInfo(IFsrmReport* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmReport_GetIDsOfNames(IFsrmReport* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmReport_Invoke(IFsrmReport* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmReport methods ***/
static FORCEINLINE HRESULT IFsrmReport_get_Type(IFsrmReport* This,FsrmReportType *reportType) {
    return This->lpVtbl->get_Type(This,reportType);
}
static FORCEINLINE HRESULT IFsrmReport_get_Name(IFsrmReport* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmReport_put_Name(IFsrmReport* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmReport_get_Description(IFsrmReport* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmReport_put_Description(IFsrmReport* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmReport_get_LastGeneratedFileNamePrefix(IFsrmReport* This,BSTR *prefix) {
    return This->lpVtbl->get_LastGeneratedFileNamePrefix(This,prefix);
}
static FORCEINLINE HRESULT IFsrmReport_GetFilter(IFsrmReport* This,FsrmReportFilter filter,VARIANT *filterValue) {
    return This->lpVtbl->GetFilter(This,filter,filterValue);
}
static FORCEINLINE HRESULT IFsrmReport_SetFilter(IFsrmReport* This,FsrmReportFilter filter,VARIANT filterValue) {
    return This->lpVtbl->SetFilter(This,filter,filterValue);
}
static FORCEINLINE HRESULT IFsrmReport_Delete(IFsrmReport* This) {
    return This->lpVtbl->Delete(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmReport_get_Type_Proxy(
    IFsrmReport* This,
    FsrmReportType *reportType);
void __RPC_STUB IFsrmReport_get_Type_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReport_get_Name_Proxy(
    IFsrmReport* This,
    BSTR *name);
void __RPC_STUB IFsrmReport_get_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReport_put_Name_Proxy(
    IFsrmReport* This,
    BSTR name);
void __RPC_STUB IFsrmReport_put_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReport_get_Description_Proxy(
    IFsrmReport* This,
    BSTR *description);
void __RPC_STUB IFsrmReport_get_Description_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReport_put_Description_Proxy(
    IFsrmReport* This,
    BSTR description);
void __RPC_STUB IFsrmReport_put_Description_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReport_get_LastGeneratedFileNamePrefix_Proxy(
    IFsrmReport* This,
    BSTR *prefix);
void __RPC_STUB IFsrmReport_get_LastGeneratedFileNamePrefix_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReport_GetFilter_Proxy(
    IFsrmReport* This,
    FsrmReportFilter filter,
    VARIANT *filterValue);
void __RPC_STUB IFsrmReport_GetFilter_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReport_SetFilter_Proxy(
    IFsrmReport* This,
    FsrmReportFilter filter,
    VARIANT filterValue);
void __RPC_STUB IFsrmReport_SetFilter_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReport_Delete_Proxy(
    IFsrmReport* This);
void __RPC_STUB IFsrmReport_Delete_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmReport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmReportScheduler interface
 */
#ifndef __IFsrmReportScheduler_INTERFACE_DEFINED__
#define __IFsrmReportScheduler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmReportScheduler, 0x6879caf9, 0x6617, 0x4484, 0x87,0x19, 0x71,0xc3,0xd8,0x64,0x5f,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6879caf9-**************-71c3d8645f94")
IFsrmReportScheduler : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE VerifyNamespaces(
        VARIANT *namespacesSafeArray) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateScheduleTask(
        BSTR taskName,
        VARIANT *namespacesSafeArray,
        BSTR serializedTask) = 0;

    virtual HRESULT STDMETHODCALLTYPE ModifyScheduleTask(
        BSTR taskName,
        VARIANT *namespacesSafeArray,
        BSTR serializedTask) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteScheduleTask(
        BSTR taskName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmReportScheduler, 0x6879caf9, 0x6617, 0x4484, 0x87,0x19, 0x71,0xc3,0xd8,0x64,0x5f,0x94)
#endif
#else
typedef struct IFsrmReportSchedulerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmReportScheduler* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmReportScheduler* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmReportScheduler* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmReportScheduler* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmReportScheduler* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmReportScheduler* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmReportScheduler* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmReportScheduler methods ***/
    HRESULT (STDMETHODCALLTYPE *VerifyNamespaces)(
        IFsrmReportScheduler* This,
        VARIANT *namespacesSafeArray);

    HRESULT (STDMETHODCALLTYPE *CreateScheduleTask)(
        IFsrmReportScheduler* This,
        BSTR taskName,
        VARIANT *namespacesSafeArray,
        BSTR serializedTask);

    HRESULT (STDMETHODCALLTYPE *ModifyScheduleTask)(
        IFsrmReportScheduler* This,
        BSTR taskName,
        VARIANT *namespacesSafeArray,
        BSTR serializedTask);

    HRESULT (STDMETHODCALLTYPE *DeleteScheduleTask)(
        IFsrmReportScheduler* This,
        BSTR taskName);

    END_INTERFACE
} IFsrmReportSchedulerVtbl;
interface IFsrmReportScheduler {
    CONST_VTBL IFsrmReportSchedulerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmReportScheduler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmReportScheduler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmReportScheduler_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmReportScheduler_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmReportScheduler_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmReportScheduler_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmReportScheduler_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmReportScheduler methods ***/
#define IFsrmReportScheduler_VerifyNamespaces(This,namespacesSafeArray) (This)->lpVtbl->VerifyNamespaces(This,namespacesSafeArray)
#define IFsrmReportScheduler_CreateScheduleTask(This,taskName,namespacesSafeArray,serializedTask) (This)->lpVtbl->CreateScheduleTask(This,taskName,namespacesSafeArray,serializedTask)
#define IFsrmReportScheduler_ModifyScheduleTask(This,taskName,namespacesSafeArray,serializedTask) (This)->lpVtbl->ModifyScheduleTask(This,taskName,namespacesSafeArray,serializedTask)
#define IFsrmReportScheduler_DeleteScheduleTask(This,taskName) (This)->lpVtbl->DeleteScheduleTask(This,taskName)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmReportScheduler_QueryInterface(IFsrmReportScheduler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmReportScheduler_AddRef(IFsrmReportScheduler* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmReportScheduler_Release(IFsrmReportScheduler* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmReportScheduler_GetTypeInfoCount(IFsrmReportScheduler* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmReportScheduler_GetTypeInfo(IFsrmReportScheduler* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmReportScheduler_GetIDsOfNames(IFsrmReportScheduler* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmReportScheduler_Invoke(IFsrmReportScheduler* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmReportScheduler methods ***/
static FORCEINLINE HRESULT IFsrmReportScheduler_VerifyNamespaces(IFsrmReportScheduler* This,VARIANT *namespacesSafeArray) {
    return This->lpVtbl->VerifyNamespaces(This,namespacesSafeArray);
}
static FORCEINLINE HRESULT IFsrmReportScheduler_CreateScheduleTask(IFsrmReportScheduler* This,BSTR taskName,VARIANT *namespacesSafeArray,BSTR serializedTask) {
    return This->lpVtbl->CreateScheduleTask(This,taskName,namespacesSafeArray,serializedTask);
}
static FORCEINLINE HRESULT IFsrmReportScheduler_ModifyScheduleTask(IFsrmReportScheduler* This,BSTR taskName,VARIANT *namespacesSafeArray,BSTR serializedTask) {
    return This->lpVtbl->ModifyScheduleTask(This,taskName,namespacesSafeArray,serializedTask);
}
static FORCEINLINE HRESULT IFsrmReportScheduler_DeleteScheduleTask(IFsrmReportScheduler* This,BSTR taskName) {
    return This->lpVtbl->DeleteScheduleTask(This,taskName);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmReportScheduler_VerifyNamespaces_Proxy(
    IFsrmReportScheduler* This,
    VARIANT *namespacesSafeArray);
void __RPC_STUB IFsrmReportScheduler_VerifyNamespaces_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportScheduler_CreateScheduleTask_Proxy(
    IFsrmReportScheduler* This,
    BSTR taskName,
    VARIANT *namespacesSafeArray,
    BSTR serializedTask);
void __RPC_STUB IFsrmReportScheduler_CreateScheduleTask_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportScheduler_ModifyScheduleTask_Proxy(
    IFsrmReportScheduler* This,
    BSTR taskName,
    VARIANT *namespacesSafeArray,
    BSTR serializedTask);
void __RPC_STUB IFsrmReportScheduler_ModifyScheduleTask_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportScheduler_DeleteScheduleTask_Proxy(
    IFsrmReportScheduler* This,
    BSTR taskName);
void __RPC_STUB IFsrmReportScheduler_DeleteScheduleTask_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmReportScheduler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileManagementJobManager interface
 */
#ifndef __IFsrmFileManagementJobManager_INTERFACE_DEFINED__
#define __IFsrmFileManagementJobManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileManagementJobManager, 0xee321ecb, 0xd95e, 0x48e9, 0x90,0x7c, 0xc7,0x68,0x5a,0x01,0x32,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ee321ecb-d95e-48e9-907c-c7685a013235")
IFsrmFileManagementJobManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ActionVariables(
        SAFEARRAY **variables) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ActionVariableDescriptions(
        SAFEARRAY **descriptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumFileManagementJobs(
        FsrmEnumOptions options,
        IFsrmCollection **fileManagementJobs) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFileManagementJob(
        IFsrmFileManagementJob **fileManagementJob) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileManagementJob(
        BSTR name,
        IFsrmFileManagementJob **fileManagementJob) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileManagementJobManager, 0xee321ecb, 0xd95e, 0x48e9, 0x90,0x7c, 0xc7,0x68,0x5a,0x01,0x32,0x35)
#endif
#else
typedef struct IFsrmFileManagementJobManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileManagementJobManager* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileManagementJobManager* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileManagementJobManager* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileManagementJobManager* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileManagementJobManager* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileManagementJobManager* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileManagementJobManager* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmFileManagementJobManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ActionVariables)(
        IFsrmFileManagementJobManager* This,
        SAFEARRAY **variables);

    HRESULT (STDMETHODCALLTYPE *get_ActionVariableDescriptions)(
        IFsrmFileManagementJobManager* This,
        SAFEARRAY **descriptions);

    HRESULT (STDMETHODCALLTYPE *EnumFileManagementJobs)(
        IFsrmFileManagementJobManager* This,
        FsrmEnumOptions options,
        IFsrmCollection **fileManagementJobs);

    HRESULT (STDMETHODCALLTYPE *CreateFileManagementJob)(
        IFsrmFileManagementJobManager* This,
        IFsrmFileManagementJob **fileManagementJob);

    HRESULT (STDMETHODCALLTYPE *GetFileManagementJob)(
        IFsrmFileManagementJobManager* This,
        BSTR name,
        IFsrmFileManagementJob **fileManagementJob);

    END_INTERFACE
} IFsrmFileManagementJobManagerVtbl;
interface IFsrmFileManagementJobManager {
    CONST_VTBL IFsrmFileManagementJobManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileManagementJobManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileManagementJobManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileManagementJobManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileManagementJobManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileManagementJobManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileManagementJobManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileManagementJobManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmFileManagementJobManager methods ***/
#define IFsrmFileManagementJobManager_get_ActionVariables(This,variables) (This)->lpVtbl->get_ActionVariables(This,variables)
#define IFsrmFileManagementJobManager_get_ActionVariableDescriptions(This,descriptions) (This)->lpVtbl->get_ActionVariableDescriptions(This,descriptions)
#define IFsrmFileManagementJobManager_EnumFileManagementJobs(This,options,fileManagementJobs) (This)->lpVtbl->EnumFileManagementJobs(This,options,fileManagementJobs)
#define IFsrmFileManagementJobManager_CreateFileManagementJob(This,fileManagementJob) (This)->lpVtbl->CreateFileManagementJob(This,fileManagementJob)
#define IFsrmFileManagementJobManager_GetFileManagementJob(This,name,fileManagementJob) (This)->lpVtbl->GetFileManagementJob(This,name,fileManagementJob)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_QueryInterface(IFsrmFileManagementJobManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmFileManagementJobManager_AddRef(IFsrmFileManagementJobManager* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmFileManagementJobManager_Release(IFsrmFileManagementJobManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_GetTypeInfoCount(IFsrmFileManagementJobManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_GetTypeInfo(IFsrmFileManagementJobManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_GetIDsOfNames(IFsrmFileManagementJobManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_Invoke(IFsrmFileManagementJobManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmFileManagementJobManager methods ***/
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_get_ActionVariables(IFsrmFileManagementJobManager* This,SAFEARRAY **variables) {
    return This->lpVtbl->get_ActionVariables(This,variables);
}
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_get_ActionVariableDescriptions(IFsrmFileManagementJobManager* This,SAFEARRAY **descriptions) {
    return This->lpVtbl->get_ActionVariableDescriptions(This,descriptions);
}
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_EnumFileManagementJobs(IFsrmFileManagementJobManager* This,FsrmEnumOptions options,IFsrmCollection **fileManagementJobs) {
    return This->lpVtbl->EnumFileManagementJobs(This,options,fileManagementJobs);
}
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_CreateFileManagementJob(IFsrmFileManagementJobManager* This,IFsrmFileManagementJob **fileManagementJob) {
    return This->lpVtbl->CreateFileManagementJob(This,fileManagementJob);
}
static FORCEINLINE HRESULT IFsrmFileManagementJobManager_GetFileManagementJob(IFsrmFileManagementJobManager* This,BSTR name,IFsrmFileManagementJob **fileManagementJob) {
    return This->lpVtbl->GetFileManagementJob(This,name,fileManagementJob);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmFileManagementJobManager_get_ActionVariables_Proxy(
    IFsrmFileManagementJobManager* This,
    SAFEARRAY **variables);
void __RPC_STUB IFsrmFileManagementJobManager_get_ActionVariables_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJobManager_get_ActionVariableDescriptions_Proxy(
    IFsrmFileManagementJobManager* This,
    SAFEARRAY **descriptions);
void __RPC_STUB IFsrmFileManagementJobManager_get_ActionVariableDescriptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJobManager_EnumFileManagementJobs_Proxy(
    IFsrmFileManagementJobManager* This,
    FsrmEnumOptions options,
    IFsrmCollection **fileManagementJobs);
void __RPC_STUB IFsrmFileManagementJobManager_EnumFileManagementJobs_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJobManager_CreateFileManagementJob_Proxy(
    IFsrmFileManagementJobManager* This,
    IFsrmFileManagementJob **fileManagementJob);
void __RPC_STUB IFsrmFileManagementJobManager_CreateFileManagementJob_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJobManager_GetFileManagementJob_Proxy(
    IFsrmFileManagementJobManager* This,
    BSTR name,
    IFsrmFileManagementJob **fileManagementJob);
void __RPC_STUB IFsrmFileManagementJobManager_GetFileManagementJob_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmFileManagementJobManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmPropertyCondition interface
 */
#ifndef __IFsrmPropertyCondition_INTERFACE_DEFINED__
#define __IFsrmPropertyCondition_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmPropertyCondition, 0x326af66f, 0x2ac0, 0x4f68, 0xbf,0x8c, 0x47,0x59,0xf0,0x54,0xfa,0x29);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("326af66f-2ac0-4f68-bf8c-4759f054fa29")
IFsrmPropertyCondition : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        FsrmPropertyConditionType *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Type(
        FsrmPropertyConditionType type) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Value(
        BSTR *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Value(
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmPropertyCondition, 0x326af66f, 0x2ac0, 0x4f68, 0xbf,0x8c, 0x47,0x59,0xf0,0x54,0xfa,0x29)
#endif
#else
typedef struct IFsrmPropertyConditionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmPropertyCondition* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmPropertyCondition* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmPropertyCondition* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmPropertyCondition* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmPropertyCondition* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmPropertyCondition* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmPropertyCondition* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmPropertyCondition methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmPropertyCondition* This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmPropertyCondition* This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IFsrmPropertyCondition* This,
        FsrmPropertyConditionType *type);

    HRESULT (STDMETHODCALLTYPE *put_Type)(
        IFsrmPropertyCondition* This,
        FsrmPropertyConditionType type);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        IFsrmPropertyCondition* This,
        BSTR *value);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        IFsrmPropertyCondition* This,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmPropertyCondition* This);

    END_INTERFACE
} IFsrmPropertyConditionVtbl;
interface IFsrmPropertyCondition {
    CONST_VTBL IFsrmPropertyConditionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmPropertyCondition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmPropertyCondition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmPropertyCondition_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmPropertyCondition_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmPropertyCondition_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmPropertyCondition_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmPropertyCondition_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmPropertyCondition methods ***/
#define IFsrmPropertyCondition_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmPropertyCondition_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmPropertyCondition_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IFsrmPropertyCondition_put_Type(This,type) (This)->lpVtbl->put_Type(This,type)
#define IFsrmPropertyCondition_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#define IFsrmPropertyCondition_put_Value(This,value) (This)->lpVtbl->put_Value(This,value)
#define IFsrmPropertyCondition_Delete(This) (This)->lpVtbl->Delete(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmPropertyCondition_QueryInterface(IFsrmPropertyCondition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmPropertyCondition_AddRef(IFsrmPropertyCondition* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmPropertyCondition_Release(IFsrmPropertyCondition* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmPropertyCondition_GetTypeInfoCount(IFsrmPropertyCondition* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_GetTypeInfo(IFsrmPropertyCondition* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_GetIDsOfNames(IFsrmPropertyCondition* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_Invoke(IFsrmPropertyCondition* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmPropertyCondition methods ***/
static FORCEINLINE HRESULT IFsrmPropertyCondition_get_Name(IFsrmPropertyCondition* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_put_Name(IFsrmPropertyCondition* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_get_Type(IFsrmPropertyCondition* This,FsrmPropertyConditionType *type) {
    return This->lpVtbl->get_Type(This,type);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_put_Type(IFsrmPropertyCondition* This,FsrmPropertyConditionType type) {
    return This->lpVtbl->put_Type(This,type);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_get_Value(IFsrmPropertyCondition* This,BSTR *value) {
    return This->lpVtbl->get_Value(This,value);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_put_Value(IFsrmPropertyCondition* This,BSTR value) {
    return This->lpVtbl->put_Value(This,value);
}
static FORCEINLINE HRESULT IFsrmPropertyCondition_Delete(IFsrmPropertyCondition* This) {
    return This->lpVtbl->Delete(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmPropertyCondition_get_Name_Proxy(
    IFsrmPropertyCondition* This,
    BSTR *name);
void __RPC_STUB IFsrmPropertyCondition_get_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmPropertyCondition_put_Name_Proxy(
    IFsrmPropertyCondition* This,
    BSTR name);
void __RPC_STUB IFsrmPropertyCondition_put_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmPropertyCondition_get_Type_Proxy(
    IFsrmPropertyCondition* This,
    FsrmPropertyConditionType *type);
void __RPC_STUB IFsrmPropertyCondition_get_Type_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmPropertyCondition_put_Type_Proxy(
    IFsrmPropertyCondition* This,
    FsrmPropertyConditionType type);
void __RPC_STUB IFsrmPropertyCondition_put_Type_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmPropertyCondition_get_Value_Proxy(
    IFsrmPropertyCondition* This,
    BSTR *value);
void __RPC_STUB IFsrmPropertyCondition_get_Value_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmPropertyCondition_put_Value_Proxy(
    IFsrmPropertyCondition* This,
    BSTR value);
void __RPC_STUB IFsrmPropertyCondition_put_Value_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmPropertyCondition_Delete_Proxy(
    IFsrmPropertyCondition* This);
void __RPC_STUB IFsrmPropertyCondition_Delete_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmPropertyCondition_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmReportManager interface
 */
#ifndef __IFsrmReportManager_INTERFACE_DEFINED__
#define __IFsrmReportManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmReportManager, 0x27b899fe, 0x6ffa, 0x4481, 0xa1,0x84, 0xd3,0xda,0xad,0xe8,0xa0,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("27b899fe-6ffa-4481-a184-d3daade8a02b")
IFsrmReportManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE EnumReportJobs(
        FsrmEnumOptions options,
        IFsrmCollection **reportJobs) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateReportJob(
        IFsrmReportJob **reportJob) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReportJob(
        BSTR taskName,
        IFsrmReportJob **reportJob) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputDirectory(
        FsrmReportGenerationContext context,
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputDirectory(
        FsrmReportGenerationContext context,
        BSTR path) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsFilterValidForReportType(
        FsrmReportType reportType,
        FsrmReportFilter filter,
        VARIANT_BOOL *valid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultFilter(
        FsrmReportType reportType,
        FsrmReportFilter filter,
        VARIANT *filterValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultFilter(
        FsrmReportType reportType,
        FsrmReportFilter filter,
        VARIANT filterValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReportSizeLimit(
        FsrmReportLimit limit,
        VARIANT *limitValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetReportSizeLimit(
        FsrmReportLimit limit,
        VARIANT limitValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmReportManager, 0x27b899fe, 0x6ffa, 0x4481, 0xa1,0x84, 0xd3,0xda,0xad,0xe8,0xa0,0x2b)
#endif
#else
typedef struct IFsrmReportManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmReportManager* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmReportManager* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmReportManager* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmReportManager* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmReportManager* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmReportManager* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmReportManager* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmReportManager methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumReportJobs)(
        IFsrmReportManager* This,
        FsrmEnumOptions options,
        IFsrmCollection **reportJobs);

    HRESULT (STDMETHODCALLTYPE *CreateReportJob)(
        IFsrmReportManager* This,
        IFsrmReportJob **reportJob);

    HRESULT (STDMETHODCALLTYPE *GetReportJob)(
        IFsrmReportManager* This,
        BSTR taskName,
        IFsrmReportJob **reportJob);

    HRESULT (STDMETHODCALLTYPE *GetOutputDirectory)(
        IFsrmReportManager* This,
        FsrmReportGenerationContext context,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *SetOutputDirectory)(
        IFsrmReportManager* This,
        FsrmReportGenerationContext context,
        BSTR path);

    HRESULT (STDMETHODCALLTYPE *IsFilterValidForReportType)(
        IFsrmReportManager* This,
        FsrmReportType reportType,
        FsrmReportFilter filter,
        VARIANT_BOOL *valid);

    HRESULT (STDMETHODCALLTYPE *GetDefaultFilter)(
        IFsrmReportManager* This,
        FsrmReportType reportType,
        FsrmReportFilter filter,
        VARIANT *filterValue);

    HRESULT (STDMETHODCALLTYPE *SetDefaultFilter)(
        IFsrmReportManager* This,
        FsrmReportType reportType,
        FsrmReportFilter filter,
        VARIANT filterValue);

    HRESULT (STDMETHODCALLTYPE *GetReportSizeLimit)(
        IFsrmReportManager* This,
        FsrmReportLimit limit,
        VARIANT *limitValue);

    HRESULT (STDMETHODCALLTYPE *SetReportSizeLimit)(
        IFsrmReportManager* This,
        FsrmReportLimit limit,
        VARIANT limitValue);

    END_INTERFACE
} IFsrmReportManagerVtbl;
interface IFsrmReportManager {
    CONST_VTBL IFsrmReportManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmReportManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmReportManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmReportManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmReportManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmReportManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmReportManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmReportManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmReportManager methods ***/
#define IFsrmReportManager_EnumReportJobs(This,options,reportJobs) (This)->lpVtbl->EnumReportJobs(This,options,reportJobs)
#define IFsrmReportManager_CreateReportJob(This,reportJob) (This)->lpVtbl->CreateReportJob(This,reportJob)
#define IFsrmReportManager_GetReportJob(This,taskName,reportJob) (This)->lpVtbl->GetReportJob(This,taskName,reportJob)
#define IFsrmReportManager_GetOutputDirectory(This,context,path) (This)->lpVtbl->GetOutputDirectory(This,context,path)
#define IFsrmReportManager_SetOutputDirectory(This,context,path) (This)->lpVtbl->SetOutputDirectory(This,context,path)
#define IFsrmReportManager_IsFilterValidForReportType(This,reportType,filter,valid) (This)->lpVtbl->IsFilterValidForReportType(This,reportType,filter,valid)
#define IFsrmReportManager_GetDefaultFilter(This,reportType,filter,filterValue) (This)->lpVtbl->GetDefaultFilter(This,reportType,filter,filterValue)
#define IFsrmReportManager_SetDefaultFilter(This,reportType,filter,filterValue) (This)->lpVtbl->SetDefaultFilter(This,reportType,filter,filterValue)
#define IFsrmReportManager_GetReportSizeLimit(This,limit,limitValue) (This)->lpVtbl->GetReportSizeLimit(This,limit,limitValue)
#define IFsrmReportManager_SetReportSizeLimit(This,limit,limitValue) (This)->lpVtbl->SetReportSizeLimit(This,limit,limitValue)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmReportManager_QueryInterface(IFsrmReportManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmReportManager_AddRef(IFsrmReportManager* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmReportManager_Release(IFsrmReportManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmReportManager_GetTypeInfoCount(IFsrmReportManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmReportManager_GetTypeInfo(IFsrmReportManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmReportManager_GetIDsOfNames(IFsrmReportManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmReportManager_Invoke(IFsrmReportManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmReportManager methods ***/
static FORCEINLINE HRESULT IFsrmReportManager_EnumReportJobs(IFsrmReportManager* This,FsrmEnumOptions options,IFsrmCollection **reportJobs) {
    return This->lpVtbl->EnumReportJobs(This,options,reportJobs);
}
static FORCEINLINE HRESULT IFsrmReportManager_CreateReportJob(IFsrmReportManager* This,IFsrmReportJob **reportJob) {
    return This->lpVtbl->CreateReportJob(This,reportJob);
}
static FORCEINLINE HRESULT IFsrmReportManager_GetReportJob(IFsrmReportManager* This,BSTR taskName,IFsrmReportJob **reportJob) {
    return This->lpVtbl->GetReportJob(This,taskName,reportJob);
}
static FORCEINLINE HRESULT IFsrmReportManager_GetOutputDirectory(IFsrmReportManager* This,FsrmReportGenerationContext context,BSTR *path) {
    return This->lpVtbl->GetOutputDirectory(This,context,path);
}
static FORCEINLINE HRESULT IFsrmReportManager_SetOutputDirectory(IFsrmReportManager* This,FsrmReportGenerationContext context,BSTR path) {
    return This->lpVtbl->SetOutputDirectory(This,context,path);
}
static FORCEINLINE HRESULT IFsrmReportManager_IsFilterValidForReportType(IFsrmReportManager* This,FsrmReportType reportType,FsrmReportFilter filter,VARIANT_BOOL *valid) {
    return This->lpVtbl->IsFilterValidForReportType(This,reportType,filter,valid);
}
static FORCEINLINE HRESULT IFsrmReportManager_GetDefaultFilter(IFsrmReportManager* This,FsrmReportType reportType,FsrmReportFilter filter,VARIANT *filterValue) {
    return This->lpVtbl->GetDefaultFilter(This,reportType,filter,filterValue);
}
static FORCEINLINE HRESULT IFsrmReportManager_SetDefaultFilter(IFsrmReportManager* This,FsrmReportType reportType,FsrmReportFilter filter,VARIANT filterValue) {
    return This->lpVtbl->SetDefaultFilter(This,reportType,filter,filterValue);
}
static FORCEINLINE HRESULT IFsrmReportManager_GetReportSizeLimit(IFsrmReportManager* This,FsrmReportLimit limit,VARIANT *limitValue) {
    return This->lpVtbl->GetReportSizeLimit(This,limit,limitValue);
}
static FORCEINLINE HRESULT IFsrmReportManager_SetReportSizeLimit(IFsrmReportManager* This,FsrmReportLimit limit,VARIANT limitValue) {
    return This->lpVtbl->SetReportSizeLimit(This,limit,limitValue);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmReportManager_EnumReportJobs_Proxy(
    IFsrmReportManager* This,
    FsrmEnumOptions options,
    IFsrmCollection **reportJobs);
void __RPC_STUB IFsrmReportManager_EnumReportJobs_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_CreateReportJob_Proxy(
    IFsrmReportManager* This,
    IFsrmReportJob **reportJob);
void __RPC_STUB IFsrmReportManager_CreateReportJob_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_GetReportJob_Proxy(
    IFsrmReportManager* This,
    BSTR taskName,
    IFsrmReportJob **reportJob);
void __RPC_STUB IFsrmReportManager_GetReportJob_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_GetOutputDirectory_Proxy(
    IFsrmReportManager* This,
    FsrmReportGenerationContext context,
    BSTR *path);
void __RPC_STUB IFsrmReportManager_GetOutputDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_SetOutputDirectory_Proxy(
    IFsrmReportManager* This,
    FsrmReportGenerationContext context,
    BSTR path);
void __RPC_STUB IFsrmReportManager_SetOutputDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_IsFilterValidForReportType_Proxy(
    IFsrmReportManager* This,
    FsrmReportType reportType,
    FsrmReportFilter filter,
    VARIANT_BOOL *valid);
void __RPC_STUB IFsrmReportManager_IsFilterValidForReportType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_GetDefaultFilter_Proxy(
    IFsrmReportManager* This,
    FsrmReportType reportType,
    FsrmReportFilter filter,
    VARIANT *filterValue);
void __RPC_STUB IFsrmReportManager_GetDefaultFilter_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_SetDefaultFilter_Proxy(
    IFsrmReportManager* This,
    FsrmReportType reportType,
    FsrmReportFilter filter,
    VARIANT filterValue);
void __RPC_STUB IFsrmReportManager_SetDefaultFilter_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_GetReportSizeLimit_Proxy(
    IFsrmReportManager* This,
    FsrmReportLimit limit,
    VARIANT *limitValue);
void __RPC_STUB IFsrmReportManager_GetReportSizeLimit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportManager_SetReportSizeLimit_Proxy(
    IFsrmReportManager* This,
    FsrmReportLimit limit,
    VARIANT limitValue);
void __RPC_STUB IFsrmReportManager_SetReportSizeLimit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmReportManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmReportJob interface
 */
#ifndef __IFsrmReportJob_INTERFACE_DEFINED__
#define __IFsrmReportJob_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmReportJob, 0x38e87280, 0x715c, 0x4c7d, 0xa2,0x80, 0xea,0x16,0x51,0xa1,0x9f,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("38e87280-715c-4c7d-a280-ea1651a19fef")
IFsrmReportJob : public IFsrmObject
{
    virtual HRESULT STDMETHODCALLTYPE get_Task(
        BSTR *taskName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Task(
        BSTR taskName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NamespaceRoots(
        SAFEARRAY **namespaceRoots) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_NamespaceRoots(
        SAFEARRAY *namespaceRoots) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Formats(
        SAFEARRAY **formats) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Formats(
        SAFEARRAY *formats) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailTo(
        BSTR *mailTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailTo(
        BSTR mailTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RunningStatus(
        FsrmReportRunningStatus *runningStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastRun(
        DATE *lastRun) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastError(
        BSTR *lastError) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastGeneratedInDirectory(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumReports(
        IFsrmCollection **reports) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateReport(
        FsrmReportType reportType,
        IFsrmReport **report) = 0;

    virtual HRESULT STDMETHODCALLTYPE Run(
        FsrmReportGenerationContext context) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitForCompletion(
        LONG waitSeconds,
        VARIANT_BOOL *completed) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmReportJob, 0x38e87280, 0x715c, 0x4c7d, 0xa2,0x80, 0xea,0x16,0x51,0xa1,0x9f,0xef)
#endif
#else
typedef struct IFsrmReportJobVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmReportJob* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmReportJob* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmReportJob* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmReportJob* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmReportJob* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmReportJob* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmReportJob* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmReportJob* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmReportJob* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmReportJob* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmReportJob* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmReportJob* This);

    /*** IFsrmReportJob methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Task)(
        IFsrmReportJob* This,
        BSTR *taskName);

    HRESULT (STDMETHODCALLTYPE *put_Task)(
        IFsrmReportJob* This,
        BSTR taskName);

    HRESULT (STDMETHODCALLTYPE *get_NamespaceRoots)(
        IFsrmReportJob* This,
        SAFEARRAY **namespaceRoots);

    HRESULT (STDMETHODCALLTYPE *put_NamespaceRoots)(
        IFsrmReportJob* This,
        SAFEARRAY *namespaceRoots);

    HRESULT (STDMETHODCALLTYPE *get_Formats)(
        IFsrmReportJob* This,
        SAFEARRAY **formats);

    HRESULT (STDMETHODCALLTYPE *put_Formats)(
        IFsrmReportJob* This,
        SAFEARRAY *formats);

    HRESULT (STDMETHODCALLTYPE *get_MailTo)(
        IFsrmReportJob* This,
        BSTR *mailTo);

    HRESULT (STDMETHODCALLTYPE *put_MailTo)(
        IFsrmReportJob* This,
        BSTR mailTo);

    HRESULT (STDMETHODCALLTYPE *get_RunningStatus)(
        IFsrmReportJob* This,
        FsrmReportRunningStatus *runningStatus);

    HRESULT (STDMETHODCALLTYPE *get_LastRun)(
        IFsrmReportJob* This,
        DATE *lastRun);

    HRESULT (STDMETHODCALLTYPE *get_LastError)(
        IFsrmReportJob* This,
        BSTR *lastError);

    HRESULT (STDMETHODCALLTYPE *get_LastGeneratedInDirectory)(
        IFsrmReportJob* This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *EnumReports)(
        IFsrmReportJob* This,
        IFsrmCollection **reports);

    HRESULT (STDMETHODCALLTYPE *CreateReport)(
        IFsrmReportJob* This,
        FsrmReportType reportType,
        IFsrmReport **report);

    HRESULT (STDMETHODCALLTYPE *Run)(
        IFsrmReportJob* This,
        FsrmReportGenerationContext context);

    HRESULT (STDMETHODCALLTYPE *WaitForCompletion)(
        IFsrmReportJob* This,
        LONG waitSeconds,
        VARIANT_BOOL *completed);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IFsrmReportJob* This);

    END_INTERFACE
} IFsrmReportJobVtbl;
interface IFsrmReportJob {
    CONST_VTBL IFsrmReportJobVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmReportJob_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmReportJob_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmReportJob_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmReportJob_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmReportJob_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmReportJob_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmReportJob_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmReportJob_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmReportJob_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmReportJob_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmReportJob_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmReportJob_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmReportJob methods ***/
#define IFsrmReportJob_get_Task(This,taskName) (This)->lpVtbl->get_Task(This,taskName)
#define IFsrmReportJob_put_Task(This,taskName) (This)->lpVtbl->put_Task(This,taskName)
#define IFsrmReportJob_get_NamespaceRoots(This,namespaceRoots) (This)->lpVtbl->get_NamespaceRoots(This,namespaceRoots)
#define IFsrmReportJob_put_NamespaceRoots(This,namespaceRoots) (This)->lpVtbl->put_NamespaceRoots(This,namespaceRoots)
#define IFsrmReportJob_get_Formats(This,formats) (This)->lpVtbl->get_Formats(This,formats)
#define IFsrmReportJob_put_Formats(This,formats) (This)->lpVtbl->put_Formats(This,formats)
#define IFsrmReportJob_get_MailTo(This,mailTo) (This)->lpVtbl->get_MailTo(This,mailTo)
#define IFsrmReportJob_put_MailTo(This,mailTo) (This)->lpVtbl->put_MailTo(This,mailTo)
#define IFsrmReportJob_get_RunningStatus(This,runningStatus) (This)->lpVtbl->get_RunningStatus(This,runningStatus)
#define IFsrmReportJob_get_LastRun(This,lastRun) (This)->lpVtbl->get_LastRun(This,lastRun)
#define IFsrmReportJob_get_LastError(This,lastError) (This)->lpVtbl->get_LastError(This,lastError)
#define IFsrmReportJob_get_LastGeneratedInDirectory(This,path) (This)->lpVtbl->get_LastGeneratedInDirectory(This,path)
#define IFsrmReportJob_EnumReports(This,reports) (This)->lpVtbl->EnumReports(This,reports)
#define IFsrmReportJob_CreateReport(This,reportType,report) (This)->lpVtbl->CreateReport(This,reportType,report)
#define IFsrmReportJob_Run(This,context) (This)->lpVtbl->Run(This,context)
#define IFsrmReportJob_WaitForCompletion(This,waitSeconds,completed) (This)->lpVtbl->WaitForCompletion(This,waitSeconds,completed)
#define IFsrmReportJob_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmReportJob_QueryInterface(IFsrmReportJob* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmReportJob_AddRef(IFsrmReportJob* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmReportJob_Release(IFsrmReportJob* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmReportJob_GetTypeInfoCount(IFsrmReportJob* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmReportJob_GetTypeInfo(IFsrmReportJob* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmReportJob_GetIDsOfNames(IFsrmReportJob* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmReportJob_Invoke(IFsrmReportJob* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmReportJob_get_Id(IFsrmReportJob* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmReportJob_get_Description(IFsrmReportJob* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmReportJob_put_Description(IFsrmReportJob* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmReportJob_Delete(IFsrmReportJob* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmReportJob_Commit(IFsrmReportJob* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmReportJob methods ***/
static FORCEINLINE HRESULT IFsrmReportJob_get_Task(IFsrmReportJob* This,BSTR *taskName) {
    return This->lpVtbl->get_Task(This,taskName);
}
static FORCEINLINE HRESULT IFsrmReportJob_put_Task(IFsrmReportJob* This,BSTR taskName) {
    return This->lpVtbl->put_Task(This,taskName);
}
static FORCEINLINE HRESULT IFsrmReportJob_get_NamespaceRoots(IFsrmReportJob* This,SAFEARRAY **namespaceRoots) {
    return This->lpVtbl->get_NamespaceRoots(This,namespaceRoots);
}
static FORCEINLINE HRESULT IFsrmReportJob_put_NamespaceRoots(IFsrmReportJob* This,SAFEARRAY *namespaceRoots) {
    return This->lpVtbl->put_NamespaceRoots(This,namespaceRoots);
}
static FORCEINLINE HRESULT IFsrmReportJob_get_Formats(IFsrmReportJob* This,SAFEARRAY **formats) {
    return This->lpVtbl->get_Formats(This,formats);
}
static FORCEINLINE HRESULT IFsrmReportJob_put_Formats(IFsrmReportJob* This,SAFEARRAY *formats) {
    return This->lpVtbl->put_Formats(This,formats);
}
static FORCEINLINE HRESULT IFsrmReportJob_get_MailTo(IFsrmReportJob* This,BSTR *mailTo) {
    return This->lpVtbl->get_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmReportJob_put_MailTo(IFsrmReportJob* This,BSTR mailTo) {
    return This->lpVtbl->put_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmReportJob_get_RunningStatus(IFsrmReportJob* This,FsrmReportRunningStatus *runningStatus) {
    return This->lpVtbl->get_RunningStatus(This,runningStatus);
}
static FORCEINLINE HRESULT IFsrmReportJob_get_LastRun(IFsrmReportJob* This,DATE *lastRun) {
    return This->lpVtbl->get_LastRun(This,lastRun);
}
static FORCEINLINE HRESULT IFsrmReportJob_get_LastError(IFsrmReportJob* This,BSTR *lastError) {
    return This->lpVtbl->get_LastError(This,lastError);
}
static FORCEINLINE HRESULT IFsrmReportJob_get_LastGeneratedInDirectory(IFsrmReportJob* This,BSTR *path) {
    return This->lpVtbl->get_LastGeneratedInDirectory(This,path);
}
static FORCEINLINE HRESULT IFsrmReportJob_EnumReports(IFsrmReportJob* This,IFsrmCollection **reports) {
    return This->lpVtbl->EnumReports(This,reports);
}
static FORCEINLINE HRESULT IFsrmReportJob_CreateReport(IFsrmReportJob* This,FsrmReportType reportType,IFsrmReport **report) {
    return This->lpVtbl->CreateReport(This,reportType,report);
}
static FORCEINLINE HRESULT IFsrmReportJob_Run(IFsrmReportJob* This,FsrmReportGenerationContext context) {
    return This->lpVtbl->Run(This,context);
}
static FORCEINLINE HRESULT IFsrmReportJob_WaitForCompletion(IFsrmReportJob* This,LONG waitSeconds,VARIANT_BOOL *completed) {
    return This->lpVtbl->WaitForCompletion(This,waitSeconds,completed);
}
static FORCEINLINE HRESULT IFsrmReportJob_Cancel(IFsrmReportJob* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmReportJob_get_Task_Proxy(
    IFsrmReportJob* This,
    BSTR *taskName);
void __RPC_STUB IFsrmReportJob_get_Task_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_put_Task_Proxy(
    IFsrmReportJob* This,
    BSTR taskName);
void __RPC_STUB IFsrmReportJob_put_Task_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_get_NamespaceRoots_Proxy(
    IFsrmReportJob* This,
    SAFEARRAY **namespaceRoots);
void __RPC_STUB IFsrmReportJob_get_NamespaceRoots_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_put_NamespaceRoots_Proxy(
    IFsrmReportJob* This,
    SAFEARRAY *namespaceRoots);
void __RPC_STUB IFsrmReportJob_put_NamespaceRoots_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_get_Formats_Proxy(
    IFsrmReportJob* This,
    SAFEARRAY **formats);
void __RPC_STUB IFsrmReportJob_get_Formats_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_put_Formats_Proxy(
    IFsrmReportJob* This,
    SAFEARRAY *formats);
void __RPC_STUB IFsrmReportJob_put_Formats_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_get_MailTo_Proxy(
    IFsrmReportJob* This,
    BSTR *mailTo);
void __RPC_STUB IFsrmReportJob_get_MailTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_put_MailTo_Proxy(
    IFsrmReportJob* This,
    BSTR mailTo);
void __RPC_STUB IFsrmReportJob_put_MailTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_get_RunningStatus_Proxy(
    IFsrmReportJob* This,
    FsrmReportRunningStatus *runningStatus);
void __RPC_STUB IFsrmReportJob_get_RunningStatus_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_get_LastRun_Proxy(
    IFsrmReportJob* This,
    DATE *lastRun);
void __RPC_STUB IFsrmReportJob_get_LastRun_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_get_LastError_Proxy(
    IFsrmReportJob* This,
    BSTR *lastError);
void __RPC_STUB IFsrmReportJob_get_LastError_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_get_LastGeneratedInDirectory_Proxy(
    IFsrmReportJob* This,
    BSTR *path);
void __RPC_STUB IFsrmReportJob_get_LastGeneratedInDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_EnumReports_Proxy(
    IFsrmReportJob* This,
    IFsrmCollection **reports);
void __RPC_STUB IFsrmReportJob_EnumReports_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_CreateReport_Proxy(
    IFsrmReportJob* This,
    FsrmReportType reportType,
    IFsrmReport **report);
void __RPC_STUB IFsrmReportJob_CreateReport_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_Run_Proxy(
    IFsrmReportJob* This,
    FsrmReportGenerationContext context);
void __RPC_STUB IFsrmReportJob_Run_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_WaitForCompletion_Proxy(
    IFsrmReportJob* This,
    LONG waitSeconds,
    VARIANT_BOOL *completed);
void __RPC_STUB IFsrmReportJob_WaitForCompletion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmReportJob_Cancel_Proxy(
    IFsrmReportJob* This);
void __RPC_STUB IFsrmReportJob_Cancel_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmReportJob_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileManagementJob interface
 */
#ifndef __IFsrmFileManagementJob_INTERFACE_DEFINED__
#define __IFsrmFileManagementJob_INTERFACE_DEFINED__

#define FsrmDaysNotSpecified (-1)

#define FsrmDateNotSpecified ((DATE)-1)

DEFINE_GUID(IID_IFsrmFileManagementJob, 0x0770687e, 0x9f36, 0x4d6f, 0x87,0x78, 0x59,0x9d,0x18,0x84,0x61,0xc9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0770687e-9f36-4d6f-8778-599d188461c9")
IFsrmFileManagementJob : public IFsrmObject
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NamespaceRoots(
        SAFEARRAY **namespaceRoots) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_NamespaceRoots(
        SAFEARRAY *namespaceRoots) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_OperationType(
        FsrmFileManagementType *operationType) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_OperationType(
        FsrmFileManagementType operationType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExpirationDirectory(
        BSTR *expirationDirectory) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ExpirationDirectory(
        BSTR expirationDirectory) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CustomAction(
        IFsrmActionCommand **action) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Notifications(
        SAFEARRAY **notifications) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Logging(
        LONG *loggingFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Logging(
        LONG loggingFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReportEnabled(
        VARIANT_BOOL *reportEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ReportEnabled(
        VARIANT_BOOL reportEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Formats(
        SAFEARRAY **formats) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Formats(
        SAFEARRAY *formats) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MailTo(
        BSTR *mailTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MailTo(
        BSTR mailTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DaysSinceFileCreated(
        LONG *daysSinceCreation) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DaysSinceFileCreated(
        LONG daysSinceCreation) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DaysSinceFileLastAccessed(
        LONG *daysSinceAccess) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DaysSinceFileLastAccessed(
        LONG daysSinceAccess) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DaysSinceFileLastModified(
        LONG *daysSinceModify) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DaysSinceFileLastModified(
        LONG daysSinceModify) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PropertyConditions(
        IFsrmCollection **propertyConditions) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FromDate(
        DATE *fromDate) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FromDate(
        DATE fromDate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Task(
        BSTR *taskName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Task(
        BSTR taskName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parameters(
        SAFEARRAY **parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Parameters(
        SAFEARRAY *parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RunningStatus(
        FsrmReportRunningStatus *runningStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastError(
        BSTR *lastError) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastReportPathWithoutExtension(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastRun(
        DATE *lastRun) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FileNamePattern(
        BSTR *fileNamePattern) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FileNamePattern(
        BSTR fileNamePattern) = 0;

    virtual HRESULT STDMETHODCALLTYPE Run(
        FsrmReportGenerationContext context) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitForCompletion(
        LONG waitSeconds,
        VARIANT_BOOL *completed) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddNotification(
        LONG days) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteNotification(
        LONG days) = 0;

    virtual HRESULT STDMETHODCALLTYPE ModifyNotification(
        LONG days,
        LONG newDays) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNotificationAction(
        LONG days,
        FsrmActionType actionType,
        IFsrmAction **action) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumNotificationActions(
        LONG days,
        IFsrmCollection **actions) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePropertyCondition(
        BSTR name,
        IFsrmPropertyCondition **propertyCondition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCustomAction(
        IFsrmActionCommand **customAction) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileManagementJob, 0x0770687e, 0x9f36, 0x4d6f, 0x87,0x78, 0x59,0x9d,0x18,0x84,0x61,0xc9)
#endif
#else
typedef struct IFsrmFileManagementJobVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileManagementJob* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileManagementJob* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileManagementJob* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileManagementJob* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileManagementJob* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileManagementJob* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileManagementJob* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmFileManagementJob* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmFileManagementJob* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmFileManagementJob* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileManagementJob* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmFileManagementJob* This);

    /*** IFsrmFileManagementJob methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmFileManagementJob* This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmFileManagementJob* This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_NamespaceRoots)(
        IFsrmFileManagementJob* This,
        SAFEARRAY **namespaceRoots);

    HRESULT (STDMETHODCALLTYPE *put_NamespaceRoots)(
        IFsrmFileManagementJob* This,
        SAFEARRAY *namespaceRoots);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IFsrmFileManagementJob* This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IFsrmFileManagementJob* This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_OperationType)(
        IFsrmFileManagementJob* This,
        FsrmFileManagementType *operationType);

    HRESULT (STDMETHODCALLTYPE *put_OperationType)(
        IFsrmFileManagementJob* This,
        FsrmFileManagementType operationType);

    HRESULT (STDMETHODCALLTYPE *get_ExpirationDirectory)(
        IFsrmFileManagementJob* This,
        BSTR *expirationDirectory);

    HRESULT (STDMETHODCALLTYPE *put_ExpirationDirectory)(
        IFsrmFileManagementJob* This,
        BSTR expirationDirectory);

    HRESULT (STDMETHODCALLTYPE *get_CustomAction)(
        IFsrmFileManagementJob* This,
        IFsrmActionCommand **action);

    HRESULT (STDMETHODCALLTYPE *get_Notifications)(
        IFsrmFileManagementJob* This,
        SAFEARRAY **notifications);

    HRESULT (STDMETHODCALLTYPE *get_Logging)(
        IFsrmFileManagementJob* This,
        LONG *loggingFlags);

    HRESULT (STDMETHODCALLTYPE *put_Logging)(
        IFsrmFileManagementJob* This,
        LONG loggingFlags);

    HRESULT (STDMETHODCALLTYPE *get_ReportEnabled)(
        IFsrmFileManagementJob* This,
        VARIANT_BOOL *reportEnabled);

    HRESULT (STDMETHODCALLTYPE *put_ReportEnabled)(
        IFsrmFileManagementJob* This,
        VARIANT_BOOL reportEnabled);

    HRESULT (STDMETHODCALLTYPE *get_Formats)(
        IFsrmFileManagementJob* This,
        SAFEARRAY **formats);

    HRESULT (STDMETHODCALLTYPE *put_Formats)(
        IFsrmFileManagementJob* This,
        SAFEARRAY *formats);

    HRESULT (STDMETHODCALLTYPE *get_MailTo)(
        IFsrmFileManagementJob* This,
        BSTR *mailTo);

    HRESULT (STDMETHODCALLTYPE *put_MailTo)(
        IFsrmFileManagementJob* This,
        BSTR mailTo);

    HRESULT (STDMETHODCALLTYPE *get_DaysSinceFileCreated)(
        IFsrmFileManagementJob* This,
        LONG *daysSinceCreation);

    HRESULT (STDMETHODCALLTYPE *put_DaysSinceFileCreated)(
        IFsrmFileManagementJob* This,
        LONG daysSinceCreation);

    HRESULT (STDMETHODCALLTYPE *get_DaysSinceFileLastAccessed)(
        IFsrmFileManagementJob* This,
        LONG *daysSinceAccess);

    HRESULT (STDMETHODCALLTYPE *put_DaysSinceFileLastAccessed)(
        IFsrmFileManagementJob* This,
        LONG daysSinceAccess);

    HRESULT (STDMETHODCALLTYPE *get_DaysSinceFileLastModified)(
        IFsrmFileManagementJob* This,
        LONG *daysSinceModify);

    HRESULT (STDMETHODCALLTYPE *put_DaysSinceFileLastModified)(
        IFsrmFileManagementJob* This,
        LONG daysSinceModify);

    HRESULT (STDMETHODCALLTYPE *get_PropertyConditions)(
        IFsrmFileManagementJob* This,
        IFsrmCollection **propertyConditions);

    HRESULT (STDMETHODCALLTYPE *get_FromDate)(
        IFsrmFileManagementJob* This,
        DATE *fromDate);

    HRESULT (STDMETHODCALLTYPE *put_FromDate)(
        IFsrmFileManagementJob* This,
        DATE fromDate);

    HRESULT (STDMETHODCALLTYPE *get_Task)(
        IFsrmFileManagementJob* This,
        BSTR *taskName);

    HRESULT (STDMETHODCALLTYPE *put_Task)(
        IFsrmFileManagementJob* This,
        BSTR taskName);

    HRESULT (STDMETHODCALLTYPE *get_Parameters)(
        IFsrmFileManagementJob* This,
        SAFEARRAY **parameters);

    HRESULT (STDMETHODCALLTYPE *put_Parameters)(
        IFsrmFileManagementJob* This,
        SAFEARRAY *parameters);

    HRESULT (STDMETHODCALLTYPE *get_RunningStatus)(
        IFsrmFileManagementJob* This,
        FsrmReportRunningStatus *runningStatus);

    HRESULT (STDMETHODCALLTYPE *get_LastError)(
        IFsrmFileManagementJob* This,
        BSTR *lastError);

    HRESULT (STDMETHODCALLTYPE *get_LastReportPathWithoutExtension)(
        IFsrmFileManagementJob* This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *get_LastRun)(
        IFsrmFileManagementJob* This,
        DATE *lastRun);

    HRESULT (STDMETHODCALLTYPE *get_FileNamePattern)(
        IFsrmFileManagementJob* This,
        BSTR *fileNamePattern);

    HRESULT (STDMETHODCALLTYPE *put_FileNamePattern)(
        IFsrmFileManagementJob* This,
        BSTR fileNamePattern);

    HRESULT (STDMETHODCALLTYPE *Run)(
        IFsrmFileManagementJob* This,
        FsrmReportGenerationContext context);

    HRESULT (STDMETHODCALLTYPE *WaitForCompletion)(
        IFsrmFileManagementJob* This,
        LONG waitSeconds,
        VARIANT_BOOL *completed);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IFsrmFileManagementJob* This);

    HRESULT (STDMETHODCALLTYPE *AddNotification)(
        IFsrmFileManagementJob* This,
        LONG days);

    HRESULT (STDMETHODCALLTYPE *DeleteNotification)(
        IFsrmFileManagementJob* This,
        LONG days);

    HRESULT (STDMETHODCALLTYPE *ModifyNotification)(
        IFsrmFileManagementJob* This,
        LONG days,
        LONG newDays);

    HRESULT (STDMETHODCALLTYPE *CreateNotificationAction)(
        IFsrmFileManagementJob* This,
        LONG days,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumNotificationActions)(
        IFsrmFileManagementJob* This,
        LONG days,
        IFsrmCollection **actions);

    HRESULT (STDMETHODCALLTYPE *CreatePropertyCondition)(
        IFsrmFileManagementJob* This,
        BSTR name,
        IFsrmPropertyCondition **propertyCondition);

    HRESULT (STDMETHODCALLTYPE *CreateCustomAction)(
        IFsrmFileManagementJob* This,
        IFsrmActionCommand **customAction);

    END_INTERFACE
} IFsrmFileManagementJobVtbl;
interface IFsrmFileManagementJob {
    CONST_VTBL IFsrmFileManagementJobVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileManagementJob_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileManagementJob_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileManagementJob_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileManagementJob_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileManagementJob_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileManagementJob_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileManagementJob_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmFileManagementJob_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmFileManagementJob_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmFileManagementJob_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmFileManagementJob_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmFileManagementJob_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmFileManagementJob methods ***/
#define IFsrmFileManagementJob_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmFileManagementJob_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmFileManagementJob_get_NamespaceRoots(This,namespaceRoots) (This)->lpVtbl->get_NamespaceRoots(This,namespaceRoots)
#define IFsrmFileManagementJob_put_NamespaceRoots(This,namespaceRoots) (This)->lpVtbl->put_NamespaceRoots(This,namespaceRoots)
#define IFsrmFileManagementJob_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IFsrmFileManagementJob_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#define IFsrmFileManagementJob_get_OperationType(This,operationType) (This)->lpVtbl->get_OperationType(This,operationType)
#define IFsrmFileManagementJob_put_OperationType(This,operationType) (This)->lpVtbl->put_OperationType(This,operationType)
#define IFsrmFileManagementJob_get_ExpirationDirectory(This,expirationDirectory) (This)->lpVtbl->get_ExpirationDirectory(This,expirationDirectory)
#define IFsrmFileManagementJob_put_ExpirationDirectory(This,expirationDirectory) (This)->lpVtbl->put_ExpirationDirectory(This,expirationDirectory)
#define IFsrmFileManagementJob_get_CustomAction(This,action) (This)->lpVtbl->get_CustomAction(This,action)
#define IFsrmFileManagementJob_get_Notifications(This,notifications) (This)->lpVtbl->get_Notifications(This,notifications)
#define IFsrmFileManagementJob_get_Logging(This,loggingFlags) (This)->lpVtbl->get_Logging(This,loggingFlags)
#define IFsrmFileManagementJob_put_Logging(This,loggingFlags) (This)->lpVtbl->put_Logging(This,loggingFlags)
#define IFsrmFileManagementJob_get_ReportEnabled(This,reportEnabled) (This)->lpVtbl->get_ReportEnabled(This,reportEnabled)
#define IFsrmFileManagementJob_put_ReportEnabled(This,reportEnabled) (This)->lpVtbl->put_ReportEnabled(This,reportEnabled)
#define IFsrmFileManagementJob_get_Formats(This,formats) (This)->lpVtbl->get_Formats(This,formats)
#define IFsrmFileManagementJob_put_Formats(This,formats) (This)->lpVtbl->put_Formats(This,formats)
#define IFsrmFileManagementJob_get_MailTo(This,mailTo) (This)->lpVtbl->get_MailTo(This,mailTo)
#define IFsrmFileManagementJob_put_MailTo(This,mailTo) (This)->lpVtbl->put_MailTo(This,mailTo)
#define IFsrmFileManagementJob_get_DaysSinceFileCreated(This,daysSinceCreation) (This)->lpVtbl->get_DaysSinceFileCreated(This,daysSinceCreation)
#define IFsrmFileManagementJob_put_DaysSinceFileCreated(This,daysSinceCreation) (This)->lpVtbl->put_DaysSinceFileCreated(This,daysSinceCreation)
#define IFsrmFileManagementJob_get_DaysSinceFileLastAccessed(This,daysSinceAccess) (This)->lpVtbl->get_DaysSinceFileLastAccessed(This,daysSinceAccess)
#define IFsrmFileManagementJob_put_DaysSinceFileLastAccessed(This,daysSinceAccess) (This)->lpVtbl->put_DaysSinceFileLastAccessed(This,daysSinceAccess)
#define IFsrmFileManagementJob_get_DaysSinceFileLastModified(This,daysSinceModify) (This)->lpVtbl->get_DaysSinceFileLastModified(This,daysSinceModify)
#define IFsrmFileManagementJob_put_DaysSinceFileLastModified(This,daysSinceModify) (This)->lpVtbl->put_DaysSinceFileLastModified(This,daysSinceModify)
#define IFsrmFileManagementJob_get_PropertyConditions(This,propertyConditions) (This)->lpVtbl->get_PropertyConditions(This,propertyConditions)
#define IFsrmFileManagementJob_get_FromDate(This,fromDate) (This)->lpVtbl->get_FromDate(This,fromDate)
#define IFsrmFileManagementJob_put_FromDate(This,fromDate) (This)->lpVtbl->put_FromDate(This,fromDate)
#define IFsrmFileManagementJob_get_Task(This,taskName) (This)->lpVtbl->get_Task(This,taskName)
#define IFsrmFileManagementJob_put_Task(This,taskName) (This)->lpVtbl->put_Task(This,taskName)
#define IFsrmFileManagementJob_get_Parameters(This,parameters) (This)->lpVtbl->get_Parameters(This,parameters)
#define IFsrmFileManagementJob_put_Parameters(This,parameters) (This)->lpVtbl->put_Parameters(This,parameters)
#define IFsrmFileManagementJob_get_RunningStatus(This,runningStatus) (This)->lpVtbl->get_RunningStatus(This,runningStatus)
#define IFsrmFileManagementJob_get_LastError(This,lastError) (This)->lpVtbl->get_LastError(This,lastError)
#define IFsrmFileManagementJob_get_LastReportPathWithoutExtension(This,path) (This)->lpVtbl->get_LastReportPathWithoutExtension(This,path)
#define IFsrmFileManagementJob_get_LastRun(This,lastRun) (This)->lpVtbl->get_LastRun(This,lastRun)
#define IFsrmFileManagementJob_get_FileNamePattern(This,fileNamePattern) (This)->lpVtbl->get_FileNamePattern(This,fileNamePattern)
#define IFsrmFileManagementJob_put_FileNamePattern(This,fileNamePattern) (This)->lpVtbl->put_FileNamePattern(This,fileNamePattern)
#define IFsrmFileManagementJob_Run(This,context) (This)->lpVtbl->Run(This,context)
#define IFsrmFileManagementJob_WaitForCompletion(This,waitSeconds,completed) (This)->lpVtbl->WaitForCompletion(This,waitSeconds,completed)
#define IFsrmFileManagementJob_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IFsrmFileManagementJob_AddNotification(This,days) (This)->lpVtbl->AddNotification(This,days)
#define IFsrmFileManagementJob_DeleteNotification(This,days) (This)->lpVtbl->DeleteNotification(This,days)
#define IFsrmFileManagementJob_ModifyNotification(This,days,newDays) (This)->lpVtbl->ModifyNotification(This,days,newDays)
#define IFsrmFileManagementJob_CreateNotificationAction(This,days,actionType,action) (This)->lpVtbl->CreateNotificationAction(This,days,actionType,action)
#define IFsrmFileManagementJob_EnumNotificationActions(This,days,actions) (This)->lpVtbl->EnumNotificationActions(This,days,actions)
#define IFsrmFileManagementJob_CreatePropertyCondition(This,name,propertyCondition) (This)->lpVtbl->CreatePropertyCondition(This,name,propertyCondition)
#define IFsrmFileManagementJob_CreateCustomAction(This,customAction) (This)->lpVtbl->CreateCustomAction(This,customAction)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmFileManagementJob_QueryInterface(IFsrmFileManagementJob* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmFileManagementJob_AddRef(IFsrmFileManagementJob* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmFileManagementJob_Release(IFsrmFileManagementJob* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmFileManagementJob_GetTypeInfoCount(IFsrmFileManagementJob* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_GetTypeInfo(IFsrmFileManagementJob* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_GetIDsOfNames(IFsrmFileManagementJob* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_Invoke(IFsrmFileManagementJob* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Id(IFsrmFileManagementJob* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Description(IFsrmFileManagementJob* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_Description(IFsrmFileManagementJob* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_Delete(IFsrmFileManagementJob* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_Commit(IFsrmFileManagementJob* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmFileManagementJob methods ***/
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Name(IFsrmFileManagementJob* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_Name(IFsrmFileManagementJob* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_NamespaceRoots(IFsrmFileManagementJob* This,SAFEARRAY **namespaceRoots) {
    return This->lpVtbl->get_NamespaceRoots(This,namespaceRoots);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_NamespaceRoots(IFsrmFileManagementJob* This,SAFEARRAY *namespaceRoots) {
    return This->lpVtbl->put_NamespaceRoots(This,namespaceRoots);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Enabled(IFsrmFileManagementJob* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_Enabled(IFsrmFileManagementJob* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_OperationType(IFsrmFileManagementJob* This,FsrmFileManagementType *operationType) {
    return This->lpVtbl->get_OperationType(This,operationType);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_OperationType(IFsrmFileManagementJob* This,FsrmFileManagementType operationType) {
    return This->lpVtbl->put_OperationType(This,operationType);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_ExpirationDirectory(IFsrmFileManagementJob* This,BSTR *expirationDirectory) {
    return This->lpVtbl->get_ExpirationDirectory(This,expirationDirectory);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_ExpirationDirectory(IFsrmFileManagementJob* This,BSTR expirationDirectory) {
    return This->lpVtbl->put_ExpirationDirectory(This,expirationDirectory);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_CustomAction(IFsrmFileManagementJob* This,IFsrmActionCommand **action) {
    return This->lpVtbl->get_CustomAction(This,action);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Notifications(IFsrmFileManagementJob* This,SAFEARRAY **notifications) {
    return This->lpVtbl->get_Notifications(This,notifications);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Logging(IFsrmFileManagementJob* This,LONG *loggingFlags) {
    return This->lpVtbl->get_Logging(This,loggingFlags);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_Logging(IFsrmFileManagementJob* This,LONG loggingFlags) {
    return This->lpVtbl->put_Logging(This,loggingFlags);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_ReportEnabled(IFsrmFileManagementJob* This,VARIANT_BOOL *reportEnabled) {
    return This->lpVtbl->get_ReportEnabled(This,reportEnabled);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_ReportEnabled(IFsrmFileManagementJob* This,VARIANT_BOOL reportEnabled) {
    return This->lpVtbl->put_ReportEnabled(This,reportEnabled);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Formats(IFsrmFileManagementJob* This,SAFEARRAY **formats) {
    return This->lpVtbl->get_Formats(This,formats);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_Formats(IFsrmFileManagementJob* This,SAFEARRAY *formats) {
    return This->lpVtbl->put_Formats(This,formats);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_MailTo(IFsrmFileManagementJob* This,BSTR *mailTo) {
    return This->lpVtbl->get_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_MailTo(IFsrmFileManagementJob* This,BSTR mailTo) {
    return This->lpVtbl->put_MailTo(This,mailTo);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_DaysSinceFileCreated(IFsrmFileManagementJob* This,LONG *daysSinceCreation) {
    return This->lpVtbl->get_DaysSinceFileCreated(This,daysSinceCreation);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_DaysSinceFileCreated(IFsrmFileManagementJob* This,LONG daysSinceCreation) {
    return This->lpVtbl->put_DaysSinceFileCreated(This,daysSinceCreation);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_DaysSinceFileLastAccessed(IFsrmFileManagementJob* This,LONG *daysSinceAccess) {
    return This->lpVtbl->get_DaysSinceFileLastAccessed(This,daysSinceAccess);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_DaysSinceFileLastAccessed(IFsrmFileManagementJob* This,LONG daysSinceAccess) {
    return This->lpVtbl->put_DaysSinceFileLastAccessed(This,daysSinceAccess);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_DaysSinceFileLastModified(IFsrmFileManagementJob* This,LONG *daysSinceModify) {
    return This->lpVtbl->get_DaysSinceFileLastModified(This,daysSinceModify);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_DaysSinceFileLastModified(IFsrmFileManagementJob* This,LONG daysSinceModify) {
    return This->lpVtbl->put_DaysSinceFileLastModified(This,daysSinceModify);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_PropertyConditions(IFsrmFileManagementJob* This,IFsrmCollection **propertyConditions) {
    return This->lpVtbl->get_PropertyConditions(This,propertyConditions);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_FromDate(IFsrmFileManagementJob* This,DATE *fromDate) {
    return This->lpVtbl->get_FromDate(This,fromDate);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_FromDate(IFsrmFileManagementJob* This,DATE fromDate) {
    return This->lpVtbl->put_FromDate(This,fromDate);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Task(IFsrmFileManagementJob* This,BSTR *taskName) {
    return This->lpVtbl->get_Task(This,taskName);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_Task(IFsrmFileManagementJob* This,BSTR taskName) {
    return This->lpVtbl->put_Task(This,taskName);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_Parameters(IFsrmFileManagementJob* This,SAFEARRAY **parameters) {
    return This->lpVtbl->get_Parameters(This,parameters);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_Parameters(IFsrmFileManagementJob* This,SAFEARRAY *parameters) {
    return This->lpVtbl->put_Parameters(This,parameters);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_RunningStatus(IFsrmFileManagementJob* This,FsrmReportRunningStatus *runningStatus) {
    return This->lpVtbl->get_RunningStatus(This,runningStatus);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_LastError(IFsrmFileManagementJob* This,BSTR *lastError) {
    return This->lpVtbl->get_LastError(This,lastError);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_LastReportPathWithoutExtension(IFsrmFileManagementJob* This,BSTR *path) {
    return This->lpVtbl->get_LastReportPathWithoutExtension(This,path);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_LastRun(IFsrmFileManagementJob* This,DATE *lastRun) {
    return This->lpVtbl->get_LastRun(This,lastRun);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_get_FileNamePattern(IFsrmFileManagementJob* This,BSTR *fileNamePattern) {
    return This->lpVtbl->get_FileNamePattern(This,fileNamePattern);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_put_FileNamePattern(IFsrmFileManagementJob* This,BSTR fileNamePattern) {
    return This->lpVtbl->put_FileNamePattern(This,fileNamePattern);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_Run(IFsrmFileManagementJob* This,FsrmReportGenerationContext context) {
    return This->lpVtbl->Run(This,context);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_WaitForCompletion(IFsrmFileManagementJob* This,LONG waitSeconds,VARIANT_BOOL *completed) {
    return This->lpVtbl->WaitForCompletion(This,waitSeconds,completed);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_Cancel(IFsrmFileManagementJob* This) {
    return This->lpVtbl->Cancel(This);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_AddNotification(IFsrmFileManagementJob* This,LONG days) {
    return This->lpVtbl->AddNotification(This,days);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_DeleteNotification(IFsrmFileManagementJob* This,LONG days) {
    return This->lpVtbl->DeleteNotification(This,days);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_ModifyNotification(IFsrmFileManagementJob* This,LONG days,LONG newDays) {
    return This->lpVtbl->ModifyNotification(This,days,newDays);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_CreateNotificationAction(IFsrmFileManagementJob* This,LONG days,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateNotificationAction(This,days,actionType,action);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_EnumNotificationActions(IFsrmFileManagementJob* This,LONG days,IFsrmCollection **actions) {
    return This->lpVtbl->EnumNotificationActions(This,days,actions);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_CreatePropertyCondition(IFsrmFileManagementJob* This,BSTR name,IFsrmPropertyCondition **propertyCondition) {
    return This->lpVtbl->CreatePropertyCondition(This,name,propertyCondition);
}
static FORCEINLINE HRESULT IFsrmFileManagementJob_CreateCustomAction(IFsrmFileManagementJob* This,IFsrmActionCommand **customAction) {
    return This->lpVtbl->CreateCustomAction(This,customAction);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_Name_Proxy(
    IFsrmFileManagementJob* This,
    BSTR *name);
void __RPC_STUB IFsrmFileManagementJob_get_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_Name_Proxy(
    IFsrmFileManagementJob* This,
    BSTR name);
void __RPC_STUB IFsrmFileManagementJob_put_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_NamespaceRoots_Proxy(
    IFsrmFileManagementJob* This,
    SAFEARRAY **namespaceRoots);
void __RPC_STUB IFsrmFileManagementJob_get_NamespaceRoots_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_NamespaceRoots_Proxy(
    IFsrmFileManagementJob* This,
    SAFEARRAY *namespaceRoots);
void __RPC_STUB IFsrmFileManagementJob_put_NamespaceRoots_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_Enabled_Proxy(
    IFsrmFileManagementJob* This,
    VARIANT_BOOL *enabled);
void __RPC_STUB IFsrmFileManagementJob_get_Enabled_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_Enabled_Proxy(
    IFsrmFileManagementJob* This,
    VARIANT_BOOL enabled);
void __RPC_STUB IFsrmFileManagementJob_put_Enabled_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_OperationType_Proxy(
    IFsrmFileManagementJob* This,
    FsrmFileManagementType *operationType);
void __RPC_STUB IFsrmFileManagementJob_get_OperationType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_OperationType_Proxy(
    IFsrmFileManagementJob* This,
    FsrmFileManagementType operationType);
void __RPC_STUB IFsrmFileManagementJob_put_OperationType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_ExpirationDirectory_Proxy(
    IFsrmFileManagementJob* This,
    BSTR *expirationDirectory);
void __RPC_STUB IFsrmFileManagementJob_get_ExpirationDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_ExpirationDirectory_Proxy(
    IFsrmFileManagementJob* This,
    BSTR expirationDirectory);
void __RPC_STUB IFsrmFileManagementJob_put_ExpirationDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_CustomAction_Proxy(
    IFsrmFileManagementJob* This,
    IFsrmActionCommand **action);
void __RPC_STUB IFsrmFileManagementJob_get_CustomAction_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_Notifications_Proxy(
    IFsrmFileManagementJob* This,
    SAFEARRAY **notifications);
void __RPC_STUB IFsrmFileManagementJob_get_Notifications_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_Logging_Proxy(
    IFsrmFileManagementJob* This,
    LONG *loggingFlags);
void __RPC_STUB IFsrmFileManagementJob_get_Logging_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_Logging_Proxy(
    IFsrmFileManagementJob* This,
    LONG loggingFlags);
void __RPC_STUB IFsrmFileManagementJob_put_Logging_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_ReportEnabled_Proxy(
    IFsrmFileManagementJob* This,
    VARIANT_BOOL *reportEnabled);
void __RPC_STUB IFsrmFileManagementJob_get_ReportEnabled_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_ReportEnabled_Proxy(
    IFsrmFileManagementJob* This,
    VARIANT_BOOL reportEnabled);
void __RPC_STUB IFsrmFileManagementJob_put_ReportEnabled_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_Formats_Proxy(
    IFsrmFileManagementJob* This,
    SAFEARRAY **formats);
void __RPC_STUB IFsrmFileManagementJob_get_Formats_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_Formats_Proxy(
    IFsrmFileManagementJob* This,
    SAFEARRAY *formats);
void __RPC_STUB IFsrmFileManagementJob_put_Formats_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_MailTo_Proxy(
    IFsrmFileManagementJob* This,
    BSTR *mailTo);
void __RPC_STUB IFsrmFileManagementJob_get_MailTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_MailTo_Proxy(
    IFsrmFileManagementJob* This,
    BSTR mailTo);
void __RPC_STUB IFsrmFileManagementJob_put_MailTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_DaysSinceFileCreated_Proxy(
    IFsrmFileManagementJob* This,
    LONG *daysSinceCreation);
void __RPC_STUB IFsrmFileManagementJob_get_DaysSinceFileCreated_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_DaysSinceFileCreated_Proxy(
    IFsrmFileManagementJob* This,
    LONG daysSinceCreation);
void __RPC_STUB IFsrmFileManagementJob_put_DaysSinceFileCreated_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_DaysSinceFileLastAccessed_Proxy(
    IFsrmFileManagementJob* This,
    LONG *daysSinceAccess);
void __RPC_STUB IFsrmFileManagementJob_get_DaysSinceFileLastAccessed_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_DaysSinceFileLastAccessed_Proxy(
    IFsrmFileManagementJob* This,
    LONG daysSinceAccess);
void __RPC_STUB IFsrmFileManagementJob_put_DaysSinceFileLastAccessed_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_DaysSinceFileLastModified_Proxy(
    IFsrmFileManagementJob* This,
    LONG *daysSinceModify);
void __RPC_STUB IFsrmFileManagementJob_get_DaysSinceFileLastModified_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_DaysSinceFileLastModified_Proxy(
    IFsrmFileManagementJob* This,
    LONG daysSinceModify);
void __RPC_STUB IFsrmFileManagementJob_put_DaysSinceFileLastModified_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_PropertyConditions_Proxy(
    IFsrmFileManagementJob* This,
    IFsrmCollection **propertyConditions);
void __RPC_STUB IFsrmFileManagementJob_get_PropertyConditions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_FromDate_Proxy(
    IFsrmFileManagementJob* This,
    DATE *fromDate);
void __RPC_STUB IFsrmFileManagementJob_get_FromDate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_FromDate_Proxy(
    IFsrmFileManagementJob* This,
    DATE fromDate);
void __RPC_STUB IFsrmFileManagementJob_put_FromDate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_Task_Proxy(
    IFsrmFileManagementJob* This,
    BSTR *taskName);
void __RPC_STUB IFsrmFileManagementJob_get_Task_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_Task_Proxy(
    IFsrmFileManagementJob* This,
    BSTR taskName);
void __RPC_STUB IFsrmFileManagementJob_put_Task_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_Parameters_Proxy(
    IFsrmFileManagementJob* This,
    SAFEARRAY **parameters);
void __RPC_STUB IFsrmFileManagementJob_get_Parameters_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_Parameters_Proxy(
    IFsrmFileManagementJob* This,
    SAFEARRAY *parameters);
void __RPC_STUB IFsrmFileManagementJob_put_Parameters_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_RunningStatus_Proxy(
    IFsrmFileManagementJob* This,
    FsrmReportRunningStatus *runningStatus);
void __RPC_STUB IFsrmFileManagementJob_get_RunningStatus_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_LastError_Proxy(
    IFsrmFileManagementJob* This,
    BSTR *lastError);
void __RPC_STUB IFsrmFileManagementJob_get_LastError_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_LastReportPathWithoutExtension_Proxy(
    IFsrmFileManagementJob* This,
    BSTR *path);
void __RPC_STUB IFsrmFileManagementJob_get_LastReportPathWithoutExtension_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_LastRun_Proxy(
    IFsrmFileManagementJob* This,
    DATE *lastRun);
void __RPC_STUB IFsrmFileManagementJob_get_LastRun_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_get_FileNamePattern_Proxy(
    IFsrmFileManagementJob* This,
    BSTR *fileNamePattern);
void __RPC_STUB IFsrmFileManagementJob_get_FileNamePattern_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_put_FileNamePattern_Proxy(
    IFsrmFileManagementJob* This,
    BSTR fileNamePattern);
void __RPC_STUB IFsrmFileManagementJob_put_FileNamePattern_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_Run_Proxy(
    IFsrmFileManagementJob* This,
    FsrmReportGenerationContext context);
void __RPC_STUB IFsrmFileManagementJob_Run_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_WaitForCompletion_Proxy(
    IFsrmFileManagementJob* This,
    LONG waitSeconds,
    VARIANT_BOOL *completed);
void __RPC_STUB IFsrmFileManagementJob_WaitForCompletion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_Cancel_Proxy(
    IFsrmFileManagementJob* This);
void __RPC_STUB IFsrmFileManagementJob_Cancel_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_AddNotification_Proxy(
    IFsrmFileManagementJob* This,
    LONG days);
void __RPC_STUB IFsrmFileManagementJob_AddNotification_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_DeleteNotification_Proxy(
    IFsrmFileManagementJob* This,
    LONG days);
void __RPC_STUB IFsrmFileManagementJob_DeleteNotification_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_ModifyNotification_Proxy(
    IFsrmFileManagementJob* This,
    LONG days,
    LONG newDays);
void __RPC_STUB IFsrmFileManagementJob_ModifyNotification_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_CreateNotificationAction_Proxy(
    IFsrmFileManagementJob* This,
    LONG days,
    FsrmActionType actionType,
    IFsrmAction **action);
void __RPC_STUB IFsrmFileManagementJob_CreateNotificationAction_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_EnumNotificationActions_Proxy(
    IFsrmFileManagementJob* This,
    LONG days,
    IFsrmCollection **actions);
void __RPC_STUB IFsrmFileManagementJob_EnumNotificationActions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_CreatePropertyCondition_Proxy(
    IFsrmFileManagementJob* This,
    BSTR name,
    IFsrmPropertyCondition **propertyCondition);
void __RPC_STUB IFsrmFileManagementJob_CreatePropertyCondition_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileManagementJob_CreateCustomAction_Proxy(
    IFsrmFileManagementJob* This,
    IFsrmActionCommand **customAction);
void __RPC_STUB IFsrmFileManagementJob_CreateCustomAction_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmFileManagementJob_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileCondition interface
 */
#ifndef __IFsrmFileCondition_INTERFACE_DEFINED__
#define __IFsrmFileCondition_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileCondition, 0x70684ffc, 0x691a, 0x4a1a, 0xb9,0x22, 0x97,0x75,0x2e,0x13,0x8c,0xc1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("70684ffc-691a-4a1a-b922-97752e138cc1")
IFsrmFileCondition : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Type(
        FsrmFileConditionType *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileCondition, 0x70684ffc, 0x691a, 0x4a1a, 0xb9,0x22, 0x97,0x75,0x2e,0x13,0x8c,0xc1)
#endif
#else
typedef struct IFsrmFileConditionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileCondition* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileCondition* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileCondition* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileCondition* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileCondition* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileCondition* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileCondition* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmFileCondition methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IFsrmFileCondition* This,
        FsrmFileConditionType *pVal);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileCondition* This);

    END_INTERFACE
} IFsrmFileConditionVtbl;
interface IFsrmFileCondition {
    CONST_VTBL IFsrmFileConditionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileCondition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileCondition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileCondition_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileCondition_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileCondition_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileCondition_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileCondition_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmFileCondition methods ***/
#define IFsrmFileCondition_get_Type(This,pVal) (This)->lpVtbl->get_Type(This,pVal)
#define IFsrmFileCondition_Delete(This) (This)->lpVtbl->Delete(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmFileCondition_QueryInterface(IFsrmFileCondition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmFileCondition_AddRef(IFsrmFileCondition* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmFileCondition_Release(IFsrmFileCondition* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmFileCondition_GetTypeInfoCount(IFsrmFileCondition* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmFileCondition_GetTypeInfo(IFsrmFileCondition* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmFileCondition_GetIDsOfNames(IFsrmFileCondition* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmFileCondition_Invoke(IFsrmFileCondition* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmFileCondition methods ***/
static FORCEINLINE HRESULT IFsrmFileCondition_get_Type(IFsrmFileCondition* This,FsrmFileConditionType *pVal) {
    return This->lpVtbl->get_Type(This,pVal);
}
static FORCEINLINE HRESULT IFsrmFileCondition_Delete(IFsrmFileCondition* This) {
    return This->lpVtbl->Delete(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmFileCondition_get_Type_Proxy(
    IFsrmFileCondition* This,
    FsrmFileConditionType *pVal);
void __RPC_STUB IFsrmFileCondition_get_Type_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileCondition_Delete_Proxy(
    IFsrmFileCondition* This);
void __RPC_STUB IFsrmFileCondition_Delete_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmFileCondition_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileConditionProperty interface
 */
#ifndef __IFsrmFileConditionProperty_INTERFACE_DEFINED__
#define __IFsrmFileConditionProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileConditionProperty, 0x81926775, 0xb981, 0x4479, 0x98,0x8f, 0xda,0x17,0x1d,0x62,0x73,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("81926775-b981-4479-988f-da171d627360")
IFsrmFileConditionProperty : public IFsrmFileCondition
{
    virtual HRESULT STDMETHODCALLTYPE get_PropertyName(
        BSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PropertyName(
        BSTR newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PropertyId(
        FsrmFileSystemPropertyId *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PropertyId(
        FsrmFileSystemPropertyId newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Operator(
        FsrmPropertyConditionType *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Operator(
        FsrmPropertyConditionType newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ValueType(
        FsrmPropertyValueType *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ValueType(
        FsrmPropertyValueType newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Value(
        VARIANT *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Value(
        VARIANT newVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileConditionProperty, 0x81926775, 0xb981, 0x4479, 0x98,0x8f, 0xda,0x17,0x1d,0x62,0x73,0x60)
#endif
#else
typedef struct IFsrmFileConditionPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileConditionProperty* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileConditionProperty* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileConditionProperty* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileConditionProperty* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileConditionProperty* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileConditionProperty* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileConditionProperty* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmFileCondition methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IFsrmFileConditionProperty* This,
        FsrmFileConditionType *pVal);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileConditionProperty* This);

    /*** IFsrmFileConditionProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PropertyName)(
        IFsrmFileConditionProperty* This,
        BSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *put_PropertyName)(
        IFsrmFileConditionProperty* This,
        BSTR newVal);

    HRESULT (STDMETHODCALLTYPE *get_PropertyId)(
        IFsrmFileConditionProperty* This,
        FsrmFileSystemPropertyId *pVal);

    HRESULT (STDMETHODCALLTYPE *put_PropertyId)(
        IFsrmFileConditionProperty* This,
        FsrmFileSystemPropertyId newVal);

    HRESULT (STDMETHODCALLTYPE *get_Operator)(
        IFsrmFileConditionProperty* This,
        FsrmPropertyConditionType *pVal);

    HRESULT (STDMETHODCALLTYPE *put_Operator)(
        IFsrmFileConditionProperty* This,
        FsrmPropertyConditionType newVal);

    HRESULT (STDMETHODCALLTYPE *get_ValueType)(
        IFsrmFileConditionProperty* This,
        FsrmPropertyValueType *pVal);

    HRESULT (STDMETHODCALLTYPE *put_ValueType)(
        IFsrmFileConditionProperty* This,
        FsrmPropertyValueType newVal);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        IFsrmFileConditionProperty* This,
        VARIANT *pVal);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        IFsrmFileConditionProperty* This,
        VARIANT newVal);

    END_INTERFACE
} IFsrmFileConditionPropertyVtbl;
interface IFsrmFileConditionProperty {
    CONST_VTBL IFsrmFileConditionPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileConditionProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileConditionProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileConditionProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileConditionProperty_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileConditionProperty_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileConditionProperty_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileConditionProperty_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmFileCondition methods ***/
#define IFsrmFileConditionProperty_get_Type(This,pVal) (This)->lpVtbl->get_Type(This,pVal)
#define IFsrmFileConditionProperty_Delete(This) (This)->lpVtbl->Delete(This)
/*** IFsrmFileConditionProperty methods ***/
#define IFsrmFileConditionProperty_get_PropertyName(This,pVal) (This)->lpVtbl->get_PropertyName(This,pVal)
#define IFsrmFileConditionProperty_put_PropertyName(This,newVal) (This)->lpVtbl->put_PropertyName(This,newVal)
#define IFsrmFileConditionProperty_get_PropertyId(This,pVal) (This)->lpVtbl->get_PropertyId(This,pVal)
#define IFsrmFileConditionProperty_put_PropertyId(This,newVal) (This)->lpVtbl->put_PropertyId(This,newVal)
#define IFsrmFileConditionProperty_get_Operator(This,pVal) (This)->lpVtbl->get_Operator(This,pVal)
#define IFsrmFileConditionProperty_put_Operator(This,newVal) (This)->lpVtbl->put_Operator(This,newVal)
#define IFsrmFileConditionProperty_get_ValueType(This,pVal) (This)->lpVtbl->get_ValueType(This,pVal)
#define IFsrmFileConditionProperty_put_ValueType(This,newVal) (This)->lpVtbl->put_ValueType(This,newVal)
#define IFsrmFileConditionProperty_get_Value(This,pVal) (This)->lpVtbl->get_Value(This,pVal)
#define IFsrmFileConditionProperty_put_Value(This,newVal) (This)->lpVtbl->put_Value(This,newVal)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmFileConditionProperty_QueryInterface(IFsrmFileConditionProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmFileConditionProperty_AddRef(IFsrmFileConditionProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmFileConditionProperty_Release(IFsrmFileConditionProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmFileConditionProperty_GetTypeInfoCount(IFsrmFileConditionProperty* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_GetTypeInfo(IFsrmFileConditionProperty* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_GetIDsOfNames(IFsrmFileConditionProperty* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_Invoke(IFsrmFileConditionProperty* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmFileCondition methods ***/
static FORCEINLINE HRESULT IFsrmFileConditionProperty_get_Type(IFsrmFileConditionProperty* This,FsrmFileConditionType *pVal) {
    return This->lpVtbl->get_Type(This,pVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_Delete(IFsrmFileConditionProperty* This) {
    return This->lpVtbl->Delete(This);
}
/*** IFsrmFileConditionProperty methods ***/
static FORCEINLINE HRESULT IFsrmFileConditionProperty_get_PropertyName(IFsrmFileConditionProperty* This,BSTR *pVal) {
    return This->lpVtbl->get_PropertyName(This,pVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_put_PropertyName(IFsrmFileConditionProperty* This,BSTR newVal) {
    return This->lpVtbl->put_PropertyName(This,newVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_get_PropertyId(IFsrmFileConditionProperty* This,FsrmFileSystemPropertyId *pVal) {
    return This->lpVtbl->get_PropertyId(This,pVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_put_PropertyId(IFsrmFileConditionProperty* This,FsrmFileSystemPropertyId newVal) {
    return This->lpVtbl->put_PropertyId(This,newVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_get_Operator(IFsrmFileConditionProperty* This,FsrmPropertyConditionType *pVal) {
    return This->lpVtbl->get_Operator(This,pVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_put_Operator(IFsrmFileConditionProperty* This,FsrmPropertyConditionType newVal) {
    return This->lpVtbl->put_Operator(This,newVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_get_ValueType(IFsrmFileConditionProperty* This,FsrmPropertyValueType *pVal) {
    return This->lpVtbl->get_ValueType(This,pVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_put_ValueType(IFsrmFileConditionProperty* This,FsrmPropertyValueType newVal) {
    return This->lpVtbl->put_ValueType(This,newVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_get_Value(IFsrmFileConditionProperty* This,VARIANT *pVal) {
    return This->lpVtbl->get_Value(This,pVal);
}
static FORCEINLINE HRESULT IFsrmFileConditionProperty_put_Value(IFsrmFileConditionProperty* This,VARIANT newVal) {
    return This->lpVtbl->put_Value(This,newVal);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_get_PropertyName_Proxy(
    IFsrmFileConditionProperty* This,
    BSTR *pVal);
void __RPC_STUB IFsrmFileConditionProperty_get_PropertyName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_put_PropertyName_Proxy(
    IFsrmFileConditionProperty* This,
    BSTR newVal);
void __RPC_STUB IFsrmFileConditionProperty_put_PropertyName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_get_PropertyId_Proxy(
    IFsrmFileConditionProperty* This,
    FsrmFileSystemPropertyId *pVal);
void __RPC_STUB IFsrmFileConditionProperty_get_PropertyId_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_put_PropertyId_Proxy(
    IFsrmFileConditionProperty* This,
    FsrmFileSystemPropertyId newVal);
void __RPC_STUB IFsrmFileConditionProperty_put_PropertyId_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_get_Operator_Proxy(
    IFsrmFileConditionProperty* This,
    FsrmPropertyConditionType *pVal);
void __RPC_STUB IFsrmFileConditionProperty_get_Operator_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_put_Operator_Proxy(
    IFsrmFileConditionProperty* This,
    FsrmPropertyConditionType newVal);
void __RPC_STUB IFsrmFileConditionProperty_put_Operator_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_get_ValueType_Proxy(
    IFsrmFileConditionProperty* This,
    FsrmPropertyValueType *pVal);
void __RPC_STUB IFsrmFileConditionProperty_get_ValueType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_put_ValueType_Proxy(
    IFsrmFileConditionProperty* This,
    FsrmPropertyValueType newVal);
void __RPC_STUB IFsrmFileConditionProperty_put_ValueType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_get_Value_Proxy(
    IFsrmFileConditionProperty* This,
    VARIANT *pVal);
void __RPC_STUB IFsrmFileConditionProperty_get_Value_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmFileConditionProperty_put_Value_Proxy(
    IFsrmFileConditionProperty* This,
    VARIANT newVal);
void __RPC_STUB IFsrmFileConditionProperty_put_Value_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmFileConditionProperty_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __fsrmreports_h__ */
