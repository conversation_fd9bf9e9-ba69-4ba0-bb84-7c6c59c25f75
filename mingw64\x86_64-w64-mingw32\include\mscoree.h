/*** Autogenerated by WIDL 3.1 from include/mscoree.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mscoree_h__
#define __mscoree_h__

/* Forward declarations */

#ifndef __IGCThreadControl_FWD_DEFINED__
#define __IGCThreadControl_FWD_DEFINED__
typedef interface IGCThreadControl IGCThreadControl;
#ifdef __cplusplus
interface IGCThreadControl;
#endif /* __cplusplus */
#endif

#ifndef __IGCHostControl_FWD_DEFINED__
#define __IGCHostControl_FWD_DEFINED__
typedef interface IGCHostControl IGCHostControl;
#ifdef __cplusplus
interface IGCHostControl;
#endif /* __cplusplus */
#endif

#ifndef __IDebuggerThreadControl_FWD_DEFINED__
#define __IDebuggerThreadControl_FWD_DEFINED__
typedef interface IDebuggerThreadControl IDebuggerThreadControl;
#ifdef __cplusplus
interface IDebuggerThreadControl;
#endif /* __cplusplus */
#endif

#ifndef __ICorConfiguration_FWD_DEFINED__
#define __ICorConfiguration_FWD_DEFINED__
typedef interface ICorConfiguration ICorConfiguration;
#ifdef __cplusplus
interface ICorConfiguration;
#endif /* __cplusplus */
#endif

#ifndef __ICLRControl_FWD_DEFINED__
#define __ICLRControl_FWD_DEFINED__
typedef interface ICLRControl ICLRControl;
#ifdef __cplusplus
interface ICLRControl;
#endif /* __cplusplus */
#endif

#ifndef __IHostControl_FWD_DEFINED__
#define __IHostControl_FWD_DEFINED__
typedef interface IHostControl IHostControl;
#ifdef __cplusplus
interface IHostControl;
#endif /* __cplusplus */
#endif

#ifndef __ICorRuntimeHost_FWD_DEFINED__
#define __ICorRuntimeHost_FWD_DEFINED__
typedef interface ICorRuntimeHost ICorRuntimeHost;
#ifdef __cplusplus
interface ICorRuntimeHost;
#endif /* __cplusplus */
#endif

#ifndef __ICLRRuntimeHost_FWD_DEFINED__
#define __ICLRRuntimeHost_FWD_DEFINED__
typedef interface ICLRRuntimeHost ICLRRuntimeHost;
#ifdef __cplusplus
interface ICLRRuntimeHost;
#endif /* __cplusplus */
#endif

#ifndef __IManagedObject_FWD_DEFINED__
#define __IManagedObject_FWD_DEFINED__
typedef interface IManagedObject IManagedObject;
#ifdef __cplusplus
interface IManagedObject;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

/* FIXME: #include <gcghost.h> */
/* FIXME: #include <ivalidator.h> */
HRESULT     WINAPI CorBindToRuntimeHost(LPCWSTR,LPCWSTR,LPCWSTR,VOID*,DWORD,REFCLSID,REFIID,LPVOID*);
void        WINAPI CorExitProcess(int);
HRESULT     WINAPI GetCORSystemDirectory(LPWSTR,DWORD,DWORD*);
HRESULT     WINAPI GetCORVersion(LPWSTR,DWORD,DWORD*);
HRESULT     WINAPI GetRequestedRuntimeInfo(LPCWSTR,LPCWSTR,LPCWSTR,DWORD,DWORD,LPWSTR,DWORD,DWORD*,LPWSTR,DWORD,DWORD*);
HRESULT     WINAPI LoadLibraryShim(LPCWSTR,LPCWSTR,LPVOID,HMODULE*);
#ifdef WINE_STRICT_PROTOTYPES
typedef HRESULT (__stdcall *FLockClrVersionCallback)(void);
#else
typedef HRESULT (__stdcall *FLockClrVersionCallback)();
#endif
HRESULT     WINAPI LockClrVersion(FLockClrVersionCallback,FLockClrVersionCallback*,FLockClrVersionCallback*);
typedef void *HDOMAINENUM;
typedef enum RUNTIME_INFO_FLAGS {
    RUNTIME_INFO_UPGRADE_VERSION = 0x1,
    RUNTIME_INFO_REQUEST_IA64 = 0x2,
    RUNTIME_INFO_REQUEST_AMD64 = 0x4,
    RUNTIME_INFO_REQUEST_X86 = 0x8,
    RUNTIME_INFO_DONT_RETURN_DIRECTORY = 0x10,
    RUNTIME_INFO_DONT_RETURN_VERSION = 0x20,
    RUNTIME_INFO_DONT_SHOW_ERROR_DIALOG = 0x40
} RUNTIME_INFO_FLAGS;
typedef HRESULT (__stdcall *FExecuteInAppDomainCallback)(void *cookie);
/*****************************************************************************
 * IGCThreadControl interface
 */
#ifndef __IGCThreadControl_INTERFACE_DEFINED__
#define __IGCThreadControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IGCThreadControl, 0xf31d1788, 0xc397, 0x4725, 0x87,0xa5, 0x6a,0xf3,0x47,0x2c,0x27,0x91);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f31d1788-c397-4725-87a5-6af3472c2791")
IGCThreadControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ThreadIsBlockingForSuspension(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SuspensionStarting(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SuspensionEnding(
        DWORD generation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGCThreadControl, 0xf31d1788, 0xc397, 0x4725, 0x87,0xa5, 0x6a,0xf3,0x47,0x2c,0x27,0x91)
#endif
#else
typedef struct IGCThreadControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGCThreadControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGCThreadControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGCThreadControl *This);

    /*** IGCThreadControl methods ***/
    HRESULT (STDMETHODCALLTYPE *ThreadIsBlockingForSuspension)(
        IGCThreadControl *This);

    HRESULT (STDMETHODCALLTYPE *SuspensionStarting)(
        IGCThreadControl *This);

    HRESULT (STDMETHODCALLTYPE *SuspensionEnding)(
        IGCThreadControl *This,
        DWORD generation);

    END_INTERFACE
} IGCThreadControlVtbl;

interface IGCThreadControl {
    CONST_VTBL IGCThreadControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGCThreadControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGCThreadControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGCThreadControl_Release(This) (This)->lpVtbl->Release(This)
/*** IGCThreadControl methods ***/
#define IGCThreadControl_ThreadIsBlockingForSuspension(This) (This)->lpVtbl->ThreadIsBlockingForSuspension(This)
#define IGCThreadControl_SuspensionStarting(This) (This)->lpVtbl->SuspensionStarting(This)
#define IGCThreadControl_SuspensionEnding(This,generation) (This)->lpVtbl->SuspensionEnding(This,generation)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IGCThreadControl_QueryInterface(IGCThreadControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IGCThreadControl_AddRef(IGCThreadControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IGCThreadControl_Release(IGCThreadControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IGCThreadControl methods ***/
static FORCEINLINE HRESULT IGCThreadControl_ThreadIsBlockingForSuspension(IGCThreadControl* This) {
    return This->lpVtbl->ThreadIsBlockingForSuspension(This);
}
static FORCEINLINE HRESULT IGCThreadControl_SuspensionStarting(IGCThreadControl* This) {
    return This->lpVtbl->SuspensionStarting(This);
}
static FORCEINLINE HRESULT IGCThreadControl_SuspensionEnding(IGCThreadControl* This,DWORD generation) {
    return This->lpVtbl->SuspensionEnding(This,generation);
}
#endif
#endif

#endif


#endif  /* __IGCThreadControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IGCHostControl interface
 */
#ifndef __IGCHostControl_INTERFACE_DEFINED__
#define __IGCHostControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IGCHostControl, 0x5513d564, 0x8374, 0x4cb9, 0xae,0xd9, 0x00,0x83,0xf4,0x16,0x0a,0x1d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5513d564-8374-4cb9-aed9-0083f4160a1d")
IGCHostControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RequestVirtualMemLimit(
        SIZE_T nMaxVirtualMemMB,
        SIZE_T *nNewMaxVirtualMemMB) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGCHostControl, 0x5513d564, 0x8374, 0x4cb9, 0xae,0xd9, 0x00,0x83,0xf4,0x16,0x0a,0x1d)
#endif
#else
typedef struct IGCHostControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGCHostControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGCHostControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGCHostControl *This);

    /*** IGCHostControl methods ***/
    HRESULT (STDMETHODCALLTYPE *RequestVirtualMemLimit)(
        IGCHostControl *This,
        SIZE_T nMaxVirtualMemMB,
        SIZE_T *nNewMaxVirtualMemMB);

    END_INTERFACE
} IGCHostControlVtbl;

interface IGCHostControl {
    CONST_VTBL IGCHostControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGCHostControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGCHostControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGCHostControl_Release(This) (This)->lpVtbl->Release(This)
/*** IGCHostControl methods ***/
#define IGCHostControl_RequestVirtualMemLimit(This,nMaxVirtualMemMB,nNewMaxVirtualMemMB) (This)->lpVtbl->RequestVirtualMemLimit(This,nMaxVirtualMemMB,nNewMaxVirtualMemMB)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IGCHostControl_QueryInterface(IGCHostControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IGCHostControl_AddRef(IGCHostControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IGCHostControl_Release(IGCHostControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IGCHostControl methods ***/
static FORCEINLINE HRESULT IGCHostControl_RequestVirtualMemLimit(IGCHostControl* This,SIZE_T nMaxVirtualMemMB,SIZE_T *nNewMaxVirtualMemMB) {
    return This->lpVtbl->RequestVirtualMemLimit(This,nMaxVirtualMemMB,nNewMaxVirtualMemMB);
}
#endif
#endif

#endif


#endif  /* __IGCHostControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebuggerThreadControl interface
 */
#ifndef __IDebuggerThreadControl_INTERFACE_DEFINED__
#define __IDebuggerThreadControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebuggerThreadControl, 0x23d86786, 0x0bb5, 0x4774, 0x8f,0xb5, 0xe3,0x52,0x2a,0xdd,0x62,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("23d86786-0bb5-4774-8fb5-e3522add6246")
IDebuggerThreadControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ThreadIsBlockingForDebugger(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseAllRuntimeThreads(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartBlockingForDebugger(
        DWORD dwUnused) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebuggerThreadControl, 0x23d86786, 0x0bb5, 0x4774, 0x8f,0xb5, 0xe3,0x52,0x2a,0xdd,0x62,0x46)
#endif
#else
typedef struct IDebuggerThreadControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebuggerThreadControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebuggerThreadControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebuggerThreadControl *This);

    /*** IDebuggerThreadControl methods ***/
    HRESULT (STDMETHODCALLTYPE *ThreadIsBlockingForDebugger)(
        IDebuggerThreadControl *This);

    HRESULT (STDMETHODCALLTYPE *ReleaseAllRuntimeThreads)(
        IDebuggerThreadControl *This);

    HRESULT (STDMETHODCALLTYPE *StartBlockingForDebugger)(
        IDebuggerThreadControl *This,
        DWORD dwUnused);

    END_INTERFACE
} IDebuggerThreadControlVtbl;

interface IDebuggerThreadControl {
    CONST_VTBL IDebuggerThreadControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebuggerThreadControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebuggerThreadControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebuggerThreadControl_Release(This) (This)->lpVtbl->Release(This)
/*** IDebuggerThreadControl methods ***/
#define IDebuggerThreadControl_ThreadIsBlockingForDebugger(This) (This)->lpVtbl->ThreadIsBlockingForDebugger(This)
#define IDebuggerThreadControl_ReleaseAllRuntimeThreads(This) (This)->lpVtbl->ReleaseAllRuntimeThreads(This)
#define IDebuggerThreadControl_StartBlockingForDebugger(This,dwUnused) (This)->lpVtbl->StartBlockingForDebugger(This,dwUnused)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDebuggerThreadControl_QueryInterface(IDebuggerThreadControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDebuggerThreadControl_AddRef(IDebuggerThreadControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDebuggerThreadControl_Release(IDebuggerThreadControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebuggerThreadControl methods ***/
static FORCEINLINE HRESULT IDebuggerThreadControl_ThreadIsBlockingForDebugger(IDebuggerThreadControl* This) {
    return This->lpVtbl->ThreadIsBlockingForDebugger(This);
}
static FORCEINLINE HRESULT IDebuggerThreadControl_ReleaseAllRuntimeThreads(IDebuggerThreadControl* This) {
    return This->lpVtbl->ReleaseAllRuntimeThreads(This);
}
static FORCEINLINE HRESULT IDebuggerThreadControl_StartBlockingForDebugger(IDebuggerThreadControl* This,DWORD dwUnused) {
    return This->lpVtbl->StartBlockingForDebugger(This,dwUnused);
}
#endif
#endif

#endif


#endif  /* __IDebuggerThreadControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICorConfiguration interface
 */
#ifndef __ICorConfiguration_INTERFACE_DEFINED__
#define __ICorConfiguration_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICorConfiguration, 0x5c2b07a5, 0x1e98, 0x11d3, 0x87,0x2f, 0x00,0xc0,0x4f,0x79,0xed,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5c2b07a5-1e98-11d3-872f-00c04f79ed0d")
ICorConfiguration : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetGCThreadControl(
        IGCThreadControl *GCThreadControl) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGCHostControl(
        IGCHostControl *GCHostControl) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDebuggerThreadControl(
        IDebuggerThreadControl *debuggerThreadControl) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDebuggerSpecialThread(
        DWORD specialThreadId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICorConfiguration, 0x5c2b07a5, 0x1e98, 0x11d3, 0x87,0x2f, 0x00,0xc0,0x4f,0x79,0xed,0x0d)
#endif
#else
typedef struct ICorConfigurationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICorConfiguration *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICorConfiguration *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICorConfiguration *This);

    /*** ICorConfiguration methods ***/
    HRESULT (STDMETHODCALLTYPE *SetGCThreadControl)(
        ICorConfiguration *This,
        IGCThreadControl *GCThreadControl);

    HRESULT (STDMETHODCALLTYPE *SetGCHostControl)(
        ICorConfiguration *This,
        IGCHostControl *GCHostControl);

    HRESULT (STDMETHODCALLTYPE *SetDebuggerThreadControl)(
        ICorConfiguration *This,
        IDebuggerThreadControl *debuggerThreadControl);

    HRESULT (STDMETHODCALLTYPE *AddDebuggerSpecialThread)(
        ICorConfiguration *This,
        DWORD specialThreadId);

    END_INTERFACE
} ICorConfigurationVtbl;

interface ICorConfiguration {
    CONST_VTBL ICorConfigurationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICorConfiguration_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICorConfiguration_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICorConfiguration_Release(This) (This)->lpVtbl->Release(This)
/*** ICorConfiguration methods ***/
#define ICorConfiguration_SetGCThreadControl(This,GCThreadControl) (This)->lpVtbl->SetGCThreadControl(This,GCThreadControl)
#define ICorConfiguration_SetGCHostControl(This,GCHostControl) (This)->lpVtbl->SetGCHostControl(This,GCHostControl)
#define ICorConfiguration_SetDebuggerThreadControl(This,debuggerThreadControl) (This)->lpVtbl->SetDebuggerThreadControl(This,debuggerThreadControl)
#define ICorConfiguration_AddDebuggerSpecialThread(This,specialThreadId) (This)->lpVtbl->AddDebuggerSpecialThread(This,specialThreadId)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICorConfiguration_QueryInterface(ICorConfiguration* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICorConfiguration_AddRef(ICorConfiguration* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICorConfiguration_Release(ICorConfiguration* This) {
    return This->lpVtbl->Release(This);
}
/*** ICorConfiguration methods ***/
static FORCEINLINE HRESULT ICorConfiguration_SetGCThreadControl(ICorConfiguration* This,IGCThreadControl *GCThreadControl) {
    return This->lpVtbl->SetGCThreadControl(This,GCThreadControl);
}
static FORCEINLINE HRESULT ICorConfiguration_SetGCHostControl(ICorConfiguration* This,IGCHostControl *GCHostControl) {
    return This->lpVtbl->SetGCHostControl(This,GCHostControl);
}
static FORCEINLINE HRESULT ICorConfiguration_SetDebuggerThreadControl(ICorConfiguration* This,IDebuggerThreadControl *debuggerThreadControl) {
    return This->lpVtbl->SetDebuggerThreadControl(This,debuggerThreadControl);
}
static FORCEINLINE HRESULT ICorConfiguration_AddDebuggerSpecialThread(ICorConfiguration* This,DWORD specialThreadId) {
    return This->lpVtbl->AddDebuggerSpecialThread(This,specialThreadId);
}
#endif
#endif

#endif


#endif  /* __ICorConfiguration_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICLRControl interface
 */
#ifndef __ICLRControl_INTERFACE_DEFINED__
#define __ICLRControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICLRControl, 0x9065597e, 0xd1a1, 0x4fb2, 0xb6,0xba, 0x7e,0x1f,0xce,0x23,0x0f,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9065597e-d1a1-4fb2-b6ba-7e1fce230f61")
ICLRControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCLRManager(
        REFIID riid,
        void **ppObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAppDomainManagerType(
        LPCWSTR appDomainManagerAssembly,
        LPCWSTR appDomainManagerType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICLRControl, 0x9065597e, 0xd1a1, 0x4fb2, 0xb6,0xba, 0x7e,0x1f,0xce,0x23,0x0f,0x61)
#endif
#else
typedef struct ICLRControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICLRControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICLRControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICLRControl *This);

    /*** ICLRControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCLRManager)(
        ICLRControl *This,
        REFIID riid,
        void **ppObject);

    HRESULT (STDMETHODCALLTYPE *SetAppDomainManagerType)(
        ICLRControl *This,
        LPCWSTR appDomainManagerAssembly,
        LPCWSTR appDomainManagerType);

    END_INTERFACE
} ICLRControlVtbl;

interface ICLRControl {
    CONST_VTBL ICLRControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICLRControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICLRControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICLRControl_Release(This) (This)->lpVtbl->Release(This)
/*** ICLRControl methods ***/
#define ICLRControl_GetCLRManager(This,riid,ppObject) (This)->lpVtbl->GetCLRManager(This,riid,ppObject)
#define ICLRControl_SetAppDomainManagerType(This,appDomainManagerAssembly,appDomainManagerType) (This)->lpVtbl->SetAppDomainManagerType(This,appDomainManagerAssembly,appDomainManagerType)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICLRControl_QueryInterface(ICLRControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICLRControl_AddRef(ICLRControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICLRControl_Release(ICLRControl* This) {
    return This->lpVtbl->Release(This);
}
/*** ICLRControl methods ***/
static FORCEINLINE HRESULT ICLRControl_GetCLRManager(ICLRControl* This,REFIID riid,void **ppObject) {
    return This->lpVtbl->GetCLRManager(This,riid,ppObject);
}
static FORCEINLINE HRESULT ICLRControl_SetAppDomainManagerType(ICLRControl* This,LPCWSTR appDomainManagerAssembly,LPCWSTR appDomainManagerType) {
    return This->lpVtbl->SetAppDomainManagerType(This,appDomainManagerAssembly,appDomainManagerType);
}
#endif
#endif

#endif


#endif  /* __ICLRControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IHostControl interface
 */
#ifndef __IHostControl_INTERFACE_DEFINED__
#define __IHostControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IHostControl, 0x02ca073c, 0x7079, 0x4860, 0x88,0x0a, 0xc2,0xf7,0xa4,0x49,0xc9,0x91);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("02ca073c-7079-4860-880a-c2f7a449c991")
IHostControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetHostManager(
        REFIID riid,
        void **ppObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAppDomainManager(
        DWORD appDomainID,
        IUnknown *appDomainManager) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IHostControl, 0x02ca073c, 0x7079, 0x4860, 0x88,0x0a, 0xc2,0xf7,0xa4,0x49,0xc9,0x91)
#endif
#else
typedef struct IHostControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IHostControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IHostControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IHostControl *This);

    /*** IHostControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetHostManager)(
        IHostControl *This,
        REFIID riid,
        void **ppObject);

    HRESULT (STDMETHODCALLTYPE *SetAppDomainManager)(
        IHostControl *This,
        DWORD appDomainID,
        IUnknown *appDomainManager);

    END_INTERFACE
} IHostControlVtbl;

interface IHostControl {
    CONST_VTBL IHostControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IHostControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHostControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHostControl_Release(This) (This)->lpVtbl->Release(This)
/*** IHostControl methods ***/
#define IHostControl_GetHostManager(This,riid,ppObject) (This)->lpVtbl->GetHostManager(This,riid,ppObject)
#define IHostControl_SetAppDomainManager(This,appDomainID,appDomainManager) (This)->lpVtbl->SetAppDomainManager(This,appDomainID,appDomainManager)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IHostControl_QueryInterface(IHostControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IHostControl_AddRef(IHostControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IHostControl_Release(IHostControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IHostControl methods ***/
static FORCEINLINE HRESULT IHostControl_GetHostManager(IHostControl* This,REFIID riid,void **ppObject) {
    return This->lpVtbl->GetHostManager(This,riid,ppObject);
}
static FORCEINLINE HRESULT IHostControl_SetAppDomainManager(IHostControl* This,DWORD appDomainID,IUnknown *appDomainManager) {
    return This->lpVtbl->SetAppDomainManager(This,appDomainID,appDomainManager);
}
#endif
#endif

#endif


#endif  /* __IHostControl_INTERFACE_DEFINED__ */

DEFINE_GUID(CLSID_CorRuntimeHost, 0xcb2f6723,0xab3a,0x11d2,0x9c,0x40,0x00,0xc0,0x4f,0xa3,0x0a,0x3e);
/*****************************************************************************
 * ICorRuntimeHost interface
 */
#ifndef __ICorRuntimeHost_INTERFACE_DEFINED__
#define __ICorRuntimeHost_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICorRuntimeHost, 0xcb2f6722, 0xab3a, 0x11d2, 0x9c,0x40, 0x00,0xc0,0x4f,0xa3,0x0a,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cb2f6722-ab3a-11d2-9c40-00c04fa30a3e")
ICorRuntimeHost : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateLogicalThreadState(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteLogicalThreadState(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SwitchInLogicalThreadState(
        DWORD *fiberCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE SwitchOutLogicalThreadState(
        DWORD **fiberCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE LocksHeldByLogicalThread(
        DWORD *pCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE MapFile(
        HANDLE hFile,
        HMODULE *mapAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConfiguration(
        ICorConfiguration **pConfiguration) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDomain(
        LPCWSTR friendlyName,
        IUnknown *identityArray,
        IUnknown **appDomain) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultDomain(
        IUnknown **pAppDomain) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumDomains(
        HDOMAINENUM *hEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE NextDomain(
        HDOMAINENUM hEnum,
        IUnknown **appDomain) = 0;

    virtual HRESULT STDMETHODCALLTYPE CloseEnum(
        HDOMAINENUM hEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDomainEx(
        LPCWSTR friendlyName,
        IUnknown *setup,
        IUnknown *evidence,
        IUnknown **appDomain) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDomainSetup(
        IUnknown **appDomainSetup) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateEvidence(
        IUnknown **evidence) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnloadDomain(
        IUnknown *appDomain) = 0;

    virtual HRESULT STDMETHODCALLTYPE CurrentDomain(
        IUnknown **appDomain) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICorRuntimeHost, 0xcb2f6722, 0xab3a, 0x11d2, 0x9c,0x40, 0x00,0xc0,0x4f,0xa3,0x0a,0x3e)
#endif
#else
typedef struct ICorRuntimeHostVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICorRuntimeHost *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICorRuntimeHost *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICorRuntimeHost *This);

    /*** ICorRuntimeHost methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateLogicalThreadState)(
        ICorRuntimeHost *This);

    HRESULT (STDMETHODCALLTYPE *DeleteLogicalThreadState)(
        ICorRuntimeHost *This);

    HRESULT (STDMETHODCALLTYPE *SwitchInLogicalThreadState)(
        ICorRuntimeHost *This,
        DWORD *fiberCookie);

    HRESULT (STDMETHODCALLTYPE *SwitchOutLogicalThreadState)(
        ICorRuntimeHost *This,
        DWORD **fiberCookie);

    HRESULT (STDMETHODCALLTYPE *LocksHeldByLogicalThread)(
        ICorRuntimeHost *This,
        DWORD *pCount);

    HRESULT (STDMETHODCALLTYPE *MapFile)(
        ICorRuntimeHost *This,
        HANDLE hFile,
        HMODULE *mapAddress);

    HRESULT (STDMETHODCALLTYPE *GetConfiguration)(
        ICorRuntimeHost *This,
        ICorConfiguration **pConfiguration);

    HRESULT (STDMETHODCALLTYPE *Start)(
        ICorRuntimeHost *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        ICorRuntimeHost *This);

    HRESULT (STDMETHODCALLTYPE *CreateDomain)(
        ICorRuntimeHost *This,
        LPCWSTR friendlyName,
        IUnknown *identityArray,
        IUnknown **appDomain);

    HRESULT (STDMETHODCALLTYPE *GetDefaultDomain)(
        ICorRuntimeHost *This,
        IUnknown **pAppDomain);

    HRESULT (STDMETHODCALLTYPE *EnumDomains)(
        ICorRuntimeHost *This,
        HDOMAINENUM *hEnum);

    HRESULT (STDMETHODCALLTYPE *NextDomain)(
        ICorRuntimeHost *This,
        HDOMAINENUM hEnum,
        IUnknown **appDomain);

    HRESULT (STDMETHODCALLTYPE *CloseEnum)(
        ICorRuntimeHost *This,
        HDOMAINENUM hEnum);

    HRESULT (STDMETHODCALLTYPE *CreateDomainEx)(
        ICorRuntimeHost *This,
        LPCWSTR friendlyName,
        IUnknown *setup,
        IUnknown *evidence,
        IUnknown **appDomain);

    HRESULT (STDMETHODCALLTYPE *CreateDomainSetup)(
        ICorRuntimeHost *This,
        IUnknown **appDomainSetup);

    HRESULT (STDMETHODCALLTYPE *CreateEvidence)(
        ICorRuntimeHost *This,
        IUnknown **evidence);

    HRESULT (STDMETHODCALLTYPE *UnloadDomain)(
        ICorRuntimeHost *This,
        IUnknown *appDomain);

    HRESULT (STDMETHODCALLTYPE *CurrentDomain)(
        ICorRuntimeHost *This,
        IUnknown **appDomain);

    END_INTERFACE
} ICorRuntimeHostVtbl;

interface ICorRuntimeHost {
    CONST_VTBL ICorRuntimeHostVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICorRuntimeHost_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICorRuntimeHost_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICorRuntimeHost_Release(This) (This)->lpVtbl->Release(This)
/*** ICorRuntimeHost methods ***/
#define ICorRuntimeHost_CreateLogicalThreadState(This) (This)->lpVtbl->CreateLogicalThreadState(This)
#define ICorRuntimeHost_DeleteLogicalThreadState(This) (This)->lpVtbl->DeleteLogicalThreadState(This)
#define ICorRuntimeHost_SwitchInLogicalThreadState(This,fiberCookie) (This)->lpVtbl->SwitchInLogicalThreadState(This,fiberCookie)
#define ICorRuntimeHost_SwitchOutLogicalThreadState(This,fiberCookie) (This)->lpVtbl->SwitchOutLogicalThreadState(This,fiberCookie)
#define ICorRuntimeHost_LocksHeldByLogicalThread(This,pCount) (This)->lpVtbl->LocksHeldByLogicalThread(This,pCount)
#define ICorRuntimeHost_MapFile(This,hFile,mapAddress) (This)->lpVtbl->MapFile(This,hFile,mapAddress)
#define ICorRuntimeHost_GetConfiguration(This,pConfiguration) (This)->lpVtbl->GetConfiguration(This,pConfiguration)
#define ICorRuntimeHost_Start(This) (This)->lpVtbl->Start(This)
#define ICorRuntimeHost_Stop(This) (This)->lpVtbl->Stop(This)
#define ICorRuntimeHost_CreateDomain(This,friendlyName,identityArray,appDomain) (This)->lpVtbl->CreateDomain(This,friendlyName,identityArray,appDomain)
#define ICorRuntimeHost_GetDefaultDomain(This,pAppDomain) (This)->lpVtbl->GetDefaultDomain(This,pAppDomain)
#define ICorRuntimeHost_EnumDomains(This,hEnum) (This)->lpVtbl->EnumDomains(This,hEnum)
#define ICorRuntimeHost_NextDomain(This,hEnum,appDomain) (This)->lpVtbl->NextDomain(This,hEnum,appDomain)
#define ICorRuntimeHost_CloseEnum(This,hEnum) (This)->lpVtbl->CloseEnum(This,hEnum)
#define ICorRuntimeHost_CreateDomainEx(This,friendlyName,setup,evidence,appDomain) (This)->lpVtbl->CreateDomainEx(This,friendlyName,setup,evidence,appDomain)
#define ICorRuntimeHost_CreateDomainSetup(This,appDomainSetup) (This)->lpVtbl->CreateDomainSetup(This,appDomainSetup)
#define ICorRuntimeHost_CreateEvidence(This,evidence) (This)->lpVtbl->CreateEvidence(This,evidence)
#define ICorRuntimeHost_UnloadDomain(This,appDomain) (This)->lpVtbl->UnloadDomain(This,appDomain)
#define ICorRuntimeHost_CurrentDomain(This,appDomain) (This)->lpVtbl->CurrentDomain(This,appDomain)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICorRuntimeHost_QueryInterface(ICorRuntimeHost* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICorRuntimeHost_AddRef(ICorRuntimeHost* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICorRuntimeHost_Release(ICorRuntimeHost* This) {
    return This->lpVtbl->Release(This);
}
/*** ICorRuntimeHost methods ***/
static FORCEINLINE HRESULT ICorRuntimeHost_CreateLogicalThreadState(ICorRuntimeHost* This) {
    return This->lpVtbl->CreateLogicalThreadState(This);
}
static FORCEINLINE HRESULT ICorRuntimeHost_DeleteLogicalThreadState(ICorRuntimeHost* This) {
    return This->lpVtbl->DeleteLogicalThreadState(This);
}
static FORCEINLINE HRESULT ICorRuntimeHost_SwitchInLogicalThreadState(ICorRuntimeHost* This,DWORD *fiberCookie) {
    return This->lpVtbl->SwitchInLogicalThreadState(This,fiberCookie);
}
static FORCEINLINE HRESULT ICorRuntimeHost_SwitchOutLogicalThreadState(ICorRuntimeHost* This,DWORD **fiberCookie) {
    return This->lpVtbl->SwitchOutLogicalThreadState(This,fiberCookie);
}
static FORCEINLINE HRESULT ICorRuntimeHost_LocksHeldByLogicalThread(ICorRuntimeHost* This,DWORD *pCount) {
    return This->lpVtbl->LocksHeldByLogicalThread(This,pCount);
}
static FORCEINLINE HRESULT ICorRuntimeHost_MapFile(ICorRuntimeHost* This,HANDLE hFile,HMODULE *mapAddress) {
    return This->lpVtbl->MapFile(This,hFile,mapAddress);
}
static FORCEINLINE HRESULT ICorRuntimeHost_GetConfiguration(ICorRuntimeHost* This,ICorConfiguration **pConfiguration) {
    return This->lpVtbl->GetConfiguration(This,pConfiguration);
}
static FORCEINLINE HRESULT ICorRuntimeHost_Start(ICorRuntimeHost* This) {
    return This->lpVtbl->Start(This);
}
static FORCEINLINE HRESULT ICorRuntimeHost_Stop(ICorRuntimeHost* This) {
    return This->lpVtbl->Stop(This);
}
static FORCEINLINE HRESULT ICorRuntimeHost_CreateDomain(ICorRuntimeHost* This,LPCWSTR friendlyName,IUnknown *identityArray,IUnknown **appDomain) {
    return This->lpVtbl->CreateDomain(This,friendlyName,identityArray,appDomain);
}
static FORCEINLINE HRESULT ICorRuntimeHost_GetDefaultDomain(ICorRuntimeHost* This,IUnknown **pAppDomain) {
    return This->lpVtbl->GetDefaultDomain(This,pAppDomain);
}
static FORCEINLINE HRESULT ICorRuntimeHost_EnumDomains(ICorRuntimeHost* This,HDOMAINENUM *hEnum) {
    return This->lpVtbl->EnumDomains(This,hEnum);
}
static FORCEINLINE HRESULT ICorRuntimeHost_NextDomain(ICorRuntimeHost* This,HDOMAINENUM hEnum,IUnknown **appDomain) {
    return This->lpVtbl->NextDomain(This,hEnum,appDomain);
}
static FORCEINLINE HRESULT ICorRuntimeHost_CloseEnum(ICorRuntimeHost* This,HDOMAINENUM hEnum) {
    return This->lpVtbl->CloseEnum(This,hEnum);
}
static FORCEINLINE HRESULT ICorRuntimeHost_CreateDomainEx(ICorRuntimeHost* This,LPCWSTR friendlyName,IUnknown *setup,IUnknown *evidence,IUnknown **appDomain) {
    return This->lpVtbl->CreateDomainEx(This,friendlyName,setup,evidence,appDomain);
}
static FORCEINLINE HRESULT ICorRuntimeHost_CreateDomainSetup(ICorRuntimeHost* This,IUnknown **appDomainSetup) {
    return This->lpVtbl->CreateDomainSetup(This,appDomainSetup);
}
static FORCEINLINE HRESULT ICorRuntimeHost_CreateEvidence(ICorRuntimeHost* This,IUnknown **evidence) {
    return This->lpVtbl->CreateEvidence(This,evidence);
}
static FORCEINLINE HRESULT ICorRuntimeHost_UnloadDomain(ICorRuntimeHost* This,IUnknown *appDomain) {
    return This->lpVtbl->UnloadDomain(This,appDomain);
}
static FORCEINLINE HRESULT ICorRuntimeHost_CurrentDomain(ICorRuntimeHost* This,IUnknown **appDomain) {
    return This->lpVtbl->CurrentDomain(This,appDomain);
}
#endif
#endif

#endif


#endif  /* __ICorRuntimeHost_INTERFACE_DEFINED__ */

DEFINE_GUID(CLSID_CLRRuntimeHost, 0x90f1a06e,0x7712,0x4762,0x86,0xb5,0x7a,0x5e,0xba,0x6b,0xdb,0x02);
/*****************************************************************************
 * ICLRRuntimeHost interface
 */
#ifndef __ICLRRuntimeHost_INTERFACE_DEFINED__
#define __ICLRRuntimeHost_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICLRRuntimeHost, 0x90f1a06c, 0x7712, 0x4762, 0x86,0xb5, 0x7a,0x5e,0xba,0x6b,0xdb,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("90f1a06c-7712-4762-86b5-7a5eba6bdb02")
ICLRRuntimeHost : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Start(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHostControl(
        IHostControl *pHostControl) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCLRControl(
        ICLRControl **pCLRControl) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnloadAppDomain(
        DWORD dwAppDomainId,
        WINBOOL fWaitUntilDone) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecuteInAppDomain(
        DWORD dwAppDomainId,
        HRESULT (__stdcall * pCallback)(void *cookie),
        void *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentAppDomainId(
        DWORD *pdwAppDomainId) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecuteApplication(
        LPCWSTR pwzAppFullName,
        DWORD dwManifestPaths,
        LPCWSTR *ppwzManifestPaths,
        DWORD dwActivationData,
        LPCWSTR *ppwzActivationData,
        int *pReturnValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecuteInDefaultAppDomain(
        LPCWSTR pwzAssemblyPath,
        LPCWSTR pwzTypeName,
        LPCWSTR pwzMethodName,
        LPCWSTR pwzArgument,
        DWORD *pReturnValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICLRRuntimeHost, 0x90f1a06c, 0x7712, 0x4762, 0x86,0xb5, 0x7a,0x5e,0xba,0x6b,0xdb,0x02)
#endif
#else
typedef struct ICLRRuntimeHostVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICLRRuntimeHost *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICLRRuntimeHost *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICLRRuntimeHost *This);

    /*** ICLRRuntimeHost methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        ICLRRuntimeHost *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        ICLRRuntimeHost *This);

    HRESULT (STDMETHODCALLTYPE *SetHostControl)(
        ICLRRuntimeHost *This,
        IHostControl *pHostControl);

    HRESULT (STDMETHODCALLTYPE *GetCLRControl)(
        ICLRRuntimeHost *This,
        ICLRControl **pCLRControl);

    HRESULT (STDMETHODCALLTYPE *UnloadAppDomain)(
        ICLRRuntimeHost *This,
        DWORD dwAppDomainId,
        WINBOOL fWaitUntilDone);

    HRESULT (STDMETHODCALLTYPE *ExecuteInAppDomain)(
        ICLRRuntimeHost *This,
        DWORD dwAppDomainId,
        HRESULT (__stdcall * pCallback)(void *cookie),
        void *cookie);

    HRESULT (STDMETHODCALLTYPE *GetCurrentAppDomainId)(
        ICLRRuntimeHost *This,
        DWORD *pdwAppDomainId);

    HRESULT (STDMETHODCALLTYPE *ExecuteApplication)(
        ICLRRuntimeHost *This,
        LPCWSTR pwzAppFullName,
        DWORD dwManifestPaths,
        LPCWSTR *ppwzManifestPaths,
        DWORD dwActivationData,
        LPCWSTR *ppwzActivationData,
        int *pReturnValue);

    HRESULT (STDMETHODCALLTYPE *ExecuteInDefaultAppDomain)(
        ICLRRuntimeHost *This,
        LPCWSTR pwzAssemblyPath,
        LPCWSTR pwzTypeName,
        LPCWSTR pwzMethodName,
        LPCWSTR pwzArgument,
        DWORD *pReturnValue);

    END_INTERFACE
} ICLRRuntimeHostVtbl;

interface ICLRRuntimeHost {
    CONST_VTBL ICLRRuntimeHostVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICLRRuntimeHost_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICLRRuntimeHost_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICLRRuntimeHost_Release(This) (This)->lpVtbl->Release(This)
/*** ICLRRuntimeHost methods ***/
#define ICLRRuntimeHost_Start(This) (This)->lpVtbl->Start(This)
#define ICLRRuntimeHost_Stop(This) (This)->lpVtbl->Stop(This)
#define ICLRRuntimeHost_SetHostControl(This,pHostControl) (This)->lpVtbl->SetHostControl(This,pHostControl)
#define ICLRRuntimeHost_GetCLRControl(This,pCLRControl) (This)->lpVtbl->GetCLRControl(This,pCLRControl)
#define ICLRRuntimeHost_UnloadAppDomain(This,dwAppDomainId,fWaitUntilDone) (This)->lpVtbl->UnloadAppDomain(This,dwAppDomainId,fWaitUntilDone)
#define ICLRRuntimeHost_ExecuteInAppDomain(This,dwAppDomainId,pCallback,cookie) (This)->lpVtbl->ExecuteInAppDomain(This,dwAppDomainId,pCallback,cookie)
#define ICLRRuntimeHost_GetCurrentAppDomainId(This,pdwAppDomainId) (This)->lpVtbl->GetCurrentAppDomainId(This,pdwAppDomainId)
#define ICLRRuntimeHost_ExecuteApplication(This,pwzAppFullName,dwManifestPaths,ppwzManifestPaths,dwActivationData,ppwzActivationData,pReturnValue) (This)->lpVtbl->ExecuteApplication(This,pwzAppFullName,dwManifestPaths,ppwzManifestPaths,dwActivationData,ppwzActivationData,pReturnValue)
#define ICLRRuntimeHost_ExecuteInDefaultAppDomain(This,pwzAssemblyPath,pwzTypeName,pwzMethodName,pwzArgument,pReturnValue) (This)->lpVtbl->ExecuteInDefaultAppDomain(This,pwzAssemblyPath,pwzTypeName,pwzMethodName,pwzArgument,pReturnValue)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICLRRuntimeHost_QueryInterface(ICLRRuntimeHost* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICLRRuntimeHost_AddRef(ICLRRuntimeHost* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICLRRuntimeHost_Release(ICLRRuntimeHost* This) {
    return This->lpVtbl->Release(This);
}
/*** ICLRRuntimeHost methods ***/
static FORCEINLINE HRESULT ICLRRuntimeHost_Start(ICLRRuntimeHost* This) {
    return This->lpVtbl->Start(This);
}
static FORCEINLINE HRESULT ICLRRuntimeHost_Stop(ICLRRuntimeHost* This) {
    return This->lpVtbl->Stop(This);
}
static FORCEINLINE HRESULT ICLRRuntimeHost_SetHostControl(ICLRRuntimeHost* This,IHostControl *pHostControl) {
    return This->lpVtbl->SetHostControl(This,pHostControl);
}
static FORCEINLINE HRESULT ICLRRuntimeHost_GetCLRControl(ICLRRuntimeHost* This,ICLRControl **pCLRControl) {
    return This->lpVtbl->GetCLRControl(This,pCLRControl);
}
static FORCEINLINE HRESULT ICLRRuntimeHost_UnloadAppDomain(ICLRRuntimeHost* This,DWORD dwAppDomainId,WINBOOL fWaitUntilDone) {
    return This->lpVtbl->UnloadAppDomain(This,dwAppDomainId,fWaitUntilDone);
}
static FORCEINLINE HRESULT ICLRRuntimeHost_ExecuteInAppDomain(ICLRRuntimeHost* This,DWORD dwAppDomainId,HRESULT (__stdcall * pCallback)(void *cookie),void *cookie) {
    return This->lpVtbl->ExecuteInAppDomain(This,dwAppDomainId,pCallback,cookie);
}
static FORCEINLINE HRESULT ICLRRuntimeHost_GetCurrentAppDomainId(ICLRRuntimeHost* This,DWORD *pdwAppDomainId) {
    return This->lpVtbl->GetCurrentAppDomainId(This,pdwAppDomainId);
}
static FORCEINLINE HRESULT ICLRRuntimeHost_ExecuteApplication(ICLRRuntimeHost* This,LPCWSTR pwzAppFullName,DWORD dwManifestPaths,LPCWSTR *ppwzManifestPaths,DWORD dwActivationData,LPCWSTR *ppwzActivationData,int *pReturnValue) {
    return This->lpVtbl->ExecuteApplication(This,pwzAppFullName,dwManifestPaths,ppwzManifestPaths,dwActivationData,ppwzActivationData,pReturnValue);
}
static FORCEINLINE HRESULT ICLRRuntimeHost_ExecuteInDefaultAppDomain(ICLRRuntimeHost* This,LPCWSTR pwzAssemblyPath,LPCWSTR pwzTypeName,LPCWSTR pwzMethodName,LPCWSTR pwzArgument,DWORD *pReturnValue) {
    return This->lpVtbl->ExecuteInDefaultAppDomain(This,pwzAssemblyPath,pwzTypeName,pwzMethodName,pwzArgument,pReturnValue);
}
#endif
#endif

#endif


#endif  /* __ICLRRuntimeHost_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IManagedObject interface
 */
#ifndef __IManagedObject_INTERFACE_DEFINED__
#define __IManagedObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IManagedObject, 0xc3fcc19e, 0xa970, 0x11d2, 0x8b,0x5a, 0x00,0xa0,0xc9,0xb7,0xc9,0xc4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c3fcc19e-a970-11d2-8b5a-00a0c9b7c9c4")
IManagedObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSerializedBuffer(
        BSTR *pBSTR) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectIdentity(
        BSTR *pBSTRGUID,
        int *AppDomainID,
        int *pCCW) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IManagedObject, 0xc3fcc19e, 0xa970, 0x11d2, 0x8b,0x5a, 0x00,0xa0,0xc9,0xb7,0xc9,0xc4)
#endif
#else
typedef struct IManagedObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IManagedObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IManagedObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IManagedObject *This);

    /*** IManagedObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSerializedBuffer)(
        IManagedObject *This,
        BSTR *pBSTR);

    HRESULT (STDMETHODCALLTYPE *GetObjectIdentity)(
        IManagedObject *This,
        BSTR *pBSTRGUID,
        int *AppDomainID,
        int *pCCW);

    END_INTERFACE
} IManagedObjectVtbl;

interface IManagedObject {
    CONST_VTBL IManagedObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IManagedObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IManagedObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IManagedObject_Release(This) (This)->lpVtbl->Release(This)
/*** IManagedObject methods ***/
#define IManagedObject_GetSerializedBuffer(This,pBSTR) (This)->lpVtbl->GetSerializedBuffer(This,pBSTR)
#define IManagedObject_GetObjectIdentity(This,pBSTRGUID,AppDomainID,pCCW) (This)->lpVtbl->GetObjectIdentity(This,pBSTRGUID,AppDomainID,pCCW)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IManagedObject_QueryInterface(IManagedObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IManagedObject_AddRef(IManagedObject* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IManagedObject_Release(IManagedObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IManagedObject methods ***/
static FORCEINLINE HRESULT IManagedObject_GetSerializedBuffer(IManagedObject* This,BSTR *pBSTR) {
    return This->lpVtbl->GetSerializedBuffer(This,pBSTR);
}
static FORCEINLINE HRESULT IManagedObject_GetObjectIdentity(IManagedObject* This,BSTR *pBSTRGUID,int *AppDomainID,int *pCCW) {
    return This->lpVtbl->GetObjectIdentity(This,pBSTRGUID,AppDomainID,pCCW);
}
#endif
#endif

#endif


#endif  /* __IManagedObject_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mscoree_h__ */
