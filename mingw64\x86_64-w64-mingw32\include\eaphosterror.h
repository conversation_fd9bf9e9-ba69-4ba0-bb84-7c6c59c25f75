/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _INC_HOSTERROR
#define _INC_HOSTERROR
#if (_WIN32_WINNT >= 0x0600)

#define _EAP_CERT_FIRST 0x0
#define _EAP_CERT_LAST 0xf
#define _EAP_CERT_NOT_FOUND 0x1
#define _EAP_CERT_INVALID 0x2
#define _EAP_CERT_EXPIRED 0x3
#define _EAP_CERT_REVOKED 0x4
#define _EAP_CERT_OTHER_ERROR 0x5
#define _EAP_CERT_REJECTED 0x6
#define _EAP_CERT_NAME_REQUIRED 0x7
#define _EAP_GENERAL_FIRST 0x10
#define _EAP_GENERAL_LAST 0x3f

#define EAP_E_EAPHOST_FIRST __MSABI_LONG(0x80420000)
#define EAP_E_EAPHOST_LAST  __MSABI_LONG(0x804200FF)
#define EAP_I_EAPHOST_FIRST __MSABI_LONG(0x80420000)
#define EAP_I_EAPHOST_FIRST __MSABI_LONG(0x804200FF)

#define EAP_E_CERT_STORE_INACCESSIBLE 0x80420011
#define EAP_E_EAPHOST_METHOD_NOT_INSTALLED 0x80420011
#define EAP_E_EAPHOST_THIRDPARTY_METHOD_HOST_RESET 0x80420012
#define EAP_E_EAPHOST_EAPQEC_INACCESSIBLE 0x80420013
#define EAP_E_EAPHOST_IDENTITY_UNKNOWN 0x80420014
#define EAP_E_AUTHENTICATION_FAILED 0x80420015
#define EAP_I_EAPHOST_EAP_NEGOTIATION_FAILED 0x40420016
#define EAP_E_EAPHOST_METHOD_INVALID_PACKET 0x40420017
#define EAP_E_EAPHOST_REMOTE_INVALID_PACKET 0x40420018
#define EAP_E_EAPHOST_XML_MALFORMED 0x40420019

#if (_WIN32_WINNT >= 0x0601)
#define EAP_E_METHOD_CONFIG_DOES_NOT_SUPPORT_SSO 0x4042001A
#endif /*(_WIN32_WINNT >= 0x0601)*/

#define EAP_E_EAPHOST_METHOD_OPERATION_NOT_SUPPORTED 0x80420020

#define EAP_E_USER_FIRST __MSABI_LONG(0x80420100)
#define EAP_E_USER_LAST  __MSABI_LONG(0x804201FF)
#define EAP_I_USER_FIRST __MSABI_LONG(0x40420100)
#define EAP_I_USER_LAST  __MSABI_LONG(0x804201FF)

#define EAP_E_USER_CERT_NOT_FOUND 0x80420100
#define EAP_E_USER_CERT_INVALID 0x80420101
#define EAP_E_USER_CERT_EXPIRED 0x80420102
#define EAP_E_USER_CERT_REVOKED 0x80420103
#define EAP_E_USER_CERT_OTHER_ERROR 0x80420104
#define EAP_E_USER_CERT_REJECTED 0x80420105
#define EAP_I_USER_ACCOUNT_OTHER_ERROR 0x40420110
#define EAP_E_USER_CREDENTIALS_REJECTED 0x40420111

#if (_WIN32_WINNT >= 0x0601)
#define EAP_E_USER_NAME_PASSWORD_REJECTED 0x40420112
#define EAP_E_NO_SMART_CARD_READER 0x80420113
#endif /*(_WIN32_WINNT >= 0x0601)*/

#define EAP_E_SERVER_FIRST __MSABI_LONG(0x80420200)
#define EAP_E_SERVER_LAST __MSABI_LONG(0x804202FF)

#define EAP_E_SERVER_CERT_NOT_FOUND 0x80420200
#define EAP_E_SERVER_CERT_INVALID 0x80420201
#define EAP_E_SERVER_CERT_EXPIRED 0x80420202
#define EAP_E_SERVER_CERT_REVOKED 0x80420203
#define EAP_E_SERVER_CERT_OTHER_ERROR 0x80420204

#define EAP_E_USER_ROOT_CERT_FIRST __MSABI_LONG(0x80420300)
#define EAP_E_USER_ROOT_CERT_LAST __MSABI_LONG(0x804203FF)
#define EAP_E_USER_ROOT_CERT_NOT_FOUND 0x80420300
#define EAP_E_USER_ROOT_CERT_INVALID 0x80420301
#define EAP_E_USER_ROOT_CERT_EXPIRED 0x80420302

#define EAP_E_SERVER_ROOT_CERT_FIRST __MSABI_LONG(0x80420400)
#define EAP_E_SERVER_ROOT_CERT_LAST __MSABI_LONG(0x804204FF)

#define EAP_E_SERVER_ROOT_CERT_NOT_FOUND 0x80420400
#define EAP_E_SERVER_ROOT_CERT_INVALID 0x80420401
#define EAP_E_SERVER_ROOT_CERT_NAME_REQUIRED 0x80420406

#endif /*(_WIN32_WINNT >= 0x0600)*/
#endif /*_INC_HOSTERROR*/
