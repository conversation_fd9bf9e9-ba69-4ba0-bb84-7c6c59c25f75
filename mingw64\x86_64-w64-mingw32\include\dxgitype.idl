/*
 * Copyright 2007 <PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "dxgicommon.idl";
import "dxgiformat.idl";

cpp_quote("#if 0")
typedef unsigned int UINT;
typedef long BOOL;
cpp_quote("#endif")

typedef enum DXGI_MODE_ROTATION
{
    DXGI_MODE_ROTATION_UNSPECIFIED  = 0x0,
    DXGI_MODE_ROTATION_IDENTITY     = 0x1,
    DXGI_MODE_ROTATION_ROTATE90     = 0x2,
    DXGI_MODE_ROTATION_ROTATE180    = 0x3,
    DXGI_MODE_ROTATION_ROTATE270    = 0x4,
} DXGI_MODE_ROTATION;

typedef enum DXGI_MODE_SCANLINE_ORDER
{
    DXGI_MODE_SCANLINE_ORDER_UNSPECIFIED        = 0x0,
    DXGI_MODE_SCANLINE_ORDER_PROGRESSIVE        = 0x1,
    DXGI_MODE_SCANLINE_ORDER_UPPER_FIELD_FIRST  = 0x2,
    DXGI_MODE_SCANLINE_ORDER_LOWER_FIELD_FIRST  = 0x3,
} DXGI_MODE_SCANLINE_ORDER;

typedef enum DXGI_MODE_SCALING
{
    DXGI_MODE_SCALING_UNSPECIFIED   = 0x0,
    DXGI_MODE_SCALING_CENTERED      = 0x1,
    DXGI_MODE_SCALING_STRETCHED     = 0x2,
} DXGI_MODE_SCALING;

cpp_quote("#ifndef D3DCOLORVALUE_DEFINED")
cpp_quote("#define D3DCOLORVALUE_DEFINED")
typedef struct _D3DCOLORVALUE
{
    float r;
    float g;
    float b;
    float a;
} D3DCOLORVALUE;
cpp_quote("#endif")
typedef D3DCOLORVALUE DXGI_RGBA;

typedef struct DXGI_MODE_DESC
{
    UINT Width;
    UINT Height;
    DXGI_RATIONAL RefreshRate;
    DXGI_FORMAT Format;
    DXGI_MODE_SCANLINE_ORDER ScanlineOrdering;
    DXGI_MODE_SCALING Scaling;
} DXGI_MODE_DESC;

typedef struct DXGI_GAMMA_CONTROL_CAPABILITIES
{
    BOOL ScaleAndOffsetSupported;
    float MaxConvertedValue;
    float MinConvertedValue;
    UINT NumGammaControlPoints;
    float ControlPointPositions[1025];
} DXGI_GAMMA_CONTROL_CAPABILITIES;

typedef struct DXGI_RGB
{
    float Red;
    float Green;
    float Blue;
} DXGI_RGB;

typedef struct DXGI_GAMMA_CONTROL
{
    DXGI_RGB Scale;
    DXGI_RGB Offset;
    DXGI_RGB GammaCurve[1025];
} DXGI_GAMMA_CONTROL;
