/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#include <fsrmenums.h>
#include <fsrm.h>
#include <fsrmquota.h>
#include <fsrmscreen.h>

#ifndef __IFsrmAction_FWD_DEFINED__
#define __IFsrmAction_FWD_DEFINED__
typedef struct IFsrmAction IFsrmAction;
#endif /* __IFsrmAction_FWD_DEFINED__ */

#ifndef __IFsrmActionCommand_FWD_DEFINED__
#define __IFsrmActionCommand_FWD_DEFINED__
typedef struct IFsrmActionCommand IFsrmActionCommand;
#endif /* __IFsrmActionCommand_FWD_DEFINED__ */

#ifndef __IFsrmActionEmail_FWD_DEFINED__
#define __IFsrmActionEmail_FWD_DEFINED__
typedef struct IFsrmActionEmail IFsrmActionEmail;
#endif /* __IFsrmActionEmail_FWD_DEFINED__ */

#ifndef __IFsrmActionEmail2_FWD_DEFINED__
#define __IFsrmActionEmail2_FWD_DEFINED__
typedef struct IFsrmActionEmail2 IFsrmActionEmail2;
#endif /* __IFsrmActionEmail2_FWD_DEFINED__ */

#ifndef __IFsrmActionEventLog_FWD_DEFINED__
#define __IFsrmActionEventLog_FWD_DEFINED__
typedef struct IFsrmActionEventLog IFsrmActionEventLog;
#endif /* __IFsrmActionEventLog_FWD_DEFINED__ */

#ifndef __IFsrmActionReport_FWD_DEFINED__
#define __IFsrmActionReport_FWD_DEFINED__
typedef struct IFsrmActionReport IFsrmActionReport;
#endif /* __IFsrmActionReport_FWD_DEFINED__ */

#ifndef __IFsrmAutoApplyQuota_FWD_DEFINED__
#define __IFsrmAutoApplyQuota_FWD_DEFINED__
typedef struct IFsrmAutoApplyQuota IFsrmAutoApplyQuota;
#endif /* __IFsrmAutoApplyQuota_FWD_DEFINED__ */

#ifndef __IFsrmClassificationManager_FWD_DEFINED__
#define __IFsrmClassificationManager_FWD_DEFINED__
typedef struct IFsrmClassificationManager IFsrmClassificationManager;
#endif /* __IFsrmClassificationManager_FWD_DEFINED__ */

#ifndef __IFsrmClassificationRule_FWD_DEFINED__
#define __IFsrmClassificationRule_FWD_DEFINED__
typedef struct IFsrmClassificationRule IFsrmClassificationRule;
#endif /* __IFsrmClassificationRule_FWD_DEFINED__ */

#ifndef __IFsrmClassifierModuleDefinition_FWD_DEFINED__
#define __IFsrmClassifierModuleDefinition_FWD_DEFINED__
typedef struct IFsrmClassifierModuleDefinition IFsrmClassifierModuleDefinition;
#endif /* __IFsrmClassifierModuleDefinition_FWD_DEFINED__ */

#ifndef __IFsrmClassifierModuleImplementation_FWD_DEFINED__
#define __IFsrmClassifierModuleImplementation_FWD_DEFINED__
typedef struct IFsrmClassifierModuleImplementation IFsrmClassifierModuleImplementation;
#endif /* __IFsrmClassifierModuleImplementation_FWD_DEFINED__ */

#ifndef __IFsrmCollection_FWD_DEFINED__
#define __IFsrmCollection_FWD_DEFINED__
typedef struct IFsrmCollection IFsrmCollection;
#endif /* __IFsrmCollection_FWD_DEFINED__ */

#ifndef __IFsrmCommittableCollection_FWD_DEFINED__
#define __IFsrmCommittableCollection_FWD_DEFINED__
typedef struct IFsrmCommittableCollection IFsrmCommittableCollection;
#endif /* __IFsrmCommittableCollection_FWD_DEFINED__ */

#ifndef __IFsrmDerivedObjectsResult_FWD_DEFINED__
#define __IFsrmDerivedObjectsResult_FWD_DEFINED__
typedef struct IFsrmDerivedObjectsResult IFsrmDerivedObjectsResult;
#endif /* __IFsrmDerivedObjectsResult_FWD_DEFINED__ */

#ifndef __IFsrmExportImport_FWD_DEFINED__
#define __IFsrmExportImport_FWD_DEFINED__
typedef struct IFsrmExportImport IFsrmExportImport;
#endif /* __IFsrmExportImport_FWD_DEFINED__ */

#ifndef __IFsrmFileGroup_FWD_DEFINED__
#define __IFsrmFileGroup_FWD_DEFINED__
typedef struct IFsrmFileGroup IFsrmFileGroup;
#endif /* __IFsrmFileGroup_FWD_DEFINED__ */

#ifndef __IFsrmFileGroupImported_FWD_DEFINED__
#define __IFsrmFileGroupImported_FWD_DEFINED__
typedef struct IFsrmFileGroupImported IFsrmFileGroupImported;
#endif /* __IFsrmFileGroupImported_FWD_DEFINED__ */

#ifndef __IFsrmFileGroupManager_FWD_DEFINED__
#define __IFsrmFileGroupManager_FWD_DEFINED__
typedef struct IFsrmFileGroupManager IFsrmFileGroupManager;
#endif /* __IFsrmFileGroupManager_FWD_DEFINED__ */

#ifndef __IFsrmFileManagementJob_FWD_DEFINED__
#define __IFsrmFileManagementJob_FWD_DEFINED__
typedef struct IFsrmFileManagementJob IFsrmFileManagementJob;
#endif /* __IFsrmFileManagementJob_FWD_DEFINED__ */

#ifndef __IFsrmFileManagementJobManager_FWD_DEFINED__
#define __IFsrmFileManagementJobManager_FWD_DEFINED__
typedef struct IFsrmFileManagementJobManager IFsrmFileManagementJobManager;
#endif /* __IFsrmFileManagementJobManager_FWD_DEFINED__ */

#ifndef __IFsrmFileScreen_FWD_DEFINED__
#define __IFsrmFileScreen_FWD_DEFINED__
typedef struct IFsrmFileScreen IFsrmFileScreen;
#endif /* __IFsrmFileScreen_FWD_DEFINED__ */

#ifndef __IFsrmFileScreenBase_FWD_DEFINED__
#define __IFsrmFileScreenBase_FWD_DEFINED__
typedef struct IFsrmFileScreenBase IFsrmFileScreenBase;
#endif /* __IFsrmFileScreenBase_FWD_DEFINED__ */

#ifndef __IFsrmFileScreenException_FWD_DEFINED__
#define __IFsrmFileScreenException_FWD_DEFINED__
typedef struct IFsrmFileScreenException IFsrmFileScreenException;
#endif /* __IFsrmFileScreenException_FWD_DEFINED__ */

#ifndef __IFsrmFileScreenManager_FWD_DEFINED__
#define __IFsrmFileScreenManager_FWD_DEFINED__
typedef struct IFsrmFileScreenManager IFsrmFileScreenManager;
#endif /* __IFsrmFileScreenManager_FWD_DEFINED__ */

#ifndef __IFsrmFileScreenTemplate_FWD_DEFINED__
#define __IFsrmFileScreenTemplate_FWD_DEFINED__
typedef struct IFsrmFileScreenTemplate IFsrmFileScreenTemplate;
#endif /* __IFsrmFileScreenTemplate_FWD_DEFINED__ */

#ifndef __IFsrmFileScreenTemplateImported_FWD_DEFINED__
#define __IFsrmFileScreenTemplateImported_FWD_DEFINED__
typedef struct IFsrmFileScreenTemplateImported IFsrmFileScreenTemplateImported;
#endif /* __IFsrmFileScreenTemplateImported_FWD_DEFINED__ */

#ifndef __IFsrmFileScreenTemplateManager_FWD_DEFINED__
#define __IFsrmFileScreenTemplateManager_FWD_DEFINED__
typedef struct IFsrmFileScreenTemplateManager IFsrmFileScreenTemplateManager;
#endif /* __IFsrmFileScreenTemplateManager_FWD_DEFINED__ */

#ifndef __IFsrmMutableCollection_FWD_DEFINED__
#define __IFsrmMutableCollection_FWD_DEFINED__
typedef struct IFsrmMutableCollection IFsrmMutableCollection;
#endif /* __IFsrmMutableCollection_FWD_DEFINED__ */

#ifndef __IFsrmObject_FWD_DEFINED__
#define __IFsrmObject_FWD_DEFINED__
typedef struct IFsrmObject IFsrmObject;
#endif /* __IFsrmObject_FWD_DEFINED__ */

#ifndef __IFsrmPathMapper_FWD_DEFINED__
#define __IFsrmPathMapper_FWD_DEFINED__
typedef struct IFsrmPathMapper IFsrmPathMapper;
#endif /* __IFsrmPathMapper_FWD_DEFINED__ */

#ifndef __IFsrmPipelineModuleConnector_FWD_DEFINED__
#define __IFsrmPipelineModuleConnector_FWD_DEFINED__
typedef struct IFsrmPipelineModuleConnector IFsrmPipelineModuleConnector;
#endif /* __IFsrmPipelineModuleConnector_FWD_DEFINED__ */

#ifndef __IFsrmPipelineModuleDefinition_FWD_DEFINED__
#define __IFsrmPipelineModuleDefinition_FWD_DEFINED__
typedef struct IFsrmPipelineModuleDefinition IFsrmPipelineModuleDefinition;
#endif /* __IFsrmPipelineModuleDefinition_FWD_DEFINED__ */

#ifndef __IFsrmPipelineModuleImplementation_FWD_DEFINED__
#define __IFsrmPipelineModuleImplementation_FWD_DEFINED__
typedef struct IFsrmPipelineModuleImplementation IFsrmPipelineModuleImplementation;
#endif /* __IFsrmPipelineModuleImplementation_FWD_DEFINED__ */

#ifndef __IFsrmProperty_FWD_DEFINED__
#define __IFsrmProperty_FWD_DEFINED__
typedef struct IFsrmProperty IFsrmProperty;
#endif /* __IFsrmProperty_FWD_DEFINED__ */

#ifndef __IFsrmPropertyBag_FWD_DEFINED__
#define __IFsrmPropertyBag_FWD_DEFINED__
typedef struct IFsrmPropertyBag IFsrmPropertyBag;
#endif /* __IFsrmPropertyBag_FWD_DEFINED__ */

#ifndef __IFsrmPropertyCondition_FWD_DEFINED__
#define __IFsrmPropertyCondition_FWD_DEFINED__
typedef struct IFsrmPropertyCondition IFsrmPropertyCondition;
#endif /* __IFsrmPropertyCondition_FWD_DEFINED__ */

#ifndef __IFsrmPropertyDefinition_FWD_DEFINED__
#define __IFsrmPropertyDefinition_FWD_DEFINED__
typedef struct IFsrmPropertyDefinition IFsrmPropertyDefinition;
#endif /* __IFsrmPropertyDefinition_FWD_DEFINED__ */

#ifndef __IFsrmQuota_FWD_DEFINED__
#define __IFsrmQuota_FWD_DEFINED__
typedef struct IFsrmQuota IFsrmQuota;
#endif /* __IFsrmQuota_FWD_DEFINED__ */

#ifndef __IFsrmQuotaBase_FWD_DEFINED__
#define __IFsrmQuotaBase_FWD_DEFINED__
typedef struct IFsrmQuotaBase IFsrmQuotaBase;
#endif /* __IFsrmQuotaBase_FWD_DEFINED__ */

#ifndef __IFsrmQuotaManager_FWD_DEFINED__
#define __IFsrmQuotaManager_FWD_DEFINED__
typedef struct IFsrmQuotaManager IFsrmQuotaManager;
#endif /* __IFsrmQuotaManager_FWD_DEFINED__ */

#ifndef __IFsrmQuotaManagerEx_FWD_DEFINED__
#define __IFsrmQuotaManagerEx_FWD_DEFINED__
typedef struct IFsrmQuotaManagerEx IFsrmQuotaManagerEx;
#endif /* __IFsrmQuotaManagerEx_FWD_DEFINED__ */

#ifndef __IFsrmQuotaObject_FWD_DEFINED__
#define __IFsrmQuotaObject_FWD_DEFINED__
typedef struct IFsrmQuotaObject IFsrmQuotaObject;
#endif /* __IFsrmQuotaObject_FWD_DEFINED__ */

#ifndef __IFsrmQuotaTemplate_FWD_DEFINED__
#define __IFsrmQuotaTemplate_FWD_DEFINED__
typedef struct IFsrmQuotaTemplate IFsrmQuotaTemplate;
#endif /* __IFsrmQuotaTemplate_FWD_DEFINED__ */

#ifndef __IFsrmQuotaTemplateImported_FWD_DEFINED__
#define __IFsrmQuotaTemplateImported_FWD_DEFINED__
typedef struct IFsrmQuotaTemplateImported IFsrmQuotaTemplateImported;
#endif /* __IFsrmQuotaTemplateImported_FWD_DEFINED__ */

#ifndef __IFsrmQuotaTemplateManager_FWD_DEFINED__
#define __IFsrmQuotaTemplateManager_FWD_DEFINED__
typedef struct IFsrmQuotaTemplateManager IFsrmQuotaTemplateManager;
#endif /* __IFsrmQuotaTemplateManager_FWD_DEFINED__ */

#ifndef __IFsrmReport_FWD_DEFINED__
#define __IFsrmReport_FWD_DEFINED__
typedef struct IFsrmReport IFsrmReport;
#endif /* __IFsrmReport_FWD_DEFINED__ */

#ifndef __IFsrmReportJob_FWD_DEFINED__
#define __IFsrmReportJob_FWD_DEFINED__
typedef struct IFsrmReportJob IFsrmReportJob;
#endif /* __IFsrmReportJob_FWD_DEFINED__ */

#ifndef __IFsrmReportManager_FWD_DEFINED__
#define __IFsrmReportManager_FWD_DEFINED__
typedef struct IFsrmReportManager IFsrmReportManager;
#endif /* __IFsrmReportManager_FWD_DEFINED__ */

#ifndef __IFsrmReportScheduler_FWD_DEFINED__
#define __IFsrmReportScheduler_FWD_DEFINED__
typedef struct IFsrmReportScheduler IFsrmReportScheduler;
#endif /* __IFsrmReportScheduler_FWD_DEFINED__ */

#ifndef __IFsrmRule_FWD_DEFINED__
#define __IFsrmRule_FWD_DEFINED__
typedef struct IFsrmRule IFsrmRule;
#endif /* __IFsrmRule_FWD_DEFINED__ */

#ifndef __IFsrmSetting_FWD_DEFINED__
#define __IFsrmSetting_FWD_DEFINED__
typedef struct IFsrmSetting IFsrmSetting;
#endif /* __IFsrmSetting_FWD_DEFINED__ */

#ifndef __IFsrmStorageModuleDefinition_FWD_DEFINED__
#define __IFsrmStorageModuleDefinition_FWD_DEFINED__
typedef struct IFsrmStorageModuleDefinition IFsrmStorageModuleDefinition;
#endif /* __IFsrmStorageModuleDefinition_FWD_DEFINED__ */

#ifndef __IFsrmStorageModuleImplementation_FWD_DEFINED__
#define __IFsrmStorageModuleImplementation_FWD_DEFINED__
typedef struct IFsrmStorageModuleImplementation IFsrmStorageModuleImplementation;
#endif /* __IFsrmStorageModuleImplementation_FWD_DEFINED__ */
