/*** Autogenerated by WIDL 1.6 from include/objidl.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __objidl_h__
#define __objidl_h__

/* Forward declarations */

#ifndef __IMarshal_FWD_DEFINED__
#define __IMarshal_FWD_DEFINED__
typedef interface IMarshal IMarshal;
#endif

#ifndef __INoMarshal_FWD_DEFINED__
#define __INoMarshal_FWD_DEFINED__
typedef interface INoMarshal INoMarshal;
#endif

#ifndef __IAgileObject_FWD_DEFINED__
#define __IAgileObject_FWD_DEFINED__
typedef interface IAgileObject IAgileObject;
#endif

#ifndef __IMarshal2_FWD_DEFINED__
#define __IMarshal2_FWD_DEFINED__
typedef interface IMarshal2 IMarshal2;
#endif

#ifndef __IMalloc_FWD_DEFINED__
#define __IMalloc_FWD_DEFINED__
typedef interface IMalloc IMalloc;
#endif

#ifndef __IStdMarshalInfo_FWD_DEFINED__
#define __IStdMarshalInfo_FWD_DEFINED__
typedef interface IStdMarshalInfo IStdMarshalInfo;
#endif

#ifndef __IExternalConnection_FWD_DEFINED__
#define __IExternalConnection_FWD_DEFINED__
typedef interface IExternalConnection IExternalConnection;
#endif

#ifndef __IMultiQI_FWD_DEFINED__
#define __IMultiQI_FWD_DEFINED__
typedef interface IMultiQI IMultiQI;
#endif

#ifndef __AsyncIMultiQI_FWD_DEFINED__
#define __AsyncIMultiQI_FWD_DEFINED__
typedef interface AsyncIMultiQI AsyncIMultiQI;
#endif

#ifndef __IInternalUnknown_FWD_DEFINED__
#define __IInternalUnknown_FWD_DEFINED__
typedef interface IInternalUnknown IInternalUnknown;
#endif

#ifndef __IEnumUnknown_FWD_DEFINED__
#define __IEnumUnknown_FWD_DEFINED__
typedef interface IEnumUnknown IEnumUnknown;
#endif

#ifndef __IEnumString_FWD_DEFINED__
#define __IEnumString_FWD_DEFINED__
typedef interface IEnumString IEnumString;
#endif

#ifndef __ISequentialStream_FWD_DEFINED__
#define __ISequentialStream_FWD_DEFINED__
typedef interface ISequentialStream ISequentialStream;
#endif

#ifndef __IStream_FWD_DEFINED__
#define __IStream_FWD_DEFINED__
typedef interface IStream IStream;
#endif

#ifndef __IRpcChannelBuffer_FWD_DEFINED__
#define __IRpcChannelBuffer_FWD_DEFINED__
typedef interface IRpcChannelBuffer IRpcChannelBuffer;
#endif

#ifndef __IRpcChannelBuffer2_FWD_DEFINED__
#define __IRpcChannelBuffer2_FWD_DEFINED__
typedef interface IRpcChannelBuffer2 IRpcChannelBuffer2;
#endif

#ifndef __IAsyncRpcChannelBuffer_FWD_DEFINED__
#define __IAsyncRpcChannelBuffer_FWD_DEFINED__
typedef interface IAsyncRpcChannelBuffer IAsyncRpcChannelBuffer;
#endif

#ifndef __IRpcChannelBuffer3_FWD_DEFINED__
#define __IRpcChannelBuffer3_FWD_DEFINED__
typedef interface IRpcChannelBuffer3 IRpcChannelBuffer3;
#endif

#ifndef __IRpcSyntaxNegotiate_FWD_DEFINED__
#define __IRpcSyntaxNegotiate_FWD_DEFINED__
typedef interface IRpcSyntaxNegotiate IRpcSyntaxNegotiate;
#endif

#ifndef __IRpcProxyBuffer_FWD_DEFINED__
#define __IRpcProxyBuffer_FWD_DEFINED__
typedef interface IRpcProxyBuffer IRpcProxyBuffer;
#endif

#ifndef __IRpcStubBuffer_FWD_DEFINED__
#define __IRpcStubBuffer_FWD_DEFINED__
typedef interface IRpcStubBuffer IRpcStubBuffer;
#endif

#ifndef __IPSFactoryBuffer_FWD_DEFINED__
#define __IPSFactoryBuffer_FWD_DEFINED__
typedef interface IPSFactoryBuffer IPSFactoryBuffer;
#endif

#ifndef __IChannelHook_FWD_DEFINED__
#define __IChannelHook_FWD_DEFINED__
typedef interface IChannelHook IChannelHook;
#endif

#ifndef __IClientSecurity_FWD_DEFINED__
#define __IClientSecurity_FWD_DEFINED__
typedef interface IClientSecurity IClientSecurity;
#endif

#ifndef __IServerSecurity_FWD_DEFINED__
#define __IServerSecurity_FWD_DEFINED__
typedef interface IServerSecurity IServerSecurity;
#endif

#ifndef __IRpcOptions_FWD_DEFINED__
#define __IRpcOptions_FWD_DEFINED__
typedef interface IRpcOptions IRpcOptions;
#endif

#ifndef __IGlobalOptions_FWD_DEFINED__
#define __IGlobalOptions_FWD_DEFINED__
typedef interface IGlobalOptions IGlobalOptions;
#endif

#ifndef __ISurrogate_FWD_DEFINED__
#define __ISurrogate_FWD_DEFINED__
typedef interface ISurrogate ISurrogate;
#endif

#ifndef __IGlobalInterfaceTable_FWD_DEFINED__
#define __IGlobalInterfaceTable_FWD_DEFINED__
typedef interface IGlobalInterfaceTable IGlobalInterfaceTable;
#endif

#ifndef __ISynchronize_FWD_DEFINED__
#define __ISynchronize_FWD_DEFINED__
typedef interface ISynchronize ISynchronize;
#endif

#ifndef __ISynchronizeHandle_FWD_DEFINED__
#define __ISynchronizeHandle_FWD_DEFINED__
typedef interface ISynchronizeHandle ISynchronizeHandle;
#endif

#ifndef __ISynchronizeEvent_FWD_DEFINED__
#define __ISynchronizeEvent_FWD_DEFINED__
typedef interface ISynchronizeEvent ISynchronizeEvent;
#endif

#ifndef __ISynchronizeContainer_FWD_DEFINED__
#define __ISynchronizeContainer_FWD_DEFINED__
typedef interface ISynchronizeContainer ISynchronizeContainer;
#endif

#ifndef __ISynchronizeMutex_FWD_DEFINED__
#define __ISynchronizeMutex_FWD_DEFINED__
typedef interface ISynchronizeMutex ISynchronizeMutex;
#endif

#ifndef __ICancelMethodCalls_FWD_DEFINED__
#define __ICancelMethodCalls_FWD_DEFINED__
typedef interface ICancelMethodCalls ICancelMethodCalls;
#endif

#ifndef __IAsyncManager_FWD_DEFINED__
#define __IAsyncManager_FWD_DEFINED__
typedef interface IAsyncManager IAsyncManager;
#endif

#ifndef __ICallFactory_FWD_DEFINED__
#define __ICallFactory_FWD_DEFINED__
typedef interface ICallFactory ICallFactory;
#endif

#ifndef __IRpcHelper_FWD_DEFINED__
#define __IRpcHelper_FWD_DEFINED__
typedef interface IRpcHelper IRpcHelper;
#endif

#ifndef __IReleaseMarshalBuffers_FWD_DEFINED__
#define __IReleaseMarshalBuffers_FWD_DEFINED__
typedef interface IReleaseMarshalBuffers IReleaseMarshalBuffers;
#endif

#ifndef __IWaitMultiple_FWD_DEFINED__
#define __IWaitMultiple_FWD_DEFINED__
typedef interface IWaitMultiple IWaitMultiple;
#endif

#ifndef __IAddrTrackingControl_FWD_DEFINED__
#define __IAddrTrackingControl_FWD_DEFINED__
typedef interface IAddrTrackingControl IAddrTrackingControl;
#endif

#ifndef __IAddrExclusionControl_FWD_DEFINED__
#define __IAddrExclusionControl_FWD_DEFINED__
typedef interface IAddrExclusionControl IAddrExclusionControl;
#endif

#ifndef __IPipeByte_FWD_DEFINED__
#define __IPipeByte_FWD_DEFINED__
typedef interface IPipeByte IPipeByte;
#endif

#ifndef __IPipeLong_FWD_DEFINED__
#define __IPipeLong_FWD_DEFINED__
typedef interface IPipeLong IPipeLong;
#endif

#ifndef __IPipeDouble_FWD_DEFINED__
#define __IPipeDouble_FWD_DEFINED__
typedef interface IPipeDouble IPipeDouble;
#endif

#ifndef __IEnumContextProps_FWD_DEFINED__
#define __IEnumContextProps_FWD_DEFINED__
typedef interface IEnumContextProps IEnumContextProps;
#endif

#ifndef __IContext_FWD_DEFINED__
#define __IContext_FWD_DEFINED__
typedef interface IContext IContext;
#endif

#ifndef __IComThreadingInfo_FWD_DEFINED__
#define __IComThreadingInfo_FWD_DEFINED__
typedef interface IComThreadingInfo IComThreadingInfo;
#endif

#ifndef __IProcessInitControl_FWD_DEFINED__
#define __IProcessInitControl_FWD_DEFINED__
typedef interface IProcessInitControl IProcessInitControl;
#endif

#ifndef __IFastRundown_FWD_DEFINED__
#define __IFastRundown_FWD_DEFINED__
typedef interface IFastRundown IFastRundown;
#endif

#ifndef __IMarshalingStream_FWD_DEFINED__
#define __IMarshalingStream_FWD_DEFINED__
typedef interface IMarshalingStream IMarshalingStream;
#endif

#ifndef __IMallocSpy_FWD_DEFINED__
#define __IMallocSpy_FWD_DEFINED__
typedef interface IMallocSpy IMallocSpy;
#endif

#ifndef __IBindCtx_FWD_DEFINED__
#define __IBindCtx_FWD_DEFINED__
typedef interface IBindCtx IBindCtx;
#endif

#ifndef __IEnumMoniker_FWD_DEFINED__
#define __IEnumMoniker_FWD_DEFINED__
typedef interface IEnumMoniker IEnumMoniker;
#endif

#ifndef __IRunnableObject_FWD_DEFINED__
#define __IRunnableObject_FWD_DEFINED__
typedef interface IRunnableObject IRunnableObject;
#endif

#ifndef __IRunningObjectTable_FWD_DEFINED__
#define __IRunningObjectTable_FWD_DEFINED__
typedef interface IRunningObjectTable IRunningObjectTable;
#endif

#ifndef __IPersist_FWD_DEFINED__
#define __IPersist_FWD_DEFINED__
typedef interface IPersist IPersist;
#endif

#ifndef __IPersistStream_FWD_DEFINED__
#define __IPersistStream_FWD_DEFINED__
typedef interface IPersistStream IPersistStream;
#endif

#ifndef __IMoniker_FWD_DEFINED__
#define __IMoniker_FWD_DEFINED__
typedef interface IMoniker IMoniker;
#endif

#ifndef __IROTData_FWD_DEFINED__
#define __IROTData_FWD_DEFINED__
typedef interface IROTData IROTData;
#endif

#ifndef __IEnumSTATSTG_FWD_DEFINED__
#define __IEnumSTATSTG_FWD_DEFINED__
typedef interface IEnumSTATSTG IEnumSTATSTG;
#endif

#ifndef __IStorage_FWD_DEFINED__
#define __IStorage_FWD_DEFINED__
typedef interface IStorage IStorage;
#endif

#ifndef __IPersistFile_FWD_DEFINED__
#define __IPersistFile_FWD_DEFINED__
typedef interface IPersistFile IPersistFile;
#endif

#ifndef __IPersistStorage_FWD_DEFINED__
#define __IPersistStorage_FWD_DEFINED__
typedef interface IPersistStorage IPersistStorage;
#endif

#ifndef __ILockBytes_FWD_DEFINED__
#define __ILockBytes_FWD_DEFINED__
typedef interface ILockBytes ILockBytes;
#endif

#ifndef __IEnumFORMATETC_FWD_DEFINED__
#define __IEnumFORMATETC_FWD_DEFINED__
typedef interface IEnumFORMATETC IEnumFORMATETC;
#endif

#ifndef __IEnumSTATDATA_FWD_DEFINED__
#define __IEnumSTATDATA_FWD_DEFINED__
typedef interface IEnumSTATDATA IEnumSTATDATA;
#endif

#ifndef __IRootStorage_FWD_DEFINED__
#define __IRootStorage_FWD_DEFINED__
typedef interface IRootStorage IRootStorage;
#endif

#ifndef __IAdviseSink_FWD_DEFINED__
#define __IAdviseSink_FWD_DEFINED__
typedef interface IAdviseSink IAdviseSink;
#endif

#ifndef __AsyncIAdviseSink_FWD_DEFINED__
#define __AsyncIAdviseSink_FWD_DEFINED__
typedef interface AsyncIAdviseSink AsyncIAdviseSink;
#endif

#ifndef __IAdviseSink2_FWD_DEFINED__
#define __IAdviseSink2_FWD_DEFINED__
typedef interface IAdviseSink2 IAdviseSink2;
#endif

#ifndef __AsyncIAdviseSink2_FWD_DEFINED__
#define __AsyncIAdviseSink2_FWD_DEFINED__
typedef interface AsyncIAdviseSink2 AsyncIAdviseSink2;
#endif

#ifndef __IDataObject_FWD_DEFINED__
#define __IDataObject_FWD_DEFINED__
typedef interface IDataObject IDataObject;
#endif

#ifndef __IDataAdviseHolder_FWD_DEFINED__
#define __IDataAdviseHolder_FWD_DEFINED__
typedef interface IDataAdviseHolder IDataAdviseHolder;
#endif

#ifndef __IMessageFilter_FWD_DEFINED__
#define __IMessageFilter_FWD_DEFINED__
typedef interface IMessageFilter IMessageFilter;
#endif

#ifndef __IClassActivator_FWD_DEFINED__
#define __IClassActivator_FWD_DEFINED__
typedef interface IClassActivator IClassActivator;
#endif

#ifndef __IFillLockBytes_FWD_DEFINED__
#define __IFillLockBytes_FWD_DEFINED__
typedef interface IFillLockBytes IFillLockBytes;
#endif

#ifndef __IProgressNotify_FWD_DEFINED__
#define __IProgressNotify_FWD_DEFINED__
typedef interface IProgressNotify IProgressNotify;
#endif

#ifndef __ILayoutStorage_FWD_DEFINED__
#define __ILayoutStorage_FWD_DEFINED__
typedef interface ILayoutStorage ILayoutStorage;
#endif

#ifndef __IBlockingLock_FWD_DEFINED__
#define __IBlockingLock_FWD_DEFINED__
typedef interface IBlockingLock IBlockingLock;
#endif

#ifndef __ITimeAndNoticeControl_FWD_DEFINED__
#define __ITimeAndNoticeControl_FWD_DEFINED__
typedef interface ITimeAndNoticeControl ITimeAndNoticeControl;
#endif

#ifndef __IOplockStorage_FWD_DEFINED__
#define __IOplockStorage_FWD_DEFINED__
typedef interface IOplockStorage IOplockStorage;
#endif

#ifndef __IDirectWriterLock_FWD_DEFINED__
#define __IDirectWriterLock_FWD_DEFINED__
typedef interface IDirectWriterLock IDirectWriterLock;
#endif

#ifndef __IUrlMon_FWD_DEFINED__
#define __IUrlMon_FWD_DEFINED__
typedef interface IUrlMon IUrlMon;
#endif

#ifndef __IForegroundTransfer_FWD_DEFINED__
#define __IForegroundTransfer_FWD_DEFINED__
typedef interface IForegroundTransfer IForegroundTransfer;
#endif

#ifndef __IThumbnailExtractor_FWD_DEFINED__
#define __IThumbnailExtractor_FWD_DEFINED__
typedef interface IThumbnailExtractor IThumbnailExtractor;
#endif

#ifndef __IDummyHICONIncluder_FWD_DEFINED__
#define __IDummyHICONIncluder_FWD_DEFINED__
typedef interface IDummyHICONIncluder IDummyHICONIncluder;
#endif

#ifndef __IProcessLock_FWD_DEFINED__
#define __IProcessLock_FWD_DEFINED__
typedef interface IProcessLock IProcessLock;
#endif

#ifndef __ISurrogateService_FWD_DEFINED__
#define __ISurrogateService_FWD_DEFINED__
typedef interface ISurrogateService ISurrogateService;
#endif

#ifndef __IInitializeSpy_FWD_DEFINED__
#define __IInitializeSpy_FWD_DEFINED__
typedef interface IInitializeSpy IInitializeSpy;
#endif

#ifndef __IApartmentShutdown_FWD_DEFINED__
#define __IApartmentShutdown_FWD_DEFINED__
typedef interface IApartmentShutdown IApartmentShutdown;
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <wtypes.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if (NTDDI_VERSION >= NTDDI_VISTA && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0600
#endif
#if(NTDDI_VERSION >= NTDDI_WS03 && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0502
#endif
#if(NTDDI_VERSION >= NTDDI_WINXP && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0501
#endif
/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if (NTDDI_VERSION >= NTDDI_VISTA && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0600
#endif

#if (NTDDI_VERSION >= NTDDI_WS03 && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0502
#endif

#if (NTDDI_VERSION >= NTDDI_WINXP && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0501
#endif

#ifndef _OBJIDLBASE_
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#ifndef __IStream_FWD_DEFINED__
#define __IStream_FWD_DEFINED__
typedef interface IStream IStream;
#endif

#ifndef __IEnumString_FWD_DEFINED__
#define __IEnumString_FWD_DEFINED__
typedef interface IEnumString IEnumString;
#endif

#ifndef __IMultiQI_FWD_DEFINED__
#define __IMultiQI_FWD_DEFINED__
typedef interface IMultiQI IMultiQI;
#endif

#ifndef __AsyncIMultiQI_FWD_DEFINED__
#define __AsyncIMultiQI_FWD_DEFINED__
typedef interface AsyncIMultiQI AsyncIMultiQI;
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IAsyncManager_FWD_DEFINED__
#define __IAsyncManager_FWD_DEFINED__
typedef interface IAsyncManager IAsyncManager;
#endif

#ifndef __ICallFactory_FWD_DEFINED__
#define __ICallFactory_FWD_DEFINED__
typedef interface ICallFactory ICallFactory;
#endif

#ifndef __ISynchronize_FWD_DEFINED__
#define __ISynchronize_FWD_DEFINED__
typedef interface ISynchronize ISynchronize;
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef struct _COSERVERINFO {
    DWORD dwReserved1;
    LPWSTR pwszName;
    COAUTHINFO *pAuthInfo;
    DWORD dwReserved2;
} COSERVERINFO;

/*****************************************************************************
 * IMarshal interface
 */
#ifndef __IMarshal_INTERFACE_DEFINED__
#define __IMarshal_INTERFACE_DEFINED__

typedef IMarshal *LPMARSHAL;
DEFINE_GUID(IID_IMarshal, 0x00000003, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000003-0000-0000-c000-000000000046")
IMarshal : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetUnmarshalClass(
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        CLSID *pCid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMarshalSizeMax(
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        DWORD *pSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE MarshalInterface(
        IStream *pStm,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnmarshalInterface(
        IStream *pStm,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseMarshalData(
        IStream *pStm) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisconnectObject(
        DWORD dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMarshal, 0x00000003, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMarshalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMarshal* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMarshal* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMarshal* This);

    /*** IMarshal methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUnmarshalClass)(
        IMarshal* This,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        CLSID *pCid);

    HRESULT (STDMETHODCALLTYPE *GetMarshalSizeMax)(
        IMarshal* This,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        DWORD *pSize);

    HRESULT (STDMETHODCALLTYPE *MarshalInterface)(
        IMarshal* This,
        IStream *pStm,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags);

    HRESULT (STDMETHODCALLTYPE *UnmarshalInterface)(
        IMarshal* This,
        IStream *pStm,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *ReleaseMarshalData)(
        IMarshal* This,
        IStream *pStm);

    HRESULT (STDMETHODCALLTYPE *DisconnectObject)(
        IMarshal* This,
        DWORD dwReserved);

    END_INTERFACE
} IMarshalVtbl;
interface IMarshal {
    CONST_VTBL IMarshalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMarshal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMarshal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMarshal_Release(This) (This)->lpVtbl->Release(This)
/*** IMarshal methods ***/
#define IMarshal_GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid) (This)->lpVtbl->GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid)
#define IMarshal_GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize) (This)->lpVtbl->GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize)
#define IMarshal_MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags) (This)->lpVtbl->MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags)
#define IMarshal_UnmarshalInterface(This,pStm,riid,ppv) (This)->lpVtbl->UnmarshalInterface(This,pStm,riid,ppv)
#define IMarshal_ReleaseMarshalData(This,pStm) (This)->lpVtbl->ReleaseMarshalData(This,pStm)
#define IMarshal_DisconnectObject(This,dwReserved) (This)->lpVtbl->DisconnectObject(This,dwReserved)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMarshal_QueryInterface(IMarshal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMarshal_AddRef(IMarshal* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMarshal_Release(IMarshal* This) {
    return This->lpVtbl->Release(This);
}
/*** IMarshal methods ***/
static FORCEINLINE HRESULT IMarshal_GetUnmarshalClass(IMarshal* This,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags,CLSID *pCid) {
    return This->lpVtbl->GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid);
}
static FORCEINLINE HRESULT IMarshal_GetMarshalSizeMax(IMarshal* This,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags,DWORD *pSize) {
    return This->lpVtbl->GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize);
}
static FORCEINLINE HRESULT IMarshal_MarshalInterface(IMarshal* This,IStream *pStm,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags) {
    return This->lpVtbl->MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags);
}
static FORCEINLINE HRESULT IMarshal_UnmarshalInterface(IMarshal* This,IStream *pStm,REFIID riid,void **ppv) {
    return This->lpVtbl->UnmarshalInterface(This,pStm,riid,ppv);
}
static FORCEINLINE HRESULT IMarshal_ReleaseMarshalData(IMarshal* This,IStream *pStm) {
    return This->lpVtbl->ReleaseMarshalData(This,pStm);
}
static FORCEINLINE HRESULT IMarshal_DisconnectObject(IMarshal* This,DWORD dwReserved) {
    return This->lpVtbl->DisconnectObject(This,dwReserved);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMarshal_GetUnmarshalClass_Proxy(
    IMarshal* This,
    REFIID riid,
    void *pv,
    DWORD dwDestContext,
    void *pvDestContext,
    DWORD mshlflags,
    CLSID *pCid);
void __RPC_STUB IMarshal_GetUnmarshalClass_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMarshal_GetMarshalSizeMax_Proxy(
    IMarshal* This,
    REFIID riid,
    void *pv,
    DWORD dwDestContext,
    void *pvDestContext,
    DWORD mshlflags,
    DWORD *pSize);
void __RPC_STUB IMarshal_GetMarshalSizeMax_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMarshal_MarshalInterface_Proxy(
    IMarshal* This,
    IStream *pStm,
    REFIID riid,
    void *pv,
    DWORD dwDestContext,
    void *pvDestContext,
    DWORD mshlflags);
void __RPC_STUB IMarshal_MarshalInterface_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMarshal_UnmarshalInterface_Proxy(
    IMarshal* This,
    IStream *pStm,
    REFIID riid,
    void **ppv);
void __RPC_STUB IMarshal_UnmarshalInterface_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMarshal_ReleaseMarshalData_Proxy(
    IMarshal* This,
    IStream *pStm);
void __RPC_STUB IMarshal_ReleaseMarshalData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMarshal_DisconnectObject_Proxy(
    IMarshal* This,
    DWORD dwReserved);
void __RPC_STUB IMarshal_DisconnectObject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMarshal_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INoMarshal interface
 */
#ifndef __INoMarshal_INTERFACE_DEFINED__
#define __INoMarshal_INTERFACE_DEFINED__

DEFINE_GUID(IID_INoMarshal, 0xecc8691b, 0xc1db, 0x4dc0, 0x85,0x5e, 0x65,0xf6,0xc5,0x51,0xaf,0x49);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ecc8691b-c1db-4dc0-855e-65f6c551af49")
INoMarshal : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INoMarshal, 0xecc8691b, 0xc1db, 0x4dc0, 0x85,0x5e, 0x65,0xf6,0xc5,0x51,0xaf,0x49)
#endif
#else
typedef struct INoMarshalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INoMarshal* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INoMarshal* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INoMarshal* This);

    END_INTERFACE
} INoMarshalVtbl;
interface INoMarshal {
    CONST_VTBL INoMarshalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INoMarshal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INoMarshal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INoMarshal_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT INoMarshal_QueryInterface(INoMarshal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG INoMarshal_AddRef(INoMarshal* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG INoMarshal_Release(INoMarshal* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __INoMarshal_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAgileObject interface
 */
#ifndef __IAgileObject_INTERFACE_DEFINED__
#define __IAgileObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAgileObject, 0x94ea2b94, 0xe9cc, 0x49e0, 0xc0,0xff, 0xee,0x64,0xca,0x8f,0x5b,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94ea2b94-e9cc-49e0-c0ff-ee64ca8f5b90")
IAgileObject : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAgileObject, 0x94ea2b94, 0xe9cc, 0x49e0, 0xc0,0xff, 0xee,0x64,0xca,0x8f,0x5b,0x90)
#endif
#else
typedef struct IAgileObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAgileObject* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAgileObject* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAgileObject* This);

    END_INTERFACE
} IAgileObjectVtbl;
interface IAgileObject {
    CONST_VTBL IAgileObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAgileObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAgileObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAgileObject_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAgileObject_QueryInterface(IAgileObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAgileObject_AddRef(IAgileObject* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAgileObject_Release(IAgileObject* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IAgileObject_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IMarshal2 interface
 */
#ifndef __IMarshal2_INTERFACE_DEFINED__
#define __IMarshal2_INTERFACE_DEFINED__

typedef IMarshal2 *LPMARSHAL2;
DEFINE_GUID(IID_IMarshal2, 0x000001cf, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001cf-0000-0000-c000-000000000046")
IMarshal2 : public IMarshal
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMarshal2, 0x000001cf, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMarshal2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMarshal2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMarshal2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMarshal2* This);

    /*** IMarshal methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUnmarshalClass)(
        IMarshal2* This,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        CLSID *pCid);

    HRESULT (STDMETHODCALLTYPE *GetMarshalSizeMax)(
        IMarshal2* This,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        DWORD *pSize);

    HRESULT (STDMETHODCALLTYPE *MarshalInterface)(
        IMarshal2* This,
        IStream *pStm,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags);

    HRESULT (STDMETHODCALLTYPE *UnmarshalInterface)(
        IMarshal2* This,
        IStream *pStm,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *ReleaseMarshalData)(
        IMarshal2* This,
        IStream *pStm);

    HRESULT (STDMETHODCALLTYPE *DisconnectObject)(
        IMarshal2* This,
        DWORD dwReserved);

    END_INTERFACE
} IMarshal2Vtbl;
interface IMarshal2 {
    CONST_VTBL IMarshal2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMarshal2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMarshal2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMarshal2_Release(This) (This)->lpVtbl->Release(This)
/*** IMarshal methods ***/
#define IMarshal2_GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid) (This)->lpVtbl->GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid)
#define IMarshal2_GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize) (This)->lpVtbl->GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize)
#define IMarshal2_MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags) (This)->lpVtbl->MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags)
#define IMarshal2_UnmarshalInterface(This,pStm,riid,ppv) (This)->lpVtbl->UnmarshalInterface(This,pStm,riid,ppv)
#define IMarshal2_ReleaseMarshalData(This,pStm) (This)->lpVtbl->ReleaseMarshalData(This,pStm)
#define IMarshal2_DisconnectObject(This,dwReserved) (This)->lpVtbl->DisconnectObject(This,dwReserved)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMarshal2_QueryInterface(IMarshal2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMarshal2_AddRef(IMarshal2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMarshal2_Release(IMarshal2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMarshal methods ***/
static FORCEINLINE HRESULT IMarshal2_GetUnmarshalClass(IMarshal2* This,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags,CLSID *pCid) {
    return This->lpVtbl->GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid);
}
static FORCEINLINE HRESULT IMarshal2_GetMarshalSizeMax(IMarshal2* This,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags,DWORD *pSize) {
    return This->lpVtbl->GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize);
}
static FORCEINLINE HRESULT IMarshal2_MarshalInterface(IMarshal2* This,IStream *pStm,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags) {
    return This->lpVtbl->MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags);
}
static FORCEINLINE HRESULT IMarshal2_UnmarshalInterface(IMarshal2* This,IStream *pStm,REFIID riid,void **ppv) {
    return This->lpVtbl->UnmarshalInterface(This,pStm,riid,ppv);
}
static FORCEINLINE HRESULT IMarshal2_ReleaseMarshalData(IMarshal2* This,IStream *pStm) {
    return This->lpVtbl->ReleaseMarshalData(This,pStm);
}
static FORCEINLINE HRESULT IMarshal2_DisconnectObject(IMarshal2* This,DWORD dwReserved) {
    return This->lpVtbl->DisconnectObject(This,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IMarshal2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMalloc interface
 */
#ifndef __IMalloc_INTERFACE_DEFINED__
#define __IMalloc_INTERFACE_DEFINED__

typedef IMalloc *LPMALLOC;

DEFINE_GUID(IID_IMalloc, 0x00000002, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000002-0000-0000-c000-000000000046")
IMalloc : public IUnknown
{
    virtual void * STDMETHODCALLTYPE Alloc(
        SIZE_T cb) = 0;

    virtual void * STDMETHODCALLTYPE Realloc(
        void *pv,
        SIZE_T cb) = 0;

    virtual void STDMETHODCALLTYPE Free(
        void *pv) = 0;

    virtual SIZE_T STDMETHODCALLTYPE GetSize(
        void *pv) = 0;

    virtual int STDMETHODCALLTYPE DidAlloc(
        void *pv) = 0;

    virtual void STDMETHODCALLTYPE HeapMinimize(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMalloc, 0x00000002, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMallocVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMalloc* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMalloc* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMalloc* This);

    /*** IMalloc methods ***/
    void * (STDMETHODCALLTYPE *Alloc)(
        IMalloc* This,
        SIZE_T cb);

    void * (STDMETHODCALLTYPE *Realloc)(
        IMalloc* This,
        void *pv,
        SIZE_T cb);

    void (STDMETHODCALLTYPE *Free)(
        IMalloc* This,
        void *pv);

    SIZE_T (STDMETHODCALLTYPE *GetSize)(
        IMalloc* This,
        void *pv);

    int (STDMETHODCALLTYPE *DidAlloc)(
        IMalloc* This,
        void *pv);

    void (STDMETHODCALLTYPE *HeapMinimize)(
        IMalloc* This);

    END_INTERFACE
} IMallocVtbl;
interface IMalloc {
    CONST_VTBL IMallocVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMalloc_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMalloc_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMalloc_Release(This) (This)->lpVtbl->Release(This)
/*** IMalloc methods ***/
#define IMalloc_Alloc(This,cb) (This)->lpVtbl->Alloc(This,cb)
#define IMalloc_Realloc(This,pv,cb) (This)->lpVtbl->Realloc(This,pv,cb)
#define IMalloc_Free(This,pv) (This)->lpVtbl->Free(This,pv)
#define IMalloc_GetSize(This,pv) (This)->lpVtbl->GetSize(This,pv)
#define IMalloc_DidAlloc(This,pv) (This)->lpVtbl->DidAlloc(This,pv)
#define IMalloc_HeapMinimize(This) (This)->lpVtbl->HeapMinimize(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMalloc_QueryInterface(IMalloc* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMalloc_AddRef(IMalloc* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMalloc_Release(IMalloc* This) {
    return This->lpVtbl->Release(This);
}
/*** IMalloc methods ***/
static FORCEINLINE void * IMalloc_Alloc(IMalloc* This,SIZE_T cb) {
    return This->lpVtbl->Alloc(This,cb);
}
static FORCEINLINE void * IMalloc_Realloc(IMalloc* This,void *pv,SIZE_T cb) {
    return This->lpVtbl->Realloc(This,pv,cb);
}
static FORCEINLINE void IMalloc_Free(IMalloc* This,void *pv) {
    This->lpVtbl->Free(This,pv);
}
static FORCEINLINE SIZE_T IMalloc_GetSize(IMalloc* This,void *pv) {
    return This->lpVtbl->GetSize(This,pv);
}
static FORCEINLINE int IMalloc_DidAlloc(IMalloc* This,void *pv) {
    return This->lpVtbl->DidAlloc(This,pv);
}
static FORCEINLINE void IMalloc_HeapMinimize(IMalloc* This) {
    This->lpVtbl->HeapMinimize(This);
}
#endif
#endif

#endif

void * STDMETHODCALLTYPE IMalloc_Alloc_Proxy(
    IMalloc* This,
    SIZE_T cb);
void __RPC_STUB IMalloc_Alloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void * STDMETHODCALLTYPE IMalloc_Realloc_Proxy(
    IMalloc* This,
    void *pv,
    SIZE_T cb);
void __RPC_STUB IMalloc_Realloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IMalloc_Free_Proxy(
    IMalloc* This,
    void *pv);
void __RPC_STUB IMalloc_Free_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
SIZE_T STDMETHODCALLTYPE IMalloc_GetSize_Proxy(
    IMalloc* This,
    void *pv);
void __RPC_STUB IMalloc_GetSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
int STDMETHODCALLTYPE IMalloc_DidAlloc_Proxy(
    IMalloc* This,
    void *pv);
void __RPC_STUB IMalloc_DidAlloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IMalloc_HeapMinimize_Proxy(
    IMalloc* This);
void __RPC_STUB IMalloc_HeapMinimize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMalloc_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IStdMarshalInfo interface
 */
#ifndef __IStdMarshalInfo_INTERFACE_DEFINED__
#define __IStdMarshalInfo_INTERFACE_DEFINED__

typedef IStdMarshalInfo *LPSTDMARSHALINFO;

DEFINE_GUID(IID_IStdMarshalInfo, 0x00000018, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000018-0000-0000-c000-000000000046")
IStdMarshalInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClassForHandler(
        DWORD dwDestContext,
        void *pvDestContext,
        CLSID *pClsid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IStdMarshalInfo, 0x00000018, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IStdMarshalInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IStdMarshalInfo* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IStdMarshalInfo* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IStdMarshalInfo* This);

    /*** IStdMarshalInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassForHandler)(
        IStdMarshalInfo* This,
        DWORD dwDestContext,
        void *pvDestContext,
        CLSID *pClsid);

    END_INTERFACE
} IStdMarshalInfoVtbl;
interface IStdMarshalInfo {
    CONST_VTBL IStdMarshalInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IStdMarshalInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IStdMarshalInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IStdMarshalInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IStdMarshalInfo methods ***/
#define IStdMarshalInfo_GetClassForHandler(This,dwDestContext,pvDestContext,pClsid) (This)->lpVtbl->GetClassForHandler(This,dwDestContext,pvDestContext,pClsid)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IStdMarshalInfo_QueryInterface(IStdMarshalInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IStdMarshalInfo_AddRef(IStdMarshalInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IStdMarshalInfo_Release(IStdMarshalInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IStdMarshalInfo methods ***/
static FORCEINLINE HRESULT IStdMarshalInfo_GetClassForHandler(IStdMarshalInfo* This,DWORD dwDestContext,void *pvDestContext,CLSID *pClsid) {
    return This->lpVtbl->GetClassForHandler(This,dwDestContext,pvDestContext,pClsid);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IStdMarshalInfo_GetClassForHandler_Proxy(
    IStdMarshalInfo* This,
    DWORD dwDestContext,
    void *pvDestContext,
    CLSID *pClsid);
void __RPC_STUB IStdMarshalInfo_GetClassForHandler_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IStdMarshalInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IExternalConnection interface
 */
#ifndef __IExternalConnection_INTERFACE_DEFINED__
#define __IExternalConnection_INTERFACE_DEFINED__

typedef IExternalConnection *LPEXTERNALCONNECTION;

typedef enum tagEXTCONN {
    EXTCONN_STRONG = 0x1,
    EXTCONN_WEAK = 0x2,
    EXTCONN_CALLABLE = 0x4
} EXTCONN;

DEFINE_GUID(IID_IExternalConnection, 0x00000019, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000019-0000-0000-c000-000000000046")
IExternalConnection : public IUnknown
{
    virtual DWORD STDMETHODCALLTYPE AddConnection(
        DWORD extconn,
        DWORD reserved) = 0;

    virtual DWORD STDMETHODCALLTYPE ReleaseConnection(
        DWORD extconn,
        DWORD reserved,
        WINBOOL fLastReleaseCloses) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IExternalConnection, 0x00000019, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IExternalConnectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IExternalConnection* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IExternalConnection* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IExternalConnection* This);

    /*** IExternalConnection methods ***/
    DWORD (STDMETHODCALLTYPE *AddConnection)(
        IExternalConnection* This,
        DWORD extconn,
        DWORD reserved);

    DWORD (STDMETHODCALLTYPE *ReleaseConnection)(
        IExternalConnection* This,
        DWORD extconn,
        DWORD reserved,
        WINBOOL fLastReleaseCloses);

    END_INTERFACE
} IExternalConnectionVtbl;
interface IExternalConnection {
    CONST_VTBL IExternalConnectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IExternalConnection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExternalConnection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExternalConnection_Release(This) (This)->lpVtbl->Release(This)
/*** IExternalConnection methods ***/
#define IExternalConnection_AddConnection(This,extconn,reserved) (This)->lpVtbl->AddConnection(This,extconn,reserved)
#define IExternalConnection_ReleaseConnection(This,extconn,reserved,fLastReleaseCloses) (This)->lpVtbl->ReleaseConnection(This,extconn,reserved,fLastReleaseCloses)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IExternalConnection_QueryInterface(IExternalConnection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IExternalConnection_AddRef(IExternalConnection* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IExternalConnection_Release(IExternalConnection* This) {
    return This->lpVtbl->Release(This);
}
/*** IExternalConnection methods ***/
static FORCEINLINE DWORD IExternalConnection_AddConnection(IExternalConnection* This,DWORD extconn,DWORD reserved) {
    return This->lpVtbl->AddConnection(This,extconn,reserved);
}
static FORCEINLINE DWORD IExternalConnection_ReleaseConnection(IExternalConnection* This,DWORD extconn,DWORD reserved,WINBOOL fLastReleaseCloses) {
    return This->lpVtbl->ReleaseConnection(This,extconn,reserved,fLastReleaseCloses);
}
#endif
#endif

#endif

DWORD STDMETHODCALLTYPE IExternalConnection_AddConnection_Proxy(
    IExternalConnection* This,
    DWORD extconn,
    DWORD reserved);
void __RPC_STUB IExternalConnection_AddConnection_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
DWORD STDMETHODCALLTYPE IExternalConnection_ReleaseConnection_Proxy(
    IExternalConnection* This,
    DWORD extconn,
    DWORD reserved,
    WINBOOL fLastReleaseCloses);
void __RPC_STUB IExternalConnection_ReleaseConnection_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IExternalConnection_INTERFACE_DEFINED__ */


typedef IMultiQI *LPMULTIQI;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef struct tagMULTI_QI {
    const IID *pIID;
    IUnknown *pItf;
    HRESULT hr;
} MULTI_QI;

/*****************************************************************************
 * IMultiQI interface
 */
#ifndef __IMultiQI_INTERFACE_DEFINED__
#define __IMultiQI_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMultiQI, 0x00000020, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000020-0000-0000-c000-000000000046")
IMultiQI : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryMultipleInterfaces(
        ULONG cMQIs,
        MULTI_QI *pMQIs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMultiQI, 0x00000020, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMultiQIVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMultiQI* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMultiQI* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMultiQI* This);

    /*** IMultiQI methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryMultipleInterfaces)(
        IMultiQI* This,
        ULONG cMQIs,
        MULTI_QI *pMQIs);

    END_INTERFACE
} IMultiQIVtbl;
interface IMultiQI {
    CONST_VTBL IMultiQIVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMultiQI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMultiQI_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMultiQI_Release(This) (This)->lpVtbl->Release(This)
/*** IMultiQI methods ***/
#define IMultiQI_QueryMultipleInterfaces(This,cMQIs,pMQIs) (This)->lpVtbl->QueryMultipleInterfaces(This,cMQIs,pMQIs)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMultiQI_QueryInterface(IMultiQI* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMultiQI_AddRef(IMultiQI* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMultiQI_Release(IMultiQI* This) {
    return This->lpVtbl->Release(This);
}
/*** IMultiQI methods ***/
static FORCEINLINE HRESULT IMultiQI_QueryMultipleInterfaces(IMultiQI* This,ULONG cMQIs,MULTI_QI *pMQIs) {
    return This->lpVtbl->QueryMultipleInterfaces(This,cMQIs,pMQIs);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMultiQI_QueryMultipleInterfaces_Proxy(
    IMultiQI* This,
    ULONG cMQIs,
    MULTI_QI *pMQIs);
void __RPC_STUB IMultiQI_QueryMultipleInterfaces_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMultiQI_INTERFACE_DEFINED__ */

/*****************************************************************************
 * AsyncIMultiQI interface
 */
#ifndef __AsyncIMultiQI_INTERFACE_DEFINED__
#define __AsyncIMultiQI_INTERFACE_DEFINED__

DEFINE_GUID(IID_AsyncIMultiQI, 0x000e0020, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000e0020-0000-0000-c000-000000000046")
AsyncIMultiQI : public IUnknown
{
    virtual void STDMETHODCALLTYPE Begin_QueryMultipleInterfaces(
        ULONG cMQIs,
        MULTI_QI *pMQIs) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finish_QueryMultipleInterfaces(
        MULTI_QI *pMQIs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AsyncIMultiQI, 0x000e0020, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct AsyncIMultiQIVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        AsyncIMultiQI* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        AsyncIMultiQI* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        AsyncIMultiQI* This);

    /*** IMultiQI methods ***/
    void (STDMETHODCALLTYPE *Begin_QueryMultipleInterfaces)(
        AsyncIMultiQI* This,
        ULONG cMQIs,
        MULTI_QI *pMQIs);

    HRESULT (STDMETHODCALLTYPE *Finish_QueryMultipleInterfaces)(
        AsyncIMultiQI* This,
        MULTI_QI *pMQIs);

    END_INTERFACE
} AsyncIMultiQIVtbl;
interface AsyncIMultiQI {
    CONST_VTBL AsyncIMultiQIVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define AsyncIMultiQI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define AsyncIMultiQI_AddRef(This) (This)->lpVtbl->AddRef(This)
#define AsyncIMultiQI_Release(This) (This)->lpVtbl->Release(This)
/*** IMultiQI methods ***/
#define AsyncIMultiQI_Begin_QueryMultipleInterfaces(This,cMQIs,pMQIs) (This)->lpVtbl->Begin_QueryMultipleInterfaces(This,cMQIs,pMQIs)
#define AsyncIMultiQI_Finish_QueryMultipleInterfaces(This,pMQIs) (This)->lpVtbl->Finish_QueryMultipleInterfaces(This,pMQIs)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT AsyncIMultiQI_QueryInterface(AsyncIMultiQI* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG AsyncIMultiQI_AddRef(AsyncIMultiQI* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG AsyncIMultiQI_Release(AsyncIMultiQI* This) {
    return This->lpVtbl->Release(This);
}
/*** IMultiQI methods ***/
static FORCEINLINE void Begin_AsyncIMultiQI_QueryMultipleInterfaces(AsyncIMultiQI* This,ULONG cMQIs,MULTI_QI *pMQIs) {
    This->lpVtbl->Begin_QueryMultipleInterfaces(This,cMQIs,pMQIs);
}
static FORCEINLINE HRESULT Finish_AsyncIMultiQI_QueryMultipleInterfaces(AsyncIMultiQI* This,MULTI_QI *pMQIs) {
    return This->lpVtbl->Finish_QueryMultipleInterfaces(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE AsyncIMultiQI_Begin_QueryMultipleInterfaces_Proxy(
    IMultiQI* This,
    ULONG cMQIs,
    MULTI_QI *pMQIs);
void __RPC_STUB AsyncIMultiQI_Begin_QueryMultipleInterfaces_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIMultiQI_Finish_QueryMultipleInterfaces_Proxy(
    IMultiQI* This,
    ULONG cMQIs,
    MULTI_QI *pMQIs);
void __RPC_STUB AsyncIMultiQI_Finish_QueryMultipleInterfaces_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __AsyncIMultiQI_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IInternalUnknown interface
 */
#ifndef __IInternalUnknown_INTERFACE_DEFINED__
#define __IInternalUnknown_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternalUnknown, 0x00000021, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000021-0000-0000-c000-000000000046")
IInternalUnknown : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryInternalInterface(
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternalUnknown, 0x00000021, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IInternalUnknownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternalUnknown* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternalUnknown* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternalUnknown* This);

    /*** IInternalUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInternalInterface)(
        IInternalUnknown* This,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IInternalUnknownVtbl;
interface IInternalUnknown {
    CONST_VTBL IInternalUnknownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternalUnknown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternalUnknown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternalUnknown_Release(This) (This)->lpVtbl->Release(This)
/*** IInternalUnknown methods ***/
#define IInternalUnknown_QueryInternalInterface(This,riid,ppv) (This)->lpVtbl->QueryInternalInterface(This,riid,ppv)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IInternalUnknown_QueryInterface(IInternalUnknown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IInternalUnknown_AddRef(IInternalUnknown* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IInternalUnknown_Release(IInternalUnknown* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternalUnknown methods ***/
static FORCEINLINE HRESULT IInternalUnknown_QueryInternalInterface(IInternalUnknown* This,REFIID riid,void **ppv) {
    return This->lpVtbl->QueryInternalInterface(This,riid,ppv);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IInternalUnknown_QueryInternalInterface_Proxy(
    IInternalUnknown* This,
    REFIID riid,
    void **ppv);
void __RPC_STUB IInternalUnknown_QueryInternalInterface_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IInternalUnknown_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IEnumUnknown interface
 */
#ifndef __IEnumUnknown_INTERFACE_DEFINED__
#define __IEnumUnknown_INTERFACE_DEFINED__

typedef IEnumUnknown *LPENUMUNKNOWN;

DEFINE_GUID(IID_IEnumUnknown, 0x00000100, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000100-0000-0000-c000-000000000046")
IEnumUnknown : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        IUnknown **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumUnknown **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumUnknown, 0x00000100, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumUnknownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumUnknown* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumUnknown* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumUnknown* This);

    /*** IEnumUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumUnknown* This,
        ULONG celt,
        IUnknown **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumUnknown* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumUnknown* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumUnknown* This,
        IEnumUnknown **ppenum);

    END_INTERFACE
} IEnumUnknownVtbl;
interface IEnumUnknown {
    CONST_VTBL IEnumUnknownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumUnknown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumUnknown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumUnknown_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumUnknown methods ***/
#define IEnumUnknown_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumUnknown_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumUnknown_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumUnknown_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumUnknown_QueryInterface(IEnumUnknown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumUnknown_AddRef(IEnumUnknown* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumUnknown_Release(IEnumUnknown* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumUnknown methods ***/
static FORCEINLINE HRESULT IEnumUnknown_Next(IEnumUnknown* This,ULONG celt,IUnknown **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static FORCEINLINE HRESULT IEnumUnknown_Skip(IEnumUnknown* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumUnknown_Reset(IEnumUnknown* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumUnknown_Clone(IEnumUnknown* This,IEnumUnknown **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumUnknown_RemoteNext_Proxy(
    IEnumUnknown* This,
    ULONG celt,
    IUnknown **rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumUnknown_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumUnknown_Skip_Proxy(
    IEnumUnknown* This,
    ULONG celt);
void __RPC_STUB IEnumUnknown_Skip_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumUnknown_Reset_Proxy(
    IEnumUnknown* This);
void __RPC_STUB IEnumUnknown_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumUnknown_Clone_Proxy(
    IEnumUnknown* This,
    IEnumUnknown **ppenum);
void __RPC_STUB IEnumUnknown_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumUnknown_Next_Proxy(
    IEnumUnknown* This,
    ULONG celt,
    IUnknown **rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumUnknown_Next_Stub(
    IEnumUnknown* This,
    ULONG celt,
    IUnknown **rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumUnknown_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumString interface
 */
#ifndef __IEnumString_INTERFACE_DEFINED__
#define __IEnumString_INTERFACE_DEFINED__

typedef IEnumString *LPENUMSTRING;

DEFINE_GUID(IID_IEnumString, 0x00000101, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000101-0000-0000-c000-000000000046")
IEnumString : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        LPOLESTR *rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumString **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumString, 0x00000101, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumStringVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumString* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumString* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumString* This);

    /*** IEnumString methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumString* This,
        ULONG celt,
        LPOLESTR *rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumString* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumString* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumString* This,
        IEnumString **ppenum);

    END_INTERFACE
} IEnumStringVtbl;
interface IEnumString {
    CONST_VTBL IEnumStringVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumString_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumString_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumString_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumString methods ***/
#define IEnumString_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumString_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumString_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumString_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumString_QueryInterface(IEnumString* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumString_AddRef(IEnumString* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumString_Release(IEnumString* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumString methods ***/
static FORCEINLINE HRESULT IEnumString_Next(IEnumString* This,ULONG celt,LPOLESTR *rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static FORCEINLINE HRESULT IEnumString_Skip(IEnumString* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumString_Reset(IEnumString* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumString_Clone(IEnumString* This,IEnumString **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumString_RemoteNext_Proxy(
    IEnumString* This,
    ULONG celt,
    LPOLESTR *rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumString_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumString_Skip_Proxy(
    IEnumString* This,
    ULONG celt);
void __RPC_STUB IEnumString_Skip_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumString_Reset_Proxy(
    IEnumString* This);
void __RPC_STUB IEnumString_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumString_Clone_Proxy(
    IEnumString* This,
    IEnumString **ppenum);
void __RPC_STUB IEnumString_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumString_Next_Proxy(
    IEnumString* This,
    ULONG celt,
    LPOLESTR *rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumString_Next_Stub(
    IEnumString* This,
    ULONG celt,
    LPOLESTR *rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumString_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISequentialStream interface
 */
#ifndef __ISequentialStream_INTERFACE_DEFINED__
#define __ISequentialStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISequentialStream, 0x0c733a30, 0x2a1c, 0x11ce, 0xad,0xe5, 0x00,0xaa,0x00,0x44,0x77,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0c733a30-2a1c-11ce-ade5-00aa0044773d")
ISequentialStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Read(
        void *pv,
        ULONG cb,
        ULONG *pcbRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write(
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISequentialStream, 0x0c733a30, 0x2a1c, 0x11ce, 0xad,0xe5, 0x00,0xaa,0x00,0x44,0x77,0x3d)
#endif
#else
typedef struct ISequentialStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISequentialStream* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISequentialStream* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISequentialStream* This);

    /*** ISequentialStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        ISequentialStream* This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        ISequentialStream* This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    END_INTERFACE
} ISequentialStreamVtbl;
interface ISequentialStream {
    CONST_VTBL ISequentialStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISequentialStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISequentialStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISequentialStream_Release(This) (This)->lpVtbl->Release(This)
/*** ISequentialStream methods ***/
#define ISequentialStream_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define ISequentialStream_Write(This,pv,cb,pcbWritten) (This)->lpVtbl->Write(This,pv,cb,pcbWritten)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISequentialStream_QueryInterface(ISequentialStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISequentialStream_AddRef(ISequentialStream* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISequentialStream_Release(ISequentialStream* This) {
    return This->lpVtbl->Release(This);
}
/*** ISequentialStream methods ***/
static FORCEINLINE HRESULT ISequentialStream_Read(ISequentialStream* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static FORCEINLINE HRESULT ISequentialStream_Write(ISequentialStream* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pv,cb,pcbWritten);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISequentialStream_RemoteRead_Proxy(
    ISequentialStream* This,
    byte *pv,
    ULONG cb,
    ULONG *pcbRead);
void __RPC_STUB ISequentialStream_RemoteRead_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISequentialStream_RemoteWrite_Proxy(
    ISequentialStream* This,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);
void __RPC_STUB ISequentialStream_RemoteWrite_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK ISequentialStream_Read_Proxy(
    ISequentialStream* This,
    void *pv,
    ULONG cb,
    ULONG *pcbRead);
HRESULT __RPC_STUB ISequentialStream_Read_Stub(
    ISequentialStream* This,
    byte *pv,
    ULONG cb,
    ULONG *pcbRead);
HRESULT CALLBACK ISequentialStream_Write_Proxy(
    ISequentialStream* This,
    const void *pv,
    ULONG cb,
    ULONG *pcbWritten);
HRESULT __RPC_STUB ISequentialStream_Write_Stub(
    ISequentialStream* This,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);

#endif  /* __ISequentialStream_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IStream interface
 */
#ifndef __IStream_INTERFACE_DEFINED__
#define __IStream_INTERFACE_DEFINED__

typedef IStream *LPSTREAM;

typedef struct tagSTATSTG {
    LPOLESTR pwcsName;
    DWORD type;
    ULARGE_INTEGER cbSize;
    FILETIME mtime;
    FILETIME ctime;
    FILETIME atime;
    DWORD grfMode;
    DWORD grfLocksSupported;
    CLSID clsid;
    DWORD grfStateBits;
    DWORD reserved;
} STATSTG;

typedef enum tagSTGTY {
    STGTY_STORAGE = 1,
    STGTY_STREAM = 2,
    STGTY_LOCKBYTES = 3,
    STGTY_PROPERTY = 4
} STGTY;

typedef enum tagSTREAM_SEEK {
    STREAM_SEEK_SET = 0,
    STREAM_SEEK_CUR = 1,
    STREAM_SEEK_END = 2
} STREAM_SEEK;

typedef enum tagLOCKTYPE {
    LOCK_WRITE = 1,
    LOCK_EXCLUSIVE = 2,
    LOCK_ONLYONCE = 4
} LOCKTYPE;

DEFINE_GUID(IID_IStream, 0x0000000c, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000000c-0000-0000-c000-000000000046")
IStream : public ISequentialStream
{
    virtual HRESULT STDMETHODCALLTYPE Seek(
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSize(
        ULARGE_INTEGER libNewSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyTo(
        IStream *pstm,
        ULARGE_INTEGER cb,
        ULARGE_INTEGER *pcbRead,
        ULARGE_INTEGER *pcbWritten) = 0;

    virtual HRESULT STDMETHODCALLTYPE Commit(
        DWORD grfCommitFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Revert(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockRegion(
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockRegion(
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stat(
        STATSTG *pstatstg,
        DWORD grfStatFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IStream **ppstm) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IStream, 0x0000000c, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IStream* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IStream* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IStream* This);

    /*** ISequentialStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IStream* This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IStream* This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    /*** IStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Seek)(
        IStream* This,
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition);

    HRESULT (STDMETHODCALLTYPE *SetSize)(
        IStream* This,
        ULARGE_INTEGER libNewSize);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        IStream* This,
        IStream *pstm,
        ULARGE_INTEGER cb,
        ULARGE_INTEGER *pcbRead,
        ULARGE_INTEGER *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IStream* This,
        DWORD grfCommitFlags);

    HRESULT (STDMETHODCALLTYPE *Revert)(
        IStream* This);

    HRESULT (STDMETHODCALLTYPE *LockRegion)(
        IStream* This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *UnlockRegion)(
        IStream* This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *Stat)(
        IStream* This,
        STATSTG *pstatstg,
        DWORD grfStatFlag);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IStream* This,
        IStream **ppstm);

    END_INTERFACE
} IStreamVtbl;
interface IStream {
    CONST_VTBL IStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IStream_Release(This) (This)->lpVtbl->Release(This)
/*** ISequentialStream methods ***/
#define IStream_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define IStream_Write(This,pv,cb,pcbWritten) (This)->lpVtbl->Write(This,pv,cb,pcbWritten)
/*** IStream methods ***/
#define IStream_Seek(This,dlibMove,dwOrigin,plibNewPosition) (This)->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition)
#define IStream_SetSize(This,libNewSize) (This)->lpVtbl->SetSize(This,libNewSize)
#define IStream_CopyTo(This,pstm,cb,pcbRead,pcbWritten) (This)->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten)
#define IStream_Commit(This,grfCommitFlags) (This)->lpVtbl->Commit(This,grfCommitFlags)
#define IStream_Revert(This) (This)->lpVtbl->Revert(This)
#define IStream_LockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->LockRegion(This,libOffset,cb,dwLockType)
#define IStream_UnlockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType)
#define IStream_Stat(This,pstatstg,grfStatFlag) (This)->lpVtbl->Stat(This,pstatstg,grfStatFlag)
#define IStream_Clone(This,ppstm) (This)->lpVtbl->Clone(This,ppstm)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IStream_QueryInterface(IStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IStream_AddRef(IStream* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IStream_Release(IStream* This) {
    return This->lpVtbl->Release(This);
}
/*** ISequentialStream methods ***/
static FORCEINLINE HRESULT IStream_Read(IStream* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static FORCEINLINE HRESULT IStream_Write(IStream* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pv,cb,pcbWritten);
}
/*** IStream methods ***/
static FORCEINLINE HRESULT IStream_Seek(IStream* This,LARGE_INTEGER dlibMove,DWORD dwOrigin,ULARGE_INTEGER *plibNewPosition) {
    return This->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition);
}
static FORCEINLINE HRESULT IStream_SetSize(IStream* This,ULARGE_INTEGER libNewSize) {
    return This->lpVtbl->SetSize(This,libNewSize);
}
static FORCEINLINE HRESULT IStream_CopyTo(IStream* This,IStream *pstm,ULARGE_INTEGER cb,ULARGE_INTEGER *pcbRead,ULARGE_INTEGER *pcbWritten) {
    return This->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten);
}
static FORCEINLINE HRESULT IStream_Commit(IStream* This,DWORD grfCommitFlags) {
    return This->lpVtbl->Commit(This,grfCommitFlags);
}
static FORCEINLINE HRESULT IStream_Revert(IStream* This) {
    return This->lpVtbl->Revert(This);
}
static FORCEINLINE HRESULT IStream_LockRegion(IStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->LockRegion(This,libOffset,cb,dwLockType);
}
static FORCEINLINE HRESULT IStream_UnlockRegion(IStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType);
}
static FORCEINLINE HRESULT IStream_Stat(IStream* This,STATSTG *pstatstg,DWORD grfStatFlag) {
    return This->lpVtbl->Stat(This,pstatstg,grfStatFlag);
}
static FORCEINLINE HRESULT IStream_Clone(IStream* This,IStream **ppstm) {
    return This->lpVtbl->Clone(This,ppstm);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IStream_RemoteSeek_Proxy(
    IStream* This,
    LARGE_INTEGER dlibMove,
    DWORD dwOrigin,
    ULARGE_INTEGER *plibNewPosition);
void __RPC_STUB IStream_RemoteSeek_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_SetSize_Proxy(
    IStream* This,
    ULARGE_INTEGER libNewSize);
void __RPC_STUB IStream_SetSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_RemoteCopyTo_Proxy(
    IStream* This,
    IStream *pstm,
    ULARGE_INTEGER cb,
    ULARGE_INTEGER *pcbRead,
    ULARGE_INTEGER *pcbWritten);
void __RPC_STUB IStream_RemoteCopyTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_Commit_Proxy(
    IStream* This,
    DWORD grfCommitFlags);
void __RPC_STUB IStream_Commit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_Revert_Proxy(
    IStream* This);
void __RPC_STUB IStream_Revert_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_LockRegion_Proxy(
    IStream* This,
    ULARGE_INTEGER libOffset,
    ULARGE_INTEGER cb,
    DWORD dwLockType);
void __RPC_STUB IStream_LockRegion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_UnlockRegion_Proxy(
    IStream* This,
    ULARGE_INTEGER libOffset,
    ULARGE_INTEGER cb,
    DWORD dwLockType);
void __RPC_STUB IStream_UnlockRegion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_Stat_Proxy(
    IStream* This,
    STATSTG *pstatstg,
    DWORD grfStatFlag);
void __RPC_STUB IStream_Stat_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_Clone_Proxy(
    IStream* This,
    IStream **ppstm);
void __RPC_STUB IStream_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IStream_Seek_Proxy(
    IStream* This,
    LARGE_INTEGER dlibMove,
    DWORD dwOrigin,
    ULARGE_INTEGER *plibNewPosition);
HRESULT __RPC_STUB IStream_Seek_Stub(
    IStream* This,
    LARGE_INTEGER dlibMove,
    DWORD dwOrigin,
    ULARGE_INTEGER *plibNewPosition);
HRESULT CALLBACK IStream_CopyTo_Proxy(
    IStream* This,
    IStream *pstm,
    ULARGE_INTEGER cb,
    ULARGE_INTEGER *pcbRead,
    ULARGE_INTEGER *pcbWritten);
HRESULT __RPC_STUB IStream_CopyTo_Stub(
    IStream* This,
    IStream *pstm,
    ULARGE_INTEGER cb,
    ULARGE_INTEGER *pcbRead,
    ULARGE_INTEGER *pcbWritten);

#endif  /* __IStream_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcChannelBuffer interface
 */
#ifndef __IRpcChannelBuffer_INTERFACE_DEFINED__
#define __IRpcChannelBuffer_INTERFACE_DEFINED__

typedef ULONG RPCOLEDATAREP;

typedef struct tagRPCOLEMESSAGE {
    void *reserved1;
    RPCOLEDATAREP dataRepresentation;
    void *Buffer;
    ULONG cbBuffer;
    ULONG iMethod;
    void * reserved2[5];
    ULONG rpcFlags;
} RPCOLEMESSAGE;

typedef RPCOLEMESSAGE *PRPCOLEMESSAGE;

DEFINE_GUID(IID_IRpcChannelBuffer, 0xd5f56b60, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f56b60-593b-101a-b569-08002b2dbf7a")
IRpcChannelBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetBuffer(
        RPCOLEMESSAGE *pMessage,
        REFIID riid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendReceive(
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeBuffer(
        RPCOLEMESSAGE *pMessage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDestCtx(
        DWORD *pdwDestContext,
        void **ppvDestContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsConnected(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcChannelBuffer, 0xd5f56b60, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a)
#endif
#else
typedef struct IRpcChannelBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcChannelBuffer* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcChannelBuffer* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcChannelBuffer* This);

    /*** IRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMessage,
        REFIID riid);

    HRESULT (STDMETHODCALLTYPE *SendReceive)(
        IRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMessage);

    HRESULT (STDMETHODCALLTYPE *GetDestCtx)(
        IRpcChannelBuffer* This,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IRpcChannelBuffer* This);

    END_INTERFACE
} IRpcChannelBufferVtbl;
interface IRpcChannelBuffer {
    CONST_VTBL IRpcChannelBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcChannelBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcChannelBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcChannelBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcChannelBuffer methods ***/
#define IRpcChannelBuffer_GetBuffer(This,pMessage,riid) (This)->lpVtbl->GetBuffer(This,pMessage,riid)
#define IRpcChannelBuffer_SendReceive(This,pMessage,pStatus) (This)->lpVtbl->SendReceive(This,pMessage,pStatus)
#define IRpcChannelBuffer_FreeBuffer(This,pMessage) (This)->lpVtbl->FreeBuffer(This,pMessage)
#define IRpcChannelBuffer_GetDestCtx(This,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext)
#define IRpcChannelBuffer_IsConnected(This) (This)->lpVtbl->IsConnected(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer_QueryInterface(IRpcChannelBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRpcChannelBuffer_AddRef(IRpcChannelBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRpcChannelBuffer_Release(IRpcChannelBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcChannelBuffer methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer_GetBuffer(IRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage,REFIID riid) {
    return This->lpVtbl->GetBuffer(This,pMessage,riid);
}
static FORCEINLINE HRESULT IRpcChannelBuffer_SendReceive(IRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage,ULONG *pStatus) {
    return This->lpVtbl->SendReceive(This,pMessage,pStatus);
}
static FORCEINLINE HRESULT IRpcChannelBuffer_FreeBuffer(IRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage) {
    return This->lpVtbl->FreeBuffer(This,pMessage);
}
static FORCEINLINE HRESULT IRpcChannelBuffer_GetDestCtx(IRpcChannelBuffer* This,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext);
}
static FORCEINLINE HRESULT IRpcChannelBuffer_IsConnected(IRpcChannelBuffer* This) {
    return This->lpVtbl->IsConnected(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRpcChannelBuffer_GetBuffer_Proxy(
    IRpcChannelBuffer* This,
    RPCOLEMESSAGE *pMessage,
    REFIID riid);
void __RPC_STUB IRpcChannelBuffer_GetBuffer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer_SendReceive_Proxy(
    IRpcChannelBuffer* This,
    RPCOLEMESSAGE *pMessage,
    ULONG *pStatus);
void __RPC_STUB IRpcChannelBuffer_SendReceive_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer_FreeBuffer_Proxy(
    IRpcChannelBuffer* This,
    RPCOLEMESSAGE *pMessage);
void __RPC_STUB IRpcChannelBuffer_FreeBuffer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer_GetDestCtx_Proxy(
    IRpcChannelBuffer* This,
    DWORD *pdwDestContext,
    void **ppvDestContext);
void __RPC_STUB IRpcChannelBuffer_GetDestCtx_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer_IsConnected_Proxy(
    IRpcChannelBuffer* This);
void __RPC_STUB IRpcChannelBuffer_IsConnected_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRpcChannelBuffer_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IRpcChannelBuffer2 interface
 */
#ifndef __IRpcChannelBuffer2_INTERFACE_DEFINED__
#define __IRpcChannelBuffer2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcChannelBuffer2, 0x594f31d0, 0x7f19, 0x11d0, 0xb1,0x94, 0x00,0xa0,0xc9,0x0d,0xc8,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("594f31d0-7f19-11d0-b194-00a0c90dc8bf")
IRpcChannelBuffer2 : public IRpcChannelBuffer
{
    virtual HRESULT STDMETHODCALLTYPE GetProtocolVersion(
        DWORD *pdwVersion) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcChannelBuffer2, 0x594f31d0, 0x7f19, 0x11d0, 0xb1,0x94, 0x00,0xa0,0xc9,0x0d,0xc8,0xbf)
#endif
#else
typedef struct IRpcChannelBuffer2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcChannelBuffer2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcChannelBuffer2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcChannelBuffer2* This);

    /*** IRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IRpcChannelBuffer2* This,
        RPCOLEMESSAGE *pMessage,
        REFIID riid);

    HRESULT (STDMETHODCALLTYPE *SendReceive)(
        IRpcChannelBuffer2* This,
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IRpcChannelBuffer2* This,
        RPCOLEMESSAGE *pMessage);

    HRESULT (STDMETHODCALLTYPE *GetDestCtx)(
        IRpcChannelBuffer2* This,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IRpcChannelBuffer2* This);

    /*** IRpcChannelBuffer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProtocolVersion)(
        IRpcChannelBuffer2* This,
        DWORD *pdwVersion);

    END_INTERFACE
} IRpcChannelBuffer2Vtbl;
interface IRpcChannelBuffer2 {
    CONST_VTBL IRpcChannelBuffer2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcChannelBuffer2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcChannelBuffer2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcChannelBuffer2_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcChannelBuffer methods ***/
#define IRpcChannelBuffer2_GetBuffer(This,pMessage,riid) (This)->lpVtbl->GetBuffer(This,pMessage,riid)
#define IRpcChannelBuffer2_SendReceive(This,pMessage,pStatus) (This)->lpVtbl->SendReceive(This,pMessage,pStatus)
#define IRpcChannelBuffer2_FreeBuffer(This,pMessage) (This)->lpVtbl->FreeBuffer(This,pMessage)
#define IRpcChannelBuffer2_GetDestCtx(This,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext)
#define IRpcChannelBuffer2_IsConnected(This) (This)->lpVtbl->IsConnected(This)
/*** IRpcChannelBuffer2 methods ***/
#define IRpcChannelBuffer2_GetProtocolVersion(This,pdwVersion) (This)->lpVtbl->GetProtocolVersion(This,pdwVersion)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer2_QueryInterface(IRpcChannelBuffer2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRpcChannelBuffer2_AddRef(IRpcChannelBuffer2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRpcChannelBuffer2_Release(IRpcChannelBuffer2* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcChannelBuffer methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer2_GetBuffer(IRpcChannelBuffer2* This,RPCOLEMESSAGE *pMessage,REFIID riid) {
    return This->lpVtbl->GetBuffer(This,pMessage,riid);
}
static FORCEINLINE HRESULT IRpcChannelBuffer2_SendReceive(IRpcChannelBuffer2* This,RPCOLEMESSAGE *pMessage,ULONG *pStatus) {
    return This->lpVtbl->SendReceive(This,pMessage,pStatus);
}
static FORCEINLINE HRESULT IRpcChannelBuffer2_FreeBuffer(IRpcChannelBuffer2* This,RPCOLEMESSAGE *pMessage) {
    return This->lpVtbl->FreeBuffer(This,pMessage);
}
static FORCEINLINE HRESULT IRpcChannelBuffer2_GetDestCtx(IRpcChannelBuffer2* This,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext);
}
static FORCEINLINE HRESULT IRpcChannelBuffer2_IsConnected(IRpcChannelBuffer2* This) {
    return This->lpVtbl->IsConnected(This);
}
/*** IRpcChannelBuffer2 methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer2_GetProtocolVersion(IRpcChannelBuffer2* This,DWORD *pdwVersion) {
    return This->lpVtbl->GetProtocolVersion(This,pdwVersion);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRpcChannelBuffer2_GetProtocolVersion_Proxy(
    IRpcChannelBuffer2* This,
    DWORD *pdwVersion);
void __RPC_STUB IRpcChannelBuffer2_GetProtocolVersion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRpcChannelBuffer2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAsyncRpcChannelBuffer interface
 */
#ifndef __IAsyncRpcChannelBuffer_INTERFACE_DEFINED__
#define __IAsyncRpcChannelBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAsyncRpcChannelBuffer, 0xa5029fb6, 0x3c34, 0x11d1, 0x9c,0x99, 0x00,0xc0,0x4f,0xb9,0x98,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a5029fb6-3c34-11d1-9c99-00c04fb998aa")
IAsyncRpcChannelBuffer : public IRpcChannelBuffer2
{
    virtual HRESULT STDMETHODCALLTYPE Send(
        RPCOLEMESSAGE *pMsg,
        ISynchronize *pSync,
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE Receive(
        RPCOLEMESSAGE *pMsg,
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDestCtxEx(
        RPCOLEMESSAGE *pMsg,
        DWORD *pdwDestContext,
        void **ppvDestContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAsyncRpcChannelBuffer, 0xa5029fb6, 0x3c34, 0x11d1, 0x9c,0x99, 0x00,0xc0,0x4f,0xb9,0x98,0xaa)
#endif
#else
typedef struct IAsyncRpcChannelBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAsyncRpcChannelBuffer* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAsyncRpcChannelBuffer* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAsyncRpcChannelBuffer* This);

    /*** IRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IAsyncRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMessage,
        REFIID riid);

    HRESULT (STDMETHODCALLTYPE *SendReceive)(
        IAsyncRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IAsyncRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMessage);

    HRESULT (STDMETHODCALLTYPE *GetDestCtx)(
        IAsyncRpcChannelBuffer* This,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IAsyncRpcChannelBuffer* This);

    /*** IRpcChannelBuffer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProtocolVersion)(
        IAsyncRpcChannelBuffer* This,
        DWORD *pdwVersion);

    /*** IAsyncRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Send)(
        IAsyncRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMsg,
        ISynchronize *pSync,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *Receive)(
        IAsyncRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMsg,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *GetDestCtxEx)(
        IAsyncRpcChannelBuffer* This,
        RPCOLEMESSAGE *pMsg,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    END_INTERFACE
} IAsyncRpcChannelBufferVtbl;
interface IAsyncRpcChannelBuffer {
    CONST_VTBL IAsyncRpcChannelBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAsyncRpcChannelBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAsyncRpcChannelBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAsyncRpcChannelBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcChannelBuffer methods ***/
#define IAsyncRpcChannelBuffer_GetBuffer(This,pMessage,riid) (This)->lpVtbl->GetBuffer(This,pMessage,riid)
#define IAsyncRpcChannelBuffer_SendReceive(This,pMessage,pStatus) (This)->lpVtbl->SendReceive(This,pMessage,pStatus)
#define IAsyncRpcChannelBuffer_FreeBuffer(This,pMessage) (This)->lpVtbl->FreeBuffer(This,pMessage)
#define IAsyncRpcChannelBuffer_GetDestCtx(This,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext)
#define IAsyncRpcChannelBuffer_IsConnected(This) (This)->lpVtbl->IsConnected(This)
/*** IRpcChannelBuffer2 methods ***/
#define IAsyncRpcChannelBuffer_GetProtocolVersion(This,pdwVersion) (This)->lpVtbl->GetProtocolVersion(This,pdwVersion)
/*** IAsyncRpcChannelBuffer methods ***/
#define IAsyncRpcChannelBuffer_Send(This,pMsg,pSync,pulStatus) (This)->lpVtbl->Send(This,pMsg,pSync,pulStatus)
#define IAsyncRpcChannelBuffer_Receive(This,pMsg,pulStatus) (This)->lpVtbl->Receive(This,pMsg,pulStatus)
#define IAsyncRpcChannelBuffer_GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_QueryInterface(IAsyncRpcChannelBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAsyncRpcChannelBuffer_AddRef(IAsyncRpcChannelBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAsyncRpcChannelBuffer_Release(IAsyncRpcChannelBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcChannelBuffer methods ***/
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_GetBuffer(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage,REFIID riid) {
    return This->lpVtbl->GetBuffer(This,pMessage,riid);
}
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_SendReceive(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage,ULONG *pStatus) {
    return This->lpVtbl->SendReceive(This,pMessage,pStatus);
}
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_FreeBuffer(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage) {
    return This->lpVtbl->FreeBuffer(This,pMessage);
}
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_GetDestCtx(IAsyncRpcChannelBuffer* This,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext);
}
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_IsConnected(IAsyncRpcChannelBuffer* This) {
    return This->lpVtbl->IsConnected(This);
}
/*** IRpcChannelBuffer2 methods ***/
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_GetProtocolVersion(IAsyncRpcChannelBuffer* This,DWORD *pdwVersion) {
    return This->lpVtbl->GetProtocolVersion(This,pdwVersion);
}
/*** IAsyncRpcChannelBuffer methods ***/
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_Send(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMsg,ISynchronize *pSync,ULONG *pulStatus) {
    return This->lpVtbl->Send(This,pMsg,pSync,pulStatus);
}
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_Receive(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMsg,ULONG *pulStatus) {
    return This->lpVtbl->Receive(This,pMsg,pulStatus);
}
static FORCEINLINE HRESULT IAsyncRpcChannelBuffer_GetDestCtxEx(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMsg,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAsyncRpcChannelBuffer_Send_Proxy(
    IAsyncRpcChannelBuffer* This,
    RPCOLEMESSAGE *pMsg,
    ISynchronize *pSync,
    ULONG *pulStatus);
void __RPC_STUB IAsyncRpcChannelBuffer_Send_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAsyncRpcChannelBuffer_Receive_Proxy(
    IAsyncRpcChannelBuffer* This,
    RPCOLEMESSAGE *pMsg,
    ULONG *pulStatus);
void __RPC_STUB IAsyncRpcChannelBuffer_Receive_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAsyncRpcChannelBuffer_GetDestCtxEx_Proxy(
    IAsyncRpcChannelBuffer* This,
    RPCOLEMESSAGE *pMsg,
    DWORD *pdwDestContext,
    void **ppvDestContext);
void __RPC_STUB IAsyncRpcChannelBuffer_GetDestCtxEx_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IAsyncRpcChannelBuffer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcChannelBuffer3 interface
 */
#ifndef __IRpcChannelBuffer3_INTERFACE_DEFINED__
#define __IRpcChannelBuffer3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcChannelBuffer3, 0x25b15600, 0x0115, 0x11d0, 0xbf,0x0d, 0x00,0xaa,0x00,0xb8,0xdf,0xd2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("25b15600-0115-11d0-bf0d-00aa00b8dfd2")
IRpcChannelBuffer3 : public IRpcChannelBuffer2
{
    virtual HRESULT STDMETHODCALLTYPE Send(
        RPCOLEMESSAGE *pMsg,
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE Receive(
        RPCOLEMESSAGE *pMsg,
        ULONG ulSize,
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        RPCOLEMESSAGE *pMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCallContext(
        RPCOLEMESSAGE *pMsg,
        REFIID riid,
        void **pInterface) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDestCtxEx(
        RPCOLEMESSAGE *pMsg,
        DWORD *pdwDestContext,
        void **ppvDestContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        RPCOLEMESSAGE *pMsg,
        DWORD *pState) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterAsync(
        RPCOLEMESSAGE *pMsg,
        IAsyncManager *pAsyncMgr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcChannelBuffer3, 0x25b15600, 0x0115, 0x11d0, 0xbf,0x0d, 0x00,0xaa,0x00,0xb8,0xdf,0xd2)
#endif
#else
typedef struct IRpcChannelBuffer3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcChannelBuffer3* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcChannelBuffer3* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcChannelBuffer3* This);

    /*** IRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMessage,
        REFIID riid);

    HRESULT (STDMETHODCALLTYPE *SendReceive)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMessage);

    HRESULT (STDMETHODCALLTYPE *GetDestCtx)(
        IRpcChannelBuffer3* This,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IRpcChannelBuffer3* This);

    /*** IRpcChannelBuffer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProtocolVersion)(
        IRpcChannelBuffer3* This,
        DWORD *pdwVersion);

    /*** IRpcChannelBuffer3 methods ***/
    HRESULT (STDMETHODCALLTYPE *Send)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMsg,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *Receive)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMsg,
        ULONG ulSize,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMsg);

    HRESULT (STDMETHODCALLTYPE *GetCallContext)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMsg,
        REFIID riid,
        void **pInterface);

    HRESULT (STDMETHODCALLTYPE *GetDestCtxEx)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMsg,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMsg,
        DWORD *pState);

    HRESULT (STDMETHODCALLTYPE *RegisterAsync)(
        IRpcChannelBuffer3* This,
        RPCOLEMESSAGE *pMsg,
        IAsyncManager *pAsyncMgr);

    END_INTERFACE
} IRpcChannelBuffer3Vtbl;
interface IRpcChannelBuffer3 {
    CONST_VTBL IRpcChannelBuffer3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcChannelBuffer3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcChannelBuffer3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcChannelBuffer3_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcChannelBuffer methods ***/
#define IRpcChannelBuffer3_GetBuffer(This,pMessage,riid) (This)->lpVtbl->GetBuffer(This,pMessage,riid)
#define IRpcChannelBuffer3_SendReceive(This,pMessage,pStatus) (This)->lpVtbl->SendReceive(This,pMessage,pStatus)
#define IRpcChannelBuffer3_FreeBuffer(This,pMessage) (This)->lpVtbl->FreeBuffer(This,pMessage)
#define IRpcChannelBuffer3_GetDestCtx(This,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext)
#define IRpcChannelBuffer3_IsConnected(This) (This)->lpVtbl->IsConnected(This)
/*** IRpcChannelBuffer2 methods ***/
#define IRpcChannelBuffer3_GetProtocolVersion(This,pdwVersion) (This)->lpVtbl->GetProtocolVersion(This,pdwVersion)
/*** IRpcChannelBuffer3 methods ***/
#define IRpcChannelBuffer3_Send(This,pMsg,pulStatus) (This)->lpVtbl->Send(This,pMsg,pulStatus)
#define IRpcChannelBuffer3_Receive(This,pMsg,ulSize,pulStatus) (This)->lpVtbl->Receive(This,pMsg,ulSize,pulStatus)
#define IRpcChannelBuffer3_Cancel(This,pMsg) (This)->lpVtbl->Cancel(This,pMsg)
#define IRpcChannelBuffer3_GetCallContext(This,pMsg,riid,pInterface) (This)->lpVtbl->GetCallContext(This,pMsg,riid,pInterface)
#define IRpcChannelBuffer3_GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext)
#define IRpcChannelBuffer3_GetState(This,pMsg,pState) (This)->lpVtbl->GetState(This,pMsg,pState)
#define IRpcChannelBuffer3_RegisterAsync(This,pMsg,pAsyncMgr) (This)->lpVtbl->RegisterAsync(This,pMsg,pAsyncMgr)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer3_QueryInterface(IRpcChannelBuffer3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRpcChannelBuffer3_AddRef(IRpcChannelBuffer3* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRpcChannelBuffer3_Release(IRpcChannelBuffer3* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcChannelBuffer methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer3_GetBuffer(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMessage,REFIID riid) {
    return This->lpVtbl->GetBuffer(This,pMessage,riid);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_SendReceive(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMessage,ULONG *pStatus) {
    return This->lpVtbl->SendReceive(This,pMessage,pStatus);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_FreeBuffer(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMessage) {
    return This->lpVtbl->FreeBuffer(This,pMessage);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_GetDestCtx(IRpcChannelBuffer3* This,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_IsConnected(IRpcChannelBuffer3* This) {
    return This->lpVtbl->IsConnected(This);
}
/*** IRpcChannelBuffer2 methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer3_GetProtocolVersion(IRpcChannelBuffer3* This,DWORD *pdwVersion) {
    return This->lpVtbl->GetProtocolVersion(This,pdwVersion);
}
/*** IRpcChannelBuffer3 methods ***/
static FORCEINLINE HRESULT IRpcChannelBuffer3_Send(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,ULONG *pulStatus) {
    return This->lpVtbl->Send(This,pMsg,pulStatus);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_Receive(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,ULONG ulSize,ULONG *pulStatus) {
    return This->lpVtbl->Receive(This,pMsg,ulSize,pulStatus);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_Cancel(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg) {
    return This->lpVtbl->Cancel(This,pMsg);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_GetCallContext(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,REFIID riid,void **pInterface) {
    return This->lpVtbl->GetCallContext(This,pMsg,riid,pInterface);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_GetDestCtxEx(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_GetState(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,DWORD *pState) {
    return This->lpVtbl->GetState(This,pMsg,pState);
}
static FORCEINLINE HRESULT IRpcChannelBuffer3_RegisterAsync(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,IAsyncManager *pAsyncMgr) {
    return This->lpVtbl->RegisterAsync(This,pMsg,pAsyncMgr);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRpcChannelBuffer3_Send_Proxy(
    IRpcChannelBuffer3* This,
    RPCOLEMESSAGE *pMsg,
    ULONG *pulStatus);
void __RPC_STUB IRpcChannelBuffer3_Send_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer3_Receive_Proxy(
    IRpcChannelBuffer3* This,
    RPCOLEMESSAGE *pMsg,
    ULONG ulSize,
    ULONG *pulStatus);
void __RPC_STUB IRpcChannelBuffer3_Receive_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer3_Cancel_Proxy(
    IRpcChannelBuffer3* This,
    RPCOLEMESSAGE *pMsg);
void __RPC_STUB IRpcChannelBuffer3_Cancel_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer3_GetCallContext_Proxy(
    IRpcChannelBuffer3* This,
    RPCOLEMESSAGE *pMsg,
    REFIID riid,
    void **pInterface);
void __RPC_STUB IRpcChannelBuffer3_GetCallContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer3_GetDestCtxEx_Proxy(
    IRpcChannelBuffer3* This,
    RPCOLEMESSAGE *pMsg,
    DWORD *pdwDestContext,
    void **ppvDestContext);
void __RPC_STUB IRpcChannelBuffer3_GetDestCtxEx_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer3_GetState_Proxy(
    IRpcChannelBuffer3* This,
    RPCOLEMESSAGE *pMsg,
    DWORD *pState);
void __RPC_STUB IRpcChannelBuffer3_GetState_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcChannelBuffer3_RegisterAsync_Proxy(
    IRpcChannelBuffer3* This,
    RPCOLEMESSAGE *pMsg,
    IAsyncManager *pAsyncMgr);
void __RPC_STUB IRpcChannelBuffer3_RegisterAsync_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRpcChannelBuffer3_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcSyntaxNegotiate interface
 */
#ifndef __IRpcSyntaxNegotiate_INTERFACE_DEFINED__
#define __IRpcSyntaxNegotiate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcSyntaxNegotiate, 0x58a08519, 0x24c8, 0x4935, 0xb4,0x82, 0x3f,0xd8,0x23,0x33,0x3a,0x4f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("58a08519-24c8-4935-b482-3fd823333a4f")
IRpcSyntaxNegotiate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE NegotiateSyntax(
        RPCOLEMESSAGE *pMsg) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcSyntaxNegotiate, 0x58a08519, 0x24c8, 0x4935, 0xb4,0x82, 0x3f,0xd8,0x23,0x33,0x3a,0x4f)
#endif
#else
typedef struct IRpcSyntaxNegotiateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcSyntaxNegotiate* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcSyntaxNegotiate* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcSyntaxNegotiate* This);

    /*** IRpcSyntaxNegotiate methods ***/
    HRESULT (STDMETHODCALLTYPE *NegotiateSyntax)(
        IRpcSyntaxNegotiate* This,
        RPCOLEMESSAGE *pMsg);

    END_INTERFACE
} IRpcSyntaxNegotiateVtbl;
interface IRpcSyntaxNegotiate {
    CONST_VTBL IRpcSyntaxNegotiateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcSyntaxNegotiate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcSyntaxNegotiate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcSyntaxNegotiate_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcSyntaxNegotiate methods ***/
#define IRpcSyntaxNegotiate_NegotiateSyntax(This,pMsg) (This)->lpVtbl->NegotiateSyntax(This,pMsg)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRpcSyntaxNegotiate_QueryInterface(IRpcSyntaxNegotiate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRpcSyntaxNegotiate_AddRef(IRpcSyntaxNegotiate* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRpcSyntaxNegotiate_Release(IRpcSyntaxNegotiate* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcSyntaxNegotiate methods ***/
static FORCEINLINE HRESULT IRpcSyntaxNegotiate_NegotiateSyntax(IRpcSyntaxNegotiate* This,RPCOLEMESSAGE *pMsg) {
    return This->lpVtbl->NegotiateSyntax(This,pMsg);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRpcSyntaxNegotiate_NegotiateSyntax_Proxy(
    IRpcSyntaxNegotiate* This,
    RPCOLEMESSAGE *pMsg);
void __RPC_STUB IRpcSyntaxNegotiate_NegotiateSyntax_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRpcSyntaxNegotiate_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcProxyBuffer interface
 */
#ifndef __IRpcProxyBuffer_INTERFACE_DEFINED__
#define __IRpcProxyBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcProxyBuffer, 0xd5f56a34, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f56a34-593b-101a-b569-08002b2dbf7a")
IRpcProxyBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Connect(
        IRpcChannelBuffer *pRpcChannelBuffer) = 0;

    virtual void STDMETHODCALLTYPE Disconnect(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcProxyBuffer, 0xd5f56a34, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a)
#endif
#else
typedef struct IRpcProxyBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcProxyBuffer* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcProxyBuffer* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcProxyBuffer* This);

    /*** IRpcProxyBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Connect)(
        IRpcProxyBuffer* This,
        IRpcChannelBuffer *pRpcChannelBuffer);

    void (STDMETHODCALLTYPE *Disconnect)(
        IRpcProxyBuffer* This);

    END_INTERFACE
} IRpcProxyBufferVtbl;
interface IRpcProxyBuffer {
    CONST_VTBL IRpcProxyBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcProxyBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcProxyBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcProxyBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcProxyBuffer methods ***/
#define IRpcProxyBuffer_Connect(This,pRpcChannelBuffer) (This)->lpVtbl->Connect(This,pRpcChannelBuffer)
#define IRpcProxyBuffer_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRpcProxyBuffer_QueryInterface(IRpcProxyBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRpcProxyBuffer_AddRef(IRpcProxyBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRpcProxyBuffer_Release(IRpcProxyBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcProxyBuffer methods ***/
static FORCEINLINE HRESULT IRpcProxyBuffer_Connect(IRpcProxyBuffer* This,IRpcChannelBuffer *pRpcChannelBuffer) {
    return This->lpVtbl->Connect(This,pRpcChannelBuffer);
}
static FORCEINLINE void IRpcProxyBuffer_Disconnect(IRpcProxyBuffer* This) {
    This->lpVtbl->Disconnect(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRpcProxyBuffer_Connect_Proxy(
    IRpcProxyBuffer* This,
    IRpcChannelBuffer *pRpcChannelBuffer);
void __RPC_STUB IRpcProxyBuffer_Connect_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IRpcProxyBuffer_Disconnect_Proxy(
    IRpcProxyBuffer* This);
void __RPC_STUB IRpcProxyBuffer_Disconnect_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRpcProxyBuffer_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IRpcStubBuffer interface
 */
#ifndef __IRpcStubBuffer_INTERFACE_DEFINED__
#define __IRpcStubBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcStubBuffer, 0xd5f56afc, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f56afc-593b-101a-b569-08002b2dbf7a")
IRpcStubBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Connect(
        IUnknown *pUnkServer) = 0;

    virtual void STDMETHODCALLTYPE Disconnect(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Invoke(
        RPCOLEMESSAGE *_prpcmsg,
        IRpcChannelBuffer *_pRpcChannelBuffer) = 0;

    virtual IRpcStubBuffer * STDMETHODCALLTYPE IsIIDSupported(
        REFIID riid) = 0;

    virtual ULONG STDMETHODCALLTYPE CountRefs(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DebugServerQueryInterface(
        void **ppv) = 0;

    virtual void STDMETHODCALLTYPE DebugServerRelease(
        void *pv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcStubBuffer, 0xd5f56afc, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a)
#endif
#else
typedef struct IRpcStubBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcStubBuffer* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcStubBuffer* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcStubBuffer* This);

    /*** IRpcStubBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Connect)(
        IRpcStubBuffer* This,
        IUnknown *pUnkServer);

    void (STDMETHODCALLTYPE *Disconnect)(
        IRpcStubBuffer* This);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRpcStubBuffer* This,
        RPCOLEMESSAGE *_prpcmsg,
        IRpcChannelBuffer *_pRpcChannelBuffer);

    IRpcStubBuffer * (STDMETHODCALLTYPE *IsIIDSupported)(
        IRpcStubBuffer* This,
        REFIID riid);

    ULONG (STDMETHODCALLTYPE *CountRefs)(
        IRpcStubBuffer* This);

    HRESULT (STDMETHODCALLTYPE *DebugServerQueryInterface)(
        IRpcStubBuffer* This,
        void **ppv);

    void (STDMETHODCALLTYPE *DebugServerRelease)(
        IRpcStubBuffer* This,
        void *pv);

    END_INTERFACE
} IRpcStubBufferVtbl;
interface IRpcStubBuffer {
    CONST_VTBL IRpcStubBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcStubBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcStubBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcStubBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcStubBuffer methods ***/
#define IRpcStubBuffer_Connect(This,pUnkServer) (This)->lpVtbl->Connect(This,pUnkServer)
#define IRpcStubBuffer_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#define IRpcStubBuffer_Invoke(This,_prpcmsg,_pRpcChannelBuffer) (This)->lpVtbl->Invoke(This,_prpcmsg,_pRpcChannelBuffer)
#define IRpcStubBuffer_IsIIDSupported(This,riid) (This)->lpVtbl->IsIIDSupported(This,riid)
#define IRpcStubBuffer_CountRefs(This) (This)->lpVtbl->CountRefs(This)
#define IRpcStubBuffer_DebugServerQueryInterface(This,ppv) (This)->lpVtbl->DebugServerQueryInterface(This,ppv)
#define IRpcStubBuffer_DebugServerRelease(This,pv) (This)->lpVtbl->DebugServerRelease(This,pv)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRpcStubBuffer_QueryInterface(IRpcStubBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRpcStubBuffer_AddRef(IRpcStubBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRpcStubBuffer_Release(IRpcStubBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcStubBuffer methods ***/
static FORCEINLINE HRESULT IRpcStubBuffer_Connect(IRpcStubBuffer* This,IUnknown *pUnkServer) {
    return This->lpVtbl->Connect(This,pUnkServer);
}
static FORCEINLINE void IRpcStubBuffer_Disconnect(IRpcStubBuffer* This) {
    This->lpVtbl->Disconnect(This);
}
static FORCEINLINE HRESULT IRpcStubBuffer_Invoke(IRpcStubBuffer* This,RPCOLEMESSAGE *_prpcmsg,IRpcChannelBuffer *_pRpcChannelBuffer) {
    return This->lpVtbl->Invoke(This,_prpcmsg,_pRpcChannelBuffer);
}
static FORCEINLINE IRpcStubBuffer * IRpcStubBuffer_IsIIDSupported(IRpcStubBuffer* This,REFIID riid) {
    return This->lpVtbl->IsIIDSupported(This,riid);
}
static FORCEINLINE ULONG IRpcStubBuffer_CountRefs(IRpcStubBuffer* This) {
    return This->lpVtbl->CountRefs(This);
}
static FORCEINLINE HRESULT IRpcStubBuffer_DebugServerQueryInterface(IRpcStubBuffer* This,void **ppv) {
    return This->lpVtbl->DebugServerQueryInterface(This,ppv);
}
static FORCEINLINE void IRpcStubBuffer_DebugServerRelease(IRpcStubBuffer* This,void *pv) {
    This->lpVtbl->DebugServerRelease(This,pv);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRpcStubBuffer_Connect_Proxy(
    IRpcStubBuffer* This,
    IUnknown *pUnkServer);
void __RPC_STUB IRpcStubBuffer_Connect_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IRpcStubBuffer_Disconnect_Proxy(
    IRpcStubBuffer* This);
void __RPC_STUB IRpcStubBuffer_Disconnect_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcStubBuffer_Invoke_Proxy(
    IRpcStubBuffer* This,
    RPCOLEMESSAGE *_prpcmsg,
    IRpcChannelBuffer *_pRpcChannelBuffer);
void __RPC_STUB IRpcStubBuffer_Invoke_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
IRpcStubBuffer * STDMETHODCALLTYPE IRpcStubBuffer_IsIIDSupported_Proxy(
    IRpcStubBuffer* This,
    REFIID riid);
void __RPC_STUB IRpcStubBuffer_IsIIDSupported_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
ULONG STDMETHODCALLTYPE IRpcStubBuffer_CountRefs_Proxy(
    IRpcStubBuffer* This);
void __RPC_STUB IRpcStubBuffer_CountRefs_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcStubBuffer_DebugServerQueryInterface_Proxy(
    IRpcStubBuffer* This,
    void **ppv);
void __RPC_STUB IRpcStubBuffer_DebugServerQueryInterface_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IRpcStubBuffer_DebugServerRelease_Proxy(
    IRpcStubBuffer* This,
    void *pv);
void __RPC_STUB IRpcStubBuffer_DebugServerRelease_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRpcStubBuffer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPSFactoryBuffer interface
 */
#ifndef __IPSFactoryBuffer_INTERFACE_DEFINED__
#define __IPSFactoryBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPSFactoryBuffer, 0xd5f569d0, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f569d0-593b-101a-b569-08002b2dbf7a")
IPSFactoryBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateProxy(
        IUnknown *pUnkOuter,
        REFIID riid,
        IRpcProxyBuffer **ppProxy,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStub(
        REFIID riid,
        IUnknown *pUnkServer,
        IRpcStubBuffer **ppStub) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPSFactoryBuffer, 0xd5f569d0, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a)
#endif
#else
typedef struct IPSFactoryBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPSFactoryBuffer* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPSFactoryBuffer* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPSFactoryBuffer* This);

    /*** IPSFactoryBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateProxy)(
        IPSFactoryBuffer* This,
        IUnknown *pUnkOuter,
        REFIID riid,
        IRpcProxyBuffer **ppProxy,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *CreateStub)(
        IPSFactoryBuffer* This,
        REFIID riid,
        IUnknown *pUnkServer,
        IRpcStubBuffer **ppStub);

    END_INTERFACE
} IPSFactoryBufferVtbl;
interface IPSFactoryBuffer {
    CONST_VTBL IPSFactoryBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPSFactoryBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPSFactoryBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPSFactoryBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IPSFactoryBuffer methods ***/
#define IPSFactoryBuffer_CreateProxy(This,pUnkOuter,riid,ppProxy,ppv) (This)->lpVtbl->CreateProxy(This,pUnkOuter,riid,ppProxy,ppv)
#define IPSFactoryBuffer_CreateStub(This,riid,pUnkServer,ppStub) (This)->lpVtbl->CreateStub(This,riid,pUnkServer,ppStub)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPSFactoryBuffer_QueryInterface(IPSFactoryBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPSFactoryBuffer_AddRef(IPSFactoryBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPSFactoryBuffer_Release(IPSFactoryBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IPSFactoryBuffer methods ***/
static FORCEINLINE HRESULT IPSFactoryBuffer_CreateProxy(IPSFactoryBuffer* This,IUnknown *pUnkOuter,REFIID riid,IRpcProxyBuffer **ppProxy,void **ppv) {
    return This->lpVtbl->CreateProxy(This,pUnkOuter,riid,ppProxy,ppv);
}
static FORCEINLINE HRESULT IPSFactoryBuffer_CreateStub(IPSFactoryBuffer* This,REFIID riid,IUnknown *pUnkServer,IRpcStubBuffer **ppStub) {
    return This->lpVtbl->CreateStub(This,riid,pUnkServer,ppStub);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPSFactoryBuffer_CreateProxy_Proxy(
    IPSFactoryBuffer* This,
    IUnknown *pUnkOuter,
    REFIID riid,
    IRpcProxyBuffer **ppProxy,
    void **ppv);
void __RPC_STUB IPSFactoryBuffer_CreateProxy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPSFactoryBuffer_CreateStub_Proxy(
    IPSFactoryBuffer* This,
    REFIID riid,
    IUnknown *pUnkServer,
    IRpcStubBuffer **ppStub);
void __RPC_STUB IPSFactoryBuffer_CreateStub_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPSFactoryBuffer_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#if  (_WIN32_WINNT >= 0x0400 ) || defined(_WIN32_DCOM)
typedef struct SChannelHookCallInfo {
    IID iid;
    DWORD cbSize;
    GUID uCausality;
    DWORD dwServerPid;
    DWORD iMethod;
    void *pObject;
} SChannelHookCallInfo;

/*****************************************************************************
 * IChannelHook interface
 */
#ifndef __IChannelHook_INTERFACE_DEFINED__
#define __IChannelHook_INTERFACE_DEFINED__

DEFINE_GUID(IID_IChannelHook, 0x1008c4a0, 0x7613, 0x11cf, 0x9a,0xf1, 0x00,0x20,0xaf,0x6e,0x72,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1008c4a0-7613-11cf-9af1-0020af6e72f4")
IChannelHook : public IUnknown
{
    virtual void STDMETHODCALLTYPE ClientGetSize(
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize) = 0;

    virtual void STDMETHODCALLTYPE ClientFillBuffer(
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize,
        void *pDataBuffer) = 0;

    virtual void STDMETHODCALLTYPE ClientNotify(
        REFGUID uExtent,
        REFIID riid,
        ULONG cbDataSize,
        void *pDataBuffer,
        DWORD lDataRep,
        HRESULT hrFault) = 0;

    virtual void STDMETHODCALLTYPE ServerNotify(
        REFGUID uExtent,
        REFIID riid,
        ULONG cbDataSize,
        void *pDataBuffer,
        DWORD lDataRep) = 0;

    virtual void STDMETHODCALLTYPE ServerGetSize(
        REFGUID uExtent,
        REFIID riid,
        HRESULT hrFault,
        ULONG *pDataSize) = 0;

    virtual void STDMETHODCALLTYPE ServerFillBuffer(
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize,
        void *pDataBuffer,
        HRESULT hrFault) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IChannelHook, 0x1008c4a0, 0x7613, 0x11cf, 0x9a,0xf1, 0x00,0x20,0xaf,0x6e,0x72,0xf4)
#endif
#else
typedef struct IChannelHookVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IChannelHook* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IChannelHook* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IChannelHook* This);

    /*** IChannelHook methods ***/
    void (STDMETHODCALLTYPE *ClientGetSize)(
        IChannelHook* This,
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize);

    void (STDMETHODCALLTYPE *ClientFillBuffer)(
        IChannelHook* This,
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize,
        void *pDataBuffer);

    void (STDMETHODCALLTYPE *ClientNotify)(
        IChannelHook* This,
        REFGUID uExtent,
        REFIID riid,
        ULONG cbDataSize,
        void *pDataBuffer,
        DWORD lDataRep,
        HRESULT hrFault);

    void (STDMETHODCALLTYPE *ServerNotify)(
        IChannelHook* This,
        REFGUID uExtent,
        REFIID riid,
        ULONG cbDataSize,
        void *pDataBuffer,
        DWORD lDataRep);

    void (STDMETHODCALLTYPE *ServerGetSize)(
        IChannelHook* This,
        REFGUID uExtent,
        REFIID riid,
        HRESULT hrFault,
        ULONG *pDataSize);

    void (STDMETHODCALLTYPE *ServerFillBuffer)(
        IChannelHook* This,
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize,
        void *pDataBuffer,
        HRESULT hrFault);

    END_INTERFACE
} IChannelHookVtbl;
interface IChannelHook {
    CONST_VTBL IChannelHookVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IChannelHook_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IChannelHook_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IChannelHook_Release(This) (This)->lpVtbl->Release(This)
/*** IChannelHook methods ***/
#define IChannelHook_ClientGetSize(This,uExtent,riid,pDataSize) (This)->lpVtbl->ClientGetSize(This,uExtent,riid,pDataSize)
#define IChannelHook_ClientFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer) (This)->lpVtbl->ClientFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer)
#define IChannelHook_ClientNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep,hrFault) (This)->lpVtbl->ClientNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep,hrFault)
#define IChannelHook_ServerNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep) (This)->lpVtbl->ServerNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep)
#define IChannelHook_ServerGetSize(This,uExtent,riid,hrFault,pDataSize) (This)->lpVtbl->ServerGetSize(This,uExtent,riid,hrFault,pDataSize)
#define IChannelHook_ServerFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer,hrFault) (This)->lpVtbl->ServerFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer,hrFault)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IChannelHook_QueryInterface(IChannelHook* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IChannelHook_AddRef(IChannelHook* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IChannelHook_Release(IChannelHook* This) {
    return This->lpVtbl->Release(This);
}
/*** IChannelHook methods ***/
static FORCEINLINE void IChannelHook_ClientGetSize(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG *pDataSize) {
    This->lpVtbl->ClientGetSize(This,uExtent,riid,pDataSize);
}
static FORCEINLINE void IChannelHook_ClientFillBuffer(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG *pDataSize,void *pDataBuffer) {
    This->lpVtbl->ClientFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer);
}
static FORCEINLINE void IChannelHook_ClientNotify(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG cbDataSize,void *pDataBuffer,DWORD lDataRep,HRESULT hrFault) {
    This->lpVtbl->ClientNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep,hrFault);
}
static FORCEINLINE void IChannelHook_ServerNotify(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG cbDataSize,void *pDataBuffer,DWORD lDataRep) {
    This->lpVtbl->ServerNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep);
}
static FORCEINLINE void IChannelHook_ServerGetSize(IChannelHook* This,REFGUID uExtent,REFIID riid,HRESULT hrFault,ULONG *pDataSize) {
    This->lpVtbl->ServerGetSize(This,uExtent,riid,hrFault,pDataSize);
}
static FORCEINLINE void IChannelHook_ServerFillBuffer(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG *pDataSize,void *pDataBuffer,HRESULT hrFault) {
    This->lpVtbl->ServerFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer,hrFault);
}
#endif
#endif

#endif

void STDMETHODCALLTYPE IChannelHook_ClientGetSize_Proxy(
    IChannelHook* This,
    REFGUID uExtent,
    REFIID riid,
    ULONG *pDataSize);
void __RPC_STUB IChannelHook_ClientGetSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IChannelHook_ClientFillBuffer_Proxy(
    IChannelHook* This,
    REFGUID uExtent,
    REFIID riid,
    ULONG *pDataSize,
    void *pDataBuffer);
void __RPC_STUB IChannelHook_ClientFillBuffer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IChannelHook_ClientNotify_Proxy(
    IChannelHook* This,
    REFGUID uExtent,
    REFIID riid,
    ULONG cbDataSize,
    void *pDataBuffer,
    DWORD lDataRep,
    HRESULT hrFault);
void __RPC_STUB IChannelHook_ClientNotify_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IChannelHook_ServerNotify_Proxy(
    IChannelHook* This,
    REFGUID uExtent,
    REFIID riid,
    ULONG cbDataSize,
    void *pDataBuffer,
    DWORD lDataRep);
void __RPC_STUB IChannelHook_ServerNotify_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IChannelHook_ServerGetSize_Proxy(
    IChannelHook* This,
    REFGUID uExtent,
    REFIID riid,
    HRESULT hrFault,
    ULONG *pDataSize);
void __RPC_STUB IChannelHook_ServerGetSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IChannelHook_ServerFillBuffer_Proxy(
    IChannelHook* This,
    REFGUID uExtent,
    REFIID riid,
    ULONG *pDataSize,
    void *pDataBuffer,
    HRESULT hrFault);
void __RPC_STUB IChannelHook_ServerFillBuffer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IChannelHook_INTERFACE_DEFINED__ */

#endif
#endif

#if  (_WIN32_WINNT >= 0x0400 ) || defined(_WIN32_DCOM)
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IClientSecurity interface
 */
#ifndef __IClientSecurity_INTERFACE_DEFINED__
#define __IClientSecurity_INTERFACE_DEFINED__

typedef struct tagSOLE_AUTHENTICATION_SERVICE {
    DWORD dwAuthnSvc;
    DWORD dwAuthzSvc;
    OLECHAR *pPrincipalName;
    HRESULT hr;
} SOLE_AUTHENTICATION_SERVICE;

typedef SOLE_AUTHENTICATION_SERVICE *PSOLE_AUTHENTICATION_SERVICE;

typedef enum tagEOLE_AUTHENTICATION_CAPABILITIES {
    EOAC_NONE = 0x0,
    EOAC_MUTUAL_AUTH = 0x1,
    EOAC_STATIC_CLOAKING = 0x20,
    EOAC_DYNAMIC_CLOAKING = 0x40,
    EOAC_ANY_AUTHORITY = 0x80,
    EOAC_MAKE_FULLSIC = 0x100,
    EOAC_DEFAULT = 0x800,
    EOAC_SECURE_REFS = 0x2,
    EOAC_ACCESS_CONTROL = 0x4,
    EOAC_APPID = 0x8,
    EOAC_DYNAMIC = 0x10,
    EOAC_REQUIRE_FULLSIC = 0x200,
    EOAC_AUTO_IMPERSONATE = 0x400,
    EOAC_NO_CUSTOM_MARSHAL = 0x2000,
    EOAC_DISABLE_AAA = 0x1000
} EOLE_AUTHENTICATION_CAPABILITIES;

#define COLE_DEFAULT_PRINCIPAL ((OLECHAR *)(INT_PTR)-1)

#define COLE_DEFAULT_AUTHINFO ((void *)(INT_PTR)-1)


typedef struct tagSOLE_AUTHENTICATION_INFO {
    DWORD dwAuthnSvc;
    DWORD dwAuthzSvc;
    void *pAuthInfo;
} SOLE_AUTHENTICATION_INFO;
typedef struct tagSOLE_AUTHENTICATION_INFO *PSOLE_AUTHENTICATION_INFO;

typedef struct tagSOLE_AUTHENTICATION_LIST {
    DWORD cAuthInfo;
    SOLE_AUTHENTICATION_INFO *aAuthInfo;
} SOLE_AUTHENTICATION_LIST;
typedef struct tagSOLE_AUTHENTICATION_LIST *PSOLE_AUTHENTICATION_LIST;

DEFINE_GUID(IID_IClientSecurity, 0x0000013d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000013d-0000-0000-c000-000000000046")
IClientSecurity : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryBlanket(
        IUnknown *pProxy,
        DWORD *pAuthnSvc,
        DWORD *pAuthzSvc,
        OLECHAR **pServerPrincName,
        DWORD *pAuthnLevel,
        DWORD *pImpLevel,
        void **pAuthInfo,
        DWORD *pCapabilites) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBlanket(
        IUnknown *pProxy,
        DWORD dwAuthnSvc,
        DWORD dwAuthzSvc,
        OLECHAR *pServerPrincName,
        DWORD dwAuthnLevel,
        DWORD dwImpLevel,
        void *pAuthInfo,
        DWORD dwCapabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyProxy(
        IUnknown *pProxy,
        IUnknown **ppCopy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IClientSecurity, 0x0000013d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IClientSecurityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IClientSecurity* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IClientSecurity* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IClientSecurity* This);

    /*** IClientSecurity methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryBlanket)(
        IClientSecurity* This,
        IUnknown *pProxy,
        DWORD *pAuthnSvc,
        DWORD *pAuthzSvc,
        OLECHAR **pServerPrincName,
        DWORD *pAuthnLevel,
        DWORD *pImpLevel,
        void **pAuthInfo,
        DWORD *pCapabilites);

    HRESULT (STDMETHODCALLTYPE *SetBlanket)(
        IClientSecurity* This,
        IUnknown *pProxy,
        DWORD dwAuthnSvc,
        DWORD dwAuthzSvc,
        OLECHAR *pServerPrincName,
        DWORD dwAuthnLevel,
        DWORD dwImpLevel,
        void *pAuthInfo,
        DWORD dwCapabilities);

    HRESULT (STDMETHODCALLTYPE *CopyProxy)(
        IClientSecurity* This,
        IUnknown *pProxy,
        IUnknown **ppCopy);

    END_INTERFACE
} IClientSecurityVtbl;
interface IClientSecurity {
    CONST_VTBL IClientSecurityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IClientSecurity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IClientSecurity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IClientSecurity_Release(This) (This)->lpVtbl->Release(This)
/*** IClientSecurity methods ***/
#define IClientSecurity_QueryBlanket(This,pProxy,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pAuthInfo,pCapabilites) (This)->lpVtbl->QueryBlanket(This,pProxy,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pAuthInfo,pCapabilites)
#define IClientSecurity_SetBlanket(This,pProxy,dwAuthnSvc,dwAuthzSvc,pServerPrincName,dwAuthnLevel,dwImpLevel,pAuthInfo,dwCapabilities) (This)->lpVtbl->SetBlanket(This,pProxy,dwAuthnSvc,dwAuthzSvc,pServerPrincName,dwAuthnLevel,dwImpLevel,pAuthInfo,dwCapabilities)
#define IClientSecurity_CopyProxy(This,pProxy,ppCopy) (This)->lpVtbl->CopyProxy(This,pProxy,ppCopy)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IClientSecurity_QueryInterface(IClientSecurity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IClientSecurity_AddRef(IClientSecurity* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IClientSecurity_Release(IClientSecurity* This) {
    return This->lpVtbl->Release(This);
}
/*** IClientSecurity methods ***/
static FORCEINLINE HRESULT IClientSecurity_QueryBlanket(IClientSecurity* This,IUnknown *pProxy,DWORD *pAuthnSvc,DWORD *pAuthzSvc,OLECHAR **pServerPrincName,DWORD *pAuthnLevel,DWORD *pImpLevel,void **pAuthInfo,DWORD *pCapabilites) {
    return This->lpVtbl->QueryBlanket(This,pProxy,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pAuthInfo,pCapabilites);
}
static FORCEINLINE HRESULT IClientSecurity_SetBlanket(IClientSecurity* This,IUnknown *pProxy,DWORD dwAuthnSvc,DWORD dwAuthzSvc,OLECHAR *pServerPrincName,DWORD dwAuthnLevel,DWORD dwImpLevel,void *pAuthInfo,DWORD dwCapabilities) {
    return This->lpVtbl->SetBlanket(This,pProxy,dwAuthnSvc,dwAuthzSvc,pServerPrincName,dwAuthnLevel,dwImpLevel,pAuthInfo,dwCapabilities);
}
static FORCEINLINE HRESULT IClientSecurity_CopyProxy(IClientSecurity* This,IUnknown *pProxy,IUnknown **ppCopy) {
    return This->lpVtbl->CopyProxy(This,pProxy,ppCopy);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IClientSecurity_QueryBlanket_Proxy(
    IClientSecurity* This,
    IUnknown *pProxy,
    DWORD *pAuthnSvc,
    DWORD *pAuthzSvc,
    OLECHAR **pServerPrincName,
    DWORD *pAuthnLevel,
    DWORD *pImpLevel,
    void **pAuthInfo,
    DWORD *pCapabilites);
void __RPC_STUB IClientSecurity_QueryBlanket_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IClientSecurity_SetBlanket_Proxy(
    IClientSecurity* This,
    IUnknown *pProxy,
    DWORD dwAuthnSvc,
    DWORD dwAuthzSvc,
    OLECHAR *pServerPrincName,
    DWORD dwAuthnLevel,
    DWORD dwImpLevel,
    void *pAuthInfo,
    DWORD dwCapabilities);
void __RPC_STUB IClientSecurity_SetBlanket_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IClientSecurity_CopyProxy_Proxy(
    IClientSecurity* This,
    IUnknown *pProxy,
    IUnknown **ppCopy);
void __RPC_STUB IClientSecurity_CopyProxy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IClientSecurity_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IServerSecurity interface
 */
#ifndef __IServerSecurity_INTERFACE_DEFINED__
#define __IServerSecurity_INTERFACE_DEFINED__

DEFINE_GUID(IID_IServerSecurity, 0x0000013e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000013e-0000-0000-c000-000000000046")
IServerSecurity : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryBlanket(
        DWORD *pAuthnSvc,
        DWORD *pAuthzSvc,
        OLECHAR **pServerPrincName,
        DWORD *pAuthnLevel,
        DWORD *pImpLevel,
        void **pPrivs,
        DWORD *pCapabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImpersonateClient(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RevertToSelf(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsImpersonating(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IServerSecurity, 0x0000013e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IServerSecurityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IServerSecurity* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IServerSecurity* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IServerSecurity* This);

    /*** IServerSecurity methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryBlanket)(
        IServerSecurity* This,
        DWORD *pAuthnSvc,
        DWORD *pAuthzSvc,
        OLECHAR **pServerPrincName,
        DWORD *pAuthnLevel,
        DWORD *pImpLevel,
        void **pPrivs,
        DWORD *pCapabilities);

    HRESULT (STDMETHODCALLTYPE *ImpersonateClient)(
        IServerSecurity* This);

    HRESULT (STDMETHODCALLTYPE *RevertToSelf)(
        IServerSecurity* This);

    WINBOOL (STDMETHODCALLTYPE *IsImpersonating)(
        IServerSecurity* This);

    END_INTERFACE
} IServerSecurityVtbl;
interface IServerSecurity {
    CONST_VTBL IServerSecurityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IServerSecurity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IServerSecurity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IServerSecurity_Release(This) (This)->lpVtbl->Release(This)
/*** IServerSecurity methods ***/
#define IServerSecurity_QueryBlanket(This,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pPrivs,pCapabilities) (This)->lpVtbl->QueryBlanket(This,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pPrivs,pCapabilities)
#define IServerSecurity_ImpersonateClient(This) (This)->lpVtbl->ImpersonateClient(This)
#define IServerSecurity_RevertToSelf(This) (This)->lpVtbl->RevertToSelf(This)
#define IServerSecurity_IsImpersonating(This) (This)->lpVtbl->IsImpersonating(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IServerSecurity_QueryInterface(IServerSecurity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IServerSecurity_AddRef(IServerSecurity* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IServerSecurity_Release(IServerSecurity* This) {
    return This->lpVtbl->Release(This);
}
/*** IServerSecurity methods ***/
static FORCEINLINE HRESULT IServerSecurity_QueryBlanket(IServerSecurity* This,DWORD *pAuthnSvc,DWORD *pAuthzSvc,OLECHAR **pServerPrincName,DWORD *pAuthnLevel,DWORD *pImpLevel,void **pPrivs,DWORD *pCapabilities) {
    return This->lpVtbl->QueryBlanket(This,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pPrivs,pCapabilities);
}
static FORCEINLINE HRESULT IServerSecurity_ImpersonateClient(IServerSecurity* This) {
    return This->lpVtbl->ImpersonateClient(This);
}
static FORCEINLINE HRESULT IServerSecurity_RevertToSelf(IServerSecurity* This) {
    return This->lpVtbl->RevertToSelf(This);
}
static FORCEINLINE WINBOOL IServerSecurity_IsImpersonating(IServerSecurity* This) {
    return This->lpVtbl->IsImpersonating(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IServerSecurity_QueryBlanket_Proxy(
    IServerSecurity* This,
    DWORD *pAuthnSvc,
    DWORD *pAuthzSvc,
    OLECHAR **pServerPrincName,
    DWORD *pAuthnLevel,
    DWORD *pImpLevel,
    void **pPrivs,
    DWORD *pCapabilities);
void __RPC_STUB IServerSecurity_QueryBlanket_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IServerSecurity_ImpersonateClient_Proxy(
    IServerSecurity* This);
void __RPC_STUB IServerSecurity_ImpersonateClient_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IServerSecurity_RevertToSelf_Proxy(
    IServerSecurity* This);
void __RPC_STUB IServerSecurity_RevertToSelf_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
WINBOOL STDMETHODCALLTYPE IServerSecurity_IsImpersonating_Proxy(
    IServerSecurity* This);
void __RPC_STUB IServerSecurity_IsImpersonating_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IServerSecurity_INTERFACE_DEFINED__ */


typedef enum tagRPCOPT_PROPERTIES {
    COMBND_RPCTIMEOUT = 0x1,
    COMBND_SERVER_LOCALITY = 0x2,
    COMBND_RESERVED1 = 0x4
} RPCOPT_PROPERTIES;

typedef enum tagRPCOPT_SERVER_LOCALITY_VALUES {
    SERVER_LOCALITY_PROCESS_LOCAL = 0,
    SERVER_LOCALITY_MACHINE_LOCAL = 1,
    SERVER_LOCALITY_REMOTE = 2
} RPCOPT_SERVER_LOCALITY_VALUES;

/*****************************************************************************
 * IRpcOptions interface
 */
#ifndef __IRpcOptions_INTERFACE_DEFINED__
#define __IRpcOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcOptions, 0x00000144, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000144-0000-0000-c000-000000000046")
IRpcOptions : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Set(
        IUnknown *pPrx,
        RPCOPT_PROPERTIES dwProperty,
        ULONG_PTR dwValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Query(
        IUnknown *pPrx,
        RPCOPT_PROPERTIES dwProperty,
        ULONG_PTR *pdwValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcOptions, 0x00000144, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IRpcOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcOptions* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcOptions* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcOptions* This);

    /*** IRpcOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *Set)(
        IRpcOptions* This,
        IUnknown *pPrx,
        RPCOPT_PROPERTIES dwProperty,
        ULONG_PTR dwValue);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IRpcOptions* This,
        IUnknown *pPrx,
        RPCOPT_PROPERTIES dwProperty,
        ULONG_PTR *pdwValue);

    END_INTERFACE
} IRpcOptionsVtbl;
interface IRpcOptions {
    CONST_VTBL IRpcOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcOptions methods ***/
#define IRpcOptions_Set(This,pPrx,dwProperty,dwValue) (This)->lpVtbl->Set(This,pPrx,dwProperty,dwValue)
#define IRpcOptions_Query(This,pPrx,dwProperty,pdwValue) (This)->lpVtbl->Query(This,pPrx,dwProperty,pdwValue)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRpcOptions_QueryInterface(IRpcOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRpcOptions_AddRef(IRpcOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRpcOptions_Release(IRpcOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcOptions methods ***/
static FORCEINLINE HRESULT IRpcOptions_Set(IRpcOptions* This,IUnknown *pPrx,RPCOPT_PROPERTIES dwProperty,ULONG_PTR dwValue) {
    return This->lpVtbl->Set(This,pPrx,dwProperty,dwValue);
}
static FORCEINLINE HRESULT IRpcOptions_Query(IRpcOptions* This,IUnknown *pPrx,RPCOPT_PROPERTIES dwProperty,ULONG_PTR *pdwValue) {
    return This->lpVtbl->Query(This,pPrx,dwProperty,pdwValue);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRpcOptions_Set_Proxy(
    IRpcOptions* This,
    IUnknown *pPrx,
    RPCOPT_PROPERTIES dwProperty,
    ULONG_PTR dwValue);
void __RPC_STUB IRpcOptions_Set_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcOptions_Query_Proxy(
    IRpcOptions* This,
    IUnknown *pPrx,
    RPCOPT_PROPERTIES dwProperty,
    ULONG_PTR *pdwValue);
void __RPC_STUB IRpcOptions_Query_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRpcOptions_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef enum tagGLOBALOPT_PROPERTIES {
    COMGLB_EXCEPTION_HANDLING = 1,
    COMGLB_APPID = 2,
    COMGLB_RPC_THREADPOOL_SETTING = 3,
    COMGLB_RO_SETTINGS = 4,
    COMGLB_UNMARSHALING_POLICY = 5
} GLOBALOPT_PROPERTIES;

typedef enum tagGLOBALOPT_EH_VALUES {
    COMGLB_EXCEPTION_HANDLE = 0,
    COMGLB_EXCEPTION_DONOT_HANDLE_FATAL = 1,
    COMGLB_EXCEPTION_DONOT_HANDLE = COMGLB_EXCEPTION_DONOT_HANDLE_FATAL,
    COMGLB_EXCEPTION_DONOT_HANDLE_ANY = 2
} GLOBALOPT_EH_VALUES;

typedef enum tagGLOBALOPT_RPCTP_VALUES {
    COMGLB_RPC_THREADPOOL_SETTING_DEFAULT_POOL = 0,
    COMGLB_RPC_THREADPOOL_SETTING_PRIVATE_POOL = 1
} GLOBALOPT_RPCTP_VALUES;

typedef enum tagGLOBALOPT_RO_FLAGS {
    COMGLB_STA_MODALLOOP_REMOVE_TOUCH_MESSAGES = 0x1,
    COMGLB_STA_MODALLOOP_SHARED_QUEUE_REMOVE_INPUT_MESSAGES = 0x2,
    COMGLB_STA_MODALLOOP_SHARED_QUEUE_DONOT_REMOVE_INPUT_MESSAGES = 0x4,
    COMGLB_FAST_RUNDOWN = 0x8,
    COMGLB_RESERVED1 = 0x10,
    COMGLB_RESERVED2 = 0x20,
    COMGLB_RESERVED3 = 0x40,
    COMGLB_STA_MODALLOOP_SHARED_QUEUE_REORDER_POINTER_MESSAGES = 0x80
} GLOBALOPT_RO_FLAGS;

typedef enum tagGLOBALOPT_UNMARSHALING_POLICY_VALUES {
    COMGLB_UNMARSHALING_POLICY_NORMAL = 0,
    COMGLB_UNMARSHALING_POLICY_STRONG = 1,
    COMGLB_UNMARSHALING_POLICY_HYBRID = 2
} GLOBALOPT_UNMARSHALING_POLICY_VALUES;

/*****************************************************************************
 * IGlobalOptions interface
 */
#ifndef __IGlobalOptions_INTERFACE_DEFINED__
#define __IGlobalOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID_IGlobalOptions, 0x0000015b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000015b-0000-0000-c000-000000000046")
IGlobalOptions : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Set(
        GLOBALOPT_PROPERTIES dwProperty,
        ULONG_PTR dwValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Query(
        GLOBALOPT_PROPERTIES dwProperty,
        ULONG_PTR *pdwValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGlobalOptions, 0x0000015b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IGlobalOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGlobalOptions* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGlobalOptions* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGlobalOptions* This);

    /*** IGlobalOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *Set)(
        IGlobalOptions* This,
        GLOBALOPT_PROPERTIES dwProperty,
        ULONG_PTR dwValue);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IGlobalOptions* This,
        GLOBALOPT_PROPERTIES dwProperty,
        ULONG_PTR *pdwValue);

    END_INTERFACE
} IGlobalOptionsVtbl;
interface IGlobalOptions {
    CONST_VTBL IGlobalOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGlobalOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGlobalOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGlobalOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IGlobalOptions methods ***/
#define IGlobalOptions_Set(This,dwProperty,dwValue) (This)->lpVtbl->Set(This,dwProperty,dwValue)
#define IGlobalOptions_Query(This,dwProperty,pdwValue) (This)->lpVtbl->Query(This,dwProperty,pdwValue)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IGlobalOptions_QueryInterface(IGlobalOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IGlobalOptions_AddRef(IGlobalOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IGlobalOptions_Release(IGlobalOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IGlobalOptions methods ***/
static FORCEINLINE HRESULT IGlobalOptions_Set(IGlobalOptions* This,GLOBALOPT_PROPERTIES dwProperty,ULONG_PTR dwValue) {
    return This->lpVtbl->Set(This,dwProperty,dwValue);
}
static FORCEINLINE HRESULT IGlobalOptions_Query(IGlobalOptions* This,GLOBALOPT_PROPERTIES dwProperty,ULONG_PTR *pdwValue) {
    return This->lpVtbl->Query(This,dwProperty,pdwValue);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IGlobalOptions_Set_Proxy(
    IGlobalOptions* This,
    GLOBALOPT_PROPERTIES dwProperty,
    ULONG_PTR dwValue);
void __RPC_STUB IGlobalOptions_Set_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IGlobalOptions_Query_Proxy(
    IGlobalOptions* This,
    GLOBALOPT_PROPERTIES dwProperty,
    ULONG_PTR *pdwValue);
void __RPC_STUB IGlobalOptions_Query_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IGlobalOptions_INTERFACE_DEFINED__ */

#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * ISurrogate interface
 */
#ifndef __ISurrogate_INTERFACE_DEFINED__
#define __ISurrogate_INTERFACE_DEFINED__

typedef ISurrogate *LPSURROGATE;

DEFINE_GUID(IID_ISurrogate, 0x00000022, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000022-0000-0000-c000-000000000046")
ISurrogate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE LoadDllServer(
        REFCLSID Clsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeSurrogate(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISurrogate, 0x00000022, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISurrogateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISurrogate* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISurrogate* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISurrogate* This);

    /*** ISurrogate methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadDllServer)(
        ISurrogate* This,
        REFCLSID Clsid);

    HRESULT (STDMETHODCALLTYPE *FreeSurrogate)(
        ISurrogate* This);

    END_INTERFACE
} ISurrogateVtbl;
interface ISurrogate {
    CONST_VTBL ISurrogateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISurrogate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISurrogate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISurrogate_Release(This) (This)->lpVtbl->Release(This)
/*** ISurrogate methods ***/
#define ISurrogate_LoadDllServer(This,Clsid) (This)->lpVtbl->LoadDllServer(This,Clsid)
#define ISurrogate_FreeSurrogate(This) (This)->lpVtbl->FreeSurrogate(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISurrogate_QueryInterface(ISurrogate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISurrogate_AddRef(ISurrogate* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISurrogate_Release(ISurrogate* This) {
    return This->lpVtbl->Release(This);
}
/*** ISurrogate methods ***/
static FORCEINLINE HRESULT ISurrogate_LoadDllServer(ISurrogate* This,REFCLSID Clsid) {
    return This->lpVtbl->LoadDllServer(This,Clsid);
}
static FORCEINLINE HRESULT ISurrogate_FreeSurrogate(ISurrogate* This) {
    return This->lpVtbl->FreeSurrogate(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISurrogate_LoadDllServer_Proxy(
    ISurrogate* This,
    REFCLSID Clsid);
void __RPC_STUB ISurrogate_LoadDllServer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISurrogate_FreeSurrogate_Proxy(
    ISurrogate* This);
void __RPC_STUB ISurrogate_FreeSurrogate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ISurrogate_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IGlobalInterfaceTable interface
 */
#ifndef __IGlobalInterfaceTable_INTERFACE_DEFINED__
#define __IGlobalInterfaceTable_INTERFACE_DEFINED__

typedef IGlobalInterfaceTable *LPGLOBALINTERFACETABLE;

DEFINE_GUID(IID_IGlobalInterfaceTable, 0x00000146, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000146-0000-0000-c000-000000000046")
IGlobalInterfaceTable : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterInterfaceInGlobal(
        IUnknown *pUnk,
        REFIID riid,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RevokeInterfaceFromGlobal(
        DWORD dwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInterfaceFromGlobal(
        DWORD dwCookie,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGlobalInterfaceTable, 0x00000146, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IGlobalInterfaceTableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGlobalInterfaceTable* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGlobalInterfaceTable* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGlobalInterfaceTable* This);

    /*** IGlobalInterfaceTable methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterInterfaceInGlobal)(
        IGlobalInterfaceTable* This,
        IUnknown *pUnk,
        REFIID riid,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RevokeInterfaceFromGlobal)(
        IGlobalInterfaceTable* This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *GetInterfaceFromGlobal)(
        IGlobalInterfaceTable* This,
        DWORD dwCookie,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IGlobalInterfaceTableVtbl;
interface IGlobalInterfaceTable {
    CONST_VTBL IGlobalInterfaceTableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGlobalInterfaceTable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGlobalInterfaceTable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGlobalInterfaceTable_Release(This) (This)->lpVtbl->Release(This)
/*** IGlobalInterfaceTable methods ***/
#define IGlobalInterfaceTable_RegisterInterfaceInGlobal(This,pUnk,riid,pdwCookie) (This)->lpVtbl->RegisterInterfaceInGlobal(This,pUnk,riid,pdwCookie)
#define IGlobalInterfaceTable_RevokeInterfaceFromGlobal(This,dwCookie) (This)->lpVtbl->RevokeInterfaceFromGlobal(This,dwCookie)
#define IGlobalInterfaceTable_GetInterfaceFromGlobal(This,dwCookie,riid,ppv) (This)->lpVtbl->GetInterfaceFromGlobal(This,dwCookie,riid,ppv)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IGlobalInterfaceTable_QueryInterface(IGlobalInterfaceTable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IGlobalInterfaceTable_AddRef(IGlobalInterfaceTable* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IGlobalInterfaceTable_Release(IGlobalInterfaceTable* This) {
    return This->lpVtbl->Release(This);
}
/*** IGlobalInterfaceTable methods ***/
static FORCEINLINE HRESULT IGlobalInterfaceTable_RegisterInterfaceInGlobal(IGlobalInterfaceTable* This,IUnknown *pUnk,REFIID riid,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterInterfaceInGlobal(This,pUnk,riid,pdwCookie);
}
static FORCEINLINE HRESULT IGlobalInterfaceTable_RevokeInterfaceFromGlobal(IGlobalInterfaceTable* This,DWORD dwCookie) {
    return This->lpVtbl->RevokeInterfaceFromGlobal(This,dwCookie);
}
static FORCEINLINE HRESULT IGlobalInterfaceTable_GetInterfaceFromGlobal(IGlobalInterfaceTable* This,DWORD dwCookie,REFIID riid,void **ppv) {
    return This->lpVtbl->GetInterfaceFromGlobal(This,dwCookie,riid,ppv);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IGlobalInterfaceTable_RegisterInterfaceInGlobal_Proxy(
    IGlobalInterfaceTable* This,
    IUnknown *pUnk,
    REFIID riid,
    DWORD *pdwCookie);
void __RPC_STUB IGlobalInterfaceTable_RegisterInterfaceInGlobal_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IGlobalInterfaceTable_RevokeInterfaceFromGlobal_Proxy(
    IGlobalInterfaceTable* This,
    DWORD dwCookie);
void __RPC_STUB IGlobalInterfaceTable_RevokeInterfaceFromGlobal_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IGlobalInterfaceTable_GetInterfaceFromGlobal_Proxy(
    IGlobalInterfaceTable* This,
    DWORD dwCookie,
    REFIID riid,
    void **ppv);
void __RPC_STUB IGlobalInterfaceTable_GetInterfaceFromGlobal_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IGlobalInterfaceTable_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * ISynchronize interface
 */
#ifndef __ISynchronize_INTERFACE_DEFINED__
#define __ISynchronize_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronize, 0x00000030, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000030-0000-0000-c000-000000000046")
ISynchronize : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Wait(
        DWORD dwFlags,
        DWORD dwMilliseconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE Signal(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronize, 0x00000030, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronize* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronize* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronize* This);

    /*** ISynchronize methods ***/
    HRESULT (STDMETHODCALLTYPE *Wait)(
        ISynchronize* This,
        DWORD dwFlags,
        DWORD dwMilliseconds);

    HRESULT (STDMETHODCALLTYPE *Signal)(
        ISynchronize* This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ISynchronize* This);

    END_INTERFACE
} ISynchronizeVtbl;
interface ISynchronize {
    CONST_VTBL ISynchronizeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronize_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronize_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronize_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronize methods ***/
#define ISynchronize_Wait(This,dwFlags,dwMilliseconds) (This)->lpVtbl->Wait(This,dwFlags,dwMilliseconds)
#define ISynchronize_Signal(This) (This)->lpVtbl->Signal(This)
#define ISynchronize_Reset(This) (This)->lpVtbl->Reset(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISynchronize_QueryInterface(ISynchronize* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISynchronize_AddRef(ISynchronize* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISynchronize_Release(ISynchronize* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronize methods ***/
static FORCEINLINE HRESULT ISynchronize_Wait(ISynchronize* This,DWORD dwFlags,DWORD dwMilliseconds) {
    return This->lpVtbl->Wait(This,dwFlags,dwMilliseconds);
}
static FORCEINLINE HRESULT ISynchronize_Signal(ISynchronize* This) {
    return This->lpVtbl->Signal(This);
}
static FORCEINLINE HRESULT ISynchronize_Reset(ISynchronize* This) {
    return This->lpVtbl->Reset(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISynchronize_Wait_Proxy(
    ISynchronize* This,
    DWORD dwFlags,
    DWORD dwMilliseconds);
void __RPC_STUB ISynchronize_Wait_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISynchronize_Signal_Proxy(
    ISynchronize* This);
void __RPC_STUB ISynchronize_Signal_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISynchronize_Reset_Proxy(
    ISynchronize* This);
void __RPC_STUB ISynchronize_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ISynchronize_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISynchronizeHandle interface
 */
#ifndef __ISynchronizeHandle_INTERFACE_DEFINED__
#define __ISynchronizeHandle_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizeHandle, 0x00000031, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000031-0000-0000-c000-000000000046")
ISynchronizeHandle : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetHandle(
        HANDLE *ph) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizeHandle, 0x00000031, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeHandleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizeHandle* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizeHandle* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizeHandle* This);

    /*** ISynchronizeHandle methods ***/
    HRESULT (STDMETHODCALLTYPE *GetHandle)(
        ISynchronizeHandle* This,
        HANDLE *ph);

    END_INTERFACE
} ISynchronizeHandleVtbl;
interface ISynchronizeHandle {
    CONST_VTBL ISynchronizeHandleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizeHandle_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizeHandle_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizeHandle_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronizeHandle methods ***/
#define ISynchronizeHandle_GetHandle(This,ph) (This)->lpVtbl->GetHandle(This,ph)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISynchronizeHandle_QueryInterface(ISynchronizeHandle* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISynchronizeHandle_AddRef(ISynchronizeHandle* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISynchronizeHandle_Release(ISynchronizeHandle* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronizeHandle methods ***/
static FORCEINLINE HRESULT ISynchronizeHandle_GetHandle(ISynchronizeHandle* This,HANDLE *ph) {
    return This->lpVtbl->GetHandle(This,ph);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISynchronizeHandle_GetHandle_Proxy(
    ISynchronizeHandle* This,
    HANDLE *ph);
void __RPC_STUB ISynchronizeHandle_GetHandle_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ISynchronizeHandle_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISynchronizeEvent interface
 */
#ifndef __ISynchronizeEvent_INTERFACE_DEFINED__
#define __ISynchronizeEvent_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizeEvent, 0x00000032, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000032-0000-0000-c000-000000000046")
ISynchronizeEvent : public ISynchronizeHandle
{
    virtual HRESULT STDMETHODCALLTYPE SetEventHandle(
        HANDLE *ph) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizeEvent, 0x00000032, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeEventVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizeEvent* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizeEvent* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizeEvent* This);

    /*** ISynchronizeHandle methods ***/
    HRESULT (STDMETHODCALLTYPE *GetHandle)(
        ISynchronizeEvent* This,
        HANDLE *ph);

    /*** ISynchronizeEvent methods ***/
    HRESULT (STDMETHODCALLTYPE *SetEventHandle)(
        ISynchronizeEvent* This,
        HANDLE *ph);

    END_INTERFACE
} ISynchronizeEventVtbl;
interface ISynchronizeEvent {
    CONST_VTBL ISynchronizeEventVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizeEvent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizeEvent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizeEvent_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronizeHandle methods ***/
#define ISynchronizeEvent_GetHandle(This,ph) (This)->lpVtbl->GetHandle(This,ph)
/*** ISynchronizeEvent methods ***/
#define ISynchronizeEvent_SetEventHandle(This,ph) (This)->lpVtbl->SetEventHandle(This,ph)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISynchronizeEvent_QueryInterface(ISynchronizeEvent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISynchronizeEvent_AddRef(ISynchronizeEvent* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISynchronizeEvent_Release(ISynchronizeEvent* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronizeHandle methods ***/
static FORCEINLINE HRESULT ISynchronizeEvent_GetHandle(ISynchronizeEvent* This,HANDLE *ph) {
    return This->lpVtbl->GetHandle(This,ph);
}
/*** ISynchronizeEvent methods ***/
static FORCEINLINE HRESULT ISynchronizeEvent_SetEventHandle(ISynchronizeEvent* This,HANDLE *ph) {
    return This->lpVtbl->SetEventHandle(This,ph);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISynchronizeEvent_SetEventHandle_Proxy(
    ISynchronizeEvent* This,
    HANDLE *ph);
void __RPC_STUB ISynchronizeEvent_SetEventHandle_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ISynchronizeEvent_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISynchronizeContainer interface
 */
#ifndef __ISynchronizeContainer_INTERFACE_DEFINED__
#define __ISynchronizeContainer_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizeContainer, 0x00000033, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000033-0000-0000-c000-000000000046")
ISynchronizeContainer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddSynchronize(
        ISynchronize *pSync) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitMultiple(
        DWORD dwFlags,
        DWORD dwTimeOut,
        ISynchronize **ppSync) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizeContainer, 0x00000033, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizeContainer* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizeContainer* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizeContainer* This);

    /*** ISynchronizeContainer methods ***/
    HRESULT (STDMETHODCALLTYPE *AddSynchronize)(
        ISynchronizeContainer* This,
        ISynchronize *pSync);

    HRESULT (STDMETHODCALLTYPE *WaitMultiple)(
        ISynchronizeContainer* This,
        DWORD dwFlags,
        DWORD dwTimeOut,
        ISynchronize **ppSync);

    END_INTERFACE
} ISynchronizeContainerVtbl;
interface ISynchronizeContainer {
    CONST_VTBL ISynchronizeContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizeContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizeContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizeContainer_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronizeContainer methods ***/
#define ISynchronizeContainer_AddSynchronize(This,pSync) (This)->lpVtbl->AddSynchronize(This,pSync)
#define ISynchronizeContainer_WaitMultiple(This,dwFlags,dwTimeOut,ppSync) (This)->lpVtbl->WaitMultiple(This,dwFlags,dwTimeOut,ppSync)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISynchronizeContainer_QueryInterface(ISynchronizeContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISynchronizeContainer_AddRef(ISynchronizeContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISynchronizeContainer_Release(ISynchronizeContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronizeContainer methods ***/
static FORCEINLINE HRESULT ISynchronizeContainer_AddSynchronize(ISynchronizeContainer* This,ISynchronize *pSync) {
    return This->lpVtbl->AddSynchronize(This,pSync);
}
static FORCEINLINE HRESULT ISynchronizeContainer_WaitMultiple(ISynchronizeContainer* This,DWORD dwFlags,DWORD dwTimeOut,ISynchronize **ppSync) {
    return This->lpVtbl->WaitMultiple(This,dwFlags,dwTimeOut,ppSync);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISynchronizeContainer_AddSynchronize_Proxy(
    ISynchronizeContainer* This,
    ISynchronize *pSync);
void __RPC_STUB ISynchronizeContainer_AddSynchronize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISynchronizeContainer_WaitMultiple_Proxy(
    ISynchronizeContainer* This,
    DWORD dwFlags,
    DWORD dwTimeOut,
    ISynchronize **ppSync);
void __RPC_STUB ISynchronizeContainer_WaitMultiple_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ISynchronizeContainer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISynchronizeMutex interface
 */
#ifndef __ISynchronizeMutex_INTERFACE_DEFINED__
#define __ISynchronizeMutex_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizeMutex, 0x00000025, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000025-0000-0000-c000-000000000046")
ISynchronizeMutex : public ISynchronize
{
    virtual HRESULT STDMETHODCALLTYPE ReleaseMutex(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizeMutex, 0x00000025, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeMutexVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizeMutex* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizeMutex* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizeMutex* This);

    /*** ISynchronize methods ***/
    HRESULT (STDMETHODCALLTYPE *Wait)(
        ISynchronizeMutex* This,
        DWORD dwFlags,
        DWORD dwMilliseconds);

    HRESULT (STDMETHODCALLTYPE *Signal)(
        ISynchronizeMutex* This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ISynchronizeMutex* This);

    /*** ISynchronizeMutex methods ***/
    HRESULT (STDMETHODCALLTYPE *ReleaseMutex)(
        ISynchronizeMutex* This);

    END_INTERFACE
} ISynchronizeMutexVtbl;
interface ISynchronizeMutex {
    CONST_VTBL ISynchronizeMutexVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizeMutex_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizeMutex_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizeMutex_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronize methods ***/
#define ISynchronizeMutex_Wait(This,dwFlags,dwMilliseconds) (This)->lpVtbl->Wait(This,dwFlags,dwMilliseconds)
#define ISynchronizeMutex_Signal(This) (This)->lpVtbl->Signal(This)
#define ISynchronizeMutex_Reset(This) (This)->lpVtbl->Reset(This)
/*** ISynchronizeMutex methods ***/
#define ISynchronizeMutex_ReleaseMutex(This) (This)->lpVtbl->ReleaseMutex(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISynchronizeMutex_QueryInterface(ISynchronizeMutex* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISynchronizeMutex_AddRef(ISynchronizeMutex* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISynchronizeMutex_Release(ISynchronizeMutex* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronize methods ***/
static FORCEINLINE HRESULT ISynchronizeMutex_Wait(ISynchronizeMutex* This,DWORD dwFlags,DWORD dwMilliseconds) {
    return This->lpVtbl->Wait(This,dwFlags,dwMilliseconds);
}
static FORCEINLINE HRESULT ISynchronizeMutex_Signal(ISynchronizeMutex* This) {
    return This->lpVtbl->Signal(This);
}
static FORCEINLINE HRESULT ISynchronizeMutex_Reset(ISynchronizeMutex* This) {
    return This->lpVtbl->Reset(This);
}
/*** ISynchronizeMutex methods ***/
static FORCEINLINE HRESULT ISynchronizeMutex_ReleaseMutex(ISynchronizeMutex* This) {
    return This->lpVtbl->ReleaseMutex(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISynchronizeMutex_ReleaseMutex_Proxy(
    ISynchronizeMutex* This);
void __RPC_STUB ISynchronizeMutex_ReleaseMutex_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ISynchronizeMutex_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICancelMethodCalls interface
 */
#ifndef __ICancelMethodCalls_INTERFACE_DEFINED__
#define __ICancelMethodCalls_INTERFACE_DEFINED__

typedef ICancelMethodCalls *LPCANCELMETHODCALLS;

DEFINE_GUID(IID_ICancelMethodCalls, 0x00000029, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000029-0000-0000-c000-000000000046")
ICancelMethodCalls : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ULONG ulSeconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE TestCancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICancelMethodCalls, 0x00000029, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ICancelMethodCallsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICancelMethodCalls* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICancelMethodCalls* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICancelMethodCalls* This);

    /*** ICancelMethodCalls methods ***/
    HRESULT (STDMETHODCALLTYPE *Cancel)(
        ICancelMethodCalls* This,
        ULONG ulSeconds);

    HRESULT (STDMETHODCALLTYPE *TestCancel)(
        ICancelMethodCalls* This);

    END_INTERFACE
} ICancelMethodCallsVtbl;
interface ICancelMethodCalls {
    CONST_VTBL ICancelMethodCallsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICancelMethodCalls_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICancelMethodCalls_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICancelMethodCalls_Release(This) (This)->lpVtbl->Release(This)
/*** ICancelMethodCalls methods ***/
#define ICancelMethodCalls_Cancel(This,ulSeconds) (This)->lpVtbl->Cancel(This,ulSeconds)
#define ICancelMethodCalls_TestCancel(This) (This)->lpVtbl->TestCancel(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICancelMethodCalls_QueryInterface(ICancelMethodCalls* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICancelMethodCalls_AddRef(ICancelMethodCalls* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICancelMethodCalls_Release(ICancelMethodCalls* This) {
    return This->lpVtbl->Release(This);
}
/*** ICancelMethodCalls methods ***/
static FORCEINLINE HRESULT ICancelMethodCalls_Cancel(ICancelMethodCalls* This,ULONG ulSeconds) {
    return This->lpVtbl->Cancel(This,ulSeconds);
}
static FORCEINLINE HRESULT ICancelMethodCalls_TestCancel(ICancelMethodCalls* This) {
    return This->lpVtbl->TestCancel(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ICancelMethodCalls_Cancel_Proxy(
    ICancelMethodCalls* This,
    ULONG ulSeconds);
void __RPC_STUB ICancelMethodCalls_Cancel_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ICancelMethodCalls_TestCancel_Proxy(
    ICancelMethodCalls* This);
void __RPC_STUB ICancelMethodCalls_TestCancel_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ICancelMethodCalls_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAsyncManager interface
 */
#ifndef __IAsyncManager_INTERFACE_DEFINED__
#define __IAsyncManager_INTERFACE_DEFINED__

typedef enum tagDCOM_CALL_STATE {
    DCOM_NONE = 0x0,
    DCOM_CALL_COMPLETE = 0x1,
    DCOM_CALL_CANCELED = 0x2
} DCOM_CALL_STATE;

DEFINE_GUID(IID_IAsyncManager, 0x0000002a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000002a-0000-0000-c000-000000000046")
IAsyncManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CompleteCall(
        HRESULT Result) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCallContext(
        REFIID riid,
        void **pInterface) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        ULONG *pulStateFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAsyncManager, 0x0000002a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IAsyncManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAsyncManager* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAsyncManager* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAsyncManager* This);

    /*** IAsyncManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CompleteCall)(
        IAsyncManager* This,
        HRESULT Result);

    HRESULT (STDMETHODCALLTYPE *GetCallContext)(
        IAsyncManager* This,
        REFIID riid,
        void **pInterface);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IAsyncManager* This,
        ULONG *pulStateFlags);

    END_INTERFACE
} IAsyncManagerVtbl;
interface IAsyncManager {
    CONST_VTBL IAsyncManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAsyncManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAsyncManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAsyncManager_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncManager methods ***/
#define IAsyncManager_CompleteCall(This,Result) (This)->lpVtbl->CompleteCall(This,Result)
#define IAsyncManager_GetCallContext(This,riid,pInterface) (This)->lpVtbl->GetCallContext(This,riid,pInterface)
#define IAsyncManager_GetState(This,pulStateFlags) (This)->lpVtbl->GetState(This,pulStateFlags)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAsyncManager_QueryInterface(IAsyncManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAsyncManager_AddRef(IAsyncManager* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAsyncManager_Release(IAsyncManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncManager methods ***/
static FORCEINLINE HRESULT IAsyncManager_CompleteCall(IAsyncManager* This,HRESULT Result) {
    return This->lpVtbl->CompleteCall(This,Result);
}
static FORCEINLINE HRESULT IAsyncManager_GetCallContext(IAsyncManager* This,REFIID riid,void **pInterface) {
    return This->lpVtbl->GetCallContext(This,riid,pInterface);
}
static FORCEINLINE HRESULT IAsyncManager_GetState(IAsyncManager* This,ULONG *pulStateFlags) {
    return This->lpVtbl->GetState(This,pulStateFlags);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAsyncManager_CompleteCall_Proxy(
    IAsyncManager* This,
    HRESULT Result);
void __RPC_STUB IAsyncManager_CompleteCall_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAsyncManager_GetCallContext_Proxy(
    IAsyncManager* This,
    REFIID riid,
    void **pInterface);
void __RPC_STUB IAsyncManager_GetCallContext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAsyncManager_GetState_Proxy(
    IAsyncManager* This,
    ULONG *pulStateFlags);
void __RPC_STUB IAsyncManager_GetState_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IAsyncManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICallFactory interface
 */
#ifndef __ICallFactory_INTERFACE_DEFINED__
#define __ICallFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICallFactory, 0x1c733a30, 0x2a1c, 0x11ce, 0xad,0xe5, 0x00,0xaa,0x00,0x44,0x77,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1c733a30-2a1c-11ce-ade5-00aa0044773d")
ICallFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateCall(
        REFIID riid,
        IUnknown *pCtrlUnk,
        REFIID riid2,
        IUnknown **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICallFactory, 0x1c733a30, 0x2a1c, 0x11ce, 0xad,0xe5, 0x00,0xaa,0x00,0x44,0x77,0x3d)
#endif
#else
typedef struct ICallFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICallFactory* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICallFactory* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICallFactory* This);

    /*** ICallFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateCall)(
        ICallFactory* This,
        REFIID riid,
        IUnknown *pCtrlUnk,
        REFIID riid2,
        IUnknown **ppv);

    END_INTERFACE
} ICallFactoryVtbl;
interface ICallFactory {
    CONST_VTBL ICallFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICallFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICallFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICallFactory_Release(This) (This)->lpVtbl->Release(This)
/*** ICallFactory methods ***/
#define ICallFactory_CreateCall(This,riid,pCtrlUnk,riid2,ppv) (This)->lpVtbl->CreateCall(This,riid,pCtrlUnk,riid2,ppv)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ICallFactory_QueryInterface(ICallFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ICallFactory_AddRef(ICallFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ICallFactory_Release(ICallFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** ICallFactory methods ***/
static FORCEINLINE HRESULT ICallFactory_CreateCall(ICallFactory* This,REFIID riid,IUnknown *pCtrlUnk,REFIID riid2,IUnknown **ppv) {
    return This->lpVtbl->CreateCall(This,riid,pCtrlUnk,riid2,ppv);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ICallFactory_CreateCall_Proxy(
    ICallFactory* This,
    REFIID riid,
    IUnknown *pCtrlUnk,
    REFIID riid2,
    IUnknown **ppv);
void __RPC_STUB ICallFactory_CreateCall_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ICallFactory_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcHelper interface
 */
#ifndef __IRpcHelper_INTERFACE_DEFINED__
#define __IRpcHelper_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcHelper, 0x00000149, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000149-0000-0000-c000-000000000046")
IRpcHelper : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDCOMProtocolVersion(
        DWORD *pComVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIIDFromOBJREF(
        void *pObjRef,
        IID **piid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcHelper, 0x00000149, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IRpcHelperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcHelper* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcHelper* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcHelper* This);

    /*** IRpcHelper methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDCOMProtocolVersion)(
        IRpcHelper* This,
        DWORD *pComVersion);

    HRESULT (STDMETHODCALLTYPE *GetIIDFromOBJREF)(
        IRpcHelper* This,
        void *pObjRef,
        IID **piid);

    END_INTERFACE
} IRpcHelperVtbl;
interface IRpcHelper {
    CONST_VTBL IRpcHelperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcHelper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcHelper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcHelper_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcHelper methods ***/
#define IRpcHelper_GetDCOMProtocolVersion(This,pComVersion) (This)->lpVtbl->GetDCOMProtocolVersion(This,pComVersion)
#define IRpcHelper_GetIIDFromOBJREF(This,pObjRef,piid) (This)->lpVtbl->GetIIDFromOBJREF(This,pObjRef,piid)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRpcHelper_QueryInterface(IRpcHelper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRpcHelper_AddRef(IRpcHelper* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRpcHelper_Release(IRpcHelper* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcHelper methods ***/
static FORCEINLINE HRESULT IRpcHelper_GetDCOMProtocolVersion(IRpcHelper* This,DWORD *pComVersion) {
    return This->lpVtbl->GetDCOMProtocolVersion(This,pComVersion);
}
static FORCEINLINE HRESULT IRpcHelper_GetIIDFromOBJREF(IRpcHelper* This,void *pObjRef,IID **piid) {
    return This->lpVtbl->GetIIDFromOBJREF(This,pObjRef,piid);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRpcHelper_GetDCOMProtocolVersion_Proxy(
    IRpcHelper* This,
    DWORD *pComVersion);
void __RPC_STUB IRpcHelper_GetDCOMProtocolVersion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRpcHelper_GetIIDFromOBJREF_Proxy(
    IRpcHelper* This,
    void *pObjRef,
    IID **piid);
void __RPC_STUB IRpcHelper_GetIIDFromOBJREF_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRpcHelper_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IReleaseMarshalBuffers interface
 */
#ifndef __IReleaseMarshalBuffers_INTERFACE_DEFINED__
#define __IReleaseMarshalBuffers_INTERFACE_DEFINED__

DEFINE_GUID(IID_IReleaseMarshalBuffers, 0xeb0cb9e8, 0x7996, 0x11d2, 0x87,0x2e, 0x00,0x00,0xf8,0x08,0x08,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eb0cb9e8-7996-11d2-872e-0000f8080859")
IReleaseMarshalBuffers : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ReleaseMarshalBuffer(
        RPCOLEMESSAGE *pMsg,
        DWORD dwFlags,
        IUnknown *pChnl) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IReleaseMarshalBuffers, 0xeb0cb9e8, 0x7996, 0x11d2, 0x87,0x2e, 0x00,0x00,0xf8,0x08,0x08,0x59)
#endif
#else
typedef struct IReleaseMarshalBuffersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IReleaseMarshalBuffers* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IReleaseMarshalBuffers* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IReleaseMarshalBuffers* This);

    /*** IReleaseMarshalBuffers methods ***/
    HRESULT (STDMETHODCALLTYPE *ReleaseMarshalBuffer)(
        IReleaseMarshalBuffers* This,
        RPCOLEMESSAGE *pMsg,
        DWORD dwFlags,
        IUnknown *pChnl);

    END_INTERFACE
} IReleaseMarshalBuffersVtbl;
interface IReleaseMarshalBuffers {
    CONST_VTBL IReleaseMarshalBuffersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IReleaseMarshalBuffers_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IReleaseMarshalBuffers_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IReleaseMarshalBuffers_Release(This) (This)->lpVtbl->Release(This)
/*** IReleaseMarshalBuffers methods ***/
#define IReleaseMarshalBuffers_ReleaseMarshalBuffer(This,pMsg,dwFlags,pChnl) (This)->lpVtbl->ReleaseMarshalBuffer(This,pMsg,dwFlags,pChnl)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IReleaseMarshalBuffers_QueryInterface(IReleaseMarshalBuffers* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IReleaseMarshalBuffers_AddRef(IReleaseMarshalBuffers* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IReleaseMarshalBuffers_Release(IReleaseMarshalBuffers* This) {
    return This->lpVtbl->Release(This);
}
/*** IReleaseMarshalBuffers methods ***/
static FORCEINLINE HRESULT IReleaseMarshalBuffers_ReleaseMarshalBuffer(IReleaseMarshalBuffers* This,RPCOLEMESSAGE *pMsg,DWORD dwFlags,IUnknown *pChnl) {
    return This->lpVtbl->ReleaseMarshalBuffer(This,pMsg,dwFlags,pChnl);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IReleaseMarshalBuffers_ReleaseMarshalBuffer_Proxy(
    IReleaseMarshalBuffers* This,
    RPCOLEMESSAGE *pMsg,
    DWORD dwFlags,
    IUnknown *pChnl);
void __RPC_STUB IReleaseMarshalBuffers_ReleaseMarshalBuffer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IReleaseMarshalBuffers_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWaitMultiple interface
 */
#ifndef __IWaitMultiple_INTERFACE_DEFINED__
#define __IWaitMultiple_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWaitMultiple, 0x0000002b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000002b-0000-0000-c000-000000000046")
IWaitMultiple : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE WaitMultiple(
        DWORD timeout,
        ISynchronize **pSync) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddSynchronize(
        ISynchronize *pSync) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWaitMultiple, 0x0000002b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IWaitMultipleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWaitMultiple* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWaitMultiple* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWaitMultiple* This);

    /*** IWaitMultiple methods ***/
    HRESULT (STDMETHODCALLTYPE *WaitMultiple)(
        IWaitMultiple* This,
        DWORD timeout,
        ISynchronize **pSync);

    HRESULT (STDMETHODCALLTYPE *AddSynchronize)(
        IWaitMultiple* This,
        ISynchronize *pSync);

    END_INTERFACE
} IWaitMultipleVtbl;
interface IWaitMultiple {
    CONST_VTBL IWaitMultipleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWaitMultiple_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWaitMultiple_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWaitMultiple_Release(This) (This)->lpVtbl->Release(This)
/*** IWaitMultiple methods ***/
#define IWaitMultiple_WaitMultiple(This,timeout,pSync) (This)->lpVtbl->WaitMultiple(This,timeout,pSync)
#define IWaitMultiple_AddSynchronize(This,pSync) (This)->lpVtbl->AddSynchronize(This,pSync)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IWaitMultiple_QueryInterface(IWaitMultiple* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IWaitMultiple_AddRef(IWaitMultiple* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IWaitMultiple_Release(IWaitMultiple* This) {
    return This->lpVtbl->Release(This);
}
/*** IWaitMultiple methods ***/
static FORCEINLINE HRESULT IWaitMultiple_WaitMultiple(IWaitMultiple* This,DWORD timeout,ISynchronize **pSync) {
    return This->lpVtbl->WaitMultiple(This,timeout,pSync);
}
static FORCEINLINE HRESULT IWaitMultiple_AddSynchronize(IWaitMultiple* This,ISynchronize *pSync) {
    return This->lpVtbl->AddSynchronize(This,pSync);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IWaitMultiple_WaitMultiple_Proxy(
    IWaitMultiple* This,
    DWORD timeout,
    ISynchronize **pSync);
void __RPC_STUB IWaitMultiple_WaitMultiple_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWaitMultiple_AddSynchronize_Proxy(
    IWaitMultiple* This,
    ISynchronize *pSync);
void __RPC_STUB IWaitMultiple_AddSynchronize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IWaitMultiple_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAddrTrackingControl interface
 */
#ifndef __IAddrTrackingControl_INTERFACE_DEFINED__
#define __IAddrTrackingControl_INTERFACE_DEFINED__

typedef IAddrTrackingControl *LPADDRTRACKINGCONTROL;
DEFINE_GUID(IID_IAddrTrackingControl, 0x00000147, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000147-0000-0000-c000-000000000046")
IAddrTrackingControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EnableCOMDynamicAddrTracking(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableCOMDynamicAddrTracking(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAddrTrackingControl, 0x00000147, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IAddrTrackingControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAddrTrackingControl* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAddrTrackingControl* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAddrTrackingControl* This);

    /*** IAddrTrackingControl methods ***/
    HRESULT (STDMETHODCALLTYPE *EnableCOMDynamicAddrTracking)(
        IAddrTrackingControl* This);

    HRESULT (STDMETHODCALLTYPE *DisableCOMDynamicAddrTracking)(
        IAddrTrackingControl* This);

    END_INTERFACE
} IAddrTrackingControlVtbl;
interface IAddrTrackingControl {
    CONST_VTBL IAddrTrackingControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAddrTrackingControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAddrTrackingControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAddrTrackingControl_Release(This) (This)->lpVtbl->Release(This)
/*** IAddrTrackingControl methods ***/
#define IAddrTrackingControl_EnableCOMDynamicAddrTracking(This) (This)->lpVtbl->EnableCOMDynamicAddrTracking(This)
#define IAddrTrackingControl_DisableCOMDynamicAddrTracking(This) (This)->lpVtbl->DisableCOMDynamicAddrTracking(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAddrTrackingControl_QueryInterface(IAddrTrackingControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAddrTrackingControl_AddRef(IAddrTrackingControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAddrTrackingControl_Release(IAddrTrackingControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IAddrTrackingControl methods ***/
static FORCEINLINE HRESULT IAddrTrackingControl_EnableCOMDynamicAddrTracking(IAddrTrackingControl* This) {
    return This->lpVtbl->EnableCOMDynamicAddrTracking(This);
}
static FORCEINLINE HRESULT IAddrTrackingControl_DisableCOMDynamicAddrTracking(IAddrTrackingControl* This) {
    return This->lpVtbl->DisableCOMDynamicAddrTracking(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAddrTrackingControl_EnableCOMDynamicAddrTracking_Proxy(
    IAddrTrackingControl* This);
void __RPC_STUB IAddrTrackingControl_EnableCOMDynamicAddrTracking_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAddrTrackingControl_DisableCOMDynamicAddrTracking_Proxy(
    IAddrTrackingControl* This);
void __RPC_STUB IAddrTrackingControl_DisableCOMDynamicAddrTracking_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IAddrTrackingControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAddrExclusionControl interface
 */
#ifndef __IAddrExclusionControl_INTERFACE_DEFINED__
#define __IAddrExclusionControl_INTERFACE_DEFINED__

typedef IAddrExclusionControl *LPADDREXCLUSIONCONTROL;
DEFINE_GUID(IID_IAddrExclusionControl, 0x00000148, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000148-0000-0000-c000-000000000046")
IAddrExclusionControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCurrentAddrExclusionList(
        REFIID riid,
        void **ppEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateAddrExclusionList(
        IUnknown *pEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAddrExclusionControl, 0x00000148, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IAddrExclusionControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAddrExclusionControl* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAddrExclusionControl* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAddrExclusionControl* This);

    /*** IAddrExclusionControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCurrentAddrExclusionList)(
        IAddrExclusionControl* This,
        REFIID riid,
        void **ppEnumerator);

    HRESULT (STDMETHODCALLTYPE *UpdateAddrExclusionList)(
        IAddrExclusionControl* This,
        IUnknown *pEnumerator);

    END_INTERFACE
} IAddrExclusionControlVtbl;
interface IAddrExclusionControl {
    CONST_VTBL IAddrExclusionControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAddrExclusionControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAddrExclusionControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAddrExclusionControl_Release(This) (This)->lpVtbl->Release(This)
/*** IAddrExclusionControl methods ***/
#define IAddrExclusionControl_GetCurrentAddrExclusionList(This,riid,ppEnumerator) (This)->lpVtbl->GetCurrentAddrExclusionList(This,riid,ppEnumerator)
#define IAddrExclusionControl_UpdateAddrExclusionList(This,pEnumerator) (This)->lpVtbl->UpdateAddrExclusionList(This,pEnumerator)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAddrExclusionControl_QueryInterface(IAddrExclusionControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAddrExclusionControl_AddRef(IAddrExclusionControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAddrExclusionControl_Release(IAddrExclusionControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IAddrExclusionControl methods ***/
static FORCEINLINE HRESULT IAddrExclusionControl_GetCurrentAddrExclusionList(IAddrExclusionControl* This,REFIID riid,void **ppEnumerator) {
    return This->lpVtbl->GetCurrentAddrExclusionList(This,riid,ppEnumerator);
}
static FORCEINLINE HRESULT IAddrExclusionControl_UpdateAddrExclusionList(IAddrExclusionControl* This,IUnknown *pEnumerator) {
    return This->lpVtbl->UpdateAddrExclusionList(This,pEnumerator);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAddrExclusionControl_GetCurrentAddrExclusionList_Proxy(
    IAddrExclusionControl* This,
    REFIID riid,
    void **ppEnumerator);
void __RPC_STUB IAddrExclusionControl_GetCurrentAddrExclusionList_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAddrExclusionControl_UpdateAddrExclusionList_Proxy(
    IAddrExclusionControl* This,
    IUnknown *pEnumerator);
void __RPC_STUB IAddrExclusionControl_UpdateAddrExclusionList_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IAddrExclusionControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPipeByte interface
 */
#ifndef __IPipeByte_INTERFACE_DEFINED__
#define __IPipeByte_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPipeByte, 0xdb2f3aca, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db2f3aca-2f86-11d1-8e04-00c04fb9989a")
IPipeByte : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Pull(
        BYTE *buf,
        ULONG cRequest,
        ULONG *pcReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE Push(
        BYTE *buf,
        ULONG cSent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPipeByte, 0xdb2f3aca, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a)
#endif
#else
typedef struct IPipeByteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPipeByte* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPipeByte* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPipeByte* This);

    /*** IPipeByte methods ***/
    HRESULT (STDMETHODCALLTYPE *Pull)(
        IPipeByte* This,
        BYTE *buf,
        ULONG cRequest,
        ULONG *pcReturned);

    HRESULT (STDMETHODCALLTYPE *Push)(
        IPipeByte* This,
        BYTE *buf,
        ULONG cSent);

    END_INTERFACE
} IPipeByteVtbl;
interface IPipeByte {
    CONST_VTBL IPipeByteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPipeByte_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPipeByte_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPipeByte_Release(This) (This)->lpVtbl->Release(This)
/*** IPipeByte methods ***/
#define IPipeByte_Pull(This,buf,cRequest,pcReturned) (This)->lpVtbl->Pull(This,buf,cRequest,pcReturned)
#define IPipeByte_Push(This,buf,cSent) (This)->lpVtbl->Push(This,buf,cSent)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPipeByte_QueryInterface(IPipeByte* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPipeByte_AddRef(IPipeByte* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPipeByte_Release(IPipeByte* This) {
    return This->lpVtbl->Release(This);
}
/*** IPipeByte methods ***/
static FORCEINLINE HRESULT IPipeByte_Pull(IPipeByte* This,BYTE *buf,ULONG cRequest,ULONG *pcReturned) {
    return This->lpVtbl->Pull(This,buf,cRequest,pcReturned);
}
static FORCEINLINE HRESULT IPipeByte_Push(IPipeByte* This,BYTE *buf,ULONG cSent) {
    return This->lpVtbl->Push(This,buf,cSent);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPipeByte_Pull_Proxy(
    IPipeByte* This,
    BYTE *buf,
    ULONG cRequest,
    ULONG *pcReturned);
void __RPC_STUB IPipeByte_Pull_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPipeByte_Push_Proxy(
    IPipeByte* This,
    BYTE *buf,
    ULONG cSent);
void __RPC_STUB IPipeByte_Push_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPipeByte_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPipeLong interface
 */
#ifndef __IPipeLong_INTERFACE_DEFINED__
#define __IPipeLong_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPipeLong, 0xdb2f3acc, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db2f3acc-2f86-11d1-8e04-00c04fb9989a")
IPipeLong : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Pull(
        LONG *buf,
        ULONG cRequest,
        ULONG *pcReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE Push(
        LONG *buf,
        ULONG cSent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPipeLong, 0xdb2f3acc, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a)
#endif
#else
typedef struct IPipeLongVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPipeLong* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPipeLong* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPipeLong* This);

    /*** IPipeLong methods ***/
    HRESULT (STDMETHODCALLTYPE *Pull)(
        IPipeLong* This,
        LONG *buf,
        ULONG cRequest,
        ULONG *pcReturned);

    HRESULT (STDMETHODCALLTYPE *Push)(
        IPipeLong* This,
        LONG *buf,
        ULONG cSent);

    END_INTERFACE
} IPipeLongVtbl;
interface IPipeLong {
    CONST_VTBL IPipeLongVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPipeLong_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPipeLong_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPipeLong_Release(This) (This)->lpVtbl->Release(This)
/*** IPipeLong methods ***/
#define IPipeLong_Pull(This,buf,cRequest,pcReturned) (This)->lpVtbl->Pull(This,buf,cRequest,pcReturned)
#define IPipeLong_Push(This,buf,cSent) (This)->lpVtbl->Push(This,buf,cSent)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPipeLong_QueryInterface(IPipeLong* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPipeLong_AddRef(IPipeLong* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPipeLong_Release(IPipeLong* This) {
    return This->lpVtbl->Release(This);
}
/*** IPipeLong methods ***/
static FORCEINLINE HRESULT IPipeLong_Pull(IPipeLong* This,LONG *buf,ULONG cRequest,ULONG *pcReturned) {
    return This->lpVtbl->Pull(This,buf,cRequest,pcReturned);
}
static FORCEINLINE HRESULT IPipeLong_Push(IPipeLong* This,LONG *buf,ULONG cSent) {
    return This->lpVtbl->Push(This,buf,cSent);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPipeLong_Pull_Proxy(
    IPipeLong* This,
    LONG *buf,
    ULONG cRequest,
    ULONG *pcReturned);
void __RPC_STUB IPipeLong_Pull_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPipeLong_Push_Proxy(
    IPipeLong* This,
    LONG *buf,
    ULONG cSent);
void __RPC_STUB IPipeLong_Push_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPipeLong_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPipeDouble interface
 */
#ifndef __IPipeDouble_INTERFACE_DEFINED__
#define __IPipeDouble_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPipeDouble, 0xdb2f3ace, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db2f3ace-2f86-11d1-8e04-00c04fb9989a")
IPipeDouble : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Pull(
        DOUBLE *buf,
        ULONG cRequest,
        ULONG *pcReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE Push(
        DOUBLE *buf,
        ULONG cSent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPipeDouble, 0xdb2f3ace, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a)
#endif
#else
typedef struct IPipeDoubleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPipeDouble* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPipeDouble* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPipeDouble* This);

    /*** IPipeDouble methods ***/
    HRESULT (STDMETHODCALLTYPE *Pull)(
        IPipeDouble* This,
        DOUBLE *buf,
        ULONG cRequest,
        ULONG *pcReturned);

    HRESULT (STDMETHODCALLTYPE *Push)(
        IPipeDouble* This,
        DOUBLE *buf,
        ULONG cSent);

    END_INTERFACE
} IPipeDoubleVtbl;
interface IPipeDouble {
    CONST_VTBL IPipeDoubleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPipeDouble_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPipeDouble_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPipeDouble_Release(This) (This)->lpVtbl->Release(This)
/*** IPipeDouble methods ***/
#define IPipeDouble_Pull(This,buf,cRequest,pcReturned) (This)->lpVtbl->Pull(This,buf,cRequest,pcReturned)
#define IPipeDouble_Push(This,buf,cSent) (This)->lpVtbl->Push(This,buf,cSent)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPipeDouble_QueryInterface(IPipeDouble* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPipeDouble_AddRef(IPipeDouble* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPipeDouble_Release(IPipeDouble* This) {
    return This->lpVtbl->Release(This);
}
/*** IPipeDouble methods ***/
static FORCEINLINE HRESULT IPipeDouble_Pull(IPipeDouble* This,DOUBLE *buf,ULONG cRequest,ULONG *pcReturned) {
    return This->lpVtbl->Pull(This,buf,cRequest,pcReturned);
}
static FORCEINLINE HRESULT IPipeDouble_Push(IPipeDouble* This,DOUBLE *buf,ULONG cSent) {
    return This->lpVtbl->Push(This,buf,cSent);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPipeDouble_Pull_Proxy(
    IPipeDouble* This,
    DOUBLE *buf,
    ULONG cRequest,
    ULONG *pcReturned);
void __RPC_STUB IPipeDouble_Pull_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPipeDouble_Push_Proxy(
    IPipeDouble* This,
    DOUBLE *buf,
    ULONG cSent);
void __RPC_STUB IPipeDouble_Push_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPipeDouble_INTERFACE_DEFINED__ */


#if defined USE_COM_CONTEXT_DEF || defined BUILDTYPE_COMSVCS || defined _COMBASEAPI_ || defined _OLE32_

typedef DWORD CPFLAGS;

typedef struct tagContextProperty {
    GUID policyId;
    CPFLAGS flags;
    IUnknown *pUnk;
} ContextProperty;

/*****************************************************************************
 * IEnumContextProps interface
 */
#ifndef __IEnumContextProps_INTERFACE_DEFINED__
#define __IEnumContextProps_INTERFACE_DEFINED__

typedef IEnumContextProps *LPENUMCONTEXTPROPS;

DEFINE_GUID(IID_IEnumContextProps, 0x000001c1, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001c1-0000-0000-c000-000000000046")
IEnumContextProps : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        ContextProperty *pContextProperties,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumContextProps **ppEnumContextProps) = 0;

    virtual HRESULT STDMETHODCALLTYPE Count(
        ULONG *pcelt) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumContextProps, 0x000001c1, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumContextPropsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumContextProps* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumContextProps* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumContextProps* This);

    /*** IEnumContextProps methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumContextProps* This,
        ULONG celt,
        ContextProperty *pContextProperties,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumContextProps* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumContextProps* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumContextProps* This,
        IEnumContextProps **ppEnumContextProps);

    HRESULT (STDMETHODCALLTYPE *Count)(
        IEnumContextProps* This,
        ULONG *pcelt);

    END_INTERFACE
} IEnumContextPropsVtbl;
interface IEnumContextProps {
    CONST_VTBL IEnumContextPropsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumContextProps_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumContextProps_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumContextProps_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumContextProps methods ***/
#define IEnumContextProps_Next(This,celt,pContextProperties,pceltFetched) (This)->lpVtbl->Next(This,celt,pContextProperties,pceltFetched)
#define IEnumContextProps_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumContextProps_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumContextProps_Clone(This,ppEnumContextProps) (This)->lpVtbl->Clone(This,ppEnumContextProps)
#define IEnumContextProps_Count(This,pcelt) (This)->lpVtbl->Count(This,pcelt)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumContextProps_QueryInterface(IEnumContextProps* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumContextProps_AddRef(IEnumContextProps* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumContextProps_Release(IEnumContextProps* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumContextProps methods ***/
static FORCEINLINE HRESULT IEnumContextProps_Next(IEnumContextProps* This,ULONG celt,ContextProperty *pContextProperties,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,pContextProperties,pceltFetched);
}
static FORCEINLINE HRESULT IEnumContextProps_Skip(IEnumContextProps* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumContextProps_Reset(IEnumContextProps* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumContextProps_Clone(IEnumContextProps* This,IEnumContextProps **ppEnumContextProps) {
    return This->lpVtbl->Clone(This,ppEnumContextProps);
}
static FORCEINLINE HRESULT IEnumContextProps_Count(IEnumContextProps* This,ULONG *pcelt) {
    return This->lpVtbl->Count(This,pcelt);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumContextProps_Next_Proxy(
    IEnumContextProps* This,
    ULONG celt,
    ContextProperty *pContextProperties,
    ULONG *pceltFetched);
void __RPC_STUB IEnumContextProps_Next_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumContextProps_Skip_Proxy(
    IEnumContextProps* This,
    ULONG celt);
void __RPC_STUB IEnumContextProps_Skip_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumContextProps_Reset_Proxy(
    IEnumContextProps* This);
void __RPC_STUB IEnumContextProps_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumContextProps_Clone_Proxy(
    IEnumContextProps* This,
    IEnumContextProps **ppEnumContextProps);
void __RPC_STUB IEnumContextProps_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumContextProps_Count_Proxy(
    IEnumContextProps* This,
    ULONG *pcelt);
void __RPC_STUB IEnumContextProps_Count_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IEnumContextProps_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IContext interface
 */
#ifndef __IContext_INTERFACE_DEFINED__
#define __IContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_IContext, 0x000001c0, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001c0-0000-0000-c000-000000000046")
IContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetProperty(
        REFGUID rpolicyId,
        CPFLAGS flags,
        IUnknown *pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveProperty(
        REFGUID rPolicyId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        REFGUID rGuid,
        CPFLAGS *pFlags,
        IUnknown **ppUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumContextProps(
        IEnumContextProps **ppEnumContextProps) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IContext, 0x000001c0, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IContext* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IContext* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IContext* This);

    /*** IContext methods ***/
    HRESULT (STDMETHODCALLTYPE *SetProperty)(
        IContext* This,
        REFGUID rpolicyId,
        CPFLAGS flags,
        IUnknown *pUnk);

    HRESULT (STDMETHODCALLTYPE *RemoveProperty)(
        IContext* This,
        REFGUID rPolicyId);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IContext* This,
        REFGUID rGuid,
        CPFLAGS *pFlags,
        IUnknown **ppUnk);

    HRESULT (STDMETHODCALLTYPE *EnumContextProps)(
        IContext* This,
        IEnumContextProps **ppEnumContextProps);

    END_INTERFACE
} IContextVtbl;
interface IContext {
    CONST_VTBL IContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IContext_Release(This) (This)->lpVtbl->Release(This)
/*** IContext methods ***/
#define IContext_SetProperty(This,rpolicyId,flags,pUnk) (This)->lpVtbl->SetProperty(This,rpolicyId,flags,pUnk)
#define IContext_RemoveProperty(This,rPolicyId) (This)->lpVtbl->RemoveProperty(This,rPolicyId)
#define IContext_GetProperty(This,rGuid,pFlags,ppUnk) (This)->lpVtbl->GetProperty(This,rGuid,pFlags,ppUnk)
#define IContext_EnumContextProps(This,ppEnumContextProps) (This)->lpVtbl->EnumContextProps(This,ppEnumContextProps)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IContext_QueryInterface(IContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IContext_AddRef(IContext* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IContext_Release(IContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IContext methods ***/
static FORCEINLINE HRESULT IContext_SetProperty(IContext* This,REFGUID rpolicyId,CPFLAGS flags,IUnknown *pUnk) {
    return This->lpVtbl->SetProperty(This,rpolicyId,flags,pUnk);
}
static FORCEINLINE HRESULT IContext_RemoveProperty(IContext* This,REFGUID rPolicyId) {
    return This->lpVtbl->RemoveProperty(This,rPolicyId);
}
static FORCEINLINE HRESULT IContext_GetProperty(IContext* This,REFGUID rGuid,CPFLAGS *pFlags,IUnknown **ppUnk) {
    return This->lpVtbl->GetProperty(This,rGuid,pFlags,ppUnk);
}
static FORCEINLINE HRESULT IContext_EnumContextProps(IContext* This,IEnumContextProps **ppEnumContextProps) {
    return This->lpVtbl->EnumContextProps(This,ppEnumContextProps);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IContext_SetProperty_Proxy(
    IContext* This,
    REFGUID rpolicyId,
    CPFLAGS flags,
    IUnknown *pUnk);
void __RPC_STUB IContext_SetProperty_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IContext_RemoveProperty_Proxy(
    IContext* This,
    REFGUID rPolicyId);
void __RPC_STUB IContext_RemoveProperty_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IContext_GetProperty_Proxy(
    IContext* This,
    REFGUID rGuid,
    CPFLAGS *pFlags,
    IUnknown **ppUnk);
void __RPC_STUB IContext_GetProperty_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IContext_EnumContextProps_Proxy(
    IContext* This,
    IEnumContextProps **ppEnumContextProps);
void __RPC_STUB IContext_EnumContextProps_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IContext_INTERFACE_DEFINED__ */

#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef enum _APTTYPEQUALIFIER {
    APTTYPEQUALIFIER_NONE = 0,
    APTTYPEQUALIFIER_IMPLICIT_MTA = 1,
    APTTYPEQUALIFIER_NA_ON_MTA = 2,
    APTTYPEQUALIFIER_NA_ON_STA = 3,
    APTTYPEQUALIFIER_NA_ON_IMPLICIT_MTA = 4,
    APTTYPEQUALIFIER_NA_ON_MAINSTA = 5,
    APTTYPEQUALIFIER_APPLICATION_STA = 6
} APTTYPEQUALIFIER;

typedef enum _APTTYPE {
    APTTYPE_CURRENT = -1,
    APTTYPE_STA = 0,
    APTTYPE_MTA = 1,
    APTTYPE_NA = 2,
    APTTYPE_MAINSTA = 3
} APTTYPE;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef enum _THDTYPE {
    THDTYPE_BLOCKMESSAGES = 0,
    THDTYPE_PROCESSMESSAGES = 1
} THDTYPE;

typedef DWORD APARTMENTID;

/*****************************************************************************
 * IComThreadingInfo interface
 */
#ifndef __IComThreadingInfo_INTERFACE_DEFINED__
#define __IComThreadingInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IComThreadingInfo, 0x000001ce, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001ce-0000-0000-c000-000000000046")
IComThreadingInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCurrentApartmentType(
        APTTYPE *pAptType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentThreadType(
        THDTYPE *pThreadType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentLogicalThreadId(
        GUID *pguidLogicalThreadId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentLogicalThreadId(
        REFGUID rguid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IComThreadingInfo, 0x000001ce, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IComThreadingInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IComThreadingInfo* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IComThreadingInfo* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IComThreadingInfo* This);

    /*** IComThreadingInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCurrentApartmentType)(
        IComThreadingInfo* This,
        APTTYPE *pAptType);

    HRESULT (STDMETHODCALLTYPE *GetCurrentThreadType)(
        IComThreadingInfo* This,
        THDTYPE *pThreadType);

    HRESULT (STDMETHODCALLTYPE *GetCurrentLogicalThreadId)(
        IComThreadingInfo* This,
        GUID *pguidLogicalThreadId);

    HRESULT (STDMETHODCALLTYPE *SetCurrentLogicalThreadId)(
        IComThreadingInfo* This,
        REFGUID rguid);

    END_INTERFACE
} IComThreadingInfoVtbl;
interface IComThreadingInfo {
    CONST_VTBL IComThreadingInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IComThreadingInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IComThreadingInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IComThreadingInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IComThreadingInfo methods ***/
#define IComThreadingInfo_GetCurrentApartmentType(This,pAptType) (This)->lpVtbl->GetCurrentApartmentType(This,pAptType)
#define IComThreadingInfo_GetCurrentThreadType(This,pThreadType) (This)->lpVtbl->GetCurrentThreadType(This,pThreadType)
#define IComThreadingInfo_GetCurrentLogicalThreadId(This,pguidLogicalThreadId) (This)->lpVtbl->GetCurrentLogicalThreadId(This,pguidLogicalThreadId)
#define IComThreadingInfo_SetCurrentLogicalThreadId(This,rguid) (This)->lpVtbl->SetCurrentLogicalThreadId(This,rguid)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IComThreadingInfo_QueryInterface(IComThreadingInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IComThreadingInfo_AddRef(IComThreadingInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IComThreadingInfo_Release(IComThreadingInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IComThreadingInfo methods ***/
static FORCEINLINE HRESULT IComThreadingInfo_GetCurrentApartmentType(IComThreadingInfo* This,APTTYPE *pAptType) {
    return This->lpVtbl->GetCurrentApartmentType(This,pAptType);
}
static FORCEINLINE HRESULT IComThreadingInfo_GetCurrentThreadType(IComThreadingInfo* This,THDTYPE *pThreadType) {
    return This->lpVtbl->GetCurrentThreadType(This,pThreadType);
}
static FORCEINLINE HRESULT IComThreadingInfo_GetCurrentLogicalThreadId(IComThreadingInfo* This,GUID *pguidLogicalThreadId) {
    return This->lpVtbl->GetCurrentLogicalThreadId(This,pguidLogicalThreadId);
}
static FORCEINLINE HRESULT IComThreadingInfo_SetCurrentLogicalThreadId(IComThreadingInfo* This,REFGUID rguid) {
    return This->lpVtbl->SetCurrentLogicalThreadId(This,rguid);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IComThreadingInfo_GetCurrentApartmentType_Proxy(
    IComThreadingInfo* This,
    APTTYPE *pAptType);
void __RPC_STUB IComThreadingInfo_GetCurrentApartmentType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IComThreadingInfo_GetCurrentThreadType_Proxy(
    IComThreadingInfo* This,
    THDTYPE *pThreadType);
void __RPC_STUB IComThreadingInfo_GetCurrentThreadType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IComThreadingInfo_GetCurrentLogicalThreadId_Proxy(
    IComThreadingInfo* This,
    GUID *pguidLogicalThreadId);
void __RPC_STUB IComThreadingInfo_GetCurrentLogicalThreadId_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IComThreadingInfo_SetCurrentLogicalThreadId_Proxy(
    IComThreadingInfo* This,
    REFGUID rguid);
void __RPC_STUB IComThreadingInfo_SetCurrentLogicalThreadId_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IComThreadingInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IProcessInitControl interface
 */
#ifndef __IProcessInitControl_INTERFACE_DEFINED__
#define __IProcessInitControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProcessInitControl, 0x72380d55, 0x8d2b, 0x43a3, 0x85,0x13, 0x2b,0x6e,0xf3,0x14,0x34,0xe9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72380d55-8d2b-43a3-8513-2b6ef31434e9")
IProcessInitControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ResetInitializerTimeout(
        DWORD dwSecondsRemaining) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProcessInitControl, 0x72380d55, 0x8d2b, 0x43a3, 0x85,0x13, 0x2b,0x6e,0xf3,0x14,0x34,0xe9)
#endif
#else
typedef struct IProcessInitControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProcessInitControl* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProcessInitControl* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProcessInitControl* This);

    /*** IProcessInitControl methods ***/
    HRESULT (STDMETHODCALLTYPE *ResetInitializerTimeout)(
        IProcessInitControl* This,
        DWORD dwSecondsRemaining);

    END_INTERFACE
} IProcessInitControlVtbl;
interface IProcessInitControl {
    CONST_VTBL IProcessInitControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProcessInitControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProcessInitControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProcessInitControl_Release(This) (This)->lpVtbl->Release(This)
/*** IProcessInitControl methods ***/
#define IProcessInitControl_ResetInitializerTimeout(This,dwSecondsRemaining) (This)->lpVtbl->ResetInitializerTimeout(This,dwSecondsRemaining)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IProcessInitControl_QueryInterface(IProcessInitControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IProcessInitControl_AddRef(IProcessInitControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IProcessInitControl_Release(IProcessInitControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IProcessInitControl methods ***/
static FORCEINLINE HRESULT IProcessInitControl_ResetInitializerTimeout(IProcessInitControl* This,DWORD dwSecondsRemaining) {
    return This->lpVtbl->ResetInitializerTimeout(This,dwSecondsRemaining);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IProcessInitControl_ResetInitializerTimeout_Proxy(
    IProcessInitControl* This,
    DWORD dwSecondsRemaining);
void __RPC_STUB IProcessInitControl_ResetInitializerTimeout_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IProcessInitControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IFastRundown interface
 */
#ifndef __IFastRundown_INTERFACE_DEFINED__
#define __IFastRundown_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFastRundown, 0x00000040, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000040-0000-0000-c000-000000000046")
IFastRundown : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFastRundown, 0x00000040, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IFastRundownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFastRundown* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFastRundown* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFastRundown* This);

    END_INTERFACE
} IFastRundownVtbl;
interface IFastRundown {
    CONST_VTBL IFastRundownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFastRundown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFastRundown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFastRundown_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFastRundown_QueryInterface(IFastRundown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFastRundown_AddRef(IFastRundown* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFastRundown_Release(IFastRundown* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IFastRundown_INTERFACE_DEFINED__ */


typedef enum CO_MARSHALING_CONTEXT_ATTRIBUTES {
    CO_MARSHALING_SOURCE_IS_APP_CONTAINER = 0
} CO_MARSHALING_CONTEXT_ATTRIBUTES;

/*****************************************************************************
 * IMarshalingStream interface
 */
#ifndef __IMarshalingStream_INTERFACE_DEFINED__
#define __IMarshalingStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMarshalingStream, 0xd8f2f5e6, 0x6102, 0x4863, 0x9f,0x26, 0x38,0x9a,0x46,0x76,0xef,0xde);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d8f2f5e6-6102-4863-9f26-389a4676efde")
IMarshalingStream : public IStream
{
    virtual HRESULT STDMETHODCALLTYPE GetMarshalingContextAttribute(
        CO_MARSHALING_CONTEXT_ATTRIBUTES attribute,
        ULONG_PTR *pAttributeValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMarshalingStream, 0xd8f2f5e6, 0x6102, 0x4863, 0x9f,0x26, 0x38,0x9a,0x46,0x76,0xef,0xde)
#endif
#else
typedef struct IMarshalingStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMarshalingStream* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMarshalingStream* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMarshalingStream* This);

    /*** ISequentialStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IMarshalingStream* This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IMarshalingStream* This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    /*** IStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Seek)(
        IMarshalingStream* This,
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition);

    HRESULT (STDMETHODCALLTYPE *SetSize)(
        IMarshalingStream* This,
        ULARGE_INTEGER libNewSize);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        IMarshalingStream* This,
        IStream *pstm,
        ULARGE_INTEGER cb,
        ULARGE_INTEGER *pcbRead,
        ULARGE_INTEGER *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IMarshalingStream* This,
        DWORD grfCommitFlags);

    HRESULT (STDMETHODCALLTYPE *Revert)(
        IMarshalingStream* This);

    HRESULT (STDMETHODCALLTYPE *LockRegion)(
        IMarshalingStream* This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *UnlockRegion)(
        IMarshalingStream* This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *Stat)(
        IMarshalingStream* This,
        STATSTG *pstatstg,
        DWORD grfStatFlag);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IMarshalingStream* This,
        IStream **ppstm);

    /*** IMarshalingStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMarshalingContextAttribute)(
        IMarshalingStream* This,
        CO_MARSHALING_CONTEXT_ATTRIBUTES attribute,
        ULONG_PTR *pAttributeValue);

    END_INTERFACE
} IMarshalingStreamVtbl;
interface IMarshalingStream {
    CONST_VTBL IMarshalingStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMarshalingStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMarshalingStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMarshalingStream_Release(This) (This)->lpVtbl->Release(This)
/*** ISequentialStream methods ***/
#define IMarshalingStream_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define IMarshalingStream_Write(This,pv,cb,pcbWritten) (This)->lpVtbl->Write(This,pv,cb,pcbWritten)
/*** IStream methods ***/
#define IMarshalingStream_Seek(This,dlibMove,dwOrigin,plibNewPosition) (This)->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition)
#define IMarshalingStream_SetSize(This,libNewSize) (This)->lpVtbl->SetSize(This,libNewSize)
#define IMarshalingStream_CopyTo(This,pstm,cb,pcbRead,pcbWritten) (This)->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten)
#define IMarshalingStream_Commit(This,grfCommitFlags) (This)->lpVtbl->Commit(This,grfCommitFlags)
#define IMarshalingStream_Revert(This) (This)->lpVtbl->Revert(This)
#define IMarshalingStream_LockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->LockRegion(This,libOffset,cb,dwLockType)
#define IMarshalingStream_UnlockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType)
#define IMarshalingStream_Stat(This,pstatstg,grfStatFlag) (This)->lpVtbl->Stat(This,pstatstg,grfStatFlag)
#define IMarshalingStream_Clone(This,ppstm) (This)->lpVtbl->Clone(This,ppstm)
/*** IMarshalingStream methods ***/
#define IMarshalingStream_GetMarshalingContextAttribute(This,attribute,pAttributeValue) (This)->lpVtbl->GetMarshalingContextAttribute(This,attribute,pAttributeValue)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMarshalingStream_QueryInterface(IMarshalingStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMarshalingStream_AddRef(IMarshalingStream* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMarshalingStream_Release(IMarshalingStream* This) {
    return This->lpVtbl->Release(This);
}
/*** ISequentialStream methods ***/
static FORCEINLINE HRESULT IMarshalingStream_Read(IMarshalingStream* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static FORCEINLINE HRESULT IMarshalingStream_Write(IMarshalingStream* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pv,cb,pcbWritten);
}
/*** IStream methods ***/
static FORCEINLINE HRESULT IMarshalingStream_Seek(IMarshalingStream* This,LARGE_INTEGER dlibMove,DWORD dwOrigin,ULARGE_INTEGER *plibNewPosition) {
    return This->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition);
}
static FORCEINLINE HRESULT IMarshalingStream_SetSize(IMarshalingStream* This,ULARGE_INTEGER libNewSize) {
    return This->lpVtbl->SetSize(This,libNewSize);
}
static FORCEINLINE HRESULT IMarshalingStream_CopyTo(IMarshalingStream* This,IStream *pstm,ULARGE_INTEGER cb,ULARGE_INTEGER *pcbRead,ULARGE_INTEGER *pcbWritten) {
    return This->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten);
}
static FORCEINLINE HRESULT IMarshalingStream_Commit(IMarshalingStream* This,DWORD grfCommitFlags) {
    return This->lpVtbl->Commit(This,grfCommitFlags);
}
static FORCEINLINE HRESULT IMarshalingStream_Revert(IMarshalingStream* This) {
    return This->lpVtbl->Revert(This);
}
static FORCEINLINE HRESULT IMarshalingStream_LockRegion(IMarshalingStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->LockRegion(This,libOffset,cb,dwLockType);
}
static FORCEINLINE HRESULT IMarshalingStream_UnlockRegion(IMarshalingStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType);
}
static FORCEINLINE HRESULT IMarshalingStream_Stat(IMarshalingStream* This,STATSTG *pstatstg,DWORD grfStatFlag) {
    return This->lpVtbl->Stat(This,pstatstg,grfStatFlag);
}
static FORCEINLINE HRESULT IMarshalingStream_Clone(IMarshalingStream* This,IStream **ppstm) {
    return This->lpVtbl->Clone(This,ppstm);
}
/*** IMarshalingStream methods ***/
static FORCEINLINE HRESULT IMarshalingStream_GetMarshalingContextAttribute(IMarshalingStream* This,CO_MARSHALING_CONTEXT_ATTRIBUTES attribute,ULONG_PTR *pAttributeValue) {
    return This->lpVtbl->GetMarshalingContextAttribute(This,attribute,pAttributeValue);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMarshalingStream_GetMarshalingContextAttribute_Proxy(
    IMarshalingStream* This,
    CO_MARSHALING_CONTEXT_ATTRIBUTES attribute,
    ULONG_PTR *pAttributeValue);
void __RPC_STUB IMarshalingStream_GetMarshalingContextAttribute_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMarshalingStream_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
EXTERN_C const GUID  IID_ICallbackWithNoReentrancyToApplicationSTA;
#endif
#define _OBJIDLBASE_
#endif
#ifndef __IMoniker_FWD_DEFINED__
#define __IMoniker_FWD_DEFINED__
typedef interface IMoniker IMoniker;
#endif

#ifndef __IEnumMoniker_FWD_DEFINED__
#define __IEnumMoniker_FWD_DEFINED__
typedef interface IEnumMoniker IEnumMoniker;
#endif

#ifndef __IRunningObjectTable_FWD_DEFINED__
#define __IRunningObjectTable_FWD_DEFINED__
typedef interface IRunningObjectTable IRunningObjectTable;
#endif

#ifndef __IStorage_FWD_DEFINED__
#define __IStorage_FWD_DEFINED__
typedef interface IStorage IStorage;
#endif

#ifndef __IEnumSTATSTG_FWD_DEFINED__
#define __IEnumSTATSTG_FWD_DEFINED__
typedef interface IEnumSTATSTG IEnumSTATSTG;
#endif

#ifndef __IAdviseSink_FWD_DEFINED__
#define __IAdviseSink_FWD_DEFINED__
typedef interface IAdviseSink IAdviseSink;
#endif

#ifndef __AsyncIAdviseSink_FWD_DEFINED__
#define __AsyncIAdviseSink_FWD_DEFINED__
typedef interface AsyncIAdviseSink AsyncIAdviseSink;
#endif

#ifndef __IBindCtx_FWD_DEFINED__
#define __IBindCtx_FWD_DEFINED__
typedef interface IBindCtx IBindCtx;
#endif

#ifndef __IEnumMoniker_FWD_DEFINED__
#define __IEnumMoniker_FWD_DEFINED__
typedef interface IEnumMoniker IEnumMoniker;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IMallocSpy interface
 */
#ifndef __IMallocSpy_INTERFACE_DEFINED__
#define __IMallocSpy_INTERFACE_DEFINED__

typedef IMallocSpy *LPMALLOCSPY;
DEFINE_GUID(IID_IMallocSpy, 0x0000001d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000001d-0000-0000-c000-000000000046")
IMallocSpy : public IUnknown
{
    virtual SIZE_T STDMETHODCALLTYPE PreAlloc(
        SIZE_T cbRequest) = 0;

    virtual void * STDMETHODCALLTYPE PostAlloc(
        void *pActual) = 0;

    virtual void * STDMETHODCALLTYPE PreFree(
        void *pRequest,
        WINBOOL fSpyed) = 0;

    virtual void STDMETHODCALLTYPE PostFree(
        WINBOOL fSpyed) = 0;

    virtual SIZE_T STDMETHODCALLTYPE PreRealloc(
        void *pRequest,
        SIZE_T cbRequest,
        void **ppNewRequest,
        WINBOOL fSpyed) = 0;

    virtual void * STDMETHODCALLTYPE PostRealloc(
        void *pActual,
        WINBOOL fSpyed) = 0;

    virtual void * STDMETHODCALLTYPE PreGetSize(
        void *pRequest,
        WINBOOL fSpyed) = 0;

    virtual SIZE_T STDMETHODCALLTYPE PostGetSize(
        SIZE_T cbActual,
        WINBOOL fSpyed) = 0;

    virtual void * STDMETHODCALLTYPE PreDidAlloc(
        void *pRequest,
        WINBOOL fSpyed) = 0;

    virtual int STDMETHODCALLTYPE PostDidAlloc(
        void *pRequest,
        WINBOOL fSpyed,
        int fActual) = 0;

    virtual void STDMETHODCALLTYPE PreHeapMinimize(
        ) = 0;

    virtual void STDMETHODCALLTYPE PostHeapMinimize(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMallocSpy, 0x0000001d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMallocSpyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMallocSpy* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMallocSpy* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMallocSpy* This);

    /*** IMallocSpy methods ***/
    SIZE_T (STDMETHODCALLTYPE *PreAlloc)(
        IMallocSpy* This,
        SIZE_T cbRequest);

    void * (STDMETHODCALLTYPE *PostAlloc)(
        IMallocSpy* This,
        void *pActual);

    void * (STDMETHODCALLTYPE *PreFree)(
        IMallocSpy* This,
        void *pRequest,
        WINBOOL fSpyed);

    void (STDMETHODCALLTYPE *PostFree)(
        IMallocSpy* This,
        WINBOOL fSpyed);

    SIZE_T (STDMETHODCALLTYPE *PreRealloc)(
        IMallocSpy* This,
        void *pRequest,
        SIZE_T cbRequest,
        void **ppNewRequest,
        WINBOOL fSpyed);

    void * (STDMETHODCALLTYPE *PostRealloc)(
        IMallocSpy* This,
        void *pActual,
        WINBOOL fSpyed);

    void * (STDMETHODCALLTYPE *PreGetSize)(
        IMallocSpy* This,
        void *pRequest,
        WINBOOL fSpyed);

    SIZE_T (STDMETHODCALLTYPE *PostGetSize)(
        IMallocSpy* This,
        SIZE_T cbActual,
        WINBOOL fSpyed);

    void * (STDMETHODCALLTYPE *PreDidAlloc)(
        IMallocSpy* This,
        void *pRequest,
        WINBOOL fSpyed);

    int (STDMETHODCALLTYPE *PostDidAlloc)(
        IMallocSpy* This,
        void *pRequest,
        WINBOOL fSpyed,
        int fActual);

    void (STDMETHODCALLTYPE *PreHeapMinimize)(
        IMallocSpy* This);

    void (STDMETHODCALLTYPE *PostHeapMinimize)(
        IMallocSpy* This);

    END_INTERFACE
} IMallocSpyVtbl;
interface IMallocSpy {
    CONST_VTBL IMallocSpyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMallocSpy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMallocSpy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMallocSpy_Release(This) (This)->lpVtbl->Release(This)
/*** IMallocSpy methods ***/
#define IMallocSpy_PreAlloc(This,cbRequest) (This)->lpVtbl->PreAlloc(This,cbRequest)
#define IMallocSpy_PostAlloc(This,pActual) (This)->lpVtbl->PostAlloc(This,pActual)
#define IMallocSpy_PreFree(This,pRequest,fSpyed) (This)->lpVtbl->PreFree(This,pRequest,fSpyed)
#define IMallocSpy_PostFree(This,fSpyed) (This)->lpVtbl->PostFree(This,fSpyed)
#define IMallocSpy_PreRealloc(This,pRequest,cbRequest,ppNewRequest,fSpyed) (This)->lpVtbl->PreRealloc(This,pRequest,cbRequest,ppNewRequest,fSpyed)
#define IMallocSpy_PostRealloc(This,pActual,fSpyed) (This)->lpVtbl->PostRealloc(This,pActual,fSpyed)
#define IMallocSpy_PreGetSize(This,pRequest,fSpyed) (This)->lpVtbl->PreGetSize(This,pRequest,fSpyed)
#define IMallocSpy_PostGetSize(This,cbActual,fSpyed) (This)->lpVtbl->PostGetSize(This,cbActual,fSpyed)
#define IMallocSpy_PreDidAlloc(This,pRequest,fSpyed) (This)->lpVtbl->PreDidAlloc(This,pRequest,fSpyed)
#define IMallocSpy_PostDidAlloc(This,pRequest,fSpyed,fActual) (This)->lpVtbl->PostDidAlloc(This,pRequest,fSpyed,fActual)
#define IMallocSpy_PreHeapMinimize(This) (This)->lpVtbl->PreHeapMinimize(This)
#define IMallocSpy_PostHeapMinimize(This) (This)->lpVtbl->PostHeapMinimize(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMallocSpy_QueryInterface(IMallocSpy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMallocSpy_AddRef(IMallocSpy* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMallocSpy_Release(IMallocSpy* This) {
    return This->lpVtbl->Release(This);
}
/*** IMallocSpy methods ***/
static FORCEINLINE SIZE_T IMallocSpy_PreAlloc(IMallocSpy* This,SIZE_T cbRequest) {
    return This->lpVtbl->PreAlloc(This,cbRequest);
}
static FORCEINLINE void * IMallocSpy_PostAlloc(IMallocSpy* This,void *pActual) {
    return This->lpVtbl->PostAlloc(This,pActual);
}
static FORCEINLINE void * IMallocSpy_PreFree(IMallocSpy* This,void *pRequest,WINBOOL fSpyed) {
    return This->lpVtbl->PreFree(This,pRequest,fSpyed);
}
static FORCEINLINE void IMallocSpy_PostFree(IMallocSpy* This,WINBOOL fSpyed) {
    This->lpVtbl->PostFree(This,fSpyed);
}
static FORCEINLINE SIZE_T IMallocSpy_PreRealloc(IMallocSpy* This,void *pRequest,SIZE_T cbRequest,void **ppNewRequest,WINBOOL fSpyed) {
    return This->lpVtbl->PreRealloc(This,pRequest,cbRequest,ppNewRequest,fSpyed);
}
static FORCEINLINE void * IMallocSpy_PostRealloc(IMallocSpy* This,void *pActual,WINBOOL fSpyed) {
    return This->lpVtbl->PostRealloc(This,pActual,fSpyed);
}
static FORCEINLINE void * IMallocSpy_PreGetSize(IMallocSpy* This,void *pRequest,WINBOOL fSpyed) {
    return This->lpVtbl->PreGetSize(This,pRequest,fSpyed);
}
static FORCEINLINE SIZE_T IMallocSpy_PostGetSize(IMallocSpy* This,SIZE_T cbActual,WINBOOL fSpyed) {
    return This->lpVtbl->PostGetSize(This,cbActual,fSpyed);
}
static FORCEINLINE void * IMallocSpy_PreDidAlloc(IMallocSpy* This,void *pRequest,WINBOOL fSpyed) {
    return This->lpVtbl->PreDidAlloc(This,pRequest,fSpyed);
}
static FORCEINLINE int IMallocSpy_PostDidAlloc(IMallocSpy* This,void *pRequest,WINBOOL fSpyed,int fActual) {
    return This->lpVtbl->PostDidAlloc(This,pRequest,fSpyed,fActual);
}
static FORCEINLINE void IMallocSpy_PreHeapMinimize(IMallocSpy* This) {
    This->lpVtbl->PreHeapMinimize(This);
}
static FORCEINLINE void IMallocSpy_PostHeapMinimize(IMallocSpy* This) {
    This->lpVtbl->PostHeapMinimize(This);
}
#endif
#endif

#endif

SIZE_T STDMETHODCALLTYPE IMallocSpy_PreAlloc_Proxy(
    IMallocSpy* This,
    SIZE_T cbRequest);
void __RPC_STUB IMallocSpy_PreAlloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void * STDMETHODCALLTYPE IMallocSpy_PostAlloc_Proxy(
    IMallocSpy* This,
    void *pActual);
void __RPC_STUB IMallocSpy_PostAlloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void * STDMETHODCALLTYPE IMallocSpy_PreFree_Proxy(
    IMallocSpy* This,
    void *pRequest,
    WINBOOL fSpyed);
void __RPC_STUB IMallocSpy_PreFree_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IMallocSpy_PostFree_Proxy(
    IMallocSpy* This,
    WINBOOL fSpyed);
void __RPC_STUB IMallocSpy_PostFree_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
SIZE_T STDMETHODCALLTYPE IMallocSpy_PreRealloc_Proxy(
    IMallocSpy* This,
    void *pRequest,
    SIZE_T cbRequest,
    void **ppNewRequest,
    WINBOOL fSpyed);
void __RPC_STUB IMallocSpy_PreRealloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void * STDMETHODCALLTYPE IMallocSpy_PostRealloc_Proxy(
    IMallocSpy* This,
    void *pActual,
    WINBOOL fSpyed);
void __RPC_STUB IMallocSpy_PostRealloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void * STDMETHODCALLTYPE IMallocSpy_PreGetSize_Proxy(
    IMallocSpy* This,
    void *pRequest,
    WINBOOL fSpyed);
void __RPC_STUB IMallocSpy_PreGetSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
SIZE_T STDMETHODCALLTYPE IMallocSpy_PostGetSize_Proxy(
    IMallocSpy* This,
    SIZE_T cbActual,
    WINBOOL fSpyed);
void __RPC_STUB IMallocSpy_PostGetSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void * STDMETHODCALLTYPE IMallocSpy_PreDidAlloc_Proxy(
    IMallocSpy* This,
    void *pRequest,
    WINBOOL fSpyed);
void __RPC_STUB IMallocSpy_PreDidAlloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
int STDMETHODCALLTYPE IMallocSpy_PostDidAlloc_Proxy(
    IMallocSpy* This,
    void *pRequest,
    WINBOOL fSpyed,
    int fActual);
void __RPC_STUB IMallocSpy_PostDidAlloc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IMallocSpy_PreHeapMinimize_Proxy(
    IMallocSpy* This);
void __RPC_STUB IMallocSpy_PreHeapMinimize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void STDMETHODCALLTYPE IMallocSpy_PostHeapMinimize_Proxy(
    IMallocSpy* This);
void __RPC_STUB IMallocSpy_PostHeapMinimize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMallocSpy_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IBindCtx interface
 */
#ifndef __IBindCtx_INTERFACE_DEFINED__
#define __IBindCtx_INTERFACE_DEFINED__

typedef IBindCtx *LPBC;
typedef IBindCtx *LPBINDCTX;
#if defined(__cplusplus)
typedef struct tagBIND_OPTS {
  DWORD cbStruct;
  DWORD grfFlags;
  DWORD grfMode;
  DWORD dwTickCountDeadline;
} BIND_OPTS, *LPBIND_OPTS;
#else
typedef struct tagBIND_OPTS {
    DWORD cbStruct;
    DWORD grfFlags;
    DWORD grfMode;
    DWORD dwTickCountDeadline;
} BIND_OPTS;
typedef struct tagBIND_OPTS *LPBIND_OPTS;
#endif
#if defined(__cplusplus)
typedef struct tagBIND_OPTS2 : tagBIND_OPTS {
DWORD           dwTrackFlags;
DWORD           dwClassContext;
LCID            locale;
COSERVERINFO *  pServerInfo;
} BIND_OPTS2, * LPBIND_OPTS2;
#else
typedef struct tagBIND_OPTS2 {
    DWORD cbStruct;
    DWORD grfFlags;
    DWORD grfMode;
    DWORD dwTickCountDeadline;
    DWORD dwTrackFlags;
    DWORD dwClassContext;
    LCID locale;
    COSERVERINFO *pServerInfo;
} BIND_OPTS2;
typedef struct tagBIND_OPTS2 *LPBIND_OPTS2;
#endif
#if defined(__cplusplus)
typedef struct tagBIND_OPTS3 : tagBIND_OPTS2 {
HWND           hwnd;
} BIND_OPTS3, * LPBIND_OPTS3;
#else
typedef struct tagBIND_OPTS3 {
    DWORD cbStruct;
    DWORD grfFlags;
    DWORD grfMode;
    DWORD dwTickCountDeadline;
    DWORD dwTrackFlags;
    DWORD dwClassContext;
    LCID locale;
    COSERVERINFO *pServerInfo;
    HWND hwnd;
} BIND_OPTS3;
typedef struct tagBIND_OPTS3 *LPBIND_OPTS3;
#endif
typedef enum tagBIND_FLAGS {
    BIND_MAYBOTHERUSER = 1,
    BIND_JUSTTESTEXISTENCE = 2
} BIND_FLAGS;
DEFINE_GUID(IID_IBindCtx, 0x0000000e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000000e-0000-0000-c000-000000000046")
IBindCtx : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterObjectBound(
        IUnknown *punk) = 0;

    virtual HRESULT STDMETHODCALLTYPE RevokeObjectBound(
        IUnknown *punk) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseBoundObjects(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBindOptions(
        BIND_OPTS *pbindopts) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBindOptions(
        BIND_OPTS *pbindopts) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRunningObjectTable(
        IRunningObjectTable **pprot) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterObjectParam(
        LPOLESTR pszKey,
        IUnknown *punk) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectParam(
        LPOLESTR pszKey,
        IUnknown **ppunk) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumObjectParam(
        IEnumString **ppenum) = 0;

    virtual HRESULT STDMETHODCALLTYPE RevokeObjectParam(
        LPOLESTR pszKey) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBindCtx, 0x0000000e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IBindCtxVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBindCtx* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBindCtx* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBindCtx* This);

    /*** IBindCtx methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterObjectBound)(
        IBindCtx* This,
        IUnknown *punk);

    HRESULT (STDMETHODCALLTYPE *RevokeObjectBound)(
        IBindCtx* This,
        IUnknown *punk);

    HRESULT (STDMETHODCALLTYPE *ReleaseBoundObjects)(
        IBindCtx* This);

    HRESULT (STDMETHODCALLTYPE *SetBindOptions)(
        IBindCtx* This,
        BIND_OPTS *pbindopts);

    HRESULT (STDMETHODCALLTYPE *GetBindOptions)(
        IBindCtx* This,
        BIND_OPTS *pbindopts);

    HRESULT (STDMETHODCALLTYPE *GetRunningObjectTable)(
        IBindCtx* This,
        IRunningObjectTable **pprot);

    HRESULT (STDMETHODCALLTYPE *RegisterObjectParam)(
        IBindCtx* This,
        LPOLESTR pszKey,
        IUnknown *punk);

    HRESULT (STDMETHODCALLTYPE *GetObjectParam)(
        IBindCtx* This,
        LPOLESTR pszKey,
        IUnknown **ppunk);

    HRESULT (STDMETHODCALLTYPE *EnumObjectParam)(
        IBindCtx* This,
        IEnumString **ppenum);

    HRESULT (STDMETHODCALLTYPE *RevokeObjectParam)(
        IBindCtx* This,
        LPOLESTR pszKey);

    END_INTERFACE
} IBindCtxVtbl;
interface IBindCtx {
    CONST_VTBL IBindCtxVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBindCtx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBindCtx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBindCtx_Release(This) (This)->lpVtbl->Release(This)
/*** IBindCtx methods ***/
#define IBindCtx_RegisterObjectBound(This,punk) (This)->lpVtbl->RegisterObjectBound(This,punk)
#define IBindCtx_RevokeObjectBound(This,punk) (This)->lpVtbl->RevokeObjectBound(This,punk)
#define IBindCtx_ReleaseBoundObjects(This) (This)->lpVtbl->ReleaseBoundObjects(This)
#define IBindCtx_SetBindOptions(This,pbindopts) (This)->lpVtbl->SetBindOptions(This,pbindopts)
#define IBindCtx_GetBindOptions(This,pbindopts) (This)->lpVtbl->GetBindOptions(This,pbindopts)
#define IBindCtx_GetRunningObjectTable(This,pprot) (This)->lpVtbl->GetRunningObjectTable(This,pprot)
#define IBindCtx_RegisterObjectParam(This,pszKey,punk) (This)->lpVtbl->RegisterObjectParam(This,pszKey,punk)
#define IBindCtx_GetObjectParam(This,pszKey,ppunk) (This)->lpVtbl->GetObjectParam(This,pszKey,ppunk)
#define IBindCtx_EnumObjectParam(This,ppenum) (This)->lpVtbl->EnumObjectParam(This,ppenum)
#define IBindCtx_RevokeObjectParam(This,pszKey) (This)->lpVtbl->RevokeObjectParam(This,pszKey)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IBindCtx_QueryInterface(IBindCtx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IBindCtx_AddRef(IBindCtx* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IBindCtx_Release(IBindCtx* This) {
    return This->lpVtbl->Release(This);
}
/*** IBindCtx methods ***/
static FORCEINLINE HRESULT IBindCtx_RegisterObjectBound(IBindCtx* This,IUnknown *punk) {
    return This->lpVtbl->RegisterObjectBound(This,punk);
}
static FORCEINLINE HRESULT IBindCtx_RevokeObjectBound(IBindCtx* This,IUnknown *punk) {
    return This->lpVtbl->RevokeObjectBound(This,punk);
}
static FORCEINLINE HRESULT IBindCtx_ReleaseBoundObjects(IBindCtx* This) {
    return This->lpVtbl->ReleaseBoundObjects(This);
}
static FORCEINLINE HRESULT IBindCtx_SetBindOptions(IBindCtx* This,BIND_OPTS *pbindopts) {
    return This->lpVtbl->SetBindOptions(This,pbindopts);
}
static FORCEINLINE HRESULT IBindCtx_GetBindOptions(IBindCtx* This,BIND_OPTS *pbindopts) {
    return This->lpVtbl->GetBindOptions(This,pbindopts);
}
static FORCEINLINE HRESULT IBindCtx_GetRunningObjectTable(IBindCtx* This,IRunningObjectTable **pprot) {
    return This->lpVtbl->GetRunningObjectTable(This,pprot);
}
static FORCEINLINE HRESULT IBindCtx_RegisterObjectParam(IBindCtx* This,LPOLESTR pszKey,IUnknown *punk) {
    return This->lpVtbl->RegisterObjectParam(This,pszKey,punk);
}
static FORCEINLINE HRESULT IBindCtx_GetObjectParam(IBindCtx* This,LPOLESTR pszKey,IUnknown **ppunk) {
    return This->lpVtbl->GetObjectParam(This,pszKey,ppunk);
}
static FORCEINLINE HRESULT IBindCtx_EnumObjectParam(IBindCtx* This,IEnumString **ppenum) {
    return This->lpVtbl->EnumObjectParam(This,ppenum);
}
static FORCEINLINE HRESULT IBindCtx_RevokeObjectParam(IBindCtx* This,LPOLESTR pszKey) {
    return This->lpVtbl->RevokeObjectParam(This,pszKey);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IBindCtx_RegisterObjectBound_Proxy(
    IBindCtx* This,
    IUnknown *punk);
void __RPC_STUB IBindCtx_RegisterObjectBound_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_RevokeObjectBound_Proxy(
    IBindCtx* This,
    IUnknown *punk);
void __RPC_STUB IBindCtx_RevokeObjectBound_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_ReleaseBoundObjects_Proxy(
    IBindCtx* This);
void __RPC_STUB IBindCtx_ReleaseBoundObjects_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_RemoteSetBindOptions_Proxy(
    IBindCtx* This,
    BIND_OPTS2 *pbindopts);
void __RPC_STUB IBindCtx_RemoteSetBindOptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_RemoteGetBindOptions_Proxy(
    IBindCtx* This,
    BIND_OPTS2 *pbindopts);
void __RPC_STUB IBindCtx_RemoteGetBindOptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_GetRunningObjectTable_Proxy(
    IBindCtx* This,
    IRunningObjectTable **pprot);
void __RPC_STUB IBindCtx_GetRunningObjectTable_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_RegisterObjectParam_Proxy(
    IBindCtx* This,
    LPOLESTR pszKey,
    IUnknown *punk);
void __RPC_STUB IBindCtx_RegisterObjectParam_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_GetObjectParam_Proxy(
    IBindCtx* This,
    LPOLESTR pszKey,
    IUnknown **ppunk);
void __RPC_STUB IBindCtx_GetObjectParam_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_EnumObjectParam_Proxy(
    IBindCtx* This,
    IEnumString **ppenum);
void __RPC_STUB IBindCtx_EnumObjectParam_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindCtx_RevokeObjectParam_Proxy(
    IBindCtx* This,
    LPOLESTR pszKey);
void __RPC_STUB IBindCtx_RevokeObjectParam_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IBindCtx_SetBindOptions_Proxy(
    IBindCtx* This,
    BIND_OPTS *pbindopts);
HRESULT __RPC_STUB IBindCtx_SetBindOptions_Stub(
    IBindCtx* This,
    BIND_OPTS2 *pbindopts);
HRESULT CALLBACK IBindCtx_GetBindOptions_Proxy(
    IBindCtx* This,
    BIND_OPTS *pbindopts);
HRESULT __RPC_STUB IBindCtx_GetBindOptions_Stub(
    IBindCtx* This,
    BIND_OPTS2 *pbindopts);

#endif  /* __IBindCtx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumMoniker interface
 */
#ifndef __IEnumMoniker_INTERFACE_DEFINED__
#define __IEnumMoniker_INTERFACE_DEFINED__

typedef IEnumMoniker *LPENUMMONIKER;
DEFINE_GUID(IID_IEnumMoniker, 0x00000102, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000102-0000-0000-c000-000000000046")
IEnumMoniker : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        IMoniker **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumMoniker **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumMoniker, 0x00000102, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumMonikerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumMoniker* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumMoniker* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumMoniker* This);

    /*** IEnumMoniker methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumMoniker* This,
        ULONG celt,
        IMoniker **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumMoniker* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumMoniker* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumMoniker* This,
        IEnumMoniker **ppenum);

    END_INTERFACE
} IEnumMonikerVtbl;
interface IEnumMoniker {
    CONST_VTBL IEnumMonikerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumMoniker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumMoniker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumMoniker_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumMoniker methods ***/
#define IEnumMoniker_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumMoniker_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumMoniker_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumMoniker_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumMoniker_QueryInterface(IEnumMoniker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumMoniker_AddRef(IEnumMoniker* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumMoniker_Release(IEnumMoniker* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumMoniker methods ***/
static FORCEINLINE HRESULT IEnumMoniker_Next(IEnumMoniker* This,ULONG celt,IMoniker **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static FORCEINLINE HRESULT IEnumMoniker_Skip(IEnumMoniker* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumMoniker_Reset(IEnumMoniker* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumMoniker_Clone(IEnumMoniker* This,IEnumMoniker **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumMoniker_RemoteNext_Proxy(
    IEnumMoniker* This,
    ULONG celt,
    IMoniker **rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumMoniker_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumMoniker_Skip_Proxy(
    IEnumMoniker* This,
    ULONG celt);
void __RPC_STUB IEnumMoniker_Skip_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumMoniker_Reset_Proxy(
    IEnumMoniker* This);
void __RPC_STUB IEnumMoniker_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumMoniker_Clone_Proxy(
    IEnumMoniker* This,
    IEnumMoniker **ppenum);
void __RPC_STUB IEnumMoniker_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumMoniker_Next_Proxy(
    IEnumMoniker* This,
    ULONG celt,
    IMoniker **rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumMoniker_Next_Stub(
    IEnumMoniker* This,
    ULONG celt,
    IMoniker **rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumMoniker_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IRunnableObject interface
 */
#ifndef __IRunnableObject_INTERFACE_DEFINED__
#define __IRunnableObject_INTERFACE_DEFINED__

typedef IRunnableObject *LPRUNNABLEOBJECT;
DEFINE_GUID(IID_IRunnableObject, 0x00000126, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000126-0000-0000-c000-000000000046")
IRunnableObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRunningClass(
        LPCLSID lpClsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Run(
        LPBINDCTX pbc) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsRunning(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockRunning(
        WINBOOL fLock,
        WINBOOL fLastUnlockCloses) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContainedObject(
        WINBOOL fContained) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRunnableObject, 0x00000126, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IRunnableObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRunnableObject* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRunnableObject* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRunnableObject* This);

    /*** IRunnableObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRunningClass)(
        IRunnableObject* This,
        LPCLSID lpClsid);

    HRESULT (STDMETHODCALLTYPE *Run)(
        IRunnableObject* This,
        LPBINDCTX pbc);

    WINBOOL (STDMETHODCALLTYPE *IsRunning)(
        IRunnableObject* This);

    HRESULT (STDMETHODCALLTYPE *LockRunning)(
        IRunnableObject* This,
        WINBOOL fLock,
        WINBOOL fLastUnlockCloses);

    HRESULT (STDMETHODCALLTYPE *SetContainedObject)(
        IRunnableObject* This,
        WINBOOL fContained);

    END_INTERFACE
} IRunnableObjectVtbl;
interface IRunnableObject {
    CONST_VTBL IRunnableObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRunnableObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRunnableObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRunnableObject_Release(This) (This)->lpVtbl->Release(This)
/*** IRunnableObject methods ***/
#define IRunnableObject_GetRunningClass(This,lpClsid) (This)->lpVtbl->GetRunningClass(This,lpClsid)
#define IRunnableObject_Run(This,pbc) (This)->lpVtbl->Run(This,pbc)
#define IRunnableObject_IsRunning(This) (This)->lpVtbl->IsRunning(This)
#define IRunnableObject_LockRunning(This,fLock,fLastUnlockCloses) (This)->lpVtbl->LockRunning(This,fLock,fLastUnlockCloses)
#define IRunnableObject_SetContainedObject(This,fContained) (This)->lpVtbl->SetContainedObject(This,fContained)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRunnableObject_QueryInterface(IRunnableObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRunnableObject_AddRef(IRunnableObject* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRunnableObject_Release(IRunnableObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IRunnableObject methods ***/
static FORCEINLINE HRESULT IRunnableObject_GetRunningClass(IRunnableObject* This,LPCLSID lpClsid) {
    return This->lpVtbl->GetRunningClass(This,lpClsid);
}
static FORCEINLINE HRESULT IRunnableObject_Run(IRunnableObject* This,LPBINDCTX pbc) {
    return This->lpVtbl->Run(This,pbc);
}
static FORCEINLINE WINBOOL IRunnableObject_IsRunning(IRunnableObject* This) {
    return This->lpVtbl->IsRunning(This);
}
static FORCEINLINE HRESULT IRunnableObject_LockRunning(IRunnableObject* This,WINBOOL fLock,WINBOOL fLastUnlockCloses) {
    return This->lpVtbl->LockRunning(This,fLock,fLastUnlockCloses);
}
static FORCEINLINE HRESULT IRunnableObject_SetContainedObject(IRunnableObject* This,WINBOOL fContained) {
    return This->lpVtbl->SetContainedObject(This,fContained);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRunnableObject_GetRunningClass_Proxy(
    IRunnableObject* This,
    LPCLSID lpClsid);
void __RPC_STUB IRunnableObject_GetRunningClass_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunnableObject_Run_Proxy(
    IRunnableObject* This,
    LPBINDCTX pbc);
void __RPC_STUB IRunnableObject_Run_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunnableObject_RemoteIsRunning_Proxy(
    IRunnableObject* This);
void __RPC_STUB IRunnableObject_RemoteIsRunning_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunnableObject_LockRunning_Proxy(
    IRunnableObject* This,
    WINBOOL fLock,
    WINBOOL fLastUnlockCloses);
void __RPC_STUB IRunnableObject_LockRunning_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunnableObject_SetContainedObject_Proxy(
    IRunnableObject* This,
    WINBOOL fContained);
void __RPC_STUB IRunnableObject_SetContainedObject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
WINBOOL CALLBACK IRunnableObject_IsRunning_Proxy(
    IRunnableObject* This);
HRESULT __RPC_STUB IRunnableObject_IsRunning_Stub(
    IRunnableObject* This);

#endif  /* __IRunnableObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRunningObjectTable interface
 */
#ifndef __IRunningObjectTable_INTERFACE_DEFINED__
#define __IRunningObjectTable_INTERFACE_DEFINED__

typedef IRunningObjectTable *LPRUNNINGOBJECTTABLE;
DEFINE_GUID(IID_IRunningObjectTable, 0x00000010, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000010-0000-0000-c000-000000000046")
IRunningObjectTable : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Register(
        DWORD grfFlags,
        IUnknown *punkObject,
        IMoniker *pmkObjectName,
        DWORD *pdwRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE Revoke(
        DWORD dwRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRunning(
        IMoniker *pmkObjectName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObject(
        IMoniker *pmkObjectName,
        IUnknown **ppunkObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE NoteChangeTime(
        DWORD dwRegister,
        FILETIME *pfiletime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimeOfLastChange(
        IMoniker *pmkObjectName,
        FILETIME *pfiletime) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRunning(
        IEnumMoniker **ppenumMoniker) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRunningObjectTable, 0x00000010, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IRunningObjectTableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRunningObjectTable* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRunningObjectTable* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRunningObjectTable* This);

    /*** IRunningObjectTable methods ***/
    HRESULT (STDMETHODCALLTYPE *Register)(
        IRunningObjectTable* This,
        DWORD grfFlags,
        IUnknown *punkObject,
        IMoniker *pmkObjectName,
        DWORD *pdwRegister);

    HRESULT (STDMETHODCALLTYPE *Revoke)(
        IRunningObjectTable* This,
        DWORD dwRegister);

    HRESULT (STDMETHODCALLTYPE *IsRunning)(
        IRunningObjectTable* This,
        IMoniker *pmkObjectName);

    HRESULT (STDMETHODCALLTYPE *GetObject)(
        IRunningObjectTable* This,
        IMoniker *pmkObjectName,
        IUnknown **ppunkObject);

    HRESULT (STDMETHODCALLTYPE *NoteChangeTime)(
        IRunningObjectTable* This,
        DWORD dwRegister,
        FILETIME *pfiletime);

    HRESULT (STDMETHODCALLTYPE *GetTimeOfLastChange)(
        IRunningObjectTable* This,
        IMoniker *pmkObjectName,
        FILETIME *pfiletime);

    HRESULT (STDMETHODCALLTYPE *EnumRunning)(
        IRunningObjectTable* This,
        IEnumMoniker **ppenumMoniker);

    END_INTERFACE
} IRunningObjectTableVtbl;
interface IRunningObjectTable {
    CONST_VTBL IRunningObjectTableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRunningObjectTable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRunningObjectTable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRunningObjectTable_Release(This) (This)->lpVtbl->Release(This)
/*** IRunningObjectTable methods ***/
#define IRunningObjectTable_Register(This,grfFlags,punkObject,pmkObjectName,pdwRegister) (This)->lpVtbl->Register(This,grfFlags,punkObject,pmkObjectName,pdwRegister)
#define IRunningObjectTable_Revoke(This,dwRegister) (This)->lpVtbl->Revoke(This,dwRegister)
#define IRunningObjectTable_IsRunning(This,pmkObjectName) (This)->lpVtbl->IsRunning(This,pmkObjectName)
#define IRunningObjectTable_GetObject(This,pmkObjectName,ppunkObject) (This)->lpVtbl->GetObject(This,pmkObjectName,ppunkObject)
#define IRunningObjectTable_NoteChangeTime(This,dwRegister,pfiletime) (This)->lpVtbl->NoteChangeTime(This,dwRegister,pfiletime)
#define IRunningObjectTable_GetTimeOfLastChange(This,pmkObjectName,pfiletime) (This)->lpVtbl->GetTimeOfLastChange(This,pmkObjectName,pfiletime)
#define IRunningObjectTable_EnumRunning(This,ppenumMoniker) (This)->lpVtbl->EnumRunning(This,ppenumMoniker)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRunningObjectTable_QueryInterface(IRunningObjectTable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRunningObjectTable_AddRef(IRunningObjectTable* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRunningObjectTable_Release(IRunningObjectTable* This) {
    return This->lpVtbl->Release(This);
}
/*** IRunningObjectTable methods ***/
static FORCEINLINE HRESULT IRunningObjectTable_Register(IRunningObjectTable* This,DWORD grfFlags,IUnknown *punkObject,IMoniker *pmkObjectName,DWORD *pdwRegister) {
    return This->lpVtbl->Register(This,grfFlags,punkObject,pmkObjectName,pdwRegister);
}
static FORCEINLINE HRESULT IRunningObjectTable_Revoke(IRunningObjectTable* This,DWORD dwRegister) {
    return This->lpVtbl->Revoke(This,dwRegister);
}
static FORCEINLINE HRESULT IRunningObjectTable_IsRunning(IRunningObjectTable* This,IMoniker *pmkObjectName) {
    return This->lpVtbl->IsRunning(This,pmkObjectName);
}
static FORCEINLINE HRESULT IRunningObjectTable_GetObject(IRunningObjectTable* This,IMoniker *pmkObjectName,IUnknown **ppunkObject) {
    return This->lpVtbl->GetObject(This,pmkObjectName,ppunkObject);
}
static FORCEINLINE HRESULT IRunningObjectTable_NoteChangeTime(IRunningObjectTable* This,DWORD dwRegister,FILETIME *pfiletime) {
    return This->lpVtbl->NoteChangeTime(This,dwRegister,pfiletime);
}
static FORCEINLINE HRESULT IRunningObjectTable_GetTimeOfLastChange(IRunningObjectTable* This,IMoniker *pmkObjectName,FILETIME *pfiletime) {
    return This->lpVtbl->GetTimeOfLastChange(This,pmkObjectName,pfiletime);
}
static FORCEINLINE HRESULT IRunningObjectTable_EnumRunning(IRunningObjectTable* This,IEnumMoniker **ppenumMoniker) {
    return This->lpVtbl->EnumRunning(This,ppenumMoniker);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRunningObjectTable_Register_Proxy(
    IRunningObjectTable* This,
    DWORD grfFlags,
    IUnknown *punkObject,
    IMoniker *pmkObjectName,
    DWORD *pdwRegister);
void __RPC_STUB IRunningObjectTable_Register_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunningObjectTable_Revoke_Proxy(
    IRunningObjectTable* This,
    DWORD dwRegister);
void __RPC_STUB IRunningObjectTable_Revoke_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunningObjectTable_IsRunning_Proxy(
    IRunningObjectTable* This,
    IMoniker *pmkObjectName);
void __RPC_STUB IRunningObjectTable_IsRunning_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunningObjectTable_GetObject_Proxy(
    IRunningObjectTable* This,
    IMoniker *pmkObjectName,
    IUnknown **ppunkObject);
void __RPC_STUB IRunningObjectTable_GetObject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunningObjectTable_NoteChangeTime_Proxy(
    IRunningObjectTable* This,
    DWORD dwRegister,
    FILETIME *pfiletime);
void __RPC_STUB IRunningObjectTable_NoteChangeTime_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunningObjectTable_GetTimeOfLastChange_Proxy(
    IRunningObjectTable* This,
    IMoniker *pmkObjectName,
    FILETIME *pfiletime);
void __RPC_STUB IRunningObjectTable_GetTimeOfLastChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IRunningObjectTable_EnumRunning_Proxy(
    IRunningObjectTable* This,
    IEnumMoniker **ppenumMoniker);
void __RPC_STUB IRunningObjectTable_EnumRunning_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRunningObjectTable_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IPersist interface
 */
#ifndef __IPersist_INTERFACE_DEFINED__
#define __IPersist_INTERFACE_DEFINED__

typedef IPersist *LPPERSIST;
DEFINE_GUID(IID_IPersist, 0x0000010c, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000010c-0000-0000-c000-000000000046")
IPersist : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClassID(
        CLSID *pClassID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersist, 0x0000010c, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IPersistVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersist* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersist* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersist* This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersist* This,
        CLSID *pClassID);

    END_INTERFACE
} IPersistVtbl;
interface IPersist {
    CONST_VTBL IPersistVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersist_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersist_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersist_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IPersist_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPersist_QueryInterface(IPersist* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPersist_AddRef(IPersist* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPersist_Release(IPersist* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static FORCEINLINE HRESULT IPersist_GetClassID(IPersist* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPersist_GetClassID_Proxy(
    IPersist* This,
    CLSID *pClassID);
void __RPC_STUB IPersist_GetClassID_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPersist_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPersistStream interface
 */
#ifndef __IPersistStream_INTERFACE_DEFINED__
#define __IPersistStream_INTERFACE_DEFINED__

typedef IPersistStream *LPPERSISTSTREAM;
DEFINE_GUID(IID_IPersistStream, 0x00000109, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000109-0000-0000-c000-000000000046")
IPersistStream : public IPersist
{
    virtual HRESULT STDMETHODCALLTYPE IsDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        IStream *pStm) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        IStream *pStm,
        WINBOOL fClearDirty) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSizeMax(
        ULARGE_INTEGER *pcbSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistStream, 0x00000109, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IPersistStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistStream* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistStream* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistStream* This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersistStream* This,
        CLSID *pClassID);

    /*** IPersistStream methods ***/
    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IPersistStream* This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IPersistStream* This,
        IStream *pStm);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IPersistStream* This,
        IStream *pStm,
        WINBOOL fClearDirty);

    HRESULT (STDMETHODCALLTYPE *GetSizeMax)(
        IPersistStream* This,
        ULARGE_INTEGER *pcbSize);

    END_INTERFACE
} IPersistStreamVtbl;
interface IPersistStream {
    CONST_VTBL IPersistStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistStream_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IPersistStream_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistStream methods ***/
#define IPersistStream_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#define IPersistStream_Load(This,pStm) (This)->lpVtbl->Load(This,pStm)
#define IPersistStream_Save(This,pStm,fClearDirty) (This)->lpVtbl->Save(This,pStm,fClearDirty)
#define IPersistStream_GetSizeMax(This,pcbSize) (This)->lpVtbl->GetSizeMax(This,pcbSize)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPersistStream_QueryInterface(IPersistStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPersistStream_AddRef(IPersistStream* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPersistStream_Release(IPersistStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static FORCEINLINE HRESULT IPersistStream_GetClassID(IPersistStream* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistStream methods ***/
static FORCEINLINE HRESULT IPersistStream_IsDirty(IPersistStream* This) {
    return This->lpVtbl->IsDirty(This);
}
static FORCEINLINE HRESULT IPersistStream_Load(IPersistStream* This,IStream *pStm) {
    return This->lpVtbl->Load(This,pStm);
}
static FORCEINLINE HRESULT IPersistStream_Save(IPersistStream* This,IStream *pStm,WINBOOL fClearDirty) {
    return This->lpVtbl->Save(This,pStm,fClearDirty);
}
static FORCEINLINE HRESULT IPersistStream_GetSizeMax(IPersistStream* This,ULARGE_INTEGER *pcbSize) {
    return This->lpVtbl->GetSizeMax(This,pcbSize);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPersistStream_IsDirty_Proxy(
    IPersistStream* This);
void __RPC_STUB IPersistStream_IsDirty_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistStream_Load_Proxy(
    IPersistStream* This,
    IStream *pStm);
void __RPC_STUB IPersistStream_Load_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistStream_Save_Proxy(
    IPersistStream* This,
    IStream *pStm,
    WINBOOL fClearDirty);
void __RPC_STUB IPersistStream_Save_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistStream_GetSizeMax_Proxy(
    IPersistStream* This,
    ULARGE_INTEGER *pcbSize);
void __RPC_STUB IPersistStream_GetSizeMax_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPersistStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMoniker interface
 */
#ifndef __IMoniker_INTERFACE_DEFINED__
#define __IMoniker_INTERFACE_DEFINED__

typedef IMoniker *LPMONIKER;
typedef enum tagMKSYS {
    MKSYS_NONE = 0,
    MKSYS_GENERICCOMPOSITE = 1,
    MKSYS_FILEMONIKER = 2,
    MKSYS_ANTIMONIKER = 3,
    MKSYS_ITEMMONIKER = 4,
    MKSYS_POINTERMONIKER = 5,
    MKSYS_CLASSMONIKER = 7,
    MKSYS_OBJREFMONIKER = 8,
    MKSYS_SESSIONMONIKER = 9,
    MKSYS_LUAMONIKER = 10
} MKSYS;
typedef enum tagMKREDUCE {
    MKRREDUCE_ONE = 3 << 16,
    MKRREDUCE_TOUSER = 2 << 16,
    MKRREDUCE_THROUGHUSER = 1 << 16,
    MKRREDUCE_ALL = 0
} MKRREDUCE;
DEFINE_GUID(IID_IMoniker, 0x0000000f, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000000f-0000-0000-c000-000000000046")
IMoniker : public IPersistStream
{
    virtual HRESULT STDMETHODCALLTYPE BindToObject(
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        REFIID riidResult,
        void **ppvResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE BindToStorage(
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        REFIID riid,
        void **ppvObj) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reduce(
        IBindCtx *pbc,
        DWORD dwReduceHowFar,
        IMoniker **ppmkToLeft,
        IMoniker **ppmkReduced) = 0;

    virtual HRESULT STDMETHODCALLTYPE ComposeWith(
        IMoniker *pmkRight,
        WINBOOL fOnlyIfNotGeneric,
        IMoniker **ppmkComposite) = 0;

    virtual HRESULT STDMETHODCALLTYPE Enum(
        WINBOOL fForward,
        IEnumMoniker **ppenumMoniker) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEqual(
        IMoniker *pmkOtherMoniker) = 0;

    virtual HRESULT STDMETHODCALLTYPE Hash(
        DWORD *pdwHash) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRunning(
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        IMoniker *pmkNewlyRunning) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimeOfLastChange(
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        FILETIME *pFileTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE Inverse(
        IMoniker **ppmk) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommonPrefixWith(
        IMoniker *pmkOther,
        IMoniker **ppmkPrefix) = 0;

    virtual HRESULT STDMETHODCALLTYPE RelativePathTo(
        IMoniker *pmkOther,
        IMoniker **ppmkRelPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayName(
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        LPOLESTR *ppszDisplayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE ParseDisplayName(
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        LPOLESTR pszDisplayName,
        ULONG *pchEaten,
        IMoniker **ppmkOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSystemMoniker(
        DWORD *pdwMksys) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMoniker, 0x0000000f, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMonikerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMoniker* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMoniker* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMoniker* This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IMoniker* This,
        CLSID *pClassID);

    /*** IPersistStream methods ***/
    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IMoniker* This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IMoniker* This,
        IStream *pStm);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IMoniker* This,
        IStream *pStm,
        WINBOOL fClearDirty);

    HRESULT (STDMETHODCALLTYPE *GetSizeMax)(
        IMoniker* This,
        ULARGE_INTEGER *pcbSize);

    /*** IMoniker methods ***/
    HRESULT (STDMETHODCALLTYPE *BindToObject)(
        IMoniker* This,
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        REFIID riidResult,
        void **ppvResult);

    HRESULT (STDMETHODCALLTYPE *BindToStorage)(
        IMoniker* This,
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        REFIID riid,
        void **ppvObj);

    HRESULT (STDMETHODCALLTYPE *Reduce)(
        IMoniker* This,
        IBindCtx *pbc,
        DWORD dwReduceHowFar,
        IMoniker **ppmkToLeft,
        IMoniker **ppmkReduced);

    HRESULT (STDMETHODCALLTYPE *ComposeWith)(
        IMoniker* This,
        IMoniker *pmkRight,
        WINBOOL fOnlyIfNotGeneric,
        IMoniker **ppmkComposite);

    HRESULT (STDMETHODCALLTYPE *Enum)(
        IMoniker* This,
        WINBOOL fForward,
        IEnumMoniker **ppenumMoniker);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        IMoniker* This,
        IMoniker *pmkOtherMoniker);

    HRESULT (STDMETHODCALLTYPE *Hash)(
        IMoniker* This,
        DWORD *pdwHash);

    HRESULT (STDMETHODCALLTYPE *IsRunning)(
        IMoniker* This,
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        IMoniker *pmkNewlyRunning);

    HRESULT (STDMETHODCALLTYPE *GetTimeOfLastChange)(
        IMoniker* This,
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        FILETIME *pFileTime);

    HRESULT (STDMETHODCALLTYPE *Inverse)(
        IMoniker* This,
        IMoniker **ppmk);

    HRESULT (STDMETHODCALLTYPE *CommonPrefixWith)(
        IMoniker* This,
        IMoniker *pmkOther,
        IMoniker **ppmkPrefix);

    HRESULT (STDMETHODCALLTYPE *RelativePathTo)(
        IMoniker* This,
        IMoniker *pmkOther,
        IMoniker **ppmkRelPath);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IMoniker* This,
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        LPOLESTR *ppszDisplayName);

    HRESULT (STDMETHODCALLTYPE *ParseDisplayName)(
        IMoniker* This,
        IBindCtx *pbc,
        IMoniker *pmkToLeft,
        LPOLESTR pszDisplayName,
        ULONG *pchEaten,
        IMoniker **ppmkOut);

    HRESULT (STDMETHODCALLTYPE *IsSystemMoniker)(
        IMoniker* This,
        DWORD *pdwMksys);

    END_INTERFACE
} IMonikerVtbl;
interface IMoniker {
    CONST_VTBL IMonikerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMoniker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMoniker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMoniker_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IMoniker_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistStream methods ***/
#define IMoniker_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#define IMoniker_Load(This,pStm) (This)->lpVtbl->Load(This,pStm)
#define IMoniker_Save(This,pStm,fClearDirty) (This)->lpVtbl->Save(This,pStm,fClearDirty)
#define IMoniker_GetSizeMax(This,pcbSize) (This)->lpVtbl->GetSizeMax(This,pcbSize)
/*** IMoniker methods ***/
#define IMoniker_BindToObject(This,pbc,pmkToLeft,riidResult,ppvResult) (This)->lpVtbl->BindToObject(This,pbc,pmkToLeft,riidResult,ppvResult)
#define IMoniker_BindToStorage(This,pbc,pmkToLeft,riid,ppvObj) (This)->lpVtbl->BindToStorage(This,pbc,pmkToLeft,riid,ppvObj)
#define IMoniker_Reduce(This,pbc,dwReduceHowFar,ppmkToLeft,ppmkReduced) (This)->lpVtbl->Reduce(This,pbc,dwReduceHowFar,ppmkToLeft,ppmkReduced)
#define IMoniker_ComposeWith(This,pmkRight,fOnlyIfNotGeneric,ppmkComposite) (This)->lpVtbl->ComposeWith(This,pmkRight,fOnlyIfNotGeneric,ppmkComposite)
#define IMoniker_Enum(This,fForward,ppenumMoniker) (This)->lpVtbl->Enum(This,fForward,ppenumMoniker)
#define IMoniker_IsEqual(This,pmkOtherMoniker) (This)->lpVtbl->IsEqual(This,pmkOtherMoniker)
#define IMoniker_Hash(This,pdwHash) (This)->lpVtbl->Hash(This,pdwHash)
#define IMoniker_IsRunning(This,pbc,pmkToLeft,pmkNewlyRunning) (This)->lpVtbl->IsRunning(This,pbc,pmkToLeft,pmkNewlyRunning)
#define IMoniker_GetTimeOfLastChange(This,pbc,pmkToLeft,pFileTime) (This)->lpVtbl->GetTimeOfLastChange(This,pbc,pmkToLeft,pFileTime)
#define IMoniker_Inverse(This,ppmk) (This)->lpVtbl->Inverse(This,ppmk)
#define IMoniker_CommonPrefixWith(This,pmkOther,ppmkPrefix) (This)->lpVtbl->CommonPrefixWith(This,pmkOther,ppmkPrefix)
#define IMoniker_RelativePathTo(This,pmkOther,ppmkRelPath) (This)->lpVtbl->RelativePathTo(This,pmkOther,ppmkRelPath)
#define IMoniker_GetDisplayName(This,pbc,pmkToLeft,ppszDisplayName) (This)->lpVtbl->GetDisplayName(This,pbc,pmkToLeft,ppszDisplayName)
#define IMoniker_ParseDisplayName(This,pbc,pmkToLeft,pszDisplayName,pchEaten,ppmkOut) (This)->lpVtbl->ParseDisplayName(This,pbc,pmkToLeft,pszDisplayName,pchEaten,ppmkOut)
#define IMoniker_IsSystemMoniker(This,pdwMksys) (This)->lpVtbl->IsSystemMoniker(This,pdwMksys)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMoniker_QueryInterface(IMoniker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMoniker_AddRef(IMoniker* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMoniker_Release(IMoniker* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static FORCEINLINE HRESULT IMoniker_GetClassID(IMoniker* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistStream methods ***/
static FORCEINLINE HRESULT IMoniker_IsDirty(IMoniker* This) {
    return This->lpVtbl->IsDirty(This);
}
static FORCEINLINE HRESULT IMoniker_Load(IMoniker* This,IStream *pStm) {
    return This->lpVtbl->Load(This,pStm);
}
static FORCEINLINE HRESULT IMoniker_Save(IMoniker* This,IStream *pStm,WINBOOL fClearDirty) {
    return This->lpVtbl->Save(This,pStm,fClearDirty);
}
static FORCEINLINE HRESULT IMoniker_GetSizeMax(IMoniker* This,ULARGE_INTEGER *pcbSize) {
    return This->lpVtbl->GetSizeMax(This,pcbSize);
}
/*** IMoniker methods ***/
static FORCEINLINE HRESULT IMoniker_BindToObject(IMoniker* This,IBindCtx *pbc,IMoniker *pmkToLeft,REFIID riidResult,void **ppvResult) {
    return This->lpVtbl->BindToObject(This,pbc,pmkToLeft,riidResult,ppvResult);
}
static FORCEINLINE HRESULT IMoniker_BindToStorage(IMoniker* This,IBindCtx *pbc,IMoniker *pmkToLeft,REFIID riid,void **ppvObj) {
    return This->lpVtbl->BindToStorage(This,pbc,pmkToLeft,riid,ppvObj);
}
static FORCEINLINE HRESULT IMoniker_Reduce(IMoniker* This,IBindCtx *pbc,DWORD dwReduceHowFar,IMoniker **ppmkToLeft,IMoniker **ppmkReduced) {
    return This->lpVtbl->Reduce(This,pbc,dwReduceHowFar,ppmkToLeft,ppmkReduced);
}
static FORCEINLINE HRESULT IMoniker_ComposeWith(IMoniker* This,IMoniker *pmkRight,WINBOOL fOnlyIfNotGeneric,IMoniker **ppmkComposite) {
    return This->lpVtbl->ComposeWith(This,pmkRight,fOnlyIfNotGeneric,ppmkComposite);
}
static FORCEINLINE HRESULT IMoniker_Enum(IMoniker* This,WINBOOL fForward,IEnumMoniker **ppenumMoniker) {
    return This->lpVtbl->Enum(This,fForward,ppenumMoniker);
}
static FORCEINLINE HRESULT IMoniker_IsEqual(IMoniker* This,IMoniker *pmkOtherMoniker) {
    return This->lpVtbl->IsEqual(This,pmkOtherMoniker);
}
static FORCEINLINE HRESULT IMoniker_Hash(IMoniker* This,DWORD *pdwHash) {
    return This->lpVtbl->Hash(This,pdwHash);
}
static FORCEINLINE HRESULT IMoniker_IsRunning(IMoniker* This,IBindCtx *pbc,IMoniker *pmkToLeft,IMoniker *pmkNewlyRunning) {
    return This->lpVtbl->IsRunning(This,pbc,pmkToLeft,pmkNewlyRunning);
}
static FORCEINLINE HRESULT IMoniker_GetTimeOfLastChange(IMoniker* This,IBindCtx *pbc,IMoniker *pmkToLeft,FILETIME *pFileTime) {
    return This->lpVtbl->GetTimeOfLastChange(This,pbc,pmkToLeft,pFileTime);
}
static FORCEINLINE HRESULT IMoniker_Inverse(IMoniker* This,IMoniker **ppmk) {
    return This->lpVtbl->Inverse(This,ppmk);
}
static FORCEINLINE HRESULT IMoniker_CommonPrefixWith(IMoniker* This,IMoniker *pmkOther,IMoniker **ppmkPrefix) {
    return This->lpVtbl->CommonPrefixWith(This,pmkOther,ppmkPrefix);
}
static FORCEINLINE HRESULT IMoniker_RelativePathTo(IMoniker* This,IMoniker *pmkOther,IMoniker **ppmkRelPath) {
    return This->lpVtbl->RelativePathTo(This,pmkOther,ppmkRelPath);
}
static FORCEINLINE HRESULT IMoniker_GetDisplayName(IMoniker* This,IBindCtx *pbc,IMoniker *pmkToLeft,LPOLESTR *ppszDisplayName) {
    return This->lpVtbl->GetDisplayName(This,pbc,pmkToLeft,ppszDisplayName);
}
static FORCEINLINE HRESULT IMoniker_ParseDisplayName(IMoniker* This,IBindCtx *pbc,IMoniker *pmkToLeft,LPOLESTR pszDisplayName,ULONG *pchEaten,IMoniker **ppmkOut) {
    return This->lpVtbl->ParseDisplayName(This,pbc,pmkToLeft,pszDisplayName,pchEaten,ppmkOut);
}
static FORCEINLINE HRESULT IMoniker_IsSystemMoniker(IMoniker* This,DWORD *pdwMksys) {
    return This->lpVtbl->IsSystemMoniker(This,pdwMksys);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMoniker_RemoteBindToObject_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    REFIID riidResult,
    IUnknown **ppvResult);
void __RPC_STUB IMoniker_RemoteBindToObject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_RemoteBindToStorage_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    REFIID riid,
    IUnknown **ppvObj);
void __RPC_STUB IMoniker_RemoteBindToStorage_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_Reduce_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    DWORD dwReduceHowFar,
    IMoniker **ppmkToLeft,
    IMoniker **ppmkReduced);
void __RPC_STUB IMoniker_Reduce_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_ComposeWith_Proxy(
    IMoniker* This,
    IMoniker *pmkRight,
    WINBOOL fOnlyIfNotGeneric,
    IMoniker **ppmkComposite);
void __RPC_STUB IMoniker_ComposeWith_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_Enum_Proxy(
    IMoniker* This,
    WINBOOL fForward,
    IEnumMoniker **ppenumMoniker);
void __RPC_STUB IMoniker_Enum_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_IsEqual_Proxy(
    IMoniker* This,
    IMoniker *pmkOtherMoniker);
void __RPC_STUB IMoniker_IsEqual_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_Hash_Proxy(
    IMoniker* This,
    DWORD *pdwHash);
void __RPC_STUB IMoniker_Hash_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_IsRunning_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    IMoniker *pmkNewlyRunning);
void __RPC_STUB IMoniker_IsRunning_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_GetTimeOfLastChange_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    FILETIME *pFileTime);
void __RPC_STUB IMoniker_GetTimeOfLastChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_Inverse_Proxy(
    IMoniker* This,
    IMoniker **ppmk);
void __RPC_STUB IMoniker_Inverse_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_CommonPrefixWith_Proxy(
    IMoniker* This,
    IMoniker *pmkOther,
    IMoniker **ppmkPrefix);
void __RPC_STUB IMoniker_CommonPrefixWith_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_RelativePathTo_Proxy(
    IMoniker* This,
    IMoniker *pmkOther,
    IMoniker **ppmkRelPath);
void __RPC_STUB IMoniker_RelativePathTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_GetDisplayName_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    LPOLESTR *ppszDisplayName);
void __RPC_STUB IMoniker_GetDisplayName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_ParseDisplayName_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    LPOLESTR pszDisplayName,
    ULONG *pchEaten,
    IMoniker **ppmkOut);
void __RPC_STUB IMoniker_ParseDisplayName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMoniker_IsSystemMoniker_Proxy(
    IMoniker* This,
    DWORD *pdwMksys);
void __RPC_STUB IMoniker_IsSystemMoniker_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMoniker_BindToObject_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    REFIID riidResult,
    void **ppvResult);
HRESULT __RPC_STUB IMoniker_BindToObject_Stub(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    REFIID riidResult,
    IUnknown **ppvResult);
HRESULT CALLBACK IMoniker_BindToStorage_Proxy(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    REFIID riid,
    void **ppvObj);
HRESULT __RPC_STUB IMoniker_BindToStorage_Stub(
    IMoniker* This,
    IBindCtx *pbc,
    IMoniker *pmkToLeft,
    REFIID riid,
    IUnknown **ppvObj);

#endif  /* __IMoniker_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IROTData interface
 */
#ifndef __IROTData_INTERFACE_DEFINED__
#define __IROTData_INTERFACE_DEFINED__

DEFINE_GUID(IID_IROTData, 0xf29f6bc0, 0x5021, 0x11ce, 0xaa,0x15, 0x00,0x00,0x69,0x01,0x29,0x3f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f29f6bc0-5021-11ce-aa15-00006901293f")
IROTData : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetComparisonData(
        byte *pbData,
        ULONG cbMax,
        ULONG *pcbData) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IROTData, 0xf29f6bc0, 0x5021, 0x11ce, 0xaa,0x15, 0x00,0x00,0x69,0x01,0x29,0x3f)
#endif
#else
typedef struct IROTDataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IROTData* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IROTData* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IROTData* This);

    /*** IROTData methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComparisonData)(
        IROTData* This,
        byte *pbData,
        ULONG cbMax,
        ULONG *pcbData);

    END_INTERFACE
} IROTDataVtbl;
interface IROTData {
    CONST_VTBL IROTDataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IROTData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IROTData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IROTData_Release(This) (This)->lpVtbl->Release(This)
/*** IROTData methods ***/
#define IROTData_GetComparisonData(This,pbData,cbMax,pcbData) (This)->lpVtbl->GetComparisonData(This,pbData,cbMax,pcbData)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IROTData_QueryInterface(IROTData* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IROTData_AddRef(IROTData* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IROTData_Release(IROTData* This) {
    return This->lpVtbl->Release(This);
}
/*** IROTData methods ***/
static FORCEINLINE HRESULT IROTData_GetComparisonData(IROTData* This,byte *pbData,ULONG cbMax,ULONG *pcbData) {
    return This->lpVtbl->GetComparisonData(This,pbData,cbMax,pcbData);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IROTData_GetComparisonData_Proxy(
    IROTData* This,
    byte *pbData,
    ULONG cbMax,
    ULONG *pcbData);
void __RPC_STUB IROTData_GetComparisonData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IROTData_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IEnumSTATSTG interface
 */
#ifndef __IEnumSTATSTG_INTERFACE_DEFINED__
#define __IEnumSTATSTG_INTERFACE_DEFINED__

typedef IEnumSTATSTG *LPENUMSTATSTG;
DEFINE_GUID(IID_IEnumSTATSTG, 0x0000000d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000000d-0000-0000-c000-000000000046")
IEnumSTATSTG : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        STATSTG *rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumSTATSTG **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumSTATSTG, 0x0000000d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumSTATSTGVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumSTATSTG* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumSTATSTG* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumSTATSTG* This);

    /*** IEnumSTATSTG methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumSTATSTG* This,
        ULONG celt,
        STATSTG *rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumSTATSTG* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumSTATSTG* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumSTATSTG* This,
        IEnumSTATSTG **ppenum);

    END_INTERFACE
} IEnumSTATSTGVtbl;
interface IEnumSTATSTG {
    CONST_VTBL IEnumSTATSTGVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumSTATSTG_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumSTATSTG_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumSTATSTG_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumSTATSTG methods ***/
#define IEnumSTATSTG_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumSTATSTG_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumSTATSTG_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumSTATSTG_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumSTATSTG_QueryInterface(IEnumSTATSTG* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumSTATSTG_AddRef(IEnumSTATSTG* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumSTATSTG_Release(IEnumSTATSTG* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumSTATSTG methods ***/
static FORCEINLINE HRESULT IEnumSTATSTG_Next(IEnumSTATSTG* This,ULONG celt,STATSTG *rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static FORCEINLINE HRESULT IEnumSTATSTG_Skip(IEnumSTATSTG* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumSTATSTG_Reset(IEnumSTATSTG* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumSTATSTG_Clone(IEnumSTATSTG* This,IEnumSTATSTG **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumSTATSTG_RemoteNext_Proxy(
    IEnumSTATSTG* This,
    ULONG celt,
    STATSTG *rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumSTATSTG_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumSTATSTG_Skip_Proxy(
    IEnumSTATSTG* This,
    ULONG celt);
void __RPC_STUB IEnumSTATSTG_Skip_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumSTATSTG_Reset_Proxy(
    IEnumSTATSTG* This);
void __RPC_STUB IEnumSTATSTG_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumSTATSTG_Clone_Proxy(
    IEnumSTATSTG* This,
    IEnumSTATSTG **ppenum);
void __RPC_STUB IEnumSTATSTG_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumSTATSTG_Next_Proxy(
    IEnumSTATSTG* This,
    ULONG celt,
    STATSTG *rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumSTATSTG_Next_Stub(
    IEnumSTATSTG* This,
    ULONG celt,
    STATSTG *rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumSTATSTG_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IStorage interface
 */
#ifndef __IStorage_INTERFACE_DEFINED__
#define __IStorage_INTERFACE_DEFINED__

typedef IStorage *LPSTORAGE;
typedef struct tagRemSNB {
    ULONG ulCntStr;
    ULONG ulCntChar;
    OLECHAR rgString[1];
} RemSNB;
typedef RemSNB *wireSNB;
typedef LPOLESTR *SNB;
DEFINE_GUID(IID_IStorage, 0x0000000b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000000b-0000-0000-c000-000000000046")
IStorage : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateStream(
        const OLECHAR *pwcsName,
        DWORD grfMode,
        DWORD reserved1,
        DWORD reserved2,
        IStream **ppstm) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenStream(
        const OLECHAR *pwcsName,
        void *reserved1,
        DWORD grfMode,
        DWORD reserved2,
        IStream **ppstm) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStorage(
        const OLECHAR *pwcsName,
        DWORD grfMode,
        DWORD reserved1,
        DWORD reserved2,
        IStorage **ppstg) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenStorage(
        const OLECHAR *pwcsName,
        IStorage *pstgPriority,
        DWORD grfMode,
        SNB snbExclude,
        DWORD reserved,
        IStorage **ppstg) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyTo(
        DWORD ciidExclude,
        const IID *rgiidExclude,
        SNB snbExclude,
        IStorage *pstgDest) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveElementTo(
        const OLECHAR *pwcsName,
        IStorage *pstgDest,
        const OLECHAR *pwcsNewName,
        DWORD grfFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Commit(
        DWORD grfCommitFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Revert(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumElements(
        DWORD reserved1,
        void *reserved2,
        DWORD reserved3,
        IEnumSTATSTG **ppenum) = 0;

    virtual HRESULT STDMETHODCALLTYPE DestroyElement(
        const OLECHAR *pwcsName) = 0;

    virtual HRESULT STDMETHODCALLTYPE RenameElement(
        const OLECHAR *pwcsOldName,
        const OLECHAR *pwcsNewName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetElementTimes(
        const OLECHAR *pwcsName,
        const FILETIME *pctime,
        const FILETIME *patime,
        const FILETIME *pmtime) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetClass(
        REFCLSID clsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStateBits(
        DWORD grfStateBits,
        DWORD grfMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stat(
        STATSTG *pstatstg,
        DWORD grfStatFlag) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IStorage, 0x0000000b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IStorageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IStorage* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IStorage* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IStorage* This);

    /*** IStorage methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateStream)(
        IStorage* This,
        const OLECHAR *pwcsName,
        DWORD grfMode,
        DWORD reserved1,
        DWORD reserved2,
        IStream **ppstm);

    HRESULT (STDMETHODCALLTYPE *OpenStream)(
        IStorage* This,
        const OLECHAR *pwcsName,
        void *reserved1,
        DWORD grfMode,
        DWORD reserved2,
        IStream **ppstm);

    HRESULT (STDMETHODCALLTYPE *CreateStorage)(
        IStorage* This,
        const OLECHAR *pwcsName,
        DWORD grfMode,
        DWORD reserved1,
        DWORD reserved2,
        IStorage **ppstg);

    HRESULT (STDMETHODCALLTYPE *OpenStorage)(
        IStorage* This,
        const OLECHAR *pwcsName,
        IStorage *pstgPriority,
        DWORD grfMode,
        SNB snbExclude,
        DWORD reserved,
        IStorage **ppstg);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        IStorage* This,
        DWORD ciidExclude,
        const IID *rgiidExclude,
        SNB snbExclude,
        IStorage *pstgDest);

    HRESULT (STDMETHODCALLTYPE *MoveElementTo)(
        IStorage* This,
        const OLECHAR *pwcsName,
        IStorage *pstgDest,
        const OLECHAR *pwcsNewName,
        DWORD grfFlags);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IStorage* This,
        DWORD grfCommitFlags);

    HRESULT (STDMETHODCALLTYPE *Revert)(
        IStorage* This);

    HRESULT (STDMETHODCALLTYPE *EnumElements)(
        IStorage* This,
        DWORD reserved1,
        void *reserved2,
        DWORD reserved3,
        IEnumSTATSTG **ppenum);

    HRESULT (STDMETHODCALLTYPE *DestroyElement)(
        IStorage* This,
        const OLECHAR *pwcsName);

    HRESULT (STDMETHODCALLTYPE *RenameElement)(
        IStorage* This,
        const OLECHAR *pwcsOldName,
        const OLECHAR *pwcsNewName);

    HRESULT (STDMETHODCALLTYPE *SetElementTimes)(
        IStorage* This,
        const OLECHAR *pwcsName,
        const FILETIME *pctime,
        const FILETIME *patime,
        const FILETIME *pmtime);

    HRESULT (STDMETHODCALLTYPE *SetClass)(
        IStorage* This,
        REFCLSID clsid);

    HRESULT (STDMETHODCALLTYPE *SetStateBits)(
        IStorage* This,
        DWORD grfStateBits,
        DWORD grfMask);

    HRESULT (STDMETHODCALLTYPE *Stat)(
        IStorage* This,
        STATSTG *pstatstg,
        DWORD grfStatFlag);

    END_INTERFACE
} IStorageVtbl;
interface IStorage {
    CONST_VTBL IStorageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IStorage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IStorage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IStorage_Release(This) (This)->lpVtbl->Release(This)
/*** IStorage methods ***/
#define IStorage_CreateStream(This,pwcsName,grfMode,reserved1,reserved2,ppstm) (This)->lpVtbl->CreateStream(This,pwcsName,grfMode,reserved1,reserved2,ppstm)
#define IStorage_OpenStream(This,pwcsName,reserved1,grfMode,reserved2,ppstm) (This)->lpVtbl->OpenStream(This,pwcsName,reserved1,grfMode,reserved2,ppstm)
#define IStorage_CreateStorage(This,pwcsName,grfMode,reserved1,reserved2,ppstg) (This)->lpVtbl->CreateStorage(This,pwcsName,grfMode,reserved1,reserved2,ppstg)
#define IStorage_OpenStorage(This,pwcsName,pstgPriority,grfMode,snbExclude,reserved,ppstg) (This)->lpVtbl->OpenStorage(This,pwcsName,pstgPriority,grfMode,snbExclude,reserved,ppstg)
#define IStorage_CopyTo(This,ciidExclude,rgiidExclude,snbExclude,pstgDest) (This)->lpVtbl->CopyTo(This,ciidExclude,rgiidExclude,snbExclude,pstgDest)
#define IStorage_MoveElementTo(This,pwcsName,pstgDest,pwcsNewName,grfFlags) (This)->lpVtbl->MoveElementTo(This,pwcsName,pstgDest,pwcsNewName,grfFlags)
#define IStorage_Commit(This,grfCommitFlags) (This)->lpVtbl->Commit(This,grfCommitFlags)
#define IStorage_Revert(This) (This)->lpVtbl->Revert(This)
#define IStorage_EnumElements(This,reserved1,reserved2,reserved3,ppenum) (This)->lpVtbl->EnumElements(This,reserved1,reserved2,reserved3,ppenum)
#define IStorage_DestroyElement(This,pwcsName) (This)->lpVtbl->DestroyElement(This,pwcsName)
#define IStorage_RenameElement(This,pwcsOldName,pwcsNewName) (This)->lpVtbl->RenameElement(This,pwcsOldName,pwcsNewName)
#define IStorage_SetElementTimes(This,pwcsName,pctime,patime,pmtime) (This)->lpVtbl->SetElementTimes(This,pwcsName,pctime,patime,pmtime)
#define IStorage_SetClass(This,clsid) (This)->lpVtbl->SetClass(This,clsid)
#define IStorage_SetStateBits(This,grfStateBits,grfMask) (This)->lpVtbl->SetStateBits(This,grfStateBits,grfMask)
#define IStorage_Stat(This,pstatstg,grfStatFlag) (This)->lpVtbl->Stat(This,pstatstg,grfStatFlag)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IStorage_QueryInterface(IStorage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IStorage_AddRef(IStorage* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IStorage_Release(IStorage* This) {
    return This->lpVtbl->Release(This);
}
/*** IStorage methods ***/
static FORCEINLINE HRESULT IStorage_CreateStream(IStorage* This,const OLECHAR *pwcsName,DWORD grfMode,DWORD reserved1,DWORD reserved2,IStream **ppstm) {
    return This->lpVtbl->CreateStream(This,pwcsName,grfMode,reserved1,reserved2,ppstm);
}
static FORCEINLINE HRESULT IStorage_OpenStream(IStorage* This,const OLECHAR *pwcsName,void *reserved1,DWORD grfMode,DWORD reserved2,IStream **ppstm) {
    return This->lpVtbl->OpenStream(This,pwcsName,reserved1,grfMode,reserved2,ppstm);
}
static FORCEINLINE HRESULT IStorage_CreateStorage(IStorage* This,const OLECHAR *pwcsName,DWORD grfMode,DWORD reserved1,DWORD reserved2,IStorage **ppstg) {
    return This->lpVtbl->CreateStorage(This,pwcsName,grfMode,reserved1,reserved2,ppstg);
}
static FORCEINLINE HRESULT IStorage_OpenStorage(IStorage* This,const OLECHAR *pwcsName,IStorage *pstgPriority,DWORD grfMode,SNB snbExclude,DWORD reserved,IStorage **ppstg) {
    return This->lpVtbl->OpenStorage(This,pwcsName,pstgPriority,grfMode,snbExclude,reserved,ppstg);
}
static FORCEINLINE HRESULT IStorage_CopyTo(IStorage* This,DWORD ciidExclude,const IID *rgiidExclude,SNB snbExclude,IStorage *pstgDest) {
    return This->lpVtbl->CopyTo(This,ciidExclude,rgiidExclude,snbExclude,pstgDest);
}
static FORCEINLINE HRESULT IStorage_MoveElementTo(IStorage* This,const OLECHAR *pwcsName,IStorage *pstgDest,const OLECHAR *pwcsNewName,DWORD grfFlags) {
    return This->lpVtbl->MoveElementTo(This,pwcsName,pstgDest,pwcsNewName,grfFlags);
}
static FORCEINLINE HRESULT IStorage_Commit(IStorage* This,DWORD grfCommitFlags) {
    return This->lpVtbl->Commit(This,grfCommitFlags);
}
static FORCEINLINE HRESULT IStorage_Revert(IStorage* This) {
    return This->lpVtbl->Revert(This);
}
static FORCEINLINE HRESULT IStorage_EnumElements(IStorage* This,DWORD reserved1,void *reserved2,DWORD reserved3,IEnumSTATSTG **ppenum) {
    return This->lpVtbl->EnumElements(This,reserved1,reserved2,reserved3,ppenum);
}
static FORCEINLINE HRESULT IStorage_DestroyElement(IStorage* This,const OLECHAR *pwcsName) {
    return This->lpVtbl->DestroyElement(This,pwcsName);
}
static FORCEINLINE HRESULT IStorage_RenameElement(IStorage* This,const OLECHAR *pwcsOldName,const OLECHAR *pwcsNewName) {
    return This->lpVtbl->RenameElement(This,pwcsOldName,pwcsNewName);
}
static FORCEINLINE HRESULT IStorage_SetElementTimes(IStorage* This,const OLECHAR *pwcsName,const FILETIME *pctime,const FILETIME *patime,const FILETIME *pmtime) {
    return This->lpVtbl->SetElementTimes(This,pwcsName,pctime,patime,pmtime);
}
static FORCEINLINE HRESULT IStorage_SetClass(IStorage* This,REFCLSID clsid) {
    return This->lpVtbl->SetClass(This,clsid);
}
static FORCEINLINE HRESULT IStorage_SetStateBits(IStorage* This,DWORD grfStateBits,DWORD grfMask) {
    return This->lpVtbl->SetStateBits(This,grfStateBits,grfMask);
}
static FORCEINLINE HRESULT IStorage_Stat(IStorage* This,STATSTG *pstatstg,DWORD grfStatFlag) {
    return This->lpVtbl->Stat(This,pstatstg,grfStatFlag);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IStorage_CreateStream_Proxy(
    IStorage* This,
    const OLECHAR *pwcsName,
    DWORD grfMode,
    DWORD reserved1,
    DWORD reserved2,
    IStream **ppstm);
void __RPC_STUB IStorage_CreateStream_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_RemoteOpenStream_Proxy(
    IStorage* This,
    const OLECHAR *pwcsName,
    ULONG cbReserved1,
    byte *reserved1,
    DWORD grfMode,
    DWORD reserved2,
    IStream **ppstm);
void __RPC_STUB IStorage_RemoteOpenStream_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_CreateStorage_Proxy(
    IStorage* This,
    const OLECHAR *pwcsName,
    DWORD grfMode,
    DWORD reserved1,
    DWORD reserved2,
    IStorage **ppstg);
void __RPC_STUB IStorage_CreateStorage_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_OpenStorage_Proxy(
    IStorage* This,
    const OLECHAR *pwcsName,
    IStorage *pstgPriority,
    DWORD grfMode,
    SNB snbExclude,
    DWORD reserved,
    IStorage **ppstg);
void __RPC_STUB IStorage_OpenStorage_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_RemoteCopyTo_Proxy(
    IStorage* This,
    DWORD ciidExclude,
    const IID *rgiidExclude,
    SNB snbExclude,
    IStorage *pstgDest);
void __RPC_STUB IStorage_RemoteCopyTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_MoveElementTo_Proxy(
    IStorage* This,
    const OLECHAR *pwcsName,
    IStorage *pstgDest,
    const OLECHAR *pwcsNewName,
    DWORD grfFlags);
void __RPC_STUB IStorage_MoveElementTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_Commit_Proxy(
    IStorage* This,
    DWORD grfCommitFlags);
void __RPC_STUB IStorage_Commit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_Revert_Proxy(
    IStorage* This);
void __RPC_STUB IStorage_Revert_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_RemoteEnumElements_Proxy(
    IStorage* This,
    DWORD reserved1,
    ULONG cbReserved2,
    byte *reserved2,
    DWORD reserved3,
    IEnumSTATSTG **ppenum);
void __RPC_STUB IStorage_RemoteEnumElements_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_DestroyElement_Proxy(
    IStorage* This,
    const OLECHAR *pwcsName);
void __RPC_STUB IStorage_DestroyElement_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_RenameElement_Proxy(
    IStorage* This,
    const OLECHAR *pwcsOldName,
    const OLECHAR *pwcsNewName);
void __RPC_STUB IStorage_RenameElement_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_SetElementTimes_Proxy(
    IStorage* This,
    const OLECHAR *pwcsName,
    const FILETIME *pctime,
    const FILETIME *patime,
    const FILETIME *pmtime);
void __RPC_STUB IStorage_SetElementTimes_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_SetClass_Proxy(
    IStorage* This,
    REFCLSID clsid);
void __RPC_STUB IStorage_SetClass_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_SetStateBits_Proxy(
    IStorage* This,
    DWORD grfStateBits,
    DWORD grfMask);
void __RPC_STUB IStorage_SetStateBits_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStorage_Stat_Proxy(
    IStorage* This,
    STATSTG *pstatstg,
    DWORD grfStatFlag);
void __RPC_STUB IStorage_Stat_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IStorage_OpenStream_Proxy(
    IStorage* This,
    const OLECHAR *pwcsName,
    void *reserved1,
    DWORD grfMode,
    DWORD reserved2,
    IStream **ppstm);
HRESULT __RPC_STUB IStorage_OpenStream_Stub(
    IStorage* This,
    const OLECHAR *pwcsName,
    ULONG cbReserved1,
    byte *reserved1,
    DWORD grfMode,
    DWORD reserved2,
    IStream **ppstm);
HRESULT CALLBACK IStorage_CopyTo_Proxy(
    IStorage* This,
    DWORD ciidExclude,
    const IID *rgiidExclude,
    SNB snbExclude,
    IStorage *pstgDest);
HRESULT __RPC_STUB IStorage_CopyTo_Stub(
    IStorage* This,
    DWORD ciidExclude,
    const IID *rgiidExclude,
    SNB snbExclude,
    IStorage *pstgDest);
HRESULT CALLBACK IStorage_EnumElements_Proxy(
    IStorage* This,
    DWORD reserved1,
    void *reserved2,
    DWORD reserved3,
    IEnumSTATSTG **ppenum);
HRESULT __RPC_STUB IStorage_EnumElements_Stub(
    IStorage* This,
    DWORD reserved1,
    ULONG cbReserved2,
    byte *reserved2,
    DWORD reserved3,
    IEnumSTATSTG **ppenum);

#endif  /* __IStorage_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IPersistFile interface
 */
#ifndef __IPersistFile_INTERFACE_DEFINED__
#define __IPersistFile_INTERFACE_DEFINED__

typedef IPersistFile *LPPERSISTFILE;
DEFINE_GUID(IID_IPersistFile, 0x0000010b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000010b-0000-0000-c000-000000000046")
IPersistFile : public IPersist
{
    virtual HRESULT STDMETHODCALLTYPE IsDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        LPCOLESTR pszFileName,
        DWORD dwMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        LPCOLESTR pszFileName,
        WINBOOL fRemember) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveCompleted(
        LPCOLESTR pszFileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurFile(
        LPOLESTR *ppszFileName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistFile, 0x0000010b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IPersistFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistFile* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistFile* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistFile* This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersistFile* This,
        CLSID *pClassID);

    /*** IPersistFile methods ***/
    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IPersistFile* This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IPersistFile* This,
        LPCOLESTR pszFileName,
        DWORD dwMode);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IPersistFile* This,
        LPCOLESTR pszFileName,
        WINBOOL fRemember);

    HRESULT (STDMETHODCALLTYPE *SaveCompleted)(
        IPersistFile* This,
        LPCOLESTR pszFileName);

    HRESULT (STDMETHODCALLTYPE *GetCurFile)(
        IPersistFile* This,
        LPOLESTR *ppszFileName);

    END_INTERFACE
} IPersistFileVtbl;
interface IPersistFile {
    CONST_VTBL IPersistFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistFile_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IPersistFile_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistFile methods ***/
#define IPersistFile_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#define IPersistFile_Load(This,pszFileName,dwMode) (This)->lpVtbl->Load(This,pszFileName,dwMode)
#define IPersistFile_Save(This,pszFileName,fRemember) (This)->lpVtbl->Save(This,pszFileName,fRemember)
#define IPersistFile_SaveCompleted(This,pszFileName) (This)->lpVtbl->SaveCompleted(This,pszFileName)
#define IPersistFile_GetCurFile(This,ppszFileName) (This)->lpVtbl->GetCurFile(This,ppszFileName)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPersistFile_QueryInterface(IPersistFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPersistFile_AddRef(IPersistFile* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPersistFile_Release(IPersistFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static FORCEINLINE HRESULT IPersistFile_GetClassID(IPersistFile* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistFile methods ***/
static FORCEINLINE HRESULT IPersistFile_IsDirty(IPersistFile* This) {
    return This->lpVtbl->IsDirty(This);
}
static FORCEINLINE HRESULT IPersistFile_Load(IPersistFile* This,LPCOLESTR pszFileName,DWORD dwMode) {
    return This->lpVtbl->Load(This,pszFileName,dwMode);
}
static FORCEINLINE HRESULT IPersistFile_Save(IPersistFile* This,LPCOLESTR pszFileName,WINBOOL fRemember) {
    return This->lpVtbl->Save(This,pszFileName,fRemember);
}
static FORCEINLINE HRESULT IPersistFile_SaveCompleted(IPersistFile* This,LPCOLESTR pszFileName) {
    return This->lpVtbl->SaveCompleted(This,pszFileName);
}
static FORCEINLINE HRESULT IPersistFile_GetCurFile(IPersistFile* This,LPOLESTR *ppszFileName) {
    return This->lpVtbl->GetCurFile(This,ppszFileName);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPersistFile_IsDirty_Proxy(
    IPersistFile* This);
void __RPC_STUB IPersistFile_IsDirty_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistFile_Load_Proxy(
    IPersistFile* This,
    LPCOLESTR pszFileName,
    DWORD dwMode);
void __RPC_STUB IPersistFile_Load_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistFile_Save_Proxy(
    IPersistFile* This,
    LPCOLESTR pszFileName,
    WINBOOL fRemember);
void __RPC_STUB IPersistFile_Save_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistFile_SaveCompleted_Proxy(
    IPersistFile* This,
    LPCOLESTR pszFileName);
void __RPC_STUB IPersistFile_SaveCompleted_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistFile_GetCurFile_Proxy(
    IPersistFile* This,
    LPOLESTR *ppszFileName);
void __RPC_STUB IPersistFile_GetCurFile_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPersistFile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPersistStorage interface
 */
#ifndef __IPersistStorage_INTERFACE_DEFINED__
#define __IPersistStorage_INTERFACE_DEFINED__

typedef IPersistStorage *LPPERSISTSTORAGE;
DEFINE_GUID(IID_IPersistStorage, 0x0000010a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000010a-0000-0000-c000-000000000046")
IPersistStorage : public IPersist
{
    virtual HRESULT STDMETHODCALLTYPE IsDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitNew(
        IStorage *pStg) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        IStorage *pStg) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        IStorage *pStgSave,
        WINBOOL fSameAsLoad) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveCompleted(
        IStorage *pStgNew) = 0;

    virtual HRESULT STDMETHODCALLTYPE HandsOffStorage(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistStorage, 0x0000010a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IPersistStorageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistStorage* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistStorage* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistStorage* This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersistStorage* This,
        CLSID *pClassID);

    /*** IPersistStorage methods ***/
    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IPersistStorage* This);

    HRESULT (STDMETHODCALLTYPE *InitNew)(
        IPersistStorage* This,
        IStorage *pStg);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IPersistStorage* This,
        IStorage *pStg);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IPersistStorage* This,
        IStorage *pStgSave,
        WINBOOL fSameAsLoad);

    HRESULT (STDMETHODCALLTYPE *SaveCompleted)(
        IPersistStorage* This,
        IStorage *pStgNew);

    HRESULT (STDMETHODCALLTYPE *HandsOffStorage)(
        IPersistStorage* This);

    END_INTERFACE
} IPersistStorageVtbl;
interface IPersistStorage {
    CONST_VTBL IPersistStorageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistStorage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistStorage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistStorage_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IPersistStorage_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistStorage methods ***/
#define IPersistStorage_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#define IPersistStorage_InitNew(This,pStg) (This)->lpVtbl->InitNew(This,pStg)
#define IPersistStorage_Load(This,pStg) (This)->lpVtbl->Load(This,pStg)
#define IPersistStorage_Save(This,pStgSave,fSameAsLoad) (This)->lpVtbl->Save(This,pStgSave,fSameAsLoad)
#define IPersistStorage_SaveCompleted(This,pStgNew) (This)->lpVtbl->SaveCompleted(This,pStgNew)
#define IPersistStorage_HandsOffStorage(This) (This)->lpVtbl->HandsOffStorage(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IPersistStorage_QueryInterface(IPersistStorage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IPersistStorage_AddRef(IPersistStorage* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IPersistStorage_Release(IPersistStorage* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static FORCEINLINE HRESULT IPersistStorage_GetClassID(IPersistStorage* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistStorage methods ***/
static FORCEINLINE HRESULT IPersistStorage_IsDirty(IPersistStorage* This) {
    return This->lpVtbl->IsDirty(This);
}
static FORCEINLINE HRESULT IPersistStorage_InitNew(IPersistStorage* This,IStorage *pStg) {
    return This->lpVtbl->InitNew(This,pStg);
}
static FORCEINLINE HRESULT IPersistStorage_Load(IPersistStorage* This,IStorage *pStg) {
    return This->lpVtbl->Load(This,pStg);
}
static FORCEINLINE HRESULT IPersistStorage_Save(IPersistStorage* This,IStorage *pStgSave,WINBOOL fSameAsLoad) {
    return This->lpVtbl->Save(This,pStgSave,fSameAsLoad);
}
static FORCEINLINE HRESULT IPersistStorage_SaveCompleted(IPersistStorage* This,IStorage *pStgNew) {
    return This->lpVtbl->SaveCompleted(This,pStgNew);
}
static FORCEINLINE HRESULT IPersistStorage_HandsOffStorage(IPersistStorage* This) {
    return This->lpVtbl->HandsOffStorage(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPersistStorage_IsDirty_Proxy(
    IPersistStorage* This);
void __RPC_STUB IPersistStorage_IsDirty_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistStorage_InitNew_Proxy(
    IPersistStorage* This,
    IStorage *pStg);
void __RPC_STUB IPersistStorage_InitNew_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistStorage_Load_Proxy(
    IPersistStorage* This,
    IStorage *pStg);
void __RPC_STUB IPersistStorage_Load_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistStorage_Save_Proxy(
    IPersistStorage* This,
    IStorage *pStgSave,
    WINBOOL fSameAsLoad);
void __RPC_STUB IPersistStorage_Save_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistStorage_SaveCompleted_Proxy(
    IPersistStorage* This,
    IStorage *pStgNew);
void __RPC_STUB IPersistStorage_SaveCompleted_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistStorage_HandsOffStorage_Proxy(
    IPersistStorage* This);
void __RPC_STUB IPersistStorage_HandsOffStorage_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IPersistStorage_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * ILockBytes interface
 */
#ifndef __ILockBytes_INTERFACE_DEFINED__
#define __ILockBytes_INTERFACE_DEFINED__

typedef ILockBytes *LPLOCKBYTES;
DEFINE_GUID(IID_ILockBytes, 0x0000000a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000000a-0000-0000-c000-000000000046")
ILockBytes : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ReadAt(
        ULARGE_INTEGER ulOffset,
        void *pv,
        ULONG cb,
        ULONG *pcbRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteAt(
        ULARGE_INTEGER ulOffset,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSize(
        ULARGE_INTEGER cb) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockRegion(
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockRegion(
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stat(
        STATSTG *pstatstg,
        DWORD grfStatFlag) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ILockBytes, 0x0000000a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ILockBytesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ILockBytes* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ILockBytes* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ILockBytes* This);

    /*** ILockBytes methods ***/
    HRESULT (STDMETHODCALLTYPE *ReadAt)(
        ILockBytes* This,
        ULARGE_INTEGER ulOffset,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *WriteAt)(
        ILockBytes* This,
        ULARGE_INTEGER ulOffset,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        ILockBytes* This);

    HRESULT (STDMETHODCALLTYPE *SetSize)(
        ILockBytes* This,
        ULARGE_INTEGER cb);

    HRESULT (STDMETHODCALLTYPE *LockRegion)(
        ILockBytes* This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *UnlockRegion)(
        ILockBytes* This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *Stat)(
        ILockBytes* This,
        STATSTG *pstatstg,
        DWORD grfStatFlag);

    END_INTERFACE
} ILockBytesVtbl;
interface ILockBytes {
    CONST_VTBL ILockBytesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ILockBytes_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ILockBytes_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ILockBytes_Release(This) (This)->lpVtbl->Release(This)
/*** ILockBytes methods ***/
#define ILockBytes_ReadAt(This,ulOffset,pv,cb,pcbRead) (This)->lpVtbl->ReadAt(This,ulOffset,pv,cb,pcbRead)
#define ILockBytes_WriteAt(This,ulOffset,pv,cb,pcbWritten) (This)->lpVtbl->WriteAt(This,ulOffset,pv,cb,pcbWritten)
#define ILockBytes_Flush(This) (This)->lpVtbl->Flush(This)
#define ILockBytes_SetSize(This,cb) (This)->lpVtbl->SetSize(This,cb)
#define ILockBytes_LockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->LockRegion(This,libOffset,cb,dwLockType)
#define ILockBytes_UnlockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType)
#define ILockBytes_Stat(This,pstatstg,grfStatFlag) (This)->lpVtbl->Stat(This,pstatstg,grfStatFlag)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ILockBytes_QueryInterface(ILockBytes* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ILockBytes_AddRef(ILockBytes* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ILockBytes_Release(ILockBytes* This) {
    return This->lpVtbl->Release(This);
}
/*** ILockBytes methods ***/
static FORCEINLINE HRESULT ILockBytes_ReadAt(ILockBytes* This,ULARGE_INTEGER ulOffset,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->ReadAt(This,ulOffset,pv,cb,pcbRead);
}
static FORCEINLINE HRESULT ILockBytes_WriteAt(ILockBytes* This,ULARGE_INTEGER ulOffset,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->WriteAt(This,ulOffset,pv,cb,pcbWritten);
}
static FORCEINLINE HRESULT ILockBytes_Flush(ILockBytes* This) {
    return This->lpVtbl->Flush(This);
}
static FORCEINLINE HRESULT ILockBytes_SetSize(ILockBytes* This,ULARGE_INTEGER cb) {
    return This->lpVtbl->SetSize(This,cb);
}
static FORCEINLINE HRESULT ILockBytes_LockRegion(ILockBytes* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->LockRegion(This,libOffset,cb,dwLockType);
}
static FORCEINLINE HRESULT ILockBytes_UnlockRegion(ILockBytes* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType);
}
static FORCEINLINE HRESULT ILockBytes_Stat(ILockBytes* This,STATSTG *pstatstg,DWORD grfStatFlag) {
    return This->lpVtbl->Stat(This,pstatstg,grfStatFlag);
}
#endif
#endif

#endif

HRESULT __stdcall ILockBytes_RemoteReadAt_Proxy(
    ILockBytes* This,
    ULARGE_INTEGER ulOffset,
    byte *pv,
    ULONG cb,
    ULONG *pcbRead);
void __RPC_STUB ILockBytes_RemoteReadAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ILockBytes_RemoteWriteAt_Proxy(
    ILockBytes* This,
    ULARGE_INTEGER ulOffset,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);
void __RPC_STUB ILockBytes_RemoteWriteAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ILockBytes_Flush_Proxy(
    ILockBytes* This);
void __RPC_STUB ILockBytes_Flush_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ILockBytes_SetSize_Proxy(
    ILockBytes* This,
    ULARGE_INTEGER cb);
void __RPC_STUB ILockBytes_SetSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ILockBytes_LockRegion_Proxy(
    ILockBytes* This,
    ULARGE_INTEGER libOffset,
    ULARGE_INTEGER cb,
    DWORD dwLockType);
void __RPC_STUB ILockBytes_LockRegion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ILockBytes_UnlockRegion_Proxy(
    ILockBytes* This,
    ULARGE_INTEGER libOffset,
    ULARGE_INTEGER cb,
    DWORD dwLockType);
void __RPC_STUB ILockBytes_UnlockRegion_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ILockBytes_Stat_Proxy(
    ILockBytes* This,
    STATSTG *pstatstg,
    DWORD grfStatFlag);
void __RPC_STUB ILockBytes_Stat_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK ILockBytes_ReadAt_Proxy(
    ILockBytes* This,
    ULARGE_INTEGER ulOffset,
    void *pv,
    ULONG cb,
    ULONG *pcbRead);
HRESULT __RPC_STUB ILockBytes_ReadAt_Stub(
    ILockBytes* This,
    ULARGE_INTEGER ulOffset,
    byte *pv,
    ULONG cb,
    ULONG *pcbRead);
HRESULT CALLBACK ILockBytes_WriteAt_Proxy(
    ILockBytes* This,
    ULARGE_INTEGER ulOffset,
    const void *pv,
    ULONG cb,
    ULONG *pcbWritten);
HRESULT __RPC_STUB ILockBytes_WriteAt_Stub(
    ILockBytes* This,
    ULARGE_INTEGER ulOffset,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);

#endif  /* __ILockBytes_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumFORMATETC interface
 */
#ifndef __IEnumFORMATETC_INTERFACE_DEFINED__
#define __IEnumFORMATETC_INTERFACE_DEFINED__

typedef IEnumFORMATETC *LPENUMFORMATETC;
typedef struct tagDVTARGETDEVICE {
    DWORD tdSize;
    WORD tdDriverNameOffset;
    WORD tdDeviceNameOffset;
    WORD tdPortNameOffset;
    WORD tdExtDevmodeOffset;
    BYTE tdData[1];
} DVTARGETDEVICE;
typedef CLIPFORMAT *LPCLIPFORMAT;
typedef struct tagFORMATETC {
    CLIPFORMAT cfFormat;
    DVTARGETDEVICE *ptd;
    DWORD dwAspect;
    LONG lindex;
    DWORD tymed;
} FORMATETC;
typedef struct tagFORMATETC *LPFORMATETC;
DEFINE_GUID(IID_IEnumFORMATETC, 0x00000103, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000103-0000-0000-c000-000000000046")
IEnumFORMATETC : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        FORMATETC *rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumFORMATETC **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumFORMATETC, 0x00000103, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumFORMATETCVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumFORMATETC* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumFORMATETC* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumFORMATETC* This);

    /*** IEnumFORMATETC methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumFORMATETC* This,
        ULONG celt,
        FORMATETC *rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumFORMATETC* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumFORMATETC* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumFORMATETC* This,
        IEnumFORMATETC **ppenum);

    END_INTERFACE
} IEnumFORMATETCVtbl;
interface IEnumFORMATETC {
    CONST_VTBL IEnumFORMATETCVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumFORMATETC_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumFORMATETC_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumFORMATETC_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumFORMATETC methods ***/
#define IEnumFORMATETC_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumFORMATETC_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumFORMATETC_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumFORMATETC_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumFORMATETC_QueryInterface(IEnumFORMATETC* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumFORMATETC_AddRef(IEnumFORMATETC* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumFORMATETC_Release(IEnumFORMATETC* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumFORMATETC methods ***/
static FORCEINLINE HRESULT IEnumFORMATETC_Next(IEnumFORMATETC* This,ULONG celt,FORMATETC *rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static FORCEINLINE HRESULT IEnumFORMATETC_Skip(IEnumFORMATETC* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumFORMATETC_Reset(IEnumFORMATETC* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumFORMATETC_Clone(IEnumFORMATETC* This,IEnumFORMATETC **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumFORMATETC_RemoteNext_Proxy(
    IEnumFORMATETC* This,
    ULONG celt,
    FORMATETC *rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumFORMATETC_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumFORMATETC_Skip_Proxy(
    IEnumFORMATETC* This,
    ULONG celt);
void __RPC_STUB IEnumFORMATETC_Skip_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumFORMATETC_Reset_Proxy(
    IEnumFORMATETC* This);
void __RPC_STUB IEnumFORMATETC_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumFORMATETC_Clone_Proxy(
    IEnumFORMATETC* This,
    IEnumFORMATETC **ppenum);
void __RPC_STUB IEnumFORMATETC_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumFORMATETC_Next_Proxy(
    IEnumFORMATETC* This,
    ULONG celt,
    FORMATETC *rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumFORMATETC_Next_Stub(
    IEnumFORMATETC* This,
    ULONG celt,
    FORMATETC *rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumFORMATETC_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumSTATDATA interface
 */
#ifndef __IEnumSTATDATA_INTERFACE_DEFINED__
#define __IEnumSTATDATA_INTERFACE_DEFINED__

typedef IEnumSTATDATA *LPENUMSTATDATA;
typedef enum tagADVF {
    ADVF_NODATA = 1,
    ADVF_PRIMEFIRST = 2,
    ADVF_ONLYONCE = 4,
    ADVF_DATAONSTOP = 64,
    ADVFCACHE_NOHANDLER = 8,
    ADVFCACHE_FORCEBUILTIN = 16,
    ADVFCACHE_ONSAVE = 32
} ADVF;
typedef struct tagSTATDATA {
    FORMATETC formatetc;
    DWORD advf;
    IAdviseSink *pAdvSink;
    DWORD dwConnection;
} STATDATA;
typedef STATDATA *LPSTATDATA;
DEFINE_GUID(IID_IEnumSTATDATA, 0x00000105, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000105-0000-0000-c000-000000000046")
IEnumSTATDATA : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        STATDATA *rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumSTATDATA **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumSTATDATA, 0x00000105, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumSTATDATAVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumSTATDATA* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumSTATDATA* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumSTATDATA* This);

    /*** IEnumSTATDATA methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumSTATDATA* This,
        ULONG celt,
        STATDATA *rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumSTATDATA* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumSTATDATA* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumSTATDATA* This,
        IEnumSTATDATA **ppenum);

    END_INTERFACE
} IEnumSTATDATAVtbl;
interface IEnumSTATDATA {
    CONST_VTBL IEnumSTATDATAVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumSTATDATA_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumSTATDATA_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumSTATDATA_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumSTATDATA methods ***/
#define IEnumSTATDATA_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumSTATDATA_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumSTATDATA_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumSTATDATA_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumSTATDATA_QueryInterface(IEnumSTATDATA* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumSTATDATA_AddRef(IEnumSTATDATA* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumSTATDATA_Release(IEnumSTATDATA* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumSTATDATA methods ***/
static FORCEINLINE HRESULT IEnumSTATDATA_Next(IEnumSTATDATA* This,ULONG celt,STATDATA *rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static FORCEINLINE HRESULT IEnumSTATDATA_Skip(IEnumSTATDATA* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumSTATDATA_Reset(IEnumSTATDATA* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumSTATDATA_Clone(IEnumSTATDATA* This,IEnumSTATDATA **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumSTATDATA_RemoteNext_Proxy(
    IEnumSTATDATA* This,
    ULONG celt,
    STATDATA *rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumSTATDATA_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumSTATDATA_Skip_Proxy(
    IEnumSTATDATA* This,
    ULONG celt);
void __RPC_STUB IEnumSTATDATA_Skip_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumSTATDATA_Reset_Proxy(
    IEnumSTATDATA* This);
void __RPC_STUB IEnumSTATDATA_Reset_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumSTATDATA_Clone_Proxy(
    IEnumSTATDATA* This,
    IEnumSTATDATA **ppenum);
void __RPC_STUB IEnumSTATDATA_Clone_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumSTATDATA_Next_Proxy(
    IEnumSTATDATA* This,
    ULONG celt,
    STATDATA *rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumSTATDATA_Next_Stub(
    IEnumSTATDATA* This,
    ULONG celt,
    STATDATA *rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumSTATDATA_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRootStorage interface
 */
#ifndef __IRootStorage_INTERFACE_DEFINED__
#define __IRootStorage_INTERFACE_DEFINED__

typedef IRootStorage *LPROOTSTORAGE;
DEFINE_GUID(IID_IRootStorage, 0x00000012, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000012-0000-0000-c000-000000000046")
IRootStorage : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SwitchToFile(
        LPOLESTR pszFile) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRootStorage, 0x00000012, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IRootStorageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRootStorage* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRootStorage* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRootStorage* This);

    /*** IRootStorage methods ***/
    HRESULT (STDMETHODCALLTYPE *SwitchToFile)(
        IRootStorage* This,
        LPOLESTR pszFile);

    END_INTERFACE
} IRootStorageVtbl;
interface IRootStorage {
    CONST_VTBL IRootStorageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRootStorage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRootStorage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRootStorage_Release(This) (This)->lpVtbl->Release(This)
/*** IRootStorage methods ***/
#define IRootStorage_SwitchToFile(This,pszFile) (This)->lpVtbl->SwitchToFile(This,pszFile)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IRootStorage_QueryInterface(IRootStorage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IRootStorage_AddRef(IRootStorage* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IRootStorage_Release(IRootStorage* This) {
    return This->lpVtbl->Release(This);
}
/*** IRootStorage methods ***/
static FORCEINLINE HRESULT IRootStorage_SwitchToFile(IRootStorage* This,LPOLESTR pszFile) {
    return This->lpVtbl->SwitchToFile(This,pszFile);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IRootStorage_SwitchToFile_Proxy(
    IRootStorage* This,
    LPOLESTR pszFile);
void __RPC_STUB IRootStorage_SwitchToFile_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IRootStorage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAdviseSink interface
 */
#ifndef __IAdviseSink_INTERFACE_DEFINED__
#define __IAdviseSink_INTERFACE_DEFINED__

typedef IAdviseSink *LPADVISESINK;
typedef enum tagTYMED {
    TYMED_HGLOBAL = 1,
    TYMED_FILE = 2,
    TYMED_ISTREAM = 4,
    TYMED_ISTORAGE = 8,
    TYMED_GDI = 16,
    TYMED_MFPICT = 32,
    TYMED_ENHMF = 64,
    TYMED_NULL = 0
} TYMED;
typedef struct tagRemSTGMEDIUM {
    DWORD tymed;
    DWORD dwHandleType;
    ULONG pData;
    ULONG pUnkForRelease;
    ULONG cbData;
    byte data[1];
} RemSTGMEDIUM;
#ifdef NONAMELESSUNION
typedef struct tagSTGMEDIUM {
DWORD tymed;
union {
HBITMAP hBitmap;
HMETAFILEPICT hMetaFilePict;
HENHMETAFILE hEnhMetaFile;
HGLOBAL hGlobal;
LPOLESTR lpszFileName;
IStream *pstm;
IStorage *pstg;
} u;
IUnknown *pUnkForRelease;
}uSTGMEDIUM;
#else
typedef struct tagSTGMEDIUM {
    DWORD tymed;
    __C89_NAMELESS union {
        HBITMAP hBitmap;
        HMETAFILEPICT hMetaFilePict;
        HENHMETAFILE hEnhMetaFile;
        HGLOBAL hGlobal;
        LPOLESTR lpszFileName;
        IStream *pstm;
        IStorage *pstg;
    } DUMMYUNIONNAME;
    IUnknown *pUnkForRelease;
} uSTGMEDIUM;
#endif
typedef struct _GDI_OBJECT {
    DWORD ObjectType;
    union {
        wireHBITMAP hBitmap;
        wireHPALETTE hPalette;
        wireHGLOBAL hGeneric;
    } u;
} GDI_OBJECT;
typedef struct _userSTGMEDIUM {
    __C89_NAMELESS struct _STGMEDIUM_UNION {
        DWORD tymed;
        union {
            wireHMETAFILEPICT hMetaFilePict;
            wireHENHMETAFILE hHEnhMetaFile;
            GDI_OBJECT *hGdiHandle;
            wireHGLOBAL hGlobal;
            LPOLESTR lpszFileName;
            BYTE_BLOB *pstm;
            BYTE_BLOB *pstg;
        } u;
    } DUMMYUNIONNAME;
    IUnknown *pUnkForRelease;
} userSTGMEDIUM;
typedef userSTGMEDIUM *wireSTGMEDIUM;
typedef uSTGMEDIUM STGMEDIUM;
typedef userSTGMEDIUM *wireASYNC_STGMEDIUM;
typedef STGMEDIUM ASYNC_STGMEDIUM;
typedef STGMEDIUM *LPSTGMEDIUM;
typedef struct _userFLAG_STGMEDIUM {
    LONG ContextFlags;
    LONG fPassOwnership;
    userSTGMEDIUM Stgmed;
} userFLAG_STGMEDIUM;
typedef userFLAG_STGMEDIUM *wireFLAG_STGMEDIUM;
typedef struct _FLAG_STGMEDIUM {
    LONG ContextFlags;
    LONG fPassOwnership;
    STGMEDIUM Stgmed;
} FLAG_STGMEDIUM;
DEFINE_GUID(IID_IAdviseSink, 0x0000010f, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000010f-0000-0000-c000-000000000046")
IAdviseSink : public IUnknown
{
    virtual void STDMETHODCALLTYPE OnDataChange(
        FORMATETC *pFormatetc,
        STGMEDIUM *pStgmed) = 0;

    virtual void STDMETHODCALLTYPE OnViewChange(
        DWORD dwAspect,
        LONG lindex) = 0;

    virtual void STDMETHODCALLTYPE OnRename(
        IMoniker *pmk) = 0;

    virtual void STDMETHODCALLTYPE OnSave(
        ) = 0;

    virtual void STDMETHODCALLTYPE OnClose(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAdviseSink, 0x0000010f, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IAdviseSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAdviseSink* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAdviseSink* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAdviseSink* This);

    /*** IAdviseSink methods ***/
    void (STDMETHODCALLTYPE *OnDataChange)(
        IAdviseSink* This,
        FORMATETC *pFormatetc,
        STGMEDIUM *pStgmed);

    void (STDMETHODCALLTYPE *OnViewChange)(
        IAdviseSink* This,
        DWORD dwAspect,
        LONG lindex);

    void (STDMETHODCALLTYPE *OnRename)(
        IAdviseSink* This,
        IMoniker *pmk);

    void (STDMETHODCALLTYPE *OnSave)(
        IAdviseSink* This);

    void (STDMETHODCALLTYPE *OnClose)(
        IAdviseSink* This);

    END_INTERFACE
} IAdviseSinkVtbl;
interface IAdviseSink {
    CONST_VTBL IAdviseSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAdviseSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAdviseSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAdviseSink_Release(This) (This)->lpVtbl->Release(This)
/*** IAdviseSink methods ***/
#define IAdviseSink_OnDataChange(This,pFormatetc,pStgmed) (This)->lpVtbl->OnDataChange(This,pFormatetc,pStgmed)
#define IAdviseSink_OnViewChange(This,dwAspect,lindex) (This)->lpVtbl->OnViewChange(This,dwAspect,lindex)
#define IAdviseSink_OnRename(This,pmk) (This)->lpVtbl->OnRename(This,pmk)
#define IAdviseSink_OnSave(This) (This)->lpVtbl->OnSave(This)
#define IAdviseSink_OnClose(This) (This)->lpVtbl->OnClose(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAdviseSink_QueryInterface(IAdviseSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAdviseSink_AddRef(IAdviseSink* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAdviseSink_Release(IAdviseSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IAdviseSink methods ***/
static FORCEINLINE void IAdviseSink_OnDataChange(IAdviseSink* This,FORMATETC *pFormatetc,STGMEDIUM *pStgmed) {
    This->lpVtbl->OnDataChange(This,pFormatetc,pStgmed);
}
static FORCEINLINE void IAdviseSink_OnViewChange(IAdviseSink* This,DWORD dwAspect,LONG lindex) {
    This->lpVtbl->OnViewChange(This,dwAspect,lindex);
}
static FORCEINLINE void IAdviseSink_OnRename(IAdviseSink* This,IMoniker *pmk) {
    This->lpVtbl->OnRename(This,pmk);
}
static FORCEINLINE void IAdviseSink_OnSave(IAdviseSink* This) {
    This->lpVtbl->OnSave(This);
}
static FORCEINLINE void IAdviseSink_OnClose(IAdviseSink* This) {
    This->lpVtbl->OnClose(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAdviseSink_RemoteOnDataChange_Proxy(
    IAdviseSink* This,
    FORMATETC *pFormatetc,
    ASYNC_STGMEDIUM *pStgmed);
void __RPC_STUB IAdviseSink_RemoteOnDataChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAdviseSink_RemoteOnViewChange_Proxy(
    IAdviseSink* This,
    DWORD dwAspect,
    LONG lindex);
void __RPC_STUB IAdviseSink_RemoteOnViewChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAdviseSink_RemoteOnRename_Proxy(
    IAdviseSink* This,
    IMoniker *pmk);
void __RPC_STUB IAdviseSink_RemoteOnRename_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAdviseSink_RemoteOnSave_Proxy(
    IAdviseSink* This);
void __RPC_STUB IAdviseSink_RemoteOnSave_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAdviseSink_RemoteOnClose_Proxy(
    IAdviseSink* This);
void __RPC_STUB IAdviseSink_RemoteOnClose_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void CALLBACK IAdviseSink_OnDataChange_Proxy(
    IAdviseSink* This,
    FORMATETC *pFormatetc,
    STGMEDIUM *pStgmed);
HRESULT __RPC_STUB IAdviseSink_OnDataChange_Stub(
    IAdviseSink* This,
    FORMATETC *pFormatetc,
    ASYNC_STGMEDIUM *pStgmed);
void CALLBACK IAdviseSink_OnViewChange_Proxy(
    IAdviseSink* This,
    DWORD dwAspect,
    LONG lindex);
HRESULT __RPC_STUB IAdviseSink_OnViewChange_Stub(
    IAdviseSink* This,
    DWORD dwAspect,
    LONG lindex);
void CALLBACK IAdviseSink_OnRename_Proxy(
    IAdviseSink* This,
    IMoniker *pmk);
HRESULT __RPC_STUB IAdviseSink_OnRename_Stub(
    IAdviseSink* This,
    IMoniker *pmk);
void CALLBACK IAdviseSink_OnSave_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB IAdviseSink_OnSave_Stub(
    IAdviseSink* This);
void CALLBACK IAdviseSink_OnClose_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB IAdviseSink_OnClose_Stub(
    IAdviseSink* This);

#endif  /* __IAdviseSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * AsyncIAdviseSink interface
 */
#ifndef __AsyncIAdviseSink_INTERFACE_DEFINED__
#define __AsyncIAdviseSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_AsyncIAdviseSink, 0x00000150, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000150-0000-0000-c000-000000000046")
AsyncIAdviseSink : public IUnknown
{
    virtual void STDMETHODCALLTYPE Begin_OnDataChange(
        FORMATETC *pFormatetc,
        STGMEDIUM *pStgmed) = 0;

    virtual void STDMETHODCALLTYPE Finish_OnDataChange(
        ) = 0;

    virtual void STDMETHODCALLTYPE Begin_OnViewChange(
        DWORD dwAspect,
        LONG lindex) = 0;

    virtual void STDMETHODCALLTYPE Finish_OnViewChange(
        ) = 0;

    virtual void STDMETHODCALLTYPE Begin_OnRename(
        IMoniker *pmk) = 0;

    virtual void STDMETHODCALLTYPE Finish_OnRename(
        ) = 0;

    virtual void STDMETHODCALLTYPE Begin_OnSave(
        ) = 0;

    virtual void STDMETHODCALLTYPE Finish_OnSave(
        ) = 0;

    virtual void STDMETHODCALLTYPE Begin_OnClose(
        ) = 0;

    virtual void STDMETHODCALLTYPE Finish_OnClose(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AsyncIAdviseSink, 0x00000150, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct AsyncIAdviseSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        AsyncIAdviseSink* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        AsyncIAdviseSink* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        AsyncIAdviseSink* This);

    /*** IAdviseSink methods ***/
    void (STDMETHODCALLTYPE *Begin_OnDataChange)(
        AsyncIAdviseSink* This,
        FORMATETC *pFormatetc,
        STGMEDIUM *pStgmed);

    void (STDMETHODCALLTYPE *Finish_OnDataChange)(
        AsyncIAdviseSink* This);

    void (STDMETHODCALLTYPE *Begin_OnViewChange)(
        AsyncIAdviseSink* This,
        DWORD dwAspect,
        LONG lindex);

    void (STDMETHODCALLTYPE *Finish_OnViewChange)(
        AsyncIAdviseSink* This);

    void (STDMETHODCALLTYPE *Begin_OnRename)(
        AsyncIAdviseSink* This,
        IMoniker *pmk);

    void (STDMETHODCALLTYPE *Finish_OnRename)(
        AsyncIAdviseSink* This);

    void (STDMETHODCALLTYPE *Begin_OnSave)(
        AsyncIAdviseSink* This);

    void (STDMETHODCALLTYPE *Finish_OnSave)(
        AsyncIAdviseSink* This);

    void (STDMETHODCALLTYPE *Begin_OnClose)(
        AsyncIAdviseSink* This);

    void (STDMETHODCALLTYPE *Finish_OnClose)(
        AsyncIAdviseSink* This);

    END_INTERFACE
} AsyncIAdviseSinkVtbl;
interface AsyncIAdviseSink {
    CONST_VTBL AsyncIAdviseSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define AsyncIAdviseSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define AsyncIAdviseSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define AsyncIAdviseSink_Release(This) (This)->lpVtbl->Release(This)
/*** IAdviseSink methods ***/
#define AsyncIAdviseSink_Begin_OnDataChange(This,pFormatetc,pStgmed) (This)->lpVtbl->Begin_OnDataChange(This,pFormatetc,pStgmed)
#define AsyncIAdviseSink_Finish_OnDataChange(This) (This)->lpVtbl->Finish_OnDataChange(This)
#define AsyncIAdviseSink_Begin_OnViewChange(This,dwAspect,lindex) (This)->lpVtbl->Begin_OnViewChange(This,dwAspect,lindex)
#define AsyncIAdviseSink_Finish_OnViewChange(This) (This)->lpVtbl->Finish_OnViewChange(This)
#define AsyncIAdviseSink_Begin_OnRename(This,pmk) (This)->lpVtbl->Begin_OnRename(This,pmk)
#define AsyncIAdviseSink_Finish_OnRename(This) (This)->lpVtbl->Finish_OnRename(This)
#define AsyncIAdviseSink_Begin_OnSave(This) (This)->lpVtbl->Begin_OnSave(This)
#define AsyncIAdviseSink_Finish_OnSave(This) (This)->lpVtbl->Finish_OnSave(This)
#define AsyncIAdviseSink_Begin_OnClose(This) (This)->lpVtbl->Begin_OnClose(This)
#define AsyncIAdviseSink_Finish_OnClose(This) (This)->lpVtbl->Finish_OnClose(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT AsyncIAdviseSink_QueryInterface(AsyncIAdviseSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG AsyncIAdviseSink_AddRef(AsyncIAdviseSink* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG AsyncIAdviseSink_Release(AsyncIAdviseSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IAdviseSink methods ***/
static FORCEINLINE void Begin_AsyncIAdviseSink_OnDataChange(AsyncIAdviseSink* This,FORMATETC *pFormatetc,STGMEDIUM *pStgmed) {
    This->lpVtbl->Begin_OnDataChange(This,pFormatetc,pStgmed);
}
static FORCEINLINE void Finish_AsyncIAdviseSink_OnDataChange(AsyncIAdviseSink* This) {
    This->lpVtbl->Finish_OnDataChange(This);
}
static FORCEINLINE void Begin_AsyncIAdviseSink_OnViewChange(AsyncIAdviseSink* This,DWORD dwAspect,LONG lindex) {
    This->lpVtbl->Begin_OnViewChange(This,dwAspect,lindex);
}
static FORCEINLINE void Finish_AsyncIAdviseSink_OnViewChange(AsyncIAdviseSink* This) {
    This->lpVtbl->Finish_OnViewChange(This);
}
static FORCEINLINE void Begin_AsyncIAdviseSink_OnRename(AsyncIAdviseSink* This,IMoniker *pmk) {
    This->lpVtbl->Begin_OnRename(This,pmk);
}
static FORCEINLINE void Finish_AsyncIAdviseSink_OnRename(AsyncIAdviseSink* This) {
    This->lpVtbl->Finish_OnRename(This);
}
static FORCEINLINE void Begin_AsyncIAdviseSink_OnSave(AsyncIAdviseSink* This) {
    This->lpVtbl->Begin_OnSave(This);
}
static FORCEINLINE void Finish_AsyncIAdviseSink_OnSave(AsyncIAdviseSink* This) {
    This->lpVtbl->Finish_OnSave(This);
}
static FORCEINLINE void Begin_AsyncIAdviseSink_OnClose(AsyncIAdviseSink* This) {
    This->lpVtbl->Begin_OnClose(This);
}
static FORCEINLINE void Finish_AsyncIAdviseSink_OnClose(AsyncIAdviseSink* This) {
    This->lpVtbl->Finish_OnClose(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Begin_RemoteOnDataChange_Proxy(
    IAdviseSink* This,
    FORMATETC *pFormatetc,
    ASYNC_STGMEDIUM *pStgmed);
void __RPC_STUB AsyncIAdviseSink_Begin_RemoteOnDataChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Finish_RemoteOnDataChange_Proxy(
    IAdviseSink* This,
    FORMATETC *pFormatetc,
    ASYNC_STGMEDIUM *pStgmed);
void __RPC_STUB AsyncIAdviseSink_Finish_RemoteOnDataChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Begin_RemoteOnViewChange_Proxy(
    IAdviseSink* This,
    DWORD dwAspect,
    LONG lindex);
void __RPC_STUB AsyncIAdviseSink_Begin_RemoteOnViewChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Finish_RemoteOnViewChange_Proxy(
    IAdviseSink* This,
    DWORD dwAspect,
    LONG lindex);
void __RPC_STUB AsyncIAdviseSink_Finish_RemoteOnViewChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Begin_RemoteOnRename_Proxy(
    IAdviseSink* This,
    IMoniker *pmk);
void __RPC_STUB AsyncIAdviseSink_Begin_RemoteOnRename_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Finish_RemoteOnRename_Proxy(
    IAdviseSink* This,
    IMoniker *pmk);
void __RPC_STUB AsyncIAdviseSink_Finish_RemoteOnRename_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Begin_RemoteOnSave_Proxy(
    IAdviseSink* This);
void __RPC_STUB AsyncIAdviseSink_Begin_RemoteOnSave_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Finish_RemoteOnSave_Proxy(
    IAdviseSink* This);
void __RPC_STUB AsyncIAdviseSink_Finish_RemoteOnSave_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Begin_RemoteOnClose_Proxy(
    IAdviseSink* This);
void __RPC_STUB AsyncIAdviseSink_Begin_RemoteOnClose_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink_Finish_RemoteOnClose_Proxy(
    IAdviseSink* This);
void __RPC_STUB AsyncIAdviseSink_Finish_RemoteOnClose_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void CALLBACK AsyncIAdviseSink_Begin_OnDataChange_Proxy(
    IAdviseSink* This,
    FORMATETC *pFormatetc,
    STGMEDIUM *pStgmed);
HRESULT __RPC_STUB AsyncIAdviseSink_Begin_OnDataChange_Stub(
    IAdviseSink* This,
    FORMATETC *pFormatetc,
    ASYNC_STGMEDIUM *pStgmed);
void CALLBACK AsyncIAdviseSink_Finish_OnDataChange_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB AsyncIAdviseSink_Finish_OnDataChange_Stub(
    IAdviseSink* This,
    FORMATETC *pFormatetc,
    ASYNC_STGMEDIUM *pStgmed);
void CALLBACK AsyncIAdviseSink_Begin_OnViewChange_Proxy(
    IAdviseSink* This,
    DWORD dwAspect,
    LONG lindex);
HRESULT __RPC_STUB AsyncIAdviseSink_Begin_OnViewChange_Stub(
    IAdviseSink* This,
    DWORD dwAspect,
    LONG lindex);
void CALLBACK AsyncIAdviseSink_Finish_OnViewChange_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB AsyncIAdviseSink_Finish_OnViewChange_Stub(
    IAdviseSink* This,
    DWORD dwAspect,
    LONG lindex);
void CALLBACK AsyncIAdviseSink_Begin_OnRename_Proxy(
    IAdviseSink* This,
    IMoniker *pmk);
HRESULT __RPC_STUB AsyncIAdviseSink_Begin_OnRename_Stub(
    IAdviseSink* This,
    IMoniker *pmk);
void CALLBACK AsyncIAdviseSink_Finish_OnRename_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB AsyncIAdviseSink_Finish_OnRename_Stub(
    IAdviseSink* This,
    IMoniker *pmk);
void CALLBACK AsyncIAdviseSink_Begin_OnSave_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB AsyncIAdviseSink_Begin_OnSave_Stub(
    IAdviseSink* This);
void CALLBACK AsyncIAdviseSink_Finish_OnSave_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB AsyncIAdviseSink_Finish_OnSave_Stub(
    IAdviseSink* This);
void CALLBACK AsyncIAdviseSink_Begin_OnClose_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB AsyncIAdviseSink_Begin_OnClose_Stub(
    IAdviseSink* This);
void CALLBACK AsyncIAdviseSink_Finish_OnClose_Proxy(
    IAdviseSink* This);
HRESULT __RPC_STUB AsyncIAdviseSink_Finish_OnClose_Stub(
    IAdviseSink* This);

#endif  /* __AsyncIAdviseSink_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IAdviseSink2 interface
 */
#ifndef __IAdviseSink2_INTERFACE_DEFINED__
#define __IAdviseSink2_INTERFACE_DEFINED__

typedef IAdviseSink2 *LPADVISESINK2;
DEFINE_GUID(IID_IAdviseSink2, 0x00000125, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000125-0000-0000-c000-000000000046")
IAdviseSink2 : public IAdviseSink
{
    virtual void STDMETHODCALLTYPE OnLinkSrcChange(
        IMoniker *pmk) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAdviseSink2, 0x00000125, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IAdviseSink2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAdviseSink2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAdviseSink2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAdviseSink2* This);

    /*** IAdviseSink methods ***/
    void (STDMETHODCALLTYPE *OnDataChange)(
        IAdviseSink2* This,
        FORMATETC *pFormatetc,
        STGMEDIUM *pStgmed);

    void (STDMETHODCALLTYPE *OnViewChange)(
        IAdviseSink2* This,
        DWORD dwAspect,
        LONG lindex);

    void (STDMETHODCALLTYPE *OnRename)(
        IAdviseSink2* This,
        IMoniker *pmk);

    void (STDMETHODCALLTYPE *OnSave)(
        IAdviseSink2* This);

    void (STDMETHODCALLTYPE *OnClose)(
        IAdviseSink2* This);

    /*** IAdviseSink2 methods ***/
    void (STDMETHODCALLTYPE *OnLinkSrcChange)(
        IAdviseSink2* This,
        IMoniker *pmk);

    END_INTERFACE
} IAdviseSink2Vtbl;
interface IAdviseSink2 {
    CONST_VTBL IAdviseSink2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAdviseSink2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAdviseSink2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAdviseSink2_Release(This) (This)->lpVtbl->Release(This)
/*** IAdviseSink methods ***/
#define IAdviseSink2_OnDataChange(This,pFormatetc,pStgmed) (This)->lpVtbl->OnDataChange(This,pFormatetc,pStgmed)
#define IAdviseSink2_OnViewChange(This,dwAspect,lindex) (This)->lpVtbl->OnViewChange(This,dwAspect,lindex)
#define IAdviseSink2_OnRename(This,pmk) (This)->lpVtbl->OnRename(This,pmk)
#define IAdviseSink2_OnSave(This) (This)->lpVtbl->OnSave(This)
#define IAdviseSink2_OnClose(This) (This)->lpVtbl->OnClose(This)
/*** IAdviseSink2 methods ***/
#define IAdviseSink2_OnLinkSrcChange(This,pmk) (This)->lpVtbl->OnLinkSrcChange(This,pmk)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAdviseSink2_QueryInterface(IAdviseSink2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAdviseSink2_AddRef(IAdviseSink2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAdviseSink2_Release(IAdviseSink2* This) {
    return This->lpVtbl->Release(This);
}
/*** IAdviseSink methods ***/
static FORCEINLINE void IAdviseSink2_OnDataChange(IAdviseSink2* This,FORMATETC *pFormatetc,STGMEDIUM *pStgmed) {
    This->lpVtbl->OnDataChange(This,pFormatetc,pStgmed);
}
static FORCEINLINE void IAdviseSink2_OnViewChange(IAdviseSink2* This,DWORD dwAspect,LONG lindex) {
    This->lpVtbl->OnViewChange(This,dwAspect,lindex);
}
static FORCEINLINE void IAdviseSink2_OnRename(IAdviseSink2* This,IMoniker *pmk) {
    This->lpVtbl->OnRename(This,pmk);
}
static FORCEINLINE void IAdviseSink2_OnSave(IAdviseSink2* This) {
    This->lpVtbl->OnSave(This);
}
static FORCEINLINE void IAdviseSink2_OnClose(IAdviseSink2* This) {
    This->lpVtbl->OnClose(This);
}
/*** IAdviseSink2 methods ***/
static FORCEINLINE void IAdviseSink2_OnLinkSrcChange(IAdviseSink2* This,IMoniker *pmk) {
    This->lpVtbl->OnLinkSrcChange(This,pmk);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAdviseSink2_RemoteOnLinkSrcChange_Proxy(
    IAdviseSink2* This,
    IMoniker *pmk);
void __RPC_STUB IAdviseSink2_RemoteOnLinkSrcChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void CALLBACK IAdviseSink2_OnLinkSrcChange_Proxy(
    IAdviseSink2* This,
    IMoniker *pmk);
HRESULT __RPC_STUB IAdviseSink2_OnLinkSrcChange_Stub(
    IAdviseSink2* This,
    IMoniker *pmk);

#endif  /* __IAdviseSink2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * AsyncIAdviseSink2 interface
 */
#ifndef __AsyncIAdviseSink2_INTERFACE_DEFINED__
#define __AsyncIAdviseSink2_INTERFACE_DEFINED__

DEFINE_GUID(IID_AsyncIAdviseSink2, 0x00000151, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000151-0000-0000-c000-000000000046")
AsyncIAdviseSink2 : public IAdviseSink
{
    virtual void STDMETHODCALLTYPE Begin_OnLinkSrcChange(
        IMoniker *pmk) = 0;

    virtual void STDMETHODCALLTYPE Finish_OnLinkSrcChange(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AsyncIAdviseSink2, 0x00000151, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct AsyncIAdviseSink2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        AsyncIAdviseSink2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        AsyncIAdviseSink2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        AsyncIAdviseSink2* This);

    /*** IAdviseSink methods ***/
    void (STDMETHODCALLTYPE *Begin_OnDataChange)(
        AsyncIAdviseSink2* This,
        FORMATETC *pFormatetc,
        STGMEDIUM *pStgmed);

    void (STDMETHODCALLTYPE *Finish_OnDataChange)(
        AsyncIAdviseSink2* This);

    void (STDMETHODCALLTYPE *Begin_OnViewChange)(
        AsyncIAdviseSink2* This,
        DWORD dwAspect,
        LONG lindex);

    void (STDMETHODCALLTYPE *Finish_OnViewChange)(
        AsyncIAdviseSink2* This);

    void (STDMETHODCALLTYPE *Begin_OnRename)(
        AsyncIAdviseSink2* This,
        IMoniker *pmk);

    void (STDMETHODCALLTYPE *Finish_OnRename)(
        AsyncIAdviseSink2* This);

    void (STDMETHODCALLTYPE *Begin_OnSave)(
        AsyncIAdviseSink2* This);

    void (STDMETHODCALLTYPE *Finish_OnSave)(
        AsyncIAdviseSink2* This);

    void (STDMETHODCALLTYPE *Begin_OnClose)(
        AsyncIAdviseSink2* This);

    void (STDMETHODCALLTYPE *Finish_OnClose)(
        AsyncIAdviseSink2* This);

    /*** IAdviseSink2 methods ***/
    void (STDMETHODCALLTYPE *Begin_OnLinkSrcChange)(
        AsyncIAdviseSink2* This,
        IMoniker *pmk);

    void (STDMETHODCALLTYPE *Finish_OnLinkSrcChange)(
        AsyncIAdviseSink2* This);

    END_INTERFACE
} AsyncIAdviseSink2Vtbl;
interface AsyncIAdviseSink2 {
    CONST_VTBL AsyncIAdviseSink2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define AsyncIAdviseSink2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define AsyncIAdviseSink2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define AsyncIAdviseSink2_Release(This) (This)->lpVtbl->Release(This)
/*** IAdviseSink methods ***/
#define AsyncIAdviseSink2_Begin_OnDataChange(This,pFormatetc,pStgmed) (This)->lpVtbl->Begin_OnDataChange(This,pFormatetc,pStgmed)
#define AsyncIAdviseSink2_Finish_OnDataChange(This) (This)->lpVtbl->Finish_OnDataChange(This)
#define AsyncIAdviseSink2_Begin_OnViewChange(This,dwAspect,lindex) (This)->lpVtbl->Begin_OnViewChange(This,dwAspect,lindex)
#define AsyncIAdviseSink2_Finish_OnViewChange(This) (This)->lpVtbl->Finish_OnViewChange(This)
#define AsyncIAdviseSink2_Begin_OnRename(This,pmk) (This)->lpVtbl->Begin_OnRename(This,pmk)
#define AsyncIAdviseSink2_Finish_OnRename(This) (This)->lpVtbl->Finish_OnRename(This)
#define AsyncIAdviseSink2_Begin_OnSave(This) (This)->lpVtbl->Begin_OnSave(This)
#define AsyncIAdviseSink2_Finish_OnSave(This) (This)->lpVtbl->Finish_OnSave(This)
#define AsyncIAdviseSink2_Begin_OnClose(This) (This)->lpVtbl->Begin_OnClose(This)
#define AsyncIAdviseSink2_Finish_OnClose(This) (This)->lpVtbl->Finish_OnClose(This)
/*** IAdviseSink2 methods ***/
#define AsyncIAdviseSink2_Begin_OnLinkSrcChange(This,pmk) (This)->lpVtbl->Begin_OnLinkSrcChange(This,pmk)
#define AsyncIAdviseSink2_Finish_OnLinkSrcChange(This) (This)->lpVtbl->Finish_OnLinkSrcChange(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT AsyncIAdviseSink2_QueryInterface(AsyncIAdviseSink2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG AsyncIAdviseSink2_AddRef(AsyncIAdviseSink2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG AsyncIAdviseSink2_Release(AsyncIAdviseSink2* This) {
    return This->lpVtbl->Release(This);
}
/*** IAdviseSink methods ***/
static FORCEINLINE void Begin_AsyncIAdviseSink2_OnDataChange(AsyncIAdviseSink2* This,FORMATETC *pFormatetc,STGMEDIUM *pStgmed) {
    This->lpVtbl->Begin_OnDataChange(This,pFormatetc,pStgmed);
}
static FORCEINLINE void Finish_AsyncIAdviseSink2_OnDataChange(AsyncIAdviseSink2* This) {
    This->lpVtbl->Finish_OnDataChange(This);
}
static FORCEINLINE void Begin_AsyncIAdviseSink2_OnViewChange(AsyncIAdviseSink2* This,DWORD dwAspect,LONG lindex) {
    This->lpVtbl->Begin_OnViewChange(This,dwAspect,lindex);
}
static FORCEINLINE void Finish_AsyncIAdviseSink2_OnViewChange(AsyncIAdviseSink2* This) {
    This->lpVtbl->Finish_OnViewChange(This);
}
static FORCEINLINE void Begin_AsyncIAdviseSink2_OnRename(AsyncIAdviseSink2* This,IMoniker *pmk) {
    This->lpVtbl->Begin_OnRename(This,pmk);
}
static FORCEINLINE void Finish_AsyncIAdviseSink2_OnRename(AsyncIAdviseSink2* This) {
    This->lpVtbl->Finish_OnRename(This);
}
static FORCEINLINE void Begin_AsyncIAdviseSink2_OnSave(AsyncIAdviseSink2* This) {
    This->lpVtbl->Begin_OnSave(This);
}
static FORCEINLINE void Finish_AsyncIAdviseSink2_OnSave(AsyncIAdviseSink2* This) {
    This->lpVtbl->Finish_OnSave(This);
}
static FORCEINLINE void Begin_AsyncIAdviseSink2_OnClose(AsyncIAdviseSink2* This) {
    This->lpVtbl->Begin_OnClose(This);
}
static FORCEINLINE void Finish_AsyncIAdviseSink2_OnClose(AsyncIAdviseSink2* This) {
    This->lpVtbl->Finish_OnClose(This);
}
/*** IAdviseSink2 methods ***/
static FORCEINLINE void Begin_AsyncIAdviseSink2_OnLinkSrcChange(AsyncIAdviseSink2* This,IMoniker *pmk) {
    This->lpVtbl->Begin_OnLinkSrcChange(This,pmk);
}
static FORCEINLINE void Finish_AsyncIAdviseSink2_OnLinkSrcChange(AsyncIAdviseSink2* This) {
    This->lpVtbl->Finish_OnLinkSrcChange(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE AsyncIAdviseSink2_Begin_RemoteOnLinkSrcChange_Proxy(
    IAdviseSink2* This,
    IMoniker *pmk);
void __RPC_STUB AsyncIAdviseSink2_Begin_RemoteOnLinkSrcChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE AsyncIAdviseSink2_Finish_RemoteOnLinkSrcChange_Proxy(
    IAdviseSink2* This,
    IMoniker *pmk);
void __RPC_STUB AsyncIAdviseSink2_Finish_RemoteOnLinkSrcChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void CALLBACK AsyncIAdviseSink2_Begin_OnLinkSrcChange_Proxy(
    IAdviseSink2* This,
    IMoniker *pmk);
HRESULT __RPC_STUB AsyncIAdviseSink2_Begin_OnLinkSrcChange_Stub(
    IAdviseSink2* This,
    IMoniker *pmk);
void CALLBACK AsyncIAdviseSink2_Finish_OnLinkSrcChange_Proxy(
    IAdviseSink2* This);
HRESULT __RPC_STUB AsyncIAdviseSink2_Finish_OnLinkSrcChange_Stub(
    IAdviseSink2* This,
    IMoniker *pmk);

#endif  /* __AsyncIAdviseSink2_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IDataObject interface
 */
#ifndef __IDataObject_INTERFACE_DEFINED__
#define __IDataObject_INTERFACE_DEFINED__

typedef IDataObject *LPDATAOBJECT;
typedef enum tagDATADIR {
    DATADIR_GET = 1,
    DATADIR_SET = 2
} DATADIR;
DEFINE_GUID(IID_IDataObject, 0x0000010e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000010e-0000-0000-c000-000000000046")
IDataObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetData(
        FORMATETC *pformatetcIn,
        STGMEDIUM *pmedium) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDataHere(
        FORMATETC *pformatetc,
        STGMEDIUM *pmedium) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryGetData(
        FORMATETC *pformatetc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCanonicalFormatEtc(
        FORMATETC *pformatectIn,
        FORMATETC *pformatetcOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetData(
        FORMATETC *pformatetc,
        STGMEDIUM *pmedium,
        WINBOOL fRelease) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumFormatEtc(
        DWORD dwDirection,
        IEnumFORMATETC **ppenumFormatEtc) = 0;

    virtual HRESULT STDMETHODCALLTYPE DAdvise(
        FORMATETC *pformatetc,
        DWORD advf,
        IAdviseSink *pAdvSink,
        DWORD *pdwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE DUnadvise(
        DWORD dwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumDAdvise(
        IEnumSTATDATA **ppenumAdvise) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDataObject, 0x0000010e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IDataObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDataObject* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDataObject* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDataObject* This);

    /*** IDataObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetData)(
        IDataObject* This,
        FORMATETC *pformatetcIn,
        STGMEDIUM *pmedium);

    HRESULT (STDMETHODCALLTYPE *GetDataHere)(
        IDataObject* This,
        FORMATETC *pformatetc,
        STGMEDIUM *pmedium);

    HRESULT (STDMETHODCALLTYPE *QueryGetData)(
        IDataObject* This,
        FORMATETC *pformatetc);

    HRESULT (STDMETHODCALLTYPE *GetCanonicalFormatEtc)(
        IDataObject* This,
        FORMATETC *pformatectIn,
        FORMATETC *pformatetcOut);

    HRESULT (STDMETHODCALLTYPE *SetData)(
        IDataObject* This,
        FORMATETC *pformatetc,
        STGMEDIUM *pmedium,
        WINBOOL fRelease);

    HRESULT (STDMETHODCALLTYPE *EnumFormatEtc)(
        IDataObject* This,
        DWORD dwDirection,
        IEnumFORMATETC **ppenumFormatEtc);

    HRESULT (STDMETHODCALLTYPE *DAdvise)(
        IDataObject* This,
        FORMATETC *pformatetc,
        DWORD advf,
        IAdviseSink *pAdvSink,
        DWORD *pdwConnection);

    HRESULT (STDMETHODCALLTYPE *DUnadvise)(
        IDataObject* This,
        DWORD dwConnection);

    HRESULT (STDMETHODCALLTYPE *EnumDAdvise)(
        IDataObject* This,
        IEnumSTATDATA **ppenumAdvise);

    END_INTERFACE
} IDataObjectVtbl;
interface IDataObject {
    CONST_VTBL IDataObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDataObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDataObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDataObject_Release(This) (This)->lpVtbl->Release(This)
/*** IDataObject methods ***/
#define IDataObject_GetData(This,pformatetcIn,pmedium) (This)->lpVtbl->GetData(This,pformatetcIn,pmedium)
#define IDataObject_GetDataHere(This,pformatetc,pmedium) (This)->lpVtbl->GetDataHere(This,pformatetc,pmedium)
#define IDataObject_QueryGetData(This,pformatetc) (This)->lpVtbl->QueryGetData(This,pformatetc)
#define IDataObject_GetCanonicalFormatEtc(This,pformatectIn,pformatetcOut) (This)->lpVtbl->GetCanonicalFormatEtc(This,pformatectIn,pformatetcOut)
#define IDataObject_SetData(This,pformatetc,pmedium,fRelease) (This)->lpVtbl->SetData(This,pformatetc,pmedium,fRelease)
#define IDataObject_EnumFormatEtc(This,dwDirection,ppenumFormatEtc) (This)->lpVtbl->EnumFormatEtc(This,dwDirection,ppenumFormatEtc)
#define IDataObject_DAdvise(This,pformatetc,advf,pAdvSink,pdwConnection) (This)->lpVtbl->DAdvise(This,pformatetc,advf,pAdvSink,pdwConnection)
#define IDataObject_DUnadvise(This,dwConnection) (This)->lpVtbl->DUnadvise(This,dwConnection)
#define IDataObject_EnumDAdvise(This,ppenumAdvise) (This)->lpVtbl->EnumDAdvise(This,ppenumAdvise)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDataObject_QueryInterface(IDataObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDataObject_AddRef(IDataObject* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDataObject_Release(IDataObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IDataObject methods ***/
static FORCEINLINE HRESULT IDataObject_GetData(IDataObject* This,FORMATETC *pformatetcIn,STGMEDIUM *pmedium) {
    return This->lpVtbl->GetData(This,pformatetcIn,pmedium);
}
static FORCEINLINE HRESULT IDataObject_GetDataHere(IDataObject* This,FORMATETC *pformatetc,STGMEDIUM *pmedium) {
    return This->lpVtbl->GetDataHere(This,pformatetc,pmedium);
}
static FORCEINLINE HRESULT IDataObject_QueryGetData(IDataObject* This,FORMATETC *pformatetc) {
    return This->lpVtbl->QueryGetData(This,pformatetc);
}
static FORCEINLINE HRESULT IDataObject_GetCanonicalFormatEtc(IDataObject* This,FORMATETC *pformatectIn,FORMATETC *pformatetcOut) {
    return This->lpVtbl->GetCanonicalFormatEtc(This,pformatectIn,pformatetcOut);
}
static FORCEINLINE HRESULT IDataObject_SetData(IDataObject* This,FORMATETC *pformatetc,STGMEDIUM *pmedium,WINBOOL fRelease) {
    return This->lpVtbl->SetData(This,pformatetc,pmedium,fRelease);
}
static FORCEINLINE HRESULT IDataObject_EnumFormatEtc(IDataObject* This,DWORD dwDirection,IEnumFORMATETC **ppenumFormatEtc) {
    return This->lpVtbl->EnumFormatEtc(This,dwDirection,ppenumFormatEtc);
}
static FORCEINLINE HRESULT IDataObject_DAdvise(IDataObject* This,FORMATETC *pformatetc,DWORD advf,IAdviseSink *pAdvSink,DWORD *pdwConnection) {
    return This->lpVtbl->DAdvise(This,pformatetc,advf,pAdvSink,pdwConnection);
}
static FORCEINLINE HRESULT IDataObject_DUnadvise(IDataObject* This,DWORD dwConnection) {
    return This->lpVtbl->DUnadvise(This,dwConnection);
}
static FORCEINLINE HRESULT IDataObject_EnumDAdvise(IDataObject* This,IEnumSTATDATA **ppenumAdvise) {
    return This->lpVtbl->EnumDAdvise(This,ppenumAdvise);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IDataObject_RemoteGetData_Proxy(
    IDataObject* This,
    FORMATETC *pformatetcIn,
    STGMEDIUM *pRemoteMedium);
void __RPC_STUB IDataObject_RemoteGetData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObject_RemoteGetDataHere_Proxy(
    IDataObject* This,
    FORMATETC *pformatetc,
    STGMEDIUM *pRemoteMedium);
void __RPC_STUB IDataObject_RemoteGetDataHere_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObject_QueryGetData_Proxy(
    IDataObject* This,
    FORMATETC *pformatetc);
void __RPC_STUB IDataObject_QueryGetData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObject_GetCanonicalFormatEtc_Proxy(
    IDataObject* This,
    FORMATETC *pformatectIn,
    FORMATETC *pformatetcOut);
void __RPC_STUB IDataObject_GetCanonicalFormatEtc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObject_RemoteSetData_Proxy(
    IDataObject* This,
    FORMATETC *pformatetc,
    FLAG_STGMEDIUM *pmedium,
    WINBOOL fRelease);
void __RPC_STUB IDataObject_RemoteSetData_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObject_EnumFormatEtc_Proxy(
    IDataObject* This,
    DWORD dwDirection,
    IEnumFORMATETC **ppenumFormatEtc);
void __RPC_STUB IDataObject_EnumFormatEtc_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObject_DAdvise_Proxy(
    IDataObject* This,
    FORMATETC *pformatetc,
    DWORD advf,
    IAdviseSink *pAdvSink,
    DWORD *pdwConnection);
void __RPC_STUB IDataObject_DAdvise_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObject_DUnadvise_Proxy(
    IDataObject* This,
    DWORD dwConnection);
void __RPC_STUB IDataObject_DUnadvise_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObject_EnumDAdvise_Proxy(
    IDataObject* This,
    IEnumSTATDATA **ppenumAdvise);
void __RPC_STUB IDataObject_EnumDAdvise_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IDataObject_GetData_Proxy(
    IDataObject* This,
    FORMATETC *pformatetcIn,
    STGMEDIUM *pmedium);
HRESULT __RPC_STUB IDataObject_GetData_Stub(
    IDataObject* This,
    FORMATETC *pformatetcIn,
    STGMEDIUM *pRemoteMedium);
HRESULT CALLBACK IDataObject_GetDataHere_Proxy(
    IDataObject* This,
    FORMATETC *pformatetc,
    STGMEDIUM *pmedium);
HRESULT __RPC_STUB IDataObject_GetDataHere_Stub(
    IDataObject* This,
    FORMATETC *pformatetc,
    STGMEDIUM *pRemoteMedium);
HRESULT CALLBACK IDataObject_SetData_Proxy(
    IDataObject* This,
    FORMATETC *pformatetc,
    STGMEDIUM *pmedium,
    WINBOOL fRelease);
HRESULT __RPC_STUB IDataObject_SetData_Stub(
    IDataObject* This,
    FORMATETC *pformatetc,
    FLAG_STGMEDIUM *pmedium,
    WINBOOL fRelease);

#endif  /* __IDataObject_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IDataAdviseHolder interface
 */
#ifndef __IDataAdviseHolder_INTERFACE_DEFINED__
#define __IDataAdviseHolder_INTERFACE_DEFINED__

typedef IDataAdviseHolder *LPDATAADVISEHOLDER;
DEFINE_GUID(IID_IDataAdviseHolder, 0x00000110, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000110-0000-0000-c000-000000000046")
IDataAdviseHolder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Advise(
        IDataObject *pDataObject,
        FORMATETC *pFetc,
        DWORD advf,
        IAdviseSink *pAdvise,
        DWORD *pdwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unadvise(
        DWORD dwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumAdvise(
        IEnumSTATDATA **ppenumAdvise) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendOnDataChange(
        IDataObject *pDataObject,
        DWORD dwReserved,
        DWORD advf) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDataAdviseHolder, 0x00000110, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IDataAdviseHolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDataAdviseHolder* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDataAdviseHolder* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDataAdviseHolder* This);

    /*** IDataAdviseHolder methods ***/
    HRESULT (STDMETHODCALLTYPE *Advise)(
        IDataAdviseHolder* This,
        IDataObject *pDataObject,
        FORMATETC *pFetc,
        DWORD advf,
        IAdviseSink *pAdvise,
        DWORD *pdwConnection);

    HRESULT (STDMETHODCALLTYPE *Unadvise)(
        IDataAdviseHolder* This,
        DWORD dwConnection);

    HRESULT (STDMETHODCALLTYPE *EnumAdvise)(
        IDataAdviseHolder* This,
        IEnumSTATDATA **ppenumAdvise);

    HRESULT (STDMETHODCALLTYPE *SendOnDataChange)(
        IDataAdviseHolder* This,
        IDataObject *pDataObject,
        DWORD dwReserved,
        DWORD advf);

    END_INTERFACE
} IDataAdviseHolderVtbl;
interface IDataAdviseHolder {
    CONST_VTBL IDataAdviseHolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDataAdviseHolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDataAdviseHolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDataAdviseHolder_Release(This) (This)->lpVtbl->Release(This)
/*** IDataAdviseHolder methods ***/
#define IDataAdviseHolder_Advise(This,pDataObject,pFetc,advf,pAdvise,pdwConnection) (This)->lpVtbl->Advise(This,pDataObject,pFetc,advf,pAdvise,pdwConnection)
#define IDataAdviseHolder_Unadvise(This,dwConnection) (This)->lpVtbl->Unadvise(This,dwConnection)
#define IDataAdviseHolder_EnumAdvise(This,ppenumAdvise) (This)->lpVtbl->EnumAdvise(This,ppenumAdvise)
#define IDataAdviseHolder_SendOnDataChange(This,pDataObject,dwReserved,advf) (This)->lpVtbl->SendOnDataChange(This,pDataObject,dwReserved,advf)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDataAdviseHolder_QueryInterface(IDataAdviseHolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDataAdviseHolder_AddRef(IDataAdviseHolder* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDataAdviseHolder_Release(IDataAdviseHolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IDataAdviseHolder methods ***/
static FORCEINLINE HRESULT IDataAdviseHolder_Advise(IDataAdviseHolder* This,IDataObject *pDataObject,FORMATETC *pFetc,DWORD advf,IAdviseSink *pAdvise,DWORD *pdwConnection) {
    return This->lpVtbl->Advise(This,pDataObject,pFetc,advf,pAdvise,pdwConnection);
}
static FORCEINLINE HRESULT IDataAdviseHolder_Unadvise(IDataAdviseHolder* This,DWORD dwConnection) {
    return This->lpVtbl->Unadvise(This,dwConnection);
}
static FORCEINLINE HRESULT IDataAdviseHolder_EnumAdvise(IDataAdviseHolder* This,IEnumSTATDATA **ppenumAdvise) {
    return This->lpVtbl->EnumAdvise(This,ppenumAdvise);
}
static FORCEINLINE HRESULT IDataAdviseHolder_SendOnDataChange(IDataAdviseHolder* This,IDataObject *pDataObject,DWORD dwReserved,DWORD advf) {
    return This->lpVtbl->SendOnDataChange(This,pDataObject,dwReserved,advf);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IDataAdviseHolder_Advise_Proxy(
    IDataAdviseHolder* This,
    IDataObject *pDataObject,
    FORMATETC *pFetc,
    DWORD advf,
    IAdviseSink *pAdvise,
    DWORD *pdwConnection);
void __RPC_STUB IDataAdviseHolder_Advise_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataAdviseHolder_Unadvise_Proxy(
    IDataAdviseHolder* This,
    DWORD dwConnection);
void __RPC_STUB IDataAdviseHolder_Unadvise_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataAdviseHolder_EnumAdvise_Proxy(
    IDataAdviseHolder* This,
    IEnumSTATDATA **ppenumAdvise);
void __RPC_STUB IDataAdviseHolder_EnumAdvise_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataAdviseHolder_SendOnDataChange_Proxy(
    IDataAdviseHolder* This,
    IDataObject *pDataObject,
    DWORD dwReserved,
    DWORD advf);
void __RPC_STUB IDataAdviseHolder_SendOnDataChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IDataAdviseHolder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMessageFilter interface
 */
#ifndef __IMessageFilter_INTERFACE_DEFINED__
#define __IMessageFilter_INTERFACE_DEFINED__

typedef IMessageFilter *LPMESSAGEFILTER;
typedef enum tagCALLTYPE {
    CALLTYPE_TOPLEVEL = 1,
    CALLTYPE_NESTED = 2,
    CALLTYPE_ASYNC = 3,
    CALLTYPE_TOPLEVEL_CALLPENDING = 4,
    CALLTYPE_ASYNC_CALLPENDING = 5
} CALLTYPE;
typedef enum tagSERVERCALL {
    SERVERCALL_ISHANDLED = 0,
    SERVERCALL_REJECTED = 1,
    SERVERCALL_RETRYLATER = 2
} SERVERCALL;
typedef enum tagPENDINGTYPE {
    PENDINGTYPE_TOPLEVEL = 1,
    PENDINGTYPE_NESTED = 2
} PENDINGTYPE;
typedef enum tagPENDINGMSG {
    PENDINGMSG_CANCELCALL = 0,
    PENDINGMSG_WAITNOPROCESS = 1,
    PENDINGMSG_WAITDEFPROCESS = 2
} PENDINGMSG;
typedef struct tagINTERFACEINFO {
    IUnknown *pUnk;
    IID iid;
    WORD wMethod;
} INTERFACEINFO;
typedef struct tagINTERFACEINFO *LPINTERFACEINFO;
DEFINE_GUID(IID_IMessageFilter, 0x00000016, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000016-0000-0000-c000-000000000046")
IMessageFilter : public IUnknown
{
    virtual DWORD STDMETHODCALLTYPE HandleInComingCall(
        DWORD dwCallType,
        HTASK htaskCaller,
        DWORD dwTickCount,
        LPINTERFACEINFO lpInterfaceInfo) = 0;

    virtual DWORD STDMETHODCALLTYPE RetryRejectedCall(
        HTASK htaskCallee,
        DWORD dwTickCount,
        DWORD dwRejectType) = 0;

    virtual DWORD STDMETHODCALLTYPE MessagePending(
        HTASK htaskCallee,
        DWORD dwTickCount,
        DWORD dwPendingType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMessageFilter, 0x00000016, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMessageFilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMessageFilter* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMessageFilter* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMessageFilter* This);

    /*** IMessageFilter methods ***/
    DWORD (STDMETHODCALLTYPE *HandleInComingCall)(
        IMessageFilter* This,
        DWORD dwCallType,
        HTASK htaskCaller,
        DWORD dwTickCount,
        LPINTERFACEINFO lpInterfaceInfo);

    DWORD (STDMETHODCALLTYPE *RetryRejectedCall)(
        IMessageFilter* This,
        HTASK htaskCallee,
        DWORD dwTickCount,
        DWORD dwRejectType);

    DWORD (STDMETHODCALLTYPE *MessagePending)(
        IMessageFilter* This,
        HTASK htaskCallee,
        DWORD dwTickCount,
        DWORD dwPendingType);

    END_INTERFACE
} IMessageFilterVtbl;
interface IMessageFilter {
    CONST_VTBL IMessageFilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMessageFilter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMessageFilter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMessageFilter_Release(This) (This)->lpVtbl->Release(This)
/*** IMessageFilter methods ***/
#define IMessageFilter_HandleInComingCall(This,dwCallType,htaskCaller,dwTickCount,lpInterfaceInfo) (This)->lpVtbl->HandleInComingCall(This,dwCallType,htaskCaller,dwTickCount,lpInterfaceInfo)
#define IMessageFilter_RetryRejectedCall(This,htaskCallee,dwTickCount,dwRejectType) (This)->lpVtbl->RetryRejectedCall(This,htaskCallee,dwTickCount,dwRejectType)
#define IMessageFilter_MessagePending(This,htaskCallee,dwTickCount,dwPendingType) (This)->lpVtbl->MessagePending(This,htaskCallee,dwTickCount,dwPendingType)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IMessageFilter_QueryInterface(IMessageFilter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IMessageFilter_AddRef(IMessageFilter* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IMessageFilter_Release(IMessageFilter* This) {
    return This->lpVtbl->Release(This);
}
/*** IMessageFilter methods ***/
static FORCEINLINE DWORD IMessageFilter_HandleInComingCall(IMessageFilter* This,DWORD dwCallType,HTASK htaskCaller,DWORD dwTickCount,LPINTERFACEINFO lpInterfaceInfo) {
    return This->lpVtbl->HandleInComingCall(This,dwCallType,htaskCaller,dwTickCount,lpInterfaceInfo);
}
static FORCEINLINE DWORD IMessageFilter_RetryRejectedCall(IMessageFilter* This,HTASK htaskCallee,DWORD dwTickCount,DWORD dwRejectType) {
    return This->lpVtbl->RetryRejectedCall(This,htaskCallee,dwTickCount,dwRejectType);
}
static FORCEINLINE DWORD IMessageFilter_MessagePending(IMessageFilter* This,HTASK htaskCallee,DWORD dwTickCount,DWORD dwPendingType) {
    return This->lpVtbl->MessagePending(This,htaskCallee,dwTickCount,dwPendingType);
}
#endif
#endif

#endif

DWORD STDMETHODCALLTYPE IMessageFilter_HandleInComingCall_Proxy(
    IMessageFilter* This,
    DWORD dwCallType,
    HTASK htaskCaller,
    DWORD dwTickCount,
    LPINTERFACEINFO lpInterfaceInfo);
void __RPC_STUB IMessageFilter_HandleInComingCall_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
DWORD STDMETHODCALLTYPE IMessageFilter_RetryRejectedCall_Proxy(
    IMessageFilter* This,
    HTASK htaskCallee,
    DWORD dwTickCount,
    DWORD dwRejectType);
void __RPC_STUB IMessageFilter_RetryRejectedCall_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
DWORD STDMETHODCALLTYPE IMessageFilter_MessagePending_Proxy(
    IMessageFilter* This,
    HTASK htaskCallee,
    DWORD dwTickCount,
    DWORD dwPendingType);
void __RPC_STUB IMessageFilter_MessagePending_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IMessageFilter_INTERFACE_DEFINED__ */


extern const FMTID FMTID_SummaryInformation;

extern const FMTID FMTID_DocSummaryInformation;

extern const FMTID FMTID_UserDefinedProperties;

extern const FMTID FMTID_DiscardableInformation;

extern const FMTID FMTID_ImageSummaryInformation;

extern const FMTID FMTID_AudioSummaryInformation;

extern const FMTID FMTID_VideoSummaryInformation;

extern const FMTID FMTID_MediaFileSummaryInformation;

/*****************************************************************************
 * IClassActivator interface
 */
#ifndef __IClassActivator_INTERFACE_DEFINED__
#define __IClassActivator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IClassActivator, 0x00000140, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000140-0000-0000-c000-000000000046")
IClassActivator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClassObject(
        REFCLSID rclsid,
        DWORD dwClassContext,
        LCID locale,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IClassActivator, 0x00000140, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IClassActivatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IClassActivator* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IClassActivator* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IClassActivator* This);

    /*** IClassActivator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassObject)(
        IClassActivator* This,
        REFCLSID rclsid,
        DWORD dwClassContext,
        LCID locale,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IClassActivatorVtbl;
interface IClassActivator {
    CONST_VTBL IClassActivatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IClassActivator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IClassActivator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IClassActivator_Release(This) (This)->lpVtbl->Release(This)
/*** IClassActivator methods ***/
#define IClassActivator_GetClassObject(This,rclsid,dwClassContext,locale,riid,ppv) (This)->lpVtbl->GetClassObject(This,rclsid,dwClassContext,locale,riid,ppv)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IClassActivator_QueryInterface(IClassActivator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IClassActivator_AddRef(IClassActivator* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IClassActivator_Release(IClassActivator* This) {
    return This->lpVtbl->Release(This);
}
/*** IClassActivator methods ***/
static FORCEINLINE HRESULT IClassActivator_GetClassObject(IClassActivator* This,REFCLSID rclsid,DWORD dwClassContext,LCID locale,REFIID riid,void **ppv) {
    return This->lpVtbl->GetClassObject(This,rclsid,dwClassContext,locale,riid,ppv);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IClassActivator_GetClassObject_Proxy(
    IClassActivator* This,
    REFCLSID rclsid,
    DWORD dwClassContext,
    LCID locale,
    REFIID riid,
    void **ppv);
void __RPC_STUB IClassActivator_GetClassObject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IClassActivator_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IFillLockBytes interface
 */
#ifndef __IFillLockBytes_INTERFACE_DEFINED__
#define __IFillLockBytes_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFillLockBytes, 0x99caf010, 0x415e, 0x11cf, 0x88,0x14, 0x00,0xaa,0x00,0xb5,0x69,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("99caf010-415e-11cf-8814-00aa00b569f5")
IFillLockBytes : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE FillAppend(
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten) = 0;

    virtual HRESULT STDMETHODCALLTYPE FillAt(
        ULARGE_INTEGER ulOffset,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFillSize(
        ULARGE_INTEGER ulSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE Terminate(
        WINBOOL bCanceled) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFillLockBytes, 0x99caf010, 0x415e, 0x11cf, 0x88,0x14, 0x00,0xaa,0x00,0xb5,0x69,0xf5)
#endif
#else
typedef struct IFillLockBytesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFillLockBytes* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFillLockBytes* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFillLockBytes* This);

    /*** IFillLockBytes methods ***/
    HRESULT (STDMETHODCALLTYPE *FillAppend)(
        IFillLockBytes* This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *FillAt)(
        IFillLockBytes* This,
        ULARGE_INTEGER ulOffset,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *SetFillSize)(
        IFillLockBytes* This,
        ULARGE_INTEGER ulSize);

    HRESULT (STDMETHODCALLTYPE *Terminate)(
        IFillLockBytes* This,
        WINBOOL bCanceled);

    END_INTERFACE
} IFillLockBytesVtbl;
interface IFillLockBytes {
    CONST_VTBL IFillLockBytesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFillLockBytes_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFillLockBytes_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFillLockBytes_Release(This) (This)->lpVtbl->Release(This)
/*** IFillLockBytes methods ***/
#define IFillLockBytes_FillAppend(This,pv,cb,pcbWritten) (This)->lpVtbl->FillAppend(This,pv,cb,pcbWritten)
#define IFillLockBytes_FillAt(This,ulOffset,pv,cb,pcbWritten) (This)->lpVtbl->FillAt(This,ulOffset,pv,cb,pcbWritten)
#define IFillLockBytes_SetFillSize(This,ulSize) (This)->lpVtbl->SetFillSize(This,ulSize)
#define IFillLockBytes_Terminate(This,bCanceled) (This)->lpVtbl->Terminate(This,bCanceled)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFillLockBytes_QueryInterface(IFillLockBytes* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFillLockBytes_AddRef(IFillLockBytes* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFillLockBytes_Release(IFillLockBytes* This) {
    return This->lpVtbl->Release(This);
}
/*** IFillLockBytes methods ***/
static FORCEINLINE HRESULT IFillLockBytes_FillAppend(IFillLockBytes* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->FillAppend(This,pv,cb,pcbWritten);
}
static FORCEINLINE HRESULT IFillLockBytes_FillAt(IFillLockBytes* This,ULARGE_INTEGER ulOffset,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->FillAt(This,ulOffset,pv,cb,pcbWritten);
}
static FORCEINLINE HRESULT IFillLockBytes_SetFillSize(IFillLockBytes* This,ULARGE_INTEGER ulSize) {
    return This->lpVtbl->SetFillSize(This,ulSize);
}
static FORCEINLINE HRESULT IFillLockBytes_Terminate(IFillLockBytes* This,WINBOOL bCanceled) {
    return This->lpVtbl->Terminate(This,bCanceled);
}
#endif
#endif

#endif

HRESULT __stdcall IFillLockBytes_RemoteFillAppend_Proxy(
    IFillLockBytes* This,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);
void __RPC_STUB IFillLockBytes_RemoteFillAppend_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT __stdcall IFillLockBytes_RemoteFillAt_Proxy(
    IFillLockBytes* This,
    ULARGE_INTEGER ulOffset,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);
void __RPC_STUB IFillLockBytes_RemoteFillAt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFillLockBytes_SetFillSize_Proxy(
    IFillLockBytes* This,
    ULARGE_INTEGER ulSize);
void __RPC_STUB IFillLockBytes_SetFillSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFillLockBytes_Terminate_Proxy(
    IFillLockBytes* This,
    WINBOOL bCanceled);
void __RPC_STUB IFillLockBytes_Terminate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IFillLockBytes_FillAppend_Proxy(
    IFillLockBytes* This,
    const void *pv,
    ULONG cb,
    ULONG *pcbWritten);
HRESULT __RPC_STUB IFillLockBytes_FillAppend_Stub(
    IFillLockBytes* This,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);
HRESULT CALLBACK IFillLockBytes_FillAt_Proxy(
    IFillLockBytes* This,
    ULARGE_INTEGER ulOffset,
    const void *pv,
    ULONG cb,
    ULONG *pcbWritten);
HRESULT __RPC_STUB IFillLockBytes_FillAt_Stub(
    IFillLockBytes* This,
    ULARGE_INTEGER ulOffset,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);

#endif  /* __IFillLockBytes_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IProgressNotify interface
 */
#ifndef __IProgressNotify_INTERFACE_DEFINED__
#define __IProgressNotify_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProgressNotify, 0xa9d758a0, 0x4617, 0x11cf, 0x95,0xfc, 0x00,0xaa,0x00,0x68,0x0d,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a9d758a0-4617-11cf-95fc-00aa00680db4")
IProgressNotify : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnProgress(
        DWORD dwProgressCurrent,
        DWORD dwProgressMaximum,
        WINBOOL fAccurate,
        WINBOOL fOwner) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProgressNotify, 0xa9d758a0, 0x4617, 0x11cf, 0x95,0xfc, 0x00,0xaa,0x00,0x68,0x0d,0xb4)
#endif
#else
typedef struct IProgressNotifyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProgressNotify* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProgressNotify* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProgressNotify* This);

    /*** IProgressNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *OnProgress)(
        IProgressNotify* This,
        DWORD dwProgressCurrent,
        DWORD dwProgressMaximum,
        WINBOOL fAccurate,
        WINBOOL fOwner);

    END_INTERFACE
} IProgressNotifyVtbl;
interface IProgressNotify {
    CONST_VTBL IProgressNotifyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProgressNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProgressNotify_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProgressNotify_Release(This) (This)->lpVtbl->Release(This)
/*** IProgressNotify methods ***/
#define IProgressNotify_OnProgress(This,dwProgressCurrent,dwProgressMaximum,fAccurate,fOwner) (This)->lpVtbl->OnProgress(This,dwProgressCurrent,dwProgressMaximum,fAccurate,fOwner)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IProgressNotify_QueryInterface(IProgressNotify* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IProgressNotify_AddRef(IProgressNotify* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IProgressNotify_Release(IProgressNotify* This) {
    return This->lpVtbl->Release(This);
}
/*** IProgressNotify methods ***/
static FORCEINLINE HRESULT IProgressNotify_OnProgress(IProgressNotify* This,DWORD dwProgressCurrent,DWORD dwProgressMaximum,WINBOOL fAccurate,WINBOOL fOwner) {
    return This->lpVtbl->OnProgress(This,dwProgressCurrent,dwProgressMaximum,fAccurate,fOwner);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IProgressNotify_OnProgress_Proxy(
    IProgressNotify* This,
    DWORD dwProgressCurrent,
    DWORD dwProgressMaximum,
    WINBOOL fAccurate,
    WINBOOL fOwner);
void __RPC_STUB IProgressNotify_OnProgress_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IProgressNotify_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * ILayoutStorage interface
 */
#ifndef __ILayoutStorage_INTERFACE_DEFINED__
#define __ILayoutStorage_INTERFACE_DEFINED__

typedef struct tagStorageLayout {
    DWORD LayoutType;
    OLECHAR *pwcsElementName;
    LARGE_INTEGER cOffset;
    LARGE_INTEGER cBytes;
} StorageLayout;
DEFINE_GUID(IID_ILayoutStorage, 0x0e6d4d90, 0x6738, 0x11cf, 0x96,0x08, 0x00,0xaa,0x00,0x68,0x0d,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0e6d4d90-6738-11cf-9608-00aa00680db4")
ILayoutStorage : public IUnknown
{
    virtual HRESULT __stdcall LayoutScript(
        StorageLayout *pStorageLayout,
        DWORD nEntries,
        DWORD glfInterleavedFlag) = 0;

    virtual HRESULT __stdcall BeginMonitor(
        ) = 0;

    virtual HRESULT __stdcall EndMonitor(
        ) = 0;

    virtual HRESULT __stdcall ReLayoutDocfile(
        OLECHAR *pwcsNewDfName) = 0;

    virtual HRESULT __stdcall ReLayoutDocfileOnILockBytes(
        ILockBytes *pILockBytes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ILayoutStorage, 0x0e6d4d90, 0x6738, 0x11cf, 0x96,0x08, 0x00,0xaa,0x00,0x68,0x0d,0xb4)
#endif
#else
typedef struct ILayoutStorageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ILayoutStorage* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ILayoutStorage* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ILayoutStorage* This);

    /*** ILayoutStorage methods ***/
    HRESULT (__stdcall *LayoutScript)(
        ILayoutStorage* This,
        StorageLayout *pStorageLayout,
        DWORD nEntries,
        DWORD glfInterleavedFlag);

    HRESULT (__stdcall *BeginMonitor)(
        ILayoutStorage* This);

    HRESULT (__stdcall *EndMonitor)(
        ILayoutStorage* This);

    HRESULT (__stdcall *ReLayoutDocfile)(
        ILayoutStorage* This,
        OLECHAR *pwcsNewDfName);

    HRESULT (__stdcall *ReLayoutDocfileOnILockBytes)(
        ILayoutStorage* This,
        ILockBytes *pILockBytes);

    END_INTERFACE
} ILayoutStorageVtbl;
interface ILayoutStorage {
    CONST_VTBL ILayoutStorageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ILayoutStorage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ILayoutStorage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ILayoutStorage_Release(This) (This)->lpVtbl->Release(This)
/*** ILayoutStorage methods ***/
#define ILayoutStorage_LayoutScript(This,pStorageLayout,nEntries,glfInterleavedFlag) (This)->lpVtbl->LayoutScript(This,pStorageLayout,nEntries,glfInterleavedFlag)
#define ILayoutStorage_BeginMonitor(This) (This)->lpVtbl->BeginMonitor(This)
#define ILayoutStorage_EndMonitor(This) (This)->lpVtbl->EndMonitor(This)
#define ILayoutStorage_ReLayoutDocfile(This,pwcsNewDfName) (This)->lpVtbl->ReLayoutDocfile(This,pwcsNewDfName)
#define ILayoutStorage_ReLayoutDocfileOnILockBytes(This,pILockBytes) (This)->lpVtbl->ReLayoutDocfileOnILockBytes(This,pILockBytes)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ILayoutStorage_QueryInterface(ILayoutStorage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ILayoutStorage_AddRef(ILayoutStorage* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ILayoutStorage_Release(ILayoutStorage* This) {
    return This->lpVtbl->Release(This);
}
/*** ILayoutStorage methods ***/
static FORCEINLINE HRESULT ILayoutStorage_LayoutScript(ILayoutStorage* This,StorageLayout *pStorageLayout,DWORD nEntries,DWORD glfInterleavedFlag) {
    return This->lpVtbl->LayoutScript(This,pStorageLayout,nEntries,glfInterleavedFlag);
}
static FORCEINLINE HRESULT ILayoutStorage_BeginMonitor(ILayoutStorage* This) {
    return This->lpVtbl->BeginMonitor(This);
}
static FORCEINLINE HRESULT ILayoutStorage_EndMonitor(ILayoutStorage* This) {
    return This->lpVtbl->EndMonitor(This);
}
static FORCEINLINE HRESULT ILayoutStorage_ReLayoutDocfile(ILayoutStorage* This,OLECHAR *pwcsNewDfName) {
    return This->lpVtbl->ReLayoutDocfile(This,pwcsNewDfName);
}
static FORCEINLINE HRESULT ILayoutStorage_ReLayoutDocfileOnILockBytes(ILayoutStorage* This,ILockBytes *pILockBytes) {
    return This->lpVtbl->ReLayoutDocfileOnILockBytes(This,pILockBytes);
}
#endif
#endif

#endif

HRESULT __stdcall ILayoutStorage_LayoutScript_Proxy(
    ILayoutStorage* This,
    StorageLayout *pStorageLayout,
    DWORD nEntries,
    DWORD glfInterleavedFlag);
void __RPC_STUB ILayoutStorage_LayoutScript_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT __stdcall ILayoutStorage_BeginMonitor_Proxy(
    ILayoutStorage* This);
void __RPC_STUB ILayoutStorage_BeginMonitor_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT __stdcall ILayoutStorage_EndMonitor_Proxy(
    ILayoutStorage* This);
void __RPC_STUB ILayoutStorage_EndMonitor_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT __stdcall ILayoutStorage_ReLayoutDocfile_Proxy(
    ILayoutStorage* This,
    OLECHAR *pwcsNewDfName);
void __RPC_STUB ILayoutStorage_ReLayoutDocfile_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT __stdcall ILayoutStorage_ReLayoutDocfileOnILockBytes_Proxy(
    ILayoutStorage* This,
    ILockBytes *pILockBytes);
void __RPC_STUB ILayoutStorage_ReLayoutDocfileOnILockBytes_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ILayoutStorage_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IBlockingLock interface
 */
#ifndef __IBlockingLock_INTERFACE_DEFINED__
#define __IBlockingLock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBlockingLock, 0x30f3d47a, 0x6447, 0x11d1, 0x8e,0x3c, 0x00,0xc0,0x4f,0xb9,0x38,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("30f3d47a-6447-11d1-8e3c-00c04fb9386d")
IBlockingLock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Lock(
        DWORD dwTimeout) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unlock(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBlockingLock, 0x30f3d47a, 0x6447, 0x11d1, 0x8e,0x3c, 0x00,0xc0,0x4f,0xb9,0x38,0x6d)
#endif
#else
typedef struct IBlockingLockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBlockingLock* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBlockingLock* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBlockingLock* This);

    /*** IBlockingLock methods ***/
    HRESULT (STDMETHODCALLTYPE *Lock)(
        IBlockingLock* This,
        DWORD dwTimeout);

    HRESULT (STDMETHODCALLTYPE *Unlock)(
        IBlockingLock* This);

    END_INTERFACE
} IBlockingLockVtbl;
interface IBlockingLock {
    CONST_VTBL IBlockingLockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBlockingLock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBlockingLock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBlockingLock_Release(This) (This)->lpVtbl->Release(This)
/*** IBlockingLock methods ***/
#define IBlockingLock_Lock(This,dwTimeout) (This)->lpVtbl->Lock(This,dwTimeout)
#define IBlockingLock_Unlock(This) (This)->lpVtbl->Unlock(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IBlockingLock_QueryInterface(IBlockingLock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IBlockingLock_AddRef(IBlockingLock* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IBlockingLock_Release(IBlockingLock* This) {
    return This->lpVtbl->Release(This);
}
/*** IBlockingLock methods ***/
static FORCEINLINE HRESULT IBlockingLock_Lock(IBlockingLock* This,DWORD dwTimeout) {
    return This->lpVtbl->Lock(This,dwTimeout);
}
static FORCEINLINE HRESULT IBlockingLock_Unlock(IBlockingLock* This) {
    return This->lpVtbl->Unlock(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IBlockingLock_Lock_Proxy(
    IBlockingLock* This,
    DWORD dwTimeout);
void __RPC_STUB IBlockingLock_Lock_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBlockingLock_Unlock_Proxy(
    IBlockingLock* This);
void __RPC_STUB IBlockingLock_Unlock_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IBlockingLock_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITimeAndNoticeControl interface
 */
#ifndef __ITimeAndNoticeControl_INTERFACE_DEFINED__
#define __ITimeAndNoticeControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITimeAndNoticeControl, 0xbc0bf6ae, 0x8878, 0x11d1, 0x83,0xe9, 0x00,0xc0,0x4f,0xc2,0xc6,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bc0bf6ae-8878-11d1-83e9-00c04fc2c6d4")
ITimeAndNoticeControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SuppressChanges(
        DWORD res1,
        DWORD res2) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITimeAndNoticeControl, 0xbc0bf6ae, 0x8878, 0x11d1, 0x83,0xe9, 0x00,0xc0,0x4f,0xc2,0xc6,0xd4)
#endif
#else
typedef struct ITimeAndNoticeControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITimeAndNoticeControl* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITimeAndNoticeControl* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITimeAndNoticeControl* This);

    /*** ITimeAndNoticeControl methods ***/
    HRESULT (STDMETHODCALLTYPE *SuppressChanges)(
        ITimeAndNoticeControl* This,
        DWORD res1,
        DWORD res2);

    END_INTERFACE
} ITimeAndNoticeControlVtbl;
interface ITimeAndNoticeControl {
    CONST_VTBL ITimeAndNoticeControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITimeAndNoticeControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITimeAndNoticeControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITimeAndNoticeControl_Release(This) (This)->lpVtbl->Release(This)
/*** ITimeAndNoticeControl methods ***/
#define ITimeAndNoticeControl_SuppressChanges(This,res1,res2) (This)->lpVtbl->SuppressChanges(This,res1,res2)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ITimeAndNoticeControl_QueryInterface(ITimeAndNoticeControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ITimeAndNoticeControl_AddRef(ITimeAndNoticeControl* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ITimeAndNoticeControl_Release(ITimeAndNoticeControl* This) {
    return This->lpVtbl->Release(This);
}
/*** ITimeAndNoticeControl methods ***/
static FORCEINLINE HRESULT ITimeAndNoticeControl_SuppressChanges(ITimeAndNoticeControl* This,DWORD res1,DWORD res2) {
    return This->lpVtbl->SuppressChanges(This,res1,res2);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ITimeAndNoticeControl_SuppressChanges_Proxy(
    ITimeAndNoticeControl* This,
    DWORD res1,
    DWORD res2);
void __RPC_STUB ITimeAndNoticeControl_SuppressChanges_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ITimeAndNoticeControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOplockStorage interface
 */
#ifndef __IOplockStorage_INTERFACE_DEFINED__
#define __IOplockStorage_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOplockStorage, 0x8d19c834, 0x8879, 0x11d1, 0x83,0xe9, 0x00,0xc0,0x4f,0xc2,0xc6,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8d19c834-8879-11d1-83e9-00c04fc2c6d4")
IOplockStorage : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateStorageEx(
        LPCWSTR pwcsName,
        DWORD grfMode,
        DWORD stgfmt,
        DWORD grfAttrs,
        REFIID riid,
        void **ppstgOpen) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenStorageEx(
        LPCWSTR pwcsName,
        DWORD grfMode,
        DWORD stgfmt,
        DWORD grfAttrs,
        REFIID riid,
        void **ppstgOpen) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOplockStorage, 0x8d19c834, 0x8879, 0x11d1, 0x83,0xe9, 0x00,0xc0,0x4f,0xc2,0xc6,0xd4)
#endif
#else
typedef struct IOplockStorageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOplockStorage* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOplockStorage* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOplockStorage* This);

    /*** IOplockStorage methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateStorageEx)(
        IOplockStorage* This,
        LPCWSTR pwcsName,
        DWORD grfMode,
        DWORD stgfmt,
        DWORD grfAttrs,
        REFIID riid,
        void **ppstgOpen);

    HRESULT (STDMETHODCALLTYPE *OpenStorageEx)(
        IOplockStorage* This,
        LPCWSTR pwcsName,
        DWORD grfMode,
        DWORD stgfmt,
        DWORD grfAttrs,
        REFIID riid,
        void **ppstgOpen);

    END_INTERFACE
} IOplockStorageVtbl;
interface IOplockStorage {
    CONST_VTBL IOplockStorageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOplockStorage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOplockStorage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOplockStorage_Release(This) (This)->lpVtbl->Release(This)
/*** IOplockStorage methods ***/
#define IOplockStorage_CreateStorageEx(This,pwcsName,grfMode,stgfmt,grfAttrs,riid,ppstgOpen) (This)->lpVtbl->CreateStorageEx(This,pwcsName,grfMode,stgfmt,grfAttrs,riid,ppstgOpen)
#define IOplockStorage_OpenStorageEx(This,pwcsName,grfMode,stgfmt,grfAttrs,riid,ppstgOpen) (This)->lpVtbl->OpenStorageEx(This,pwcsName,grfMode,stgfmt,grfAttrs,riid,ppstgOpen)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IOplockStorage_QueryInterface(IOplockStorage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IOplockStorage_AddRef(IOplockStorage* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IOplockStorage_Release(IOplockStorage* This) {
    return This->lpVtbl->Release(This);
}
/*** IOplockStorage methods ***/
static FORCEINLINE HRESULT IOplockStorage_CreateStorageEx(IOplockStorage* This,LPCWSTR pwcsName,DWORD grfMode,DWORD stgfmt,DWORD grfAttrs,REFIID riid,void **ppstgOpen) {
    return This->lpVtbl->CreateStorageEx(This,pwcsName,grfMode,stgfmt,grfAttrs,riid,ppstgOpen);
}
static FORCEINLINE HRESULT IOplockStorage_OpenStorageEx(IOplockStorage* This,LPCWSTR pwcsName,DWORD grfMode,DWORD stgfmt,DWORD grfAttrs,REFIID riid,void **ppstgOpen) {
    return This->lpVtbl->OpenStorageEx(This,pwcsName,grfMode,stgfmt,grfAttrs,riid,ppstgOpen);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IOplockStorage_CreateStorageEx_Proxy(
    IOplockStorage* This,
    LPCWSTR pwcsName,
    DWORD grfMode,
    DWORD stgfmt,
    DWORD grfAttrs,
    REFIID riid,
    void **ppstgOpen);
void __RPC_STUB IOplockStorage_CreateStorageEx_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IOplockStorage_OpenStorageEx_Proxy(
    IOplockStorage* This,
    LPCWSTR pwcsName,
    DWORD grfMode,
    DWORD stgfmt,
    DWORD grfAttrs,
    REFIID riid,
    void **ppstgOpen);
void __RPC_STUB IOplockStorage_OpenStorageEx_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IOplockStorage_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IDirectWriterLock interface
 */
#ifndef __IDirectWriterLock_INTERFACE_DEFINED__
#define __IDirectWriterLock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectWriterLock, 0x0e6d4d92, 0x6738, 0x11cf, 0x96,0x08, 0x00,0xaa,0x00,0x68,0x0d,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0e6d4d92-6738-11cf-9608-00aa00680db4")
IDirectWriterLock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE WaitForWriteAccess(
        DWORD dwTimeout) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseWriteAccess(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE HaveWriteAccess(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectWriterLock, 0x0e6d4d92, 0x6738, 0x11cf, 0x96,0x08, 0x00,0xaa,0x00,0x68,0x0d,0xb4)
#endif
#else
typedef struct IDirectWriterLockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectWriterLock* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectWriterLock* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectWriterLock* This);

    /*** IDirectWriterLock methods ***/
    HRESULT (STDMETHODCALLTYPE *WaitForWriteAccess)(
        IDirectWriterLock* This,
        DWORD dwTimeout);

    HRESULT (STDMETHODCALLTYPE *ReleaseWriteAccess)(
        IDirectWriterLock* This);

    HRESULT (STDMETHODCALLTYPE *HaveWriteAccess)(
        IDirectWriterLock* This);

    END_INTERFACE
} IDirectWriterLockVtbl;
interface IDirectWriterLock {
    CONST_VTBL IDirectWriterLockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectWriterLock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectWriterLock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectWriterLock_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectWriterLock methods ***/
#define IDirectWriterLock_WaitForWriteAccess(This,dwTimeout) (This)->lpVtbl->WaitForWriteAccess(This,dwTimeout)
#define IDirectWriterLock_ReleaseWriteAccess(This) (This)->lpVtbl->ReleaseWriteAccess(This)
#define IDirectWriterLock_HaveWriteAccess(This) (This)->lpVtbl->HaveWriteAccess(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDirectWriterLock_QueryInterface(IDirectWriterLock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDirectWriterLock_AddRef(IDirectWriterLock* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDirectWriterLock_Release(IDirectWriterLock* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectWriterLock methods ***/
static FORCEINLINE HRESULT IDirectWriterLock_WaitForWriteAccess(IDirectWriterLock* This,DWORD dwTimeout) {
    return This->lpVtbl->WaitForWriteAccess(This,dwTimeout);
}
static FORCEINLINE HRESULT IDirectWriterLock_ReleaseWriteAccess(IDirectWriterLock* This) {
    return This->lpVtbl->ReleaseWriteAccess(This);
}
static FORCEINLINE HRESULT IDirectWriterLock_HaveWriteAccess(IDirectWriterLock* This) {
    return This->lpVtbl->HaveWriteAccess(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IDirectWriterLock_WaitForWriteAccess_Proxy(
    IDirectWriterLock* This,
    DWORD dwTimeout);
void __RPC_STUB IDirectWriterLock_WaitForWriteAccess_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDirectWriterLock_ReleaseWriteAccess_Proxy(
    IDirectWriterLock* This);
void __RPC_STUB IDirectWriterLock_ReleaseWriteAccess_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDirectWriterLock_HaveWriteAccess_Proxy(
    IDirectWriterLock* This);
void __RPC_STUB IDirectWriterLock_HaveWriteAccess_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IDirectWriterLock_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IUrlMon interface
 */
#ifndef __IUrlMon_INTERFACE_DEFINED__
#define __IUrlMon_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUrlMon, 0x00000026, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000026-0000-0000-c000-000000000046")
IUrlMon : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AsyncGetClassBits(
        REFCLSID rclsid,
        LPCWSTR pszTYPE,
        LPCWSTR pszExt,
        DWORD dwFileVersionMS,
        DWORD dwFileVersionLS,
        LPCWSTR pszCodeBase,
        IBindCtx *pbc,
        DWORD dwClassContext,
        REFIID riid,
        DWORD flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUrlMon, 0x00000026, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IUrlMonVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUrlMon* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUrlMon* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUrlMon* This);

    /*** IUrlMon methods ***/
    HRESULT (STDMETHODCALLTYPE *AsyncGetClassBits)(
        IUrlMon* This,
        REFCLSID rclsid,
        LPCWSTR pszTYPE,
        LPCWSTR pszExt,
        DWORD dwFileVersionMS,
        DWORD dwFileVersionLS,
        LPCWSTR pszCodeBase,
        IBindCtx *pbc,
        DWORD dwClassContext,
        REFIID riid,
        DWORD flags);

    END_INTERFACE
} IUrlMonVtbl;
interface IUrlMon {
    CONST_VTBL IUrlMonVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUrlMon_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUrlMon_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUrlMon_Release(This) (This)->lpVtbl->Release(This)
/*** IUrlMon methods ***/
#define IUrlMon_AsyncGetClassBits(This,rclsid,pszTYPE,pszExt,dwFileVersionMS,dwFileVersionLS,pszCodeBase,pbc,dwClassContext,riid,flags) (This)->lpVtbl->AsyncGetClassBits(This,rclsid,pszTYPE,pszExt,dwFileVersionMS,dwFileVersionLS,pszCodeBase,pbc,dwClassContext,riid,flags)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IUrlMon_QueryInterface(IUrlMon* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IUrlMon_AddRef(IUrlMon* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IUrlMon_Release(IUrlMon* This) {
    return This->lpVtbl->Release(This);
}
/*** IUrlMon methods ***/
static FORCEINLINE HRESULT IUrlMon_AsyncGetClassBits(IUrlMon* This,REFCLSID rclsid,LPCWSTR pszTYPE,LPCWSTR pszExt,DWORD dwFileVersionMS,DWORD dwFileVersionLS,LPCWSTR pszCodeBase,IBindCtx *pbc,DWORD dwClassContext,REFIID riid,DWORD flags) {
    return This->lpVtbl->AsyncGetClassBits(This,rclsid,pszTYPE,pszExt,dwFileVersionMS,dwFileVersionLS,pszCodeBase,pbc,dwClassContext,riid,flags);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IUrlMon_AsyncGetClassBits_Proxy(
    IUrlMon* This,
    REFCLSID rclsid,
    LPCWSTR pszTYPE,
    LPCWSTR pszExt,
    DWORD dwFileVersionMS,
    DWORD dwFileVersionLS,
    LPCWSTR pszCodeBase,
    IBindCtx *pbc,
    DWORD dwClassContext,
    REFIID riid,
    DWORD flags);
void __RPC_STUB IUrlMon_AsyncGetClassBits_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IUrlMon_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IForegroundTransfer interface
 */
#ifndef __IForegroundTransfer_INTERFACE_DEFINED__
#define __IForegroundTransfer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IForegroundTransfer, 0x00000145, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000145-0000-0000-c000-000000000046")
IForegroundTransfer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AllowForegroundTransfer(
        void *lpvReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IForegroundTransfer, 0x00000145, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IForegroundTransferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IForegroundTransfer* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IForegroundTransfer* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IForegroundTransfer* This);

    /*** IForegroundTransfer methods ***/
    HRESULT (STDMETHODCALLTYPE *AllowForegroundTransfer)(
        IForegroundTransfer* This,
        void *lpvReserved);

    END_INTERFACE
} IForegroundTransferVtbl;
interface IForegroundTransfer {
    CONST_VTBL IForegroundTransferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IForegroundTransfer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IForegroundTransfer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IForegroundTransfer_Release(This) (This)->lpVtbl->Release(This)
/*** IForegroundTransfer methods ***/
#define IForegroundTransfer_AllowForegroundTransfer(This,lpvReserved) (This)->lpVtbl->AllowForegroundTransfer(This,lpvReserved)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IForegroundTransfer_QueryInterface(IForegroundTransfer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IForegroundTransfer_AddRef(IForegroundTransfer* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IForegroundTransfer_Release(IForegroundTransfer* This) {
    return This->lpVtbl->Release(This);
}
/*** IForegroundTransfer methods ***/
static FORCEINLINE HRESULT IForegroundTransfer_AllowForegroundTransfer(IForegroundTransfer* This,void *lpvReserved) {
    return This->lpVtbl->AllowForegroundTransfer(This,lpvReserved);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IForegroundTransfer_AllowForegroundTransfer_Proxy(
    IForegroundTransfer* This,
    void *lpvReserved);
void __RPC_STUB IForegroundTransfer_AllowForegroundTransfer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IForegroundTransfer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IThumbnailExtractor interface
 */
#ifndef __IThumbnailExtractor_INTERFACE_DEFINED__
#define __IThumbnailExtractor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IThumbnailExtractor, 0x969dc708, 0x5c76, 0x11d1, 0x8d,0x86, 0x00,0x00,0xf8,0x04,0xb0,0x57);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("969dc708-5c76-11d1-8d86-0000f804b057")
IThumbnailExtractor : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ExtractThumbnail(
        IStorage *pStg,
        ULONG ulLength,
        ULONG ulHeight,
        ULONG *pulOutputLength,
        ULONG *pulOutputHeight,
        HBITMAP *phOutputBitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnFileUpdated(
        IStorage *pStg) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IThumbnailExtractor, 0x969dc708, 0x5c76, 0x11d1, 0x8d,0x86, 0x00,0x00,0xf8,0x04,0xb0,0x57)
#endif
#else
typedef struct IThumbnailExtractorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IThumbnailExtractor* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IThumbnailExtractor* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IThumbnailExtractor* This);

    /*** IThumbnailExtractor methods ***/
    HRESULT (STDMETHODCALLTYPE *ExtractThumbnail)(
        IThumbnailExtractor* This,
        IStorage *pStg,
        ULONG ulLength,
        ULONG ulHeight,
        ULONG *pulOutputLength,
        ULONG *pulOutputHeight,
        HBITMAP *phOutputBitmap);

    HRESULT (STDMETHODCALLTYPE *OnFileUpdated)(
        IThumbnailExtractor* This,
        IStorage *pStg);

    END_INTERFACE
} IThumbnailExtractorVtbl;
interface IThumbnailExtractor {
    CONST_VTBL IThumbnailExtractorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IThumbnailExtractor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IThumbnailExtractor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IThumbnailExtractor_Release(This) (This)->lpVtbl->Release(This)
/*** IThumbnailExtractor methods ***/
#define IThumbnailExtractor_ExtractThumbnail(This,pStg,ulLength,ulHeight,pulOutputLength,pulOutputHeight,phOutputBitmap) (This)->lpVtbl->ExtractThumbnail(This,pStg,ulLength,ulHeight,pulOutputLength,pulOutputHeight,phOutputBitmap)
#define IThumbnailExtractor_OnFileUpdated(This,pStg) (This)->lpVtbl->OnFileUpdated(This,pStg)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IThumbnailExtractor_QueryInterface(IThumbnailExtractor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IThumbnailExtractor_AddRef(IThumbnailExtractor* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IThumbnailExtractor_Release(IThumbnailExtractor* This) {
    return This->lpVtbl->Release(This);
}
/*** IThumbnailExtractor methods ***/
static FORCEINLINE HRESULT IThumbnailExtractor_ExtractThumbnail(IThumbnailExtractor* This,IStorage *pStg,ULONG ulLength,ULONG ulHeight,ULONG *pulOutputLength,ULONG *pulOutputHeight,HBITMAP *phOutputBitmap) {
    return This->lpVtbl->ExtractThumbnail(This,pStg,ulLength,ulHeight,pulOutputLength,pulOutputHeight,phOutputBitmap);
}
static FORCEINLINE HRESULT IThumbnailExtractor_OnFileUpdated(IThumbnailExtractor* This,IStorage *pStg) {
    return This->lpVtbl->OnFileUpdated(This,pStg);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IThumbnailExtractor_ExtractThumbnail_Proxy(
    IThumbnailExtractor* This,
    IStorage *pStg,
    ULONG ulLength,
    ULONG ulHeight,
    ULONG *pulOutputLength,
    ULONG *pulOutputHeight,
    HBITMAP *phOutputBitmap);
void __RPC_STUB IThumbnailExtractor_ExtractThumbnail_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IThumbnailExtractor_OnFileUpdated_Proxy(
    IThumbnailExtractor* This,
    IStorage *pStg);
void __RPC_STUB IThumbnailExtractor_OnFileUpdated_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IThumbnailExtractor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDummyHICONIncluder interface
 */
#ifndef __IDummyHICONIncluder_INTERFACE_DEFINED__
#define __IDummyHICONIncluder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDummyHICONIncluder, 0x947990de, 0xcc28, 0x11d2, 0xa0,0xf7, 0x00,0x80,0x5f,0x85,0x8f,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("947990de-cc28-11d2-a0f7-00805f858fb1")
IDummyHICONIncluder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Dummy(
        HICON h1,
        HDC h2) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDummyHICONIncluder, 0x947990de, 0xcc28, 0x11d2, 0xa0,0xf7, 0x00,0x80,0x5f,0x85,0x8f,0xb1)
#endif
#else
typedef struct IDummyHICONIncluderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDummyHICONIncluder* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDummyHICONIncluder* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDummyHICONIncluder* This);

    /*** IDummyHICONIncluder methods ***/
    HRESULT (STDMETHODCALLTYPE *Dummy)(
        IDummyHICONIncluder* This,
        HICON h1,
        HDC h2);

    END_INTERFACE
} IDummyHICONIncluderVtbl;
interface IDummyHICONIncluder {
    CONST_VTBL IDummyHICONIncluderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDummyHICONIncluder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDummyHICONIncluder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDummyHICONIncluder_Release(This) (This)->lpVtbl->Release(This)
/*** IDummyHICONIncluder methods ***/
#define IDummyHICONIncluder_Dummy(This,h1,h2) (This)->lpVtbl->Dummy(This,h1,h2)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDummyHICONIncluder_QueryInterface(IDummyHICONIncluder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDummyHICONIncluder_AddRef(IDummyHICONIncluder* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDummyHICONIncluder_Release(IDummyHICONIncluder* This) {
    return This->lpVtbl->Release(This);
}
/*** IDummyHICONIncluder methods ***/
static FORCEINLINE HRESULT IDummyHICONIncluder_Dummy(IDummyHICONIncluder* This,HICON h1,HDC h2) {
    return This->lpVtbl->Dummy(This,h1,h2);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IDummyHICONIncluder_Dummy_Proxy(
    IDummyHICONIncluder* This,
    HICON h1,
    HDC h2);
void __RPC_STUB IDummyHICONIncluder_Dummy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IDummyHICONIncluder_INTERFACE_DEFINED__ */

typedef enum tagApplicationType {
    ServerApplication = 0,
    LibraryApplication = 1
} ApplicationType;
typedef enum tagShutdownType {
    IdleShutdown = 0,
    ForcedShutdown = 1
} ShutdownType;
/*****************************************************************************
 * IProcessLock interface
 */
#ifndef __IProcessLock_INTERFACE_DEFINED__
#define __IProcessLock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProcessLock, 0x000001d5, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001d5-0000-0000-c000-000000000046")
IProcessLock : public IUnknown
{
    virtual ULONG STDMETHODCALLTYPE AddRefOnProcess(
        ) = 0;

    virtual ULONG STDMETHODCALLTYPE ReleaseRefOnProcess(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProcessLock, 0x000001d5, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IProcessLockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProcessLock* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProcessLock* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProcessLock* This);

    /*** IProcessLock methods ***/
    ULONG (STDMETHODCALLTYPE *AddRefOnProcess)(
        IProcessLock* This);

    ULONG (STDMETHODCALLTYPE *ReleaseRefOnProcess)(
        IProcessLock* This);

    END_INTERFACE
} IProcessLockVtbl;
interface IProcessLock {
    CONST_VTBL IProcessLockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProcessLock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProcessLock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProcessLock_Release(This) (This)->lpVtbl->Release(This)
/*** IProcessLock methods ***/
#define IProcessLock_AddRefOnProcess(This) (This)->lpVtbl->AddRefOnProcess(This)
#define IProcessLock_ReleaseRefOnProcess(This) (This)->lpVtbl->ReleaseRefOnProcess(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IProcessLock_QueryInterface(IProcessLock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IProcessLock_AddRef(IProcessLock* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IProcessLock_Release(IProcessLock* This) {
    return This->lpVtbl->Release(This);
}
/*** IProcessLock methods ***/
static FORCEINLINE ULONG IProcessLock_AddRefOnProcess(IProcessLock* This) {
    return This->lpVtbl->AddRefOnProcess(This);
}
static FORCEINLINE ULONG IProcessLock_ReleaseRefOnProcess(IProcessLock* This) {
    return This->lpVtbl->ReleaseRefOnProcess(This);
}
#endif
#endif

#endif

ULONG STDMETHODCALLTYPE IProcessLock_AddRefOnProcess_Proxy(
    IProcessLock* This);
void __RPC_STUB IProcessLock_AddRefOnProcess_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
ULONG STDMETHODCALLTYPE IProcessLock_ReleaseRefOnProcess_Proxy(
    IProcessLock* This);
void __RPC_STUB IProcessLock_ReleaseRefOnProcess_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IProcessLock_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISurrogateService interface
 */
#ifndef __ISurrogateService_INTERFACE_DEFINED__
#define __ISurrogateService_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISurrogateService, 0x000001d4, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001d4-0000-0000-c000-000000000046")
ISurrogateService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Init(
        REFGUID rguidProcessID,
        IProcessLock *pProcessLock,
        WINBOOL *pfApplicationAware) = 0;

    virtual HRESULT STDMETHODCALLTYPE ApplicationLaunch(
        REFGUID rguidApplID,
        ApplicationType appType) = 0;

    virtual HRESULT STDMETHODCALLTYPE ApplicationFree(
        REFGUID rguidApplID) = 0;

    virtual HRESULT STDMETHODCALLTYPE CatalogRefresh(
        ULONG ulReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessShutdown(
        ShutdownType shutdownType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISurrogateService, 0x000001d4, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISurrogateServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISurrogateService* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISurrogateService* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISurrogateService* This);

    /*** ISurrogateService methods ***/
    HRESULT (STDMETHODCALLTYPE *Init)(
        ISurrogateService* This,
        REFGUID rguidProcessID,
        IProcessLock *pProcessLock,
        WINBOOL *pfApplicationAware);

    HRESULT (STDMETHODCALLTYPE *ApplicationLaunch)(
        ISurrogateService* This,
        REFGUID rguidApplID,
        ApplicationType appType);

    HRESULT (STDMETHODCALLTYPE *ApplicationFree)(
        ISurrogateService* This,
        REFGUID rguidApplID);

    HRESULT (STDMETHODCALLTYPE *CatalogRefresh)(
        ISurrogateService* This,
        ULONG ulReserved);

    HRESULT (STDMETHODCALLTYPE *ProcessShutdown)(
        ISurrogateService* This,
        ShutdownType shutdownType);

    END_INTERFACE
} ISurrogateServiceVtbl;
interface ISurrogateService {
    CONST_VTBL ISurrogateServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISurrogateService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISurrogateService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISurrogateService_Release(This) (This)->lpVtbl->Release(This)
/*** ISurrogateService methods ***/
#define ISurrogateService_Init(This,rguidProcessID,pProcessLock,pfApplicationAware) (This)->lpVtbl->Init(This,rguidProcessID,pProcessLock,pfApplicationAware)
#define ISurrogateService_ApplicationLaunch(This,rguidApplID,appType) (This)->lpVtbl->ApplicationLaunch(This,rguidApplID,appType)
#define ISurrogateService_ApplicationFree(This,rguidApplID) (This)->lpVtbl->ApplicationFree(This,rguidApplID)
#define ISurrogateService_CatalogRefresh(This,ulReserved) (This)->lpVtbl->CatalogRefresh(This,ulReserved)
#define ISurrogateService_ProcessShutdown(This,shutdownType) (This)->lpVtbl->ProcessShutdown(This,shutdownType)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT ISurrogateService_QueryInterface(ISurrogateService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG ISurrogateService_AddRef(ISurrogateService* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG ISurrogateService_Release(ISurrogateService* This) {
    return This->lpVtbl->Release(This);
}
/*** ISurrogateService methods ***/
static FORCEINLINE HRESULT ISurrogateService_Init(ISurrogateService* This,REFGUID rguidProcessID,IProcessLock *pProcessLock,WINBOOL *pfApplicationAware) {
    return This->lpVtbl->Init(This,rguidProcessID,pProcessLock,pfApplicationAware);
}
static FORCEINLINE HRESULT ISurrogateService_ApplicationLaunch(ISurrogateService* This,REFGUID rguidApplID,ApplicationType appType) {
    return This->lpVtbl->ApplicationLaunch(This,rguidApplID,appType);
}
static FORCEINLINE HRESULT ISurrogateService_ApplicationFree(ISurrogateService* This,REFGUID rguidApplID) {
    return This->lpVtbl->ApplicationFree(This,rguidApplID);
}
static FORCEINLINE HRESULT ISurrogateService_CatalogRefresh(ISurrogateService* This,ULONG ulReserved) {
    return This->lpVtbl->CatalogRefresh(This,ulReserved);
}
static FORCEINLINE HRESULT ISurrogateService_ProcessShutdown(ISurrogateService* This,ShutdownType shutdownType) {
    return This->lpVtbl->ProcessShutdown(This,shutdownType);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISurrogateService_Init_Proxy(
    ISurrogateService* This,
    REFGUID rguidProcessID,
    IProcessLock *pProcessLock,
    WINBOOL *pfApplicationAware);
void __RPC_STUB ISurrogateService_Init_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISurrogateService_ApplicationLaunch_Proxy(
    ISurrogateService* This,
    REFGUID rguidApplID,
    ApplicationType appType);
void __RPC_STUB ISurrogateService_ApplicationLaunch_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISurrogateService_ApplicationFree_Proxy(
    ISurrogateService* This,
    REFGUID rguidApplID);
void __RPC_STUB ISurrogateService_ApplicationFree_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISurrogateService_CatalogRefresh_Proxy(
    ISurrogateService* This,
    ULONG ulReserved);
void __RPC_STUB ISurrogateService_CatalogRefresh_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISurrogateService_ProcessShutdown_Proxy(
    ISurrogateService* This,
    ShutdownType shutdownType);
void __RPC_STUB ISurrogateService_ProcessShutdown_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __ISurrogateService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInitializeSpy interface
 */
#ifndef __IInitializeSpy_INTERFACE_DEFINED__
#define __IInitializeSpy_INTERFACE_DEFINED__

typedef IInitializeSpy *LPINITIALIZESPY;
DEFINE_GUID(IID_IInitializeSpy, 0x00000034, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000034-0000-0000-c000-000000000046")
IInitializeSpy : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PreInitialize(
        DWORD dwCoInit,
        DWORD dwCurThreadAptRefs) = 0;

    virtual HRESULT STDMETHODCALLTYPE PostInitialize(
        HRESULT hrCoInit,
        DWORD dwCoInit,
        DWORD dwNewThreadAptRefs) = 0;

    virtual HRESULT STDMETHODCALLTYPE PreUninitialize(
        DWORD dwCurThreadAptRefs) = 0;

    virtual HRESULT STDMETHODCALLTYPE PostUninitialize(
        DWORD dwNewThreadAptRefs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInitializeSpy, 0x00000034, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IInitializeSpyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInitializeSpy* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInitializeSpy* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInitializeSpy* This);

    /*** IInitializeSpy methods ***/
    HRESULT (STDMETHODCALLTYPE *PreInitialize)(
        IInitializeSpy* This,
        DWORD dwCoInit,
        DWORD dwCurThreadAptRefs);

    HRESULT (STDMETHODCALLTYPE *PostInitialize)(
        IInitializeSpy* This,
        HRESULT hrCoInit,
        DWORD dwCoInit,
        DWORD dwNewThreadAptRefs);

    HRESULT (STDMETHODCALLTYPE *PreUninitialize)(
        IInitializeSpy* This,
        DWORD dwCurThreadAptRefs);

    HRESULT (STDMETHODCALLTYPE *PostUninitialize)(
        IInitializeSpy* This,
        DWORD dwNewThreadAptRefs);

    END_INTERFACE
} IInitializeSpyVtbl;
interface IInitializeSpy {
    CONST_VTBL IInitializeSpyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInitializeSpy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInitializeSpy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInitializeSpy_Release(This) (This)->lpVtbl->Release(This)
/*** IInitializeSpy methods ***/
#define IInitializeSpy_PreInitialize(This,dwCoInit,dwCurThreadAptRefs) (This)->lpVtbl->PreInitialize(This,dwCoInit,dwCurThreadAptRefs)
#define IInitializeSpy_PostInitialize(This,hrCoInit,dwCoInit,dwNewThreadAptRefs) (This)->lpVtbl->PostInitialize(This,hrCoInit,dwCoInit,dwNewThreadAptRefs)
#define IInitializeSpy_PreUninitialize(This,dwCurThreadAptRefs) (This)->lpVtbl->PreUninitialize(This,dwCurThreadAptRefs)
#define IInitializeSpy_PostUninitialize(This,dwNewThreadAptRefs) (This)->lpVtbl->PostUninitialize(This,dwNewThreadAptRefs)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IInitializeSpy_QueryInterface(IInitializeSpy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IInitializeSpy_AddRef(IInitializeSpy* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IInitializeSpy_Release(IInitializeSpy* This) {
    return This->lpVtbl->Release(This);
}
/*** IInitializeSpy methods ***/
static FORCEINLINE HRESULT IInitializeSpy_PreInitialize(IInitializeSpy* This,DWORD dwCoInit,DWORD dwCurThreadAptRefs) {
    return This->lpVtbl->PreInitialize(This,dwCoInit,dwCurThreadAptRefs);
}
static FORCEINLINE HRESULT IInitializeSpy_PostInitialize(IInitializeSpy* This,HRESULT hrCoInit,DWORD dwCoInit,DWORD dwNewThreadAptRefs) {
    return This->lpVtbl->PostInitialize(This,hrCoInit,dwCoInit,dwNewThreadAptRefs);
}
static FORCEINLINE HRESULT IInitializeSpy_PreUninitialize(IInitializeSpy* This,DWORD dwCurThreadAptRefs) {
    return This->lpVtbl->PreUninitialize(This,dwCurThreadAptRefs);
}
static FORCEINLINE HRESULT IInitializeSpy_PostUninitialize(IInitializeSpy* This,DWORD dwNewThreadAptRefs) {
    return This->lpVtbl->PostUninitialize(This,dwNewThreadAptRefs);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IInitializeSpy_PreInitialize_Proxy(
    IInitializeSpy* This,
    DWORD dwCoInit,
    DWORD dwCurThreadAptRefs);
void __RPC_STUB IInitializeSpy_PreInitialize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IInitializeSpy_PostInitialize_Proxy(
    IInitializeSpy* This,
    HRESULT hrCoInit,
    DWORD dwCoInit,
    DWORD dwNewThreadAptRefs);
void __RPC_STUB IInitializeSpy_PostInitialize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IInitializeSpy_PreUninitialize_Proxy(
    IInitializeSpy* This,
    DWORD dwCurThreadAptRefs);
void __RPC_STUB IInitializeSpy_PreUninitialize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IInitializeSpy_PostUninitialize_Proxy(
    IInitializeSpy* This,
    DWORD dwNewThreadAptRefs);
void __RPC_STUB IInitializeSpy_PostUninitialize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IInitializeSpy_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IApartmentShutdown interface
 */
#ifndef __IApartmentShutdown_INTERFACE_DEFINED__
#define __IApartmentShutdown_INTERFACE_DEFINED__

DEFINE_GUID(IID_IApartmentShutdown, 0xa2f05a09, 0x27a2, 0x42b5, 0xbc,0x0e, 0xac,0x16,0x3e,0xf4,0x9d,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a2f05a09-27a2-42b5-bc0e-ac163ef49d9b")
IApartmentShutdown : public IUnknown
{
    virtual void STDMETHODCALLTYPE OnUninitialize(
        UINT64 ui64ApartmentIdentifier) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IApartmentShutdown, 0xa2f05a09, 0x27a2, 0x42b5, 0xbc,0x0e, 0xac,0x16,0x3e,0xf4,0x9d,0x9b)
#endif
#else
typedef struct IApartmentShutdownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IApartmentShutdown* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IApartmentShutdown* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IApartmentShutdown* This);

    /*** IApartmentShutdown methods ***/
    void (STDMETHODCALLTYPE *OnUninitialize)(
        IApartmentShutdown* This,
        UINT64 ui64ApartmentIdentifier);

    END_INTERFACE
} IApartmentShutdownVtbl;
interface IApartmentShutdown {
    CONST_VTBL IApartmentShutdownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IApartmentShutdown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IApartmentShutdown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IApartmentShutdown_Release(This) (This)->lpVtbl->Release(This)
/*** IApartmentShutdown methods ***/
#define IApartmentShutdown_OnUninitialize(This,ui64ApartmentIdentifier) (This)->lpVtbl->OnUninitialize(This,ui64ApartmentIdentifier)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IApartmentShutdown_QueryInterface(IApartmentShutdown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IApartmentShutdown_AddRef(IApartmentShutdown* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IApartmentShutdown_Release(IApartmentShutdown* This) {
    return This->lpVtbl->Release(This);
}
/*** IApartmentShutdown methods ***/
static FORCEINLINE void IApartmentShutdown_OnUninitialize(IApartmentShutdown* This,UINT64 ui64ApartmentIdentifier) {
    This->lpVtbl->OnUninitialize(This,ui64ApartmentIdentifier);
}
#endif
#endif

#endif

void STDMETHODCALLTYPE IApartmentShutdown_OnUninitialize_Proxy(
    IApartmentShutdown* This,
    UINT64 ui64ApartmentIdentifier);
void __RPC_STUB IApartmentShutdown_OnUninitialize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IApartmentShutdown_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER SNB_UserSize     (ULONG *, ULONG, SNB *);
unsigned char * __RPC_USER SNB_UserMarshal  (ULONG *, unsigned char *, SNB *);
unsigned char * __RPC_USER SNB_UserUnmarshal(ULONG *, unsigned char *, SNB *);
void            __RPC_USER SNB_UserFree     (ULONG *, SNB *);
ULONG           __RPC_USER CLIPFORMAT_UserSize     (ULONG *, ULONG, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserMarshal  (ULONG *, unsigned char *, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserUnmarshal(ULONG *, unsigned char *, CLIPFORMAT *);
void            __RPC_USER CLIPFORMAT_UserFree     (ULONG *, CLIPFORMAT *);
ULONG           __RPC_USER STGMEDIUM_UserSize     (ULONG *, ULONG, STGMEDIUM *);
unsigned char * __RPC_USER STGMEDIUM_UserMarshal  (ULONG *, unsigned char *, STGMEDIUM *);
unsigned char * __RPC_USER STGMEDIUM_UserUnmarshal(ULONG *, unsigned char *, STGMEDIUM *);
void            __RPC_USER STGMEDIUM_UserFree     (ULONG *, STGMEDIUM *);
ULONG           __RPC_USER ASYNC_STGMEDIUM_UserSize     (ULONG *, ULONG, ASYNC_STGMEDIUM *);
unsigned char * __RPC_USER ASYNC_STGMEDIUM_UserMarshal  (ULONG *, unsigned char *, ASYNC_STGMEDIUM *);
unsigned char * __RPC_USER ASYNC_STGMEDIUM_UserUnmarshal(ULONG *, unsigned char *, ASYNC_STGMEDIUM *);
void            __RPC_USER ASYNC_STGMEDIUM_UserFree     (ULONG *, ASYNC_STGMEDIUM *);
ULONG           __RPC_USER FLAG_STGMEDIUM_UserSize     (ULONG *, ULONG, FLAG_STGMEDIUM *);
unsigned char * __RPC_USER FLAG_STGMEDIUM_UserMarshal  (ULONG *, unsigned char *, FLAG_STGMEDIUM *);
unsigned char * __RPC_USER FLAG_STGMEDIUM_UserUnmarshal(ULONG *, unsigned char *, FLAG_STGMEDIUM *);
void            __RPC_USER FLAG_STGMEDIUM_UserFree     (ULONG *, FLAG_STGMEDIUM *);
ULONG           __RPC_USER HBITMAP_UserSize     (ULONG *, ULONG, HBITMAP *);
unsigned char * __RPC_USER HBITMAP_UserMarshal  (ULONG *, unsigned char *, HBITMAP *);
unsigned char * __RPC_USER HBITMAP_UserUnmarshal(ULONG *, unsigned char *, HBITMAP *);
void            __RPC_USER HBITMAP_UserFree     (ULONG *, HBITMAP *);
ULONG           __RPC_USER HICON_UserSize     (ULONG *, ULONG, HICON *);
unsigned char * __RPC_USER HICON_UserMarshal  (ULONG *, unsigned char *, HICON *);
unsigned char * __RPC_USER HICON_UserUnmarshal(ULONG *, unsigned char *, HICON *);
void            __RPC_USER HICON_UserFree     (ULONG *, HICON *);
ULONG           __RPC_USER HDC_UserSize     (ULONG *, ULONG, HDC *);
unsigned char * __RPC_USER HDC_UserMarshal  (ULONG *, unsigned char *, HDC *);
unsigned char * __RPC_USER HDC_UserUnmarshal(ULONG *, unsigned char *, HDC *);
void            __RPC_USER HDC_UserFree     (ULONG *, HDC *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __objidl_h__ */
