/*** Autogenerated by WIDL 1.6 from direct-x/include/qnetwork.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __qnetwork_h__
#define __qnetwork_h__

/* Forward declarations */

#ifndef __IAMMediaContent_FWD_DEFINED__
#define __IAMMediaContent_FWD_DEFINED__
typedef interface IAMMediaContent IAMMediaContent;
#endif

#ifndef __IAMNetworkStatus_FWD_DEFINED__
#define __IAMNetworkStatus_FWD_DEFINED__
typedef interface IAMNetworkStatus IAMNetworkStatus;
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <strmif.h>
#include <wtypes.h>

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************************
 * IAMMediaContent interface
 */
#ifndef __IAMMediaContent_INTERFACE_DEFINED__
#define __IAMMediaContent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMMediaContent, 0xfa2aa8f4, 0x8b62, 0x11d0, 0xa5,0x20, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa2aa8f4-8b62-11d0-a520-000000000000")
IAMMediaContent : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_AuthorName(
        BSTR *pbstrAuthorName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Title(
        BSTR *pbstrTitle) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Rating(
        BSTR *pbstrRating) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *pbstrDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Copyright(
        BSTR *pbstrCopyright) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BaseURL(
        BSTR *pbstrBaseURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LogoURL(
        BSTR *pbstrLogoURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LogoIconURL(
        BSTR *pbstrLogoURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WatermarkURL(
        BSTR *pbstrWatermarkURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MoreInfoURL(
        BSTR *pbstrMoreInfoURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MoreInfoBannerImage(
        BSTR *pbstrMoreInfoBannerImage) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MoreInfoBannerURL(
        BSTR *pbstrMoreInfoBannerURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MoreInfoText(
        BSTR *pbstrMoreInfoText) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMMediaContent, 0xfa2aa8f4, 0x8b62, 0x11d0, 0xa5,0x20, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IAMMediaContentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMMediaContent* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMMediaContent* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMMediaContent* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IAMMediaContent* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IAMMediaContent* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IAMMediaContent* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IAMMediaContent* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IAMMediaContent methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AuthorName)(
        IAMMediaContent* This,
        BSTR *pbstrAuthorName);

    HRESULT (STDMETHODCALLTYPE *get_Title)(
        IAMMediaContent* This,
        BSTR *pbstrTitle);

    HRESULT (STDMETHODCALLTYPE *get_Rating)(
        IAMMediaContent* This,
        BSTR *pbstrRating);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IAMMediaContent* This,
        BSTR *pbstrDescription);

    HRESULT (STDMETHODCALLTYPE *get_Copyright)(
        IAMMediaContent* This,
        BSTR *pbstrCopyright);

    HRESULT (STDMETHODCALLTYPE *get_BaseURL)(
        IAMMediaContent* This,
        BSTR *pbstrBaseURL);

    HRESULT (STDMETHODCALLTYPE *get_LogoURL)(
        IAMMediaContent* This,
        BSTR *pbstrLogoURL);

    HRESULT (STDMETHODCALLTYPE *get_LogoIconURL)(
        IAMMediaContent* This,
        BSTR *pbstrLogoURL);

    HRESULT (STDMETHODCALLTYPE *get_WatermarkURL)(
        IAMMediaContent* This,
        BSTR *pbstrWatermarkURL);

    HRESULT (STDMETHODCALLTYPE *get_MoreInfoURL)(
        IAMMediaContent* This,
        BSTR *pbstrMoreInfoURL);

    HRESULT (STDMETHODCALLTYPE *get_MoreInfoBannerImage)(
        IAMMediaContent* This,
        BSTR *pbstrMoreInfoBannerImage);

    HRESULT (STDMETHODCALLTYPE *get_MoreInfoBannerURL)(
        IAMMediaContent* This,
        BSTR *pbstrMoreInfoBannerURL);

    HRESULT (STDMETHODCALLTYPE *get_MoreInfoText)(
        IAMMediaContent* This,
        BSTR *pbstrMoreInfoText);

    END_INTERFACE
} IAMMediaContentVtbl;
interface IAMMediaContent {
    CONST_VTBL IAMMediaContentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMMediaContent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMMediaContent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMMediaContent_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IAMMediaContent_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IAMMediaContent_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IAMMediaContent_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IAMMediaContent_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IAMMediaContent methods ***/
#define IAMMediaContent_get_AuthorName(This,pbstrAuthorName) (This)->lpVtbl->get_AuthorName(This,pbstrAuthorName)
#define IAMMediaContent_get_Title(This,pbstrTitle) (This)->lpVtbl->get_Title(This,pbstrTitle)
#define IAMMediaContent_get_Rating(This,pbstrRating) (This)->lpVtbl->get_Rating(This,pbstrRating)
#define IAMMediaContent_get_Description(This,pbstrDescription) (This)->lpVtbl->get_Description(This,pbstrDescription)
#define IAMMediaContent_get_Copyright(This,pbstrCopyright) (This)->lpVtbl->get_Copyright(This,pbstrCopyright)
#define IAMMediaContent_get_BaseURL(This,pbstrBaseURL) (This)->lpVtbl->get_BaseURL(This,pbstrBaseURL)
#define IAMMediaContent_get_LogoURL(This,pbstrLogoURL) (This)->lpVtbl->get_LogoURL(This,pbstrLogoURL)
#define IAMMediaContent_get_LogoIconURL(This,pbstrLogoURL) (This)->lpVtbl->get_LogoIconURL(This,pbstrLogoURL)
#define IAMMediaContent_get_WatermarkURL(This,pbstrWatermarkURL) (This)->lpVtbl->get_WatermarkURL(This,pbstrWatermarkURL)
#define IAMMediaContent_get_MoreInfoURL(This,pbstrMoreInfoURL) (This)->lpVtbl->get_MoreInfoURL(This,pbstrMoreInfoURL)
#define IAMMediaContent_get_MoreInfoBannerImage(This,pbstrMoreInfoBannerImage) (This)->lpVtbl->get_MoreInfoBannerImage(This,pbstrMoreInfoBannerImage)
#define IAMMediaContent_get_MoreInfoBannerURL(This,pbstrMoreInfoBannerURL) (This)->lpVtbl->get_MoreInfoBannerURL(This,pbstrMoreInfoBannerURL)
#define IAMMediaContent_get_MoreInfoText(This,pbstrMoreInfoText) (This)->lpVtbl->get_MoreInfoText(This,pbstrMoreInfoText)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAMMediaContent_QueryInterface(IAMMediaContent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAMMediaContent_AddRef(IAMMediaContent* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAMMediaContent_Release(IAMMediaContent* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IAMMediaContent_GetTypeInfoCount(IAMMediaContent* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IAMMediaContent_GetTypeInfo(IAMMediaContent* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IAMMediaContent_GetIDsOfNames(IAMMediaContent* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IAMMediaContent_Invoke(IAMMediaContent* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IAMMediaContent methods ***/
static FORCEINLINE HRESULT IAMMediaContent_get_AuthorName(IAMMediaContent* This,BSTR *pbstrAuthorName) {
    return This->lpVtbl->get_AuthorName(This,pbstrAuthorName);
}
static FORCEINLINE HRESULT IAMMediaContent_get_Title(IAMMediaContent* This,BSTR *pbstrTitle) {
    return This->lpVtbl->get_Title(This,pbstrTitle);
}
static FORCEINLINE HRESULT IAMMediaContent_get_Rating(IAMMediaContent* This,BSTR *pbstrRating) {
    return This->lpVtbl->get_Rating(This,pbstrRating);
}
static FORCEINLINE HRESULT IAMMediaContent_get_Description(IAMMediaContent* This,BSTR *pbstrDescription) {
    return This->lpVtbl->get_Description(This,pbstrDescription);
}
static FORCEINLINE HRESULT IAMMediaContent_get_Copyright(IAMMediaContent* This,BSTR *pbstrCopyright) {
    return This->lpVtbl->get_Copyright(This,pbstrCopyright);
}
static FORCEINLINE HRESULT IAMMediaContent_get_BaseURL(IAMMediaContent* This,BSTR *pbstrBaseURL) {
    return This->lpVtbl->get_BaseURL(This,pbstrBaseURL);
}
static FORCEINLINE HRESULT IAMMediaContent_get_LogoURL(IAMMediaContent* This,BSTR *pbstrLogoURL) {
    return This->lpVtbl->get_LogoURL(This,pbstrLogoURL);
}
static FORCEINLINE HRESULT IAMMediaContent_get_LogoIconURL(IAMMediaContent* This,BSTR *pbstrLogoURL) {
    return This->lpVtbl->get_LogoIconURL(This,pbstrLogoURL);
}
static FORCEINLINE HRESULT IAMMediaContent_get_WatermarkURL(IAMMediaContent* This,BSTR *pbstrWatermarkURL) {
    return This->lpVtbl->get_WatermarkURL(This,pbstrWatermarkURL);
}
static FORCEINLINE HRESULT IAMMediaContent_get_MoreInfoURL(IAMMediaContent* This,BSTR *pbstrMoreInfoURL) {
    return This->lpVtbl->get_MoreInfoURL(This,pbstrMoreInfoURL);
}
static FORCEINLINE HRESULT IAMMediaContent_get_MoreInfoBannerImage(IAMMediaContent* This,BSTR *pbstrMoreInfoBannerImage) {
    return This->lpVtbl->get_MoreInfoBannerImage(This,pbstrMoreInfoBannerImage);
}
static FORCEINLINE HRESULT IAMMediaContent_get_MoreInfoBannerURL(IAMMediaContent* This,BSTR *pbstrMoreInfoBannerURL) {
    return This->lpVtbl->get_MoreInfoBannerURL(This,pbstrMoreInfoBannerURL);
}
static FORCEINLINE HRESULT IAMMediaContent_get_MoreInfoText(IAMMediaContent* This,BSTR *pbstrMoreInfoText) {
    return This->lpVtbl->get_MoreInfoText(This,pbstrMoreInfoText);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAMMediaContent_get_AuthorName_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrAuthorName);
void __RPC_STUB IAMMediaContent_get_AuthorName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_Title_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrTitle);
void __RPC_STUB IAMMediaContent_get_Title_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_Rating_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrRating);
void __RPC_STUB IAMMediaContent_get_Rating_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_Description_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrDescription);
void __RPC_STUB IAMMediaContent_get_Description_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_Copyright_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrCopyright);
void __RPC_STUB IAMMediaContent_get_Copyright_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_BaseURL_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrBaseURL);
void __RPC_STUB IAMMediaContent_get_BaseURL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_LogoURL_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrLogoURL);
void __RPC_STUB IAMMediaContent_get_LogoURL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_LogoIconURL_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrLogoURL);
void __RPC_STUB IAMMediaContent_get_LogoIconURL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_WatermarkURL_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrWatermarkURL);
void __RPC_STUB IAMMediaContent_get_WatermarkURL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_MoreInfoURL_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrMoreInfoURL);
void __RPC_STUB IAMMediaContent_get_MoreInfoURL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_MoreInfoBannerImage_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrMoreInfoBannerImage);
void __RPC_STUB IAMMediaContent_get_MoreInfoBannerImage_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_MoreInfoBannerURL_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrMoreInfoBannerURL);
void __RPC_STUB IAMMediaContent_get_MoreInfoBannerURL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMMediaContent_get_MoreInfoText_Proxy(
    IAMMediaContent* This,
    BSTR *pbstrMoreInfoText);
void __RPC_STUB IAMMediaContent_get_MoreInfoText_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IAMMediaContent_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMNetworkStatus interface
 */
#ifndef __IAMNetworkStatus_INTERFACE_DEFINED__
#define __IAMNetworkStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMNetworkStatus, 0xfa2aa8f3, 0x8b62, 0x11d0, 0xa5,0x20, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa2aa8f3-8b62-11d0-a520-000000000000")
IAMNetworkStatus : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ReceivedPackets(
        LONG *pReceivedPackets) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RecoveredPackets(
        LONG *pRecoveredPackets) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LostPackets(
        LONG *pLostPackets) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReceptionQuality(
        LONG *pReceptionQuality) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BufferingCount(
        LONG *pBufferingCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsBroadcast(
        VARIANT_BOOL *pIsBroadcast) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BufferingProgress(
        LONG *pBufferingProgress) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMNetworkStatus, 0xfa2aa8f3, 0x8b62, 0x11d0, 0xa5,0x20, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IAMNetworkStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMNetworkStatus* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMNetworkStatus* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMNetworkStatus* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IAMNetworkStatus* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IAMNetworkStatus* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IAMNetworkStatus* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IAMNetworkStatus* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IAMNetworkStatus methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ReceivedPackets)(
        IAMNetworkStatus* This,
        LONG *pReceivedPackets);

    HRESULT (STDMETHODCALLTYPE *get_RecoveredPackets)(
        IAMNetworkStatus* This,
        LONG *pRecoveredPackets);

    HRESULT (STDMETHODCALLTYPE *get_LostPackets)(
        IAMNetworkStatus* This,
        LONG *pLostPackets);

    HRESULT (STDMETHODCALLTYPE *get_ReceptionQuality)(
        IAMNetworkStatus* This,
        LONG *pReceptionQuality);

    HRESULT (STDMETHODCALLTYPE *get_BufferingCount)(
        IAMNetworkStatus* This,
        LONG *pBufferingCount);

    HRESULT (STDMETHODCALLTYPE *get_IsBroadcast)(
        IAMNetworkStatus* This,
        VARIANT_BOOL *pIsBroadcast);

    HRESULT (STDMETHODCALLTYPE *get_BufferingProgress)(
        IAMNetworkStatus* This,
        LONG *pBufferingProgress);

    END_INTERFACE
} IAMNetworkStatusVtbl;
interface IAMNetworkStatus {
    CONST_VTBL IAMNetworkStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMNetworkStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMNetworkStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMNetworkStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IAMNetworkStatus_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IAMNetworkStatus_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IAMNetworkStatus_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IAMNetworkStatus_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IAMNetworkStatus methods ***/
#define IAMNetworkStatus_get_ReceivedPackets(This,pReceivedPackets) (This)->lpVtbl->get_ReceivedPackets(This,pReceivedPackets)
#define IAMNetworkStatus_get_RecoveredPackets(This,pRecoveredPackets) (This)->lpVtbl->get_RecoveredPackets(This,pRecoveredPackets)
#define IAMNetworkStatus_get_LostPackets(This,pLostPackets) (This)->lpVtbl->get_LostPackets(This,pLostPackets)
#define IAMNetworkStatus_get_ReceptionQuality(This,pReceptionQuality) (This)->lpVtbl->get_ReceptionQuality(This,pReceptionQuality)
#define IAMNetworkStatus_get_BufferingCount(This,pBufferingCount) (This)->lpVtbl->get_BufferingCount(This,pBufferingCount)
#define IAMNetworkStatus_get_IsBroadcast(This,pIsBroadcast) (This)->lpVtbl->get_IsBroadcast(This,pIsBroadcast)
#define IAMNetworkStatus_get_BufferingProgress(This,pBufferingProgress) (This)->lpVtbl->get_BufferingProgress(This,pBufferingProgress)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAMNetworkStatus_QueryInterface(IAMNetworkStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAMNetworkStatus_AddRef(IAMNetworkStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAMNetworkStatus_Release(IAMNetworkStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IAMNetworkStatus_GetTypeInfoCount(IAMNetworkStatus* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IAMNetworkStatus_GetTypeInfo(IAMNetworkStatus* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IAMNetworkStatus_GetIDsOfNames(IAMNetworkStatus* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IAMNetworkStatus_Invoke(IAMNetworkStatus* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IAMNetworkStatus methods ***/
static FORCEINLINE HRESULT IAMNetworkStatus_get_ReceivedPackets(IAMNetworkStatus* This,LONG *pReceivedPackets) {
    return This->lpVtbl->get_ReceivedPackets(This,pReceivedPackets);
}
static FORCEINLINE HRESULT IAMNetworkStatus_get_RecoveredPackets(IAMNetworkStatus* This,LONG *pRecoveredPackets) {
    return This->lpVtbl->get_RecoveredPackets(This,pRecoveredPackets);
}
static FORCEINLINE HRESULT IAMNetworkStatus_get_LostPackets(IAMNetworkStatus* This,LONG *pLostPackets) {
    return This->lpVtbl->get_LostPackets(This,pLostPackets);
}
static FORCEINLINE HRESULT IAMNetworkStatus_get_ReceptionQuality(IAMNetworkStatus* This,LONG *pReceptionQuality) {
    return This->lpVtbl->get_ReceptionQuality(This,pReceptionQuality);
}
static FORCEINLINE HRESULT IAMNetworkStatus_get_BufferingCount(IAMNetworkStatus* This,LONG *pBufferingCount) {
    return This->lpVtbl->get_BufferingCount(This,pBufferingCount);
}
static FORCEINLINE HRESULT IAMNetworkStatus_get_IsBroadcast(IAMNetworkStatus* This,VARIANT_BOOL *pIsBroadcast) {
    return This->lpVtbl->get_IsBroadcast(This,pIsBroadcast);
}
static FORCEINLINE HRESULT IAMNetworkStatus_get_BufferingProgress(IAMNetworkStatus* This,LONG *pBufferingProgress) {
    return This->lpVtbl->get_BufferingProgress(This,pBufferingProgress);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAMNetworkStatus_get_ReceivedPackets_Proxy(
    IAMNetworkStatus* This,
    LONG *pReceivedPackets);
void __RPC_STUB IAMNetworkStatus_get_ReceivedPackets_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMNetworkStatus_get_RecoveredPackets_Proxy(
    IAMNetworkStatus* This,
    LONG *pRecoveredPackets);
void __RPC_STUB IAMNetworkStatus_get_RecoveredPackets_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMNetworkStatus_get_LostPackets_Proxy(
    IAMNetworkStatus* This,
    LONG *pLostPackets);
void __RPC_STUB IAMNetworkStatus_get_LostPackets_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMNetworkStatus_get_ReceptionQuality_Proxy(
    IAMNetworkStatus* This,
    LONG *pReceptionQuality);
void __RPC_STUB IAMNetworkStatus_get_ReceptionQuality_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMNetworkStatus_get_BufferingCount_Proxy(
    IAMNetworkStatus* This,
    LONG *pBufferingCount);
void __RPC_STUB IAMNetworkStatus_get_BufferingCount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMNetworkStatus_get_IsBroadcast_Proxy(
    IAMNetworkStatus* This,
    VARIANT_BOOL *pIsBroadcast);
void __RPC_STUB IAMNetworkStatus_get_IsBroadcast_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAMNetworkStatus_get_BufferingProgress_Proxy(
    IAMNetworkStatus* This,
    LONG *pBufferingProgress);
void __RPC_STUB IAMNetworkStatus_get_BufferingProgress_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IAMNetworkStatus_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __qnetwork_h__ */
