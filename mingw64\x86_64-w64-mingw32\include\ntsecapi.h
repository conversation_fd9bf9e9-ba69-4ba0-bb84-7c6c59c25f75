/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _NTSECAPI_
#define _NTSECAPI_

#ifdef __cplusplus
extern "C" {
#endif

#if !defined (_NTDEF_) && !defined (_NTSTATUS_PSDK)
#define _NTSTATUS_PSDK
  typedef LONG NTSTATUS,*PNTSTATUS;
#endif

#ifndef _NTLSA_IFS_
  typedef ULONG LSA_OPERATIONAL_MODE,*PLSA_OPERATIONAL_MODE;
#endif

#define LSA_MODE_PASSWORD_PROTECTED (__MSABI_LONG(0x00000001))
#define LSA_MODE_INDIVIDUAL_ACCOUNTS (__MSABI_LONG(0x00000002))
#define LSA_MODE_MANDATORY_ACCESS (__MSABI_LONG(0x00000004))
#define LSA_MODE_LOG_FULL (__MSABI_LONG(0x00000008))

#ifndef _NTLSA_IFS_
  typedef enum _SECURITY_LOGON_TYPE {
    Interactive = 2,Network,Batch,Service,Proxy,Unlock,NetworkCleartext,NewCredentials,RemoteInteractive,CachedInteractive,
    CachedRemoteInteractive,CachedUnlock
  } SECURITY_LOGON_TYPE,*PSECURITY_LOGON_TYPE;
#endif

#ifndef _NTLSA_IFS_

#ifndef _NTLSA_AUDIT_
#define _NTLSA_AUDIT_

  typedef enum _SE_ADT_PARAMETER_TYPE {
    SeAdtParmTypeNone = 0,SeAdtParmTypeString,SeAdtParmTypeFileSpec,SeAdtParmTypeUlong,SeAdtParmTypeSid,SeAdtParmTypeLogonId,
    SeAdtParmTypeNoLogonId,SeAdtParmTypeAccessMask,SeAdtParmTypePrivs,SeAdtParmTypeObjectTypes,SeAdtParmTypeHexUlong,SeAdtParmTypePtr,
    SeAdtParmTypeTime,SeAdtParmTypeGuid,SeAdtParmTypeLuid,SeAdtParmTypeHexInt64,SeAdtParmTypeStringList,SeAdtParmTypeSidList,
    SeAdtParmTypeDuration,SeAdtParmTypeUserAccountControl,SeAdtParmTypeNoUac,SeAdtParmTypeMessage,SeAdtParmTypeDateTime,SeAdtParmTypeSockAddr
  } SE_ADT_PARAMETER_TYPE,*PSE_ADT_PARAMETER_TYPE;

#include <guiddef.h>

#define SE_ADT_OBJECT_ONLY 0x1

  typedef struct _SE_ADT_OBJECT_TYPE {
    GUID ObjectType;
    USHORT Flags;
    USHORT Level;
    ACCESS_MASK AccessMask;
  } SE_ADT_OBJECT_TYPE,*PSE_ADT_OBJECT_TYPE;

  typedef struct _SE_ADT_PARAMETER_ARRAY_ENTRY {
    SE_ADT_PARAMETER_TYPE Type;
    ULONG Length;
    ULONG_PTR Data[2];
    PVOID Address;
  } SE_ADT_PARAMETER_ARRAY_ENTRY,*PSE_ADT_PARAMETER_ARRAY_ENTRY;

#define SE_MAX_AUDIT_PARAMETERS 32
#define SE_MAX_GENERIC_AUDIT_PARAMETERS 28

  typedef struct _SE_ADT_PARAMETER_ARRAY {
    ULONG CategoryId;
    ULONG AuditId;
    ULONG ParameterCount;
    ULONG Length;
    USHORT Type;
    ULONG Flags;
    SE_ADT_PARAMETER_ARRAY_ENTRY Parameters[SE_MAX_AUDIT_PARAMETERS ];
  } SE_ADT_PARAMETER_ARRAY,*PSE_ADT_PARAMETER_ARRAY;

#define SE_ADT_PARAMETERS_SELF_RELATIVE 0x00000001
#endif
#endif

  typedef enum _POLICY_AUDIT_EVENT_TYPE {
    AuditCategorySystem = 0,AuditCategoryLogon,AuditCategoryObjectAccess,AuditCategoryPrivilegeUse,AuditCategoryDetailedTracking,
    AuditCategoryPolicyChange,AuditCategoryAccountManagement,AuditCategoryDirectoryServiceAccess,AuditCategoryAccountLogon
  } POLICY_AUDIT_EVENT_TYPE,*PPOLICY_AUDIT_EVENT_TYPE;

#define POLICY_AUDIT_EVENT_UNCHANGED (__MSABI_LONG(0x00000000))
#define POLICY_AUDIT_EVENT_SUCCESS (__MSABI_LONG(0x00000001))
#define POLICY_AUDIT_EVENT_FAILURE (__MSABI_LONG(0x00000002))
#define POLICY_AUDIT_EVENT_NONE (__MSABI_LONG(0x00000004))
#define POLICY_AUDIT_EVENT_MASK (POLICY_AUDIT_EVENT_SUCCESS | POLICY_AUDIT_EVENT_FAILURE | POLICY_AUDIT_EVENT_UNCHANGED | POLICY_AUDIT_EVENT_NONE)

#ifdef _NTDEF_
  typedef UNICODE_STRING LSA_UNICODE_STRING,*PLSA_UNICODE_STRING;
  typedef STRING LSA_STRING,*PLSA_STRING;
  typedef OBJECT_ATTRIBUTES LSA_OBJECT_ATTRIBUTES,*PLSA_OBJECT_ATTRIBUTES;
#else

#ifndef _NO_W32_PSEUDO_MODIFIERS
#ifndef IN
#define IN
#endif
#ifndef OUT
#define OUT
#endif
#ifndef OPTIONAL
#define OPTIONAL
#endif
#endif

  typedef struct _LSA_UNICODE_STRING {
    USHORT Length;
    USHORT MaximumLength;
    PWSTR Buffer;
  } LSA_UNICODE_STRING,*PLSA_UNICODE_STRING;

  typedef struct _LSA_STRING {
    USHORT Length;
    USHORT MaximumLength;
    PCHAR Buffer;
  } LSA_STRING,*PLSA_STRING;

  typedef struct _LSA_OBJECT_ATTRIBUTES {
    ULONG Length;
    HANDLE RootDirectory;
    PLSA_UNICODE_STRING ObjectName;
    ULONG Attributes;
    PVOID SecurityDescriptor;
    PVOID SecurityQualityOfService;
  } LSA_OBJECT_ATTRIBUTES,*PLSA_OBJECT_ATTRIBUTES;
#endif

#define LSA_SUCCESS(Error) ((LONG)(Error) >= 0)

#ifndef _NTLSA_IFS_
  NTSTATUS NTAPI LsaRegisterLogonProcess(PLSA_STRING LogonProcessName,PHANDLE LsaHandle,PLSA_OPERATIONAL_MODE SecurityMode);
  NTSTATUS NTAPI LsaLogonUser(HANDLE LsaHandle,PLSA_STRING OriginName,SECURITY_LOGON_TYPE LogonType,ULONG AuthenticationPackage,PVOID AuthenticationInformation,ULONG AuthenticationInformationLength,PTOKEN_GROUPS LocalGroups,PTOKEN_SOURCE SourceContext,PVOID *ProfileBuffer,PULONG ProfileBufferLength,PLUID LogonId,PHANDLE Token,PQUOTA_LIMITS Quotas,PNTSTATUS SubStatus);
  NTSTATUS NTAPI LsaLookupAuthenticationPackage(HANDLE LsaHandle,PLSA_STRING PackageName,PULONG AuthenticationPackage);
  NTSTATUS NTAPI LsaFreeReturnBuffer (PVOID Buffer);
  NTSTATUS NTAPI LsaCallAuthenticationPackage(HANDLE LsaHandle,ULONG AuthenticationPackage,PVOID ProtocolSubmitBuffer,ULONG SubmitBufferLength,PVOID *ProtocolReturnBuffer,PULONG ReturnBufferLength,PNTSTATUS ProtocolStatus);
  NTSTATUS NTAPI LsaDeregisterLogonProcess(HANDLE LsaHandle);
  NTSTATUS NTAPI LsaConnectUntrusted(PHANDLE LsaHandle);
#endif

#define POLICY_VIEW_LOCAL_INFORMATION __MSABI_LONG(0x00000001)
#define POLICY_VIEW_AUDIT_INFORMATION __MSABI_LONG(0x00000002)
#define POLICY_GET_PRIVATE_INFORMATION __MSABI_LONG(0x00000004)
#define POLICY_TRUST_ADMIN __MSABI_LONG(0x00000008)
#define POLICY_CREATE_ACCOUNT __MSABI_LONG(0x00000010)
#define POLICY_CREATE_SECRET __MSABI_LONG(0x00000020)
#define POLICY_CREATE_PRIVILEGE __MSABI_LONG(0x00000040)
#define POLICY_SET_DEFAULT_QUOTA_LIMITS __MSABI_LONG(0x00000080)
#define POLICY_SET_AUDIT_REQUIREMENTS __MSABI_LONG(0x00000100)
#define POLICY_AUDIT_LOG_ADMIN __MSABI_LONG(0x00000200)
#define POLICY_SERVER_ADMIN __MSABI_LONG(0x00000400)
#define POLICY_LOOKUP_NAMES __MSABI_LONG(0x00000800)
#define POLICY_NOTIFICATION __MSABI_LONG(0x00001000)

#define POLICY_ALL_ACCESS (STANDARD_RIGHTS_REQUIRED | POLICY_VIEW_LOCAL_INFORMATION | POLICY_VIEW_AUDIT_INFORMATION | POLICY_GET_PRIVATE_INFORMATION | POLICY_TRUST_ADMIN | POLICY_CREATE_ACCOUNT | POLICY_CREATE_SECRET | POLICY_CREATE_PRIVILEGE | POLICY_SET_DEFAULT_QUOTA_LIMITS | POLICY_SET_AUDIT_REQUIREMENTS | POLICY_AUDIT_LOG_ADMIN | POLICY_SERVER_ADMIN | POLICY_LOOKUP_NAMES)
#define POLICY_READ (STANDARD_RIGHTS_READ | POLICY_VIEW_AUDIT_INFORMATION | POLICY_GET_PRIVATE_INFORMATION)
#define POLICY_WRITE (STANDARD_RIGHTS_WRITE | POLICY_TRUST_ADMIN | POLICY_CREATE_ACCOUNT | POLICY_CREATE_SECRET | POLICY_CREATE_PRIVILEGE | POLICY_SET_DEFAULT_QUOTA_LIMITS | POLICY_SET_AUDIT_REQUIREMENTS | POLICY_AUDIT_LOG_ADMIN | POLICY_SERVER_ADMIN)
#define POLICY_EXECUTE (STANDARD_RIGHTS_EXECUTE | POLICY_VIEW_LOCAL_INFORMATION | POLICY_LOOKUP_NAMES)

  typedef struct _LSA_TRUST_INFORMATION {
    LSA_UNICODE_STRING Name;
    PSID Sid;
  } LSA_TRUST_INFORMATION,*PLSA_TRUST_INFORMATION;

  typedef struct _LSA_REFERENCED_DOMAIN_LIST {
    ULONG Entries;
    PLSA_TRUST_INFORMATION Domains;
  } LSA_REFERENCED_DOMAIN_LIST,*PLSA_REFERENCED_DOMAIN_LIST;

  typedef struct _LSA_TRANSLATED_SID {
    SID_NAME_USE Use;
    ULONG RelativeId;
    LONG DomainIndex;
  } LSA_TRANSLATED_SID,*PLSA_TRANSLATED_SID;

  typedef struct _LSA_TRANSLATED_SID2 {
    SID_NAME_USE Use;
    PSID Sid;
    LONG DomainIndex;
    ULONG Flags;
  } LSA_TRANSLATED_SID2,*PLSA_TRANSLATED_SID2;

  typedef struct _LSA_TRANSLATED_NAME {
    SID_NAME_USE Use;
    LSA_UNICODE_STRING Name;
    LONG DomainIndex;
  } LSA_TRANSLATED_NAME,*PLSA_TRANSLATED_NAME;

  typedef enum _POLICY_LSA_SERVER_ROLE {
    PolicyServerRoleBackup = 2,PolicyServerRolePrimary
  } POLICY_LSA_SERVER_ROLE,*PPOLICY_LSA_SERVER_ROLE;

  typedef ULONG POLICY_AUDIT_EVENT_OPTIONS,*PPOLICY_AUDIT_EVENT_OPTIONS;

  typedef enum _POLICY_INFORMATION_CLASS {
    PolicyAuditLogInformation = 1,PolicyAuditEventsInformation,PolicyPrimaryDomainInformation,PolicyPdAccountInformation,
    PolicyAccountDomainInformation,PolicyLsaServerRoleInformation,PolicyReplicaSourceInformation,PolicyDefaultQuotaInformation,
    PolicyModificationInformation,PolicyAuditFullSetInformation,PolicyAuditFullQueryInformation,PolicyDnsDomainInformation,
    PolicyDnsDomainInformationInt
  } POLICY_INFORMATION_CLASS,*PPOLICY_INFORMATION_CLASS;

  typedef struct _POLICY_AUDIT_LOG_INFO {
    ULONG AuditLogPercentFull;
    ULONG MaximumLogSize;
    LARGE_INTEGER AuditRetentionPeriod;
    BOOLEAN AuditLogFullShutdownInProgress;
    LARGE_INTEGER TimeToShutdown;
    ULONG NextAuditRecordId;
  } POLICY_AUDIT_LOG_INFO,*PPOLICY_AUDIT_LOG_INFO;

  typedef struct _POLICY_AUDIT_EVENTS_INFO {
    BOOLEAN AuditingMode;
    PPOLICY_AUDIT_EVENT_OPTIONS EventAuditingOptions;
    ULONG MaximumAuditEventCount;
  } POLICY_AUDIT_EVENTS_INFO,*PPOLICY_AUDIT_EVENTS_INFO;

  typedef struct _POLICY_ACCOUNT_DOMAIN_INFO {
    LSA_UNICODE_STRING DomainName;
    PSID DomainSid;
  } POLICY_ACCOUNT_DOMAIN_INFO,*PPOLICY_ACCOUNT_DOMAIN_INFO;

  typedef struct _POLICY_PRIMARY_DOMAIN_INFO {
    LSA_UNICODE_STRING Name;
    PSID Sid;
  } POLICY_PRIMARY_DOMAIN_INFO,*PPOLICY_PRIMARY_DOMAIN_INFO;

  typedef struct _POLICY_DNS_DOMAIN_INFO {
    LSA_UNICODE_STRING Name;
    LSA_UNICODE_STRING DnsDomainName;
    LSA_UNICODE_STRING DnsForestName;
    GUID DomainGuid;
    PSID Sid;
  } POLICY_DNS_DOMAIN_INFO,*PPOLICY_DNS_DOMAIN_INFO;

  typedef struct _POLICY_PD_ACCOUNT_INFO {
    LSA_UNICODE_STRING Name;
  } POLICY_PD_ACCOUNT_INFO,*PPOLICY_PD_ACCOUNT_INFO;

  typedef struct _POLICY_LSA_SERVER_ROLE_INFO {
    POLICY_LSA_SERVER_ROLE LsaServerRole;
  } POLICY_LSA_SERVER_ROLE_INFO,*PPOLICY_LSA_SERVER_ROLE_INFO;

  typedef struct _POLICY_REPLICA_SOURCE_INFO {
    LSA_UNICODE_STRING ReplicaSource;
    LSA_UNICODE_STRING ReplicaAccountName;
  } POLICY_REPLICA_SOURCE_INFO,*PPOLICY_REPLICA_SOURCE_INFO;

  typedef struct _POLICY_DEFAULT_QUOTA_INFO {
    QUOTA_LIMITS QuotaLimits;
  } POLICY_DEFAULT_QUOTA_INFO,*PPOLICY_DEFAULT_QUOTA_INFO;

  typedef struct _POLICY_MODIFICATION_INFO {
    LARGE_INTEGER ModifiedId;
    LARGE_INTEGER DatabaseCreationTime;
  } POLICY_MODIFICATION_INFO,*PPOLICY_MODIFICATION_INFO;

  typedef struct _POLICY_AUDIT_FULL_SET_INFO {
    BOOLEAN ShutDownOnFull;
  } POLICY_AUDIT_FULL_SET_INFO,*PPOLICY_AUDIT_FULL_SET_INFO;

  typedef struct _POLICY_AUDIT_FULL_QUERY_INFO {
    BOOLEAN ShutDownOnFull;
    BOOLEAN LogIsFull;
  } POLICY_AUDIT_FULL_QUERY_INFO,*PPOLICY_AUDIT_FULL_QUERY_INFO;

  typedef enum _POLICY_DOMAIN_INFORMATION_CLASS {
    PolicyDomainEfsInformation = 2,PolicyDomainKerberosTicketInformation
  } POLICY_DOMAIN_INFORMATION_CLASS,*PPOLICY_DOMAIN_INFORMATION_CLASS;

  typedef struct _POLICY_DOMAIN_EFS_INFO {
    ULONG InfoLength;
    PUCHAR EfsBlob;
  } POLICY_DOMAIN_EFS_INFO,*PPOLICY_DOMAIN_EFS_INFO;

#define POLICY_KERBEROS_VALIDATE_CLIENT 0x00000080

  typedef struct _POLICY_DOMAIN_KERBEROS_TICKET_INFO {
    ULONG AuthenticationOptions;
    LARGE_INTEGER MaxServiceTicketAge;
    LARGE_INTEGER MaxTicketAge;
    LARGE_INTEGER MaxRenewAge;
    LARGE_INTEGER MaxClockSkew;
    LARGE_INTEGER Reserved;
  } POLICY_DOMAIN_KERBEROS_TICKET_INFO,*PPOLICY_DOMAIN_KERBEROS_TICKET_INFO;

  typedef enum _POLICY_NOTIFICATION_INFORMATION_CLASS {
    PolicyNotifyAuditEventsInformation = 1,PolicyNotifyAccountDomainInformation,PolicyNotifyServerRoleInformation,PolicyNotifyDnsDomainInformation,
    PolicyNotifyDomainEfsInformation,PolicyNotifyDomainKerberosTicketInformation,PolicyNotifyMachineAccountPasswordInformation
  } POLICY_NOTIFICATION_INFORMATION_CLASS,*PPOLICY_NOTIFICATION_INFORMATION_CLASS;

  typedef PVOID LSA_HANDLE,*PLSA_HANDLE;

  typedef enum _TRUSTED_INFORMATION_CLASS {
    TrustedDomainNameInformation = 1,TrustedControllersInformation,TrustedPosixOffsetInformation,TrustedPasswordInformation,
    TrustedDomainInformationBasic,TrustedDomainInformationEx,TrustedDomainAuthInformation,TrustedDomainFullInformation,
    TrustedDomainAuthInformationInternal,TrustedDomainFullInformationInternal,TrustedDomainInformationEx2Internal,TrustedDomainFullInformation2Internal
  } TRUSTED_INFORMATION_CLASS,*PTRUSTED_INFORMATION_CLASS;

  typedef struct _TRUSTED_DOMAIN_NAME_INFO {
    LSA_UNICODE_STRING Name;
  } TRUSTED_DOMAIN_NAME_INFO,*PTRUSTED_DOMAIN_NAME_INFO;

  typedef struct _TRUSTED_CONTROLLERS_INFO {
    ULONG Entries;
    PLSA_UNICODE_STRING Names;
  } TRUSTED_CONTROLLERS_INFO,*PTRUSTED_CONTROLLERS_INFO;

  typedef struct _TRUSTED_POSIX_OFFSET_INFO {
    ULONG Offset;
  } TRUSTED_POSIX_OFFSET_INFO,*PTRUSTED_POSIX_OFFSET_INFO;

  typedef struct _TRUSTED_PASSWORD_INFO {
    LSA_UNICODE_STRING Password;
    LSA_UNICODE_STRING OldPassword;
  } TRUSTED_PASSWORD_INFO,*PTRUSTED_PASSWORD_INFO;

  typedef LSA_TRUST_INFORMATION TRUSTED_DOMAIN_INFORMATION_BASIC;
  typedef PLSA_TRUST_INFORMATION PTRUSTED_DOMAIN_INFORMATION_BASIC;

#define TRUST_DIRECTION_DISABLED 0x00000000
#define TRUST_DIRECTION_INBOUND 0x00000001
#define TRUST_DIRECTION_OUTBOUND 0x00000002
#define TRUST_DIRECTION_BIDIRECTIONAL (TRUST_DIRECTION_INBOUND | TRUST_DIRECTION_OUTBOUND)

#define TRUST_TYPE_DOWNLEVEL 0x00000001
#define TRUST_TYPE_UPLEVEL 0x00000002
#define TRUST_TYPE_MIT 0x00000003

#define TRUST_ATTRIBUTE_NON_TRANSITIVE 0x00000001
#define TRUST_ATTRIBUTE_UPLEVEL_ONLY 0x00000002
#define TRUST_ATTRIBUTE_QUARANTINED_DOMAIN 0x00000004
#define TRUST_ATTRIBUTE_FOREST_TRANSITIVE 0x00000008
#define TRUST_ATTRIBUTE_CROSS_ORGANIZATION 0x00000010
#define TRUST_ATTRIBUTE_WITHIN_FOREST 0x00000020
#define TRUST_ATTRIBUTE_TREAT_AS_EXTERNAL 0x00000040
#define TRUST_ATTRIBUTE_TRUST_USES_RC4_ENCRYPTION 0x00000080

#define TRUST_ATTRIBUTES_VALID 0xFF03FFFF
#define TRUST_ATTRIBUTES_USER 0xFF000000

  typedef struct _TRUSTED_DOMAIN_INFORMATION_EX {
    LSA_UNICODE_STRING Name;
    LSA_UNICODE_STRING FlatName;
    PSID Sid;
    ULONG TrustDirection;
    ULONG TrustType;
    ULONG TrustAttributes;
  } TRUSTED_DOMAIN_INFORMATION_EX,*PTRUSTED_DOMAIN_INFORMATION_EX;

  typedef struct _TRUSTED_DOMAIN_INFORMATION_EX2 {
    LSA_UNICODE_STRING Name;
    LSA_UNICODE_STRING FlatName;
    PSID Sid;
    ULONG TrustDirection;
    ULONG TrustType;
    ULONG TrustAttributes;
    ULONG ForestTrustLength;
    PUCHAR ForestTrustInfo;
  } TRUSTED_DOMAIN_INFORMATION_EX2,*PTRUSTED_DOMAIN_INFORMATION_EX2;

#define TRUST_AUTH_TYPE_NONE 0
#define TRUST_AUTH_TYPE_NT4OWF 1
#define TRUST_AUTH_TYPE_CLEAR 2
#define TRUST_AUTH_TYPE_VERSION 3

  typedef struct _LSA_AUTH_INFORMATION {
    LARGE_INTEGER LastUpdateTime;
    ULONG AuthType;
    ULONG AuthInfoLength;
    PUCHAR AuthInfo;
  } LSA_AUTH_INFORMATION,*PLSA_AUTH_INFORMATION;

  typedef struct _TRUSTED_DOMAIN_AUTH_INFORMATION {
    ULONG IncomingAuthInfos;
    PLSA_AUTH_INFORMATION IncomingAuthenticationInformation;
    PLSA_AUTH_INFORMATION IncomingPreviousAuthenticationInformation;
    ULONG OutgoingAuthInfos;
    PLSA_AUTH_INFORMATION OutgoingAuthenticationInformation;
    PLSA_AUTH_INFORMATION OutgoingPreviousAuthenticationInformation;
  } TRUSTED_DOMAIN_AUTH_INFORMATION,*PTRUSTED_DOMAIN_AUTH_INFORMATION;

  typedef struct _TRUSTED_DOMAIN_FULL_INFORMATION {
    TRUSTED_DOMAIN_INFORMATION_EX Information;
    TRUSTED_POSIX_OFFSET_INFO PosixOffset;
    TRUSTED_DOMAIN_AUTH_INFORMATION AuthInformation;
  } TRUSTED_DOMAIN_FULL_INFORMATION,*PTRUSTED_DOMAIN_FULL_INFORMATION;

  typedef struct _TRUSTED_DOMAIN_FULL_INFORMATION2 {
    TRUSTED_DOMAIN_INFORMATION_EX2 Information;
    TRUSTED_POSIX_OFFSET_INFO PosixOffset;
    TRUSTED_DOMAIN_AUTH_INFORMATION AuthInformation;
  } TRUSTED_DOMAIN_FULL_INFORMATION2,*PTRUSTED_DOMAIN_FULL_INFORMATION2;

  typedef enum {
    ForestTrustTopLevelName,ForestTrustTopLevelNameEx,ForestTrustDomainInfo,ForestTrustRecordTypeLast = ForestTrustDomainInfo
  } LSA_FOREST_TRUST_RECORD_TYPE;

#define LSA_FTRECORD_DISABLED_REASONS (__MSABI_LONG(0x0000FFFF))

#define LSA_TLN_DISABLED_NEW (__MSABI_LONG(0x00000001))
#define LSA_TLN_DISABLED_ADMIN (__MSABI_LONG(0x00000002))
#define LSA_TLN_DISABLED_CONFLICT (__MSABI_LONG(0x00000004))

#define LSA_SID_DISABLED_ADMIN (__MSABI_LONG(0x00000001))
#define LSA_SID_DISABLED_CONFLICT (__MSABI_LONG(0x00000002))
#define LSA_NB_DISABLED_ADMIN (__MSABI_LONG(0x00000004))
#define LSA_NB_DISABLED_CONFLICT (__MSABI_LONG(0x00000008))

  typedef struct _LSA_FOREST_TRUST_DOMAIN_INFO {
    PSID Sid;
    LSA_UNICODE_STRING DnsName;
    LSA_UNICODE_STRING NetbiosName;
  } LSA_FOREST_TRUST_DOMAIN_INFO,*PLSA_FOREST_TRUST_DOMAIN_INFO;

#define MAX_FOREST_TRUST_BINARY_DATA_SIZE (128*1024)

  typedef struct _LSA_FOREST_TRUST_BINARY_DATA {
    ULONG Length;
    PUCHAR Buffer;
  } LSA_FOREST_TRUST_BINARY_DATA,*PLSA_FOREST_TRUST_BINARY_DATA;

  typedef struct _LSA_FOREST_TRUST_RECORD {
    ULONG Flags;
    LSA_FOREST_TRUST_RECORD_TYPE ForestTrustType;
    LARGE_INTEGER Time;
    union {
      LSA_UNICODE_STRING TopLevelName;
      LSA_FOREST_TRUST_DOMAIN_INFO DomainInfo;
      LSA_FOREST_TRUST_BINARY_DATA Data;
    } ForestTrustData;
  } LSA_FOREST_TRUST_RECORD,*PLSA_FOREST_TRUST_RECORD;

#define MAX_RECORDS_IN_FOREST_TRUST_INFO 4000

  typedef struct _LSA_FOREST_TRUST_INFORMATION {
    ULONG RecordCount;
    PLSA_FOREST_TRUST_RECORD *Entries;
  } LSA_FOREST_TRUST_INFORMATION,*PLSA_FOREST_TRUST_INFORMATION;

  typedef enum {
    CollisionTdo,CollisionXref,CollisionOther
  } LSA_FOREST_TRUST_COLLISION_RECORD_TYPE;

  typedef struct _LSA_FOREST_TRUST_COLLISION_RECORD {
    ULONG Index;
    LSA_FOREST_TRUST_COLLISION_RECORD_TYPE Type;
    ULONG Flags;
    LSA_UNICODE_STRING Name;
  } LSA_FOREST_TRUST_COLLISION_RECORD,*PLSA_FOREST_TRUST_COLLISION_RECORD;

  typedef struct _LSA_FOREST_TRUST_COLLISION_INFORMATION {
    ULONG RecordCount;
    PLSA_FOREST_TRUST_COLLISION_RECORD *Entries;
  } LSA_FOREST_TRUST_COLLISION_INFORMATION,*PLSA_FOREST_TRUST_COLLISION_INFORMATION;

  typedef ULONG LSA_ENUMERATION_HANDLE,*PLSA_ENUMERATION_HANDLE;

  typedef struct _LSA_ENUMERATION_INFORMATION {
    PSID Sid;
  } LSA_ENUMERATION_INFORMATION,*PLSA_ENUMERATION_INFORMATION;

  NTSTATUS NTAPI LsaFreeMemory(PVOID Buffer);
  NTSTATUS NTAPI LsaClose(LSA_HANDLE ObjectHandle);

  #if (_WIN32_WINNT >= 0x0600)
  typedef struct _LSA_LAST_INTER_LOGON_INFO {
    LARGE_INTEGER LastSuccessfulLogon;
    LARGE_INTEGER LastFailedLogon;
    ULONG FailedAttemptCountSinceLastSuccessfulLogon;
  } LSA_LAST_INTER_LOGON_INFO,*PLSA_LAST_INTER_LOGON_INFO;
  #endif
  
  typedef struct _SECURITY_LOGON_SESSION_DATA {
    ULONG Size;
    LUID LogonId;
    LSA_UNICODE_STRING UserName;
    LSA_UNICODE_STRING LogonDomain;
    LSA_UNICODE_STRING AuthenticationPackage;
    ULONG LogonType;
    ULONG Session;
    PSID Sid;
    LARGE_INTEGER LogonTime;
    LSA_UNICODE_STRING LogonServer;
    LSA_UNICODE_STRING DnsDomainName;
    LSA_UNICODE_STRING Upn;
    #if (_WIN32_WINNT >= 0x0600)
    ULONG UserFlags;
    LSA_LAST_INTER_LOGON_INFO LastLogonInfo;
    LSA_UNICODE_STRING LogonScript;
    LSA_UNICODE_STRING ProfilePath;
    LSA_UNICODE_STRING HomeDirectory;
    LSA_UNICODE_STRING HomeDirectoryDrive;
    LARGE_INTEGER LogoffTime;
    LARGE_INTEGER KickOffTime;
    LARGE_INTEGER PasswordLastSet;
    LARGE_INTEGER PasswordCanChange;
    LARGE_INTEGER PasswordMustChange;
    #endif
  } SECURITY_LOGON_SESSION_DATA,*PSECURITY_LOGON_SESSION_DATA;

  NTSTATUS NTAPI LsaEnumerateLogonSessions(PULONG LogonSessionCount,PLUID *LogonSessionList);
  NTSTATUS NTAPI LsaGetLogonSessionData(PLUID LogonId,PSECURITY_LOGON_SESSION_DATA *ppLogonSessionData);
  NTSTATUS NTAPI LsaOpenPolicy(PLSA_UNICODE_STRING SystemName,PLSA_OBJECT_ATTRIBUTES ObjectAttributes,ACCESS_MASK DesiredAccess,PLSA_HANDLE PolicyHandle);
  NTSTATUS NTAPI LsaQueryInformationPolicy(LSA_HANDLE PolicyHandle,POLICY_INFORMATION_CLASS InformationClass,PVOID *Buffer);
  NTSTATUS NTAPI LsaSetInformationPolicy(LSA_HANDLE PolicyHandle,POLICY_INFORMATION_CLASS InformationClass,PVOID Buffer);
  NTSTATUS NTAPI LsaQueryDomainInformationPolicy(LSA_HANDLE PolicyHandle,POLICY_DOMAIN_INFORMATION_CLASS InformationClass,PVOID *Buffer);
  NTSTATUS NTAPI LsaSetDomainInformationPolicy(LSA_HANDLE PolicyHandle,POLICY_DOMAIN_INFORMATION_CLASS InformationClass,PVOID Buffer);
  NTSTATUS NTAPI LsaRegisterPolicyChangeNotification(POLICY_NOTIFICATION_INFORMATION_CLASS InformationClass,HANDLE NotificationEventHandle);
  NTSTATUS NTAPI LsaUnregisterPolicyChangeNotification(POLICY_NOTIFICATION_INFORMATION_CLASS InformationClass,HANDLE NotificationEventHandle);
  NTSTATUS NTAPI LsaEnumerateTrustedDomains(LSA_HANDLE PolicyHandle,PLSA_ENUMERATION_HANDLE EnumerationContext,PVOID *Buffer,ULONG PreferedMaximumLength,PULONG CountReturned);
  NTSTATUS NTAPI LsaLookupNames(LSA_HANDLE PolicyHandle,ULONG Count,PLSA_UNICODE_STRING Names,PLSA_REFERENCED_DOMAIN_LIST *ReferencedDomains,PLSA_TRANSLATED_SID *Sids);
  NTSTATUS NTAPI LsaLookupNames2(LSA_HANDLE PolicyHandle,ULONG Flags,ULONG Count,PLSA_UNICODE_STRING Names,PLSA_REFERENCED_DOMAIN_LIST *ReferencedDomains,PLSA_TRANSLATED_SID2 *Sids);
  NTSTATUS NTAPI LsaLookupSids(LSA_HANDLE PolicyHandle,ULONG Count,PSID *Sids,PLSA_REFERENCED_DOMAIN_LIST *ReferencedDomains,PLSA_TRANSLATED_NAME *Names);

#define SE_INTERACTIVE_LOGON_NAME TEXT("SeInteractiveLogonRight")
#define SE_NETWORK_LOGON_NAME TEXT("SeNetworkLogonRight")
#define SE_BATCH_LOGON_NAME TEXT("SeBatchLogonRight")
#define SE_SERVICE_LOGON_NAME TEXT("SeServiceLogonRight")
#define SE_DENY_INTERACTIVE_LOGON_NAME TEXT("SeDenyInteractiveLogonRight")
#define SE_DENY_NETWORK_LOGON_NAME TEXT("SeDenyNetworkLogonRight")
#define SE_DENY_BATCH_LOGON_NAME TEXT("SeDenyBatchLogonRight")
#define SE_DENY_SERVICE_LOGON_NAME TEXT("SeDenyServiceLogonRight")
#define SE_REMOTE_INTERACTIVE_LOGON_NAME TEXT("SeRemoteInteractiveLogonRight")
#define SE_DENY_REMOTE_INTERACTIVE_LOGON_NAME TEXT("SeDenyRemoteInteractiveLogonRight")

  NTSTATUS NTAPI LsaEnumerateAccountsWithUserRight(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING UserRight,PVOID *Buffer,PULONG CountReturned);
  NTSTATUS NTAPI LsaEnumerateAccountRights(LSA_HANDLE PolicyHandle,PSID AccountSid,PLSA_UNICODE_STRING *UserRights,PULONG CountOfRights);
  NTSTATUS NTAPI LsaAddAccountRights(LSA_HANDLE PolicyHandle,PSID AccountSid,PLSA_UNICODE_STRING UserRights,ULONG CountOfRights);
  NTSTATUS NTAPI LsaRemoveAccountRights(LSA_HANDLE PolicyHandle,PSID AccountSid,BOOLEAN AllRights,PLSA_UNICODE_STRING UserRights,ULONG CountOfRights);
  NTSTATUS NTAPI LsaOpenTrustedDomainByName(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,ACCESS_MASK DesiredAccess,PLSA_HANDLE TrustedDomainHandle);
  NTSTATUS NTAPI LsaQueryTrustedDomainInfo(LSA_HANDLE PolicyHandle,PSID TrustedDomainSid,TRUSTED_INFORMATION_CLASS InformationClass,PVOID *Buffer);
  NTSTATUS NTAPI LsaSetTrustedDomainInformation(LSA_HANDLE PolicyHandle,PSID TrustedDomainSid,TRUSTED_INFORMATION_CLASS InformationClass,PVOID Buffer);
  NTSTATUS NTAPI LsaDeleteTrustedDomain(LSA_HANDLE PolicyHandle,PSID TrustedDomainSid);
  NTSTATUS NTAPI LsaQueryTrustedDomainInfoByName(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,TRUSTED_INFORMATION_CLASS InformationClass,PVOID *Buffer);
  NTSTATUS NTAPI LsaSetTrustedDomainInfoByName(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,TRUSTED_INFORMATION_CLASS InformationClass,PVOID Buffer);
  NTSTATUS NTAPI LsaEnumerateTrustedDomainsEx(LSA_HANDLE PolicyHandle,PLSA_ENUMERATION_HANDLE EnumerationContext,PVOID *Buffer,ULONG PreferedMaximumLength,PULONG CountReturned);
  NTSTATUS NTAPI LsaCreateTrustedDomainEx(LSA_HANDLE PolicyHandle,PTRUSTED_DOMAIN_INFORMATION_EX TrustedDomainInformation,PTRUSTED_DOMAIN_AUTH_INFORMATION AuthenticationInformation,ACCESS_MASK DesiredAccess,PLSA_HANDLE TrustedDomainHandle);
  NTSTATUS NTAPI LsaQueryForestTrustInformation(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,PLSA_FOREST_TRUST_INFORMATION *ForestTrustInfo);
  NTSTATUS NTAPI LsaSetForestTrustInformation(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,PLSA_FOREST_TRUST_INFORMATION ForestTrustInfo,BOOLEAN CheckOnly,PLSA_FOREST_TRUST_COLLISION_INFORMATION *CollisionInfo);

#ifdef TESTING_MATCHING_ROUTINE
  NTSTATUS NTAPI LsaForestTrustFindMatch(LSA_HANDLE PolicyHandle,ULONG Type,PLSA_UNICODE_STRING Name,PLSA_UNICODE_STRING *Match);
#endif

  NTSTATUS NTAPI LsaStorePrivateData(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING KeyName,PLSA_UNICODE_STRING PrivateData);
  NTSTATUS NTAPI LsaRetrievePrivateData(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING KeyName,PLSA_UNICODE_STRING *PrivateData);
  ULONG NTAPI LsaNtStatusToWinError(NTSTATUS Status);

#ifndef _NTLSA_IFS_
#define _NTLSA_IFS_
#endif

  enum NEGOTIATE_MESSAGES {
    NegEnumPackagePrefixes = 0,NegGetCallerName = 1,NegCallPackageMax
  };

#define NEGOTIATE_MAX_PREFIX 32

  typedef struct _NEGOTIATE_PACKAGE_PREFIX {
    ULONG_PTR PackageId;
    PVOID PackageDataA;
    PVOID PackageDataW;
    ULONG_PTR PrefixLen;
    UCHAR Prefix[NEGOTIATE_MAX_PREFIX ];
  } NEGOTIATE_PACKAGE_PREFIX,*PNEGOTIATE_PACKAGE_PREFIX;

  typedef struct _NEGOTIATE_PACKAGE_PREFIXES {
    ULONG MessageType;
    ULONG PrefixCount;
    ULONG Offset;
    ULONG Pad;
  } NEGOTIATE_PACKAGE_PREFIXES,*PNEGOTIATE_PACKAGE_PREFIXES;

  typedef struct _NEGOTIATE_CALLER_NAME_REQUEST {
    ULONG MessageType;
    LUID LogonId;
  } NEGOTIATE_CALLER_NAME_REQUEST,*PNEGOTIATE_CALLER_NAME_REQUEST;

  typedef struct _NEGOTIATE_CALLER_NAME_RESPONSE {
    ULONG MessageType;
    PWSTR CallerName;
  } NEGOTIATE_CALLER_NAME_RESPONSE,*PNEGOTIATE_CALLER_NAME_RESPONSE;

#ifndef _NTDEF_
#ifndef __UNICODE_STRING_DEFINED
#define __UNICODE_STRING_DEFINED
  typedef LSA_UNICODE_STRING UNICODE_STRING,*PUNICODE_STRING;
#endif
#ifndef __STRING_DEFINED
#define __STRING_DEFINED
  typedef LSA_STRING STRING,*PSTRING;
#endif
#endif

#ifndef _DOMAIN_PASSWORD_INFORMATION_DEFINED
#define _DOMAIN_PASSWORD_INFORMATION_DEFINED
  typedef struct _DOMAIN_PASSWORD_INFORMATION {
    USHORT MinPasswordLength;
    USHORT PasswordHistoryLength;
    ULONG PasswordProperties;
    LARGE_INTEGER MaxPasswordAge;
    LARGE_INTEGER MinPasswordAge;
  } DOMAIN_PASSWORD_INFORMATION,*PDOMAIN_PASSWORD_INFORMATION;
#endif

#define DOMAIN_PASSWORD_COMPLEX __MSABI_LONG(0x00000001)
#define DOMAIN_PASSWORD_NO_ANON_CHANGE __MSABI_LONG(0x00000002)
#define DOMAIN_PASSWORD_NO_CLEAR_CHANGE __MSABI_LONG(0x00000004)
#define DOMAIN_LOCKOUT_ADMINS __MSABI_LONG(0x00000008)
#define DOMAIN_PASSWORD_STORE_CLEARTEXT __MSABI_LONG(0x00000010)
#define DOMAIN_REFUSE_PASSWORD_CHANGE __MSABI_LONG(0x00000020)

#ifndef _PASSWORD_NOTIFICATION_DEFINED
#define _PASSWORD_NOTIFICATION_DEFINED
  typedef NTSTATUS (*PSAM_PASSWORD_NOTIFICATION_ROUTINE)(PUNICODE_STRING UserName,ULONG RelativeId,PUNICODE_STRING NewPassword);

#define SAM_PASSWORD_CHANGE_NOTIFY_ROUTINE "PasswordChangeNotify"

  typedef BOOLEAN (*PSAM_INIT_NOTIFICATION_ROUTINE)();

#define SAM_INIT_NOTIFICATION_ROUTINE "InitializeChangeNotify"
#define SAM_PASSWORD_FILTER_ROUTINE "PasswordFilter"

  typedef BOOLEAN (*PSAM_PASSWORD_FILTER_ROUTINE)(PUNICODE_STRING AccountName,PUNICODE_STRING FullName,PUNICODE_STRING Password,BOOLEAN SetOperation);
#endif

#define MSV1_0_PACKAGE_NAME "MICROSOFT_AUTHENTICATION_PACKAGE_V1_0"
#define MSV1_0_PACKAGE_NAMEW L"MICROSOFT_AUTHENTICATION_PACKAGE_V1_0"
#define MSV1_0_PACKAGE_NAMEW_LENGTH sizeof(MSV1_0_PACKAGE_NAMEW) - sizeof(WCHAR)

#define MSV1_0_SUBAUTHENTICATION_KEY "SYSTEM\\CurrentControlSet\\Control\\Lsa\\MSV1_0"
#define MSV1_0_SUBAUTHENTICATION_VALUE "Auth"

  typedef enum _MSV1_0_LOGON_SUBMIT_TYPE {
    MsV1_0InteractiveLogon = 2,MsV1_0Lm20Logon,MsV1_0NetworkLogon,MsV1_0SubAuthLogon,MsV1_0WorkstationUnlockLogon = 7
  } MSV1_0_LOGON_SUBMIT_TYPE,*PMSV1_0_LOGON_SUBMIT_TYPE;

  typedef enum _MSV1_0_PROFILE_BUFFER_TYPE {
    MsV1_0InteractiveProfile = 2,MsV1_0Lm20LogonProfile,MsV1_0SmartCardProfile
  } MSV1_0_PROFILE_BUFFER_TYPE,*PMSV1_0_PROFILE_BUFFER_TYPE;

  typedef struct _MSV1_0_INTERACTIVE_LOGON {
    MSV1_0_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING LogonDomainName;
    UNICODE_STRING UserName;
    UNICODE_STRING Password;
  } MSV1_0_INTERACTIVE_LOGON,*PMSV1_0_INTERACTIVE_LOGON;

  typedef struct _MSV1_0_INTERACTIVE_PROFILE {
    MSV1_0_PROFILE_BUFFER_TYPE MessageType;
    USHORT LogonCount;
    USHORT BadPasswordCount;
    LARGE_INTEGER LogonTime;
    LARGE_INTEGER LogoffTime;
    LARGE_INTEGER KickOffTime;
    LARGE_INTEGER PasswordLastSet;
    LARGE_INTEGER PasswordCanChange;
    LARGE_INTEGER PasswordMustChange;
    UNICODE_STRING LogonScript;
    UNICODE_STRING HomeDirectory;
    UNICODE_STRING FullName;
    UNICODE_STRING ProfilePath;
    UNICODE_STRING HomeDirectoryDrive;
    UNICODE_STRING LogonServer;
    ULONG UserFlags;
  } MSV1_0_INTERACTIVE_PROFILE,*PMSV1_0_INTERACTIVE_PROFILE;

#define MSV1_0_CHALLENGE_LENGTH 8
#define MSV1_0_USER_SESSION_KEY_LENGTH 16
#define MSV1_0_LANMAN_SESSION_KEY_LENGTH 8

#define MSV1_0_CLEARTEXT_PASSWORD_ALLOWED 0x02
#define MSV1_0_UPDATE_LOGON_STATISTICS 0x04
#define MSV1_0_RETURN_USER_PARAMETERS 0x08
#define MSV1_0_DONT_TRY_GUEST_ACCOUNT 0x10
#define MSV1_0_ALLOW_SERVER_TRUST_ACCOUNT 0x20
#define MSV1_0_RETURN_PASSWORD_EXPIRY 0x40

#define MSV1_0_USE_CLIENT_CHALLENGE 0x80
#define MSV1_0_TRY_GUEST_ACCOUNT_ONLY 0x100
#define MSV1_0_RETURN_PROFILE_PATH 0x200
#define MSV1_0_TRY_SPECIFIED_DOMAIN_ONLY 0x400
#define MSV1_0_ALLOW_WORKSTATION_TRUST_ACCOUNT 0x800
#define MSV1_0_DISABLE_PERSONAL_FALLBACK 0x00001000
#define MSV1_0_ALLOW_FORCE_GUEST 0x00002000
#define MSV1_0_CLEARTEXT_PASSWORD_SUPPLIED 0x00004000
#define MSV1_0_USE_DOMAIN_FOR_ROUTING_ONLY 0x00008000
#define MSV1_0_SUBAUTHENTICATION_DLL_EX 0x00100000
#define MSV1_0_ALLOW_MSVCHAPV2 0x00010000

#define MSV1_0_SUBAUTHENTICATION_DLL 0xFF000000
#define MSV1_0_SUBAUTHENTICATION_DLL_SHIFT 24
#define MSV1_0_MNS_LOGON 0x01000000

#define MSV1_0_SUBAUTHENTICATION_DLL_RAS 2
#define MSV1_0_SUBAUTHENTICATION_DLL_IIS 132

  typedef struct _MSV1_0_LM20_LOGON {
    MSV1_0_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING LogonDomainName;
    UNICODE_STRING UserName;
    UNICODE_STRING Workstation;
    UCHAR ChallengeToClient[MSV1_0_CHALLENGE_LENGTH];
    STRING CaseSensitiveChallengeResponse;
    STRING CaseInsensitiveChallengeResponse;
    ULONG ParameterControl;
  } MSV1_0_LM20_LOGON,*PMSV1_0_LM20_LOGON;

  typedef struct _MSV1_0_SUBAUTH_LOGON{
    MSV1_0_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING LogonDomainName;
    UNICODE_STRING UserName;
    UNICODE_STRING Workstation;
    UCHAR ChallengeToClient[MSV1_0_CHALLENGE_LENGTH];
    STRING AuthenticationInfo1;
    STRING AuthenticationInfo2;
    ULONG ParameterControl;
    ULONG SubAuthPackageId;
  } MSV1_0_SUBAUTH_LOGON,*PMSV1_0_SUBAUTH_LOGON;

#define LOGON_GUEST 0x01
#define LOGON_NOENCRYPTION 0x02
#define LOGON_CACHED_ACCOUNT 0x04
#define LOGON_USED_LM_PASSWORD 0x08
#define LOGON_EXTRA_SIDS 0x20
#define LOGON_SUBAUTH_SESSION_KEY 0x40
#define LOGON_SERVER_TRUST_ACCOUNT 0x80
#define LOGON_NTLMV2_ENABLED 0x100
#define LOGON_RESOURCE_GROUPS 0x200
#define LOGON_PROFILE_PATH_RETURNED 0x400

#define MSV1_0_SUBAUTHENTICATION_FLAGS 0xFF000000

#define LOGON_GRACE_LOGON 0x01000000

  typedef struct _MSV1_0_LM20_LOGON_PROFILE {
    MSV1_0_PROFILE_BUFFER_TYPE MessageType;
    LARGE_INTEGER KickOffTime;
    LARGE_INTEGER LogoffTime;
    ULONG UserFlags;
    UCHAR UserSessionKey[MSV1_0_USER_SESSION_KEY_LENGTH];
    UNICODE_STRING LogonDomainName;
    UCHAR LanmanSessionKey[MSV1_0_LANMAN_SESSION_KEY_LENGTH];
    UNICODE_STRING LogonServer;
    UNICODE_STRING UserParameters;
  } MSV1_0_LM20_LOGON_PROFILE,*PMSV1_0_LM20_LOGON_PROFILE;

#define MSV1_0_OWF_PASSWORD_LENGTH 16
#define MSV1_0_CRED_LM_PRESENT 0x1
#define MSV1_0_CRED_NT_PRESENT 0x2
#define MSV1_0_CRED_VERSION 0

  typedef struct _MSV1_0_SUPPLEMENTAL_CREDENTIAL {
    ULONG Version;
    ULONG Flags;
    UCHAR LmPassword[MSV1_0_OWF_PASSWORD_LENGTH];
    UCHAR NtPassword[MSV1_0_OWF_PASSWORD_LENGTH];
  } MSV1_0_SUPPLEMENTAL_CREDENTIAL,*PMSV1_0_SUPPLEMENTAL_CREDENTIAL;

#define MSV1_0_NTLM3_RESPONSE_LENGTH 16
#define MSV1_0_NTLM3_OWF_LENGTH 16

#define MSV1_0_MAX_NTLM3_LIFE 129600
#define MSV1_0_MAX_AVL_SIZE 64000

#define MSV1_0_AV_FLAG_FORCE_GUEST 0x00000001

  typedef struct _MSV1_0_NTLM3_RESPONSE {
    UCHAR Response[MSV1_0_NTLM3_RESPONSE_LENGTH];
    UCHAR RespType;
    UCHAR HiRespType;
    USHORT Flags;
    ULONG MsgWord;
    ULONGLONG TimeStamp;
    UCHAR ChallengeFromClient[MSV1_0_CHALLENGE_LENGTH];
    ULONG AvPairsOff;
    UCHAR Buffer[1];
  } MSV1_0_NTLM3_RESPONSE,*PMSV1_0_NTLM3_RESPONSE;

#define MSV1_0_NTLM3_INPUT_LENGTH (sizeof(MSV1_0_NTLM3_RESPONSE) - MSV1_0_NTLM3_RESPONSE_LENGTH)
#define MSV1_0_NTLM3_MIN_NT_RESPONSE_LENGTH RTL_SIZEOF_THROUGH_FIELD(MSV1_0_NTLM3_RESPONSE,AvPairsOff)

  typedef enum {
    MsvAvEOL,MsvAvNbComputerName,MsvAvNbDomainName,MsvAvDnsComputerName,MsvAvDnsDomainName,MsvAvDnsTreeName,MsvAvFlags
  } MSV1_0_AVID;

  typedef struct _MSV1_0_AV_PAIR {
    USHORT AvId;
    USHORT AvLen;

  } MSV1_0_AV_PAIR,*PMSV1_0_AV_PAIR;

  typedef enum _MSV1_0_PROTOCOL_MESSAGE_TYPE {
    MsV1_0Lm20ChallengeRequest = 0,MsV1_0Lm20GetChallengeResponse,MsV1_0EnumerateUsers,MsV1_0GetUserInfo,MsV1_0ReLogonUsers,MsV1_0ChangePassword,
    MsV1_0ChangeCachedPassword,MsV1_0GenericPassthrough,MsV1_0CacheLogon,MsV1_0SubAuth,MsV1_0DeriveCredential,MsV1_0CacheLookup,
    MsV1_0SetProcessOption
  } MSV1_0_PROTOCOL_MESSAGE_TYPE,*PMSV1_0_PROTOCOL_MESSAGE_TYPE;

  typedef struct _MSV1_0_CHANGEPASSWORD_REQUEST {
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING DomainName;
    UNICODE_STRING AccountName;
    UNICODE_STRING OldPassword;
    UNICODE_STRING NewPassword;
    BOOLEAN Impersonating;
  } MSV1_0_CHANGEPASSWORD_REQUEST,*PMSV1_0_CHANGEPASSWORD_REQUEST;

  typedef struct _MSV1_0_CHANGEPASSWORD_RESPONSE {
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    BOOLEAN PasswordInfoValid;
    DOMAIN_PASSWORD_INFORMATION DomainPasswordInfo;
  } MSV1_0_CHANGEPASSWORD_RESPONSE,*PMSV1_0_CHANGEPASSWORD_RESPONSE;

  typedef struct _MSV1_0_PASSTHROUGH_REQUEST {
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING DomainName;
    UNICODE_STRING PackageName;
    ULONG DataLength;
    PUCHAR LogonData;
    ULONG Pad;
  } MSV1_0_PASSTHROUGH_REQUEST,*PMSV1_0_PASSTHROUGH_REQUEST;

  typedef struct _MSV1_0_PASSTHROUGH_RESPONSE {
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Pad;
    ULONG DataLength;
    PUCHAR ValidationData;
  } MSV1_0_PASSTHROUGH_RESPONSE,*PMSV1_0_PASSTHROUGH_RESPONSE;

  typedef struct _MSV1_0_SUBAUTH_REQUEST{
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG SubAuthPackageId;
    ULONG SubAuthInfoLength;
    PUCHAR SubAuthSubmitBuffer;
  } MSV1_0_SUBAUTH_REQUEST,*PMSV1_0_SUBAUTH_REQUEST;

  typedef struct _MSV1_0_SUBAUTH_RESPONSE{
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG SubAuthInfoLength;
    PUCHAR SubAuthReturnBuffer;
  } MSV1_0_SUBAUTH_RESPONSE,*PMSV1_0_SUBAUTH_RESPONSE;

#define RtlGenRandom SystemFunction036
#define RtlEncryptMemory SystemFunction040
#define RtlDecryptMemory SystemFunction041

  BOOLEAN RtlGenRandom(PVOID RandomBuffer,ULONG RandomBufferLength);

#define RTL_ENCRYPT_MEMORY_SIZE 8
#define RTL_ENCRYPT_OPTION_CROSS_PROCESS 0x01
#define RTL_ENCRYPT_OPTION_SAME_LOGON 0x02

  NTSTATUS RtlEncryptMemory(PVOID Memory,ULONG MemorySize,ULONG OptionFlags);
  NTSTATUS RtlDecryptMemory(PVOID Memory,ULONG MemorySize,ULONG OptionFlags);

#define KERBEROS_VERSION 5
#define KERBEROS_REVISION 6

#define KERB_ETYPE_NULL 0
#define KERB_ETYPE_DES_CBC_CRC 1
#define KERB_ETYPE_DES_CBC_MD4 2
#define KERB_ETYPE_DES_CBC_MD5 3

#define KERB_ETYPE_RC4_MD4 -128
#define KERB_ETYPE_RC4_PLAIN2 -129
#define KERB_ETYPE_RC4_LM -130
#define KERB_ETYPE_RC4_SHA -131
#define KERB_ETYPE_DES_PLAIN -132
#define KERB_ETYPE_RC4_HMAC_OLD -133
#define KERB_ETYPE_RC4_PLAIN_OLD -134
#define KERB_ETYPE_RC4_HMAC_OLD_EXP -135
#define KERB_ETYPE_RC4_PLAIN_OLD_EXP -136
#define KERB_ETYPE_RC4_PLAIN -140
#define KERB_ETYPE_RC4_PLAIN_EXP -141

#define KERB_ETYPE_DSA_SHA1_CMS 9
#define KERB_ETYPE_RSA_MD5_CMS 10
#define KERB_ETYPE_RSA_SHA1_CMS 11
#define KERB_ETYPE_RC2_CBC_ENV 12
#define KERB_ETYPE_RSA_ENV 13
#define KERB_ETYPE_RSA_ES_OEAP_ENV 14
#define KERB_ETYPE_DES_EDE3_CBC_ENV 15

#define KERB_ETYPE_DSA_SIGN 8
#define KERB_ETYPE_RSA_PRIV 9
#define KERB_ETYPE_RSA_PUB 10
#define KERB_ETYPE_RSA_PUB_MD5 11
#define KERB_ETYPE_RSA_PUB_SHA1 12
#define KERB_ETYPE_PKCS7_PUB 13

#define KERB_ETYPE_DES3_CBC_MD5 5
#define KERB_ETYPE_DES3_CBC_SHA1 7
#define KERB_ETYPE_DES3_CBC_SHA1_KD 16

#define KERB_ETYPE_DES_CBC_MD5_NT 20
#define KERB_ETYPE_RC4_HMAC_NT 23
#define KERB_ETYPE_RC4_HMAC_NT_EXP 24

#define KERB_CHECKSUM_NONE 0
#define KERB_CHECKSUM_CRC32 1
#define KERB_CHECKSUM_MD4 2
#define KERB_CHECKSUM_KRB_DES_MAC 4
#define KERB_CHECKSUM_KRB_DES_MAC_K 5
#define KERB_CHECKSUM_MD5 7
#define KERB_CHECKSUM_MD5_DES 8

#define KERB_CHECKSUM_LM -130
#define KERB_CHECKSUM_SHA1 -131
#define KERB_CHECKSUM_REAL_CRC32 -132
#define KERB_CHECKSUM_DES_MAC -133
#define KERB_CHECKSUM_DES_MAC_MD5 -134
#define KERB_CHECKSUM_MD25 -135
#define KERB_CHECKSUM_RC4_MD5 -136
#define KERB_CHECKSUM_MD5_HMAC -137
#define KERB_CHECKSUM_HMAC_MD5 -138

#define AUTH_REQ_ALLOW_FORWARDABLE 0x00000001
#define AUTH_REQ_ALLOW_PROXIABLE 0x00000002
#define AUTH_REQ_ALLOW_POSTDATE 0x00000004
#define AUTH_REQ_ALLOW_RENEWABLE 0x00000008
#define AUTH_REQ_ALLOW_NOADDRESS 0x00000010
#define AUTH_REQ_ALLOW_ENC_TKT_IN_SKEY 0x00000020
#define AUTH_REQ_ALLOW_VALIDATE 0x00000040
#define AUTH_REQ_VALIDATE_CLIENT 0x00000080
#define AUTH_REQ_OK_AS_DELEGATE 0x00000100
#define AUTH_REQ_PREAUTH_REQUIRED 0x00000200
#define AUTH_REQ_TRANSITIVE_TRUST 0x00000400
#define AUTH_REQ_ALLOW_S4U_DELEGATE 0x00000800

#define AUTH_REQ_PER_USER_FLAGS (AUTH_REQ_ALLOW_FORWARDABLE | AUTH_REQ_ALLOW_PROXIABLE | AUTH_REQ_ALLOW_POSTDATE | AUTH_REQ_ALLOW_RENEWABLE | AUTH_REQ_ALLOW_VALIDATE)

#define KERB_TICKET_FLAGS_reserved 0x80000000
#define KERB_TICKET_FLAGS_forwardable 0x40000000
#define KERB_TICKET_FLAGS_forwarded 0x20000000
#define KERB_TICKET_FLAGS_proxiable 0x10000000
#define KERB_TICKET_FLAGS_proxy 0x08000000
#define KERB_TICKET_FLAGS_may_postdate 0x04000000
#define KERB_TICKET_FLAGS_postdated 0x02000000
#define KERB_TICKET_FLAGS_invalid 0x01000000
#define KERB_TICKET_FLAGS_renewable 0x00800000
#define KERB_TICKET_FLAGS_initial 0x00400000
#define KERB_TICKET_FLAGS_pre_authent 0x00200000
#define KERB_TICKET_FLAGS_hw_authent 0x00100000
#define KERB_TICKET_FLAGS_ok_as_delegate 0x00040000
#define KERB_TICKET_FLAGS_name_canonicalize 0x00010000
#define KERB_TICKET_FLAGS_reserved1 0x00000001

#define KRB_NT_UNKNOWN 0
#define KRB_NT_PRINCIPAL 1
#define KRB_NT_PRINCIPAL_AND_ID -131
#define KRB_NT_SRV_INST 2
#define KRB_NT_SRV_INST_AND_ID -132
#define KRB_NT_SRV_HST 3
#define KRB_NT_SRV_XHST 4
#define KRB_NT_UID 5
#define KRB_NT_ENTERPRISE_PRINCIPAL 10
#define KRB_NT_ENT_PRINCIPAL_AND_ID -130
#define KRB_NT_MS_PRINCIPAL -128
#define KRB_NT_MS_PRINCIPAL_AND_ID -129

#define KERB_IS_MS_PRINCIPAL(_x_) (((_x_) <= KRB_NT_MS_PRINCIPAL) || ((_x_) >= KRB_NT_ENTERPRISE_PRINCIPAL))

#ifndef MICROSOFT_KERBEROS_NAME_A

#define MICROSOFT_KERBEROS_NAME_A "Kerberos"
#define MICROSOFT_KERBEROS_NAME_W L"Kerberos"
#ifdef WIN32_CHICAGO
#define MICROSOFT_KERBEROS_NAME MICROSOFT_KERBEROS_NAME_A
#else
#define MICROSOFT_KERBEROS_NAME MICROSOFT_KERBEROS_NAME_W
#endif
#endif

#define KERB_WRAP_NO_ENCRYPT 0x80000001

  typedef enum _KERB_LOGON_SUBMIT_TYPE {
    KerbInteractiveLogon = 2,KerbSmartCardLogon = 6,KerbWorkstationUnlockLogon = 7,KerbSmartCardUnlockLogon = 8,KerbProxyLogon = 9,
    KerbTicketLogon = 10,KerbTicketUnlockLogon = 11,KerbS4ULogon = 12
#if (_WIN32_WINNT >= 0x0600)
   ,KerbCertificateLogon         = 13,
    KerbCertificateS4ULogon      = 14,
    KerbCertificateUnlockLogon   = 15 
#endif
  } KERB_LOGON_SUBMIT_TYPE,*PKERB_LOGON_SUBMIT_TYPE;

  typedef struct _KERB_INTERACTIVE_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING LogonDomainName;
    UNICODE_STRING UserName;
    UNICODE_STRING Password;
  } KERB_INTERACTIVE_LOGON,*PKERB_INTERACTIVE_LOGON;

  typedef struct _KERB_INTERACTIVE_UNLOCK_LOGON {
    KERB_INTERACTIVE_LOGON Logon;
    LUID LogonId;
  } KERB_INTERACTIVE_UNLOCK_LOGON,*PKERB_INTERACTIVE_UNLOCK_LOGON;

  typedef struct _KERB_SMART_CARD_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING Pin;
    ULONG CspDataLength;
    PUCHAR CspData;
  } KERB_SMART_CARD_LOGON,*PKERB_SMART_CARD_LOGON;

  typedef struct _KERB_SMART_CARD_UNLOCK_LOGON {
    KERB_SMART_CARD_LOGON Logon;
    LUID LogonId;
  } KERB_SMART_CARD_UNLOCK_LOGON,*PKERB_SMART_CARD_UNLOCK_LOGON;

  typedef struct _KERB_TICKET_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    ULONG Flags;
    ULONG ServiceTicketLength;
    ULONG TicketGrantingTicketLength;
    PUCHAR ServiceTicket;
    PUCHAR TicketGrantingTicket;
  } KERB_TICKET_LOGON,*PKERB_TICKET_LOGON;

#define KERB_LOGON_FLAG_ALLOW_EXPIRED_TICKET 0x1

  typedef struct _KERB_TICKET_UNLOCK_LOGON {
    KERB_TICKET_LOGON Logon;
    LUID LogonId;
  } KERB_TICKET_UNLOCK_LOGON,*PKERB_TICKET_UNLOCK_LOGON;

  typedef struct _KERB_S4U_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    ULONG Flags;
    UNICODE_STRING ClientUpn;
    UNICODE_STRING ClientRealm;
  } KERB_S4U_LOGON,*PKERB_S4U_LOGON;

  typedef enum _KERB_PROFILE_BUFFER_TYPE {
    KerbInteractiveProfile = 2,KerbSmartCardProfile = 4,KerbTicketProfile = 6
  } KERB_PROFILE_BUFFER_TYPE,*PKERB_PROFILE_BUFFER_TYPE;

  typedef struct _KERB_INTERACTIVE_PROFILE {
    KERB_PROFILE_BUFFER_TYPE MessageType;
    USHORT LogonCount;
    USHORT BadPasswordCount;
    LARGE_INTEGER LogonTime;
    LARGE_INTEGER LogoffTime;
    LARGE_INTEGER KickOffTime;
    LARGE_INTEGER PasswordLastSet;
    LARGE_INTEGER PasswordCanChange;
    LARGE_INTEGER PasswordMustChange;
    UNICODE_STRING LogonScript;
    UNICODE_STRING HomeDirectory;
    UNICODE_STRING FullName;
    UNICODE_STRING ProfilePath;
    UNICODE_STRING HomeDirectoryDrive;
    UNICODE_STRING LogonServer;
    ULONG UserFlags;
  } KERB_INTERACTIVE_PROFILE,*PKERB_INTERACTIVE_PROFILE;

  typedef struct _KERB_SMART_CARD_PROFILE {
    KERB_INTERACTIVE_PROFILE Profile;
    ULONG CertificateSize;
    PUCHAR CertificateData;
  } KERB_SMART_CARD_PROFILE,*PKERB_SMART_CARD_PROFILE;

  typedef struct KERB_CRYPTO_KEY {
    LONG KeyType;
    ULONG Length;
    PUCHAR Value;
  } KERB_CRYPTO_KEY,*PKERB_CRYPTO_KEY;

  typedef struct _KERB_TICKET_PROFILE {
    KERB_INTERACTIVE_PROFILE Profile;
    KERB_CRYPTO_KEY SessionKey;
  } KERB_TICKET_PROFILE,*PKERB_TICKET_PROFILE;

  typedef enum _KERB_PROTOCOL_MESSAGE_TYPE {
    KerbDebugRequestMessage = 0,KerbQueryTicketCacheMessage,KerbChangeMachinePasswordMessage,KerbVerifyPacMessage,KerbRetrieveTicketMessage,
    KerbUpdateAddressesMessage,KerbPurgeTicketCacheMessage,KerbChangePasswordMessage,KerbRetrieveEncodedTicketMessage,KerbDecryptDataMessage,
    KerbAddBindingCacheEntryMessage,KerbSetPasswordMessage,KerbSetPasswordExMessage,KerbVerifyCredentialsMessage,KerbQueryTicketCacheExMessage,
    KerbPurgeTicketCacheExMessage,KerbRefreshSmartcardCredentialsMessage,KerbAddExtraCredentialsMessage,KerbQuerySupplementalCredentialsMessage,
    KerbTransferCredentialsMessage,KerbQueryTicketCacheEx2Message
  } KERB_PROTOCOL_MESSAGE_TYPE,*PKERB_PROTOCOL_MESSAGE_TYPE;

  typedef struct _KERB_QUERY_TKT_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
  } KERB_QUERY_TKT_CACHE_REQUEST,*PKERB_QUERY_TKT_CACHE_REQUEST;

  typedef struct _KERB_TICKET_CACHE_INFO {
    UNICODE_STRING ServerName;
    UNICODE_STRING RealmName;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewTime;
    LONG EncryptionType;
    ULONG TicketFlags;
  } KERB_TICKET_CACHE_INFO,*PKERB_TICKET_CACHE_INFO;

  typedef struct _KERB_TICKET_CACHE_INFO_EX {
    UNICODE_STRING ClientName;
    UNICODE_STRING ClientRealm;
    UNICODE_STRING ServerName;
    UNICODE_STRING ServerRealm;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewTime;
    LONG EncryptionType;
    ULONG TicketFlags;
  } KERB_TICKET_CACHE_INFO_EX,*PKERB_TICKET_CACHE_INFO_EX;

  typedef struct _KERB_TICKET_CACHE_INFO_EX2 {
    UNICODE_STRING ClientName;
    UNICODE_STRING ClientRealm;
    UNICODE_STRING ServerName;
    UNICODE_STRING ServerRealm;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewTime;
    LONG EncryptionType;
    ULONG TicketFlags;
    ULONG SessionKeyType;
  } KERB_TICKET_CACHE_INFO_EX2,*PKERB_TICKET_CACHE_INFO_EX2;

  typedef struct _KERB_QUERY_TKT_CACHE_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfTickets;
    KERB_TICKET_CACHE_INFO Tickets[ANYSIZE_ARRAY];
  } KERB_QUERY_TKT_CACHE_RESPONSE,*PKERB_QUERY_TKT_CACHE_RESPONSE;

  typedef struct _KERB_QUERY_TKT_CACHE_EX_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfTickets;
    KERB_TICKET_CACHE_INFO_EX Tickets[ANYSIZE_ARRAY];
  } KERB_QUERY_TKT_CACHE_EX_RESPONSE,*PKERB_QUERY_TKT_CACHE_EX_RESPONSE;

  typedef struct _KERB_QUERY_TKT_CACHE_EX2_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfTickets;
    KERB_TICKET_CACHE_INFO_EX2 Tickets[ANYSIZE_ARRAY];
  } KERB_QUERY_TKT_CACHE_EX2_RESPONSE,*PKERB_QUERY_TKT_CACHE_EX2_RESPONSE;

#ifndef __SECHANDLE_DEFINED__
  typedef struct _SecHandle {
    ULONG_PTR dwLower;
    ULONG_PTR dwUpper;
  } SecHandle,*PSecHandle;

#define __SECHANDLE_DEFINED__
#endif

#define KERB_USE_DEFAULT_TICKET_FLAGS 0x0

#define KERB_RETRIEVE_TICKET_DEFAULT 0x0
#define KERB_RETRIEVE_TICKET_DONT_USE_CACHE 0x1
#define KERB_RETRIEVE_TICKET_USE_CACHE_ONLY 0x2
#define KERB_RETRIEVE_TICKET_USE_CREDHANDLE 0x4
#define KERB_RETRIEVE_TICKET_AS_KERB_CRED 0x8
#define KERB_RETRIEVE_TICKET_WITH_SEC_CRED 0x10
#define KERB_RETRIEVE_TICKET_CACHE_TICKET 0x20

#define KERB_ETYPE_DEFAULT 0x0

  typedef struct _KERB_AUTH_DATA {
    ULONG Type;
    ULONG Length;
    PUCHAR Data;
  } KERB_AUTH_DATA,*PKERB_AUTH_DATA;

  typedef struct _KERB_NET_ADDRESS {
    ULONG Family;
    ULONG Length;
    PCHAR Address;
  } KERB_NET_ADDRESS,*PKERB_NET_ADDRESS;

  typedef struct _KERB_NET_ADDRESSES {
    ULONG Number;
    KERB_NET_ADDRESS Addresses[ANYSIZE_ARRAY];
  } KERB_NET_ADDRESSES,*PKERB_NET_ADDRESSES;

  typedef struct _KERB_EXTERNAL_NAME {
    SHORT NameType;
    USHORT NameCount;
    UNICODE_STRING Names[ANYSIZE_ARRAY];
  } KERB_EXTERNAL_NAME,*PKERB_EXTERNAL_NAME;

  typedef struct _KERB_EXTERNAL_TICKET {
    PKERB_EXTERNAL_NAME ServiceName;
    PKERB_EXTERNAL_NAME TargetName;
    PKERB_EXTERNAL_NAME ClientName;
    UNICODE_STRING DomainName;
    UNICODE_STRING TargetDomainName;
    UNICODE_STRING AltTargetDomainName;
    KERB_CRYPTO_KEY SessionKey;
    ULONG TicketFlags;
    ULONG Flags;
    LARGE_INTEGER KeyExpirationTime;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewUntil;
    LARGE_INTEGER TimeSkew;
    ULONG EncodedTicketSize;
    PUCHAR EncodedTicket;
  } KERB_EXTERNAL_TICKET,*PKERB_EXTERNAL_TICKET;

  typedef struct _KERB_RETRIEVE_TKT_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    UNICODE_STRING TargetName;
    ULONG TicketFlags;
    ULONG CacheOptions;
    LONG EncryptionType;
    SecHandle CredentialsHandle;
  } KERB_RETRIEVE_TKT_REQUEST,*PKERB_RETRIEVE_TKT_REQUEST;

  typedef struct _KERB_RETRIEVE_TKT_RESPONSE {
    KERB_EXTERNAL_TICKET Ticket;
  } KERB_RETRIEVE_TKT_RESPONSE,*PKERB_RETRIEVE_TKT_RESPONSE;

  typedef struct _KERB_PURGE_TKT_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    UNICODE_STRING ServerName;
    UNICODE_STRING RealmName;
  } KERB_PURGE_TKT_CACHE_REQUEST,*PKERB_PURGE_TKT_CACHE_REQUEST;

#define KERB_PURGE_ALL_TICKETS 1

  typedef struct _KERB_PURGE_TKT_CACHE_EX_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    ULONG Flags;
    KERB_TICKET_CACHE_INFO_EX TicketTemplate;
  } KERB_PURGE_TKT_CACHE_EX_REQUEST,*PKERB_PURGE_TKT_CACHE_EX_REQUEST;

  typedef struct _KERB_CHANGEPASSWORD_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING DomainName;
    UNICODE_STRING AccountName;
    UNICODE_STRING OldPassword;
    UNICODE_STRING NewPassword;
    BOOLEAN Impersonating;
  } KERB_CHANGEPASSWORD_REQUEST,*PKERB_CHANGEPASSWORD_REQUEST;

  typedef struct _KERB_SETPASSWORD_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    SecHandle CredentialsHandle;
    ULONG Flags;
    UNICODE_STRING DomainName;
    UNICODE_STRING AccountName;
    UNICODE_STRING Password;
  } KERB_SETPASSWORD_REQUEST,*PKERB_SETPASSWORD_REQUEST;

  typedef struct _KERB_SETPASSWORD_EX_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    SecHandle CredentialsHandle;
    ULONG Flags;
    UNICODE_STRING AccountRealm;
    UNICODE_STRING AccountName;
    UNICODE_STRING Password;
    UNICODE_STRING ClientRealm;
    UNICODE_STRING ClientName;
    BOOLEAN Impersonating;
    UNICODE_STRING KdcAddress;
    ULONG KdcAddressType;
  } KERB_SETPASSWORD_EX_REQUEST,*PKERB_SETPASSWORD_EX_REQUEST;

#define DS_UNKNOWN_ADDRESS_TYPE 0
#define KERB_SETPASS_USE_LOGONID 1
#define KERB_SETPASS_USE_CREDHANDLE 2

  typedef struct _KERB_DECRYPT_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    ULONG Flags;
    LONG CryptoType;
    LONG KeyUsage;
    KERB_CRYPTO_KEY Key;
    ULONG EncryptedDataSize;
    ULONG InitialVectorSize;
    PUCHAR InitialVector;
    PUCHAR EncryptedData;
  } KERB_DECRYPT_REQUEST,*PKERB_DECRYPT_REQUEST;

#define KERB_DECRYPT_FLAG_DEFAULT_KEY 0x00000001

  typedef struct _KERB_DECRYPT_RESPONSE {
    UCHAR DecryptedData[ANYSIZE_ARRAY];
  } KERB_DECRYPT_RESPONSE,*PKERB_DECRYPT_RESPONSE;

  typedef struct _KERB_ADD_BINDING_CACHE_ENTRY_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING RealmName;
    UNICODE_STRING KdcAddress;
    ULONG AddressType;
  } KERB_ADD_BINDING_CACHE_ENTRY_REQUEST,*PKERB_ADD_BINDING_CACHE_ENTRY_REQUEST;

  typedef struct _KERB_REFRESH_SCCRED_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING CredentialBlob;
    LUID LogonId;
    ULONG Flags;
  } KERB_REFRESH_SCCRED_REQUEST,*PKERB_REFRESH_SCCRED_REQUEST;

#define KERB_REFRESH_SCCRED_RELEASE 0x0
#define KERB_REFRESH_SCCRED_GETTGT 0x1

  typedef struct _KERB_ADD_CREDENTIALS_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING UserName;
    UNICODE_STRING DomainName;
    UNICODE_STRING Password;
    LUID LogonId;
    ULONG Flags;
  } KERB_ADD_CREDENTIALS_REQUEST,*PKERB_ADD_CREDENTIALS_REQUEST;

#define KERB_REQUEST_ADD_CREDENTIAL 1
#define KERB_REQUEST_REPLACE_CREDENTIAL 2
#define KERB_REQUEST_REMOVE_CREDENTIAL 4

  typedef struct _KERB_TRANSFER_CRED_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID OriginLogonId;
    LUID DestinationLogonId;
    ULONG Flags;
  } KERB_TRANSFER_CRED_REQUEST,*PKERB_TRANSFER_CRED_REQUEST;

#if (_WIN32_WINNT >= 0x0600)

#define PER_USER_POLICY_UNCHANGED 0x00
#define PER_USER_AUDIT_SUCCESS_INCLUDE 0x01
#define PER_USER_AUDIT_SUCCESS_EXCLUDE 0x02
#define PER_USER_AUDIT_FAILURE_INCLUDE 0x04
#define PER_USER_AUDIT_FAILURE_EXCLUDE 0x08
#define PER_USER_AUDIT_NONE 0x10

  typedef struct _AUDIT_POLICY_INFORMATION {
    GUID  AuditSubCategoryGuid;
    ULONG AuditingInformation;
    GUID  AuditCategoryGuid;
  } AUDIT_POLICY_INFORMATION, *PAUDIT_POLICY_INFORMATION, *PCAUDIT_POLICY_INFORMATION;

  typedef struct _POLICY_AUDIT_SID_ARRAY {
    ULONG UsersCount;
    PSID  *UserSidArray;
  } POLICY_AUDIT_SID_ARRAY, *PPOLICY_AUDIT_SID_ARRAY;

  typedef struct _KERB_CERTIFICATE_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING         DomainName;
    UNICODE_STRING         UserName;
    UNICODE_STRING         Pin;
    ULONG                  Flags;
    ULONG                  CspDataLength;
    PUCHAR                 CspData;
  } KERB_CERTIFICATE_LOGON, *PKERB_CERTIFICATE_LOGON;

  typedef struct _KERB_CERTIFICATE_UNLOCK_LOGON {
    KERB_CERTIFICATE_LOGON Logon;
    LUID                   LogonId;
  } KERB_CERTIFICATE_UNLOCK_LOGON, *PKERB_CERTIFICATE_UNLOCK_LOGON;

  typedef struct _KERB_SMARTCARD_CSP_INFO {
    DWORD dwCspInfoLen;
    DWORD MessageType;
    __C89_NAMELESS union {
      PVOID   ContextInformation;
      ULONG64 SpaceHolderForWow64;
    };
    DWORD flags;
    DWORD KeySpec;
    ULONG nCardNameOffset;
    ULONG nReaderNameOffset;
    ULONG nContainerNameOffset;
    ULONG nCSPNameOffset;
    TCHAR bBuffer;
  } KERB_SMARTCARD_CSP_INFO, *PKERB_SMARTCARD_CSP_INFO;

  BOOLEAN WINAPI AuditComputeEffectivePolicyBySid(
    const PSID pSid,
    const GUID *pSubCategoryGuids,
    ULONG PolicyCount,
    PAUDIT_POLICY_INFORMATION *ppAuditPolicy
  );

  VOID WINAPI AuditFree(
    PVOID Buffer
  );

  BOOLEAN WINAPI AuditSetSystemPolicy(
    PCAUDIT_POLICY_INFORMATION pAuditPolicy,
    ULONG PolicyCount
  );

  BOOLEAN WINAPI AuditQuerySystemPolicy(
    const GUID *pSubCategoryGuids,
    ULONG PolicyCount,
    PAUDIT_POLICY_INFORMATION *ppAuditPolicy
  );

  BOOLEAN WINAPI AuditSetPerUserPolicy(
    const PSID pSid,
    PCAUDIT_POLICY_INFORMATION pAuditPolicy,
    ULONG PolicyCount
  );

  BOOLEAN WINAPI AuditQueryPerUserPolicy(
    const PSID pSid,
    const GUID *pSubCategoryGuids,
    ULONG PolicyCount,
    PAUDIT_POLICY_INFORMATION *ppAuditPolicy
  );

  BOOLEAN WINAPI AuditComputeEffectivePolicyByToken(
    HANDLE hTokenHandle,
    const GUID *pSubCategoryGuids,
    ULONG PolicyCount,
    PAUDIT_POLICY_INFORMATION *ppAuditPolicy
  );

  BOOLEAN WINAPI AuditEnumerateCategories(
    GUID **ppAuditCategoriesArray,
    PULONG pCountReturned
  );

  BOOLEAN WINAPI AuditEnumeratePerUserPolicy(
    PPOLICY_AUDIT_SID_ARRAY *ppAuditSidArray
  );

  BOOLEAN WINAPI AuditEnumerateSubCategories(
    const GUID *pAuditCategoryGuid,
    BOOLEAN bRetrieveAllSubCategories,
    GUID **ppAuditSubCategoriesArray,
    PULONG pCountReturned
  );

  BOOLEAN WINAPI AuditLookupCategoryGuidFromCategoryId(
    POLICY_AUDIT_EVENT_TYPE AuditCategoryId,
    GUID *pAuditCategoryGuid
  );

  BOOLEAN WINAPI AuditQuerySecurity(
    SECURITY_INFORMATION SecurityInformation,
    PSECURITY_DESCRIPTOR *ppSecurityDescriptor
  );

#define AuditLookupSubCategoryName __MINGW_NAME_AW(AuditLookupSubCategoryName)
#define AuditLookupCategoryName __MINGW_NAME_AW(AuditLookupCategoryName)

  BOOLEAN WINAPI AuditLookupSubCategoryNameA(
    const GUID *pAuditSubCategoryGuid,
    LPSTR *ppszSubCategoryName
  );

  BOOLEAN WINAPI AuditLookupSubCategoryNameW(
    const GUID *pAuditSubCategoryGuid,
    LPWSTR *ppszSubCategoryName
  );

  BOOLEAN WINAPI AuditLookupCategoryNameA(
    const GUID *pAuditCategoryGuid,
    LPSTR *ppszCategoryName
  );

  BOOLEAN WINAPI AuditLookupCategoryNameW(
    const GUID *pAuditCategoryGuid,
    LPWSTR *ppszCategoryName
  );

  BOOLEAN WINAPI AuditLookupCategoryIdFromCategoryGuid(
    const GUID *pAuditCategoryGuid,
    PPOLICY_AUDIT_EVENT_TYPE pAuditCategoryId
  );

  BOOLEAN WINAPI AuditSetSecurity(
    SECURITY_INFORMATION SecurityInformation,
    PSECURITY_DESCRIPTOR pSecurityDescriptor
  );

#endif /*(_WIN32_WINNT >= 0x0600)*/

#ifdef __cplusplus
}
#endif
#endif
