/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _HTMLGUID_H_
#define _HTMLGUID_H_

DEFINE_GUID(CLSID_HTMLViewer,0x25336920,0x3f9,0x11cf,0x8f,0xd0,0x0,0xaa,0x0,0x68,0x6f,0x13);
DEFINE_GUID(CLSID_HTMLBSCBProxy,0x25336922,0x3f9,0x11cf,0x8f,0xd0,0x0,0xaa,0x0,0x68,0x6f,0x13);
DEFINE_GUID(GUID_PageTL,0x71bc8840,0x60bb,0x11cf,0x8b,0x97,0x0,0xaa,0x0,0x47,0x6d,0xa6);
DEFINE_GUID(IID_Page<PERSON>rops,0x71bc8841,0x60bb,0x11cf,0x8b,0x97,0x0,0xaa,0x0,0x47,0x6d,0xa6);
DEFINE_GUID(IID_PageEvents,0x71bc8842,0x60bb,0x11cf,0x8b,0x97,0x0,0xaa,0x0,0x47,0x6d,0xa6);
DEFINE_GUID(CLSID_Page,0x71bc8843,0x60bb,0x11cf,0x8b,0x97,0x0,0xaa,0x0,0x47,0x6d,0xa6);

#endif
