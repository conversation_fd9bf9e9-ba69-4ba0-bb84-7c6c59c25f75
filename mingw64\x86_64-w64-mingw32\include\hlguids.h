/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef HLGUIDS_H
#define HLGUIDS_H

DEFINE_GUID(IID_IHlinkSite,0x79eac9c2,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
DEFINE_GUID(IID_IHlink,0x79eac9c3,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
DEFINE_GUID(IID_IHlinkTarget,0x79eac9c4,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
DEFINE_GUID(IID_IHlinkFrame,0x79eac9c5,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
DEFINE_GUID(IID_IEnumHLITEM,0x79eac9c6,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
DEFINE_GUID(IID_IHlinkBrowseContext,0x79eac9c7,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
DEFINE_GUID(IID_IExtensionServices,0x79eac9cb,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
DEFINE_GUID(CLSID_StdHlink,0x79eac9d0,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
DEFINE_GUID(CLSID_StdHlinkBrowseContext,0x79eac9d1,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#ifndef SID_SHlinkFrame
DEFINE_GUID(SID_SHlinkFrame,0x79eac9c5,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#endif
DEFINE_GUID(SID_SContainer,0x79eac9c4,0xbaf9,0x11ce,0x8c,0x82,0x00,0xaa,0x00,0x4b,0xa9,0x0b);

#endif
