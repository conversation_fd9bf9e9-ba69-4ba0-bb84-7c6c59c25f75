/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION (WINAPI_PARTITION_DESKTOP)

#ifdef DEFINE_KNOWN_FOLDER
#undef DEFINE_KNOWN_FOLDER
#endif

#ifdef INITGUID
#ifdef __cplusplus
#define DEFINE_KNOWN_FOLDER(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8) EXTERN_C const GUID DECLSPEC_SELECTANY name = { l, w1, w2,{ b1, b2, b3, b4, b5, b6, b7, b8 } }
#else
#define DEFINE_KNOWN_FOLDER(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8) const GUID DECLSPEC_SELECTANY name = { l, w1, w2,{ b1, b2, b3, b4, b5, b6, b7, b8 } }
#endif
#else
#define DEFINE_KNOWN_FOLDER(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8) EXTERN_C const GUID name
#endif

DEFINE_KNOWN_FOLDER (FOLDERID_AccountPictures, 0x008ca0b1, 0x55b4, 0x4c56, 0xb8, 0xa8, 0x4d, 0xe4, 0xb2, 0x99, 0xd3, 0xbe);
DEFINE_KNOWN_FOLDER (FOLDERID_AddNewPrograms, 0xde61d971, 0x5ebc, 0x4f02, 0xa3, 0xa9, 0x6c, 0x82, 0x89, 0x5e, 0x5c, 0x04);
DEFINE_KNOWN_FOLDER (FOLDERID_AdminTools, 0x724ef170, 0xa42d, 0x4fef, 0x9f, 0x26, 0xb6, 0x0e, 0x84, 0x6f, 0xba, 0x4f);
DEFINE_KNOWN_FOLDER (FOLDERID_AppsFolder, 0x1e87508d, 0x89c2, 0x42f0, 0x8a, 0x7e, 0x64, 0x5a, 0x0f, 0x50, 0xca, 0x58);
DEFINE_KNOWN_FOLDER (FOLDERID_ApplicationShortcuts, 0xa3918781, 0xe5f2, 0x4890, 0xb3, 0xd9, 0xa7, 0xe5, 0x43, 0x32, 0x32, 0x8c);
DEFINE_KNOWN_FOLDER (FOLDERID_AppUpdates, 0xa305ce99, 0xf527, 0x492b, 0x8b, 0x1a, 0x7e, 0x76, 0xfa, 0x98, 0xd6, 0xe4);
DEFINE_KNOWN_FOLDER (FOLDERID_CDBurning, 0x9e52ab10, 0xf80d, 0x49df, 0xac, 0xb8, 0x43, 0x30, 0xf5, 0x68, 0x78, 0x55);
DEFINE_KNOWN_FOLDER (FOLDERID_ChangeRemovePrograms, 0xdf7266ac, 0x9274, 0x4867, 0x8d, 0x55, 0x3b, 0xd6, 0x61, 0xde, 0x87, 0x2d);
DEFINE_KNOWN_FOLDER (FOLDERID_CommonAdminTools, 0xd0384e7d, 0xbac3, 0x4797, 0x8f, 0x14, 0xcb, 0xa2, 0x29, 0xb3, 0x92, 0xb5);
DEFINE_KNOWN_FOLDER (FOLDERID_CommonOEMLinks, 0xc1bae2d0, 0x10df, 0x4334, 0xbe, 0xdd, 0x7a, 0xa2, 0x0b, 0x22, 0x7a, 0x9d);
DEFINE_KNOWN_FOLDER (FOLDERID_CommonPrograms, 0x0139d44e, 0x6afe, 0x49f2, 0x86, 0x90, 0x3d, 0xaf, 0xca, 0xe6, 0xff, 0xb8);
DEFINE_KNOWN_FOLDER (FOLDERID_CommonStartMenu, 0xa4115719, 0xd62e, 0x491d, 0xaa, 0x7c, 0xe7, 0x4b, 0x8b, 0xe3, 0xb0, 0x67);
DEFINE_KNOWN_FOLDER (FOLDERID_CommonStartup, 0x82a5ea35, 0xd9cd, 0x47c5, 0x96, 0x29, 0xe1, 0x5d, 0x2f, 0x71, 0x4e, 0x6e);
DEFINE_KNOWN_FOLDER (FOLDERID_CommonTemplates, 0xb94237e7, 0x57ac, 0x4347, 0x91, 0x51, 0xb0, 0x8c, 0x6c, 0x32, 0xd1, 0xf7);
DEFINE_KNOWN_FOLDER (FOLDERID_ComputerFolder, 0x0ac0837c, 0xbbf8, 0x452a, 0x85, 0x0d, 0x79, 0xd0, 0x8e, 0x66, 0x7c, 0xa7);
DEFINE_KNOWN_FOLDER (FOLDERID_ConflictFolder, 0x4bfefb45, 0x347d, 0x4006, 0xa5, 0xbe, 0xac, 0x0c, 0xb0, 0x56, 0x71, 0x92);
DEFINE_KNOWN_FOLDER (FOLDERID_ConnectionsFolder, 0x6f0cd92b, 0x2e97, 0x45d1, 0x88, 0xff, 0xb0, 0xd1, 0x86, 0xb8, 0xde, 0xdd);
DEFINE_KNOWN_FOLDER (FOLDERID_Contacts, 0x56784854, 0xc6cb, 0x462b, 0x81, 0x69, 0x88, 0xe3, 0x50, 0xac, 0xb8, 0x82);
DEFINE_KNOWN_FOLDER (FOLDERID_ControlPanelFolder, 0x82a74aeb, 0xaeb4, 0x465c, 0xa0, 0x14, 0xd0, 0x97, 0xee, 0x34, 0x6d, 0x63);
DEFINE_KNOWN_FOLDER (FOLDERID_Cookies, 0x2b0f765d, 0xc0e9, 0x4171, 0x90, 0x8e, 0x08, 0xa6, 0x11, 0xb8, 0x4f, 0xf6);
DEFINE_KNOWN_FOLDER (FOLDERID_Desktop, 0xb4bfcc3a, 0xdb2c, 0x424c, 0xb0, 0x29, 0x7f, 0xe9, 0x9a, 0x87, 0xc6, 0x41);
DEFINE_KNOWN_FOLDER (FOLDERID_DeviceMetadataStore, 0x5ce4a5e9, 0xe4eb, 0x479d, 0xb8, 0x9f, 0x13, 0x0c, 0x02, 0x88, 0x61, 0x55);
DEFINE_KNOWN_FOLDER (FOLDERID_Documents, 0xfdd39ad0, 0x238f, 0x46af, 0xad, 0xb4, 0x6c, 0x85, 0x48, 0x03, 0x69, 0xc7);
DEFINE_KNOWN_FOLDER (FOLDERID_DocumentsLibrary, 0x7b0db17d, 0x9cd2, 0x4a93, 0x97, 0x33, 0x46, 0xcc, 0x89, 0x02, 0x2e, 0x7c);
DEFINE_KNOWN_FOLDER (FOLDERID_Downloads, 0x374de290, 0x123f, 0x4565, 0x91, 0x64, 0x39, 0xc4, 0x92, 0x5e, 0x46, 0x7b);
DEFINE_KNOWN_FOLDER (FOLDERID_Favorites, 0x1777f761, 0x68ad, 0x4d8a, 0x87, 0xbd, 0x30, 0xb7, 0x59, 0xfa, 0x33, 0xdd);
DEFINE_KNOWN_FOLDER (FOLDERID_Fonts, 0xfd228cb7, 0xae11, 0x4ae3, 0x86, 0x4c, 0x16, 0xf3, 0x91, 0x0a, 0xb8, 0xfe);
DEFINE_KNOWN_FOLDER (FOLDERID_Games, 0xcac52c1a, 0xb53d, 0x4edc, 0x92, 0xd7, 0x6b, 0x2e, 0x8a, 0xc1, 0x94, 0x34);
DEFINE_KNOWN_FOLDER (FOLDERID_GameTasks, 0x54fae61, 0x4dd8, 0x4787, 0x80, 0xb6, 0x9, 0x2, 0x20, 0xc4, 0xb7, 0x0);
DEFINE_KNOWN_FOLDER (FOLDERID_History, 0xd9dc8a3b, 0xb784, 0x432e, 0xa7, 0x81, 0x5a, 0x11, 0x30, 0xa7, 0x59, 0x63);
DEFINE_KNOWN_FOLDER (FOLDERID_HomeGroup, 0x52528a6b, 0xb9e3, 0x4add, 0xb6, 0xd, 0x58, 0x8c, 0x2d, 0xba, 0x84, 0x2d);
DEFINE_KNOWN_FOLDER (FOLDERID_HomeGroupCurrentUser, 0x9b74b6a3, 0xdfd, 0x4f11, 0x9e, 0x78, 0x5f, 0x78, 0x0, 0xf2, 0xe7, 0x72);
DEFINE_KNOWN_FOLDER (FOLDERID_ImplicitAppShortcuts, 0xbcb5256f, 0x79f6, 0x4cee, 0xb7, 0x25, 0xdc, 0x34, 0xe4, 0x2, 0xfd, 0x46);
DEFINE_KNOWN_FOLDER (FOLDERID_InternetCache, 0x352481e8, 0x33be, 0x4251, 0xba, 0x85, 0x60, 0x07, 0xca, 0xed, 0xcf, 0x9d);
DEFINE_KNOWN_FOLDER (FOLDERID_InternetFolder, 0x4d9f7874, 0x4e0c, 0x4904, 0x96, 0x7b, 0x40, 0xb0, 0xd2, 0x0c, 0x3e, 0x4b);
DEFINE_KNOWN_FOLDER (FOLDERID_Libraries, 0x1b3ea5dc, 0xb587, 0x4786, 0xb4, 0xef, 0xbd, 0x1d, 0xc3, 0x32, 0xae, 0xae);
DEFINE_KNOWN_FOLDER (FOLDERID_Links, 0xbfb9d5e0, 0xc6a9, 0x404c, 0xb2, 0xb2, 0xae, 0x6d, 0xb6, 0xaf, 0x49, 0x68);
DEFINE_KNOWN_FOLDER (FOLDERID_LocalAppData, 0xf1b32785, 0x6fba, 0x4fcf, 0x9d, 0x55, 0x7b, 0x8e, 0x7f, 0x15, 0x70, 0x91);
DEFINE_KNOWN_FOLDER (FOLDERID_LocalAppDataLow, 0xa520a1a4, 0x1780, 0x4ff6, 0xbd, 0x18, 0x16, 0x73, 0x43, 0xc5, 0xaf, 0x16);
DEFINE_KNOWN_FOLDER (FOLDERID_LocalizedResourcesDir, 0x2a00375e, 0x224c, 0x49de, 0xb8, 0xd1, 0x44, 0x0d, 0xf7, 0xef, 0x3d, 0xdc);
DEFINE_KNOWN_FOLDER (FOLDERID_Music, 0x4bd8d571, 0x6d19, 0x48d3, 0xbe, 0x97, 0x42, 0x22, 0x20, 0x08, 0x0e, 0x43);
DEFINE_KNOWN_FOLDER (FOLDERID_MusicLibrary, 0x2112ab0a, 0xc86a, 0x4ffe, 0xa3, 0x68, 0xd, 0xe9, 0x6e, 0x47, 0x1, 0x2e);
DEFINE_KNOWN_FOLDER (FOLDERID_NetHood, 0xc5abbf53, 0xe17f, 0x4121, 0x89, 0x00, 0x86, 0x62, 0x6f, 0xc2, 0xc9, 0x73);
DEFINE_KNOWN_FOLDER (FOLDERID_NetworkFolder, 0xd20beec4, 0x5ca8, 0x4905, 0xae, 0x3b, 0xbf, 0x25, 0x1e, 0xa0, 0x9b, 0x53);
DEFINE_KNOWN_FOLDER (FOLDERID_OriginalImages, 0x2c36c0aa, 0x5812, 0x4b87, 0xbf, 0xd0, 0x4c, 0xd0, 0xdf, 0xb1, 0x9b, 0x39);
DEFINE_KNOWN_FOLDER (FOLDERID_PhotoAlbums, 0x69d2cf90, 0xfc33, 0x4fb7, 0x9a, 0x0c, 0xeb, 0xb0, 0xf0, 0xfc, 0xb4, 0x3c);
DEFINE_KNOWN_FOLDER (FOLDERID_Pictures, 0x33e28130, 0x4e1e, 0x4676, 0x83, 0x5a, 0x98, 0x39, 0x5c, 0x3b, 0xc3, 0xbb);
DEFINE_KNOWN_FOLDER (FOLDERID_PicturesLibrary, 0xa990ae9f, 0xa03b, 0x4e80, 0x94, 0xbc, 0x99, 0x12, 0xd7, 0x50, 0x41, 0x4);
DEFINE_KNOWN_FOLDER (FOLDERID_Playlists, 0xde92c1c7, 0x837f, 0x4f69, 0xa3, 0xbb, 0x86, 0xe6, 0x31, 0x20, 0x4a, 0x23);
DEFINE_KNOWN_FOLDER (FOLDERID_PrintHood, 0x9274bd8d, 0xcfd1, 0x41c3, 0xb3, 0x5e, 0xb1, 0x3f, 0x55, 0xa7, 0x58, 0xf4);
DEFINE_KNOWN_FOLDER (FOLDERID_PrintersFolder, 0x76fc4e2d, 0xd6ad, 0x4519, 0xa6, 0x63, 0x37, 0xbd, 0x56, 0x06, 0x81, 0x85);
DEFINE_KNOWN_FOLDER (FOLDERID_Profile, 0x5e6c858f, 0x0e22, 0x4760, 0x9a, 0xfe, 0xea, 0x33, 0x17, 0xb6, 0x71, 0x73);
DEFINE_KNOWN_FOLDER (FOLDERID_ProgramData, 0x62ab5d82, 0xfdc1, 0x4dc3, 0xa9, 0xdd, 0x07, 0x0d, 0x1d, 0x49, 0x5d, 0x97);
DEFINE_KNOWN_FOLDER (FOLDERID_ProgramFiles, 0x905e63b6, 0xc1bf, 0x494e, 0xb2, 0x9c, 0x65, 0xb7, 0x32, 0xd3, 0xd2, 0x1a);
DEFINE_KNOWN_FOLDER (FOLDERID_ProgramFilesX64, 0x6d809377, 0x6af0, 0x444b, 0x89, 0x57, 0xa3, 0x77, 0x3f, 0x02, 0x20, 0x0e);
DEFINE_KNOWN_FOLDER (FOLDERID_ProgramFilesX86, 0x7c5a40ef, 0xa0fb, 0x4bfc, 0x87, 0x4a, 0xc0, 0xf2, 0xe0, 0xb9, 0xfa, 0x8e);
DEFINE_KNOWN_FOLDER (FOLDERID_ProgramFilesCommon, 0xf7f1ed05, 0x9f6d, 0x47a2, 0xaa, 0xae, 0x29, 0xd3, 0x17, 0xc6, 0xf0, 0x66);
DEFINE_KNOWN_FOLDER (FOLDERID_ProgramFilesCommonX64, 0x6365d5a7, 0xf0d, 0x45e5, 0x87, 0xf6, 0xd, 0xa5, 0x6b, 0x6a, 0x4f, 0x7d);
DEFINE_KNOWN_FOLDER (FOLDERID_ProgramFilesCommonX86, 0xde974d24, 0xd9c6, 0x4d3e, 0xbf, 0x91, 0xf4, 0x45, 0x51, 0x20, 0xb9, 0x17);
DEFINE_KNOWN_FOLDER (FOLDERID_Programs, 0xa77f5d77, 0x2e2b, 0x44c3, 0xa6, 0xa2, 0xab, 0xa6, 0x01, 0x05, 0x4a, 0x51);
DEFINE_KNOWN_FOLDER (FOLDERID_Public, 0xdfdf76a2, 0xc82a, 0x4d63, 0x90, 0x6a, 0x56, 0x44, 0xac, 0x45, 0x73, 0x85);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicDesktop, 0xc4aa340d, 0xf20f, 0x4863, 0xaf, 0xef, 0xf8, 0x7e, 0xf2, 0xe6, 0xba, 0x25);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicDocuments, 0xed4824af, 0xdce4, 0x45a8, 0x81, 0xe2, 0xfc, 0x79, 0x65, 0x08, 0x36, 0x34);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicDownloads, 0x3d644c9b, 0x1fb8, 0x4f30, 0x9b, 0x45, 0xf6, 0x70, 0x23, 0x5f, 0x79, 0xc0);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicGameTasks, 0xdebf2536, 0xe1a8, 0x4c59, 0xb6, 0xa2, 0x41, 0x45, 0x86, 0x47, 0x6a, 0xea);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicLibraries, 0x48daf80b, 0xe6cf, 0x4f4e, 0xb8, 0x00, 0x0e, 0x69, 0xd8, 0x4e, 0xe3, 0x84);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicMusic, 0x3214fab5, 0x9757, 0x4298, 0xbb, 0x61, 0x92, 0xa9, 0xde, 0xaa, 0x44, 0xff);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicPictures, 0xb6ebfb86, 0x6907, 0x413c, 0x9a, 0xf7, 0x4f, 0xc2, 0xab, 0xf0, 0x7c, 0xc5);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicRingtones, 0xe555ab60, 0x153b, 0x4d17, 0x9f, 0x04, 0xa5, 0xfe, 0x99, 0xfc, 0x15, 0xec);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicUserTiles, 0x0482af6c, 0x08f1, 0x4c34, 0x8c, 0x90, 0xe1, 0x7e, 0xc9, 0x8b, 0x1e, 0x17);
DEFINE_KNOWN_FOLDER (FOLDERID_PublicVideos, 0x2400183a, 0x6185, 0x49fb, 0xa2, 0xd8, 0x4a, 0x39, 0x2a, 0x60, 0x2b, 0xa3);
DEFINE_KNOWN_FOLDER (FOLDERID_QuickLaunch, 0x52a4f021, 0x7b75, 0x48a9, 0x9f, 0x6b, 0x4b, 0x87, 0xa2, 0x10, 0xbc, 0x8f);
DEFINE_KNOWN_FOLDER (FOLDERID_Recent, 0xae50c081, 0xebd2, 0x438a, 0x86, 0x55, 0x8a, 0x09, 0x2e, 0x34, 0x98, 0x7a);
DEFINE_KNOWN_FOLDER (FOLDERID_RecordedTVLibrary, 0x1a6fdba2, 0xf42d, 0x4358, 0xa7, 0x98, 0xb7, 0x4d, 0x74, 0x59, 0x26, 0xc5);
DEFINE_KNOWN_FOLDER (FOLDERID_RecycleBinFolder, 0xb7534046, 0x3ecb, 0x4c18, 0xbe, 0x4e, 0x64, 0xcd, 0x4c, 0xb7, 0xd6, 0xac);
DEFINE_KNOWN_FOLDER (FOLDERID_ResourceDir, 0x8ad10c31, 0x2adb, 0x4296, 0xa8, 0xf7, 0xe4, 0x70, 0x12, 0x32, 0xc9, 0x72);
DEFINE_KNOWN_FOLDER (FOLDERID_Ringtones, 0xc870044b, 0xf49e, 0x4126, 0xa9, 0xc3, 0xb5, 0x2a, 0x1f, 0xf4, 0x11, 0xe8);
DEFINE_KNOWN_FOLDER (FOLDERID_RoamingAppData, 0x3eb685db, 0x65f9, 0x4cf6, 0xa0, 0x3a, 0xe3, 0xef, 0x65, 0x72, 0x9f, 0x3d);
DEFINE_KNOWN_FOLDER (FOLDERID_RoamingTiles, 0xbcfc5a, 0xed94, 0x4e48, 0x96, 0xa1, 0x3f, 0x62, 0x17, 0xf2, 0x19, 0x90);
DEFINE_KNOWN_FOLDER (FOLDERID_RoamedTileImages, 0xaaa8d5a5, 0xf1d6, 0x4259, 0xba, 0xa8, 0x78, 0xe7, 0xef, 0x60, 0x83, 0x5e);
DEFINE_KNOWN_FOLDER (FOLDERID_SampleMusic, 0xb250c668, 0xf57d, 0x4ee1, 0xa6, 0x3c, 0x29, 0x0e, 0xe7, 0xd1, 0xaa, 0x1f);
DEFINE_KNOWN_FOLDER (FOLDERID_SamplePictures, 0xc4900540, 0x2379, 0x4c75, 0x84, 0x4b, 0x64, 0xe6, 0xfa, 0xf8, 0x71, 0x6b);
DEFINE_KNOWN_FOLDER (FOLDERID_SamplePlaylists, 0x15ca69b3, 0x30ee, 0x49c1, 0xac, 0xe1, 0x6b, 0x5e, 0xc3, 0x72, 0xaf, 0xb5);
DEFINE_KNOWN_FOLDER (FOLDERID_SampleVideos, 0x859ead94, 0x2e85, 0x48ad, 0xa7, 0x1a, 0x09, 0x69, 0xcb, 0x56, 0xa6, 0xcd);
DEFINE_KNOWN_FOLDER (FOLDERID_SavedGames, 0x4c5c32ff, 0xbb9d, 0x43b0, 0xb5, 0xb4, 0x2d, 0x72, 0xe5, 0x4e, 0xaa, 0xa4);
DEFINE_KNOWN_FOLDER (FOLDERID_SavedSearches, 0x7d1d3a04, 0xdebb, 0x4115, 0x95, 0xcf, 0x2f, 0x29, 0xda, 0x29, 0x20, 0xda);
DEFINE_KNOWN_FOLDER (FOLDERID_Screenshots, 0xb7bede81, 0xdf94, 0x4682, 0xa7, 0xd8, 0x57, 0xa5, 0x26, 0x20, 0xb8, 0x6f);
DEFINE_KNOWN_FOLDER (FOLDERID_SEARCH_MAPI, 0x98ec0e18, 0x2098, 0x4d44, 0x86, 0x44, 0x66, 0x97, 0x93, 0x15, 0xa2, 0x81);
DEFINE_KNOWN_FOLDER (FOLDERID_SEARCH_CSC, 0xee32e446, 0x31ca, 0x4aba, 0x81, 0x4f, 0xa5, 0xeb, 0xd2, 0xfd, 0x6d, 0x5e);
DEFINE_KNOWN_FOLDER (FOLDERID_SearchHome, 0x190337d1, 0xb8ca, 0x4121, 0xa6, 0x39, 0x6d, 0x47, 0x2d, 0x16, 0x97, 0x2a);
DEFINE_KNOWN_FOLDER (FOLDERID_SendTo, 0x8983036c, 0x27c0, 0x404b, 0x8f, 0x08, 0x10, 0x2d, 0x10, 0xdc, 0xfd, 0x74);
DEFINE_KNOWN_FOLDER (FOLDERID_SidebarDefaultParts, 0x7b396e54, 0x9ec5, 0x4300, 0xbe, 0xa, 0x24, 0x82, 0xeb, 0xae, 0x1a, 0x26);
DEFINE_KNOWN_FOLDER (FOLDERID_SidebarParts, 0xa75d362e, 0x50fc, 0x4fb7, 0xac, 0x2c, 0xa8, 0xbe, 0xaa, 0x31, 0x44, 0x93);
DEFINE_KNOWN_FOLDER (FOLDERID_StartMenu, 0x625b53c3, 0xab48, 0x4ec1, 0xba, 0x1f, 0xa1, 0xef, 0x41, 0x46, 0xfc, 0x19);
DEFINE_KNOWN_FOLDER (FOLDERID_Startup, 0xb97d20bb, 0xf46a, 0x4c97, 0xba, 0x10, 0x5e, 0x36, 0x08, 0x43, 0x08, 0x54);
DEFINE_KNOWN_FOLDER (FOLDERID_SyncManagerFolder, 0x43668bf8, 0xc14e, 0x49b2, 0x97, 0xc9, 0x74, 0x77, 0x84, 0xd7, 0x84, 0xb7);
DEFINE_KNOWN_FOLDER (FOLDERID_SyncResultsFolder, 0x289a9a43, 0xbe44, 0x4057, 0xa4, 0x1b, 0x58, 0x7a, 0x76, 0xd7, 0xe7, 0xf9);
DEFINE_KNOWN_FOLDER (FOLDERID_SyncSetupFolder, 0xf214138, 0xb1d3, 0x4a90, 0xbb, 0xa9, 0x27, 0xcb, 0xc0, 0xc5, 0x38, 0x9a);
DEFINE_KNOWN_FOLDER (FOLDERID_System, 0x1ac14e77, 0x02e7, 0x4e5d, 0xb7, 0x44, 0x2e, 0xb1, 0xae, 0x51, 0x98, 0xb7);
DEFINE_KNOWN_FOLDER (FOLDERID_SystemX86, 0xd65231b0, 0xb2f1, 0x4857, 0xa4, 0xce, 0xa8, 0xe7, 0xc6, 0xea, 0x7d, 0x27);
DEFINE_KNOWN_FOLDER (FOLDERID_Templates, 0xa63293e8, 0x664e, 0x48db, 0xa0, 0x79, 0xdf, 0x75, 0x9e, 0x05, 0x09, 0xf7);
DEFINE_KNOWN_FOLDER (FOLDERID_UserPinned, 0x9e3995ab, 0x1f9c, 0x4f13, 0xb8, 0x27, 0x48, 0xb2, 0x4b, 0x6c, 0x71, 0x74);
DEFINE_KNOWN_FOLDER (FOLDERID_UserProfiles, 0x0762d272, 0xc50a, 0x4bb0, 0xa3, 0x82, 0x69, 0x7d, 0xcd, 0x72, 0x9b, 0x80);
DEFINE_KNOWN_FOLDER (FOLDERID_UserProgramFiles, 0x5cd7aee2, 0x2219, 0x4a67, 0xb8, 0x5d, 0x6c, 0x9c, 0xe1, 0x56, 0x60, 0xcb);
DEFINE_KNOWN_FOLDER (FOLDERID_UserProgramFilesCommon, 0xbcbd3057, 0xca5c, 0x4622, 0xb4, 0x2d, 0xbc, 0x56, 0xdb, 0x0a, 0xe5, 0x16);
DEFINE_KNOWN_FOLDER (FOLDERID_UsersFiles, 0xf3ce0f7c, 0x4901, 0x4acc, 0x86, 0x48, 0xd5, 0xd4, 0x4b, 0x04, 0xef, 0x8f);
DEFINE_KNOWN_FOLDER (FOLDERID_UsersLibraries, 0xa302545d, 0xdeff, 0x464b, 0xab, 0xe8, 0x61, 0xc8, 0x64, 0x8d, 0x93, 0x9b);
DEFINE_KNOWN_FOLDER (FOLDERID_Videos, 0x18989b1d, 0x99b5, 0x455b, 0x84, 0x1c, 0xab, 0x7c, 0x74, 0xe4, 0xdd, 0xfc);
DEFINE_KNOWN_FOLDER (FOLDERID_VideosLibrary, 0x491e922f, 0x5643, 0x4af4, 0xa7, 0xeb, 0x4e, 0x7a, 0x13, 0x8d, 0x81, 0x74);
DEFINE_KNOWN_FOLDER (FOLDERID_Windows, 0xf38bf404, 0x1d43, 0x42f2, 0x93, 0x05, 0x67, 0xde, 0x0b, 0x28, 0xfc, 0x23);
#endif
