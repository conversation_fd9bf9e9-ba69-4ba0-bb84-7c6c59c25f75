/* This file is part of the KDE project
   Copyright (C) 2007 Shane King

   This program is free software; you can redistribute it and/or
   modify it under the terms of the GNU Library General Public
   License as published by the Free Software Foundation; either
   version 2 of the License, or (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   ME<PERSON><PERSON><PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
   Library General Public License for more details.

   You should have received a copy of the GNU Library General Public License
   along with this program; see the file COPYING.  If not, write to
   the Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
   Boston, MA 02110-1301, USA.
*/

import "unknwn.idl";
import "strmif.idl";

typedef DWORD MP_TIMEDATA;
typedef DWORD MP_CAPS;
typedef DWORD MP_FLAGS;
typedef float MP_DATA;

typedef enum _MP_Type {
    MPT_INT,
    MPT_FLOAT,
    MPT_BOOL,
    MPT_ENUM,
    MPT_MAX,
} MP_TYPE;

typedef enum _MP_CURVE_TYPE {
    MP_CURVE_JUMP       = 0x0001, 
    MP_CURVE_LINEAR     = 0x0002, 
    MP_CURVE_SQUARE     = 0x0004, 
    MP_CURVE_INVSQUARE  = 0x0008, 
    MP_CURVE_SINE       = 0x0010, 
} MP_CURVE_TYPE;

typedef struct _MP_PARAMINFO {
    MP_TYPE    mpType;
    MP_CAPS    mopCaps;
    MP_DATA    mpdMinValue;
    MP_DATA    mpdMaxValue;
    MP_DATA    mpdNeutralValue;
    WCHAR      szUnitText[32];
    WCHAR      szLabel[32]; 
} MP_PARAMINFO;

typedef struct _MP_ENVELOPE_SEGMENT {
    REFERENCE_TIME   rtStart;
    REFERENCE_TIME   rtEnd;
    MP_DATA          valStart;
    MP_DATA          valEnd;
    MP_CURVE_TYPE    iCurve;
    MP_FLAGS         flags;
} MP_ENVELOPE_SEGMENT;

const MP_CAPS MP_CAPS_CURVE_JUMP        = MP_CURVE_JUMP;
const MP_CAPS MP_CAPS_CURVE_LINEAR      = MP_CURVE_LINEAR;
const MP_CAPS MP_CAPS_CURVE_SQUARE      = MP_CURVE_SQUARE;
const MP_CAPS MP_CAPS_CURVE_INVSQUARE   = MP_CURVE_INVSQUARE;
const MP_CAPS MP_CAPS_CURVE_SINE        = MP_CURVE_SINE;

[
    object,
    uuid(6d6cbb61-a223-44aa-842f-a2f06750be6e)
]
interface IMediaParams : IUnknown
{
    HRESULT AddEnvelope(
        DWORD dwParamIndex,
        DWORD cPoints,
        MP_ENVELOPE_SEGMENT *pEnvelope
    );

    HRESULT FlushEnvelope(
        DWORD dwParamIndex,
        REFERENCE_TIME refTimeStart,
        REFERENCE_TIME refTimeEnd
    );

    HRESULT GetParam(
        DWORD dwParamIndex,
        MP_DATA *pValue
    );

    HRESULT SetParam(
        DWORD dwParamIndex,
        MP_DATA value
    );

    HRESULT SetTimeFormat(
        GUID guidTimeFormat,
        MP_TIMEDATA mpTimeData
    );
}

[
    object,
    uuid(6d6cbb60-a223-44aa-842f-a2f06750be6d)
]
interface IMediaParamInfo : IUnknown
{
    HRESULT GetParamCount(
        DWORD *pdwParams
    );
    
    HRESULT GetParamInfo(
        DWORD dwParamIndex,
        MP_PARAMINFO *pInfo
    );
    
    HRESULT GetParamText(
        DWORD dwParamIndex,
        WCHAR **ppwchText
    );   
   
    HRESULT GetNumTimeFormats(
        DWORD *pdwNumTimeFormats
    );
 
    HRESULT GetSupportedTimeFormat(
        DWORD dwFormatIndex,
        GUID *pguidTimeFormat
    );
    
    HRESULT GetCurrentTimeFormat(
        GUID *pguidTimeFormat,
        MP_TIMEDATA *pTimeData
    );    
}
