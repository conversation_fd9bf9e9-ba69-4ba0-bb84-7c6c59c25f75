/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _INC_SETUPAPI
#define _INC_SETUPAPI

#include <_mingw_unicode.h>

#ifndef _SETUPAPI_
#define WINSETUPAPI DECLSPEC_IMPORT
#else
#define WINSETU<PERSON>PI
#endif

#ifndef _SETUPAPI_VER
#define _SETUPAPI_VER 0x0502
#endif

#ifndef __LPGUID_DEFINED__
#define __LPGUID_DEFINED__
typedef GUID *LPGUID;
#endif

/* FIXME: #include <spapidef.h> */
#include <commctrl.h>
#include <devpropdef.h>

#ifdef _WIN64
#include <pshpack8.h>
#else
#include <pshpack1.h>
#endif

#ifdef __cplusplus
extern "C" {
#endif

#define LINE_LEN 256

#define MAX_INF_STRING_LENGTH 4096
#define MAX_INF_SECTION_NAME_LENGTH 255
#define MAX_TITLE_LEN 60
#define MAX_INSTRUCTION_LEN 256
#define MAX_LABEL_LEN 30
#define MAX_SERVICE_NAME_LEN 256
#define MAX_SUBTITLE_LEN 256

#define SP_MAX_MACHINENAME_LENGTH (MAX_PATH + 3)

  typedef PVOID HINF;

  typedef struct _INFCONTEXT {
    PVOID Inf;
    PVOID CurrentInf;
    UINT Section;
    UINT Line;
  } INFCONTEXT,*PINFCONTEXT;

  typedef struct _SP_INF_INFORMATION {
    DWORD InfStyle;
    DWORD InfCount;
    BYTE VersionData[ANYSIZE_ARRAY];
  } SP_INF_INFORMATION,*PSP_INF_INFORMATION;

  typedef struct _SP_ALTPLATFORM_INFO_V2 {
    DWORD cbSize;
    DWORD Platform;
    DWORD MajorVersion;
    DWORD MinorVersion;
    WORD ProcessorArchitecture;
    __C89_NAMELESS union {
      WORD Reserved;
      WORD Flags;
    };
    DWORD FirstValidatedMajorVersion;
    DWORD FirstValidatedMinorVersion;
  } SP_ALTPLATFORM_INFO_V2,*PSP_ALTPLATFORM_INFO_V2;

  typedef struct _SP_ALTPLATFORM_INFO_V1 {
    DWORD cbSize;
    DWORD Platform;
    DWORD MajorVersion;
    DWORD MinorVersion;
    WORD ProcessorArchitecture;
    WORD Reserved;
  } SP_ALTPLATFORM_INFO_V1,*PSP_ALTPLATFORM_INFO_V1;

#if USE_SP_ALTPLATFORM_INFO_V1

  typedef SP_ALTPLATFORM_INFO_V1 SP_ALTPLATFORM_INFO;
  typedef PSP_ALTPLATFORM_INFO_V1 PSP_ALTPLATFORM_INFO;
#else

  typedef SP_ALTPLATFORM_INFO_V2 SP_ALTPLATFORM_INFO;
  typedef PSP_ALTPLATFORM_INFO_V2 PSP_ALTPLATFORM_INFO;

#define SP_ALTPLATFORM_FLAGS_VERSION_RANGE (0x0001)
#endif

  typedef struct _SP_ORIGINAL_FILE_INFO_A {
    DWORD cbSize;
    CHAR OriginalInfName[MAX_PATH];
    CHAR OriginalCatalogName[MAX_PATH];
  } SP_ORIGINAL_FILE_INFO_A,*PSP_ORIGINAL_FILE_INFO_A;

  typedef struct _SP_ORIGINAL_FILE_INFO_W {
    DWORD cbSize;
    WCHAR OriginalInfName[MAX_PATH];
    WCHAR OriginalCatalogName[MAX_PATH];
  } SP_ORIGINAL_FILE_INFO_W,*PSP_ORIGINAL_FILE_INFO_W;

  __MINGW_TYPEDEF_UAW(SP_ORIGINAL_FILE_INFO)
  __MINGW_TYPEDEF_UAW(PSP_ORIGINAL_FILE_INFO)

#define INF_STYLE_NONE 0x00000000
#define INF_STYLE_OLDNT 0x00000001
#define INF_STYLE_WIN4 0x00000002

#define INF_STYLE_CACHE_ENABLE 0x00000010
#define INF_STYLE_CACHE_DISABLE 0x00000020
#define INF_STYLE_CACHE_IGNORE 0x00000040

#define DIRID_ABSOLUTE -1
#define DIRID_ABSOLUTE_16BIT 0xffff
#define DIRID_NULL 0
#define DIRID_SRCPATH 1
#define DIRID_WINDOWS 10
#define DIRID_SYSTEM 11
#define DIRID_DRIVERS 12
#define DIRID_IOSUBSYS DIRID_DRIVERS
#define DIRID_INF 17
#define DIRID_HELP 18
#define DIRID_FONTS 20
#define DIRID_VIEWERS 21
#define DIRID_COLOR 23
#define DIRID_APPS 24
#define DIRID_SHARED 25
#define DIRID_BOOT 30

#define DIRID_SYSTEM16 50
#define DIRID_SPOOL 51
#define DIRID_SPOOLDRIVERS 52
#define DIRID_USERPROFILE 53
#define DIRID_LOADER 54
#define DIRID_PRINTPROCESSOR 55

#define DIRID_DEFAULT DIRID_SYSTEM

#define DIRID_COMMON_STARTMENU 16406
#define DIRID_COMMON_PROGRAMS 16407
#define DIRID_COMMON_STARTUP 16408
#define DIRID_COMMON_DESKTOPDIRECTORY 16409
#define DIRID_COMMON_FAVORITES 16415
#define DIRID_COMMON_APPDATA 16419

#define DIRID_PROGRAM_FILES 16422
#define DIRID_SYSTEM_X86 16425
#define DIRID_PROGRAM_FILES_X86 16426
#define DIRID_PROGRAM_FILES_COMMON 16427
#define DIRID_PROGRAM_FILES_COMMONX86 16428

#define DIRID_COMMON_TEMPLATES 16429
#define DIRID_COMMON_DOCUMENTS 16430

#define DIRID_USER 0x8000

  typedef UINT (CALLBACK *PSP_FILE_CALLBACK_A)(PVOID Context,UINT Notification,UINT_PTR Param1,UINT_PTR Param2);
  typedef UINT (CALLBACK *PSP_FILE_CALLBACK_W)(PVOID Context,UINT Notification,UINT_PTR Param1,UINT_PTR Param2);

#define PSP_FILE_CALLBACK __MINGW_NAME_UAW(PSP_FILE_CALLBACK)

#define SPFILENOTIFY_STARTQUEUE 0x00000001
#define SPFILENOTIFY_ENDQUEUE 0x00000002
#define SPFILENOTIFY_STARTSUBQUEUE 0x00000003
#define SPFILENOTIFY_ENDSUBQUEUE 0x00000004
#define SPFILENOTIFY_STARTDELETE 0x00000005
#define SPFILENOTIFY_ENDDELETE 0x00000006
#define SPFILENOTIFY_DELETEERROR 0x00000007
#define SPFILENOTIFY_STARTRENAME 0x00000008
#define SPFILENOTIFY_ENDRENAME 0x00000009
#define SPFILENOTIFY_RENAMEERROR 0x0000000a
#define SPFILENOTIFY_STARTCOPY 0x0000000b
#define SPFILENOTIFY_ENDCOPY 0x0000000c
#define SPFILENOTIFY_COPYERROR 0x0000000d
#define SPFILENOTIFY_NEEDMEDIA 0x0000000e
#define SPFILENOTIFY_QUEUESCAN 0x0000000f

#define SPFILENOTIFY_CABINETINFO 0x00000010
#define SPFILENOTIFY_FILEINCABINET 0x00000011
#define SPFILENOTIFY_NEEDNEWCABINET 0x00000012
#define SPFILENOTIFY_FILEEXTRACTED 0x00000013
#define SPFILENOTIFY_FILEOPDELAYED 0x00000014

#define SPFILENOTIFY_STARTBACKUP 0x00000015
#define SPFILENOTIFY_BACKUPERROR 0x00000016
#define SPFILENOTIFY_ENDBACKUP 0x00000017

#define SPFILENOTIFY_QUEUESCAN_EX 0x00000018

#define SPFILENOTIFY_STARTREGISTRATION 0x00000019
#define SPFILENOTIFY_ENDREGISTRATION 0x00000020
#define SPFILENOTIFY_QUEUESCAN_SIGNERINFO 0x00000040

#define SPFILENOTIFY_LANGMISMATCH 0x00010000
#define SPFILENOTIFY_TARGETEXISTS 0x00020000
#define SPFILENOTIFY_TARGETNEWER 0x00040000

#define FILEOP_COPY 0
#define FILEOP_RENAME 1
#define FILEOP_DELETE 2
#define FILEOP_BACKUP 3

#define FILEOP_ABORT 0
#define FILEOP_DOIT 1
#define FILEOP_SKIP 2
#define FILEOP_RETRY FILEOP_DOIT
#define FILEOP_NEWPATH 4

#define COPYFLG_WARN_IF_SKIP 0x00000001
#define COPYFLG_NOSKIP 0x00000002
#define COPYFLG_NOVERSIONCHECK 0x00000004
#define COPYFLG_FORCE_FILE_IN_USE 0x00000008
#define COPYFLG_NO_OVERWRITE 0x00000010
#define COPYFLG_NO_VERSION_DIALOG 0x00000020
#define COPYFLG_OVERWRITE_OLDER_ONLY 0x00000040
#define COPYFLG_REPLACEONLY 0x00000400
#define COPYFLG_NODECOMP 0x00000800
#define COPYFLG_REPLACE_BOOT_FILE 0x00001000

#define COPYFLG_NOPRUNE 0x00002000

#define DELFLG_IN_USE 0x00000001
#define DELFLG_IN_USE1 0x00010000

  typedef struct _FILEPATHS_A {
    PCSTR Target;
    PCSTR Source;
    UINT Win32Error;
    DWORD Flags;
  } FILEPATHS_A,*PFILEPATHS_A;

  typedef struct _FILEPATHS_W {
    PCWSTR Target;
    PCWSTR Source;
    UINT Win32Error;
    DWORD Flags;
  } FILEPATHS_W,*PFILEPATHS_W;

  __MINGW_TYPEDEF_UAW(FILEPATHS)
  __MINGW_TYPEDEF_UAW(PFILEPATHS)

  typedef struct _FILEPATHS_SIGNERINFO_A {
    PCSTR Target;
    PCSTR Source;
    UINT Win32Error;
    DWORD Flags;
    PCSTR DigitalSigner;
    PCSTR Version;
    PCSTR CatalogFile;
  } FILEPATHS_SIGNERINFO_A,*PFILEPATHS_SIGNERINFO_A;

  typedef struct _FILEPATHS_SIGNERINFO_W {
    PCWSTR Target;
    PCWSTR Source;
    UINT Win32Error;
    DWORD Flags;
    PCWSTR DigitalSigner;
    PCWSTR Version;
    PCWSTR CatalogFile;
  } FILEPATHS_SIGNERINFO_W,*PFILEPATHS_SIGNERINFO_W;

  __MINGW_TYPEDEF_UAW(FILEPATHS_SIGNERINFO)
  __MINGW_TYPEDEF_UAW(PFILEPATHS_SIGNERINFO)

  typedef struct _SOURCE_MEDIA_A {
    PCSTR Reserved;
    PCSTR Tagfile;
    PCSTR Description;
    PCSTR SourcePath;
    PCSTR SourceFile;
    DWORD Flags;
  } SOURCE_MEDIA_A,*PSOURCE_MEDIA_A;

  typedef struct _SOURCE_MEDIA_W {
    PCWSTR Reserved;
    PCWSTR Tagfile;
    PCWSTR Description;
    PCWSTR SourcePath;
    PCWSTR SourceFile;
    DWORD Flags;
  } SOURCE_MEDIA_W,*PSOURCE_MEDIA_W;

  __MINGW_TYPEDEF_UAW(SOURCE_MEDIA)
  __MINGW_TYPEDEF_UAW(PSOURCE_MEDIA)

  typedef struct _CABINET_INFO_A {
    PCSTR CabinetPath;
    PCSTR CabinetFile;
    PCSTR DiskName;
    USHORT SetId;
    USHORT CabinetNumber;
  } CABINET_INFO_A,*PCABINET_INFO_A;

  typedef struct _CABINET_INFO_W {
    PCWSTR CabinetPath;
    PCWSTR CabinetFile;
    PCWSTR DiskName;
    USHORT SetId;
    USHORT CabinetNumber;
  } CABINET_INFO_W,*PCABINET_INFO_W;

  __MINGW_TYPEDEF_UAW(CABINET_INFO)
  __MINGW_TYPEDEF_UAW(PCABINET_INFO)

  typedef struct _FILE_IN_CABINET_INFO_A {
    PCSTR NameInCabinet;
    DWORD FileSize;
    DWORD Win32Error;
    WORD DosDate;
    WORD DosTime;
    WORD DosAttribs;
    CHAR FullTargetName[MAX_PATH];
  } FILE_IN_CABINET_INFO_A,*PFILE_IN_CABINET_INFO_A;

  typedef struct _FILE_IN_CABINET_INFO_W {
    PCWSTR NameInCabinet;
    DWORD FileSize;
    DWORD Win32Error;
    WORD DosDate;
    WORD DosTime;
    WORD DosAttribs;
    WCHAR FullTargetName[MAX_PATH];
  } FILE_IN_CABINET_INFO_W,*PFILE_IN_CABINET_INFO_W;

  __MINGW_TYPEDEF_UAW(FILE_IN_CABINET_INFO)
  __MINGW_TYPEDEF_UAW(PFILE_IN_CABINET_INFO)

  typedef struct _SP_REGISTER_CONTROL_STATUSA {
    DWORD cbSize;
    PCSTR FileName;
    DWORD Win32Error;
    DWORD FailureCode;
  } SP_REGISTER_CONTROL_STATUSA,*PSP_REGISTER_CONTROL_STATUSA;

  typedef struct _SP_REGISTER_CONTROL_STATUSW {
    DWORD cbSize;
    PCWSTR FileName;
    DWORD Win32Error;
    DWORD FailureCode;
  } SP_REGISTER_CONTROL_STATUSW,*PSP_REGISTER_CONTROL_STATUSW;

  __MINGW_TYPEDEF_AW(SP_REGISTER_CONTROL_STATUS)
  __MINGW_TYPEDEF_AW(PSP_REGISTER_CONTROL_STATUS)

#define SPREG_SUCCESS 0x00000000
#define SPREG_LOADLIBRARY 0x00000001
#define SPREG_GETPROCADDR 0x00000002
#define SPREG_REGSVR 0x00000003
#define SPREG_DLLINSTALL 0x00000004
#define SPREG_TIMEOUT 0x00000005
#define SPREG_UNKNOWN 0xFFFFFFFF

  typedef PVOID HSPFILEQ;

  typedef struct _SP_FILE_COPY_PARAMS_A {
    DWORD cbSize;
    HSPFILEQ QueueHandle;
    PCSTR SourceRootPath;
    PCSTR SourcePath;
    PCSTR SourceFilename;
    PCSTR SourceDescription;
    PCSTR SourceTagfile;
    PCSTR TargetDirectory;
    PCSTR TargetFilename;
    DWORD CopyStyle;
    HINF LayoutInf;
    PCSTR SecurityDescriptor;
  } SP_FILE_COPY_PARAMS_A,*PSP_FILE_COPY_PARAMS_A;

  typedef struct _SP_FILE_COPY_PARAMS_W {
    DWORD cbSize;
    HSPFILEQ QueueHandle;
    PCWSTR SourceRootPath;
    PCWSTR SourcePath;
    PCWSTR SourceFilename;
    PCWSTR SourceDescription;
    PCWSTR SourceTagfile;
    PCWSTR TargetDirectory;
    PCWSTR TargetFilename;
    DWORD CopyStyle;
    HINF LayoutInf;
    PCWSTR SecurityDescriptor;
  } SP_FILE_COPY_PARAMS_W,*PSP_FILE_COPY_PARAMS_W;

  __MINGW_TYPEDEF_UAW(SP_FILE_COPY_PARAMS)
  __MINGW_TYPEDEF_UAW(PSP_FILE_COPY_PARAMS)

  typedef PVOID HDSKSPC;
  typedef PVOID HDEVINFO;

  typedef struct _SP_DEVINFO_DATA {
    DWORD cbSize;
    GUID ClassGuid;
    DWORD DevInst;
    ULONG_PTR Reserved;
  } SP_DEVINFO_DATA,*PSP_DEVINFO_DATA;

  typedef struct _SP_DEVICE_INTERFACE_DATA {
    DWORD cbSize;
    GUID InterfaceClassGuid;
    DWORD Flags;
    ULONG_PTR Reserved;
  } SP_DEVICE_INTERFACE_DATA,*PSP_DEVICE_INTERFACE_DATA;

#define SPINT_ACTIVE 0x00000001
#define SPINT_DEFAULT 0x00000002
#define SPINT_REMOVED 0x00000004

  typedef SP_DEVICE_INTERFACE_DATA SP_INTERFACE_DEVICE_DATA;
  typedef PSP_DEVICE_INTERFACE_DATA PSP_INTERFACE_DEVICE_DATA;
#define SPID_ACTIVE SPINT_ACTIVE
#define SPID_DEFAULT SPINT_DEFAULT
#define SPID_REMOVED SPINT_REMOVED

  typedef struct _SP_DEVICE_INTERFACE_DETAIL_DATA_A {
    DWORD cbSize;
    CHAR DevicePath[ANYSIZE_ARRAY];
  } SP_DEVICE_INTERFACE_DETAIL_DATA_A,*PSP_DEVICE_INTERFACE_DETAIL_DATA_A;

  typedef struct _SP_DEVICE_INTERFACE_DETAIL_DATA_W {
    DWORD cbSize;
    WCHAR DevicePath[ANYSIZE_ARRAY];
  } SP_DEVICE_INTERFACE_DETAIL_DATA_W,*PSP_DEVICE_INTERFACE_DETAIL_DATA_W;

  __MINGW_TYPEDEF_UAW(SP_DEVICE_INTERFACE_DETAIL_DATA)
  __MINGW_TYPEDEF_UAW(PSP_DEVICE_INTERFACE_DETAIL_DATA)

  typedef SP_DEVICE_INTERFACE_DETAIL_DATA_W SP_INTERFACE_DEVICE_DETAIL_DATA_W;
  typedef PSP_DEVICE_INTERFACE_DETAIL_DATA_W PSP_INTERFACE_DEVICE_DETAIL_DATA_W;
  typedef SP_DEVICE_INTERFACE_DETAIL_DATA_A SP_INTERFACE_DEVICE_DETAIL_DATA_A;
  typedef PSP_DEVICE_INTERFACE_DETAIL_DATA_A PSP_INTERFACE_DEVICE_DETAIL_DATA_A;

  __MINGW_TYPEDEF_UAW(SP_INTERFACE_DEVICE_DETAIL_DATA)
  __MINGW_TYPEDEF_UAW(PSP_INTERFACE_DEVICE_DETAIL_DATA)

  typedef struct _SP_DEVINFO_LIST_DETAIL_DATA_A {
    DWORD cbSize;
    GUID ClassGuid;
    HANDLE RemoteMachineHandle;
    CHAR RemoteMachineName[SP_MAX_MACHINENAME_LENGTH];
  } SP_DEVINFO_LIST_DETAIL_DATA_A,*PSP_DEVINFO_LIST_DETAIL_DATA_A;

  typedef struct _SP_DEVINFO_LIST_DETAIL_DATA_W {
    DWORD cbSize;
    GUID ClassGuid;
    HANDLE RemoteMachineHandle;
    WCHAR RemoteMachineName[SP_MAX_MACHINENAME_LENGTH];
  } SP_DEVINFO_LIST_DETAIL_DATA_W,*PSP_DEVINFO_LIST_DETAIL_DATA_W;

  __MINGW_TYPEDEF_UAW(SP_DEVINFO_LIST_DETAIL_DATA)
  __MINGW_TYPEDEF_UAW(PSP_DEVINFO_LIST_DETAIL_DATA)

#define DIF_SELECTDEVICE 0x00000001
#define DIF_INSTALLDEVICE 0x00000002
#define DIF_ASSIGNRESOURCES 0x00000003
#define DIF_PROPERTIES 0x00000004
#define DIF_REMOVE 0x00000005
#define DIF_FIRSTTIMESETUP 0x00000006
#define DIF_FOUNDDEVICE 0x00000007
#define DIF_SELECTCLASSDRIVERS 0x00000008
#define DIF_VALIDATECLASSDRIVERS 0x00000009
#define DIF_INSTALLCLASSDRIVERS 0x0000000A
#define DIF_CALCDISKSPACE 0x0000000B
#define DIF_DESTROYPRIVATEDATA 0x0000000C
#define DIF_VALIDATEDRIVER 0x0000000D
#define DIF_DETECT 0x0000000F
#define DIF_INSTALLWIZARD 0x00000010
#define DIF_DESTROYWIZARDDATA 0x00000011
#define DIF_PROPERTYCHANGE 0x00000012
#define DIF_ENABLECLASS 0x00000013
#define DIF_DETECTVERIFY 0x00000014
#define DIF_INSTALLDEVICEFILES 0x00000015
#define DIF_UNREMOVE 0x00000016
#define DIF_SELECTBESTCOMPATDRV 0x00000017
#define DIF_ALLOW_INSTALL 0x00000018
#define DIF_REGISTERDEVICE 0x00000019
#define DIF_NEWDEVICEWIZARD_PRESELECT 0x0000001A
#define DIF_NEWDEVICEWIZARD_SELECT 0x0000001B
#define DIF_NEWDEVICEWIZARD_PREANALYZE 0x0000001C
#define DIF_NEWDEVICEWIZARD_POSTANALYZE 0x0000001D
#define DIF_NEWDEVICEWIZARD_FINISHINSTALL 0x0000001E
#define DIF_UNUSED1 0x0000001F
#define DIF_INSTALLINTERFACES 0x00000020
#define DIF_DETECTCANCEL 0x00000021
#define DIF_REGISTER_COINSTALLERS 0x00000022
#define DIF_ADDPROPERTYPAGE_ADVANCED 0x00000023
#define DIF_ADDPROPERTYPAGE_BASIC 0x00000024
#define DIF_RESERVED1 0x00000025
#define DIF_TROUBLESHOOTER 0x00000026
#define DIF_POWERMESSAGEWAKE 0x00000027
#define DIF_ADDREMOTEPROPERTYPAGE_ADVANCED 0x00000028
#define DIF_UPDATEDRIVER_UI 0x00000029
#define DIF_RESERVED2 0x00000030

#define DIF_MOVEDEVICE 0x0000000E

  typedef UINT DI_FUNCTION;

  typedef struct _SP_DEVINSTALL_PARAMS_A {
    DWORD cbSize;
    DWORD Flags;
    DWORD FlagsEx;
    HWND hwndParent;
    PSP_FILE_CALLBACK InstallMsgHandler;
    PVOID InstallMsgHandlerContext;
    HSPFILEQ FileQueue;
    ULONG_PTR ClassInstallReserved;
    DWORD Reserved;
    CHAR DriverPath[MAX_PATH];
  } SP_DEVINSTALL_PARAMS_A,*PSP_DEVINSTALL_PARAMS_A;

  typedef struct _SP_DEVINSTALL_PARAMS_W {
    DWORD cbSize;
    DWORD Flags;
    DWORD FlagsEx;
    HWND hwndParent;
    PSP_FILE_CALLBACK InstallMsgHandler;
    PVOID InstallMsgHandlerContext;
    HSPFILEQ FileQueue;
    ULONG_PTR ClassInstallReserved;
    DWORD Reserved;
    WCHAR DriverPath[MAX_PATH];
  } SP_DEVINSTALL_PARAMS_W,*PSP_DEVINSTALL_PARAMS_W;

  __MINGW_TYPEDEF_UAW(SP_DEVINSTALL_PARAMS)
  __MINGW_TYPEDEF_UAW(PSP_DEVINSTALL_PARAMS)

#define DI_SHOWOEM __MSABI_LONG(0x00000001)
#define DI_SHOWCOMPAT __MSABI_LONG(0x00000002)
#define DI_SHOWCLASS __MSABI_LONG(0x00000004)
#define DI_SHOWALL __MSABI_LONG(0x00000007)
#define DI_NOVCP __MSABI_LONG(0x00000008)
#define DI_DIDCOMPAT __MSABI_LONG(0x00000010)
#define DI_DIDCLASS __MSABI_LONG(0x00000020)
#define DI_AUTOASSIGNRES __MSABI_LONG(0x00000040)
#define DI_NEEDRESTART __MSABI_LONG(0x00000080)
#define DI_NEEDREBOOT __MSABI_LONG(0x00000100)
#define DI_NOBROWSE __MSABI_LONG(0x00000200)
#define DI_MULTMFGS __MSABI_LONG(0x00000400)
#define DI_DISABLED __MSABI_LONG(0x00000800)
#define DI_GENERALPAGE_ADDED __MSABI_LONG(0x00001000)
#define DI_RESOURCEPAGE_ADDED __MSABI_LONG(0x00002000)
#define DI_PROPERTIES_CHANGE __MSABI_LONG(0x00004000)
#define DI_INF_IS_SORTED __MSABI_LONG(0x00008000)
#define DI_ENUMSINGLEINF __MSABI_LONG(0x00010000)
#define DI_DONOTCALLCONFIGMG __MSABI_LONG(0x00020000)
#define DI_INSTALLDISABLED __MSABI_LONG(0x00040000)
#define DI_COMPAT_FROM_CLASS __MSABI_LONG(0x00080000)
#define DI_CLASSINSTALLPARAMS __MSABI_LONG(0x00100000)
#define DI_NODI_DEFAULTACTION __MSABI_LONG(0x00200000)
#define DI_QUIETINSTALL __MSABI_LONG(0x00800000)
#define DI_NOFILECOPY __MSABI_LONG(0x01000000)
#define DI_FORCECOPY __MSABI_LONG(0x02000000)
#define DI_DRIVERPAGE_ADDED __MSABI_LONG(0x04000000)
#define DI_USECI_SELECTSTRINGS __MSABI_LONG(0x08000000)
#define DI_OVERRIDE_INFFLAGS __MSABI_LONG(0x10000000)
#define DI_PROPS_NOCHANGEUSAGE __MSABI_LONG(0x20000000)
#define DI_NOSELECTICONS __MSABI_LONG(0x40000000)
#define DI_NOWRITE_IDS __MSABI_LONG(0x80000000)
#define DI_FLAGSEX_USEOLDINFSEARCH __MSABI_LONG(0x00000001)
#define DI_FLAGSEX_RESERVED2 __MSABI_LONG(0x00000002)
#define DI_FLAGSEX_CI_FAILED __MSABI_LONG(0x00000004)
#define DI_FLAGSEX_DIDINFOLIST __MSABI_LONG(0x00000010)
#define DI_FLAGSEX_DIDCOMPATINFO __MSABI_LONG(0x00000020)
#define DI_FLAGSEX_FILTERCLASSES __MSABI_LONG(0x00000040)
#define DI_FLAGSEX_SETFAILEDINSTALL __MSABI_LONG(0x00000080)
#define DI_FLAGSEX_DEVICECHANGE __MSABI_LONG(0x00000100)
#define DI_FLAGSEX_ALWAYSWRITEIDS __MSABI_LONG(0x00000200)
#define DI_FLAGSEX_PROPCHANGE_PENDING __MSABI_LONG(0x00000400)
#define DI_FLAGSEX_ALLOWEXCLUDEDDRVS __MSABI_LONG(0x00000800)
#define DI_FLAGSEX_NOUIONQUERYREMOVE __MSABI_LONG(0x00001000)
#define DI_FLAGSEX_USECLASSFORCOMPAT __MSABI_LONG(0x00002000)
#define DI_FLAGSEX_RESERVED3 __MSABI_LONG(0x00004000)
#define DI_FLAGSEX_NO_DRVREG_MODIFY __MSABI_LONG(0x00008000)
#define DI_FLAGSEX_IN_SYSTEM_SETUP __MSABI_LONG(0x00010000)
#define DI_FLAGSEX_INET_DRIVER __MSABI_LONG(0x00020000)
#define DI_FLAGSEX_APPENDDRIVERLIST __MSABI_LONG(0x00040000)
#define DI_FLAGSEX_PREINSTALLBACKUP __MSABI_LONG(0x00080000)
#define DI_FLAGSEX_BACKUPONREPLACE __MSABI_LONG(0x00100000)
#define DI_FLAGSEX_DRIVERLIST_FROM_URL __MSABI_LONG(0x00200000)
#define DI_FLAGSEX_RESERVED1 __MSABI_LONG(0x00400000)
#define DI_FLAGSEX_EXCLUDE_OLD_INET_DRIVERS __MSABI_LONG(0x00800000)
#define DI_FLAGSEX_POWERPAGE_ADDED __MSABI_LONG(0x01000000)
#define DI_FLAGSEX_FILTERSIMILARDRIVERS __MSABI_LONG(0x02000000)
#define DI_FLAGSEX_INSTALLEDDRIVER __MSABI_LONG(0x04000000)
#define DI_FLAGSEX_NO_CLASSLIST_NODE_MERGE __MSABI_LONG(0x08000000)
#define DI_FLAGSEX_ALTPLATFORM_DRVSEARCH __MSABI_LONG(0x10000000)
#define DI_FLAGSEX_RESTART_DEVICE_ONLY __MSABI_LONG(0x20000000)

  typedef struct _SP_CLASSINSTALL_HEADER {
    DWORD cbSize;
    DI_FUNCTION InstallFunction;
  } SP_CLASSINSTALL_HEADER,*PSP_CLASSINSTALL_HEADER;

  typedef struct _SP_ENABLECLASS_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    GUID ClassGuid;
    DWORD EnableMessage;
  } SP_ENABLECLASS_PARAMS,*PSP_ENABLECLASS_PARAMS;

#define ENABLECLASS_QUERY 0
#define ENABLECLASS_SUCCESS 1
#define ENABLECLASS_FAILURE 2

#define DICS_ENABLE 0x00000001
#define DICS_DISABLE 0x00000002
#define DICS_PROPCHANGE 0x00000003
#define DICS_START 0x00000004
#define DICS_STOP 0x00000005

#define DICS_FLAG_GLOBAL 0x00000001
#define DICS_FLAG_CONFIGSPECIFIC 0x00000002
#define DICS_FLAG_CONFIGGENERAL 0x00000004

  typedef struct _SP_PROPCHANGE_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD StateChange;
    DWORD Scope;
    DWORD HwProfile;
  } SP_PROPCHANGE_PARAMS,*PSP_PROPCHANGE_PARAMS;

  typedef struct _SP_REMOVEDEVICE_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD Scope;
    DWORD HwProfile;
  } SP_REMOVEDEVICE_PARAMS,*PSP_REMOVEDEVICE_PARAMS;

#define DI_REMOVEDEVICE_GLOBAL 0x00000001
#define DI_REMOVEDEVICE_CONFIGSPECIFIC 0x00000002

  typedef struct _SP_UNREMOVEDEVICE_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD Scope;
    DWORD HwProfile;
  } SP_UNREMOVEDEVICE_PARAMS,*PSP_UNREMOVEDEVICE_PARAMS;

#define DI_UNREMOVEDEVICE_CONFIGSPECIFIC 0x00000002

  typedef struct _SP_SELECTDEVICE_PARAMS_A {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    CHAR Title[MAX_TITLE_LEN];
    CHAR Instructions[MAX_INSTRUCTION_LEN];
    CHAR ListLabel[MAX_LABEL_LEN];
    CHAR SubTitle[MAX_SUBTITLE_LEN];
    BYTE Reserved[2];
  } SP_SELECTDEVICE_PARAMS_A,*PSP_SELECTDEVICE_PARAMS_A;

  typedef struct _SP_SELECTDEVICE_PARAMS_W {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    WCHAR Title[MAX_TITLE_LEN];
    WCHAR Instructions[MAX_INSTRUCTION_LEN];
    WCHAR ListLabel[MAX_LABEL_LEN];
    WCHAR SubTitle[MAX_SUBTITLE_LEN];
  } SP_SELECTDEVICE_PARAMS_W,*PSP_SELECTDEVICE_PARAMS_W;

  __MINGW_TYPEDEF_UAW(SP_SELECTDEVICE_PARAMS)
  __MINGW_TYPEDEF_UAW(PSP_SELECTDEVICE_PARAMS)

  typedef WINBOOL (CALLBACK *PDETECT_PROGRESS_NOTIFY)(PVOID ProgressNotifyParam,DWORD DetectComplete);

  typedef struct _SP_DETECTDEVICE_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    PDETECT_PROGRESS_NOTIFY DetectProgressNotify;
    PVOID ProgressNotifyParam;
  } SP_DETECTDEVICE_PARAMS,*PSP_DETECTDEVICE_PARAMS;

#define MAX_INSTALLWIZARD_DYNAPAGES 20

  typedef struct _SP_INSTALLWIZARD_DATA {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD Flags;
    HPROPSHEETPAGE DynamicPages[MAX_INSTALLWIZARD_DYNAPAGES];
    DWORD NumDynamicPages;
    DWORD DynamicPageFlags;
    DWORD PrivateFlags;
    LPARAM PrivateData;
    HWND hwndWizardDlg;
  } SP_INSTALLWIZARD_DATA,*PSP_INSTALLWIZARD_DATA;

#define NDW_INSTALLFLAG_DIDFACTDEFS 0x00000001
#define NDW_INSTALLFLAG_HARDWAREALLREADYIN 0x00000002
#define NDW_INSTALLFLAG_NEEDRESTART DI_NEEDRESTART
#define NDW_INSTALLFLAG_NEEDREBOOT DI_NEEDREBOOT
#define NDW_INSTALLFLAG_NEEDSHUTDOWN 0x00000200
#define NDW_INSTALLFLAG_EXPRESSINTRO 0x00000400
#define NDW_INSTALLFLAG_SKIPISDEVINSTALLED 0x00000800
#define NDW_INSTALLFLAG_NODETECTEDDEVS 0x00001000
#define NDW_INSTALLFLAG_INSTALLSPECIFIC 0x00002000
#define NDW_INSTALLFLAG_SKIPCLASSLIST 0x00004000
#define NDW_INSTALLFLAG_CI_PICKED_OEM 0x00008000
#define NDW_INSTALLFLAG_PCMCIAMODE 0x00010000
#define NDW_INSTALLFLAG_PCMCIADEVICE 0x00020000
#define NDW_INSTALLFLAG_USERCANCEL 0x00040000
#define NDW_INSTALLFLAG_KNOWNCLASS 0x00080000

#define DYNAWIZ_FLAG_PAGESADDED 0x00000001
#define DYNAWIZ_FLAG_ANALYZE_HANDLECONFLICT 0x00000008
#define DYNAWIZ_FLAG_INSTALLDET_NEXT 0x00000002
#define DYNAWIZ_FLAG_INSTALLDET_PREV 0x00000004

#define MIN_IDD_DYNAWIZ_RESOURCE_ID 10000
#define MAX_IDD_DYNAWIZ_RESOURCE_ID 11000

#define IDD_DYNAWIZ_FIRSTPAGE 10000
#define IDD_DYNAWIZ_SELECT_PREVPAGE 10001
#define IDD_DYNAWIZ_SELECT_NEXTPAGE 10002
#define IDD_DYNAWIZ_ANALYZE_PREVPAGE 10003
#define IDD_DYNAWIZ_ANALYZE_NEXTPAGE 10004
#define IDD_DYNAWIZ_SELECTDEV_PAGE 10009
#define IDD_DYNAWIZ_ANALYZEDEV_PAGE 10010
#define IDD_DYNAWIZ_INSTALLDETECTEDDEVS_PAGE 10011
#define IDD_DYNAWIZ_SELECTCLASS_PAGE 10012
#define IDD_DYNAWIZ_INSTALLDETECTED_PREVPAGE 10006
#define IDD_DYNAWIZ_INSTALLDETECTED_NEXTPAGE 10007
#define IDD_DYNAWIZ_INSTALLDETECTED_NODEVS 10008

  typedef struct _SP_NEWDEVICEWIZARD_DATA {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD Flags;
    HPROPSHEETPAGE DynamicPages[MAX_INSTALLWIZARD_DYNAPAGES];
    DWORD NumDynamicPages;
    HWND hwndWizardDlg;
  } SP_NEWDEVICEWIZARD_DATA,*PSP_NEWDEVICEWIZARD_DATA;

  typedef SP_NEWDEVICEWIZARD_DATA SP_ADDPROPERTYPAGE_DATA;
  typedef PSP_NEWDEVICEWIZARD_DATA PSP_ADDPROPERTYPAGE_DATA;

  typedef struct _SP_TROUBLESHOOTER_PARAMS_A {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    CHAR ChmFile[MAX_PATH];
    CHAR HtmlTroubleShooter[MAX_PATH];
  } SP_TROUBLESHOOTER_PARAMS_A,*PSP_TROUBLESHOOTER_PARAMS_A;

  typedef struct _SP_TROUBLESHOOTER_PARAMS_W {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    WCHAR ChmFile[MAX_PATH];
    WCHAR HtmlTroubleShooter[MAX_PATH];
  } SP_TROUBLESHOOTER_PARAMS_W,*PSP_TROUBLESHOOTER_PARAMS_W;

  __MINGW_TYPEDEF_UAW(SP_TROUBLESHOOTER_PARAMS)
  __MINGW_TYPEDEF_UAW(PSP_TROUBLESHOOTER_PARAMS)

  typedef struct _SP_POWERMESSAGEWAKE_PARAMS_A {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    CHAR PowerMessageWake[LINE_LEN*2];
  } SP_POWERMESSAGEWAKE_PARAMS_A,*PSP_POWERMESSAGEWAKE_PARAMS_A;

  typedef struct _SP_POWERMESSAGEWAKE_PARAMS_W {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    WCHAR PowerMessageWake[LINE_LEN*2];
  } SP_POWERMESSAGEWAKE_PARAMS_W,*PSP_POWERMESSAGEWAKE_PARAMS_W;

  __MINGW_TYPEDEF_UAW(SP_POWERMESSAGEWAKE_PARAMS)
  __MINGW_TYPEDEF_UAW(PSP_POWERMESSAGEWAKE_PARAMS)

  typedef struct _SP_DRVINFO_DATA_V2_A {
    DWORD cbSize;
    DWORD DriverType;
    ULONG_PTR Reserved;
    CHAR Description[LINE_LEN];
    CHAR MfgName[LINE_LEN];
    CHAR ProviderName[LINE_LEN];
    FILETIME DriverDate;
    DWORDLONG DriverVersion;
  } SP_DRVINFO_DATA_V2_A,*PSP_DRVINFO_DATA_V2_A;

  typedef struct _SP_DRVINFO_DATA_V2_W {
    DWORD cbSize;
    DWORD DriverType;
    ULONG_PTR Reserved;
    WCHAR Description[LINE_LEN];
    WCHAR MfgName[LINE_LEN];
    WCHAR ProviderName[LINE_LEN];
    FILETIME DriverDate;
    DWORDLONG DriverVersion;
  } SP_DRVINFO_DATA_V2_W,*PSP_DRVINFO_DATA_V2_W;

  typedef struct _SP_DRVINFO_DATA_V1_A {
    DWORD cbSize;
    DWORD DriverType;
    ULONG_PTR Reserved;
    CHAR Description[LINE_LEN];
    CHAR MfgName[LINE_LEN];
    CHAR ProviderName[LINE_LEN];
  } SP_DRVINFO_DATA_V1_A,*PSP_DRVINFO_DATA_V1_A;

  typedef struct _SP_DRVINFO_DATA_V1_W {
    DWORD cbSize;
    DWORD DriverType;
    ULONG_PTR Reserved;
    WCHAR Description[LINE_LEN];
    WCHAR MfgName[LINE_LEN];
    WCHAR ProviderName[LINE_LEN];
  } SP_DRVINFO_DATA_V1_W,*PSP_DRVINFO_DATA_V1_W;

  __MINGW_TYPEDEF_UAW(SP_DRVINFO_DATA_V1)
  __MINGW_TYPEDEF_UAW(PSP_DRVINFO_DATA_V1)
  __MINGW_TYPEDEF_UAW(SP_DRVINFO_DATA_V2)
  __MINGW_TYPEDEF_UAW(PSP_DRVINFO_DATA_V2)

#if USE_SP_DRVINFO_DATA_V1

  typedef SP_DRVINFO_DATA_V1_A SP_DRVINFO_DATA_A;
  typedef PSP_DRVINFO_DATA_V1_A PSP_DRVINFO_DATA_A;
  typedef SP_DRVINFO_DATA_V1_W SP_DRVINFO_DATA_W;
  typedef PSP_DRVINFO_DATA_V1_W PSP_DRVINFO_DATA_W;
  typedef SP_DRVINFO_DATA_V1 SP_DRVINFO_DATA;
  typedef PSP_DRVINFO_DATA_V1 PSP_DRVINFO_DATA;
#else

  typedef SP_DRVINFO_DATA_V2_A SP_DRVINFO_DATA_A;
  typedef PSP_DRVINFO_DATA_V2_A PSP_DRVINFO_DATA_A;
  typedef SP_DRVINFO_DATA_V2_W SP_DRVINFO_DATA_W;
  typedef PSP_DRVINFO_DATA_V2_W PSP_DRVINFO_DATA_W;
  typedef SP_DRVINFO_DATA_V2 SP_DRVINFO_DATA;
  typedef PSP_DRVINFO_DATA_V2 PSP_DRVINFO_DATA;
#endif

  typedef struct _SP_DRVINFO_DETAIL_DATA_A {
    DWORD cbSize;
    FILETIME InfDate;
    DWORD CompatIDsOffset;
    DWORD CompatIDsLength;
    ULONG_PTR Reserved;
    CHAR SectionName[LINE_LEN];
    CHAR InfFileName[MAX_PATH];
    CHAR DrvDescription[LINE_LEN];
    CHAR HardwareID[ANYSIZE_ARRAY];
  } SP_DRVINFO_DETAIL_DATA_A,*PSP_DRVINFO_DETAIL_DATA_A;

  typedef struct _SP_DRVINFO_DETAIL_DATA_W {
    DWORD cbSize;
    FILETIME InfDate;
    DWORD CompatIDsOffset;
    DWORD CompatIDsLength;
    ULONG_PTR Reserved;
    WCHAR SectionName[LINE_LEN];
    WCHAR InfFileName[MAX_PATH];
    WCHAR DrvDescription[LINE_LEN];
    WCHAR HardwareID[ANYSIZE_ARRAY];
  } SP_DRVINFO_DETAIL_DATA_W,*PSP_DRVINFO_DETAIL_DATA_W;

  __MINGW_TYPEDEF_UAW(SP_DRVINFO_DETAIL_DATA)
  __MINGW_TYPEDEF_UAW(PSP_DRVINFO_DETAIL_DATA)

  typedef struct _SP_DRVINSTALL_PARAMS {
    DWORD cbSize;
    DWORD Rank;
    DWORD Flags;
    DWORD_PTR PrivateData;
    DWORD Reserved;
  } SP_DRVINSTALL_PARAMS,*PSP_DRVINSTALL_PARAMS;

#define DNF_DUPDESC 0x00000001
#define DNF_OLDDRIVER 0x00000002
#define DNF_EXCLUDEFROMLIST 0x00000004
#define DNF_NODRIVER 0x00000008
#define DNF_LEGACYINF 0x00000010
#define DNF_CLASS_DRIVER 0x00000020
#define DNF_COMPATIBLE_DRIVER 0x00000040
#define DNF_INET_DRIVER 0x00000080
#define DNF_UNUSED1 0x00000100
#define DNF_INDEXED_DRIVER 0x00000200
#define DNF_OLD_INET_DRIVER 0x00000400
#define DNF_BAD_DRIVER 0x00000800
#define DNF_DUPPROVIDER 0x00001000
#define DNF_INF_IS_SIGNED 0x00002000
#define DNF_OEM_F6_INF 0x00004000
#define DNF_DUPDRIVERVER 0x00008000
#define DNF_BASIC_DRIVER 0x00010000
#define DNF_AUTHENTICODE_SIGNED 0x00020000

#define DRIVER_HARDWAREID_RANK 0x00000FFF
#define DRIVER_COMPATID_RANK 0x00003FFF
#define DRIVER_UNTRUSTED_RANK 0x00008000
#define DRIVER_UNTRUSTED_HARDWAREID_RANK 0x00008FFF
#define DRIVER_UNTRUSTED_COMPATID_RANK 0x0000BFFF
#define DRIVER_W9X_SUSPECT_RANK 0x0000C000
#define DRIVER_W9X_SUSPECT_HARDWAREID_RANK 0x0000CFFF
#define DRIVER_W9X_SUSPECT_COMPATID_RANK 0x0000FFFF

  typedef DWORD (CALLBACK *PSP_DETSIG_CMPPROC)(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA NewDeviceData,PSP_DEVINFO_DATA ExistingDeviceData,PVOID CompareContext);

  typedef struct _COINSTALLER_CONTEXT_DATA {
    WINBOOL PostProcessing;
    DWORD InstallResult;
    PVOID PrivateData;
  } COINSTALLER_CONTEXT_DATA,*PCOINSTALLER_CONTEXT_DATA;

  typedef struct _SP_CLASSIMAGELIST_DATA {
    DWORD cbSize;
    HIMAGELIST ImageList;
    ULONG_PTR Reserved;
  } SP_CLASSIMAGELIST_DATA,*PSP_CLASSIMAGELIST_DATA;

  typedef struct _SP_PROPSHEETPAGE_REQUEST {
    DWORD cbSize;
    DWORD PageRequested;
    HDEVINFO DeviceInfoSet;
    PSP_DEVINFO_DATA DeviceInfoData;
  } SP_PROPSHEETPAGE_REQUEST,*PSP_PROPSHEETPAGE_REQUEST;

#define SPPSR_SELECT_DEVICE_RESOURCES 1
#define SPPSR_ENUM_BASIC_DEVICE_PROPERTIES 2
#define SPPSR_ENUM_ADV_DEVICE_PROPERTIES 3

  typedef struct _SP_BACKUP_QUEUE_PARAMS_V2_A {
    DWORD cbSize;
    CHAR FullInfPath[MAX_PATH];
    INT FilenameOffset;
    CHAR ReinstallInstance[MAX_PATH];
  } SP_BACKUP_QUEUE_PARAMS_V2_A,*PSP_BACKUP_QUEUE_PARAMS_V2_A;

  typedef struct _SP_BACKUP_QUEUE_PARAMS_V2_W {
    DWORD cbSize;
    WCHAR FullInfPath[MAX_PATH];
    INT FilenameOffset;
    WCHAR ReinstallInstance[MAX_PATH];
  } SP_BACKUP_QUEUE_PARAMS_V2_W,*PSP_BACKUP_QUEUE_PARAMS_V2_W;

  typedef struct _SP_BACKUP_QUEUE_PARAMS_V1_A {
    DWORD cbSize;
    CHAR FullInfPath[MAX_PATH];
    INT FilenameOffset;
  } SP_BACKUP_QUEUE_PARAMS_V1_A,*PSP_BACKUP_QUEUE_PARAMS_V1_A;

  typedef struct _SP_BACKUP_QUEUE_PARAMS_V1_W {
    DWORD cbSize;
    WCHAR FullInfPath[MAX_PATH];
    INT FilenameOffset;
  } SP_BACKUP_QUEUE_PARAMS_V1_W,*PSP_BACKUP_QUEUE_PARAMS_V1_W;

  __MINGW_TYPEDEF_UAW(SP_BACKUP_QUEUE_PARAMS_V1)
  __MINGW_TYPEDEF_UAW(PSP_BACKUP_QUEUE_PARAMS_V1)
  __MINGW_TYPEDEF_UAW(SP_BACKUP_QUEUE_PARAMS_V2)
  __MINGW_TYPEDEF_UAW(PSP_BACKUP_QUEUE_PARAMS_V2)

#if USE_SP_BACKUP_QUEUE_PARAMS_V1

  typedef SP_BACKUP_QUEUE_PARAMS_V1_A SP_BACKUP_QUEUE_PARAMS_A;
  typedef PSP_BACKUP_QUEUE_PARAMS_V1_A PSP_BACKUP_QUEUE_PARAMS_A;
  typedef SP_BACKUP_QUEUE_PARAMS_V1_W SP_BACKUP_QUEUE_PARAMS_W;
  typedef PSP_BACKUP_QUEUE_PARAMS_V1_W PSP_BACKUP_QUEUE_PARAMS_W;
  typedef SP_BACKUP_QUEUE_PARAMS_V1 SP_BACKUP_QUEUE_PARAMS;
  typedef PSP_BACKUP_QUEUE_PARAMS_V1 PSP_BACKUP_QUEUE_PARAMS;
#else

  typedef SP_BACKUP_QUEUE_PARAMS_V2_A SP_BACKUP_QUEUE_PARAMS_A;
  typedef PSP_BACKUP_QUEUE_PARAMS_V2_A PSP_BACKUP_QUEUE_PARAMS_A;
  typedef SP_BACKUP_QUEUE_PARAMS_V2_W SP_BACKUP_QUEUE_PARAMS_W;
  typedef PSP_BACKUP_QUEUE_PARAMS_V2_W PSP_BACKUP_QUEUE_PARAMS_W;
  typedef SP_BACKUP_QUEUE_PARAMS_V2 SP_BACKUP_QUEUE_PARAMS;
  typedef PSP_BACKUP_QUEUE_PARAMS_V2 PSP_BACKUP_QUEUE_PARAMS;
#endif

#define ERROR_EXPECTED_SECTION_NAME (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0)
#define ERROR_BAD_SECTION_NAME_LINE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|1)
#define ERROR_SECTION_NAME_TOO_LONG (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|2)
#define ERROR_GENERAL_SYNTAX (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|3)
#define ERROR_WRONG_INF_STYLE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x100)
#define ERROR_SECTION_NOT_FOUND (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x101)
#define ERROR_LINE_NOT_FOUND (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x102)
#define ERROR_NO_BACKUP (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x103)
#define ERROR_NO_ASSOCIATED_CLASS (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x200)
#define ERROR_CLASS_MISMATCH (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x201)
#define ERROR_DUPLICATE_FOUND (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x202)
#define ERROR_NO_DRIVER_SELECTED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x203)
#define ERROR_KEY_DOES_NOT_EXIST (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x204)
#define ERROR_INVALID_DEVINST_NAME (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x205)
#define ERROR_INVALID_CLASS (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x206)
#define ERROR_DEVINST_ALREADY_EXISTS (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x207)
#define ERROR_DEVINFO_NOT_REGISTERED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x208)
#define ERROR_INVALID_REG_PROPERTY (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x209)
#define ERROR_NO_INF (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20A)
#define ERROR_NO_SUCH_DEVINST (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20B)
#define ERROR_CANT_LOAD_CLASS_ICON (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20C)
#define ERROR_INVALID_CLASS_INSTALLER (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20D)
#define ERROR_DI_DO_DEFAULT (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20E)
#define ERROR_DI_NOFILECOPY (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20F)
#define ERROR_INVALID_HWPROFILE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x210)
#define ERROR_NO_DEVICE_SELECTED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x211)
#define ERROR_DEVINFO_LIST_LOCKED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x212)
#define ERROR_DEVINFO_DATA_LOCKED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x213)
#define ERROR_DI_BAD_PATH (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x214)
#define ERROR_NO_CLASSINSTALL_PARAMS (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x215)
#define ERROR_FILEQUEUE_LOCKED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x216)
#define ERROR_BAD_SERVICE_INSTALLSECT (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x217)
#define ERROR_NO_CLASS_DRIVER_LIST (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x218)
#define ERROR_NO_ASSOCIATED_SERVICE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x219)
#define ERROR_NO_DEFAULT_DEVICE_INTERFACE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21A)
#define ERROR_DEVICE_INTERFACE_ACTIVE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21B)
#define ERROR_DEVICE_INTERFACE_REMOVED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21C)
#define ERROR_BAD_INTERFACE_INSTALLSECT (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21D)
#define ERROR_NO_SUCH_INTERFACE_CLASS (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21E)
#define ERROR_INVALID_REFERENCE_STRING (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21F)
#define ERROR_INVALID_MACHINENAME (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x220)
#define ERROR_REMOTE_COMM_FAILURE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x221)
#define ERROR_MACHINE_UNAVAILABLE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x222)
#define ERROR_NO_CONFIGMGR_SERVICES (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x223)
#define ERROR_INVALID_PROPPAGE_PROVIDER (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x224)
#define ERROR_NO_SUCH_DEVICE_INTERFACE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x225)
#define ERROR_DI_POSTPROCESSING_REQUIRED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x226)
#define ERROR_INVALID_COINSTALLER (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x227)
#define ERROR_NO_COMPAT_DRIVERS (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x228)
#define ERROR_NO_DEVICE_ICON (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x229)
#define ERROR_INVALID_INF_LOGCONFIG (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22A)
#define ERROR_DI_DONT_INSTALL (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22B)
#define ERROR_INVALID_FILTER_DRIVER (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22C)
#define ERROR_NON_WINDOWS_NT_DRIVER (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22D)
#define ERROR_NON_WINDOWS_DRIVER (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22E)
#define ERROR_NO_CATALOG_FOR_OEM_INF (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22F)
#define ERROR_DEVINSTALL_QUEUE_NONNATIVE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x230)
#define ERROR_NOT_DISABLEABLE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x231)
#define ERROR_CANT_REMOVE_DEVINST (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x232)
#define ERROR_INVALID_TARGET (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x233)
#define ERROR_DRIVER_NONNATIVE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x234)
#define ERROR_IN_WOW64 (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x235)
#define ERROR_SET_SYSTEM_RESTORE_POINT (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x236)
#define ERROR_INCORRECTLY_COPIED_INF (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x237)
#define ERROR_SCE_DISABLED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x238)
#define ERROR_UNKNOWN_EXCEPTION (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x239)
#define ERROR_PNP_REGISTRY_ERROR (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x23A)
#define ERROR_REMOTE_REQUEST_UNSUPPORTED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x23B)
#define ERROR_NOT_AN_INSTALLED_OEM_INF (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x23C)
#define ERROR_INF_IN_USE_BY_DEVICES (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x23D)
#define ERROR_DI_FUNCTION_OBSOLETE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x23E)
#define ERROR_NO_AUTHENTICODE_CATALOG (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x23F)
#define ERROR_AUTHENTICODE_DISALLOWED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x240)
#define ERROR_AUTHENTICODE_TRUSTED_PUBLISHER (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x241)
#define ERROR_AUTHENTICODE_TRUST_NOT_ESTABLISHED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x242)
#define ERROR_AUTHENTICODE_PUBLISHER_NOT_TRUSTED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x243)
#define ERROR_SIGNATURE_OSATTRIBUTE_MISMATCH (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x244)
#define ERROR_ONLY_VALIDATE_VIA_AUTHENTICODE (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x245)
#define ERROR_UNRECOVERABLE_STACK_OVERFLOW (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x300)
#define EXCEPTION_SPAPI_UNRECOVERABLE_STACK_OVERFLOW ERROR_UNRECOVERABLE_STACK_OVERFLOW
#define ERROR_NO_DEFAULT_INTERFACE_DEVICE ERROR_NO_DEFAULT_DEVICE_INTERFACE
#define ERROR_INTERFACE_DEVICE_ACTIVE ERROR_DEVICE_INTERFACE_ACTIVE
#define ERROR_INTERFACE_DEVICE_REMOVED ERROR_DEVICE_INTERFACE_REMOVED
#define ERROR_NO_SUCH_INTERFACE_DEVICE ERROR_NO_SUCH_DEVICE_INTERFACE
#define ERROR_NOT_INSTALLED (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x1000)

  WINSETUPAPI WINBOOL WINAPI SetupGetInfInformationA(LPCVOID InfSpec,DWORD SearchControl,PSP_INF_INFORMATION ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetInfInformationW(LPCVOID InfSpec,DWORD SearchControl,PSP_INF_INFORMATION ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);

#define INFINFO_INF_SPEC_IS_HINF 1
#define INFINFO_INF_NAME_IS_ABSOLUTE 2
#define INFINFO_DEFAULT_SEARCH 3
#define INFINFO_REVERSE_DEFAULT_SEARCH 4
#define INFINFO_INF_PATH_LIST_SEARCH 5

#define SetupGetInfInformation __MINGW_NAME_AW(SetupGetInfInformation)
#define SetupQueryInfFileInformation __MINGW_NAME_AW(SetupQueryInfFileInformation)
#define SetupQueryInfOriginalFileInformation __MINGW_NAME_AW(SetupQueryInfOriginalFileInformation)
#define SetupQueryInfVersionInformation __MINGW_NAME_AW(SetupQueryInfVersionInformation)
#define SetupGetInfFileList __MINGW_NAME_AW(SetupGetInfFileList)
#define SetupOpenInfFile __MINGW_NAME_AW(SetupOpenInfFile)
#define SetupOpenAppendInfFile __MINGW_NAME_AW(SetupOpenAppendInfFile)
#define SetupFindFirstLine __MINGW_NAME_AW(SetupFindFirstLine)
#define SetupFindNextMatchLine __MINGW_NAME_AW(SetupFindNextMatchLine)
#define SetupGetLineByIndex __MINGW_NAME_AW(SetupGetLineByIndex)
#define SetupGetLineCount __MINGW_NAME_AW(SetupGetLineCount)
#define SetupGetLineText __MINGW_NAME_AW(SetupGetLineText)
#define SetupGetStringField __MINGW_NAME_AW(SetupGetStringField)
#define SetupGetMultiSzField __MINGW_NAME_AW(SetupGetMultiSzField)
#define SetupGetFileCompressionInfo __MINGW_NAME_AW(SetupGetFileCompressionInfo)
#define SetupGetFileCompressionInfoEx __MINGW_NAME_AW(SetupGetFileCompressionInfoEx)
#define SetupDecompressOrCopyFile __MINGW_NAME_AW(SetupDecompressOrCopyFile)
#define SetupGetSourceFileLocation __MINGW_NAME_AW(SetupGetSourceFileLocation)
#define SetupGetSourceFileSize __MINGW_NAME_AW(SetupGetSourceFileSize)
#define SetupGetTargetPath __MINGW_NAME_AW(SetupGetTargetPath)
#define SetupSetSourceList __MINGW_NAME_AW(SetupSetSourceList)
#define SetupAddToSourceList __MINGW_NAME_AW(SetupAddToSourceList)
#define SetupRemoveFromSourceList __MINGW_NAME_AW(SetupRemoveFromSourceList)
#define SetupQuerySourceList __MINGW_NAME_AW(SetupQuerySourceList)
#define SetupFreeSourceList __MINGW_NAME_AW(SetupFreeSourceList)
#define SetupPromptForDisk __MINGW_NAME_AW(SetupPromptForDisk)
#define SetupCopyError __MINGW_NAME_AW(SetupCopyError)
#define SetupRenameError __MINGW_NAME_AW(SetupRenameError)
#define SetupDeleteError __MINGW_NAME_AW(SetupDeleteError)
#define SetupBackupError __MINGW_NAME_AW(SetupBackupError)

#define SRCLIST_TEMPORARY 0x00000001
#define SRCLIST_NOBROWSE 0x00000002
#define SRCLIST_SYSTEM 0x00000010
#define SRCLIST_USER 0x00000020
#define SRCLIST_SYSIFADMIN 0x00000040
#define SRCLIST_SUBDIRS 0x00000100
#define SRCLIST_APPEND 0x00000200
#define SRCLIST_NOSTRIPPLATFORM 0x00000400

#define FILE_COMPRESSION_NONE 0
#define FILE_COMPRESSION_WINLZA 1
#define FILE_COMPRESSION_MSZIP 2
#define FILE_COMPRESSION_NTCAB 3

  WINSETUPAPI WINBOOL WINAPI SetupQueryInfFileInformationA(PSP_INF_INFORMATION InfInformation,UINT InfIndex,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupQueryInfFileInformationW(PSP_INF_INFORMATION InfInformation,UINT InfIndex,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupQueryInfOriginalFileInformationA(PSP_INF_INFORMATION InfInformation,UINT InfIndex,PSP_ALTPLATFORM_INFO AlternatePlatformInfo,PSP_ORIGINAL_FILE_INFO_A OriginalFileInfo);
  WINSETUPAPI WINBOOL WINAPI SetupQueryInfOriginalFileInformationW(PSP_INF_INFORMATION InfInformation,UINT InfIndex,PSP_ALTPLATFORM_INFO AlternatePlatformInfo,PSP_ORIGINAL_FILE_INFO_W OriginalFileInfo);
  WINSETUPAPI WINBOOL WINAPI SetupQueryInfVersionInformationA(PSP_INF_INFORMATION InfInformation,UINT InfIndex,PCSTR Key,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupQueryInfVersionInformationW(PSP_INF_INFORMATION InfInformation,UINT InfIndex,PCWSTR Key,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetInfFileListA(PCSTR DirectoryPath,DWORD InfStyle,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetInfFileListW(PCWSTR DirectoryPath,DWORD InfStyle,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI HINF WINAPI SetupOpenInfFileW(PCWSTR FileName,PCWSTR InfClass,DWORD InfStyle,PUINT ErrorLine);
  WINSETUPAPI HINF WINAPI SetupOpenInfFileA(PCSTR FileName,PCSTR InfClass,DWORD InfStyle,PUINT ErrorLine);
  WINSETUPAPI HINF WINAPI SetupOpenMasterInf(VOID);
  WINSETUPAPI WINBOOL WINAPI SetupOpenAppendInfFileW(PCWSTR FileName,HINF InfHandle,PUINT ErrorLine);
  WINSETUPAPI WINBOOL WINAPI SetupOpenAppendInfFileA(PCSTR FileName,HINF InfHandle,PUINT ErrorLine);
  WINSETUPAPI VOID WINAPI SetupCloseInfFile(HINF InfHandle);
  WINSETUPAPI WINBOOL WINAPI SetupFindFirstLineA(HINF InfHandle,PCSTR Section,PCSTR Key,PINFCONTEXT Context);
  WINSETUPAPI WINBOOL WINAPI SetupFindFirstLineW(HINF InfHandle,PCWSTR Section,PCWSTR Key,PINFCONTEXT Context);
  WINSETUPAPI WINBOOL WINAPI SetupFindNextLine(PINFCONTEXT ContextIn,PINFCONTEXT ContextOut);
  WINSETUPAPI WINBOOL WINAPI SetupFindNextMatchLineA(PINFCONTEXT ContextIn,PCSTR Key,PINFCONTEXT ContextOut);
  WINSETUPAPI WINBOOL WINAPI SetupFindNextMatchLineW(PINFCONTEXT ContextIn,PCWSTR Key,PINFCONTEXT ContextOut);
  WINSETUPAPI WINBOOL WINAPI SetupGetLineByIndexA(HINF InfHandle,PCSTR Section,DWORD Index,PINFCONTEXT Context);
  WINSETUPAPI WINBOOL WINAPI SetupGetLineByIndexW(HINF InfHandle,PCWSTR Section,DWORD Index,PINFCONTEXT Context);
  WINSETUPAPI LONG WINAPI SetupGetLineCountA(HINF InfHandle,PCSTR Section);
  WINSETUPAPI LONG WINAPI SetupGetLineCountW(HINF InfHandle,PCWSTR Section);
  WINSETUPAPI WINBOOL WINAPI SetupGetLineTextA(PINFCONTEXT Context,HINF InfHandle,PCSTR Section,PCSTR Key,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetLineTextW(PINFCONTEXT Context,HINF InfHandle,PCWSTR Section,PCWSTR Key,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI DWORD WINAPI SetupGetFieldCount(PINFCONTEXT Context);
  WINSETUPAPI WINBOOL WINAPI SetupGetStringFieldA(PINFCONTEXT Context,DWORD FieldIndex,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetStringFieldW(PINFCONTEXT Context,DWORD FieldIndex,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetIntField(PINFCONTEXT Context,DWORD FieldIndex,PINT IntegerValue);
  WINSETUPAPI WINBOOL WINAPI SetupGetMultiSzFieldA(PINFCONTEXT Context,DWORD FieldIndex,PSTR ReturnBuffer,DWORD ReturnBufferSize,LPDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetMultiSzFieldW(PINFCONTEXT Context,DWORD FieldIndex,PWSTR ReturnBuffer,DWORD ReturnBufferSize,LPDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetBinaryField(PINFCONTEXT Context,DWORD FieldIndex,PBYTE ReturnBuffer,DWORD ReturnBufferSize,LPDWORD RequiredSize);
  WINSETUPAPI DWORD WINAPI SetupGetFileCompressionInfoA(PCSTR SourceFileName,PSTR *ActualSourceFileName,PDWORD SourceFileSize,PDWORD TargetFileSize,PUINT CompressionType);
  WINSETUPAPI DWORD WINAPI SetupGetFileCompressionInfoW(PCWSTR SourceFileName,PWSTR *ActualSourceFileName,PDWORD SourceFileSize,PDWORD TargetFileSize,PUINT CompressionType);
  WINSETUPAPI WINBOOL WINAPI SetupGetFileCompressionInfoExA(PCSTR SourceFileName,PSTR ActualSourceFileNameBuffer,DWORD ActualSourceFileNameBufferLen,PDWORD RequiredBufferLen,PDWORD SourceFileSize,PDWORD TargetFileSize,PUINT CompressionType);
  WINSETUPAPI WINBOOL WINAPI SetupGetFileCompressionInfoExW(PCWSTR SourceFileName,PWSTR ActualSourceFileNameBuffer,DWORD ActualSourceFileNameBufferLen,PDWORD RequiredBufferLen,PDWORD SourceFileSize,PDWORD TargetFileSize,PUINT CompressionType);
  WINSETUPAPI DWORD WINAPI SetupDecompressOrCopyFileA(PCSTR SourceFileName,PCSTR TargetFileName,PUINT CompressionType);
  WINSETUPAPI DWORD WINAPI SetupDecompressOrCopyFileW(PCWSTR SourceFileName,PCWSTR TargetFileName,PUINT CompressionType);
  WINSETUPAPI WINBOOL WINAPI SetupGetSourceFileLocationA(HINF InfHandle,PINFCONTEXT InfContext,PCSTR FileName,PUINT SourceId,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetSourceFileLocationW(HINF InfHandle,PINFCONTEXT InfContext,PCWSTR FileName,PUINT SourceId,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetSourceFileSizeA(HINF InfHandle,PINFCONTEXT InfContext,PCSTR FileName,PCSTR Section,PDWORD FileSize,UINT RoundingFactor);
  WINSETUPAPI WINBOOL WINAPI SetupGetSourceFileSizeW(HINF InfHandle,PINFCONTEXT InfContext,PCWSTR FileName,PCWSTR Section,PDWORD FileSize,UINT RoundingFactor);
  WINSETUPAPI WINBOOL WINAPI SetupGetTargetPathA(HINF InfHandle,PINFCONTEXT InfContext,PCSTR Section,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetTargetPathW(HINF InfHandle,PINFCONTEXT InfContext,PCWSTR Section,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupSetSourceListA(DWORD Flags,PCSTR *SourceList,UINT SourceCount);
  WINSETUPAPI WINBOOL WINAPI SetupSetSourceListW(DWORD Flags,PCWSTR *SourceList,UINT SourceCount);
  WINSETUPAPI WINBOOL WINAPI SetupCancelTemporarySourceList(VOID);
  WINSETUPAPI WINBOOL WINAPI SetupAddToSourceListA(DWORD Flags,PCSTR Source);
  WINSETUPAPI WINBOOL WINAPI SetupAddToSourceListW(DWORD Flags,PCWSTR Source);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveFromSourceListA(DWORD Flags,PCSTR Source);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveFromSourceListW(DWORD Flags,PCWSTR Source);
  WINSETUPAPI WINBOOL WINAPI SetupQuerySourceListA(DWORD Flags,PCSTR **List,PUINT Count);
  WINSETUPAPI WINBOOL WINAPI SetupQuerySourceListW(DWORD Flags,PCWSTR **List,PUINT Count);
  WINSETUPAPI WINBOOL WINAPI SetupFreeSourceListA(PCSTR **List,UINT Count);
  WINSETUPAPI WINBOOL WINAPI SetupFreeSourceListW(PCWSTR **List,UINT Count);
  WINSETUPAPI UINT WINAPI SetupPromptForDiskA(HWND hwndParent,PCSTR DialogTitle,PCSTR DiskName,PCSTR PathToSource,PCSTR FileSought,PCSTR TagFile,DWORD DiskPromptStyle,PSTR PathBuffer,DWORD PathBufferSize,PDWORD PathRequiredSize);
  WINSETUPAPI UINT WINAPI SetupPromptForDiskW(HWND hwndParent,PCWSTR DialogTitle,PCWSTR DiskName,PCWSTR PathToSource,PCWSTR FileSought,PCWSTR TagFile,DWORD DiskPromptStyle,PWSTR PathBuffer,DWORD PathBufferSize,PDWORD PathRequiredSize);
  WINSETUPAPI UINT WINAPI SetupCopyErrorA(HWND hwndParent,PCSTR DialogTitle,PCSTR DiskName,PCSTR PathToSource,PCSTR SourceFile,PCSTR TargetPathFile,UINT Win32ErrorCode,DWORD Style,PSTR PathBuffer,DWORD PathBufferSize,PDWORD PathRequiredSize);
  WINSETUPAPI UINT WINAPI SetupCopyErrorW(HWND hwndParent,PCWSTR DialogTitle,PCWSTR DiskName,PCWSTR PathToSource,PCWSTR SourceFile,PCWSTR TargetPathFile,UINT Win32ErrorCode,DWORD Style,PWSTR PathBuffer,DWORD PathBufferSize,PDWORD PathRequiredSize);
  WINSETUPAPI UINT WINAPI SetupRenameErrorA(HWND hwndParent,PCSTR DialogTitle,PCSTR SourceFile,PCSTR TargetFile,UINT Win32ErrorCode,DWORD Style);
  WINSETUPAPI UINT WINAPI SetupRenameErrorW(HWND hwndParent,PCWSTR DialogTitle,PCWSTR SourceFile,PCWSTR TargetFile,UINT Win32ErrorCode,DWORD Style);
  WINSETUPAPI UINT WINAPI SetupDeleteErrorA(HWND hwndParent,PCSTR DialogTitle,PCSTR File,UINT Win32ErrorCode,DWORD Style);
  WINSETUPAPI UINT WINAPI SetupDeleteErrorW(HWND hwndParent,PCWSTR DialogTitle,PCWSTR File,UINT Win32ErrorCode,DWORD Style);
  WINSETUPAPI UINT WINAPI SetupBackupErrorA(HWND hwndParent,PCSTR DialogTitle,PCSTR SourceFile,PCSTR TargetFile,UINT Win32ErrorCode,DWORD Style);
  WINSETUPAPI UINT WINAPI SetupBackupErrorW(HWND hwndParent,PCWSTR DialogTitle,PCWSTR SourceFile,PCWSTR TargetFile,UINT Win32ErrorCode,DWORD Style);

#define IDF_NOBROWSE 0x00000001
#define IDF_NOSKIP 0x00000002
#define IDF_NODETAILS 0x00000004
#define IDF_NOCOMPRESSED 0x00000008
#define IDF_CHECKFIRST 0x00000100
#define IDF_NOBEEP 0x00000200
#define IDF_NOFOREGROUND 0x00000400
#define IDF_WARNIFSKIP 0x00000800

#define IDF_NOREMOVABLEMEDIAPROMPT 0x00001000
#define IDF_USEDISKNAMEASPROMPT 0x00002000
#define IDF_OEMDISK 0x80000000

#define DPROMPT_SUCCESS 0
#define DPROMPT_CANCEL 1
#define DPROMPT_SKIPFILE 2
#define DPROMPT_BUFFERTOOSMALL 3
#define DPROMPT_OUTOFMEMORY 4

#define SETDIRID_NOT_FULL_PATH 0x00000001

#define SRCINFO_PATH 1
#define SRCINFO_TAGFILE 2
#define SRCINFO_DESCRIPTION 3
#define SRCINFO_FLAGS 4
#define SRCINFO_TAGFILE2 5

#define SRC_FLAGS_CABFILE (0x0010)

#define SetupSetDirectoryId __MINGW_NAME_AW(SetupSetDirectoryId)
#define SetupSetDirectoryIdEx __MINGW_NAME_AW(SetupSetDirectoryIdEx)
#define SetupGetSourceInfo __MINGW_NAME_AW(SetupGetSourceInfo)
#define SetupInstallFile __MINGW_NAME_AW(SetupInstallFile)
#define SetupInstallFileEx __MINGW_NAME_AW(SetupInstallFileEx)

  WINSETUPAPI WINBOOL WINAPI SetupSetDirectoryIdA(HINF InfHandle,DWORD Id,PCSTR Directory);
  WINSETUPAPI WINBOOL WINAPI SetupSetDirectoryIdW(HINF InfHandle,DWORD Id,PCWSTR Directory);
  WINSETUPAPI WINBOOL WINAPI SetupSetDirectoryIdExA(HINF InfHandle,DWORD Id,PCSTR Directory,DWORD Flags,DWORD Reserved1,PVOID Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupSetDirectoryIdExW(HINF InfHandle,DWORD Id,PCWSTR Directory,DWORD Flags,DWORD Reserved1,PVOID Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupGetSourceInfoA(HINF InfHandle,UINT SourceId,UINT InfoDesired,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupGetSourceInfoW(HINF InfHandle,UINT SourceId,UINT InfoDesired,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupInstallFileA(HINF InfHandle,PINFCONTEXT InfContext,PCSTR SourceFile,PCSTR SourcePathRoot,PCSTR DestinationName,DWORD CopyStyle,PSP_FILE_CALLBACK_A CopyMsgHandler,PVOID Context);
  WINSETUPAPI WINBOOL WINAPI SetupInstallFileW(HINF InfHandle,PINFCONTEXT InfContext,PCWSTR SourceFile,PCWSTR SourcePathRoot,PCWSTR DestinationName,DWORD CopyStyle,PSP_FILE_CALLBACK_W CopyMsgHandler,PVOID Context);
  WINSETUPAPI WINBOOL WINAPI SetupInstallFileExA(HINF InfHandle,PINFCONTEXT InfContext,PCSTR SourceFile,PCSTR SourcePathRoot,PCSTR DestinationName,DWORD CopyStyle,PSP_FILE_CALLBACK_A CopyMsgHandler,PVOID Context,PBOOL FileWasInUse);
  WINSETUPAPI WINBOOL WINAPI SetupInstallFileExW(HINF InfHandle,PINFCONTEXT InfContext,PCWSTR SourceFile,PCWSTR SourcePathRoot,PCWSTR DestinationName,DWORD CopyStyle,PSP_FILE_CALLBACK_W CopyMsgHandler,PVOID Context,PBOOL FileWasInUse);

#define SP_COPY_DELETESOURCE 0x0000001
#define SP_COPY_REPLACEONLY 0x0000002
#define SP_COPY_NEWER 0x0000004
#define SP_COPY_NEWER_OR_SAME SP_COPY_NEWER
#define SP_COPY_NOOVERWRITE 0x0000008
#define SP_COPY_NODECOMP 0x0000010
#define SP_COPY_LANGUAGEAWARE 0x0000020
#define SP_COPY_SOURCE_ABSOLUTE 0x0000040
#define SP_COPY_SOURCEPATH_ABSOLUTE 0x0000080
#define SP_COPY_IN_USE_NEEDS_REBOOT 0x0000100
#define SP_COPY_FORCE_IN_USE 0x0000200
#define SP_COPY_NOSKIP 0x0000400
#define SP_FLAG_CABINETCONTINUATION 0x0000800
#define SP_COPY_FORCE_NOOVERWRITE 0x0001000
#define SP_COPY_FORCE_NEWER 0x0002000
#define SP_COPY_WARNIFSKIP 0x0004000
#define SP_COPY_NOBROWSE 0x0008000
#define SP_COPY_NEWER_ONLY 0x0010000
#define SP_COPY_SOURCE_SIS_MASTER 0x0020000
#define SP_COPY_OEMINF_CATALOG_ONLY 0x0040000
#define SP_COPY_REPLACE_BOOT_FILE 0x0080000
#define SP_COPY_NOPRUNE 0x0100000
#define SP_COPY_OEM_F6_INF 0x0200000

#define SP_BACKUP_BACKUPPASS 0x00000001
#define SP_BACKUP_DEMANDPASS 0x00000002
#define SP_BACKUP_SPECIAL 0x00000004
#define SP_BACKUP_BOOTFILE 0x00000008

#define SetupSetFileQueueAlternatePlatform __MINGW_NAME_AW(SetupSetFileQueueAlternatePlatform)
#define SetupQueueDeleteSection __MINGW_NAME_AW(SetupQueueDeleteSection)
#define SetupQueueRename __MINGW_NAME_AW(SetupQueueRename)
#define SetupQueueRenameSection __MINGW_NAME_AW(SetupQueueRenameSection)
#define SetupCommitFileQueue __MINGW_NAME_AW(SetupCommitFileQueue)
#define SetupScanFileQueue __MINGW_NAME_AW(SetupScanFileQueue)
#define SetupSetPlatformPathOverride __MINGW_NAME_AW(SetupSetPlatformPathOverride)
#define SetupQueueCopy __MINGW_NAME_AW(SetupQueueCopy)
#define SetupQueueCopyIndirect __MINGW_NAME_AW(SetupQueueCopyIndirect)
#define SetupQueueDefaultCopy __MINGW_NAME_AW(SetupQueueDefaultCopy)
#define SetupQueueCopySection __MINGW_NAME_AW(SetupQueueCopySection)
#define SetupQueueDelete __MINGW_NAME_AW(SetupQueueDelete)

  WINSETUPAPI HSPFILEQ WINAPI SetupOpenFileQueue(VOID);
  WINSETUPAPI WINBOOL WINAPI SetupCloseFileQueue(HSPFILEQ QueueHandle);
  WINSETUPAPI WINBOOL WINAPI SetupSetFileQueueAlternatePlatformA(HSPFILEQ QueueHandle,PSP_ALTPLATFORM_INFO AlternatePlatformInfo,PCSTR AlternateDefaultCatalogFile);
  WINSETUPAPI WINBOOL WINAPI SetupSetFileQueueAlternatePlatformW(HSPFILEQ QueueHandle,PSP_ALTPLATFORM_INFO AlternatePlatformInfo,PCWSTR AlternateDefaultCatalogFile);
  WINSETUPAPI WINBOOL WINAPI SetupSetPlatformPathOverrideA(PCSTR Override);
  WINSETUPAPI WINBOOL WINAPI SetupSetPlatformPathOverrideW(PCWSTR Override);
  WINSETUPAPI WINBOOL WINAPI SetupQueueCopyA(HSPFILEQ QueueHandle,PCSTR SourceRootPath,PCSTR SourcePath,PCSTR SourceFilename,PCSTR SourceDescription,PCSTR SourceTagfile,PCSTR TargetDirectory,PCSTR TargetFilename,DWORD CopyStyle);
  WINSETUPAPI WINBOOL WINAPI SetupQueueCopyW(HSPFILEQ QueueHandle,PCWSTR SourceRootPath,PCWSTR SourcePath,PCWSTR SourceFilename,PCWSTR SourceDescription,PCWSTR SourceTagfile,PCWSTR TargetDirectory,PCWSTR TargetFilename,DWORD CopyStyle);
  WINSETUPAPI WINBOOL WINAPI SetupQueueCopyIndirectA(PSP_FILE_COPY_PARAMS_A CopyParams);
  WINSETUPAPI WINBOOL WINAPI SetupQueueCopyIndirectW(PSP_FILE_COPY_PARAMS_W CopyParams);
  WINSETUPAPI WINBOOL WINAPI SetupQueueDefaultCopyA(HSPFILEQ QueueHandle,HINF InfHandle,PCSTR SourceRootPath,PCSTR SourceFilename,PCSTR TargetFilename,DWORD CopyStyle);
  WINSETUPAPI WINBOOL WINAPI SetupQueueDefaultCopyW(HSPFILEQ QueueHandle,HINF InfHandle,PCWSTR SourceRootPath,PCWSTR SourceFilename,PCWSTR TargetFilename,DWORD CopyStyle);
  WINSETUPAPI WINBOOL WINAPI SetupQueueCopySectionA(HSPFILEQ QueueHandle,PCSTR SourceRootPath,HINF InfHandle,HINF ListInfHandle,PCSTR Section,DWORD CopyStyle);
  WINSETUPAPI WINBOOL WINAPI SetupQueueCopySectionW(HSPFILEQ QueueHandle,PCWSTR SourceRootPath,HINF InfHandle,HINF ListInfHandle,PCWSTR Section,DWORD CopyStyle);
  WINSETUPAPI WINBOOL WINAPI SetupQueueDeleteA(HSPFILEQ QueueHandle,PCSTR PathPart1,PCSTR PathPart2);
  WINSETUPAPI WINBOOL WINAPI SetupQueueDeleteW(HSPFILEQ QueueHandle,PCWSTR PathPart1,PCWSTR PathPart2);
  WINSETUPAPI WINBOOL WINAPI SetupQueueDeleteSectionA(HSPFILEQ QueueHandle,HINF InfHandle,HINF ListInfHandle,PCSTR Section);
  WINSETUPAPI WINBOOL WINAPI SetupQueueDeleteSectionW(HSPFILEQ QueueHandle,HINF InfHandle,HINF ListInfHandle,PCWSTR Section);
  WINSETUPAPI WINBOOL WINAPI SetupQueueRenameA(HSPFILEQ QueueHandle,PCSTR SourcePath,PCSTR SourceFilename,PCSTR TargetPath,PCSTR TargetFilename);
  WINSETUPAPI WINBOOL WINAPI SetupQueueRenameW(HSPFILEQ QueueHandle,PCWSTR SourcePath,PCWSTR SourceFilename,PCWSTR TargetPath,PCWSTR TargetFilename);
  WINSETUPAPI WINBOOL WINAPI SetupQueueRenameSectionA(HSPFILEQ QueueHandle,HINF InfHandle,HINF ListInfHandle,PCSTR Section);
  WINSETUPAPI WINBOOL WINAPI SetupQueueRenameSectionW(HSPFILEQ QueueHandle,HINF InfHandle,HINF ListInfHandle,PCWSTR Section);
  WINSETUPAPI WINBOOL WINAPI SetupCommitFileQueueA(HWND Owner,HSPFILEQ QueueHandle,PSP_FILE_CALLBACK_A MsgHandler,PVOID Context);
  WINSETUPAPI WINBOOL WINAPI SetupCommitFileQueueW(HWND Owner,HSPFILEQ QueueHandle,PSP_FILE_CALLBACK_W MsgHandler,PVOID Context);
  WINSETUPAPI WINBOOL WINAPI SetupScanFileQueueA(HSPFILEQ FileQueue,DWORD Flags,HWND Window,PSP_FILE_CALLBACK_A CallbackRoutine,PVOID CallbackContext,PDWORD Result);
  WINSETUPAPI WINBOOL WINAPI SetupScanFileQueueW(HSPFILEQ FileQueue,DWORD Flags,HWND Window,PSP_FILE_CALLBACK_W CallbackRoutine,PVOID CallbackContext,PDWORD Result);

#define SPQ_SCAN_FILE_PRESENCE 0x00000001
#define SPQ_SCAN_FILE_VALIDITY 0x00000002
#define SPQ_SCAN_USE_CALLBACK 0x00000004
#define SPQ_SCAN_USE_CALLBACKEX 0x00000008
#define SPQ_SCAN_INFORM_USER 0x00000010
#define SPQ_SCAN_PRUNE_COPY_QUEUE 0x00000020

#define SPQ_SCAN_USE_CALLBACK_SIGNERINFO 0x00000040
#define SPQ_SCAN_PRUNE_DELREN 0x00000080

#define SPQ_DELAYED_COPY 0x00000001

  WINSETUPAPI WINBOOL WINAPI SetupGetFileQueueCount(HSPFILEQ FileQueue,UINT SubQueueFileOp,PUINT NumOperations);
  WINSETUPAPI WINBOOL WINAPI SetupGetFileQueueFlags(HSPFILEQ FileQueue,PDWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupSetFileQueueFlags(HSPFILEQ FileQueue,DWORD FlagMask,DWORD Flags);

#define SPQ_FLAG_BACKUP_AWARE 0x00000001
#define SPQ_FLAG_ABORT_IF_UNSIGNED 0x00000002
#define SPQ_FLAG_FILES_MODIFIED 0x00000004
#define SPQ_FLAG_VALID 0x00000007

#define SPOST_NONE 0
#define SPOST_PATH 1
#define SPOST_URL 2
#define SPOST_MAX 3

#define SetupCopyOEMInf __MINGW_NAME_AW(SetupCopyOEMInf)

  WINSETUPAPI WINBOOL WINAPI SetupCopyOEMInfA(PCSTR SourceInfFileName,PCSTR OEMSourceMediaLocation,DWORD OEMSourceMediaType,DWORD CopyStyle,PSTR DestinationInfFileName,DWORD DestinationInfFileNameSize,PDWORD RequiredSize,PSTR *DestinationInfFileNameComponent);
  WINSETUPAPI WINBOOL WINAPI SetupCopyOEMInfW(PCWSTR SourceInfFileName,PCWSTR OEMSourceMediaLocation,DWORD OEMSourceMediaType,DWORD CopyStyle,PWSTR DestinationInfFileName,DWORD DestinationInfFileNameSize,PDWORD RequiredSize,PWSTR *DestinationInfFileNameComponent);

#define SUOI_FORCEDELETE 0x00000001

#define SetupUninstallOEMInf __MINGW_NAME_AW(SetupUninstallOEMInf)
#define SetupCreateDiskSpaceList __MINGW_NAME_AW(SetupCreateDiskSpaceList)

  WINSETUPAPI WINBOOL WINAPI SetupUninstallOEMInfA(PCSTR InfFileName,DWORD Flags,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupUninstallOEMInfW(PCWSTR InfFileName,DWORD Flags,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupUninstallNewlyCopiedInfs(HSPFILEQ FileQueue,DWORD Flags,PVOID Reserved);
  WINSETUPAPI HDSKSPC WINAPI SetupCreateDiskSpaceListA(PVOID Reserved1,DWORD Reserved2,UINT Flags);
  WINSETUPAPI HDSKSPC WINAPI SetupCreateDiskSpaceListW(PVOID Reserved1,DWORD Reserved2,UINT Flags);

#define SPDSL_IGNORE_DISK 0x00000001
#define SPDSL_DISALLOW_NEGATIVE_ADJUST 0x00000002

#define SetupDuplicateDiskSpaceList __MINGW_NAME_AW(SetupDuplicateDiskSpaceList)
#define SetupQueryDrivesInDiskSpaceList __MINGW_NAME_AW(SetupQueryDrivesInDiskSpaceList)
#define SetupQuerySpaceRequiredOnDrive __MINGW_NAME_AW(SetupQuerySpaceRequiredOnDrive)
#define SetupAdjustDiskSpaceList __MINGW_NAME_AW(SetupAdjustDiskSpaceList)
#define SetupAddToDiskSpaceList __MINGW_NAME_AW(SetupAddToDiskSpaceList)
#define SetupAddSectionToDiskSpaceList __MINGW_NAME_AW(SetupAddSectionToDiskSpaceList)
#define SetupAddInstallSectionToDiskSpaceList __MINGW_NAME_AW(SetupAddInstallSectionToDiskSpaceList)
#define SetupRemoveFromDiskSpaceList __MINGW_NAME_AW(SetupRemoveFromDiskSpaceList)
#define SetupRemoveSectionFromDiskSpaceList __MINGW_NAME_AW(SetupRemoveSectionFromDiskSpaceList)
#define SetupRemoveInstallSectionFromDiskSpaceList __MINGW_NAME_AW(SetupRemoveInstallSectionFromDiskSpaceList)
#define SetupIterateCabinet __MINGW_NAME_AW(SetupIterateCabinet)

  WINSETUPAPI HDSKSPC WINAPI SetupDuplicateDiskSpaceListA(HDSKSPC DiskSpace,PVOID Reserved1,DWORD Reserved2,UINT Flags);
  WINSETUPAPI HDSKSPC WINAPI SetupDuplicateDiskSpaceListW(HDSKSPC DiskSpace,PVOID Reserved1,DWORD Reserved2,UINT Flags);
  WINSETUPAPI WINBOOL WINAPI SetupDestroyDiskSpaceList(HDSKSPC DiskSpace);
  WINSETUPAPI WINBOOL WINAPI SetupQueryDrivesInDiskSpaceListA(HDSKSPC DiskSpace,PSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupQueryDrivesInDiskSpaceListW(HDSKSPC DiskSpace,PWSTR ReturnBuffer,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupQuerySpaceRequiredOnDriveA(HDSKSPC DiskSpace,PCSTR DriveSpec,LONGLONG *SpaceRequired,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupQuerySpaceRequiredOnDriveW(HDSKSPC DiskSpace,PCWSTR DriveSpec,LONGLONG *SpaceRequired,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupAdjustDiskSpaceListA(HDSKSPC DiskSpace,LPCSTR DriveRoot,LONGLONG Amount,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupAdjustDiskSpaceListW(HDSKSPC DiskSpace,LPCWSTR DriveRoot,LONGLONG Amount,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupAddToDiskSpaceListA(HDSKSPC DiskSpace,PCSTR TargetFilespec,LONGLONG FileSize,UINT Operation,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupAddToDiskSpaceListW(HDSKSPC DiskSpace,PCWSTR TargetFilespec,LONGLONG FileSize,UINT Operation,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupAddSectionToDiskSpaceListA(HDSKSPC DiskSpace,HINF InfHandle,HINF ListInfHandle,PCSTR SectionName,UINT Operation,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupAddSectionToDiskSpaceListW(HDSKSPC DiskSpace,HINF InfHandle,HINF ListInfHandle,PCWSTR SectionName,UINT Operation,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupAddInstallSectionToDiskSpaceListA(HDSKSPC DiskSpace,HINF InfHandle,HINF LayoutInfHandle,PCSTR SectionName,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupAddInstallSectionToDiskSpaceListW(HDSKSPC DiskSpace,HINF InfHandle,HINF LayoutInfHandle,PCWSTR SectionName,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveFromDiskSpaceListA(HDSKSPC DiskSpace,PCSTR TargetFilespec,UINT Operation,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveFromDiskSpaceListW(HDSKSPC DiskSpace,PCWSTR TargetFilespec,UINT Operation,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveSectionFromDiskSpaceListA(HDSKSPC DiskSpace,HINF InfHandle,HINF ListInfHandle,PCSTR SectionName,UINT Operation,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveSectionFromDiskSpaceListW(HDSKSPC DiskSpace,HINF InfHandle,HINF ListInfHandle,PCWSTR SectionName,UINT Operation,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveInstallSectionFromDiskSpaceListA(HDSKSPC DiskSpace,HINF InfHandle,HINF LayoutInfHandle,PCSTR SectionName,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveInstallSectionFromDiskSpaceListW(HDSKSPC DiskSpace,HINF InfHandle,HINF LayoutInfHandle,PCWSTR SectionName,PVOID Reserved1,UINT Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupIterateCabinetA(PCSTR CabinetFile,DWORD Reserved,PSP_FILE_CALLBACK_A MsgHandler,PVOID Context);
  WINSETUPAPI WINBOOL WINAPI SetupIterateCabinetW(PCWSTR CabinetFile,DWORD Reserved,PSP_FILE_CALLBACK_W MsgHandler,PVOID Context);
  WINSETUPAPI INT WINAPI SetupPromptReboot(HSPFILEQ FileQueue,HWND Owner,WINBOOL ScanOnly);

#define SPFILEQ_FILE_IN_USE 0x00000001
#define SPFILEQ_REBOOT_RECOMMENDED 0x00000002
#define SPFILEQ_REBOOT_IN_PROGRESS 0x00000004

#define SetupDefaultQueueCallback __MINGW_NAME_AW(SetupDefaultQueueCallback)

  WINSETUPAPI PVOID WINAPI SetupInitDefaultQueueCallback(HWND OwnerWindow);
  WINSETUPAPI PVOID WINAPI SetupInitDefaultQueueCallbackEx(HWND OwnerWindow,HWND AlternateProgressWindow,UINT ProgressMessage,DWORD Reserved1,PVOID Reserved2);
  WINSETUPAPI VOID WINAPI SetupTermDefaultQueueCallback(PVOID Context);
  WINSETUPAPI UINT WINAPI SetupDefaultQueueCallbackA(PVOID Context,UINT Notification,UINT_PTR Param1,UINT_PTR Param2);
  WINSETUPAPI UINT WINAPI SetupDefaultQueueCallbackW(PVOID Context,UINT Notification,UINT_PTR Param1,UINT_PTR Param2);

#define FLG_ADDREG_DELREG_BIT (0x00008000)
#define FLG_ADDREG_BINVALUETYPE (0x00000001)
#define FLG_ADDREG_NOCLOBBER (0x00000002)
#define FLG_ADDREG_DELVAL (0x00000004)
#define FLG_ADDREG_APPEND (0x00000008)
#define FLG_ADDREG_KEYONLY (0x00000010)
#define FLG_ADDREG_OVERWRITEONLY (0x00000020)
#define FLG_ADDREG_64BITKEY (0x00001000)
#define FLG_ADDREG_KEYONLY_COMMON (0x00002000)
#define FLG_ADDREG_32BITKEY (0x00004000)
#define FLG_ADDREG_TYPE_MASK (0xFFFF0000 | FLG_ADDREG_BINVALUETYPE)
#define FLG_ADDREG_TYPE_SZ (0x00000000)
#define FLG_ADDREG_TYPE_MULTI_SZ (0x00010000)
#define FLG_ADDREG_TYPE_EXPAND_SZ (0x00020000)
#define FLG_ADDREG_TYPE_BINARY (0x00000000 | FLG_ADDREG_BINVALUETYPE)
#define FLG_ADDREG_TYPE_DWORD (0x00010000 | FLG_ADDREG_BINVALUETYPE)
#define FLG_ADDREG_TYPE_NONE (0x00020000 | FLG_ADDREG_BINVALUETYPE)

#define FLG_DELREG_VALUE (0x00000000)

#define FLG_DELREG_TYPE_MASK FLG_ADDREG_TYPE_MASK
#define FLG_DELREG_TYPE_SZ FLG_ADDREG_TYPE_SZ
#define FLG_DELREG_TYPE_MULTI_SZ FLG_ADDREG_TYPE_MULTI_SZ
#define FLG_DELREG_TYPE_EXPAND_SZ FLG_ADDREG_TYPE_EXPAND_SZ
#define FLG_DELREG_TYPE_BINARY FLG_ADDREG_TYPE_BINARY
#define FLG_DELREG_TYPE_DWORD FLG_ADDREG_TYPE_DWORD
#define FLG_DELREG_TYPE_NONE FLG_ADDREG_TYPE_NONE
#define FLG_DELREG_64BITKEY FLG_ADDREG_64BITKEY
#define FLG_DELREG_KEYONLY_COMMON FLG_ADDREG_KEYONLY_COMMON
#define FLG_DELREG_32BITKEY FLG_ADDREG_32BITKEY

#define FLG_DELREG_OPERATION_MASK (0x000000FE)
#define FLG_DELREG_MULTI_SZ_DELSTRING (FLG_DELREG_TYPE_MULTI_SZ | FLG_ADDREG_DELREG_BIT | 0x00000002)

#define FLG_BITREG_CLEARBITS (0x00000000)
#define FLG_BITREG_SETBITS (0x00000001)

#define FLG_BITREG_64BITKEY (0x00001000)
#define FLG_BITREG_32BITKEY (0x00004000)
#define FLG_INI2REG_64BITKEY (0x00001000)
#define FLG_INI2REG_32BITKEY (0x00004000)
#define FLG_REGSVR_DLLREGISTER (0x00000001)
#define FLG_REGSVR_DLLINSTALL (0x00000002)

#define FLG_PROFITEM_CURRENTUSER (0x00000001)
#define FLG_PROFITEM_DELETE (0x00000002)
#define FLG_PROFITEM_GROUP (0x00000004)
#define FLG_PROFITEM_CSIDL (0x00000008)

#define SetupInstallFromInfSection __MINGW_NAME_AW(SetupInstallFromInfSection)

  WINSETUPAPI WINBOOL WINAPI SetupInstallFromInfSectionA(HWND Owner,HINF InfHandle,PCSTR SectionName,UINT Flags,HKEY RelativeKeyRoot,PCSTR SourceRootPath,UINT CopyFlags,PSP_FILE_CALLBACK_A MsgHandler,PVOID Context,HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupInstallFromInfSectionW(HWND Owner,HINF InfHandle,PCWSTR SectionName,UINT Flags,HKEY RelativeKeyRoot,PCWSTR SourceRootPath,UINT CopyFlags,PSP_FILE_CALLBACK_W MsgHandler,PVOID Context,HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);

#define SPINST_LOGCONFIG 0x00000001
#define SPINST_INIFILES 0x00000002
#define SPINST_REGISTRY 0x00000004
#define SPINST_INI2REG 0x00000008
#define SPINST_FILES 0x00000010
#define SPINST_BITREG 0x00000020
#define SPINST_REGSVR 0x00000040
#define SPINST_UNREGSVR 0x00000080
#define SPINST_PROFILEITEMS 0x00000100
#define SPINST_COPYINF 0x00000200
#define SPINST_ALL 0x000003ff
#define SPINST_SINGLESECTION 0x00010000
#define SPINST_LOGCONFIG_IS_FORCED 0x00020000
#define SPINST_LOGCONFIGS_ARE_OVERRIDES 0x00040000
#define SPINST_REGISTERCALLBACKAWARE 0x00080000

#define SetupInstallFilesFromInfSection __MINGW_NAME_AW(SetupInstallFilesFromInfSection)

  WINSETUPAPI WINBOOL WINAPI SetupInstallFilesFromInfSectionA(HINF InfHandle,HINF LayoutInfHandle,HSPFILEQ FileQueue,PCSTR SectionName,PCSTR SourceRootPath,UINT CopyFlags);
  WINSETUPAPI WINBOOL WINAPI SetupInstallFilesFromInfSectionW(HINF InfHandle,HINF LayoutInfHandle,HSPFILEQ FileQueue,PCWSTR SectionName,PCWSTR SourceRootPath,UINT CopyFlags);

#define SPSVCINST_TAGTOFRONT (0x00000001)
#define SPSVCINST_ASSOCSERVICE (0x00000002)
#define SPSVCINST_DELETEEVENTLOGENTRY (0x00000004)
#define SPSVCINST_NOCLOBBER_DISPLAYNAME (0x00000008)
#define SPSVCINST_NOCLOBBER_STARTTYPE (0x00000010)
#define SPSVCINST_NOCLOBBER_ERRORCONTROL (0x00000020)
#define SPSVCINST_NOCLOBBER_LOADORDERGROUP (0x00000040)
#define SPSVCINST_NOCLOBBER_DEPENDENCIES (0x00000080)
#define SPSVCINST_NOCLOBBER_DESCRIPTION (0x00000100)
#define SPSVCINST_STOPSERVICE (0x00000200)
#define SPSVCINST_CLOBBER_SECURITY (0x00000400)

#define SPFILELOG_SYSTEMLOG 0x00000001
#define SPFILELOG_FORCENEW 0x00000002
#define SPFILELOG_QUERYONLY 0x00000004

#define SPFILELOG_OEMFILE 0x00000001

  typedef PVOID HSPFILELOG;

#define SetupInstallServicesFromInfSection __MINGW_NAME_AW(SetupInstallServicesFromInfSection)
#define SetupInstallServicesFromInfSectionEx __MINGW_NAME_AW(SetupInstallServicesFromInfSectionEx)
#define InstallHinfSection __MINGW_NAME_AW(InstallHinfSection)
#define SetupInitializeFileLog __MINGW_NAME_AW(SetupInitializeFileLog)
#define SetupLogFile __MINGW_NAME_AW(SetupLogFile)
#define SetupRemoveFileLogEntry __MINGW_NAME_AW(SetupRemoveFileLogEntry)

  WINSETUPAPI WINBOOL WINAPI SetupInstallServicesFromInfSectionA(HINF InfHandle,PCSTR SectionName,DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupInstallServicesFromInfSectionW(HINF InfHandle,PCWSTR SectionName,DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupInstallServicesFromInfSectionExA(HINF InfHandle,PCSTR SectionName,DWORD Flags,HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PVOID Reserved1,PVOID Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupInstallServicesFromInfSectionExW(HINF InfHandle,PCWSTR SectionName,DWORD Flags,HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PVOID Reserved1,PVOID Reserved2);
  VOID WINAPI InstallHinfSectionA(HWND Window,HINSTANCE ModuleHandle,PCSTR CommandLine,INT ShowCommand);
  VOID WINAPI InstallHinfSectionW(HWND Window,HINSTANCE ModuleHandle,PCWSTR CommandLine,INT ShowCommand);
  WINSETUPAPI HSPFILELOG WINAPI SetupInitializeFileLogA(PCSTR LogFileName,DWORD Flags);
  WINSETUPAPI HSPFILELOG WINAPI SetupInitializeFileLogW(PCWSTR LogFileName,DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupTerminateFileLog(HSPFILELOG FileLogHandle);
  WINSETUPAPI WINBOOL WINAPI SetupLogFileA(HSPFILELOG FileLogHandle,PCSTR LogSectionName,PCSTR SourceFilename,PCSTR TargetFilename,DWORD Checksum,PCSTR DiskTagfile,PCSTR DiskDescription,PCSTR OtherInfo,DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupLogFileW(HSPFILELOG FileLogHandle,PCWSTR LogSectionName,PCWSTR SourceFilename,PCWSTR TargetFilename,DWORD Checksum,PCWSTR DiskTagfile,PCWSTR DiskDescription,PCWSTR OtherInfo,DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveFileLogEntryA(HSPFILELOG FileLogHandle,PCSTR LogSectionName,PCSTR TargetFilename);
  WINSETUPAPI WINBOOL WINAPI SetupRemoveFileLogEntryW(HSPFILELOG FileLogHandle,PCWSTR LogSectionName,PCWSTR TargetFilename);

  typedef enum {
    SetupFileLogSourceFilename,SetupFileLogChecksum,SetupFileLogDiskTagfile,SetupFileLogDiskDescription,SetupFileLogOtherInfo,SetupFileLogMax
  } SetupFileLogInfo;

#define SetupQueryFileLog __MINGW_NAME_AW(SetupQueryFileLog)

  WINSETUPAPI WINBOOL WINAPI SetupQueryFileLogA(HSPFILELOG FileLogHandle,PCSTR LogSectionName,PCSTR TargetFilename,SetupFileLogInfo DesiredInfo,PSTR DataOut,DWORD ReturnBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupQueryFileLogW(HSPFILELOG FileLogHandle,PCWSTR LogSectionName,PCWSTR TargetFilename,SetupFileLogInfo DesiredInfo,PWSTR DataOut,DWORD ReturnBufferSize,PDWORD RequiredSize);

#define LogSeverity DWORD
#define LogSevInformation 0x00000000
#define LogSevWarning 0x00000001
#define LogSevError 0x00000002
#define LogSevFatalError 0x00000003
#define LogSevMaximum 0x00000004

#define DICD_GENERATE_ID 0x00000001
#define DICD_INHERIT_CLASSDRVS 0x00000002

#define DIOD_INHERIT_CLASSDRVS 0x00000002
#define DIOD_CANCEL_REMOVE 0x00000004

#define DIODI_NO_ADD 0x00000001

#define SPRDI_FIND_DUPS 0x00000001

#define SPDIT_NODRIVER 0x00000000
#define SPDIT_CLASSDRIVER 0x00000001
#define SPDIT_COMPATDRIVER 0x00000002

#define SetupLogError __MINGW_NAME_AW(SetupLogError)
#define SetupGetBackupInformation __MINGW_NAME_AW(SetupGetBackupInformation)
#define SetupPrepareQueueForRestore __MINGW_NAME_AW(SetupPrepareQueueForRestore)
#define SetupDiCreateDeviceInfoListEx __MINGW_NAME_AW(SetupDiCreateDeviceInfoListEx)
#define SetupDiGetDeviceInfoListDetail __MINGW_NAME_AW(SetupDiGetDeviceInfoListDetail)
#define SetupDiCreateDeviceInfo __MINGW_NAME_AW(SetupDiCreateDeviceInfo)
#define SetupDiOpenDeviceInfo __MINGW_NAME_AW(SetupDiOpenDeviceInfo)
#define SetupDiGetDeviceInstanceId __MINGW_NAME_AW(SetupDiGetDeviceInstanceId)
#define SetupDiCreateDeviceInterface __MINGW_NAME_AW(SetupDiCreateDeviceInterface)
#define SetupDiCreateInterfaceDevice __MINGW_NAME_AW(SetupDiCreateDeviceInterface)
#define SetupDiOpenDeviceInterface __MINGW_NAME_AW(SetupDiOpenDeviceInterface)

  WINSETUPAPI WINBOOL WINAPI SetupOpenLog(WINBOOL Erase);
  WINSETUPAPI WINBOOL WINAPI SetupLogErrorA(LPCSTR MessageString,LogSeverity Severity);
  WINSETUPAPI WINBOOL WINAPI SetupLogErrorW(LPCWSTR MessageString,LogSeverity Severity);
  WINSETUPAPI VOID WINAPI SetupCloseLog(VOID);
  WINSETUPAPI WINBOOL WINAPI SetupGetBackupInformationA(HSPFILEQ QueueHandle,PSP_BACKUP_QUEUE_PARAMS_A BackupParams);
  WINSETUPAPI WINBOOL WINAPI SetupGetBackupInformationW(HSPFILEQ QueueHandle,PSP_BACKUP_QUEUE_PARAMS_W BackupParams);
  WINSETUPAPI WINBOOL WINAPI SetupPrepareQueueForRestoreA(HSPFILEQ QueueHandle,PCSTR BackupPath,DWORD RestoreFlags);
  WINSETUPAPI WINBOOL WINAPI SetupPrepareQueueForRestoreW(HSPFILEQ QueueHandle,PCWSTR BackupPath,DWORD RestoreFlags);
  WINSETUPAPI WINBOOL WINAPI SetupSetNonInteractiveMode(WINBOOL NonInteractiveFlag);
  WINSETUPAPI WINBOOL WINAPI SetupGetNonInteractiveMode(VOID);
  WINSETUPAPI HDEVINFO WINAPI SetupDiCreateDeviceInfoList(CONST GUID *ClassGuid,HWND hwndParent);
  WINSETUPAPI HDEVINFO WINAPI SetupDiCreateDeviceInfoListExA(CONST GUID *ClassGuid,HWND hwndParent,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI HDEVINFO WINAPI SetupDiCreateDeviceInfoListExW(CONST GUID *ClassGuid,HWND hwndParent,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInfoListClass(HDEVINFO DeviceInfoSet,LPGUID ClassGuid);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInfoListDetailA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_LIST_DETAIL_DATA_A DeviceInfoSetDetailData);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInfoListDetailW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_LIST_DETAIL_DATA_W DeviceInfoSetDetailData);
  WINSETUPAPI WINBOOL WINAPI SetupDiCreateDeviceInfoA(HDEVINFO DeviceInfoSet,PCSTR DeviceName,CONST GUID *ClassGuid,PCSTR DeviceDescription,HWND hwndParent,DWORD CreationFlags,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiCreateDeviceInfoW(HDEVINFO DeviceInfoSet,PCWSTR DeviceName,CONST GUID *ClassGuid,PCWSTR DeviceDescription,HWND hwndParent,DWORD CreationFlags,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiOpenDeviceInfoA(HDEVINFO DeviceInfoSet,PCSTR DeviceInstanceId,HWND hwndParent,DWORD OpenFlags,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiOpenDeviceInfoW(HDEVINFO DeviceInfoSet,PCWSTR DeviceInstanceId,HWND hwndParent,DWORD OpenFlags,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInstanceIdA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSTR DeviceInstanceId,DWORD DeviceInstanceIdSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInstanceIdW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PWSTR DeviceInstanceId,DWORD DeviceInstanceIdSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiDeleteDeviceInfo(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiEnumDeviceInfo(HDEVINFO DeviceInfoSet,DWORD MemberIndex,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiDestroyDeviceInfoList(HDEVINFO DeviceInfoSet);
  WINSETUPAPI WINBOOL WINAPI SetupDiEnumDeviceInterfaces(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,CONST GUID *InterfaceClassGuid,DWORD MemberIndex,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData);
#define SetupDiEnumInterfaceDevice SetupDiEnumDeviceInterfaces
  WINSETUPAPI WINBOOL WINAPI SetupDiCreateDeviceInterfaceA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,CONST GUID *InterfaceClassGuid,PCSTR ReferenceString,DWORD CreationFlags,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData);
  WINSETUPAPI WINBOOL WINAPI SetupDiCreateDeviceInterfaceW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,CONST GUID *InterfaceClassGuid,PCWSTR ReferenceString,DWORD CreationFlags,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData);
#define SetupDiCreateInterfaceDeviceW SetupDiCreateDeviceInterfaceW
#define SetupDiCreateInterfaceDeviceA SetupDiCreateDeviceInterfaceA
  WINSETUPAPI WINBOOL WINAPI SetupDiOpenDeviceInterfaceA(HDEVINFO DeviceInfoSet,PCSTR DevicePath,DWORD OpenFlags,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData);
  WINSETUPAPI WINBOOL WINAPI SetupDiOpenDeviceInterfaceW(HDEVINFO DeviceInfoSet,PCWSTR DevicePath,DWORD OpenFlags,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData);
#define SetupDiOpenInterfaceDeviceW SetupDiOpenDeviceInterfaceW
#define SetupDiOpenInterfaceDeviceA SetupDiOpenDeviceInterfaceA

#define SetupDiOpenInterfaceDevice __MINGW_NAME_AW(SetupDiOpenDeviceInterface)
#define SetupDiGetDeviceInterfaceDetail __MINGW_NAME_AW(SetupDiGetDeviceInterfaceDetail)
#define SetupDiGetInterfaceDeviceDetail __MINGW_NAME_AW(SetupDiGetDeviceInterfaceDetail)
#define SetupDiEnumDriverInfo __MINGW_NAME_AW(SetupDiEnumDriverInfo)
#define SetupDiGetSelectedDriver __MINGW_NAME_AW(SetupDiGetSelectedDriver)
#define SetupDiSetSelectedDriver __MINGW_NAME_AW(SetupDiSetSelectedDriver)
#define SetupDiGetDriverInfoDetail __MINGW_NAME_AW(SetupDiGetDriverInfoDetail)

  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInterfaceAlias(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData,CONST GUID *AliasInterfaceClassGuid,PSP_DEVICE_INTERFACE_DATA AliasDeviceInterfaceData);
#define SetupDiGetInterfaceDeviceAlias SetupDiGetDeviceInterfaceAlias
  WINSETUPAPI WINBOOL WINAPI SetupDiDeleteDeviceInterfaceData(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData);
#define SetupDiDeleteInterfaceDeviceData SetupDiDeleteDeviceInterfaceData
  WINSETUPAPI WINBOOL WINAPI SetupDiRemoveDeviceInterface(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData);
#define SetupDiRemoveInterfaceDevice SetupDiRemoveDeviceInterface
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInterfaceDetailA(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData,PSP_DEVICE_INTERFACE_DETAIL_DATA_A DeviceInterfaceDetailData,DWORD DeviceInterfaceDetailDataSize,PDWORD RequiredSize,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInterfaceDetailW(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData,PSP_DEVICE_INTERFACE_DETAIL_DATA_W DeviceInterfaceDetailData,DWORD DeviceInterfaceDetailDataSize,PDWORD RequiredSize,PSP_DEVINFO_DATA DeviceInfoData);
#define SetupDiGetInterfaceDeviceDetailW SetupDiGetDeviceInterfaceDetailW
#define SetupDiGetInterfaceDeviceDetailA SetupDiGetDeviceInterfaceDetailA
  WINSETUPAPI WINBOOL WINAPI SetupDiInstallDeviceInterfaces(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
#define SetupDiInstallInterfaceDevices SetupDiInstallDeviceInterfaces
  WINSETUPAPI WINBOOL WINAPI SetupDiSetDeviceInterfaceDefault(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData,DWORD Flags,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiRegisterDeviceInfo(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Flags,PSP_DETSIG_CMPPROC CompareProc,PVOID CompareContext,PSP_DEVINFO_DATA DupDeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiBuildDriverInfoList(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD DriverType);
  WINSETUPAPI WINBOOL WINAPI SetupDiCancelDriverInfoSearch(HDEVINFO DeviceInfoSet);
  WINSETUPAPI WINBOOL WINAPI SetupDiEnumDriverInfoA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD DriverType,DWORD MemberIndex,PSP_DRVINFO_DATA_A DriverInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiEnumDriverInfoW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD DriverType,DWORD MemberIndex,PSP_DRVINFO_DATA_W DriverInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetSelectedDriverA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_A DriverInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetSelectedDriverW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_W DriverInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetSelectedDriverA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_A DriverInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetSelectedDriverW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_W DriverInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDriverInfoDetailA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_A DriverInfoData,PSP_DRVINFO_DETAIL_DATA_A DriverInfoDetailData,DWORD DriverInfoDetailDataSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDriverInfoDetailW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_W DriverInfoData,PSP_DRVINFO_DETAIL_DATA_W DriverInfoDetailData,DWORD DriverInfoDetailDataSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiDestroyDriverInfoList(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD DriverType);

#define DIGCF_DEFAULT 0x00000001
#define DIGCF_PRESENT 0x00000002
#define DIGCF_ALLCLASSES 0x00000004
#define DIGCF_PROFILE 0x00000008
#define DIGCF_DEVICEINTERFACE 0x00000010

#define DIGCF_INTERFACEDEVICE DIGCF_DEVICEINTERFACE

#define DIBCI_NOINSTALLCLASS 0x00000001
#define DIBCI_NODISPLAYCLASS 0x00000002

#define SetupDiGetClassDevs __MINGW_NAME_AW(SetupDiGetClassDevs)
#define SetupDiGetClassDevsEx __MINGW_NAME_AW(SetupDiGetClassDevsEx)
#define SetupDiGetINFClass __MINGW_NAME_AW(SetupDiGetINFClass)
#define SetupDiBuildClassInfoListEx __MINGW_NAME_AW(SetupDiBuildClassInfoListEx)
#define SetupDiGetClassDescription __MINGW_NAME_AW(SetupDiGetClassDescription)
#define SetupDiGetClassDescriptionEx __MINGW_NAME_AW(SetupDiGetClassDescriptionEx)
#define SetupDiInstallClass __MINGW_NAME_AW(SetupDiInstallClass)
#define SetupDiInstallClassEx __MINGW_NAME_AW(SetupDiInstallClassEx)
#define SetupDiOpenClassRegKeyEx __MINGW_NAME_AW(SetupDiOpenClassRegKeyEx)
#define SetupDiCreateDeviceInterfaceRegKey __MINGW_NAME_AW(SetupDiCreateDeviceInterfaceRegKey)
#define SetupDiCreateInterfaceDeviceRegKey __MINGW_NAME_AW(SetupDiCreateDeviceInterfaceRegKey)
#define SetupDiCreateDevRegKey __MINGW_NAME_AW(SetupDiCreateDevRegKey)
#define SetupDiGetHwProfileListEx __MINGW_NAME_AW(SetupDiGetHwProfileListEx)

  WINSETUPAPI HDEVINFO WINAPI SetupDiGetClassDevsA(CONST GUID *ClassGuid,PCSTR Enumerator,HWND hwndParent,DWORD Flags);
  WINSETUPAPI HDEVINFO WINAPI SetupDiGetClassDevsW(CONST GUID *ClassGuid,PCWSTR Enumerator,HWND hwndParent,DWORD Flags);
  WINSETUPAPI HDEVINFO WINAPI SetupDiGetClassDevsExA(CONST GUID *ClassGuid,PCSTR Enumerator,HWND hwndParent,DWORD Flags,HDEVINFO DeviceInfoSet,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI HDEVINFO WINAPI SetupDiGetClassDevsExW(CONST GUID *ClassGuid,PCWSTR Enumerator,HWND hwndParent,DWORD Flags,HDEVINFO DeviceInfoSet,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetINFClassA(PCSTR InfName,LPGUID ClassGuid,PSTR ClassName,DWORD ClassNameSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetINFClassW(PCWSTR InfName,LPGUID ClassGuid,PWSTR ClassName,DWORD ClassNameSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiBuildClassInfoList(DWORD Flags,LPGUID ClassGuidList,DWORD ClassGuidListSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiBuildClassInfoListExA(DWORD Flags,LPGUID ClassGuidList,DWORD ClassGuidListSize,PDWORD RequiredSize,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiBuildClassInfoListExW(DWORD Flags,LPGUID ClassGuidList,DWORD ClassGuidListSize,PDWORD RequiredSize,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassDescriptionA(CONST GUID *ClassGuid,PSTR ClassDescription,DWORD ClassDescriptionSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassDescriptionW(CONST GUID *ClassGuid,PWSTR ClassDescription,DWORD ClassDescriptionSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassDescriptionExA(CONST GUID *ClassGuid,PSTR ClassDescription,DWORD ClassDescriptionSize,PDWORD RequiredSize,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassDescriptionExW(CONST GUID *ClassGuid,PWSTR ClassDescription,DWORD ClassDescriptionSize,PDWORD RequiredSize,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiCallClassInstaller(DI_FUNCTION InstallFunction,HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiSelectDevice(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiSelectBestCompatDrv(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiInstallDevice(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiInstallDriverFiles(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiRegisterCoDeviceInstallers(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiRemoveDevice(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiUnremoveDevice(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiRestartDevices(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiChangeState(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiInstallClassA(HWND hwndParent,PCSTR InfFileName,DWORD Flags,HSPFILEQ FileQueue);
  WINSETUPAPI WINBOOL WINAPI SetupDiInstallClassW(HWND hwndParent,PCWSTR InfFileName,DWORD Flags,HSPFILEQ FileQueue);
  WINSETUPAPI WINBOOL WINAPI SetupDiInstallClassExA(HWND hwndParent,PCSTR InfFileName,DWORD Flags,HSPFILEQ FileQueue,CONST GUID *InterfaceClassGuid,PVOID Reserved1,PVOID Reserved2);
  WINSETUPAPI WINBOOL WINAPI SetupDiInstallClassExW(HWND hwndParent,PCWSTR InfFileName,DWORD Flags,HSPFILEQ FileQueue,CONST GUID *InterfaceClassGuid,PVOID Reserved1,PVOID Reserved2);
  WINSETUPAPI HKEY WINAPI SetupDiOpenClassRegKey(CONST GUID *ClassGuid,REGSAM samDesired);

#define DIOCR_INSTALLER 0x00000001
#define DIOCR_INTERFACE 0x00000002

  WINSETUPAPI HKEY WINAPI SetupDiOpenClassRegKeyExA(CONST GUID *ClassGuid,REGSAM samDesired,DWORD Flags,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI HKEY WINAPI SetupDiOpenClassRegKeyExW(CONST GUID *ClassGuid,REGSAM samDesired,DWORD Flags,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI HKEY WINAPI SetupDiCreateDeviceInterfaceRegKeyA(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData,DWORD Reserved,REGSAM samDesired,HINF InfHandle,PCSTR InfSectionName);
  WINSETUPAPI HKEY WINAPI SetupDiCreateDeviceInterfaceRegKeyW(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData,DWORD Reserved,REGSAM samDesired,HINF InfHandle,PCWSTR InfSectionName);

#define SetupDiCreateInterfaceDeviceRegKeyW SetupDiCreateDeviceInterfaceRegKeyW
#define SetupDiCreateInterfaceDeviceRegKeyA SetupDiCreateDeviceInterfaceRegKeyA

  WINSETUPAPI HKEY WINAPI SetupDiOpenDeviceInterfaceRegKey(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData,DWORD Reserved,REGSAM samDesired);
#define SetupDiOpenInterfaceDeviceRegKey SetupDiOpenDeviceInterfaceRegKey
  WINSETUPAPI WINBOOL WINAPI SetupDiDeleteDeviceInterfaceRegKey(HDEVINFO DeviceInfoSet,PSP_DEVICE_INTERFACE_DATA DeviceInterfaceData,DWORD Reserved);
#define SetupDiDeleteInterfaceDeviceRegKey SetupDiDeleteDeviceInterfaceRegKey

#define DIREG_DEV 0x00000001
#define DIREG_DRV 0x00000002
#define DIREG_BOTH 0x00000004

  WINSETUPAPI HKEY WINAPI SetupDiCreateDevRegKeyA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Scope,DWORD HwProfile,DWORD KeyType,HINF InfHandle,PCSTR InfSectionName);
  WINSETUPAPI HKEY WINAPI SetupDiCreateDevRegKeyW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Scope,DWORD HwProfile,DWORD KeyType,HINF InfHandle,PCWSTR InfSectionName);
  WINSETUPAPI HKEY WINAPI SetupDiOpenDevRegKey(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Scope,DWORD HwProfile,DWORD KeyType,REGSAM samDesired);
  WINSETUPAPI WINBOOL WINAPI SetupDiDeleteDevRegKey(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Scope,DWORD HwProfile,DWORD KeyType);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetHwProfileList(PDWORD HwProfileList,DWORD HwProfileListSize,PDWORD RequiredSize,PDWORD CurrentlyActiveIndex);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetHwProfileListExA(PDWORD HwProfileList,DWORD HwProfileListSize,PDWORD RequiredSize,PDWORD CurrentlyActiveIndex,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetHwProfileListExW(PDWORD HwProfileList,DWORD HwProfileListSize,PDWORD RequiredSize,PDWORD CurrentlyActiveIndex,PCWSTR MachineName,PVOID Reserved);

#define SPDRP_DEVICEDESC (0x00000000)
#define SPDRP_HARDWAREID (0x00000001)
#define SPDRP_COMPATIBLEIDS (0x00000002)
#define SPDRP_UNUSED0 (0x00000003)
#define SPDRP_SERVICE (0x00000004)
#define SPDRP_UNUSED1 (0x00000005)
#define SPDRP_UNUSED2 (0x00000006)
#define SPDRP_CLASS (0x00000007)
#define SPDRP_CLASSGUID (0x00000008)
#define SPDRP_DRIVER (0x00000009)
#define SPDRP_CONFIGFLAGS (0x0000000A)
#define SPDRP_MFG (0x0000000B)
#define SPDRP_FRIENDLYNAME (0x0000000C)
#define SPDRP_LOCATION_INFORMATION (0x0000000D)
#define SPDRP_PHYSICAL_DEVICE_OBJECT_NAME (0x0000000E)
#define SPDRP_CAPABILITIES (0x0000000F)
#define SPDRP_UI_NUMBER (0x00000010)
#define SPDRP_UPPERFILTERS (0x00000011)
#define SPDRP_LOWERFILTERS (0x00000012)
#define SPDRP_BUSTYPEGUID (0x00000013)
#define SPDRP_LEGACYBUSTYPE (0x00000014)
#define SPDRP_BUSNUMBER (0x00000015)
#define SPDRP_ENUMERATOR_NAME (0x00000016)
#define SPDRP_SECURITY (0x00000017)
#define SPDRP_SECURITY_SDS (0x00000018)
#define SPDRP_DEVTYPE (0x00000019)
#define SPDRP_EXCLUSIVE (0x0000001A)
#define SPDRP_CHARACTERISTICS (0x0000001B)
#define SPDRP_ADDRESS (0x0000001C)
#define SPDRP_UI_NUMBER_DESC_FORMAT (0X0000001D)
#define SPDRP_DEVICE_POWER_DATA (0x0000001E)
#define SPDRP_REMOVAL_POLICY (0x0000001F)
#define SPDRP_REMOVAL_POLICY_HW_DEFAULT (0x00000020)
#define SPDRP_REMOVAL_POLICY_OVERRIDE (0x00000021)
#define SPDRP_INSTALL_STATE (0x00000022)
#define SPDRP_LOCATION_PATHS (0x00000023)
#define SPDRP_MAXIMUM_PROPERTY (0x00000024)
#define SPCRP_SECURITY (0x00000017)
#define SPCRP_SECURITY_SDS (0x00000018)
#define SPCRP_DEVTYPE (0x00000019)
#define SPCRP_EXCLUSIVE (0x0000001A)
#define SPCRP_CHARACTERISTICS (0x0000001B)
#define SPCRP_MAXIMUM_PROPERTY (0x0000001C)

#define SetupDiGetDeviceRegistryProperty __MINGW_NAME_AW(SetupDiGetDeviceRegistryProperty)
#define SetupDiGetClassRegistryProperty __MINGW_NAME_AW(SetupDiGetClassRegistryProperty)
#define SetupDiSetDeviceRegistryProperty __MINGW_NAME_AW(SetupDiSetDeviceRegistryProperty)
#define SetupDiSetClassRegistryProperty __MINGW_NAME_AW(SetupDiSetClassRegistryProperty)
#define SetupDiGetDeviceInstallParams __MINGW_NAME_AW(SetupDiGetDeviceInstallParams)
#define SetupDiGetClassInstallParams __MINGW_NAME_AW(SetupDiGetClassInstallParams)
#define SetupDiSetDeviceInstallParams __MINGW_NAME_AW(SetupDiSetDeviceInstallParams)
#define SetupDiSetClassInstallParams __MINGW_NAME_AW(SetupDiSetClassInstallParams)
#define SetupDiGetDriverInstallParams __MINGW_NAME_AW(SetupDiGetDriverInstallParams)
#define SetupDiSetDriverInstallParams __MINGW_NAME_AW(SetupDiSetDriverInstallParams)
#define SetupDiGetClassImageListEx __MINGW_NAME_AW(SetupDiGetClassImageListEx)
#define SetupDiGetClassDevPropertySheets __MINGW_NAME_AW(SetupDiGetClassDevPropertySheets)
#define SetupDiClassNameFromGuid __MINGW_NAME_AW(SetupDiClassNameFromGuid)
#define SetupDiClassNameFromGuidEx __MINGW_NAME_AW(SetupDiClassNameFromGuidEx)
#define SetupDiClassGuidsFromName __MINGW_NAME_AW(SetupDiClassGuidsFromName)
#define SetupDiClassGuidsFromNameEx __MINGW_NAME_AW(SetupDiClassGuidsFromNameEx)
#define SetupDiGetHwProfileFriendlyName __MINGW_NAME_AW(SetupDiGetHwProfileFriendlyName)
#define SetupDiGetHwProfileFriendlyNameEx __MINGW_NAME_AW(SetupDiGetHwProfileFriendlyNameEx)
#define SetupDiGetActualModelsSection __MINGW_NAME_AW(SetupDiGetActualModelsSection)
#define SetupDiGetActualSectionToInstall __MINGW_NAME_AW(SetupDiGetActualSectionToInstall)
#define SetupDiGetActualSectionToInstallEx __MINGW_NAME_AW(SetupDiGetActualSectionToInstallEx)
#define SetupEnumInfSections __MINGW_NAME_AW(SetupEnumInfSections)

  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceRegistryPropertyA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Property,PDWORD PropertyRegDataType,PBYTE PropertyBuffer,DWORD PropertyBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceRegistryPropertyW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Property,PDWORD PropertyRegDataType,PBYTE PropertyBuffer,DWORD PropertyBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassRegistryPropertyA(CONST GUID *ClassGuid,DWORD Property,PDWORD PropertyRegDataType,PBYTE PropertyBuffer,DWORD PropertyBufferSize,PDWORD RequiredSize,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassRegistryPropertyW(CONST GUID *ClassGuid,DWORD Property,PDWORD PropertyRegDataType,PBYTE PropertyBuffer,DWORD PropertyBufferSize,PDWORD RequiredSize,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetDeviceRegistryPropertyA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Property,CONST BYTE *PropertyBuffer,DWORD PropertyBufferSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetDeviceRegistryPropertyW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,DWORD Property,CONST BYTE *PropertyBuffer,DWORD PropertyBufferSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetClassRegistryPropertyA(CONST GUID *ClassGuid,DWORD Property,CONST BYTE *PropertyBuffer,DWORD PropertyBufferSize,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetClassRegistryPropertyW(CONST GUID *ClassGuid,DWORD Property,CONST BYTE *PropertyBuffer,DWORD PropertyBufferSize,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInstallParamsA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DEVINSTALL_PARAMS_A DeviceInstallParams);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDeviceInstallParamsW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DEVINSTALL_PARAMS_W DeviceInstallParams);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassInstallParamsA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_CLASSINSTALL_HEADER ClassInstallParams,DWORD ClassInstallParamsSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassInstallParamsW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_CLASSINSTALL_HEADER ClassInstallParams,DWORD ClassInstallParamsSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetDeviceInstallParamsA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DEVINSTALL_PARAMS_A DeviceInstallParams);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetDeviceInstallParamsW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DEVINSTALL_PARAMS_W DeviceInstallParams);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetClassInstallParamsA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_CLASSINSTALL_HEADER ClassInstallParams,DWORD ClassInstallParamsSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetClassInstallParamsW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_CLASSINSTALL_HEADER ClassInstallParams,DWORD ClassInstallParamsSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDriverInstallParamsA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_A DriverInfoData,PSP_DRVINSTALL_PARAMS DriverInstallParams);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDriverInstallParamsW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_W DriverInfoData,PSP_DRVINSTALL_PARAMS DriverInstallParams);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetDriverInstallParamsA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_A DriverInfoData,PSP_DRVINSTALL_PARAMS DriverInstallParams);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetDriverInstallParamsW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_DRVINFO_DATA_W DriverInfoData,PSP_DRVINSTALL_PARAMS DriverInstallParams);
  WINSETUPAPI WINBOOL WINAPI SetupDiLoadClassIcon(CONST GUID *ClassGuid,HICON *LargeIcon,PINT MiniIconIndex);

#define DMI_MASK 0x00000001
#define DMI_BKCOLOR 0x00000002
#define DMI_USERECT 0x00000004

  WINSETUPAPI INT WINAPI SetupDiDrawMiniIcon(HDC hdc,RECT rc,INT MiniIconIndex,DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassBitmapIndex(CONST GUID *ClassGuid,PINT MiniIconIndex);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassImageList(PSP_CLASSIMAGELIST_DATA ClassImageListData);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassImageListExA(PSP_CLASSIMAGELIST_DATA ClassImageListData,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassImageListExW(PSP_CLASSIMAGELIST_DATA ClassImageListData,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassImageIndex(PSP_CLASSIMAGELIST_DATA ClassImageListData,CONST GUID *ClassGuid,PINT ImageIndex);
  WINSETUPAPI WINBOOL WINAPI SetupDiDestroyClassImageList(PSP_CLASSIMAGELIST_DATA ClassImageListData);

#define DIGCDP_FLAG_BASIC 0x00000001
#define DIGCDP_FLAG_ADVANCED 0x00000002
#define DIGCDP_FLAG_REMOTE_BASIC 0x00000003
#define DIGCDP_FLAG_REMOTE_ADVANCED 0x00000004

  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassDevPropertySheetsA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,LPPROPSHEETHEADERA PropertySheetHeader,DWORD PropertySheetHeaderPageListSize,PDWORD RequiredSize,DWORD PropertySheetType);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetClassDevPropertySheetsW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,LPPROPSHEETHEADERW PropertySheetHeader,DWORD PropertySheetHeaderPageListSize,PDWORD RequiredSize,DWORD PropertySheetType);

#define IDI_RESOURCEFIRST 159
#define IDI_RESOURCE 159
#define IDI_RESOURCELAST 161
#define IDI_RESOURCEOVERLAYFIRST 161
#define IDI_RESOURCEOVERLAYLAST 161
#define IDI_CONFLICT 161

#define IDI_CLASSICON_OVERLAYFIRST 500
#define IDI_CLASSICON_OVERLAYLAST 502
#define IDI_PROBLEM_OVL 500
#define IDI_DISABLED_OVL 501
#define IDI_FORCED_OVL 502

  WINSETUPAPI WINBOOL WINAPI SetupDiAskForOEMDisk(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiSelectOEMDrv(HWND hwndParent,HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiClassNameFromGuidA(CONST GUID *ClassGuid,PSTR ClassName,DWORD ClassNameSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiClassNameFromGuidW(CONST GUID *ClassGuid,PWSTR ClassName,DWORD ClassNameSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiClassNameFromGuidExA(CONST GUID *ClassGuid,PSTR ClassName,DWORD ClassNameSize,PDWORD RequiredSize,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiClassNameFromGuidExW(CONST GUID *ClassGuid,PWSTR ClassName,DWORD ClassNameSize,PDWORD RequiredSize,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiClassGuidsFromNameA(PCSTR ClassName,LPGUID ClassGuidList,DWORD ClassGuidListSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiClassGuidsFromNameW(PCWSTR ClassName,LPGUID ClassGuidList,DWORD ClassGuidListSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiClassGuidsFromNameExA(PCSTR ClassName,LPGUID ClassGuidList,DWORD ClassGuidListSize,PDWORD RequiredSize,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiClassGuidsFromNameExW(PCWSTR ClassName,LPGUID ClassGuidList,DWORD ClassGuidListSize,PDWORD RequiredSize,PCWSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetHwProfileFriendlyNameA(DWORD HwProfile,PSTR FriendlyName,DWORD FriendlyNameSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetHwProfileFriendlyNameW(DWORD HwProfile,PWSTR FriendlyName,DWORD FriendlyNameSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetHwProfileFriendlyNameExA(DWORD HwProfile,PSTR FriendlyName,DWORD FriendlyNameSize,PDWORD RequiredSize,PCSTR MachineName,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetHwProfileFriendlyNameExW(DWORD HwProfile,PWSTR FriendlyName,DWORD FriendlyNameSize,PDWORD RequiredSize,PCWSTR MachineName,PVOID Reserved);

#define SPWPT_SELECTDEVICE 0x00000001
#define SPWP_USE_DEVINFO_DATA 0x00000001

  WINSETUPAPI HPROPSHEETPAGE WINAPI SetupDiGetWizardPage(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PSP_INSTALLWIZARD_DATA InstallWizardData,DWORD PageType,DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetSelectedDevice(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiSetSelectedDevice(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetActualModelsSectionA(PINFCONTEXT Context,PSP_ALTPLATFORM_INFO AlternatePlatformInfo,PSTR InfSectionWithExt,DWORD InfSectionWithExtSize,PDWORD RequiredSize,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetActualModelsSectionW(PINFCONTEXT Context,PSP_ALTPLATFORM_INFO AlternatePlatformInfo,PWSTR InfSectionWithExt,DWORD InfSectionWithExtSize,PDWORD RequiredSize,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetActualSectionToInstallA(HINF InfHandle,PCSTR InfSectionName,PSTR InfSectionWithExt,DWORD InfSectionWithExtSize,PDWORD RequiredSize,PSTR *Extension);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetActualSectionToInstallW(HINF InfHandle,PCWSTR InfSectionName,PWSTR InfSectionWithExt,DWORD InfSectionWithExtSize,PDWORD RequiredSize,PWSTR *Extension);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetActualSectionToInstallExA(HINF InfHandle,PCSTR InfSectionName,PSP_ALTPLATFORM_INFO AlternatePlatformInfo,PSTR InfSectionWithExt,DWORD InfSectionWithExtSize,PDWORD RequiredSize,PSTR *Extension,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetActualSectionToInstallExW(HINF InfHandle,PCWSTR InfSectionName,PSP_ALTPLATFORM_INFO AlternatePlatformInfo,PWSTR InfSectionWithExt,DWORD InfSectionWithExtSize,PDWORD RequiredSize,PWSTR *Extension,PVOID Reserved);
  WINSETUPAPI WINBOOL WINAPI SetupEnumInfSectionsA (HINF InfHandle,UINT Index,PSTR Buffer,UINT Size,UINT *SizeNeeded);
  WINSETUPAPI WINBOOL WINAPI SetupEnumInfSectionsW (HINF InfHandle,UINT Index,PWSTR Buffer,UINT Size,UINT *SizeNeeded);

  typedef struct _SP_INF_SIGNER_INFO_A {
    DWORD cbSize;
    CHAR CatalogFile[MAX_PATH];
    CHAR DigitalSigner[MAX_PATH];
    CHAR DigitalSignerVersion[MAX_PATH];
  } SP_INF_SIGNER_INFO_A,*PSP_INF_SIGNER_INFO_A;

  typedef struct _SP_INF_SIGNER_INFO_W {
    DWORD cbSize;
    WCHAR CatalogFile[MAX_PATH];
    WCHAR DigitalSigner[MAX_PATH];
    WCHAR DigitalSignerVersion[MAX_PATH];
  } SP_INF_SIGNER_INFO_W,*PSP_INF_SIGNER_INFO_W;

  __MINGW_TYPEDEF_UAW(SP_INF_SIGNER_INFO)
  __MINGW_TYPEDEF_UAW(PSP_INF_SIGNER_INFO)

#define SetupVerifyInfFile __MINGW_NAME_AW(SetupVerifyInfFile)
#define SetupDiGetCustomDeviceProperty __MINGW_NAME_AW(SetupDiGetCustomDeviceProperty)
#define SetupConfigureWmiFromInfSection __MINGW_NAME_AW(SetupConfigureWmiFromInfSection)

  WINSETUPAPI WINBOOL WINAPI SetupVerifyInfFileA(PCSTR InfName,PSP_ALTPLATFORM_INFO AltPlatformInfo,PSP_INF_SIGNER_INFO_A InfSignerInfo);
  WINSETUPAPI WINBOOL WINAPI SetupVerifyInfFileW(PCWSTR InfName,PSP_ALTPLATFORM_INFO AltPlatformInfo,PSP_INF_SIGNER_INFO_W InfSignerInfo);

#define DICUSTOMDEVPROP_MERGE_MULTISZ 0x00000001

  WINSETUPAPI WINBOOL WINAPI SetupDiGetCustomDevicePropertyA(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PCSTR CustomPropertyName,DWORD Flags,PDWORD PropertyRegDataType,PBYTE PropertyBuffer,DWORD PropertyBufferSize,PDWORD RequiredSize);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetCustomDevicePropertyW(HDEVINFO DeviceInfoSet,PSP_DEVINFO_DATA DeviceInfoData,PCWSTR CustomPropertyName,DWORD Flags,PDWORD PropertyRegDataType,PBYTE PropertyBuffer,DWORD PropertyBufferSize,PDWORD RequiredSize);

#define SCWMI_CLOBBER_SECURITY 0x00000001

  WINSETUPAPI WINBOOL WINAPI SetupConfigureWmiFromInfSectionA(HINF InfHandle,PCSTR SectionName,DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupConfigureWmiFromInfSectionW(HINF InfHandle,PCWSTR SectionName,DWORD Flags);

#if _WIN32_WINNT >= 0x0600
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDevicePropertyW(HDEVINFO DeviceInfoSet, PSP_DEVINFO_DATA DeviceInfoData, const DEVPROPKEY *PropertyKey, DEVPROPTYPE *PropertyType, PBYTE PropertyBuffer, DWORD PropertyBufferSize, PDWORD RequiredSize, DWORD Flags);
  WINSETUPAPI WINBOOL WINAPI SetupDiGetDevicePropertyKeys(HDEVINFO DeviceInfoSet, PSP_DEVINFO_DATA DeviceInfoData, DEVPROPKEY *PropertyKeyArray, DWORD PropertyKeyCount, PDWORD RequiredPropertyKeyCount, DWORD Flags);
#endif

#ifdef __cplusplus
}
#endif

#include <poppack.h>
#endif
