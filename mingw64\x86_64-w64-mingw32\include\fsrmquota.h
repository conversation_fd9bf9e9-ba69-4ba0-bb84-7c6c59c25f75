/*** Autogenerated by WIDL 1.6 from include/fsrmquota.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __fsrmquota_h__
#define __fsrmquota_h__

/* Forward declarations */

#ifndef __IFsrmQuotaManager_FWD_DEFINED__
#define __IFsrmQuotaManager_FWD_DEFINED__
typedef interface IFsrmQuotaManager IFsrmQuotaManager;
#endif

#ifndef __IFsrmQuotaTemplateManager_FWD_DEFINED__
#define __IFsrmQuotaTemplateManager_FWD_DEFINED__
typedef interface IFsrmQuotaTemplateManager IFsrmQuotaTemplateManager;
#endif

#ifndef __IFsrmQuotaBase_FWD_DEFINED__
#define __IFsrmQuotaBase_FWD_DEFINED__
typedef interface IFsrmQuotaBase IFsrmQuotaBase;
#endif

#ifndef __IFsrmQuotaObject_FWD_DEFINED__
#define __IFsrmQuotaObject_FWD_DEFINED__
typedef interface IFsrmQuotaObject IFsrmQuotaObject;
#endif

#ifndef __IFsrmQuotaTemplate_FWD_DEFINED__
#define __IFsrmQuotaTemplate_FWD_DEFINED__
typedef interface IFsrmQuotaTemplate IFsrmQuotaTemplate;
#endif

#ifndef __IFsrmQuota_FWD_DEFINED__
#define __IFsrmQuota_FWD_DEFINED__
typedef interface IFsrmQuota IFsrmQuota;
#endif

#ifndef __IFsrmAutoApplyQuota_FWD_DEFINED__
#define __IFsrmAutoApplyQuota_FWD_DEFINED__
typedef interface IFsrmAutoApplyQuota IFsrmAutoApplyQuota;
#endif

#ifndef __IFsrmQuotaManagerEx_FWD_DEFINED__
#define __IFsrmQuotaManagerEx_FWD_DEFINED__
typedef interface IFsrmQuotaManagerEx IFsrmQuotaManagerEx;
#endif

#ifndef __IFsrmQuotaTemplateImported_FWD_DEFINED__
#define __IFsrmQuotaTemplateImported_FWD_DEFINED__
typedef interface IFsrmQuotaTemplateImported IFsrmQuotaTemplateImported;
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <fsrmenums.h>
#include <fsrm.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IFsrmAutoApplyQuota_FWD_DEFINED__
#define __IFsrmAutoApplyQuota_FWD_DEFINED__
typedef interface IFsrmAutoApplyQuota IFsrmAutoApplyQuota;
#endif

#ifndef __IFsrmQuota_FWD_DEFINED__
#define __IFsrmQuota_FWD_DEFINED__
typedef interface IFsrmQuota IFsrmQuota;
#endif

#ifndef __IFsrmQuotaBase_FWD_DEFINED__
#define __IFsrmQuotaBase_FWD_DEFINED__
typedef interface IFsrmQuotaBase IFsrmQuotaBase;
#endif

#ifndef __IFsrmQuotaObject_FWD_DEFINED__
#define __IFsrmQuotaObject_FWD_DEFINED__
typedef interface IFsrmQuotaObject IFsrmQuotaObject;
#endif

#ifndef __IFsrmQuotaManager_FWD_DEFINED__
#define __IFsrmQuotaManager_FWD_DEFINED__
typedef interface IFsrmQuotaManager IFsrmQuotaManager;
#endif

#ifndef __IFsrmQuotaTemplate_FWD_DEFINED__
#define __IFsrmQuotaTemplate_FWD_DEFINED__
typedef interface IFsrmQuotaTemplate IFsrmQuotaTemplate;
#endif

#ifndef __IFsrmQuotaTemplateImported_FWD_DEFINED__
#define __IFsrmQuotaTemplateImported_FWD_DEFINED__
typedef interface IFsrmQuotaTemplateImported IFsrmQuotaTemplateImported;
#endif

#ifndef __IFsrmQuotaTemplateManager_FWD_DEFINED__
#define __IFsrmQuotaTemplateManager_FWD_DEFINED__
typedef interface IFsrmQuotaTemplateManager IFsrmQuotaTemplateManager;
#endif

typedef LONG FSRM_QUOTA_THRESHOLD;
#define FSRM_DISPID_QUOTA_BASE (FSRM_DISPID_FEATURE_QUOTA | 0x100000)

#define FSRM_DISPID_QUOTA_OBJECT (FSRM_DISPID_QUOTA_BASE | 0x10000)

#define FSRM_DISPID_QUOTA (FSRM_DISPID_QUOTA_OBJECT | 0x1000)

#define FSRM_DISPID_AUTOAPPLYQUOTA (FSRM_DISPID_QUOTA_OBJECT | 0x2000)

#define FSRM_DISPID_QUOTA_TEMPLATE (FSRM_DISPID_QUOTA_BASE | 0x20000)

#define FSRM_DISPID_QUOTA_TEMPLATE_IMPORTED (FSRM_DISPID_QUOTA_TEMPLATE | 0x1000)

#define FSRM_DISPID_QUOTA_MANAGER (FSRM_DISPID_FEATURE_QUOTA | 0x200000)

#define FSRM_DISPID_QUOTA_TEMPLATE_MANAGER (FSRM_DISPID_FEATURE_QUOTA | 0x300000)

#define FSRM_DISPID_QUOTA_MANAGER_EX (FSRM_DISPID_FEATURE_QUOTA | 0x400000)

#define FsrmMaxNumberThresholds (16)

#define FsrmMinThresholdValue (1)

#define FsrmMaxThresholdValue (250)

#define FsrmMinQuotaLimit (1024)

#define FsrmMaxExcludeFolders (32)

/*****************************************************************************
 * IFsrmQuotaManager interface
 */
#ifndef __IFsrmQuotaManager_INTERFACE_DEFINED__
#define __IFsrmQuotaManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmQuotaManager, 0x8bb68c7d, 0x19d8, 0x4ffb, 0x80,0x9e, 0xbe,0x4f,0xc1,0x73,0x40,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8bb68c7d-19d8-4ffb-809e-be4fc1734014")
IFsrmQuotaManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ActionVariables(
        SAFEARRAY **variables) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ActionVariableDescriptions(
        SAFEARRAY **descriptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateQuota(
        BSTR path,
        IFsrmQuota **quota) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAutoApplyQuota(
        BSTR quotaTemplateName,
        BSTR path,
        IFsrmAutoApplyQuota **quota) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetQuota(
        BSTR path,
        IFsrmQuota **quota) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAutoApplyQuota(
        BSTR path,
        IFsrmAutoApplyQuota **quota) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestrictiveQuota(
        BSTR path,
        IFsrmQuota **quota) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumQuotas(
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumAutoApplyQuotas(
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumEffectiveQuotas(
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas) = 0;

    virtual HRESULT STDMETHODCALLTYPE Scan(
        BSTR strPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateQuotaCollection(
        IFsrmCommittableCollection **collection) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmQuotaManager, 0x8bb68c7d, 0x19d8, 0x4ffb, 0x80,0x9e, 0xbe,0x4f,0xc1,0x73,0x40,0x14)
#endif
#else
typedef struct IFsrmQuotaManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmQuotaManager* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmQuotaManager* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmQuotaManager* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmQuotaManager* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmQuotaManager* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmQuotaManager* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmQuotaManager* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmQuotaManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ActionVariables)(
        IFsrmQuotaManager* This,
        SAFEARRAY **variables);

    HRESULT (STDMETHODCALLTYPE *get_ActionVariableDescriptions)(
        IFsrmQuotaManager* This,
        SAFEARRAY **descriptions);

    HRESULT (STDMETHODCALLTYPE *CreateQuota)(
        IFsrmQuotaManager* This,
        BSTR path,
        IFsrmQuota **quota);

    HRESULT (STDMETHODCALLTYPE *CreateAutoApplyQuota)(
        IFsrmQuotaManager* This,
        BSTR quotaTemplateName,
        BSTR path,
        IFsrmAutoApplyQuota **quota);

    HRESULT (STDMETHODCALLTYPE *GetQuota)(
        IFsrmQuotaManager* This,
        BSTR path,
        IFsrmQuota **quota);

    HRESULT (STDMETHODCALLTYPE *GetAutoApplyQuota)(
        IFsrmQuotaManager* This,
        BSTR path,
        IFsrmAutoApplyQuota **quota);

    HRESULT (STDMETHODCALLTYPE *GetRestrictiveQuota)(
        IFsrmQuotaManager* This,
        BSTR path,
        IFsrmQuota **quota);

    HRESULT (STDMETHODCALLTYPE *EnumQuotas)(
        IFsrmQuotaManager* This,
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas);

    HRESULT (STDMETHODCALLTYPE *EnumAutoApplyQuotas)(
        IFsrmQuotaManager* This,
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas);

    HRESULT (STDMETHODCALLTYPE *EnumEffectiveQuotas)(
        IFsrmQuotaManager* This,
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas);

    HRESULT (STDMETHODCALLTYPE *Scan)(
        IFsrmQuotaManager* This,
        BSTR strPath);

    HRESULT (STDMETHODCALLTYPE *CreateQuotaCollection)(
        IFsrmQuotaManager* This,
        IFsrmCommittableCollection **collection);

    END_INTERFACE
} IFsrmQuotaManagerVtbl;
interface IFsrmQuotaManager {
    CONST_VTBL IFsrmQuotaManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmQuotaManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmQuotaManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmQuotaManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmQuotaManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmQuotaManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmQuotaManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmQuotaManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmQuotaManager methods ***/
#define IFsrmQuotaManager_get_ActionVariables(This,variables) (This)->lpVtbl->get_ActionVariables(This,variables)
#define IFsrmQuotaManager_get_ActionVariableDescriptions(This,descriptions) (This)->lpVtbl->get_ActionVariableDescriptions(This,descriptions)
#define IFsrmQuotaManager_CreateQuota(This,path,quota) (This)->lpVtbl->CreateQuota(This,path,quota)
#define IFsrmQuotaManager_CreateAutoApplyQuota(This,quotaTemplateName,path,quota) (This)->lpVtbl->CreateAutoApplyQuota(This,quotaTemplateName,path,quota)
#define IFsrmQuotaManager_GetQuota(This,path,quota) (This)->lpVtbl->GetQuota(This,path,quota)
#define IFsrmQuotaManager_GetAutoApplyQuota(This,path,quota) (This)->lpVtbl->GetAutoApplyQuota(This,path,quota)
#define IFsrmQuotaManager_GetRestrictiveQuota(This,path,quota) (This)->lpVtbl->GetRestrictiveQuota(This,path,quota)
#define IFsrmQuotaManager_EnumQuotas(This,path,options,quotas) (This)->lpVtbl->EnumQuotas(This,path,options,quotas)
#define IFsrmQuotaManager_EnumAutoApplyQuotas(This,path,options,quotas) (This)->lpVtbl->EnumAutoApplyQuotas(This,path,options,quotas)
#define IFsrmQuotaManager_EnumEffectiveQuotas(This,path,options,quotas) (This)->lpVtbl->EnumEffectiveQuotas(This,path,options,quotas)
#define IFsrmQuotaManager_Scan(This,strPath) (This)->lpVtbl->Scan(This,strPath)
#define IFsrmQuotaManager_CreateQuotaCollection(This,collection) (This)->lpVtbl->CreateQuotaCollection(This,collection)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmQuotaManager_QueryInterface(IFsrmQuotaManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmQuotaManager_AddRef(IFsrmQuotaManager* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmQuotaManager_Release(IFsrmQuotaManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmQuotaManager_GetTypeInfoCount(IFsrmQuotaManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_GetTypeInfo(IFsrmQuotaManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_GetIDsOfNames(IFsrmQuotaManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_Invoke(IFsrmQuotaManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmQuotaManager methods ***/
static FORCEINLINE HRESULT IFsrmQuotaManager_get_ActionVariables(IFsrmQuotaManager* This,SAFEARRAY **variables) {
    return This->lpVtbl->get_ActionVariables(This,variables);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_get_ActionVariableDescriptions(IFsrmQuotaManager* This,SAFEARRAY **descriptions) {
    return This->lpVtbl->get_ActionVariableDescriptions(This,descriptions);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_CreateQuota(IFsrmQuotaManager* This,BSTR path,IFsrmQuota **quota) {
    return This->lpVtbl->CreateQuota(This,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_CreateAutoApplyQuota(IFsrmQuotaManager* This,BSTR quotaTemplateName,BSTR path,IFsrmAutoApplyQuota **quota) {
    return This->lpVtbl->CreateAutoApplyQuota(This,quotaTemplateName,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_GetQuota(IFsrmQuotaManager* This,BSTR path,IFsrmQuota **quota) {
    return This->lpVtbl->GetQuota(This,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_GetAutoApplyQuota(IFsrmQuotaManager* This,BSTR path,IFsrmAutoApplyQuota **quota) {
    return This->lpVtbl->GetAutoApplyQuota(This,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_GetRestrictiveQuota(IFsrmQuotaManager* This,BSTR path,IFsrmQuota **quota) {
    return This->lpVtbl->GetRestrictiveQuota(This,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_EnumQuotas(IFsrmQuotaManager* This,BSTR path,FsrmEnumOptions options,IFsrmCommittableCollection **quotas) {
    return This->lpVtbl->EnumQuotas(This,path,options,quotas);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_EnumAutoApplyQuotas(IFsrmQuotaManager* This,BSTR path,FsrmEnumOptions options,IFsrmCommittableCollection **quotas) {
    return This->lpVtbl->EnumAutoApplyQuotas(This,path,options,quotas);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_EnumEffectiveQuotas(IFsrmQuotaManager* This,BSTR path,FsrmEnumOptions options,IFsrmCommittableCollection **quotas) {
    return This->lpVtbl->EnumEffectiveQuotas(This,path,options,quotas);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_Scan(IFsrmQuotaManager* This,BSTR strPath) {
    return This->lpVtbl->Scan(This,strPath);
}
static FORCEINLINE HRESULT IFsrmQuotaManager_CreateQuotaCollection(IFsrmQuotaManager* This,IFsrmCommittableCollection **collection) {
    return This->lpVtbl->CreateQuotaCollection(This,collection);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_get_ActionVariables_Proxy(
    IFsrmQuotaManager* This,
    SAFEARRAY **variables);
void __RPC_STUB IFsrmQuotaManager_get_ActionVariables_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_get_ActionVariableDescriptions_Proxy(
    IFsrmQuotaManager* This,
    SAFEARRAY **descriptions);
void __RPC_STUB IFsrmQuotaManager_get_ActionVariableDescriptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_CreateQuota_Proxy(
    IFsrmQuotaManager* This,
    BSTR path,
    IFsrmQuota **quota);
void __RPC_STUB IFsrmQuotaManager_CreateQuota_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_CreateAutoApplyQuota_Proxy(
    IFsrmQuotaManager* This,
    BSTR quotaTemplateName,
    BSTR path,
    IFsrmAutoApplyQuota **quota);
void __RPC_STUB IFsrmQuotaManager_CreateAutoApplyQuota_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_GetQuota_Proxy(
    IFsrmQuotaManager* This,
    BSTR path,
    IFsrmQuota **quota);
void __RPC_STUB IFsrmQuotaManager_GetQuota_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_GetAutoApplyQuota_Proxy(
    IFsrmQuotaManager* This,
    BSTR path,
    IFsrmAutoApplyQuota **quota);
void __RPC_STUB IFsrmQuotaManager_GetAutoApplyQuota_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_GetRestrictiveQuota_Proxy(
    IFsrmQuotaManager* This,
    BSTR path,
    IFsrmQuota **quota);
void __RPC_STUB IFsrmQuotaManager_GetRestrictiveQuota_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_EnumQuotas_Proxy(
    IFsrmQuotaManager* This,
    BSTR path,
    FsrmEnumOptions options,
    IFsrmCommittableCollection **quotas);
void __RPC_STUB IFsrmQuotaManager_EnumQuotas_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_EnumAutoApplyQuotas_Proxy(
    IFsrmQuotaManager* This,
    BSTR path,
    FsrmEnumOptions options,
    IFsrmCommittableCollection **quotas);
void __RPC_STUB IFsrmQuotaManager_EnumAutoApplyQuotas_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_EnumEffectiveQuotas_Proxy(
    IFsrmQuotaManager* This,
    BSTR path,
    FsrmEnumOptions options,
    IFsrmCommittableCollection **quotas);
void __RPC_STUB IFsrmQuotaManager_EnumEffectiveQuotas_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_Scan_Proxy(
    IFsrmQuotaManager* This,
    BSTR strPath);
void __RPC_STUB IFsrmQuotaManager_Scan_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaManager_CreateQuotaCollection_Proxy(
    IFsrmQuotaManager* This,
    IFsrmCommittableCollection **collection);
void __RPC_STUB IFsrmQuotaManager_CreateQuotaCollection_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmQuotaManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmQuotaTemplateManager interface
 */
#ifndef __IFsrmQuotaTemplateManager_INTERFACE_DEFINED__
#define __IFsrmQuotaTemplateManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmQuotaTemplateManager, 0x4173ac41, 0x172d, 0x4d52, 0x96,0x3c, 0xfd,0xc7,0xe4,0x15,0xf7,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4173ac41-172d-4d52-963c-fdc7e415f717")
IFsrmQuotaTemplateManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE CreateTemplate(
        IFsrmQuotaTemplate **quotaTemplate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTemplate(
        BSTR name,
        IFsrmQuotaTemplate **quotaTemplate) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumTemplates(
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotaTemplates) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExportTemplates(
        VARIANT *quotaTemplateNamesArray,
        BSTR *serializedQuotaTemplates) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportTemplates(
        BSTR serializedQuotaTemplates,
        VARIANT *quotaTemplateNamesArray,
        IFsrmCommittableCollection **quotaTemplates) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmQuotaTemplateManager, 0x4173ac41, 0x172d, 0x4d52, 0x96,0x3c, 0xfd,0xc7,0xe4,0x15,0xf7,0x17)
#endif
#else
typedef struct IFsrmQuotaTemplateManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmQuotaTemplateManager* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmQuotaTemplateManager* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmQuotaTemplateManager* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmQuotaTemplateManager* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmQuotaTemplateManager* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmQuotaTemplateManager* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmQuotaTemplateManager* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmQuotaTemplateManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTemplate)(
        IFsrmQuotaTemplateManager* This,
        IFsrmQuotaTemplate **quotaTemplate);

    HRESULT (STDMETHODCALLTYPE *GetTemplate)(
        IFsrmQuotaTemplateManager* This,
        BSTR name,
        IFsrmQuotaTemplate **quotaTemplate);

    HRESULT (STDMETHODCALLTYPE *EnumTemplates)(
        IFsrmQuotaTemplateManager* This,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotaTemplates);

    HRESULT (STDMETHODCALLTYPE *ExportTemplates)(
        IFsrmQuotaTemplateManager* This,
        VARIANT *quotaTemplateNamesArray,
        BSTR *serializedQuotaTemplates);

    HRESULT (STDMETHODCALLTYPE *ImportTemplates)(
        IFsrmQuotaTemplateManager* This,
        BSTR serializedQuotaTemplates,
        VARIANT *quotaTemplateNamesArray,
        IFsrmCommittableCollection **quotaTemplates);

    END_INTERFACE
} IFsrmQuotaTemplateManagerVtbl;
interface IFsrmQuotaTemplateManager {
    CONST_VTBL IFsrmQuotaTemplateManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmQuotaTemplateManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmQuotaTemplateManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmQuotaTemplateManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmQuotaTemplateManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmQuotaTemplateManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmQuotaTemplateManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmQuotaTemplateManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmQuotaTemplateManager methods ***/
#define IFsrmQuotaTemplateManager_CreateTemplate(This,quotaTemplate) (This)->lpVtbl->CreateTemplate(This,quotaTemplate)
#define IFsrmQuotaTemplateManager_GetTemplate(This,name,quotaTemplate) (This)->lpVtbl->GetTemplate(This,name,quotaTemplate)
#define IFsrmQuotaTemplateManager_EnumTemplates(This,options,quotaTemplates) (This)->lpVtbl->EnumTemplates(This,options,quotaTemplates)
#define IFsrmQuotaTemplateManager_ExportTemplates(This,quotaTemplateNamesArray,serializedQuotaTemplates) (This)->lpVtbl->ExportTemplates(This,quotaTemplateNamesArray,serializedQuotaTemplates)
#define IFsrmQuotaTemplateManager_ImportTemplates(This,serializedQuotaTemplates,quotaTemplateNamesArray,quotaTemplates) (This)->lpVtbl->ImportTemplates(This,serializedQuotaTemplates,quotaTemplateNamesArray,quotaTemplates)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_QueryInterface(IFsrmQuotaTemplateManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmQuotaTemplateManager_AddRef(IFsrmQuotaTemplateManager* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmQuotaTemplateManager_Release(IFsrmQuotaTemplateManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_GetTypeInfoCount(IFsrmQuotaTemplateManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_GetTypeInfo(IFsrmQuotaTemplateManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_GetIDsOfNames(IFsrmQuotaTemplateManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_Invoke(IFsrmQuotaTemplateManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmQuotaTemplateManager methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_CreateTemplate(IFsrmQuotaTemplateManager* This,IFsrmQuotaTemplate **quotaTemplate) {
    return This->lpVtbl->CreateTemplate(This,quotaTemplate);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_GetTemplate(IFsrmQuotaTemplateManager* This,BSTR name,IFsrmQuotaTemplate **quotaTemplate) {
    return This->lpVtbl->GetTemplate(This,name,quotaTemplate);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_EnumTemplates(IFsrmQuotaTemplateManager* This,FsrmEnumOptions options,IFsrmCommittableCollection **quotaTemplates) {
    return This->lpVtbl->EnumTemplates(This,options,quotaTemplates);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_ExportTemplates(IFsrmQuotaTemplateManager* This,VARIANT *quotaTemplateNamesArray,BSTR *serializedQuotaTemplates) {
    return This->lpVtbl->ExportTemplates(This,quotaTemplateNamesArray,serializedQuotaTemplates);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateManager_ImportTemplates(IFsrmQuotaTemplateManager* This,BSTR serializedQuotaTemplates,VARIANT *quotaTemplateNamesArray,IFsrmCommittableCollection **quotaTemplates) {
    return This->lpVtbl->ImportTemplates(This,serializedQuotaTemplates,quotaTemplateNamesArray,quotaTemplates);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplateManager_CreateTemplate_Proxy(
    IFsrmQuotaTemplateManager* This,
    IFsrmQuotaTemplate **quotaTemplate);
void __RPC_STUB IFsrmQuotaTemplateManager_CreateTemplate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplateManager_GetTemplate_Proxy(
    IFsrmQuotaTemplateManager* This,
    BSTR name,
    IFsrmQuotaTemplate **quotaTemplate);
void __RPC_STUB IFsrmQuotaTemplateManager_GetTemplate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplateManager_EnumTemplates_Proxy(
    IFsrmQuotaTemplateManager* This,
    FsrmEnumOptions options,
    IFsrmCommittableCollection **quotaTemplates);
void __RPC_STUB IFsrmQuotaTemplateManager_EnumTemplates_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplateManager_ExportTemplates_Proxy(
    IFsrmQuotaTemplateManager* This,
    VARIANT *quotaTemplateNamesArray,
    BSTR *serializedQuotaTemplates);
void __RPC_STUB IFsrmQuotaTemplateManager_ExportTemplates_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplateManager_ImportTemplates_Proxy(
    IFsrmQuotaTemplateManager* This,
    BSTR serializedQuotaTemplates,
    VARIANT *quotaTemplateNamesArray,
    IFsrmCommittableCollection **quotaTemplates);
void __RPC_STUB IFsrmQuotaTemplateManager_ImportTemplates_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmQuotaTemplateManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmQuotaBase interface
 */
#ifndef __IFsrmQuotaBase_INTERFACE_DEFINED__
#define __IFsrmQuotaBase_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmQuotaBase, 0x1568a795, 0x3924, 0x4118, 0xb7,0x4b, 0x68,0xd8,0xf0,0xfa,0x5d,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1568a795-3924-4118-b74b-68d8f0fa5daf")
IFsrmQuotaBase : public IFsrmObject
{
    virtual HRESULT STDMETHODCALLTYPE get_QuotaLimit(
        VARIANT *quotaLimit) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_QuotaLimit(
        VARIANT quotaLimit) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_QuotaFlags(
        LONG *quotaFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_QuotaFlags(
        LONG quotaFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Thresholds(
        SAFEARRAY **thresholds) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddThreshold(
        FSRM_QUOTA_THRESHOLD threshold) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteThreshold(
        FSRM_QUOTA_THRESHOLD threshold) = 0;

    virtual HRESULT STDMETHODCALLTYPE ModifyThreshold(
        FSRM_QUOTA_THRESHOLD threshold,
        FSRM_QUOTA_THRESHOLD newThreshold) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateThresholdAction(
        FSRM_QUOTA_THRESHOLD threshold,
        FsrmActionType actionType,
        IFsrmAction **action) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumThresholdActions(
        FSRM_QUOTA_THRESHOLD threshold,
        IFsrmCollection **actions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmQuotaBase, 0x1568a795, 0x3924, 0x4118, 0xb7,0x4b, 0x68,0xd8,0xf0,0xfa,0x5d,0xaf)
#endif
#else
typedef struct IFsrmQuotaBaseVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmQuotaBase* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmQuotaBase* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmQuotaBase* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmQuotaBase* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmQuotaBase* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmQuotaBase* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmQuotaBase* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmQuotaBase* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmQuotaBase* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmQuotaBase* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmQuotaBase* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmQuotaBase* This);

    /*** IFsrmQuotaBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_QuotaLimit)(
        IFsrmQuotaBase* This,
        VARIANT *quotaLimit);

    HRESULT (STDMETHODCALLTYPE *put_QuotaLimit)(
        IFsrmQuotaBase* This,
        VARIANT quotaLimit);

    HRESULT (STDMETHODCALLTYPE *get_QuotaFlags)(
        IFsrmQuotaBase* This,
        LONG *quotaFlags);

    HRESULT (STDMETHODCALLTYPE *put_QuotaFlags)(
        IFsrmQuotaBase* This,
        LONG quotaFlags);

    HRESULT (STDMETHODCALLTYPE *get_Thresholds)(
        IFsrmQuotaBase* This,
        SAFEARRAY **thresholds);

    HRESULT (STDMETHODCALLTYPE *AddThreshold)(
        IFsrmQuotaBase* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *DeleteThreshold)(
        IFsrmQuotaBase* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *ModifyThreshold)(
        IFsrmQuotaBase* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FSRM_QUOTA_THRESHOLD newThreshold);

    HRESULT (STDMETHODCALLTYPE *CreateThresholdAction)(
        IFsrmQuotaBase* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumThresholdActions)(
        IFsrmQuotaBase* This,
        FSRM_QUOTA_THRESHOLD threshold,
        IFsrmCollection **actions);

    END_INTERFACE
} IFsrmQuotaBaseVtbl;
interface IFsrmQuotaBase {
    CONST_VTBL IFsrmQuotaBaseVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmQuotaBase_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmQuotaBase_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmQuotaBase_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmQuotaBase_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmQuotaBase_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmQuotaBase_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmQuotaBase_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmQuotaBase_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmQuotaBase_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmQuotaBase_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmQuotaBase_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmQuotaBase_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmQuotaBase methods ***/
#define IFsrmQuotaBase_get_QuotaLimit(This,quotaLimit) (This)->lpVtbl->get_QuotaLimit(This,quotaLimit)
#define IFsrmQuotaBase_put_QuotaLimit(This,quotaLimit) (This)->lpVtbl->put_QuotaLimit(This,quotaLimit)
#define IFsrmQuotaBase_get_QuotaFlags(This,quotaFlags) (This)->lpVtbl->get_QuotaFlags(This,quotaFlags)
#define IFsrmQuotaBase_put_QuotaFlags(This,quotaFlags) (This)->lpVtbl->put_QuotaFlags(This,quotaFlags)
#define IFsrmQuotaBase_get_Thresholds(This,thresholds) (This)->lpVtbl->get_Thresholds(This,thresholds)
#define IFsrmQuotaBase_AddThreshold(This,threshold) (This)->lpVtbl->AddThreshold(This,threshold)
#define IFsrmQuotaBase_DeleteThreshold(This,threshold) (This)->lpVtbl->DeleteThreshold(This,threshold)
#define IFsrmQuotaBase_ModifyThreshold(This,threshold,newThreshold) (This)->lpVtbl->ModifyThreshold(This,threshold,newThreshold)
#define IFsrmQuotaBase_CreateThresholdAction(This,threshold,actionType,action) (This)->lpVtbl->CreateThresholdAction(This,threshold,actionType,action)
#define IFsrmQuotaBase_EnumThresholdActions(This,threshold,actions) (This)->lpVtbl->EnumThresholdActions(This,threshold,actions)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmQuotaBase_QueryInterface(IFsrmQuotaBase* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmQuotaBase_AddRef(IFsrmQuotaBase* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmQuotaBase_Release(IFsrmQuotaBase* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmQuotaBase_GetTypeInfoCount(IFsrmQuotaBase* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_GetTypeInfo(IFsrmQuotaBase* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_GetIDsOfNames(IFsrmQuotaBase* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_Invoke(IFsrmQuotaBase* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmQuotaBase_get_Id(IFsrmQuotaBase* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_get_Description(IFsrmQuotaBase* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_put_Description(IFsrmQuotaBase* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_Delete(IFsrmQuotaBase* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_Commit(IFsrmQuotaBase* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmQuotaBase methods ***/
static FORCEINLINE HRESULT IFsrmQuotaBase_get_QuotaLimit(IFsrmQuotaBase* This,VARIANT *quotaLimit) {
    return This->lpVtbl->get_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_put_QuotaLimit(IFsrmQuotaBase* This,VARIANT quotaLimit) {
    return This->lpVtbl->put_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_get_QuotaFlags(IFsrmQuotaBase* This,LONG *quotaFlags) {
    return This->lpVtbl->get_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_put_QuotaFlags(IFsrmQuotaBase* This,LONG quotaFlags) {
    return This->lpVtbl->put_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_get_Thresholds(IFsrmQuotaBase* This,SAFEARRAY **thresholds) {
    return This->lpVtbl->get_Thresholds(This,thresholds);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_AddThreshold(IFsrmQuotaBase* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->AddThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_DeleteThreshold(IFsrmQuotaBase* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->DeleteThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_ModifyThreshold(IFsrmQuotaBase* This,FSRM_QUOTA_THRESHOLD threshold,FSRM_QUOTA_THRESHOLD newThreshold) {
    return This->lpVtbl->ModifyThreshold(This,threshold,newThreshold);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_CreateThresholdAction(IFsrmQuotaBase* This,FSRM_QUOTA_THRESHOLD threshold,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateThresholdAction(This,threshold,actionType,action);
}
static FORCEINLINE HRESULT IFsrmQuotaBase_EnumThresholdActions(IFsrmQuotaBase* This,FSRM_QUOTA_THRESHOLD threshold,IFsrmCollection **actions) {
    return This->lpVtbl->EnumThresholdActions(This,threshold,actions);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_get_QuotaLimit_Proxy(
    IFsrmQuotaBase* This,
    VARIANT *quotaLimit);
void __RPC_STUB IFsrmQuotaBase_get_QuotaLimit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_put_QuotaLimit_Proxy(
    IFsrmQuotaBase* This,
    VARIANT quotaLimit);
void __RPC_STUB IFsrmQuotaBase_put_QuotaLimit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_get_QuotaFlags_Proxy(
    IFsrmQuotaBase* This,
    LONG *quotaFlags);
void __RPC_STUB IFsrmQuotaBase_get_QuotaFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_put_QuotaFlags_Proxy(
    IFsrmQuotaBase* This,
    LONG quotaFlags);
void __RPC_STUB IFsrmQuotaBase_put_QuotaFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_get_Thresholds_Proxy(
    IFsrmQuotaBase* This,
    SAFEARRAY **thresholds);
void __RPC_STUB IFsrmQuotaBase_get_Thresholds_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_AddThreshold_Proxy(
    IFsrmQuotaBase* This,
    FSRM_QUOTA_THRESHOLD threshold);
void __RPC_STUB IFsrmQuotaBase_AddThreshold_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_DeleteThreshold_Proxy(
    IFsrmQuotaBase* This,
    FSRM_QUOTA_THRESHOLD threshold);
void __RPC_STUB IFsrmQuotaBase_DeleteThreshold_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_ModifyThreshold_Proxy(
    IFsrmQuotaBase* This,
    FSRM_QUOTA_THRESHOLD threshold,
    FSRM_QUOTA_THRESHOLD newThreshold);
void __RPC_STUB IFsrmQuotaBase_ModifyThreshold_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_CreateThresholdAction_Proxy(
    IFsrmQuotaBase* This,
    FSRM_QUOTA_THRESHOLD threshold,
    FsrmActionType actionType,
    IFsrmAction **action);
void __RPC_STUB IFsrmQuotaBase_CreateThresholdAction_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaBase_EnumThresholdActions_Proxy(
    IFsrmQuotaBase* This,
    FSRM_QUOTA_THRESHOLD threshold,
    IFsrmCollection **actions);
void __RPC_STUB IFsrmQuotaBase_EnumThresholdActions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmQuotaBase_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmQuotaObject interface
 */
#ifndef __IFsrmQuotaObject_INTERFACE_DEFINED__
#define __IFsrmQuotaObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmQuotaObject, 0x42dc3511, 0x61d5, 0x48ae, 0xb6,0xdc, 0x59,0xfc,0x00,0xc0,0xa8,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42dc3511-61d5-48ae-b6dc-59fc00c0a8d6")
IFsrmQuotaObject : public IFsrmQuotaBase
{
    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserSid(
        BSTR *userSid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserAccount(
        BSTR *userAccount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SourceTemplateName(
        BSTR *quotaTemplateName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MatchesSourceTemplate(
        VARIANT_BOOL *matches) = 0;

    virtual HRESULT STDMETHODCALLTYPE ApplyTemplate(
        BSTR quotaTemplateName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmQuotaObject, 0x42dc3511, 0x61d5, 0x48ae, 0xb6,0xdc, 0x59,0xfc,0x00,0xc0,0xa8,0xd6)
#endif
#else
typedef struct IFsrmQuotaObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmQuotaObject* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmQuotaObject* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmQuotaObject* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmQuotaObject* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmQuotaObject* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmQuotaObject* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmQuotaObject* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmQuotaObject* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmQuotaObject* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmQuotaObject* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmQuotaObject* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmQuotaObject* This);

    /*** IFsrmQuotaBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_QuotaLimit)(
        IFsrmQuotaObject* This,
        VARIANT *quotaLimit);

    HRESULT (STDMETHODCALLTYPE *put_QuotaLimit)(
        IFsrmQuotaObject* This,
        VARIANT quotaLimit);

    HRESULT (STDMETHODCALLTYPE *get_QuotaFlags)(
        IFsrmQuotaObject* This,
        LONG *quotaFlags);

    HRESULT (STDMETHODCALLTYPE *put_QuotaFlags)(
        IFsrmQuotaObject* This,
        LONG quotaFlags);

    HRESULT (STDMETHODCALLTYPE *get_Thresholds)(
        IFsrmQuotaObject* This,
        SAFEARRAY **thresholds);

    HRESULT (STDMETHODCALLTYPE *AddThreshold)(
        IFsrmQuotaObject* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *DeleteThreshold)(
        IFsrmQuotaObject* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *ModifyThreshold)(
        IFsrmQuotaObject* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FSRM_QUOTA_THRESHOLD newThreshold);

    HRESULT (STDMETHODCALLTYPE *CreateThresholdAction)(
        IFsrmQuotaObject* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumThresholdActions)(
        IFsrmQuotaObject* This,
        FSRM_QUOTA_THRESHOLD threshold,
        IFsrmCollection **actions);

    /*** IFsrmQuotaObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IFsrmQuotaObject* This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *get_UserSid)(
        IFsrmQuotaObject* This,
        BSTR *userSid);

    HRESULT (STDMETHODCALLTYPE *get_UserAccount)(
        IFsrmQuotaObject* This,
        BSTR *userAccount);

    HRESULT (STDMETHODCALLTYPE *get_SourceTemplateName)(
        IFsrmQuotaObject* This,
        BSTR *quotaTemplateName);

    HRESULT (STDMETHODCALLTYPE *get_MatchesSourceTemplate)(
        IFsrmQuotaObject* This,
        VARIANT_BOOL *matches);

    HRESULT (STDMETHODCALLTYPE *ApplyTemplate)(
        IFsrmQuotaObject* This,
        BSTR quotaTemplateName);

    END_INTERFACE
} IFsrmQuotaObjectVtbl;
interface IFsrmQuotaObject {
    CONST_VTBL IFsrmQuotaObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmQuotaObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmQuotaObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmQuotaObject_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmQuotaObject_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmQuotaObject_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmQuotaObject_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmQuotaObject_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmQuotaObject_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmQuotaObject_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmQuotaObject_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmQuotaObject_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmQuotaObject_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmQuotaBase methods ***/
#define IFsrmQuotaObject_get_QuotaLimit(This,quotaLimit) (This)->lpVtbl->get_QuotaLimit(This,quotaLimit)
#define IFsrmQuotaObject_put_QuotaLimit(This,quotaLimit) (This)->lpVtbl->put_QuotaLimit(This,quotaLimit)
#define IFsrmQuotaObject_get_QuotaFlags(This,quotaFlags) (This)->lpVtbl->get_QuotaFlags(This,quotaFlags)
#define IFsrmQuotaObject_put_QuotaFlags(This,quotaFlags) (This)->lpVtbl->put_QuotaFlags(This,quotaFlags)
#define IFsrmQuotaObject_get_Thresholds(This,thresholds) (This)->lpVtbl->get_Thresholds(This,thresholds)
#define IFsrmQuotaObject_AddThreshold(This,threshold) (This)->lpVtbl->AddThreshold(This,threshold)
#define IFsrmQuotaObject_DeleteThreshold(This,threshold) (This)->lpVtbl->DeleteThreshold(This,threshold)
#define IFsrmQuotaObject_ModifyThreshold(This,threshold,newThreshold) (This)->lpVtbl->ModifyThreshold(This,threshold,newThreshold)
#define IFsrmQuotaObject_CreateThresholdAction(This,threshold,actionType,action) (This)->lpVtbl->CreateThresholdAction(This,threshold,actionType,action)
#define IFsrmQuotaObject_EnumThresholdActions(This,threshold,actions) (This)->lpVtbl->EnumThresholdActions(This,threshold,actions)
/*** IFsrmQuotaObject methods ***/
#define IFsrmQuotaObject_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define IFsrmQuotaObject_get_UserSid(This,userSid) (This)->lpVtbl->get_UserSid(This,userSid)
#define IFsrmQuotaObject_get_UserAccount(This,userAccount) (This)->lpVtbl->get_UserAccount(This,userAccount)
#define IFsrmQuotaObject_get_SourceTemplateName(This,quotaTemplateName) (This)->lpVtbl->get_SourceTemplateName(This,quotaTemplateName)
#define IFsrmQuotaObject_get_MatchesSourceTemplate(This,matches) (This)->lpVtbl->get_MatchesSourceTemplate(This,matches)
#define IFsrmQuotaObject_ApplyTemplate(This,quotaTemplateName) (This)->lpVtbl->ApplyTemplate(This,quotaTemplateName)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmQuotaObject_QueryInterface(IFsrmQuotaObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmQuotaObject_AddRef(IFsrmQuotaObject* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmQuotaObject_Release(IFsrmQuotaObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmQuotaObject_GetTypeInfoCount(IFsrmQuotaObject* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_GetTypeInfo(IFsrmQuotaObject* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_GetIDsOfNames(IFsrmQuotaObject* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_Invoke(IFsrmQuotaObject* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmQuotaObject_get_Id(IFsrmQuotaObject* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_get_Description(IFsrmQuotaObject* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_put_Description(IFsrmQuotaObject* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_Delete(IFsrmQuotaObject* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_Commit(IFsrmQuotaObject* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmQuotaBase methods ***/
static FORCEINLINE HRESULT IFsrmQuotaObject_get_QuotaLimit(IFsrmQuotaObject* This,VARIANT *quotaLimit) {
    return This->lpVtbl->get_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_put_QuotaLimit(IFsrmQuotaObject* This,VARIANT quotaLimit) {
    return This->lpVtbl->put_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_get_QuotaFlags(IFsrmQuotaObject* This,LONG *quotaFlags) {
    return This->lpVtbl->get_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_put_QuotaFlags(IFsrmQuotaObject* This,LONG quotaFlags) {
    return This->lpVtbl->put_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_get_Thresholds(IFsrmQuotaObject* This,SAFEARRAY **thresholds) {
    return This->lpVtbl->get_Thresholds(This,thresholds);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_AddThreshold(IFsrmQuotaObject* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->AddThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_DeleteThreshold(IFsrmQuotaObject* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->DeleteThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_ModifyThreshold(IFsrmQuotaObject* This,FSRM_QUOTA_THRESHOLD threshold,FSRM_QUOTA_THRESHOLD newThreshold) {
    return This->lpVtbl->ModifyThreshold(This,threshold,newThreshold);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_CreateThresholdAction(IFsrmQuotaObject* This,FSRM_QUOTA_THRESHOLD threshold,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateThresholdAction(This,threshold,actionType,action);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_EnumThresholdActions(IFsrmQuotaObject* This,FSRM_QUOTA_THRESHOLD threshold,IFsrmCollection **actions) {
    return This->lpVtbl->EnumThresholdActions(This,threshold,actions);
}
/*** IFsrmQuotaObject methods ***/
static FORCEINLINE HRESULT IFsrmQuotaObject_get_Path(IFsrmQuotaObject* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_get_UserSid(IFsrmQuotaObject* This,BSTR *userSid) {
    return This->lpVtbl->get_UserSid(This,userSid);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_get_UserAccount(IFsrmQuotaObject* This,BSTR *userAccount) {
    return This->lpVtbl->get_UserAccount(This,userAccount);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_get_SourceTemplateName(IFsrmQuotaObject* This,BSTR *quotaTemplateName) {
    return This->lpVtbl->get_SourceTemplateName(This,quotaTemplateName);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_get_MatchesSourceTemplate(IFsrmQuotaObject* This,VARIANT_BOOL *matches) {
    return This->lpVtbl->get_MatchesSourceTemplate(This,matches);
}
static FORCEINLINE HRESULT IFsrmQuotaObject_ApplyTemplate(IFsrmQuotaObject* This,BSTR quotaTemplateName) {
    return This->lpVtbl->ApplyTemplate(This,quotaTemplateName);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmQuotaObject_get_Path_Proxy(
    IFsrmQuotaObject* This,
    BSTR *path);
void __RPC_STUB IFsrmQuotaObject_get_Path_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaObject_get_UserSid_Proxy(
    IFsrmQuotaObject* This,
    BSTR *userSid);
void __RPC_STUB IFsrmQuotaObject_get_UserSid_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaObject_get_UserAccount_Proxy(
    IFsrmQuotaObject* This,
    BSTR *userAccount);
void __RPC_STUB IFsrmQuotaObject_get_UserAccount_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaObject_get_SourceTemplateName_Proxy(
    IFsrmQuotaObject* This,
    BSTR *quotaTemplateName);
void __RPC_STUB IFsrmQuotaObject_get_SourceTemplateName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaObject_get_MatchesSourceTemplate_Proxy(
    IFsrmQuotaObject* This,
    VARIANT_BOOL *matches);
void __RPC_STUB IFsrmQuotaObject_get_MatchesSourceTemplate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaObject_ApplyTemplate_Proxy(
    IFsrmQuotaObject* This,
    BSTR quotaTemplateName);
void __RPC_STUB IFsrmQuotaObject_ApplyTemplate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmQuotaObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmQuotaTemplate interface
 */
#ifndef __IFsrmQuotaTemplate_INTERFACE_DEFINED__
#define __IFsrmQuotaTemplate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmQuotaTemplate, 0xa2efab31, 0x295e, 0x46bb, 0xb9,0x76, 0xe8,0x6d,0x58,0xb5,0x2e,0x8b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a2efab31-295e-46bb-b976-e86d58b52e8b")
IFsrmQuotaTemplate : public IFsrmQuotaBase
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyTemplate(
        BSTR quotaTemplateName) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommitAndUpdateDerived(
        FsrmCommitOptions commitOptions,
        FsrmTemplateApplyOptions applyOptions,
        IFsrmDerivedObjectsResult **derivedObjectsResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmQuotaTemplate, 0xa2efab31, 0x295e, 0x46bb, 0xb9,0x76, 0xe8,0x6d,0x58,0xb5,0x2e,0x8b)
#endif
#else
typedef struct IFsrmQuotaTemplateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmQuotaTemplate* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmQuotaTemplate* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmQuotaTemplate* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmQuotaTemplate* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmQuotaTemplate* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmQuotaTemplate* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmQuotaTemplate* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmQuotaTemplate* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmQuotaTemplate* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmQuotaTemplate* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmQuotaTemplate* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmQuotaTemplate* This);

    /*** IFsrmQuotaBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_QuotaLimit)(
        IFsrmQuotaTemplate* This,
        VARIANT *quotaLimit);

    HRESULT (STDMETHODCALLTYPE *put_QuotaLimit)(
        IFsrmQuotaTemplate* This,
        VARIANT quotaLimit);

    HRESULT (STDMETHODCALLTYPE *get_QuotaFlags)(
        IFsrmQuotaTemplate* This,
        LONG *quotaFlags);

    HRESULT (STDMETHODCALLTYPE *put_QuotaFlags)(
        IFsrmQuotaTemplate* This,
        LONG quotaFlags);

    HRESULT (STDMETHODCALLTYPE *get_Thresholds)(
        IFsrmQuotaTemplate* This,
        SAFEARRAY **thresholds);

    HRESULT (STDMETHODCALLTYPE *AddThreshold)(
        IFsrmQuotaTemplate* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *DeleteThreshold)(
        IFsrmQuotaTemplate* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *ModifyThreshold)(
        IFsrmQuotaTemplate* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FSRM_QUOTA_THRESHOLD newThreshold);

    HRESULT (STDMETHODCALLTYPE *CreateThresholdAction)(
        IFsrmQuotaTemplate* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumThresholdActions)(
        IFsrmQuotaTemplate* This,
        FSRM_QUOTA_THRESHOLD threshold,
        IFsrmCollection **actions);

    /*** IFsrmQuotaTemplate methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmQuotaTemplate* This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmQuotaTemplate* This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *CopyTemplate)(
        IFsrmQuotaTemplate* This,
        BSTR quotaTemplateName);

    HRESULT (STDMETHODCALLTYPE *CommitAndUpdateDerived)(
        IFsrmQuotaTemplate* This,
        FsrmCommitOptions commitOptions,
        FsrmTemplateApplyOptions applyOptions,
        IFsrmDerivedObjectsResult **derivedObjectsResult);

    END_INTERFACE
} IFsrmQuotaTemplateVtbl;
interface IFsrmQuotaTemplate {
    CONST_VTBL IFsrmQuotaTemplateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmQuotaTemplate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmQuotaTemplate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmQuotaTemplate_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmQuotaTemplate_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmQuotaTemplate_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmQuotaTemplate_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmQuotaTemplate_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmQuotaTemplate_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmQuotaTemplate_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmQuotaTemplate_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmQuotaTemplate_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmQuotaTemplate_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmQuotaBase methods ***/
#define IFsrmQuotaTemplate_get_QuotaLimit(This,quotaLimit) (This)->lpVtbl->get_QuotaLimit(This,quotaLimit)
#define IFsrmQuotaTemplate_put_QuotaLimit(This,quotaLimit) (This)->lpVtbl->put_QuotaLimit(This,quotaLimit)
#define IFsrmQuotaTemplate_get_QuotaFlags(This,quotaFlags) (This)->lpVtbl->get_QuotaFlags(This,quotaFlags)
#define IFsrmQuotaTemplate_put_QuotaFlags(This,quotaFlags) (This)->lpVtbl->put_QuotaFlags(This,quotaFlags)
#define IFsrmQuotaTemplate_get_Thresholds(This,thresholds) (This)->lpVtbl->get_Thresholds(This,thresholds)
#define IFsrmQuotaTemplate_AddThreshold(This,threshold) (This)->lpVtbl->AddThreshold(This,threshold)
#define IFsrmQuotaTemplate_DeleteThreshold(This,threshold) (This)->lpVtbl->DeleteThreshold(This,threshold)
#define IFsrmQuotaTemplate_ModifyThreshold(This,threshold,newThreshold) (This)->lpVtbl->ModifyThreshold(This,threshold,newThreshold)
#define IFsrmQuotaTemplate_CreateThresholdAction(This,threshold,actionType,action) (This)->lpVtbl->CreateThresholdAction(This,threshold,actionType,action)
#define IFsrmQuotaTemplate_EnumThresholdActions(This,threshold,actions) (This)->lpVtbl->EnumThresholdActions(This,threshold,actions)
/*** IFsrmQuotaTemplate methods ***/
#define IFsrmQuotaTemplate_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmQuotaTemplate_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmQuotaTemplate_CopyTemplate(This,quotaTemplateName) (This)->lpVtbl->CopyTemplate(This,quotaTemplateName)
#define IFsrmQuotaTemplate_CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult) (This)->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplate_QueryInterface(IFsrmQuotaTemplate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmQuotaTemplate_AddRef(IFsrmQuotaTemplate* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmQuotaTemplate_Release(IFsrmQuotaTemplate* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplate_GetTypeInfoCount(IFsrmQuotaTemplate* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_GetTypeInfo(IFsrmQuotaTemplate* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_GetIDsOfNames(IFsrmQuotaTemplate* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_Invoke(IFsrmQuotaTemplate* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplate_get_Id(IFsrmQuotaTemplate* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_get_Description(IFsrmQuotaTemplate* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_put_Description(IFsrmQuotaTemplate* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_Delete(IFsrmQuotaTemplate* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_Commit(IFsrmQuotaTemplate* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmQuotaBase methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplate_get_QuotaLimit(IFsrmQuotaTemplate* This,VARIANT *quotaLimit) {
    return This->lpVtbl->get_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_put_QuotaLimit(IFsrmQuotaTemplate* This,VARIANT quotaLimit) {
    return This->lpVtbl->put_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_get_QuotaFlags(IFsrmQuotaTemplate* This,LONG *quotaFlags) {
    return This->lpVtbl->get_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_put_QuotaFlags(IFsrmQuotaTemplate* This,LONG quotaFlags) {
    return This->lpVtbl->put_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_get_Thresholds(IFsrmQuotaTemplate* This,SAFEARRAY **thresholds) {
    return This->lpVtbl->get_Thresholds(This,thresholds);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_AddThreshold(IFsrmQuotaTemplate* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->AddThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_DeleteThreshold(IFsrmQuotaTemplate* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->DeleteThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_ModifyThreshold(IFsrmQuotaTemplate* This,FSRM_QUOTA_THRESHOLD threshold,FSRM_QUOTA_THRESHOLD newThreshold) {
    return This->lpVtbl->ModifyThreshold(This,threshold,newThreshold);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_CreateThresholdAction(IFsrmQuotaTemplate* This,FSRM_QUOTA_THRESHOLD threshold,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateThresholdAction(This,threshold,actionType,action);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_EnumThresholdActions(IFsrmQuotaTemplate* This,FSRM_QUOTA_THRESHOLD threshold,IFsrmCollection **actions) {
    return This->lpVtbl->EnumThresholdActions(This,threshold,actions);
}
/*** IFsrmQuotaTemplate methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplate_get_Name(IFsrmQuotaTemplate* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_put_Name(IFsrmQuotaTemplate* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_CopyTemplate(IFsrmQuotaTemplate* This,BSTR quotaTemplateName) {
    return This->lpVtbl->CopyTemplate(This,quotaTemplateName);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplate_CommitAndUpdateDerived(IFsrmQuotaTemplate* This,FsrmCommitOptions commitOptions,FsrmTemplateApplyOptions applyOptions,IFsrmDerivedObjectsResult **derivedObjectsResult) {
    return This->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplate_get_Name_Proxy(
    IFsrmQuotaTemplate* This,
    BSTR *name);
void __RPC_STUB IFsrmQuotaTemplate_get_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplate_put_Name_Proxy(
    IFsrmQuotaTemplate* This,
    BSTR name);
void __RPC_STUB IFsrmQuotaTemplate_put_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplate_CopyTemplate_Proxy(
    IFsrmQuotaTemplate* This,
    BSTR quotaTemplateName);
void __RPC_STUB IFsrmQuotaTemplate_CopyTemplate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplate_CommitAndUpdateDerived_Proxy(
    IFsrmQuotaTemplate* This,
    FsrmCommitOptions commitOptions,
    FsrmTemplateApplyOptions applyOptions,
    IFsrmDerivedObjectsResult **derivedObjectsResult);
void __RPC_STUB IFsrmQuotaTemplate_CommitAndUpdateDerived_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmQuotaTemplate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmQuota interface
 */
#ifndef __IFsrmQuota_INTERFACE_DEFINED__
#define __IFsrmQuota_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmQuota, 0x377f739d, 0x9647, 0x4b8e, 0x97,0xd2, 0x5f,0xfc,0xe6,0xd7,0x59,0xcd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("377f739d-9647-4b8e-97d2-5ffce6d759cd")
IFsrmQuota : public IFsrmQuotaObject
{
    virtual HRESULT STDMETHODCALLTYPE get_QuotaUsed(
        VARIANT *used) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_QuotaPeakUsage(
        VARIANT *peakUsage) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_QuotaPeakUsageTime(
        DATE *peakUsageDateTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResetPeakUsage(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RefreshUsageProperties(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmQuota, 0x377f739d, 0x9647, 0x4b8e, 0x97,0xd2, 0x5f,0xfc,0xe6,0xd7,0x59,0xcd)
#endif
#else
typedef struct IFsrmQuotaVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmQuota* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmQuota* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmQuota* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmQuota* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmQuota* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmQuota* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmQuota* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmQuota* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmQuota* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmQuota* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmQuota* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmQuota* This);

    /*** IFsrmQuotaBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_QuotaLimit)(
        IFsrmQuota* This,
        VARIANT *quotaLimit);

    HRESULT (STDMETHODCALLTYPE *put_QuotaLimit)(
        IFsrmQuota* This,
        VARIANT quotaLimit);

    HRESULT (STDMETHODCALLTYPE *get_QuotaFlags)(
        IFsrmQuota* This,
        LONG *quotaFlags);

    HRESULT (STDMETHODCALLTYPE *put_QuotaFlags)(
        IFsrmQuota* This,
        LONG quotaFlags);

    HRESULT (STDMETHODCALLTYPE *get_Thresholds)(
        IFsrmQuota* This,
        SAFEARRAY **thresholds);

    HRESULT (STDMETHODCALLTYPE *AddThreshold)(
        IFsrmQuota* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *DeleteThreshold)(
        IFsrmQuota* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *ModifyThreshold)(
        IFsrmQuota* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FSRM_QUOTA_THRESHOLD newThreshold);

    HRESULT (STDMETHODCALLTYPE *CreateThresholdAction)(
        IFsrmQuota* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumThresholdActions)(
        IFsrmQuota* This,
        FSRM_QUOTA_THRESHOLD threshold,
        IFsrmCollection **actions);

    /*** IFsrmQuotaObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IFsrmQuota* This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *get_UserSid)(
        IFsrmQuota* This,
        BSTR *userSid);

    HRESULT (STDMETHODCALLTYPE *get_UserAccount)(
        IFsrmQuota* This,
        BSTR *userAccount);

    HRESULT (STDMETHODCALLTYPE *get_SourceTemplateName)(
        IFsrmQuota* This,
        BSTR *quotaTemplateName);

    HRESULT (STDMETHODCALLTYPE *get_MatchesSourceTemplate)(
        IFsrmQuota* This,
        VARIANT_BOOL *matches);

    HRESULT (STDMETHODCALLTYPE *ApplyTemplate)(
        IFsrmQuota* This,
        BSTR quotaTemplateName);

    /*** IFsrmQuota methods ***/
    HRESULT (STDMETHODCALLTYPE *get_QuotaUsed)(
        IFsrmQuota* This,
        VARIANT *used);

    HRESULT (STDMETHODCALLTYPE *get_QuotaPeakUsage)(
        IFsrmQuota* This,
        VARIANT *peakUsage);

    HRESULT (STDMETHODCALLTYPE *get_QuotaPeakUsageTime)(
        IFsrmQuota* This,
        DATE *peakUsageDateTime);

    HRESULT (STDMETHODCALLTYPE *ResetPeakUsage)(
        IFsrmQuota* This);

    HRESULT (STDMETHODCALLTYPE *RefreshUsageProperties)(
        IFsrmQuota* This);

    END_INTERFACE
} IFsrmQuotaVtbl;
interface IFsrmQuota {
    CONST_VTBL IFsrmQuotaVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmQuota_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmQuota_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmQuota_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmQuota_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmQuota_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmQuota_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmQuota_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmQuota_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmQuota_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmQuota_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmQuota_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmQuota_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmQuotaBase methods ***/
#define IFsrmQuota_get_QuotaLimit(This,quotaLimit) (This)->lpVtbl->get_QuotaLimit(This,quotaLimit)
#define IFsrmQuota_put_QuotaLimit(This,quotaLimit) (This)->lpVtbl->put_QuotaLimit(This,quotaLimit)
#define IFsrmQuota_get_QuotaFlags(This,quotaFlags) (This)->lpVtbl->get_QuotaFlags(This,quotaFlags)
#define IFsrmQuota_put_QuotaFlags(This,quotaFlags) (This)->lpVtbl->put_QuotaFlags(This,quotaFlags)
#define IFsrmQuota_get_Thresholds(This,thresholds) (This)->lpVtbl->get_Thresholds(This,thresholds)
#define IFsrmQuota_AddThreshold(This,threshold) (This)->lpVtbl->AddThreshold(This,threshold)
#define IFsrmQuota_DeleteThreshold(This,threshold) (This)->lpVtbl->DeleteThreshold(This,threshold)
#define IFsrmQuota_ModifyThreshold(This,threshold,newThreshold) (This)->lpVtbl->ModifyThreshold(This,threshold,newThreshold)
#define IFsrmQuota_CreateThresholdAction(This,threshold,actionType,action) (This)->lpVtbl->CreateThresholdAction(This,threshold,actionType,action)
#define IFsrmQuota_EnumThresholdActions(This,threshold,actions) (This)->lpVtbl->EnumThresholdActions(This,threshold,actions)
/*** IFsrmQuotaObject methods ***/
#define IFsrmQuota_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define IFsrmQuota_get_UserSid(This,userSid) (This)->lpVtbl->get_UserSid(This,userSid)
#define IFsrmQuota_get_UserAccount(This,userAccount) (This)->lpVtbl->get_UserAccount(This,userAccount)
#define IFsrmQuota_get_SourceTemplateName(This,quotaTemplateName) (This)->lpVtbl->get_SourceTemplateName(This,quotaTemplateName)
#define IFsrmQuota_get_MatchesSourceTemplate(This,matches) (This)->lpVtbl->get_MatchesSourceTemplate(This,matches)
#define IFsrmQuota_ApplyTemplate(This,quotaTemplateName) (This)->lpVtbl->ApplyTemplate(This,quotaTemplateName)
/*** IFsrmQuota methods ***/
#define IFsrmQuota_get_QuotaUsed(This,used) (This)->lpVtbl->get_QuotaUsed(This,used)
#define IFsrmQuota_get_QuotaPeakUsage(This,peakUsage) (This)->lpVtbl->get_QuotaPeakUsage(This,peakUsage)
#define IFsrmQuota_get_QuotaPeakUsageTime(This,peakUsageDateTime) (This)->lpVtbl->get_QuotaPeakUsageTime(This,peakUsageDateTime)
#define IFsrmQuota_ResetPeakUsage(This) (This)->lpVtbl->ResetPeakUsage(This)
#define IFsrmQuota_RefreshUsageProperties(This) (This)->lpVtbl->RefreshUsageProperties(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmQuota_QueryInterface(IFsrmQuota* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmQuota_AddRef(IFsrmQuota* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmQuota_Release(IFsrmQuota* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmQuota_GetTypeInfoCount(IFsrmQuota* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmQuota_GetTypeInfo(IFsrmQuota* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmQuota_GetIDsOfNames(IFsrmQuota* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmQuota_Invoke(IFsrmQuota* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmQuota_get_Id(IFsrmQuota* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmQuota_get_Description(IFsrmQuota* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuota_put_Description(IFsrmQuota* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuota_Delete(IFsrmQuota* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmQuota_Commit(IFsrmQuota* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmQuotaBase methods ***/
static FORCEINLINE HRESULT IFsrmQuota_get_QuotaLimit(IFsrmQuota* This,VARIANT *quotaLimit) {
    return This->lpVtbl->get_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuota_put_QuotaLimit(IFsrmQuota* This,VARIANT quotaLimit) {
    return This->lpVtbl->put_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuota_get_QuotaFlags(IFsrmQuota* This,LONG *quotaFlags) {
    return This->lpVtbl->get_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuota_put_QuotaFlags(IFsrmQuota* This,LONG quotaFlags) {
    return This->lpVtbl->put_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuota_get_Thresholds(IFsrmQuota* This,SAFEARRAY **thresholds) {
    return This->lpVtbl->get_Thresholds(This,thresholds);
}
static FORCEINLINE HRESULT IFsrmQuota_AddThreshold(IFsrmQuota* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->AddThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuota_DeleteThreshold(IFsrmQuota* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->DeleteThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuota_ModifyThreshold(IFsrmQuota* This,FSRM_QUOTA_THRESHOLD threshold,FSRM_QUOTA_THRESHOLD newThreshold) {
    return This->lpVtbl->ModifyThreshold(This,threshold,newThreshold);
}
static FORCEINLINE HRESULT IFsrmQuota_CreateThresholdAction(IFsrmQuota* This,FSRM_QUOTA_THRESHOLD threshold,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateThresholdAction(This,threshold,actionType,action);
}
static FORCEINLINE HRESULT IFsrmQuota_EnumThresholdActions(IFsrmQuota* This,FSRM_QUOTA_THRESHOLD threshold,IFsrmCollection **actions) {
    return This->lpVtbl->EnumThresholdActions(This,threshold,actions);
}
/*** IFsrmQuotaObject methods ***/
static FORCEINLINE HRESULT IFsrmQuota_get_Path(IFsrmQuota* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static FORCEINLINE HRESULT IFsrmQuota_get_UserSid(IFsrmQuota* This,BSTR *userSid) {
    return This->lpVtbl->get_UserSid(This,userSid);
}
static FORCEINLINE HRESULT IFsrmQuota_get_UserAccount(IFsrmQuota* This,BSTR *userAccount) {
    return This->lpVtbl->get_UserAccount(This,userAccount);
}
static FORCEINLINE HRESULT IFsrmQuota_get_SourceTemplateName(IFsrmQuota* This,BSTR *quotaTemplateName) {
    return This->lpVtbl->get_SourceTemplateName(This,quotaTemplateName);
}
static FORCEINLINE HRESULT IFsrmQuota_get_MatchesSourceTemplate(IFsrmQuota* This,VARIANT_BOOL *matches) {
    return This->lpVtbl->get_MatchesSourceTemplate(This,matches);
}
static FORCEINLINE HRESULT IFsrmQuota_ApplyTemplate(IFsrmQuota* This,BSTR quotaTemplateName) {
    return This->lpVtbl->ApplyTemplate(This,quotaTemplateName);
}
/*** IFsrmQuota methods ***/
static FORCEINLINE HRESULT IFsrmQuota_get_QuotaUsed(IFsrmQuota* This,VARIANT *used) {
    return This->lpVtbl->get_QuotaUsed(This,used);
}
static FORCEINLINE HRESULT IFsrmQuota_get_QuotaPeakUsage(IFsrmQuota* This,VARIANT *peakUsage) {
    return This->lpVtbl->get_QuotaPeakUsage(This,peakUsage);
}
static FORCEINLINE HRESULT IFsrmQuota_get_QuotaPeakUsageTime(IFsrmQuota* This,DATE *peakUsageDateTime) {
    return This->lpVtbl->get_QuotaPeakUsageTime(This,peakUsageDateTime);
}
static FORCEINLINE HRESULT IFsrmQuota_ResetPeakUsage(IFsrmQuota* This) {
    return This->lpVtbl->ResetPeakUsage(This);
}
static FORCEINLINE HRESULT IFsrmQuota_RefreshUsageProperties(IFsrmQuota* This) {
    return This->lpVtbl->RefreshUsageProperties(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmQuota_get_QuotaUsed_Proxy(
    IFsrmQuota* This,
    VARIANT *used);
void __RPC_STUB IFsrmQuota_get_QuotaUsed_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuota_get_QuotaPeakUsage_Proxy(
    IFsrmQuota* This,
    VARIANT *peakUsage);
void __RPC_STUB IFsrmQuota_get_QuotaPeakUsage_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuota_get_QuotaPeakUsageTime_Proxy(
    IFsrmQuota* This,
    DATE *peakUsageDateTime);
void __RPC_STUB IFsrmQuota_get_QuotaPeakUsageTime_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuota_ResetPeakUsage_Proxy(
    IFsrmQuota* This);
void __RPC_STUB IFsrmQuota_ResetPeakUsage_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuota_RefreshUsageProperties_Proxy(
    IFsrmQuota* This);
void __RPC_STUB IFsrmQuota_RefreshUsageProperties_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmQuota_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmAutoApplyQuota interface
 */
#ifndef __IFsrmAutoApplyQuota_INTERFACE_DEFINED__
#define __IFsrmAutoApplyQuota_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmAutoApplyQuota, 0xf82e5729, 0x6aba, 0x4740, 0xbf,0xc7, 0xc7,0xf5,0x8f,0x75,0xfb,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f82e5729-6aba-4740-bfc7-c7f58f75fb7b")
IFsrmAutoApplyQuota : public IFsrmQuotaObject
{
    virtual HRESULT STDMETHODCALLTYPE get_ExcludeFolders(
        SAFEARRAY **folders) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ExcludeFolders(
        SAFEARRAY *folders) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommitAndUpdateDerived(
        FsrmCommitOptions commitOptions,
        FsrmTemplateApplyOptions applyOptions,
        IFsrmDerivedObjectsResult **derivedObjectsResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmAutoApplyQuota, 0xf82e5729, 0x6aba, 0x4740, 0xbf,0xc7, 0xc7,0xf5,0x8f,0x75,0xfb,0x7b)
#endif
#else
typedef struct IFsrmAutoApplyQuotaVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmAutoApplyQuota* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmAutoApplyQuota* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmAutoApplyQuota* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmAutoApplyQuota* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmAutoApplyQuota* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmAutoApplyQuota* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmAutoApplyQuota* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmAutoApplyQuota* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmAutoApplyQuota* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmAutoApplyQuota* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmAutoApplyQuota* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmAutoApplyQuota* This);

    /*** IFsrmQuotaBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_QuotaLimit)(
        IFsrmAutoApplyQuota* This,
        VARIANT *quotaLimit);

    HRESULT (STDMETHODCALLTYPE *put_QuotaLimit)(
        IFsrmAutoApplyQuota* This,
        VARIANT quotaLimit);

    HRESULT (STDMETHODCALLTYPE *get_QuotaFlags)(
        IFsrmAutoApplyQuota* This,
        LONG *quotaFlags);

    HRESULT (STDMETHODCALLTYPE *put_QuotaFlags)(
        IFsrmAutoApplyQuota* This,
        LONG quotaFlags);

    HRESULT (STDMETHODCALLTYPE *get_Thresholds)(
        IFsrmAutoApplyQuota* This,
        SAFEARRAY **thresholds);

    HRESULT (STDMETHODCALLTYPE *AddThreshold)(
        IFsrmAutoApplyQuota* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *DeleteThreshold)(
        IFsrmAutoApplyQuota* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *ModifyThreshold)(
        IFsrmAutoApplyQuota* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FSRM_QUOTA_THRESHOLD newThreshold);

    HRESULT (STDMETHODCALLTYPE *CreateThresholdAction)(
        IFsrmAutoApplyQuota* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumThresholdActions)(
        IFsrmAutoApplyQuota* This,
        FSRM_QUOTA_THRESHOLD threshold,
        IFsrmCollection **actions);

    /*** IFsrmQuotaObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IFsrmAutoApplyQuota* This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *get_UserSid)(
        IFsrmAutoApplyQuota* This,
        BSTR *userSid);

    HRESULT (STDMETHODCALLTYPE *get_UserAccount)(
        IFsrmAutoApplyQuota* This,
        BSTR *userAccount);

    HRESULT (STDMETHODCALLTYPE *get_SourceTemplateName)(
        IFsrmAutoApplyQuota* This,
        BSTR *quotaTemplateName);

    HRESULT (STDMETHODCALLTYPE *get_MatchesSourceTemplate)(
        IFsrmAutoApplyQuota* This,
        VARIANT_BOOL *matches);

    HRESULT (STDMETHODCALLTYPE *ApplyTemplate)(
        IFsrmAutoApplyQuota* This,
        BSTR quotaTemplateName);

    /*** IFsrmAutoApplyQuota methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ExcludeFolders)(
        IFsrmAutoApplyQuota* This,
        SAFEARRAY **folders);

    HRESULT (STDMETHODCALLTYPE *put_ExcludeFolders)(
        IFsrmAutoApplyQuota* This,
        SAFEARRAY *folders);

    HRESULT (STDMETHODCALLTYPE *CommitAndUpdateDerived)(
        IFsrmAutoApplyQuota* This,
        FsrmCommitOptions commitOptions,
        FsrmTemplateApplyOptions applyOptions,
        IFsrmDerivedObjectsResult **derivedObjectsResult);

    END_INTERFACE
} IFsrmAutoApplyQuotaVtbl;
interface IFsrmAutoApplyQuota {
    CONST_VTBL IFsrmAutoApplyQuotaVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmAutoApplyQuota_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmAutoApplyQuota_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmAutoApplyQuota_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmAutoApplyQuota_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmAutoApplyQuota_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmAutoApplyQuota_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmAutoApplyQuota_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmAutoApplyQuota_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmAutoApplyQuota_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmAutoApplyQuota_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmAutoApplyQuota_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmAutoApplyQuota_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmQuotaBase methods ***/
#define IFsrmAutoApplyQuota_get_QuotaLimit(This,quotaLimit) (This)->lpVtbl->get_QuotaLimit(This,quotaLimit)
#define IFsrmAutoApplyQuota_put_QuotaLimit(This,quotaLimit) (This)->lpVtbl->put_QuotaLimit(This,quotaLimit)
#define IFsrmAutoApplyQuota_get_QuotaFlags(This,quotaFlags) (This)->lpVtbl->get_QuotaFlags(This,quotaFlags)
#define IFsrmAutoApplyQuota_put_QuotaFlags(This,quotaFlags) (This)->lpVtbl->put_QuotaFlags(This,quotaFlags)
#define IFsrmAutoApplyQuota_get_Thresholds(This,thresholds) (This)->lpVtbl->get_Thresholds(This,thresholds)
#define IFsrmAutoApplyQuota_AddThreshold(This,threshold) (This)->lpVtbl->AddThreshold(This,threshold)
#define IFsrmAutoApplyQuota_DeleteThreshold(This,threshold) (This)->lpVtbl->DeleteThreshold(This,threshold)
#define IFsrmAutoApplyQuota_ModifyThreshold(This,threshold,newThreshold) (This)->lpVtbl->ModifyThreshold(This,threshold,newThreshold)
#define IFsrmAutoApplyQuota_CreateThresholdAction(This,threshold,actionType,action) (This)->lpVtbl->CreateThresholdAction(This,threshold,actionType,action)
#define IFsrmAutoApplyQuota_EnumThresholdActions(This,threshold,actions) (This)->lpVtbl->EnumThresholdActions(This,threshold,actions)
/*** IFsrmQuotaObject methods ***/
#define IFsrmAutoApplyQuota_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define IFsrmAutoApplyQuota_get_UserSid(This,userSid) (This)->lpVtbl->get_UserSid(This,userSid)
#define IFsrmAutoApplyQuota_get_UserAccount(This,userAccount) (This)->lpVtbl->get_UserAccount(This,userAccount)
#define IFsrmAutoApplyQuota_get_SourceTemplateName(This,quotaTemplateName) (This)->lpVtbl->get_SourceTemplateName(This,quotaTemplateName)
#define IFsrmAutoApplyQuota_get_MatchesSourceTemplate(This,matches) (This)->lpVtbl->get_MatchesSourceTemplate(This,matches)
#define IFsrmAutoApplyQuota_ApplyTemplate(This,quotaTemplateName) (This)->lpVtbl->ApplyTemplate(This,quotaTemplateName)
/*** IFsrmAutoApplyQuota methods ***/
#define IFsrmAutoApplyQuota_get_ExcludeFolders(This,folders) (This)->lpVtbl->get_ExcludeFolders(This,folders)
#define IFsrmAutoApplyQuota_put_ExcludeFolders(This,folders) (This)->lpVtbl->put_ExcludeFolders(This,folders)
#define IFsrmAutoApplyQuota_CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult) (This)->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_QueryInterface(IFsrmAutoApplyQuota* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmAutoApplyQuota_AddRef(IFsrmAutoApplyQuota* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmAutoApplyQuota_Release(IFsrmAutoApplyQuota* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_GetTypeInfoCount(IFsrmAutoApplyQuota* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_GetTypeInfo(IFsrmAutoApplyQuota* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_GetIDsOfNames(IFsrmAutoApplyQuota* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_Invoke(IFsrmAutoApplyQuota* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_Id(IFsrmAutoApplyQuota* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_Description(IFsrmAutoApplyQuota* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_put_Description(IFsrmAutoApplyQuota* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_Delete(IFsrmAutoApplyQuota* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_Commit(IFsrmAutoApplyQuota* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmQuotaBase methods ***/
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_QuotaLimit(IFsrmAutoApplyQuota* This,VARIANT *quotaLimit) {
    return This->lpVtbl->get_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_put_QuotaLimit(IFsrmAutoApplyQuota* This,VARIANT quotaLimit) {
    return This->lpVtbl->put_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_QuotaFlags(IFsrmAutoApplyQuota* This,LONG *quotaFlags) {
    return This->lpVtbl->get_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_put_QuotaFlags(IFsrmAutoApplyQuota* This,LONG quotaFlags) {
    return This->lpVtbl->put_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_Thresholds(IFsrmAutoApplyQuota* This,SAFEARRAY **thresholds) {
    return This->lpVtbl->get_Thresholds(This,thresholds);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_AddThreshold(IFsrmAutoApplyQuota* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->AddThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_DeleteThreshold(IFsrmAutoApplyQuota* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->DeleteThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_ModifyThreshold(IFsrmAutoApplyQuota* This,FSRM_QUOTA_THRESHOLD threshold,FSRM_QUOTA_THRESHOLD newThreshold) {
    return This->lpVtbl->ModifyThreshold(This,threshold,newThreshold);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_CreateThresholdAction(IFsrmAutoApplyQuota* This,FSRM_QUOTA_THRESHOLD threshold,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateThresholdAction(This,threshold,actionType,action);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_EnumThresholdActions(IFsrmAutoApplyQuota* This,FSRM_QUOTA_THRESHOLD threshold,IFsrmCollection **actions) {
    return This->lpVtbl->EnumThresholdActions(This,threshold,actions);
}
/*** IFsrmQuotaObject methods ***/
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_Path(IFsrmAutoApplyQuota* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_UserSid(IFsrmAutoApplyQuota* This,BSTR *userSid) {
    return This->lpVtbl->get_UserSid(This,userSid);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_UserAccount(IFsrmAutoApplyQuota* This,BSTR *userAccount) {
    return This->lpVtbl->get_UserAccount(This,userAccount);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_SourceTemplateName(IFsrmAutoApplyQuota* This,BSTR *quotaTemplateName) {
    return This->lpVtbl->get_SourceTemplateName(This,quotaTemplateName);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_MatchesSourceTemplate(IFsrmAutoApplyQuota* This,VARIANT_BOOL *matches) {
    return This->lpVtbl->get_MatchesSourceTemplate(This,matches);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_ApplyTemplate(IFsrmAutoApplyQuota* This,BSTR quotaTemplateName) {
    return This->lpVtbl->ApplyTemplate(This,quotaTemplateName);
}
/*** IFsrmAutoApplyQuota methods ***/
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_get_ExcludeFolders(IFsrmAutoApplyQuota* This,SAFEARRAY **folders) {
    return This->lpVtbl->get_ExcludeFolders(This,folders);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_put_ExcludeFolders(IFsrmAutoApplyQuota* This,SAFEARRAY *folders) {
    return This->lpVtbl->put_ExcludeFolders(This,folders);
}
static FORCEINLINE HRESULT IFsrmAutoApplyQuota_CommitAndUpdateDerived(IFsrmAutoApplyQuota* This,FsrmCommitOptions commitOptions,FsrmTemplateApplyOptions applyOptions,IFsrmDerivedObjectsResult **derivedObjectsResult) {
    return This->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmAutoApplyQuota_get_ExcludeFolders_Proxy(
    IFsrmAutoApplyQuota* This,
    SAFEARRAY **folders);
void __RPC_STUB IFsrmAutoApplyQuota_get_ExcludeFolders_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmAutoApplyQuota_put_ExcludeFolders_Proxy(
    IFsrmAutoApplyQuota* This,
    SAFEARRAY *folders);
void __RPC_STUB IFsrmAutoApplyQuota_put_ExcludeFolders_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmAutoApplyQuota_CommitAndUpdateDerived_Proxy(
    IFsrmAutoApplyQuota* This,
    FsrmCommitOptions commitOptions,
    FsrmTemplateApplyOptions applyOptions,
    IFsrmDerivedObjectsResult **derivedObjectsResult);
void __RPC_STUB IFsrmAutoApplyQuota_CommitAndUpdateDerived_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmAutoApplyQuota_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmQuotaManagerEx interface
 */
#ifndef __IFsrmQuotaManagerEx_INTERFACE_DEFINED__
#define __IFsrmQuotaManagerEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmQuotaManagerEx, 0x4846cb01, 0xd430, 0x494f, 0xab,0xb4, 0xb1,0x05,0x49,0x99,0xfb,0x09);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4846cb01-d430-494f-abb4-b1054999fb09")
IFsrmQuotaManagerEx : public IFsrmQuotaManager
{
    virtual HRESULT STDMETHODCALLTYPE IsAffectedByQuota(
        BSTR path,
        FsrmEnumOptions options,
        VARIANT_BOOL *affected) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmQuotaManagerEx, 0x4846cb01, 0xd430, 0x494f, 0xab,0xb4, 0xb1,0x05,0x49,0x99,0xfb,0x09)
#endif
#else
typedef struct IFsrmQuotaManagerExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmQuotaManagerEx* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmQuotaManagerEx* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmQuotaManagerEx* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmQuotaManagerEx* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmQuotaManagerEx* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmQuotaManagerEx* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmQuotaManagerEx* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmQuotaManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ActionVariables)(
        IFsrmQuotaManagerEx* This,
        SAFEARRAY **variables);

    HRESULT (STDMETHODCALLTYPE *get_ActionVariableDescriptions)(
        IFsrmQuotaManagerEx* This,
        SAFEARRAY **descriptions);

    HRESULT (STDMETHODCALLTYPE *CreateQuota)(
        IFsrmQuotaManagerEx* This,
        BSTR path,
        IFsrmQuota **quota);

    HRESULT (STDMETHODCALLTYPE *CreateAutoApplyQuota)(
        IFsrmQuotaManagerEx* This,
        BSTR quotaTemplateName,
        BSTR path,
        IFsrmAutoApplyQuota **quota);

    HRESULT (STDMETHODCALLTYPE *GetQuota)(
        IFsrmQuotaManagerEx* This,
        BSTR path,
        IFsrmQuota **quota);

    HRESULT (STDMETHODCALLTYPE *GetAutoApplyQuota)(
        IFsrmQuotaManagerEx* This,
        BSTR path,
        IFsrmAutoApplyQuota **quota);

    HRESULT (STDMETHODCALLTYPE *GetRestrictiveQuota)(
        IFsrmQuotaManagerEx* This,
        BSTR path,
        IFsrmQuota **quota);

    HRESULT (STDMETHODCALLTYPE *EnumQuotas)(
        IFsrmQuotaManagerEx* This,
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas);

    HRESULT (STDMETHODCALLTYPE *EnumAutoApplyQuotas)(
        IFsrmQuotaManagerEx* This,
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas);

    HRESULT (STDMETHODCALLTYPE *EnumEffectiveQuotas)(
        IFsrmQuotaManagerEx* This,
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **quotas);

    HRESULT (STDMETHODCALLTYPE *Scan)(
        IFsrmQuotaManagerEx* This,
        BSTR strPath);

    HRESULT (STDMETHODCALLTYPE *CreateQuotaCollection)(
        IFsrmQuotaManagerEx* This,
        IFsrmCommittableCollection **collection);

    /*** IFsrmQuotaManagerEx methods ***/
    HRESULT (STDMETHODCALLTYPE *IsAffectedByQuota)(
        IFsrmQuotaManagerEx* This,
        BSTR path,
        FsrmEnumOptions options,
        VARIANT_BOOL *affected);

    END_INTERFACE
} IFsrmQuotaManagerExVtbl;
interface IFsrmQuotaManagerEx {
    CONST_VTBL IFsrmQuotaManagerExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmQuotaManagerEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmQuotaManagerEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmQuotaManagerEx_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmQuotaManagerEx_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmQuotaManagerEx_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmQuotaManagerEx_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmQuotaManagerEx_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmQuotaManager methods ***/
#define IFsrmQuotaManagerEx_get_ActionVariables(This,variables) (This)->lpVtbl->get_ActionVariables(This,variables)
#define IFsrmQuotaManagerEx_get_ActionVariableDescriptions(This,descriptions) (This)->lpVtbl->get_ActionVariableDescriptions(This,descriptions)
#define IFsrmQuotaManagerEx_CreateQuota(This,path,quota) (This)->lpVtbl->CreateQuota(This,path,quota)
#define IFsrmQuotaManagerEx_CreateAutoApplyQuota(This,quotaTemplateName,path,quota) (This)->lpVtbl->CreateAutoApplyQuota(This,quotaTemplateName,path,quota)
#define IFsrmQuotaManagerEx_GetQuota(This,path,quota) (This)->lpVtbl->GetQuota(This,path,quota)
#define IFsrmQuotaManagerEx_GetAutoApplyQuota(This,path,quota) (This)->lpVtbl->GetAutoApplyQuota(This,path,quota)
#define IFsrmQuotaManagerEx_GetRestrictiveQuota(This,path,quota) (This)->lpVtbl->GetRestrictiveQuota(This,path,quota)
#define IFsrmQuotaManagerEx_EnumQuotas(This,path,options,quotas) (This)->lpVtbl->EnumQuotas(This,path,options,quotas)
#define IFsrmQuotaManagerEx_EnumAutoApplyQuotas(This,path,options,quotas) (This)->lpVtbl->EnumAutoApplyQuotas(This,path,options,quotas)
#define IFsrmQuotaManagerEx_EnumEffectiveQuotas(This,path,options,quotas) (This)->lpVtbl->EnumEffectiveQuotas(This,path,options,quotas)
#define IFsrmQuotaManagerEx_Scan(This,strPath) (This)->lpVtbl->Scan(This,strPath)
#define IFsrmQuotaManagerEx_CreateQuotaCollection(This,collection) (This)->lpVtbl->CreateQuotaCollection(This,collection)
/*** IFsrmQuotaManagerEx methods ***/
#define IFsrmQuotaManagerEx_IsAffectedByQuota(This,path,options,affected) (This)->lpVtbl->IsAffectedByQuota(This,path,options,affected)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_QueryInterface(IFsrmQuotaManagerEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmQuotaManagerEx_AddRef(IFsrmQuotaManagerEx* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmQuotaManagerEx_Release(IFsrmQuotaManagerEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_GetTypeInfoCount(IFsrmQuotaManagerEx* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_GetTypeInfo(IFsrmQuotaManagerEx* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_GetIDsOfNames(IFsrmQuotaManagerEx* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_Invoke(IFsrmQuotaManagerEx* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmQuotaManager methods ***/
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_get_ActionVariables(IFsrmQuotaManagerEx* This,SAFEARRAY **variables) {
    return This->lpVtbl->get_ActionVariables(This,variables);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_get_ActionVariableDescriptions(IFsrmQuotaManagerEx* This,SAFEARRAY **descriptions) {
    return This->lpVtbl->get_ActionVariableDescriptions(This,descriptions);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_CreateQuota(IFsrmQuotaManagerEx* This,BSTR path,IFsrmQuota **quota) {
    return This->lpVtbl->CreateQuota(This,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_CreateAutoApplyQuota(IFsrmQuotaManagerEx* This,BSTR quotaTemplateName,BSTR path,IFsrmAutoApplyQuota **quota) {
    return This->lpVtbl->CreateAutoApplyQuota(This,quotaTemplateName,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_GetQuota(IFsrmQuotaManagerEx* This,BSTR path,IFsrmQuota **quota) {
    return This->lpVtbl->GetQuota(This,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_GetAutoApplyQuota(IFsrmQuotaManagerEx* This,BSTR path,IFsrmAutoApplyQuota **quota) {
    return This->lpVtbl->GetAutoApplyQuota(This,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_GetRestrictiveQuota(IFsrmQuotaManagerEx* This,BSTR path,IFsrmQuota **quota) {
    return This->lpVtbl->GetRestrictiveQuota(This,path,quota);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_EnumQuotas(IFsrmQuotaManagerEx* This,BSTR path,FsrmEnumOptions options,IFsrmCommittableCollection **quotas) {
    return This->lpVtbl->EnumQuotas(This,path,options,quotas);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_EnumAutoApplyQuotas(IFsrmQuotaManagerEx* This,BSTR path,FsrmEnumOptions options,IFsrmCommittableCollection **quotas) {
    return This->lpVtbl->EnumAutoApplyQuotas(This,path,options,quotas);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_EnumEffectiveQuotas(IFsrmQuotaManagerEx* This,BSTR path,FsrmEnumOptions options,IFsrmCommittableCollection **quotas) {
    return This->lpVtbl->EnumEffectiveQuotas(This,path,options,quotas);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_Scan(IFsrmQuotaManagerEx* This,BSTR strPath) {
    return This->lpVtbl->Scan(This,strPath);
}
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_CreateQuotaCollection(IFsrmQuotaManagerEx* This,IFsrmCommittableCollection **collection) {
    return This->lpVtbl->CreateQuotaCollection(This,collection);
}
/*** IFsrmQuotaManagerEx methods ***/
static FORCEINLINE HRESULT IFsrmQuotaManagerEx_IsAffectedByQuota(IFsrmQuotaManagerEx* This,BSTR path,FsrmEnumOptions options,VARIANT_BOOL *affected) {
    return This->lpVtbl->IsAffectedByQuota(This,path,options,affected);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmQuotaManagerEx_IsAffectedByQuota_Proxy(
    IFsrmQuotaManagerEx* This,
    BSTR path,
    FsrmEnumOptions options,
    VARIANT_BOOL *affected);
void __RPC_STUB IFsrmQuotaManagerEx_IsAffectedByQuota_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmQuotaManagerEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmQuotaTemplateImported interface
 */
#ifndef __IFsrmQuotaTemplateImported_INTERFACE_DEFINED__
#define __IFsrmQuotaTemplateImported_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmQuotaTemplateImported, 0x9a2bf113, 0xa329, 0x44cc, 0x80,0x9a, 0x5c,0x00,0xfc,0xe8,0xda,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9a2bf113-a329-44cc-809a-5c00fce8da40")
IFsrmQuotaTemplateImported : public IFsrmQuotaTemplate
{
    virtual HRESULT STDMETHODCALLTYPE get_OverwriteOnCommit(
        VARIANT_BOOL *overwrite) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_OverwriteOnCommit(
        VARIANT_BOOL overwrite) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmQuotaTemplateImported, 0x9a2bf113, 0xa329, 0x44cc, 0x80,0x9a, 0x5c,0x00,0xfc,0xe8,0xda,0x40)
#endif
#else
typedef struct IFsrmQuotaTemplateImportedVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmQuotaTemplateImported* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmQuotaTemplateImported* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmQuotaTemplateImported* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmQuotaTemplateImported* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmQuotaTemplateImported* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmQuotaTemplateImported* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmQuotaTemplateImported* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmQuotaTemplateImported* This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmQuotaTemplateImported* This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmQuotaTemplateImported* This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmQuotaTemplateImported* This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmQuotaTemplateImported* This);

    /*** IFsrmQuotaBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_QuotaLimit)(
        IFsrmQuotaTemplateImported* This,
        VARIANT *quotaLimit);

    HRESULT (STDMETHODCALLTYPE *put_QuotaLimit)(
        IFsrmQuotaTemplateImported* This,
        VARIANT quotaLimit);

    HRESULT (STDMETHODCALLTYPE *get_QuotaFlags)(
        IFsrmQuotaTemplateImported* This,
        LONG *quotaFlags);

    HRESULT (STDMETHODCALLTYPE *put_QuotaFlags)(
        IFsrmQuotaTemplateImported* This,
        LONG quotaFlags);

    HRESULT (STDMETHODCALLTYPE *get_Thresholds)(
        IFsrmQuotaTemplateImported* This,
        SAFEARRAY **thresholds);

    HRESULT (STDMETHODCALLTYPE *AddThreshold)(
        IFsrmQuotaTemplateImported* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *DeleteThreshold)(
        IFsrmQuotaTemplateImported* This,
        FSRM_QUOTA_THRESHOLD threshold);

    HRESULT (STDMETHODCALLTYPE *ModifyThreshold)(
        IFsrmQuotaTemplateImported* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FSRM_QUOTA_THRESHOLD newThreshold);

    HRESULT (STDMETHODCALLTYPE *CreateThresholdAction)(
        IFsrmQuotaTemplateImported* This,
        FSRM_QUOTA_THRESHOLD threshold,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumThresholdActions)(
        IFsrmQuotaTemplateImported* This,
        FSRM_QUOTA_THRESHOLD threshold,
        IFsrmCollection **actions);

    /*** IFsrmQuotaTemplate methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmQuotaTemplateImported* This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmQuotaTemplateImported* This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *CopyTemplate)(
        IFsrmQuotaTemplateImported* This,
        BSTR quotaTemplateName);

    HRESULT (STDMETHODCALLTYPE *CommitAndUpdateDerived)(
        IFsrmQuotaTemplateImported* This,
        FsrmCommitOptions commitOptions,
        FsrmTemplateApplyOptions applyOptions,
        IFsrmDerivedObjectsResult **derivedObjectsResult);

    /*** IFsrmQuotaTemplateImported methods ***/
    HRESULT (STDMETHODCALLTYPE *get_OverwriteOnCommit)(
        IFsrmQuotaTemplateImported* This,
        VARIANT_BOOL *overwrite);

    HRESULT (STDMETHODCALLTYPE *put_OverwriteOnCommit)(
        IFsrmQuotaTemplateImported* This,
        VARIANT_BOOL overwrite);

    END_INTERFACE
} IFsrmQuotaTemplateImportedVtbl;
interface IFsrmQuotaTemplateImported {
    CONST_VTBL IFsrmQuotaTemplateImportedVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmQuotaTemplateImported_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmQuotaTemplateImported_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmQuotaTemplateImported_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmQuotaTemplateImported_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmQuotaTemplateImported_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmQuotaTemplateImported_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmQuotaTemplateImported_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmQuotaTemplateImported_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmQuotaTemplateImported_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmQuotaTemplateImported_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmQuotaTemplateImported_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmQuotaTemplateImported_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmQuotaBase methods ***/
#define IFsrmQuotaTemplateImported_get_QuotaLimit(This,quotaLimit) (This)->lpVtbl->get_QuotaLimit(This,quotaLimit)
#define IFsrmQuotaTemplateImported_put_QuotaLimit(This,quotaLimit) (This)->lpVtbl->put_QuotaLimit(This,quotaLimit)
#define IFsrmQuotaTemplateImported_get_QuotaFlags(This,quotaFlags) (This)->lpVtbl->get_QuotaFlags(This,quotaFlags)
#define IFsrmQuotaTemplateImported_put_QuotaFlags(This,quotaFlags) (This)->lpVtbl->put_QuotaFlags(This,quotaFlags)
#define IFsrmQuotaTemplateImported_get_Thresholds(This,thresholds) (This)->lpVtbl->get_Thresholds(This,thresholds)
#define IFsrmQuotaTemplateImported_AddThreshold(This,threshold) (This)->lpVtbl->AddThreshold(This,threshold)
#define IFsrmQuotaTemplateImported_DeleteThreshold(This,threshold) (This)->lpVtbl->DeleteThreshold(This,threshold)
#define IFsrmQuotaTemplateImported_ModifyThreshold(This,threshold,newThreshold) (This)->lpVtbl->ModifyThreshold(This,threshold,newThreshold)
#define IFsrmQuotaTemplateImported_CreateThresholdAction(This,threshold,actionType,action) (This)->lpVtbl->CreateThresholdAction(This,threshold,actionType,action)
#define IFsrmQuotaTemplateImported_EnumThresholdActions(This,threshold,actions) (This)->lpVtbl->EnumThresholdActions(This,threshold,actions)
/*** IFsrmQuotaTemplate methods ***/
#define IFsrmQuotaTemplateImported_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmQuotaTemplateImported_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmQuotaTemplateImported_CopyTemplate(This,quotaTemplateName) (This)->lpVtbl->CopyTemplate(This,quotaTemplateName)
#define IFsrmQuotaTemplateImported_CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult) (This)->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult)
/*** IFsrmQuotaTemplateImported methods ***/
#define IFsrmQuotaTemplateImported_get_OverwriteOnCommit(This,overwrite) (This)->lpVtbl->get_OverwriteOnCommit(This,overwrite)
#define IFsrmQuotaTemplateImported_put_OverwriteOnCommit(This,overwrite) (This)->lpVtbl->put_OverwriteOnCommit(This,overwrite)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_QueryInterface(IFsrmQuotaTemplateImported* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFsrmQuotaTemplateImported_AddRef(IFsrmQuotaTemplateImported* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFsrmQuotaTemplateImported_Release(IFsrmQuotaTemplateImported* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_GetTypeInfoCount(IFsrmQuotaTemplateImported* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_GetTypeInfo(IFsrmQuotaTemplateImported* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_GetIDsOfNames(IFsrmQuotaTemplateImported* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_Invoke(IFsrmQuotaTemplateImported* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_get_Id(IFsrmQuotaTemplateImported* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_get_Description(IFsrmQuotaTemplateImported* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_put_Description(IFsrmQuotaTemplateImported* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_Delete(IFsrmQuotaTemplateImported* This) {
    return This->lpVtbl->Delete(This);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_Commit(IFsrmQuotaTemplateImported* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmQuotaBase methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_get_QuotaLimit(IFsrmQuotaTemplateImported* This,VARIANT *quotaLimit) {
    return This->lpVtbl->get_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_put_QuotaLimit(IFsrmQuotaTemplateImported* This,VARIANT quotaLimit) {
    return This->lpVtbl->put_QuotaLimit(This,quotaLimit);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_get_QuotaFlags(IFsrmQuotaTemplateImported* This,LONG *quotaFlags) {
    return This->lpVtbl->get_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_put_QuotaFlags(IFsrmQuotaTemplateImported* This,LONG quotaFlags) {
    return This->lpVtbl->put_QuotaFlags(This,quotaFlags);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_get_Thresholds(IFsrmQuotaTemplateImported* This,SAFEARRAY **thresholds) {
    return This->lpVtbl->get_Thresholds(This,thresholds);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_AddThreshold(IFsrmQuotaTemplateImported* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->AddThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_DeleteThreshold(IFsrmQuotaTemplateImported* This,FSRM_QUOTA_THRESHOLD threshold) {
    return This->lpVtbl->DeleteThreshold(This,threshold);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_ModifyThreshold(IFsrmQuotaTemplateImported* This,FSRM_QUOTA_THRESHOLD threshold,FSRM_QUOTA_THRESHOLD newThreshold) {
    return This->lpVtbl->ModifyThreshold(This,threshold,newThreshold);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_CreateThresholdAction(IFsrmQuotaTemplateImported* This,FSRM_QUOTA_THRESHOLD threshold,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateThresholdAction(This,threshold,actionType,action);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_EnumThresholdActions(IFsrmQuotaTemplateImported* This,FSRM_QUOTA_THRESHOLD threshold,IFsrmCollection **actions) {
    return This->lpVtbl->EnumThresholdActions(This,threshold,actions);
}
/*** IFsrmQuotaTemplate methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_get_Name(IFsrmQuotaTemplateImported* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_put_Name(IFsrmQuotaTemplateImported* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_CopyTemplate(IFsrmQuotaTemplateImported* This,BSTR quotaTemplateName) {
    return This->lpVtbl->CopyTemplate(This,quotaTemplateName);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_CommitAndUpdateDerived(IFsrmQuotaTemplateImported* This,FsrmCommitOptions commitOptions,FsrmTemplateApplyOptions applyOptions,IFsrmDerivedObjectsResult **derivedObjectsResult) {
    return This->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult);
}
/*** IFsrmQuotaTemplateImported methods ***/
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_get_OverwriteOnCommit(IFsrmQuotaTemplateImported* This,VARIANT_BOOL *overwrite) {
    return This->lpVtbl->get_OverwriteOnCommit(This,overwrite);
}
static FORCEINLINE HRESULT IFsrmQuotaTemplateImported_put_OverwriteOnCommit(IFsrmQuotaTemplateImported* This,VARIANT_BOOL overwrite) {
    return This->lpVtbl->put_OverwriteOnCommit(This,overwrite);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplateImported_get_OverwriteOnCommit_Proxy(
    IFsrmQuotaTemplateImported* This,
    VARIANT_BOOL *overwrite);
void __RPC_STUB IFsrmQuotaTemplateImported_get_OverwriteOnCommit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFsrmQuotaTemplateImported_put_OverwriteOnCommit_Proxy(
    IFsrmQuotaTemplateImported* This,
    VARIANT_BOOL overwrite);
void __RPC_STUB IFsrmQuotaTemplateImported_put_OverwriteOnCommit_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFsrmQuotaTemplateImported_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);
ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __fsrmquota_h__ */
