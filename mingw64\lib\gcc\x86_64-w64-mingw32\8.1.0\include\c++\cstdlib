// -*- C++ -*- forwarding header.

// Copyright (C) 1997-2018 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/cstdlib
 *  This is a Standard C++ Library file.  You should @c \#include this file
 *  in your programs, rather than any of the @a *.h implementation files.
 *
 *  This is the C++ version of the Standard C Library header @c stdlib.h,
 *  and its contents are (mostly) the same as that header, but are all
 *  contained in the namespace @c std (except for names which are defined
 *  as macros in C).
 */

//
// ISO C++ 14882: 20.4.6  C library
//

#pragma GCC system_header

#include <bits/c++config.h>

#ifndef _GLIBCXX_CSTDLIB
#define _GLIBCXX_CSTDLIB 1

#if !_GLIBCXX_HOSTED
// The C standard does not require a freestanding implementation to
// provide <stdlib.h>.  However, the C++ standard does still require
// <cstdlib> -- but only the functionality mentioned in
// [lib.support.start.term].

#define EXIT_SUCCESS 0
#define EXIT_FAILURE 1

namespace std
{
  extern "C" void abort(void) throw () _GLIBCXX_NORETURN;
  extern "C" int atexit(void (*)(void)) throw ();
  extern "C" void exit(int) throw () _GLIBCXX_NORETURN;
#if __cplusplus >= 201103L
# ifdef _GLIBCXX_HAVE_AT_QUICK_EXIT
  extern "C" int at_quick_exit(void (*)(void)) throw ();
# endif
# ifdef _GLIBCXX_HAVE_QUICK_EXIT
  extern "C" void quick_exit(int) throw() _GLIBCXX_NORETURN;
# endif
#endif
} // namespace std

#else

// Need to ensure this finds the C library's <stdlib.h> not a libstdc++
// wrapper that might already be installed later in the include search path.
#define _GLIBCXX_INCLUDE_NEXT_C_HEADERS
#include_next <stdlib.h>
#undef _GLIBCXX_INCLUDE_NEXT_C_HEADERS
#include <bits/std_abs.h>

// Get rid of those macros defined in <stdlib.h> in lieu of real functions.
#undef abort
#if __cplusplus >= 201703L && defined(_GLIBCXX_HAVE_ALIGNED_ALLOC)
# undef aligned_alloc
#endif
#undef atexit
#if __cplusplus >= 201103L
# ifdef _GLIBCXX_HAVE_AT_QUICK_EXIT
#  undef at_quick_exit
# endif
#endif
#undef atof
#undef atoi
#undef atol
#undef bsearch
#undef calloc
#undef div
#undef exit
#undef free
#undef getenv
#undef labs
#undef ldiv
#undef malloc
#undef mblen
#undef mbstowcs
#undef mbtowc
#undef qsort
#if __cplusplus >= 201103L
# ifdef _GLIBCXX_HAVE_QUICK_EXIT
#  undef quick_exit
# endif
#endif
#undef rand
#undef realloc
#undef srand
#undef strtod
#undef strtol
#undef strtoul
#undef system
#undef wcstombs
#undef wctomb

extern "C++"
{
namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  using ::div_t;
  using ::ldiv_t;

  using ::abort;
#if __cplusplus >= 201703L && defined(_GLIBCXX_HAVE_ALIGNED_ALLOC)
  using ::aligned_alloc;
#endif
  using ::atexit;
#if __cplusplus >= 201103L
# ifdef _GLIBCXX_HAVE_AT_QUICK_EXIT
  using ::at_quick_exit;
# endif
#endif
  using ::atof;
  using ::atoi;
  using ::atol;
  using ::bsearch;
  using ::calloc;
  using ::div;
  using ::exit;
  using ::free;
  using ::getenv;
  using ::labs;
  using ::ldiv;
  using ::malloc;
#ifdef _GLIBCXX_HAVE_MBSTATE_T
  using ::mblen;
  using ::mbstowcs;
  using ::mbtowc;
#endif // _GLIBCXX_HAVE_MBSTATE_T
  using ::qsort;
#if __cplusplus >= 201103L
# ifdef _GLIBCXX_HAVE_QUICK_EXIT
  using ::quick_exit;
# endif
#endif
  using ::rand;
  using ::realloc;
  using ::srand;
  using ::strtod;
  using ::strtol;
  using ::strtoul;
  using ::system;
#ifdef _GLIBCXX_USE_WCHAR_T
  using ::wcstombs;
  using ::wctomb;
#endif // _GLIBCXX_USE_WCHAR_T

#ifndef __CORRECT_ISO_CPP_STDLIB_H_PROTO
  inline ldiv_t
  div(long __i, long __j) { return ldiv(__i, __j); }
#endif


_GLIBCXX_END_NAMESPACE_VERSION
} // namespace

#if _GLIBCXX_USE_C99_STDLIB

#undef _Exit
#undef llabs
#undef lldiv
#undef atoll
#undef strtoll
#undef strtoull
#undef strtof
#undef strtold

namespace __gnu_cxx _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

#if !_GLIBCXX_USE_C99_LONG_LONG_DYNAMIC
  using ::lldiv_t;
#endif
#if _GLIBCXX_USE_C99_CHECK || _GLIBCXX_USE_C99_DYNAMIC
  extern "C" void (_Exit)(int) throw () _GLIBCXX_NORETURN;
#endif
#if !_GLIBCXX_USE_C99_DYNAMIC
  using ::_Exit;
#endif

#if !_GLIBCXX_USE_C99_LONG_LONG_DYNAMIC
  using ::llabs;

  inline lldiv_t
  div(long long __n, long long __d)
  { lldiv_t __q; __q.quot = __n / __d; __q.rem = __n % __d; return __q; }

  using ::lldiv;
#endif

#if _GLIBCXX_USE_C99_LONG_LONG_CHECK || _GLIBCXX_USE_C99_LONG_LONG_DYNAMIC
  extern "C" long long int (atoll)(const char *) throw ();
  extern "C" long long int
    (strtoll)(const char * __restrict, char ** __restrict, int) throw ();
  extern "C" unsigned long long int
    (strtoull)(const char * __restrict, char ** __restrict, int) throw ();
#endif
#if !_GLIBCXX_USE_C99_LONG_LONG_DYNAMIC
  using ::atoll;
  using ::strtoll;
  using ::strtoull;
#endif
  using ::strtof;
  using ::strtold;

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace __gnu_cxx

namespace std
{
#if !_GLIBCXX_USE_C99_LONG_LONG_DYNAMIC
  using ::__gnu_cxx::lldiv_t;
#endif
  using ::__gnu_cxx::_Exit;
#if !_GLIBCXX_USE_C99_LONG_LONG_DYNAMIC
  using ::__gnu_cxx::llabs;
  using ::__gnu_cxx::div;
  using ::__gnu_cxx::lldiv;
#endif
  using ::__gnu_cxx::atoll;
  using ::__gnu_cxx::strtof;
  using ::__gnu_cxx::strtoll;
  using ::__gnu_cxx::strtoull;
  using ::__gnu_cxx::strtold;
} // namespace std

#endif // _GLIBCXX_USE_C99_STDLIB

} // extern "C++"

#endif // !_GLIBCXX_HOSTED

#endif
