/*
 * Copyright (C) 2002 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifndef _MEDIAERR_H_
#define _MEDIAERR_H_

#define DMO_E_INVALIDSTREAMINDEX   0x80040201
#define DMO_E_INVALIDTYPE          0x80040202
#define DMO_E_TYPE_NOT_SET         0x80040203
#define DMO_E_NOTACCEPTING         0x80040204
#define DMO_E_TYPE_NOT_ACCEPTED    0x80040205
#define DMO_E_NO_MORE_ITEMS        0x80040206

#endif /* _MEDIAERR_H_ */
