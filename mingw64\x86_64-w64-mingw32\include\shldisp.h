/*** Autogenerated by WIDL 1.6 from include/shldisp.idl - Do not edit ***/

#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __shldisp_h__
#define __shldisp_h__

/* Forward declarations */

#ifndef __IFolderViewOC_FWD_DEFINED__
#define __IFolderViewOC_FWD_DEFINED__
typedef interface IFolderViewOC IFolderViewOC;
#endif

#ifndef __DShellFolderViewEvents_FWD_DEFINED__
#define __DShellFolderViewEvents_FWD_DEFINED__
typedef interface DShellFolderViewEvents DShellFolderViewEvents;
#endif

#ifndef __ShellFolderViewOC_FWD_DEFINED__
#define __ShellFolderViewOC_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellFolderViewOC ShellFolderViewOC;
#else
typedef struct ShellFolderViewOC ShellFolderViewOC;
#endif /* defined __cplusplus */
#endif /* defined __ShellFolderViewOC_FWD_DEFINED__ */

#ifndef __DFConstraint_FWD_DEFINED__
#define __DFConstraint_FWD_DEFINED__
typedef interface DFConstraint DFConstraint;
#endif

#ifndef __Folder_FWD_DEFINED__
#define __Folder_FWD_DEFINED__
typedef interface Folder Folder;
#endif

#ifndef __Folder2_FWD_DEFINED__
#define __Folder2_FWD_DEFINED__
typedef interface Folder2 Folder2;
#endif

#ifndef __Folder3_FWD_DEFINED__
#define __Folder3_FWD_DEFINED__
typedef interface Folder3 Folder3;
#endif

#ifndef __FolderItem_FWD_DEFINED__
#define __FolderItem_FWD_DEFINED__
typedef interface FolderItem FolderItem;
#endif

#ifndef __FolderItem2_FWD_DEFINED__
#define __FolderItem2_FWD_DEFINED__
typedef interface FolderItem2 FolderItem2;
#endif

#ifndef __ShellFolderItem_FWD_DEFINED__
#define __ShellFolderItem_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellFolderItem ShellFolderItem;
#else
typedef struct ShellFolderItem ShellFolderItem;
#endif /* defined __cplusplus */
#endif /* defined __ShellFolderItem_FWD_DEFINED__ */

#ifndef __FolderItems_FWD_DEFINED__
#define __FolderItems_FWD_DEFINED__
typedef interface FolderItems FolderItems;
#endif

#ifndef __FolderItems2_FWD_DEFINED__
#define __FolderItems2_FWD_DEFINED__
typedef interface FolderItems2 FolderItems2;
#endif

#ifndef __FolderItems3_FWD_DEFINED__
#define __FolderItems3_FWD_DEFINED__
typedef interface FolderItems3 FolderItems3;
#endif

#ifndef __FolderItemVerb_FWD_DEFINED__
#define __FolderItemVerb_FWD_DEFINED__
typedef interface FolderItemVerb FolderItemVerb;
#endif

#ifndef __FolderItemVerbs_FWD_DEFINED__
#define __FolderItemVerbs_FWD_DEFINED__
typedef interface FolderItemVerbs FolderItemVerbs;
#endif

#ifndef __IShellLinkDual_FWD_DEFINED__
#define __IShellLinkDual_FWD_DEFINED__
typedef interface IShellLinkDual IShellLinkDual;
#endif

#ifndef __IShellLinkDual2_FWD_DEFINED__
#define __IShellLinkDual2_FWD_DEFINED__
typedef interface IShellLinkDual2 IShellLinkDual2;
#endif

#ifndef __ShellLinkObject_FWD_DEFINED__
#define __ShellLinkObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellLinkObject ShellLinkObject;
#else
typedef struct ShellLinkObject ShellLinkObject;
#endif /* defined __cplusplus */
#endif /* defined __ShellLinkObject_FWD_DEFINED__ */

#ifndef __IShellFolderViewDual_FWD_DEFINED__
#define __IShellFolderViewDual_FWD_DEFINED__
typedef interface IShellFolderViewDual IShellFolderViewDual;
#endif

#ifndef __IShellFolderViewDual2_FWD_DEFINED__
#define __IShellFolderViewDual2_FWD_DEFINED__
typedef interface IShellFolderViewDual2 IShellFolderViewDual2;
#endif

#ifndef __IShellFolderViewDual3_FWD_DEFINED__
#define __IShellFolderViewDual3_FWD_DEFINED__
typedef interface IShellFolderViewDual3 IShellFolderViewDual3;
#endif

#ifndef __ShellFolderView_FWD_DEFINED__
#define __ShellFolderView_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellFolderView ShellFolderView;
#else
typedef struct ShellFolderView ShellFolderView;
#endif /* defined __cplusplus */
#endif /* defined __ShellFolderView_FWD_DEFINED__ */

#ifndef __IShellDispatch_FWD_DEFINED__
#define __IShellDispatch_FWD_DEFINED__
typedef interface IShellDispatch IShellDispatch;
#endif

#ifndef __IShellDispatch2_FWD_DEFINED__
#define __IShellDispatch2_FWD_DEFINED__
typedef interface IShellDispatch2 IShellDispatch2;
#endif

#ifndef __IShellDispatch3_FWD_DEFINED__
#define __IShellDispatch3_FWD_DEFINED__
typedef interface IShellDispatch3 IShellDispatch3;
#endif

#ifndef __IShellDispatch4_FWD_DEFINED__
#define __IShellDispatch4_FWD_DEFINED__
typedef interface IShellDispatch4 IShellDispatch4;
#endif

#ifndef __IShellDispatch5_FWD_DEFINED__
#define __IShellDispatch5_FWD_DEFINED__
typedef interface IShellDispatch5 IShellDispatch5;
#endif

#ifndef __IShellDispatch6_FWD_DEFINED__
#define __IShellDispatch6_FWD_DEFINED__
typedef interface IShellDispatch6 IShellDispatch6;
#endif

#ifndef __Shell_FWD_DEFINED__
#define __Shell_FWD_DEFINED__
#ifdef __cplusplus
typedef class Shell Shell;
#else
typedef struct Shell Shell;
#endif /* defined __cplusplus */
#endif /* defined __Shell_FWD_DEFINED__ */

#ifndef __ShellDispatchInproc_FWD_DEFINED__
#define __ShellDispatchInproc_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellDispatchInproc ShellDispatchInproc;
#else
typedef struct ShellDispatchInproc ShellDispatchInproc;
#endif /* defined __cplusplus */
#endif /* defined __ShellDispatchInproc_FWD_DEFINED__ */

#ifndef __IFileSearchBand_FWD_DEFINED__
#define __IFileSearchBand_FWD_DEFINED__
typedef interface IFileSearchBand IFileSearchBand;
#endif

#ifndef __FileSearchBand_FWD_DEFINED__
#define __FileSearchBand_FWD_DEFINED__
#ifdef __cplusplus
typedef class FileSearchBand FileSearchBand;
#else
typedef struct FileSearchBand FileSearchBand;
#endif /* defined __cplusplus */
#endif /* defined __FileSearchBand_FWD_DEFINED__ */

#ifndef __IWebWizardHost_FWD_DEFINED__
#define __IWebWizardHost_FWD_DEFINED__
typedef interface IWebWizardHost IWebWizardHost;
#endif

#ifndef __INewWDEvents_FWD_DEFINED__
#define __INewWDEvents_FWD_DEFINED__
typedef interface INewWDEvents INewWDEvents;
#endif

#ifndef __IAutoComplete_FWD_DEFINED__
#define __IAutoComplete_FWD_DEFINED__
typedef interface IAutoComplete IAutoComplete;
#endif

#ifndef __IAutoComplete2_FWD_DEFINED__
#define __IAutoComplete2_FWD_DEFINED__
typedef interface IAutoComplete2 IAutoComplete2;
#endif

#ifndef __IEnumACString_FWD_DEFINED__
#define __IEnumACString_FWD_DEFINED__
typedef interface IEnumACString IEnumACString;
#endif

#ifndef __IDataObjectAsyncCapability_FWD_DEFINED__
#define __IDataObjectAsyncCapability_FWD_DEFINED__
typedef interface IDataObjectAsyncCapability IDataObjectAsyncCapability;
#endif

/* Headers for imported files */

#include <ocidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __DShellFolderViewEvents_FWD_DEFINED__
#define __DShellFolderViewEvents_FWD_DEFINED__
typedef interface DShellFolderViewEvents DShellFolderViewEvents;
#endif

#ifndef __IAutoComplete_FWD_DEFINED__
#define __IAutoComplete_FWD_DEFINED__
typedef interface IAutoComplete IAutoComplete;
#endif

#ifndef __FolderItem_FWD_DEFINED__
#define __FolderItem_FWD_DEFINED__
typedef interface FolderItem FolderItem;
#endif

#ifndef __FolderItems_FWD_DEFINED__
#define __FolderItems_FWD_DEFINED__
typedef interface FolderItems FolderItems;
#endif

#ifndef __FolderItemVerb_FWD_DEFINED__
#define __FolderItemVerb_FWD_DEFINED__
typedef interface FolderItemVerb FolderItemVerb;
#endif

#ifndef __FolderItemVerbs_FWD_DEFINED__
#define __FolderItemVerbs_FWD_DEFINED__
typedef interface FolderItemVerbs FolderItemVerbs;
#endif


DEFINE_GUID(LIBID_Shell32, 0x50a7e9b0, 0x70ef, 0x11d1, 0xb7,0x5a, 0x00,0xa0,0xc9,0x05,0x64,0xfe);

/*****************************************************************************
 * IFolderViewOC interface
 */
#ifndef __IFolderViewOC_INTERFACE_DEFINED__
#define __IFolderViewOC_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFolderViewOC, 0x9ba05970, 0xf6a8, 0x11cf, 0xa4,0x42, 0x00,0xa0,0xc9,0x0a,0x8f,0x39);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9ba05970-f6a8-11cf-a442-00a0c90a8f39")
IFolderViewOC : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE SetFolderView(
        IDispatch *pdisp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFolderViewOC, 0x9ba05970, 0xf6a8, 0x11cf, 0xa4,0x42, 0x00,0xa0,0xc9,0x0a,0x8f,0x39)
#endif
#else
typedef struct IFolderViewOCVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFolderViewOC* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFolderViewOC* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFolderViewOC* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFolderViewOC* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFolderViewOC* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFolderViewOC* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFolderViewOC* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFolderViewOC methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFolderView)(
        IFolderViewOC* This,
        IDispatch *pdisp);

    END_INTERFACE
} IFolderViewOCVtbl;
interface IFolderViewOC {
    CONST_VTBL IFolderViewOCVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFolderViewOC_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFolderViewOC_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFolderViewOC_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFolderViewOC_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFolderViewOC_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFolderViewOC_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFolderViewOC_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFolderViewOC methods ***/
#define IFolderViewOC_SetFolderView(This,pdisp) (This)->lpVtbl->SetFolderView(This,pdisp)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFolderViewOC_QueryInterface(IFolderViewOC* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFolderViewOC_AddRef(IFolderViewOC* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFolderViewOC_Release(IFolderViewOC* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFolderViewOC_GetTypeInfoCount(IFolderViewOC* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFolderViewOC_GetTypeInfo(IFolderViewOC* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFolderViewOC_GetIDsOfNames(IFolderViewOC* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFolderViewOC_Invoke(IFolderViewOC* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFolderViewOC methods ***/
static FORCEINLINE HRESULT IFolderViewOC_SetFolderView(IFolderViewOC* This,IDispatch *pdisp) {
    return This->lpVtbl->SetFolderView(This,pdisp);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFolderViewOC_SetFolderView_Proxy(
    IFolderViewOC* This,
    IDispatch *pdisp);
void __RPC_STUB IFolderViewOC_SetFolderView_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFolderViewOC_INTERFACE_DEFINED__ */

/*****************************************************************************
 * DShellFolderViewEvents dispinterface
 */
#ifndef __DShellFolderViewEvents_DISPINTERFACE_DEFINED__
#define __DShellFolderViewEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID_DShellFolderViewEvents, 0x62112aa2, 0xebe4, 0x11cf, 0xa5,0xfb, 0x00,0x20,0xaf,0xe7,0x29,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("62112aa2-ebe4-11cf-a5fb-0020afe7292d")
DShellFolderViewEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DShellFolderViewEvents, 0x62112aa2, 0xebe4, 0x11cf, 0xa5,0xfb, 0x00,0x20,0xaf,0xe7,0x29,0x2d)
#endif
#else
typedef struct DShellFolderViewEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        DShellFolderViewEvents* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        DShellFolderViewEvents* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        DShellFolderViewEvents* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        DShellFolderViewEvents* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        DShellFolderViewEvents* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        DShellFolderViewEvents* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        DShellFolderViewEvents* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} DShellFolderViewEventsVtbl;
interface DShellFolderViewEvents {
    CONST_VTBL DShellFolderViewEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define DShellFolderViewEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define DShellFolderViewEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define DShellFolderViewEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define DShellFolderViewEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define DShellFolderViewEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define DShellFolderViewEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define DShellFolderViewEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT DShellFolderViewEvents_QueryInterface(DShellFolderViewEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG DShellFolderViewEvents_AddRef(DShellFolderViewEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG DShellFolderViewEvents_Release(DShellFolderViewEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT DShellFolderViewEvents_GetTypeInfoCount(DShellFolderViewEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT DShellFolderViewEvents_GetTypeInfo(DShellFolderViewEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT DShellFolderViewEvents_GetIDsOfNames(DShellFolderViewEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT DShellFolderViewEvents_Invoke(DShellFolderViewEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* __DShellFolderViewEvents_DISPINTERFACE_DEFINED__ */

/*****************************************************************************
 * ShellFolderViewOC coclass
 */

DEFINE_GUID(CLSID_ShellFolderViewOC, 0x9ba05971, 0xf6a8, 0x11cf, 0xa4,0x42, 0x00,0xa0,0xc9,0x0a,0x8f,0x39);

#ifdef __cplusplus
class DECLSPEC_UUID("9ba05971-f6a8-11cf-a442-00a0c90a8f39") ShellFolderViewOC;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellFolderViewOC, 0x9ba05971, 0xf6a8, 0x11cf, 0xa4,0x42, 0x00,0xa0,0xc9,0x0a,0x8f,0x39)
#endif
#endif

/*****************************************************************************
 * DFConstraint interface
 */
#ifndef __DFConstraint_INTERFACE_DEFINED__
#define __DFConstraint_INTERFACE_DEFINED__

DEFINE_GUID(IID_DFConstraint, 0x4a3df050, 0x23bd, 0x11d2, 0x93,0x9f, 0x00,0xa0,0xc9,0x1e,0xed,0xba);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4a3df050-23bd-11d2-939f-00a0c91eedba")
DFConstraint : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Value(
        VARIANT *pv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DFConstraint, 0x4a3df050, 0x23bd, 0x11d2, 0x93,0x9f, 0x00,0xa0,0xc9,0x1e,0xed,0xba)
#endif
#else
typedef struct DFConstraintVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        DFConstraint* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        DFConstraint* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        DFConstraint* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        DFConstraint* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        DFConstraint* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        DFConstraint* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        DFConstraint* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** DFConstraint methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        DFConstraint* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        DFConstraint* This,
        VARIANT *pv);

    END_INTERFACE
} DFConstraintVtbl;
interface DFConstraint {
    CONST_VTBL DFConstraintVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define DFConstraint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define DFConstraint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define DFConstraint_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define DFConstraint_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define DFConstraint_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define DFConstraint_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define DFConstraint_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** DFConstraint methods ***/
#define DFConstraint_get_Name(This,pbs) (This)->lpVtbl->get_Name(This,pbs)
#define DFConstraint_get_Value(This,pv) (This)->lpVtbl->get_Value(This,pv)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT DFConstraint_QueryInterface(DFConstraint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG DFConstraint_AddRef(DFConstraint* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG DFConstraint_Release(DFConstraint* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT DFConstraint_GetTypeInfoCount(DFConstraint* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT DFConstraint_GetTypeInfo(DFConstraint* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT DFConstraint_GetIDsOfNames(DFConstraint* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT DFConstraint_Invoke(DFConstraint* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** DFConstraint methods ***/
static FORCEINLINE HRESULT DFConstraint_get_Name(DFConstraint* This,BSTR *pbs) {
    return This->lpVtbl->get_Name(This,pbs);
}
static FORCEINLINE HRESULT DFConstraint_get_Value(DFConstraint* This,VARIANT *pv) {
    return This->lpVtbl->get_Value(This,pv);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE DFConstraint_get_Name_Proxy(
    DFConstraint* This,
    BSTR *pbs);
void __RPC_STUB DFConstraint_get_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE DFConstraint_get_Value_Proxy(
    DFConstraint* This,
    VARIANT *pv);
void __RPC_STUB DFConstraint_get_Value_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __DFConstraint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * Folder interface
 */
#ifndef __Folder_INTERFACE_DEFINED__
#define __Folder_INTERFACE_DEFINED__

DEFINE_GUID(IID_Folder, 0xbbcbde60, 0xc3ff, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bbcbde60-c3ff-11ce-8350-************")
Folder : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Title(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ParentFolder(
        Folder **ppsf) = 0;

    virtual HRESULT STDMETHODCALLTYPE Items(
        FolderItems **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE ParseName(
        BSTR bName,
        FolderItem **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE NewFolder(
        BSTR bName,
        VARIANT vOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveHere(
        VARIANT vItem,
        VARIANT vOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyHere(
        VARIANT vItem,
        VARIANT vOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDetailsOf(
        VARIANT vItem,
        int iColumn,
        BSTR *pbs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(Folder, 0xbbcbde60, 0xc3ff, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00)
#endif
#else
typedef struct FolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        Folder* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        Folder* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        Folder* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        Folder* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        Folder* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        Folder* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        Folder* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** Folder methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Title)(
        Folder* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        Folder* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        Folder* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_ParentFolder)(
        Folder* This,
        Folder **ppsf);

    HRESULT (STDMETHODCALLTYPE *Items)(
        Folder* This,
        FolderItems **ppid);

    HRESULT (STDMETHODCALLTYPE *ParseName)(
        Folder* This,
        BSTR bName,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *NewFolder)(
        Folder* This,
        BSTR bName,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *MoveHere)(
        Folder* This,
        VARIANT vItem,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *CopyHere)(
        Folder* This,
        VARIANT vItem,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *GetDetailsOf)(
        Folder* This,
        VARIANT vItem,
        int iColumn,
        BSTR *pbs);

    END_INTERFACE
} FolderVtbl;
interface Folder {
    CONST_VTBL FolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define Folder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define Folder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define Folder_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define Folder_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define Folder_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define Folder_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define Folder_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** Folder methods ***/
#define Folder_get_Title(This,pbs) (This)->lpVtbl->get_Title(This,pbs)
#define Folder_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define Folder_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define Folder_get_ParentFolder(This,ppsf) (This)->lpVtbl->get_ParentFolder(This,ppsf)
#define Folder_Items(This,ppid) (This)->lpVtbl->Items(This,ppid)
#define Folder_ParseName(This,bName,ppid) (This)->lpVtbl->ParseName(This,bName,ppid)
#define Folder_NewFolder(This,bName,vOptions) (This)->lpVtbl->NewFolder(This,bName,vOptions)
#define Folder_MoveHere(This,vItem,vOptions) (This)->lpVtbl->MoveHere(This,vItem,vOptions)
#define Folder_CopyHere(This,vItem,vOptions) (This)->lpVtbl->CopyHere(This,vItem,vOptions)
#define Folder_GetDetailsOf(This,vItem,iColumn,pbs) (This)->lpVtbl->GetDetailsOf(This,vItem,iColumn,pbs)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT Folder_QueryInterface(Folder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG Folder_AddRef(Folder* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG Folder_Release(Folder* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT Folder_GetTypeInfoCount(Folder* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT Folder_GetTypeInfo(Folder* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT Folder_GetIDsOfNames(Folder* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT Folder_Invoke(Folder* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** Folder methods ***/
static FORCEINLINE HRESULT Folder_get_Title(Folder* This,BSTR *pbs) {
    return This->lpVtbl->get_Title(This,pbs);
}
static FORCEINLINE HRESULT Folder_get_Application(Folder* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT Folder_get_Parent(Folder* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT Folder_get_ParentFolder(Folder* This,Folder **ppsf) {
    return This->lpVtbl->get_ParentFolder(This,ppsf);
}
static FORCEINLINE HRESULT Folder_Items(Folder* This,FolderItems **ppid) {
    return This->lpVtbl->Items(This,ppid);
}
static FORCEINLINE HRESULT Folder_ParseName(Folder* This,BSTR bName,FolderItem **ppid) {
    return This->lpVtbl->ParseName(This,bName,ppid);
}
static FORCEINLINE HRESULT Folder_NewFolder(Folder* This,BSTR bName,VARIANT vOptions) {
    return This->lpVtbl->NewFolder(This,bName,vOptions);
}
static FORCEINLINE HRESULT Folder_MoveHere(Folder* This,VARIANT vItem,VARIANT vOptions) {
    return This->lpVtbl->MoveHere(This,vItem,vOptions);
}
static FORCEINLINE HRESULT Folder_CopyHere(Folder* This,VARIANT vItem,VARIANT vOptions) {
    return This->lpVtbl->CopyHere(This,vItem,vOptions);
}
static FORCEINLINE HRESULT Folder_GetDetailsOf(Folder* This,VARIANT vItem,int iColumn,BSTR *pbs) {
    return This->lpVtbl->GetDetailsOf(This,vItem,iColumn,pbs);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE Folder_get_Title_Proxy(
    Folder* This,
    BSTR *pbs);
void __RPC_STUB Folder_get_Title_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_get_Application_Proxy(
    Folder* This,
    IDispatch **ppid);
void __RPC_STUB Folder_get_Application_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_get_Parent_Proxy(
    Folder* This,
    IDispatch **ppid);
void __RPC_STUB Folder_get_Parent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_get_ParentFolder_Proxy(
    Folder* This,
    Folder **ppsf);
void __RPC_STUB Folder_get_ParentFolder_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_Items_Proxy(
    Folder* This,
    FolderItems **ppid);
void __RPC_STUB Folder_Items_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_ParseName_Proxy(
    Folder* This,
    BSTR bName,
    FolderItem **ppid);
void __RPC_STUB Folder_ParseName_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_NewFolder_Proxy(
    Folder* This,
    BSTR bName,
    VARIANT vOptions);
void __RPC_STUB Folder_NewFolder_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_MoveHere_Proxy(
    Folder* This,
    VARIANT vItem,
    VARIANT vOptions);
void __RPC_STUB Folder_MoveHere_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_CopyHere_Proxy(
    Folder* This,
    VARIANT vItem,
    VARIANT vOptions);
void __RPC_STUB Folder_CopyHere_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder_GetDetailsOf_Proxy(
    Folder* This,
    VARIANT vItem,
    int iColumn,
    BSTR *pbs);
void __RPC_STUB Folder_GetDetailsOf_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __Folder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * Folder2 interface
 */
#ifndef __Folder2_INTERFACE_DEFINED__
#define __Folder2_INTERFACE_DEFINED__

DEFINE_GUID(IID_Folder2, 0xf0d2d8ef, 0x3890, 0x11d2, 0xbf,0x8b, 0x00,0xc0,0x4f,0xb9,0x36,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f0d2d8ef-3890-11d2-bf8b-00c04fb93661")
Folder2 : public Folder
{
    virtual HRESULT STDMETHODCALLTYPE get_Self(
        FolderItem **ppfi) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_OfflineStatus(
        LONG *pul) = 0;

    virtual HRESULT STDMETHODCALLTYPE Synchronize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HaveToShowWebViewBarricade(
        VARIANT_BOOL *pbHaveToShowWebViewBarricade) = 0;

    virtual HRESULT STDMETHODCALLTYPE DismissedWebViewBarricade(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(Folder2, 0xf0d2d8ef, 0x3890, 0x11d2, 0xbf,0x8b, 0x00,0xc0,0x4f,0xb9,0x36,0x61)
#endif
#else
typedef struct Folder2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        Folder2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        Folder2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        Folder2* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        Folder2* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        Folder2* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        Folder2* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        Folder2* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** Folder methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Title)(
        Folder2* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        Folder2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        Folder2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_ParentFolder)(
        Folder2* This,
        Folder **ppsf);

    HRESULT (STDMETHODCALLTYPE *Items)(
        Folder2* This,
        FolderItems **ppid);

    HRESULT (STDMETHODCALLTYPE *ParseName)(
        Folder2* This,
        BSTR bName,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *NewFolder)(
        Folder2* This,
        BSTR bName,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *MoveHere)(
        Folder2* This,
        VARIANT vItem,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *CopyHere)(
        Folder2* This,
        VARIANT vItem,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *GetDetailsOf)(
        Folder2* This,
        VARIANT vItem,
        int iColumn,
        BSTR *pbs);

    /*** Folder2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Self)(
        Folder2* This,
        FolderItem **ppfi);

    HRESULT (STDMETHODCALLTYPE *get_OfflineStatus)(
        Folder2* This,
        LONG *pul);

    HRESULT (STDMETHODCALLTYPE *Synchronize)(
        Folder2* This);

    HRESULT (STDMETHODCALLTYPE *get_HaveToShowWebViewBarricade)(
        Folder2* This,
        VARIANT_BOOL *pbHaveToShowWebViewBarricade);

    HRESULT (STDMETHODCALLTYPE *DismissedWebViewBarricade)(
        Folder2* This);

    END_INTERFACE
} Folder2Vtbl;
interface Folder2 {
    CONST_VTBL Folder2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define Folder2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define Folder2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define Folder2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define Folder2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define Folder2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define Folder2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define Folder2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** Folder methods ***/
#define Folder2_get_Title(This,pbs) (This)->lpVtbl->get_Title(This,pbs)
#define Folder2_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define Folder2_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define Folder2_get_ParentFolder(This,ppsf) (This)->lpVtbl->get_ParentFolder(This,ppsf)
#define Folder2_Items(This,ppid) (This)->lpVtbl->Items(This,ppid)
#define Folder2_ParseName(This,bName,ppid) (This)->lpVtbl->ParseName(This,bName,ppid)
#define Folder2_NewFolder(This,bName,vOptions) (This)->lpVtbl->NewFolder(This,bName,vOptions)
#define Folder2_MoveHere(This,vItem,vOptions) (This)->lpVtbl->MoveHere(This,vItem,vOptions)
#define Folder2_CopyHere(This,vItem,vOptions) (This)->lpVtbl->CopyHere(This,vItem,vOptions)
#define Folder2_GetDetailsOf(This,vItem,iColumn,pbs) (This)->lpVtbl->GetDetailsOf(This,vItem,iColumn,pbs)
/*** Folder2 methods ***/
#define Folder2_get_Self(This,ppfi) (This)->lpVtbl->get_Self(This,ppfi)
#define Folder2_get_OfflineStatus(This,pul) (This)->lpVtbl->get_OfflineStatus(This,pul)
#define Folder2_Synchronize(This) (This)->lpVtbl->Synchronize(This)
#define Folder2_get_HaveToShowWebViewBarricade(This,pbHaveToShowWebViewBarricade) (This)->lpVtbl->get_HaveToShowWebViewBarricade(This,pbHaveToShowWebViewBarricade)
#define Folder2_DismissedWebViewBarricade(This) (This)->lpVtbl->DismissedWebViewBarricade(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT Folder2_QueryInterface(Folder2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG Folder2_AddRef(Folder2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG Folder2_Release(Folder2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT Folder2_GetTypeInfoCount(Folder2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT Folder2_GetTypeInfo(Folder2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT Folder2_GetIDsOfNames(Folder2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT Folder2_Invoke(Folder2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** Folder methods ***/
static FORCEINLINE HRESULT Folder2_get_Title(Folder2* This,BSTR *pbs) {
    return This->lpVtbl->get_Title(This,pbs);
}
static FORCEINLINE HRESULT Folder2_get_Application(Folder2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT Folder2_get_Parent(Folder2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT Folder2_get_ParentFolder(Folder2* This,Folder **ppsf) {
    return This->lpVtbl->get_ParentFolder(This,ppsf);
}
static FORCEINLINE HRESULT Folder2_Items(Folder2* This,FolderItems **ppid) {
    return This->lpVtbl->Items(This,ppid);
}
static FORCEINLINE HRESULT Folder2_ParseName(Folder2* This,BSTR bName,FolderItem **ppid) {
    return This->lpVtbl->ParseName(This,bName,ppid);
}
static FORCEINLINE HRESULT Folder2_NewFolder(Folder2* This,BSTR bName,VARIANT vOptions) {
    return This->lpVtbl->NewFolder(This,bName,vOptions);
}
static FORCEINLINE HRESULT Folder2_MoveHere(Folder2* This,VARIANT vItem,VARIANT vOptions) {
    return This->lpVtbl->MoveHere(This,vItem,vOptions);
}
static FORCEINLINE HRESULT Folder2_CopyHere(Folder2* This,VARIANT vItem,VARIANT vOptions) {
    return This->lpVtbl->CopyHere(This,vItem,vOptions);
}
static FORCEINLINE HRESULT Folder2_GetDetailsOf(Folder2* This,VARIANT vItem,int iColumn,BSTR *pbs) {
    return This->lpVtbl->GetDetailsOf(This,vItem,iColumn,pbs);
}
/*** Folder2 methods ***/
static FORCEINLINE HRESULT Folder2_get_Self(Folder2* This,FolderItem **ppfi) {
    return This->lpVtbl->get_Self(This,ppfi);
}
static FORCEINLINE HRESULT Folder2_get_OfflineStatus(Folder2* This,LONG *pul) {
    return This->lpVtbl->get_OfflineStatus(This,pul);
}
static FORCEINLINE HRESULT Folder2_Synchronize(Folder2* This) {
    return This->lpVtbl->Synchronize(This);
}
static FORCEINLINE HRESULT Folder2_get_HaveToShowWebViewBarricade(Folder2* This,VARIANT_BOOL *pbHaveToShowWebViewBarricade) {
    return This->lpVtbl->get_HaveToShowWebViewBarricade(This,pbHaveToShowWebViewBarricade);
}
static FORCEINLINE HRESULT Folder2_DismissedWebViewBarricade(Folder2* This) {
    return This->lpVtbl->DismissedWebViewBarricade(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE Folder2_get_Self_Proxy(
    Folder2* This,
    FolderItem **ppfi);
void __RPC_STUB Folder2_get_Self_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder2_get_OfflineStatus_Proxy(
    Folder2* This,
    LONG *pul);
void __RPC_STUB Folder2_get_OfflineStatus_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder2_Synchronize_Proxy(
    Folder2* This);
void __RPC_STUB Folder2_Synchronize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder2_get_HaveToShowWebViewBarricade_Proxy(
    Folder2* This,
    VARIANT_BOOL *pbHaveToShowWebViewBarricade);
void __RPC_STUB Folder2_get_HaveToShowWebViewBarricade_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder2_DismissedWebViewBarricade_Proxy(
    Folder2* This);
void __RPC_STUB Folder2_DismissedWebViewBarricade_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __Folder2_INTERFACE_DEFINED__ */

typedef enum OfflineFolderStatus {
    OFS_INACTIVE = -1,
    OFS_ONLINE = 0,
    OFS_OFFLINE = 1,
    OFS_SERVERBACK = 2,
    OFS_DIRTYCACHE = 3
} OfflineFolderStatus;
/*****************************************************************************
 * Folder3 interface
 */
#ifndef __Folder3_INTERFACE_DEFINED__
#define __Folder3_INTERFACE_DEFINED__

DEFINE_GUID(IID_Folder3, 0xa7ae5f64, 0xc4d7, 0x4d7f, 0x93,0x07, 0x4d,0x24,0xee,0x54,0xb8,0x41);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a7ae5f64-c4d7-4d7f-9307-4d24ee54b841")
Folder3 : public Folder2
{
    virtual HRESULT STDMETHODCALLTYPE get_ShowWebViewBarricade(
        VARIANT_BOOL *pbShowWebViewBarricade) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ShowWebViewBarricade(
        VARIANT_BOOL bShowWebViewBarricade) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(Folder3, 0xa7ae5f64, 0xc4d7, 0x4d7f, 0x93,0x07, 0x4d,0x24,0xee,0x54,0xb8,0x41)
#endif
#else
typedef struct Folder3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        Folder3* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        Folder3* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        Folder3* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        Folder3* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        Folder3* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        Folder3* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        Folder3* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** Folder methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Title)(
        Folder3* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        Folder3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        Folder3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_ParentFolder)(
        Folder3* This,
        Folder **ppsf);

    HRESULT (STDMETHODCALLTYPE *Items)(
        Folder3* This,
        FolderItems **ppid);

    HRESULT (STDMETHODCALLTYPE *ParseName)(
        Folder3* This,
        BSTR bName,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *NewFolder)(
        Folder3* This,
        BSTR bName,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *MoveHere)(
        Folder3* This,
        VARIANT vItem,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *CopyHere)(
        Folder3* This,
        VARIANT vItem,
        VARIANT vOptions);

    HRESULT (STDMETHODCALLTYPE *GetDetailsOf)(
        Folder3* This,
        VARIANT vItem,
        int iColumn,
        BSTR *pbs);

    /*** Folder2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Self)(
        Folder3* This,
        FolderItem **ppfi);

    HRESULT (STDMETHODCALLTYPE *get_OfflineStatus)(
        Folder3* This,
        LONG *pul);

    HRESULT (STDMETHODCALLTYPE *Synchronize)(
        Folder3* This);

    HRESULT (STDMETHODCALLTYPE *get_HaveToShowWebViewBarricade)(
        Folder3* This,
        VARIANT_BOOL *pbHaveToShowWebViewBarricade);

    HRESULT (STDMETHODCALLTYPE *DismissedWebViewBarricade)(
        Folder3* This);

    /*** Folder3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ShowWebViewBarricade)(
        Folder3* This,
        VARIANT_BOOL *pbShowWebViewBarricade);

    HRESULT (STDMETHODCALLTYPE *put_ShowWebViewBarricade)(
        Folder3* This,
        VARIANT_BOOL bShowWebViewBarricade);

    END_INTERFACE
} Folder3Vtbl;
interface Folder3 {
    CONST_VTBL Folder3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define Folder3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define Folder3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define Folder3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define Folder3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define Folder3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define Folder3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define Folder3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** Folder methods ***/
#define Folder3_get_Title(This,pbs) (This)->lpVtbl->get_Title(This,pbs)
#define Folder3_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define Folder3_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define Folder3_get_ParentFolder(This,ppsf) (This)->lpVtbl->get_ParentFolder(This,ppsf)
#define Folder3_Items(This,ppid) (This)->lpVtbl->Items(This,ppid)
#define Folder3_ParseName(This,bName,ppid) (This)->lpVtbl->ParseName(This,bName,ppid)
#define Folder3_NewFolder(This,bName,vOptions) (This)->lpVtbl->NewFolder(This,bName,vOptions)
#define Folder3_MoveHere(This,vItem,vOptions) (This)->lpVtbl->MoveHere(This,vItem,vOptions)
#define Folder3_CopyHere(This,vItem,vOptions) (This)->lpVtbl->CopyHere(This,vItem,vOptions)
#define Folder3_GetDetailsOf(This,vItem,iColumn,pbs) (This)->lpVtbl->GetDetailsOf(This,vItem,iColumn,pbs)
/*** Folder2 methods ***/
#define Folder3_get_Self(This,ppfi) (This)->lpVtbl->get_Self(This,ppfi)
#define Folder3_get_OfflineStatus(This,pul) (This)->lpVtbl->get_OfflineStatus(This,pul)
#define Folder3_Synchronize(This) (This)->lpVtbl->Synchronize(This)
#define Folder3_get_HaveToShowWebViewBarricade(This,pbHaveToShowWebViewBarricade) (This)->lpVtbl->get_HaveToShowWebViewBarricade(This,pbHaveToShowWebViewBarricade)
#define Folder3_DismissedWebViewBarricade(This) (This)->lpVtbl->DismissedWebViewBarricade(This)
/*** Folder3 methods ***/
#define Folder3_get_ShowWebViewBarricade(This,pbShowWebViewBarricade) (This)->lpVtbl->get_ShowWebViewBarricade(This,pbShowWebViewBarricade)
#define Folder3_put_ShowWebViewBarricade(This,bShowWebViewBarricade) (This)->lpVtbl->put_ShowWebViewBarricade(This,bShowWebViewBarricade)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT Folder3_QueryInterface(Folder3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG Folder3_AddRef(Folder3* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG Folder3_Release(Folder3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT Folder3_GetTypeInfoCount(Folder3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT Folder3_GetTypeInfo(Folder3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT Folder3_GetIDsOfNames(Folder3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT Folder3_Invoke(Folder3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** Folder methods ***/
static FORCEINLINE HRESULT Folder3_get_Title(Folder3* This,BSTR *pbs) {
    return This->lpVtbl->get_Title(This,pbs);
}
static FORCEINLINE HRESULT Folder3_get_Application(Folder3* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT Folder3_get_Parent(Folder3* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT Folder3_get_ParentFolder(Folder3* This,Folder **ppsf) {
    return This->lpVtbl->get_ParentFolder(This,ppsf);
}
static FORCEINLINE HRESULT Folder3_Items(Folder3* This,FolderItems **ppid) {
    return This->lpVtbl->Items(This,ppid);
}
static FORCEINLINE HRESULT Folder3_ParseName(Folder3* This,BSTR bName,FolderItem **ppid) {
    return This->lpVtbl->ParseName(This,bName,ppid);
}
static FORCEINLINE HRESULT Folder3_NewFolder(Folder3* This,BSTR bName,VARIANT vOptions) {
    return This->lpVtbl->NewFolder(This,bName,vOptions);
}
static FORCEINLINE HRESULT Folder3_MoveHere(Folder3* This,VARIANT vItem,VARIANT vOptions) {
    return This->lpVtbl->MoveHere(This,vItem,vOptions);
}
static FORCEINLINE HRESULT Folder3_CopyHere(Folder3* This,VARIANT vItem,VARIANT vOptions) {
    return This->lpVtbl->CopyHere(This,vItem,vOptions);
}
static FORCEINLINE HRESULT Folder3_GetDetailsOf(Folder3* This,VARIANT vItem,int iColumn,BSTR *pbs) {
    return This->lpVtbl->GetDetailsOf(This,vItem,iColumn,pbs);
}
/*** Folder2 methods ***/
static FORCEINLINE HRESULT Folder3_get_Self(Folder3* This,FolderItem **ppfi) {
    return This->lpVtbl->get_Self(This,ppfi);
}
static FORCEINLINE HRESULT Folder3_get_OfflineStatus(Folder3* This,LONG *pul) {
    return This->lpVtbl->get_OfflineStatus(This,pul);
}
static FORCEINLINE HRESULT Folder3_Synchronize(Folder3* This) {
    return This->lpVtbl->Synchronize(This);
}
static FORCEINLINE HRESULT Folder3_get_HaveToShowWebViewBarricade(Folder3* This,VARIANT_BOOL *pbHaveToShowWebViewBarricade) {
    return This->lpVtbl->get_HaveToShowWebViewBarricade(This,pbHaveToShowWebViewBarricade);
}
static FORCEINLINE HRESULT Folder3_DismissedWebViewBarricade(Folder3* This) {
    return This->lpVtbl->DismissedWebViewBarricade(This);
}
/*** Folder3 methods ***/
static FORCEINLINE HRESULT Folder3_get_ShowWebViewBarricade(Folder3* This,VARIANT_BOOL *pbShowWebViewBarricade) {
    return This->lpVtbl->get_ShowWebViewBarricade(This,pbShowWebViewBarricade);
}
static FORCEINLINE HRESULT Folder3_put_ShowWebViewBarricade(Folder3* This,VARIANT_BOOL bShowWebViewBarricade) {
    return This->lpVtbl->put_ShowWebViewBarricade(This,bShowWebViewBarricade);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE Folder3_get_ShowWebViewBarricade_Proxy(
    Folder3* This,
    VARIANT_BOOL *pbShowWebViewBarricade);
void __RPC_STUB Folder3_get_ShowWebViewBarricade_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE Folder3_put_ShowWebViewBarricade_Proxy(
    Folder3* This,
    VARIANT_BOOL bShowWebViewBarricade);
void __RPC_STUB Folder3_put_ShowWebViewBarricade_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __Folder3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * FolderItem interface
 */
#ifndef __FolderItem_INTERFACE_DEFINED__
#define __FolderItem_INTERFACE_DEFINED__

typedef FolderItem *LPFOLDERITEM;
DEFINE_GUID(IID_FolderItem, 0xfac32c80, 0xcbe4, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fac32c80-cbe4-11ce-8350-************")
FolderItem : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR bs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_GetLink(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_GetFolder(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsLink(
        VARIANT_BOOL *pb) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsFolder(
        VARIANT_BOOL *pb) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsFileSystem(
        VARIANT_BOOL *pb) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsBrowsable(
        VARIANT_BOOL *pb) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ModifyDate(
        DATE *pdt) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ModifyDate(
        DATE dt) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Size(
        LONG *pul) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE Verbs(
        FolderItemVerbs **ppfic) = 0;

    virtual HRESULT STDMETHODCALLTYPE InvokeVerb(
        VARIANT vVerb) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(FolderItem, 0xfac32c80, 0xcbe4, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00)
#endif
#else
typedef struct FolderItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        FolderItem* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        FolderItem* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        FolderItem* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        FolderItem* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        FolderItem* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        FolderItem* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        FolderItem* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** FolderItem methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        FolderItem* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        FolderItem* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        FolderItem* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        FolderItem* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_Path)(
        FolderItem* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_GetLink)(
        FolderItem* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_GetFolder)(
        FolderItem* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_IsLink)(
        FolderItem* This,
        VARIANT_BOOL *pb);

    HRESULT (STDMETHODCALLTYPE *get_IsFolder)(
        FolderItem* This,
        VARIANT_BOOL *pb);

    HRESULT (STDMETHODCALLTYPE *get_IsFileSystem)(
        FolderItem* This,
        VARIANT_BOOL *pb);

    HRESULT (STDMETHODCALLTYPE *get_IsBrowsable)(
        FolderItem* This,
        VARIANT_BOOL *pb);

    HRESULT (STDMETHODCALLTYPE *get_ModifyDate)(
        FolderItem* This,
        DATE *pdt);

    HRESULT (STDMETHODCALLTYPE *put_ModifyDate)(
        FolderItem* This,
        DATE dt);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        FolderItem* This,
        LONG *pul);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        FolderItem* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *Verbs)(
        FolderItem* This,
        FolderItemVerbs **ppfic);

    HRESULT (STDMETHODCALLTYPE *InvokeVerb)(
        FolderItem* This,
        VARIANT vVerb);

    END_INTERFACE
} FolderItemVtbl;
interface FolderItem {
    CONST_VTBL FolderItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define FolderItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define FolderItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define FolderItem_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define FolderItem_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define FolderItem_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define FolderItem_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define FolderItem_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** FolderItem methods ***/
#define FolderItem_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define FolderItem_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define FolderItem_get_Name(This,pbs) (This)->lpVtbl->get_Name(This,pbs)
#define FolderItem_put_Name(This,bs) (This)->lpVtbl->put_Name(This,bs)
#define FolderItem_get_Path(This,pbs) (This)->lpVtbl->get_Path(This,pbs)
#define FolderItem_get_GetLink(This,ppid) (This)->lpVtbl->get_GetLink(This,ppid)
#define FolderItem_get_GetFolder(This,ppid) (This)->lpVtbl->get_GetFolder(This,ppid)
#define FolderItem_get_IsLink(This,pb) (This)->lpVtbl->get_IsLink(This,pb)
#define FolderItem_get_IsFolder(This,pb) (This)->lpVtbl->get_IsFolder(This,pb)
#define FolderItem_get_IsFileSystem(This,pb) (This)->lpVtbl->get_IsFileSystem(This,pb)
#define FolderItem_get_IsBrowsable(This,pb) (This)->lpVtbl->get_IsBrowsable(This,pb)
#define FolderItem_get_ModifyDate(This,pdt) (This)->lpVtbl->get_ModifyDate(This,pdt)
#define FolderItem_put_ModifyDate(This,dt) (This)->lpVtbl->put_ModifyDate(This,dt)
#define FolderItem_get_Size(This,pul) (This)->lpVtbl->get_Size(This,pul)
#define FolderItem_get_Type(This,pbs) (This)->lpVtbl->get_Type(This,pbs)
#define FolderItem_Verbs(This,ppfic) (This)->lpVtbl->Verbs(This,ppfic)
#define FolderItem_InvokeVerb(This,vVerb) (This)->lpVtbl->InvokeVerb(This,vVerb)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT FolderItem_QueryInterface(FolderItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG FolderItem_AddRef(FolderItem* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG FolderItem_Release(FolderItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT FolderItem_GetTypeInfoCount(FolderItem* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT FolderItem_GetTypeInfo(FolderItem* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT FolderItem_GetIDsOfNames(FolderItem* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT FolderItem_Invoke(FolderItem* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** FolderItem methods ***/
static FORCEINLINE HRESULT FolderItem_get_Application(FolderItem* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT FolderItem_get_Parent(FolderItem* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT FolderItem_get_Name(FolderItem* This,BSTR *pbs) {
    return This->lpVtbl->get_Name(This,pbs);
}
static FORCEINLINE HRESULT FolderItem_put_Name(FolderItem* This,BSTR bs) {
    return This->lpVtbl->put_Name(This,bs);
}
static FORCEINLINE HRESULT FolderItem_get_Path(FolderItem* This,BSTR *pbs) {
    return This->lpVtbl->get_Path(This,pbs);
}
static FORCEINLINE HRESULT FolderItem_get_GetLink(FolderItem* This,IDispatch **ppid) {
    return This->lpVtbl->get_GetLink(This,ppid);
}
static FORCEINLINE HRESULT FolderItem_get_GetFolder(FolderItem* This,IDispatch **ppid) {
    return This->lpVtbl->get_GetFolder(This,ppid);
}
static FORCEINLINE HRESULT FolderItem_get_IsLink(FolderItem* This,VARIANT_BOOL *pb) {
    return This->lpVtbl->get_IsLink(This,pb);
}
static FORCEINLINE HRESULT FolderItem_get_IsFolder(FolderItem* This,VARIANT_BOOL *pb) {
    return This->lpVtbl->get_IsFolder(This,pb);
}
static FORCEINLINE HRESULT FolderItem_get_IsFileSystem(FolderItem* This,VARIANT_BOOL *pb) {
    return This->lpVtbl->get_IsFileSystem(This,pb);
}
static FORCEINLINE HRESULT FolderItem_get_IsBrowsable(FolderItem* This,VARIANT_BOOL *pb) {
    return This->lpVtbl->get_IsBrowsable(This,pb);
}
static FORCEINLINE HRESULT FolderItem_get_ModifyDate(FolderItem* This,DATE *pdt) {
    return This->lpVtbl->get_ModifyDate(This,pdt);
}
static FORCEINLINE HRESULT FolderItem_put_ModifyDate(FolderItem* This,DATE dt) {
    return This->lpVtbl->put_ModifyDate(This,dt);
}
static FORCEINLINE HRESULT FolderItem_get_Size(FolderItem* This,LONG *pul) {
    return This->lpVtbl->get_Size(This,pul);
}
static FORCEINLINE HRESULT FolderItem_get_Type(FolderItem* This,BSTR *pbs) {
    return This->lpVtbl->get_Type(This,pbs);
}
static FORCEINLINE HRESULT FolderItem_Verbs(FolderItem* This,FolderItemVerbs **ppfic) {
    return This->lpVtbl->Verbs(This,ppfic);
}
static FORCEINLINE HRESULT FolderItem_InvokeVerb(FolderItem* This,VARIANT vVerb) {
    return This->lpVtbl->InvokeVerb(This,vVerb);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE FolderItem_get_Application_Proxy(
    FolderItem* This,
    IDispatch **ppid);
void __RPC_STUB FolderItem_get_Application_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_Parent_Proxy(
    FolderItem* This,
    IDispatch **ppid);
void __RPC_STUB FolderItem_get_Parent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_Name_Proxy(
    FolderItem* This,
    BSTR *pbs);
void __RPC_STUB FolderItem_get_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_put_Name_Proxy(
    FolderItem* This,
    BSTR bs);
void __RPC_STUB FolderItem_put_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_Path_Proxy(
    FolderItem* This,
    BSTR *pbs);
void __RPC_STUB FolderItem_get_Path_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_GetLink_Proxy(
    FolderItem* This,
    IDispatch **ppid);
void __RPC_STUB FolderItem_get_GetLink_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_GetFolder_Proxy(
    FolderItem* This,
    IDispatch **ppid);
void __RPC_STUB FolderItem_get_GetFolder_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_IsLink_Proxy(
    FolderItem* This,
    VARIANT_BOOL *pb);
void __RPC_STUB FolderItem_get_IsLink_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_IsFolder_Proxy(
    FolderItem* This,
    VARIANT_BOOL *pb);
void __RPC_STUB FolderItem_get_IsFolder_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_IsFileSystem_Proxy(
    FolderItem* This,
    VARIANT_BOOL *pb);
void __RPC_STUB FolderItem_get_IsFileSystem_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_IsBrowsable_Proxy(
    FolderItem* This,
    VARIANT_BOOL *pb);
void __RPC_STUB FolderItem_get_IsBrowsable_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_ModifyDate_Proxy(
    FolderItem* This,
    DATE *pdt);
void __RPC_STUB FolderItem_get_ModifyDate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_put_ModifyDate_Proxy(
    FolderItem* This,
    DATE dt);
void __RPC_STUB FolderItem_put_ModifyDate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_Size_Proxy(
    FolderItem* This,
    LONG *pul);
void __RPC_STUB FolderItem_get_Size_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_get_Type_Proxy(
    FolderItem* This,
    BSTR *pbs);
void __RPC_STUB FolderItem_get_Type_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_Verbs_Proxy(
    FolderItem* This,
    FolderItemVerbs **ppfic);
void __RPC_STUB FolderItem_Verbs_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem_InvokeVerb_Proxy(
    FolderItem* This,
    VARIANT vVerb);
void __RPC_STUB FolderItem_InvokeVerb_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __FolderItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * FolderItem2 interface
 */
#ifndef __FolderItem2_INTERFACE_DEFINED__
#define __FolderItem2_INTERFACE_DEFINED__

DEFINE_GUID(IID_FolderItem2, 0xedc817aa, 0x92b8, 0x11d1, 0xb0,0x75, 0x00,0xc0,0x4f,0xc3,0x3a,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("edc817aa-92b8-11d1-b075-00c04fc33aa5")
FolderItem2 : public FolderItem
{
    virtual HRESULT STDMETHODCALLTYPE InvokeVerbEx(
        VARIANT vVerb,
        VARIANT vArgs) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExtendedProperty(
        BSTR bstrPropName,
        VARIANT *pvRet) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(FolderItem2, 0xedc817aa, 0x92b8, 0x11d1, 0xb0,0x75, 0x00,0xc0,0x4f,0xc3,0x3a,0xa5)
#endif
#else
typedef struct FolderItem2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        FolderItem2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        FolderItem2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        FolderItem2* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        FolderItem2* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        FolderItem2* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        FolderItem2* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        FolderItem2* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** FolderItem methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        FolderItem2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        FolderItem2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        FolderItem2* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        FolderItem2* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_Path)(
        FolderItem2* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_GetLink)(
        FolderItem2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_GetFolder)(
        FolderItem2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_IsLink)(
        FolderItem2* This,
        VARIANT_BOOL *pb);

    HRESULT (STDMETHODCALLTYPE *get_IsFolder)(
        FolderItem2* This,
        VARIANT_BOOL *pb);

    HRESULT (STDMETHODCALLTYPE *get_IsFileSystem)(
        FolderItem2* This,
        VARIANT_BOOL *pb);

    HRESULT (STDMETHODCALLTYPE *get_IsBrowsable)(
        FolderItem2* This,
        VARIANT_BOOL *pb);

    HRESULT (STDMETHODCALLTYPE *get_ModifyDate)(
        FolderItem2* This,
        DATE *pdt);

    HRESULT (STDMETHODCALLTYPE *put_ModifyDate)(
        FolderItem2* This,
        DATE dt);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        FolderItem2* This,
        LONG *pul);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        FolderItem2* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *Verbs)(
        FolderItem2* This,
        FolderItemVerbs **ppfic);

    HRESULT (STDMETHODCALLTYPE *InvokeVerb)(
        FolderItem2* This,
        VARIANT vVerb);

    /*** FolderItem2 methods ***/
    HRESULT (STDMETHODCALLTYPE *InvokeVerbEx)(
        FolderItem2* This,
        VARIANT vVerb,
        VARIANT vArgs);

    HRESULT (STDMETHODCALLTYPE *ExtendedProperty)(
        FolderItem2* This,
        BSTR bstrPropName,
        VARIANT *pvRet);

    END_INTERFACE
} FolderItem2Vtbl;
interface FolderItem2 {
    CONST_VTBL FolderItem2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define FolderItem2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define FolderItem2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define FolderItem2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define FolderItem2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define FolderItem2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define FolderItem2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define FolderItem2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** FolderItem methods ***/
#define FolderItem2_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define FolderItem2_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define FolderItem2_get_Name(This,pbs) (This)->lpVtbl->get_Name(This,pbs)
#define FolderItem2_put_Name(This,bs) (This)->lpVtbl->put_Name(This,bs)
#define FolderItem2_get_Path(This,pbs) (This)->lpVtbl->get_Path(This,pbs)
#define FolderItem2_get_GetLink(This,ppid) (This)->lpVtbl->get_GetLink(This,ppid)
#define FolderItem2_get_GetFolder(This,ppid) (This)->lpVtbl->get_GetFolder(This,ppid)
#define FolderItem2_get_IsLink(This,pb) (This)->lpVtbl->get_IsLink(This,pb)
#define FolderItem2_get_IsFolder(This,pb) (This)->lpVtbl->get_IsFolder(This,pb)
#define FolderItem2_get_IsFileSystem(This,pb) (This)->lpVtbl->get_IsFileSystem(This,pb)
#define FolderItem2_get_IsBrowsable(This,pb) (This)->lpVtbl->get_IsBrowsable(This,pb)
#define FolderItem2_get_ModifyDate(This,pdt) (This)->lpVtbl->get_ModifyDate(This,pdt)
#define FolderItem2_put_ModifyDate(This,dt) (This)->lpVtbl->put_ModifyDate(This,dt)
#define FolderItem2_get_Size(This,pul) (This)->lpVtbl->get_Size(This,pul)
#define FolderItem2_get_Type(This,pbs) (This)->lpVtbl->get_Type(This,pbs)
#define FolderItem2_Verbs(This,ppfic) (This)->lpVtbl->Verbs(This,ppfic)
#define FolderItem2_InvokeVerb(This,vVerb) (This)->lpVtbl->InvokeVerb(This,vVerb)
/*** FolderItem2 methods ***/
#define FolderItem2_InvokeVerbEx(This,vVerb,vArgs) (This)->lpVtbl->InvokeVerbEx(This,vVerb,vArgs)
#define FolderItem2_ExtendedProperty(This,bstrPropName,pvRet) (This)->lpVtbl->ExtendedProperty(This,bstrPropName,pvRet)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT FolderItem2_QueryInterface(FolderItem2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG FolderItem2_AddRef(FolderItem2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG FolderItem2_Release(FolderItem2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT FolderItem2_GetTypeInfoCount(FolderItem2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT FolderItem2_GetTypeInfo(FolderItem2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT FolderItem2_GetIDsOfNames(FolderItem2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT FolderItem2_Invoke(FolderItem2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** FolderItem methods ***/
static FORCEINLINE HRESULT FolderItem2_get_Application(FolderItem2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT FolderItem2_get_Parent(FolderItem2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT FolderItem2_get_Name(FolderItem2* This,BSTR *pbs) {
    return This->lpVtbl->get_Name(This,pbs);
}
static FORCEINLINE HRESULT FolderItem2_put_Name(FolderItem2* This,BSTR bs) {
    return This->lpVtbl->put_Name(This,bs);
}
static FORCEINLINE HRESULT FolderItem2_get_Path(FolderItem2* This,BSTR *pbs) {
    return This->lpVtbl->get_Path(This,pbs);
}
static FORCEINLINE HRESULT FolderItem2_get_GetLink(FolderItem2* This,IDispatch **ppid) {
    return This->lpVtbl->get_GetLink(This,ppid);
}
static FORCEINLINE HRESULT FolderItem2_get_GetFolder(FolderItem2* This,IDispatch **ppid) {
    return This->lpVtbl->get_GetFolder(This,ppid);
}
static FORCEINLINE HRESULT FolderItem2_get_IsLink(FolderItem2* This,VARIANT_BOOL *pb) {
    return This->lpVtbl->get_IsLink(This,pb);
}
static FORCEINLINE HRESULT FolderItem2_get_IsFolder(FolderItem2* This,VARIANT_BOOL *pb) {
    return This->lpVtbl->get_IsFolder(This,pb);
}
static FORCEINLINE HRESULT FolderItem2_get_IsFileSystem(FolderItem2* This,VARIANT_BOOL *pb) {
    return This->lpVtbl->get_IsFileSystem(This,pb);
}
static FORCEINLINE HRESULT FolderItem2_get_IsBrowsable(FolderItem2* This,VARIANT_BOOL *pb) {
    return This->lpVtbl->get_IsBrowsable(This,pb);
}
static FORCEINLINE HRESULT FolderItem2_get_ModifyDate(FolderItem2* This,DATE *pdt) {
    return This->lpVtbl->get_ModifyDate(This,pdt);
}
static FORCEINLINE HRESULT FolderItem2_put_ModifyDate(FolderItem2* This,DATE dt) {
    return This->lpVtbl->put_ModifyDate(This,dt);
}
static FORCEINLINE HRESULT FolderItem2_get_Size(FolderItem2* This,LONG *pul) {
    return This->lpVtbl->get_Size(This,pul);
}
static FORCEINLINE HRESULT FolderItem2_get_Type(FolderItem2* This,BSTR *pbs) {
    return This->lpVtbl->get_Type(This,pbs);
}
static FORCEINLINE HRESULT FolderItem2_Verbs(FolderItem2* This,FolderItemVerbs **ppfic) {
    return This->lpVtbl->Verbs(This,ppfic);
}
static FORCEINLINE HRESULT FolderItem2_InvokeVerb(FolderItem2* This,VARIANT vVerb) {
    return This->lpVtbl->InvokeVerb(This,vVerb);
}
/*** FolderItem2 methods ***/
static FORCEINLINE HRESULT FolderItem2_InvokeVerbEx(FolderItem2* This,VARIANT vVerb,VARIANT vArgs) {
    return This->lpVtbl->InvokeVerbEx(This,vVerb,vArgs);
}
static FORCEINLINE HRESULT FolderItem2_ExtendedProperty(FolderItem2* This,BSTR bstrPropName,VARIANT *pvRet) {
    return This->lpVtbl->ExtendedProperty(This,bstrPropName,pvRet);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE FolderItem2_InvokeVerbEx_Proxy(
    FolderItem2* This,
    VARIANT vVerb,
    VARIANT vArgs);
void __RPC_STUB FolderItem2_InvokeVerbEx_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItem2_ExtendedProperty_Proxy(
    FolderItem2* This,
    BSTR bstrPropName,
    VARIANT *pvRet);
void __RPC_STUB FolderItem2_ExtendedProperty_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __FolderItem2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ShellFolderItem coclass
 */

DEFINE_GUID(CLSID_ShellFolderItem, 0x2fe352ea, 0xfd1f, 0x11d2, 0xb1,0xf4, 0x00,0xc0,0x4f,0x8e,0xeb,0x3e);

#ifdef __cplusplus
class DECLSPEC_UUID("2fe352ea-fd1f-11d2-b1f4-00c04f8eeb3e") ShellFolderItem;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellFolderItem, 0x2fe352ea, 0xfd1f, 0x11d2, 0xb1,0xf4, 0x00,0xc0,0x4f,0x8e,0xeb,0x3e)
#endif
#endif

/*****************************************************************************
 * FolderItems interface
 */
#ifndef __FolderItems_INTERFACE_DEFINED__
#define __FolderItems_INTERFACE_DEFINED__

DEFINE_GUID(IID_FolderItems, 0x744129e0, 0xcbe5, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("744129e0-cbe5-11ce-8350-************")
FolderItems : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        VARIANT index,
        FolderItem **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE _NewEnum(
        IUnknown **ppunk) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(FolderItems, 0x744129e0, 0xcbe5, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00)
#endif
#else
typedef struct FolderItemsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        FolderItems* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        FolderItems* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        FolderItems* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        FolderItems* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        FolderItems* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        FolderItems* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        FolderItems* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** FolderItems methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        FolderItems* This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        FolderItems* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        FolderItems* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Item)(
        FolderItems* This,
        VARIANT index,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *_NewEnum)(
        FolderItems* This,
        IUnknown **ppunk);

    END_INTERFACE
} FolderItemsVtbl;
interface FolderItems {
    CONST_VTBL FolderItemsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define FolderItems_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define FolderItems_AddRef(This) (This)->lpVtbl->AddRef(This)
#define FolderItems_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define FolderItems_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define FolderItems_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define FolderItems_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define FolderItems_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** FolderItems methods ***/
#define FolderItems_get_Count(This,plCount) (This)->lpVtbl->get_Count(This,plCount)
#define FolderItems_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define FolderItems_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define FolderItems_Item(This,index,ppid) (This)->lpVtbl->Item(This,index,ppid)
#define FolderItems__NewEnum(This,ppunk) (This)->lpVtbl->_NewEnum(This,ppunk)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT FolderItems_QueryInterface(FolderItems* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG FolderItems_AddRef(FolderItems* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG FolderItems_Release(FolderItems* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT FolderItems_GetTypeInfoCount(FolderItems* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT FolderItems_GetTypeInfo(FolderItems* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT FolderItems_GetIDsOfNames(FolderItems* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT FolderItems_Invoke(FolderItems* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** FolderItems methods ***/
static FORCEINLINE HRESULT FolderItems_get_Count(FolderItems* This,LONG *plCount) {
    return This->lpVtbl->get_Count(This,plCount);
}
static FORCEINLINE HRESULT FolderItems_get_Application(FolderItems* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT FolderItems_get_Parent(FolderItems* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT FolderItems_Item(FolderItems* This,VARIANT index,FolderItem **ppid) {
    return This->lpVtbl->Item(This,index,ppid);
}
static FORCEINLINE HRESULT FolderItems__NewEnum(FolderItems* This,IUnknown **ppunk) {
    return This->lpVtbl->_NewEnum(This,ppunk);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE FolderItems_get_Count_Proxy(
    FolderItems* This,
    LONG *plCount);
void __RPC_STUB FolderItems_get_Count_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItems_get_Application_Proxy(
    FolderItems* This,
    IDispatch **ppid);
void __RPC_STUB FolderItems_get_Application_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItems_get_Parent_Proxy(
    FolderItems* This,
    IDispatch **ppid);
void __RPC_STUB FolderItems_get_Parent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItems_Item_Proxy(
    FolderItems* This,
    VARIANT index,
    FolderItem **ppid);
void __RPC_STUB FolderItems_Item_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItems__NewEnum_Proxy(
    FolderItems* This,
    IUnknown **ppunk);
void __RPC_STUB FolderItems__NewEnum_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __FolderItems_INTERFACE_DEFINED__ */

/*****************************************************************************
 * FolderItems2 interface
 */
#ifndef __FolderItems2_INTERFACE_DEFINED__
#define __FolderItems2_INTERFACE_DEFINED__

DEFINE_GUID(IID_FolderItems2, 0xc94f0ad0, 0xf363, 0x11d2, 0xa3,0x27, 0x00,0xc0,0x4f,0x8e,0xec,0x7f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c94f0ad0-f363-11d2-a327-00c04f8eec7f")
FolderItems2 : public FolderItems
{
    virtual HRESULT STDMETHODCALLTYPE InvokeVerbEx(
        VARIANT vVerb,
        VARIANT vArgs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(FolderItems2, 0xc94f0ad0, 0xf363, 0x11d2, 0xa3,0x27, 0x00,0xc0,0x4f,0x8e,0xec,0x7f)
#endif
#else
typedef struct FolderItems2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        FolderItems2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        FolderItems2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        FolderItems2* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        FolderItems2* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        FolderItems2* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        FolderItems2* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        FolderItems2* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** FolderItems methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        FolderItems2* This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        FolderItems2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        FolderItems2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Item)(
        FolderItems2* This,
        VARIANT index,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *_NewEnum)(
        FolderItems2* This,
        IUnknown **ppunk);

    /*** FolderItems2 methods ***/
    HRESULT (STDMETHODCALLTYPE *InvokeVerbEx)(
        FolderItems2* This,
        VARIANT vVerb,
        VARIANT vArgs);

    END_INTERFACE
} FolderItems2Vtbl;
interface FolderItems2 {
    CONST_VTBL FolderItems2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define FolderItems2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define FolderItems2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define FolderItems2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define FolderItems2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define FolderItems2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define FolderItems2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define FolderItems2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** FolderItems methods ***/
#define FolderItems2_get_Count(This,plCount) (This)->lpVtbl->get_Count(This,plCount)
#define FolderItems2_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define FolderItems2_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define FolderItems2_Item(This,index,ppid) (This)->lpVtbl->Item(This,index,ppid)
#define FolderItems2__NewEnum(This,ppunk) (This)->lpVtbl->_NewEnum(This,ppunk)
/*** FolderItems2 methods ***/
#define FolderItems2_InvokeVerbEx(This,vVerb,vArgs) (This)->lpVtbl->InvokeVerbEx(This,vVerb,vArgs)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT FolderItems2_QueryInterface(FolderItems2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG FolderItems2_AddRef(FolderItems2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG FolderItems2_Release(FolderItems2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT FolderItems2_GetTypeInfoCount(FolderItems2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT FolderItems2_GetTypeInfo(FolderItems2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT FolderItems2_GetIDsOfNames(FolderItems2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT FolderItems2_Invoke(FolderItems2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** FolderItems methods ***/
static FORCEINLINE HRESULT FolderItems2_get_Count(FolderItems2* This,LONG *plCount) {
    return This->lpVtbl->get_Count(This,plCount);
}
static FORCEINLINE HRESULT FolderItems2_get_Application(FolderItems2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT FolderItems2_get_Parent(FolderItems2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT FolderItems2_Item(FolderItems2* This,VARIANT index,FolderItem **ppid) {
    return This->lpVtbl->Item(This,index,ppid);
}
static FORCEINLINE HRESULT FolderItems2__NewEnum(FolderItems2* This,IUnknown **ppunk) {
    return This->lpVtbl->_NewEnum(This,ppunk);
}
/*** FolderItems2 methods ***/
static FORCEINLINE HRESULT FolderItems2_InvokeVerbEx(FolderItems2* This,VARIANT vVerb,VARIANT vArgs) {
    return This->lpVtbl->InvokeVerbEx(This,vVerb,vArgs);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE FolderItems2_InvokeVerbEx_Proxy(
    FolderItems2* This,
    VARIANT vVerb,
    VARIANT vArgs);
void __RPC_STUB FolderItems2_InvokeVerbEx_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __FolderItems2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * FolderItems3 interface
 */
#ifndef __FolderItems3_INTERFACE_DEFINED__
#define __FolderItems3_INTERFACE_DEFINED__

DEFINE_GUID(IID_FolderItems3, 0xeaa7c309, 0xbbec, 0x49d5, 0x82,0x1d, 0x64,0xd9,0x66,0xcb,0x66,0x7f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eaa7c309-bbec-49d5-821d-64d966cb667f")
FolderItems3 : public FolderItems2
{
    virtual HRESULT STDMETHODCALLTYPE Filter(
        LONG grfFlags,
        BSTR bstrFileSpec) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Verbs(
        FolderItemVerbs **ppfic) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(FolderItems3, 0xeaa7c309, 0xbbec, 0x49d5, 0x82,0x1d, 0x64,0xd9,0x66,0xcb,0x66,0x7f)
#endif
#else
typedef struct FolderItems3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        FolderItems3* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        FolderItems3* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        FolderItems3* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        FolderItems3* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        FolderItems3* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        FolderItems3* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        FolderItems3* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** FolderItems methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        FolderItems3* This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        FolderItems3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        FolderItems3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Item)(
        FolderItems3* This,
        VARIANT index,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *_NewEnum)(
        FolderItems3* This,
        IUnknown **ppunk);

    /*** FolderItems2 methods ***/
    HRESULT (STDMETHODCALLTYPE *InvokeVerbEx)(
        FolderItems3* This,
        VARIANT vVerb,
        VARIANT vArgs);

    /*** FolderItems3 methods ***/
    HRESULT (STDMETHODCALLTYPE *Filter)(
        FolderItems3* This,
        LONG grfFlags,
        BSTR bstrFileSpec);

    HRESULT (STDMETHODCALLTYPE *get_Verbs)(
        FolderItems3* This,
        FolderItemVerbs **ppfic);

    END_INTERFACE
} FolderItems3Vtbl;
interface FolderItems3 {
    CONST_VTBL FolderItems3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define FolderItems3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define FolderItems3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define FolderItems3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define FolderItems3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define FolderItems3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define FolderItems3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define FolderItems3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** FolderItems methods ***/
#define FolderItems3_get_Count(This,plCount) (This)->lpVtbl->get_Count(This,plCount)
#define FolderItems3_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define FolderItems3_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define FolderItems3_Item(This,index,ppid) (This)->lpVtbl->Item(This,index,ppid)
#define FolderItems3__NewEnum(This,ppunk) (This)->lpVtbl->_NewEnum(This,ppunk)
/*** FolderItems2 methods ***/
#define FolderItems3_InvokeVerbEx(This,vVerb,vArgs) (This)->lpVtbl->InvokeVerbEx(This,vVerb,vArgs)
/*** FolderItems3 methods ***/
#define FolderItems3_Filter(This,grfFlags,bstrFileSpec) (This)->lpVtbl->Filter(This,grfFlags,bstrFileSpec)
#define FolderItems3_get_Verbs(This,ppfic) (This)->lpVtbl->get_Verbs(This,ppfic)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT FolderItems3_QueryInterface(FolderItems3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG FolderItems3_AddRef(FolderItems3* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG FolderItems3_Release(FolderItems3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT FolderItems3_GetTypeInfoCount(FolderItems3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT FolderItems3_GetTypeInfo(FolderItems3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT FolderItems3_GetIDsOfNames(FolderItems3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT FolderItems3_Invoke(FolderItems3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** FolderItems methods ***/
static FORCEINLINE HRESULT FolderItems3_get_Count(FolderItems3* This,LONG *plCount) {
    return This->lpVtbl->get_Count(This,plCount);
}
static FORCEINLINE HRESULT FolderItems3_get_Application(FolderItems3* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT FolderItems3_get_Parent(FolderItems3* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT FolderItems3_Item(FolderItems3* This,VARIANT index,FolderItem **ppid) {
    return This->lpVtbl->Item(This,index,ppid);
}
static FORCEINLINE HRESULT FolderItems3__NewEnum(FolderItems3* This,IUnknown **ppunk) {
    return This->lpVtbl->_NewEnum(This,ppunk);
}
/*** FolderItems2 methods ***/
static FORCEINLINE HRESULT FolderItems3_InvokeVerbEx(FolderItems3* This,VARIANT vVerb,VARIANT vArgs) {
    return This->lpVtbl->InvokeVerbEx(This,vVerb,vArgs);
}
/*** FolderItems3 methods ***/
static FORCEINLINE HRESULT FolderItems3_Filter(FolderItems3* This,LONG grfFlags,BSTR bstrFileSpec) {
    return This->lpVtbl->Filter(This,grfFlags,bstrFileSpec);
}
static FORCEINLINE HRESULT FolderItems3_get_Verbs(FolderItems3* This,FolderItemVerbs **ppfic) {
    return This->lpVtbl->get_Verbs(This,ppfic);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE FolderItems3_Filter_Proxy(
    FolderItems3* This,
    LONG grfFlags,
    BSTR bstrFileSpec);
void __RPC_STUB FolderItems3_Filter_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItems3_get_Verbs_Proxy(
    FolderItems3* This,
    FolderItemVerbs **ppfic);
void __RPC_STUB FolderItems3_get_Verbs_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __FolderItems3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * FolderItemVerb interface
 */
#ifndef __FolderItemVerb_INTERFACE_DEFINED__
#define __FolderItemVerb_INTERFACE_DEFINED__

DEFINE_GUID(IID_FolderItemVerb, 0x08ec3e00, 0x50b0, 0x11cf, 0x96,0x0c, 0x00,0x80,0xc7,0xf4,0xee,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("08ec3e00-50b0-11cf-960c-0080c7f4ee85")
FolderItemVerb : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoIt(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(FolderItemVerb, 0x08ec3e00, 0x50b0, 0x11cf, 0x96,0x0c, 0x00,0x80,0xc7,0xf4,0xee,0x85)
#endif
#else
typedef struct FolderItemVerbVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        FolderItemVerb* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        FolderItemVerb* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        FolderItemVerb* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        FolderItemVerb* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        FolderItemVerb* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        FolderItemVerb* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        FolderItemVerb* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** FolderItemVerb methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        FolderItemVerb* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        FolderItemVerb* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        FolderItemVerb* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *DoIt)(
        FolderItemVerb* This);

    END_INTERFACE
} FolderItemVerbVtbl;
interface FolderItemVerb {
    CONST_VTBL FolderItemVerbVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define FolderItemVerb_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define FolderItemVerb_AddRef(This) (This)->lpVtbl->AddRef(This)
#define FolderItemVerb_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define FolderItemVerb_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define FolderItemVerb_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define FolderItemVerb_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define FolderItemVerb_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** FolderItemVerb methods ***/
#define FolderItemVerb_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define FolderItemVerb_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define FolderItemVerb_get_Name(This,pbs) (This)->lpVtbl->get_Name(This,pbs)
#define FolderItemVerb_DoIt(This) (This)->lpVtbl->DoIt(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT FolderItemVerb_QueryInterface(FolderItemVerb* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG FolderItemVerb_AddRef(FolderItemVerb* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG FolderItemVerb_Release(FolderItemVerb* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT FolderItemVerb_GetTypeInfoCount(FolderItemVerb* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT FolderItemVerb_GetTypeInfo(FolderItemVerb* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT FolderItemVerb_GetIDsOfNames(FolderItemVerb* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT FolderItemVerb_Invoke(FolderItemVerb* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** FolderItemVerb methods ***/
static FORCEINLINE HRESULT FolderItemVerb_get_Application(FolderItemVerb* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT FolderItemVerb_get_Parent(FolderItemVerb* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT FolderItemVerb_get_Name(FolderItemVerb* This,BSTR *pbs) {
    return This->lpVtbl->get_Name(This,pbs);
}
static FORCEINLINE HRESULT FolderItemVerb_DoIt(FolderItemVerb* This) {
    return This->lpVtbl->DoIt(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE FolderItemVerb_get_Application_Proxy(
    FolderItemVerb* This,
    IDispatch **ppid);
void __RPC_STUB FolderItemVerb_get_Application_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItemVerb_get_Parent_Proxy(
    FolderItemVerb* This,
    IDispatch **ppid);
void __RPC_STUB FolderItemVerb_get_Parent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItemVerb_get_Name_Proxy(
    FolderItemVerb* This,
    BSTR *pbs);
void __RPC_STUB FolderItemVerb_get_Name_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItemVerb_DoIt_Proxy(
    FolderItemVerb* This);
void __RPC_STUB FolderItemVerb_DoIt_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __FolderItemVerb_INTERFACE_DEFINED__ */

/*****************************************************************************
 * FolderItemVerbs interface
 */
#ifndef __FolderItemVerbs_INTERFACE_DEFINED__
#define __FolderItemVerbs_INTERFACE_DEFINED__

DEFINE_GUID(IID_FolderItemVerbs, 0x1f8352c0, 0x50b0, 0x11cf, 0x96,0x0c, 0x00,0x80,0xc7,0xf4,0xee,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1f8352c0-50b0-11cf-960c-0080c7f4ee85")
FolderItemVerbs : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        VARIANT index,
        FolderItemVerb **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE _NewEnum(
        IUnknown **ppunk) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(FolderItemVerbs, 0x1f8352c0, 0x50b0, 0x11cf, 0x96,0x0c, 0x00,0x80,0xc7,0xf4,0xee,0x85)
#endif
#else
typedef struct FolderItemVerbsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        FolderItemVerbs* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        FolderItemVerbs* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        FolderItemVerbs* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        FolderItemVerbs* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        FolderItemVerbs* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        FolderItemVerbs* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        FolderItemVerbs* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** FolderItemVerbs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        FolderItemVerbs* This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        FolderItemVerbs* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        FolderItemVerbs* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Item)(
        FolderItemVerbs* This,
        VARIANT index,
        FolderItemVerb **ppid);

    HRESULT (STDMETHODCALLTYPE *_NewEnum)(
        FolderItemVerbs* This,
        IUnknown **ppunk);

    END_INTERFACE
} FolderItemVerbsVtbl;
interface FolderItemVerbs {
    CONST_VTBL FolderItemVerbsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define FolderItemVerbs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define FolderItemVerbs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define FolderItemVerbs_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define FolderItemVerbs_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define FolderItemVerbs_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define FolderItemVerbs_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define FolderItemVerbs_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** FolderItemVerbs methods ***/
#define FolderItemVerbs_get_Count(This,plCount) (This)->lpVtbl->get_Count(This,plCount)
#define FolderItemVerbs_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define FolderItemVerbs_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define FolderItemVerbs_Item(This,index,ppid) (This)->lpVtbl->Item(This,index,ppid)
#define FolderItemVerbs__NewEnum(This,ppunk) (This)->lpVtbl->_NewEnum(This,ppunk)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT FolderItemVerbs_QueryInterface(FolderItemVerbs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG FolderItemVerbs_AddRef(FolderItemVerbs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG FolderItemVerbs_Release(FolderItemVerbs* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT FolderItemVerbs_GetTypeInfoCount(FolderItemVerbs* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT FolderItemVerbs_GetTypeInfo(FolderItemVerbs* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT FolderItemVerbs_GetIDsOfNames(FolderItemVerbs* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT FolderItemVerbs_Invoke(FolderItemVerbs* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** FolderItemVerbs methods ***/
static FORCEINLINE HRESULT FolderItemVerbs_get_Count(FolderItemVerbs* This,LONG *plCount) {
    return This->lpVtbl->get_Count(This,plCount);
}
static FORCEINLINE HRESULT FolderItemVerbs_get_Application(FolderItemVerbs* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT FolderItemVerbs_get_Parent(FolderItemVerbs* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT FolderItemVerbs_Item(FolderItemVerbs* This,VARIANT index,FolderItemVerb **ppid) {
    return This->lpVtbl->Item(This,index,ppid);
}
static FORCEINLINE HRESULT FolderItemVerbs__NewEnum(FolderItemVerbs* This,IUnknown **ppunk) {
    return This->lpVtbl->_NewEnum(This,ppunk);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE FolderItemVerbs_get_Count_Proxy(
    FolderItemVerbs* This,
    LONG *plCount);
void __RPC_STUB FolderItemVerbs_get_Count_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItemVerbs_get_Application_Proxy(
    FolderItemVerbs* This,
    IDispatch **ppid);
void __RPC_STUB FolderItemVerbs_get_Application_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItemVerbs_get_Parent_Proxy(
    FolderItemVerbs* This,
    IDispatch **ppid);
void __RPC_STUB FolderItemVerbs_get_Parent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItemVerbs_Item_Proxy(
    FolderItemVerbs* This,
    VARIANT index,
    FolderItemVerb **ppid);
void __RPC_STUB FolderItemVerbs_Item_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE FolderItemVerbs__NewEnum_Proxy(
    FolderItemVerbs* This,
    IUnknown **ppunk);
void __RPC_STUB FolderItemVerbs__NewEnum_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __FolderItemVerbs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellLinkDual interface
 */
#ifndef __IShellLinkDual_INTERFACE_DEFINED__
#define __IShellLinkDual_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellLinkDual, 0x88a05c00, 0xf000, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("88a05c00-f000-11ce-8350-************")
IShellLinkDual : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Path(
        BSTR bs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Description(
        BSTR bs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WorkingDirectory(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WorkingDirectory(
        BSTR bs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Arguments(
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Arguments(
        BSTR bs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Hotkey(
        int *piHK) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Hotkey(
        int iHK) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ShowCommand(
        int *piShowCommand) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ShowCommand(
        int iShowCommand) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resolve(
        int fFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIconLocation(
        BSTR *pbs,
        int *piIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIconLocation(
        BSTR bs,
        int iIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        VARIANT vWhere) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellLinkDual, 0x88a05c00, 0xf000, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00)
#endif
#else
typedef struct IShellLinkDualVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellLinkDual* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellLinkDual* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellLinkDual* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellLinkDual* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellLinkDual* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellLinkDual* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellLinkDual* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellLinkDual methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IShellLinkDual* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_Path)(
        IShellLinkDual* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IShellLinkDual* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IShellLinkDual* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_WorkingDirectory)(
        IShellLinkDual* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_WorkingDirectory)(
        IShellLinkDual* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_Arguments)(
        IShellLinkDual* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_Arguments)(
        IShellLinkDual* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_Hotkey)(
        IShellLinkDual* This,
        int *piHK);

    HRESULT (STDMETHODCALLTYPE *put_Hotkey)(
        IShellLinkDual* This,
        int iHK);

    HRESULT (STDMETHODCALLTYPE *get_ShowCommand)(
        IShellLinkDual* This,
        int *piShowCommand);

    HRESULT (STDMETHODCALLTYPE *put_ShowCommand)(
        IShellLinkDual* This,
        int iShowCommand);

    HRESULT (STDMETHODCALLTYPE *Resolve)(
        IShellLinkDual* This,
        int fFlags);

    HRESULT (STDMETHODCALLTYPE *GetIconLocation)(
        IShellLinkDual* This,
        BSTR *pbs,
        int *piIcon);

    HRESULT (STDMETHODCALLTYPE *SetIconLocation)(
        IShellLinkDual* This,
        BSTR bs,
        int iIcon);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IShellLinkDual* This,
        VARIANT vWhere);

    END_INTERFACE
} IShellLinkDualVtbl;
interface IShellLinkDual {
    CONST_VTBL IShellLinkDualVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellLinkDual_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellLinkDual_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellLinkDual_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellLinkDual_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellLinkDual_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellLinkDual_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellLinkDual_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellLinkDual methods ***/
#define IShellLinkDual_get_Path(This,pbs) (This)->lpVtbl->get_Path(This,pbs)
#define IShellLinkDual_put_Path(This,bs) (This)->lpVtbl->put_Path(This,bs)
#define IShellLinkDual_get_Description(This,pbs) (This)->lpVtbl->get_Description(This,pbs)
#define IShellLinkDual_put_Description(This,bs) (This)->lpVtbl->put_Description(This,bs)
#define IShellLinkDual_get_WorkingDirectory(This,pbs) (This)->lpVtbl->get_WorkingDirectory(This,pbs)
#define IShellLinkDual_put_WorkingDirectory(This,bs) (This)->lpVtbl->put_WorkingDirectory(This,bs)
#define IShellLinkDual_get_Arguments(This,pbs) (This)->lpVtbl->get_Arguments(This,pbs)
#define IShellLinkDual_put_Arguments(This,bs) (This)->lpVtbl->put_Arguments(This,bs)
#define IShellLinkDual_get_Hotkey(This,piHK) (This)->lpVtbl->get_Hotkey(This,piHK)
#define IShellLinkDual_put_Hotkey(This,iHK) (This)->lpVtbl->put_Hotkey(This,iHK)
#define IShellLinkDual_get_ShowCommand(This,piShowCommand) (This)->lpVtbl->get_ShowCommand(This,piShowCommand)
#define IShellLinkDual_put_ShowCommand(This,iShowCommand) (This)->lpVtbl->put_ShowCommand(This,iShowCommand)
#define IShellLinkDual_Resolve(This,fFlags) (This)->lpVtbl->Resolve(This,fFlags)
#define IShellLinkDual_GetIconLocation(This,pbs,piIcon) (This)->lpVtbl->GetIconLocation(This,pbs,piIcon)
#define IShellLinkDual_SetIconLocation(This,bs,iIcon) (This)->lpVtbl->SetIconLocation(This,bs,iIcon)
#define IShellLinkDual_Save(This,vWhere) (This)->lpVtbl->Save(This,vWhere)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellLinkDual_QueryInterface(IShellLinkDual* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellLinkDual_AddRef(IShellLinkDual* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellLinkDual_Release(IShellLinkDual* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellLinkDual_GetTypeInfoCount(IShellLinkDual* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellLinkDual_GetTypeInfo(IShellLinkDual* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellLinkDual_GetIDsOfNames(IShellLinkDual* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellLinkDual_Invoke(IShellLinkDual* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellLinkDual methods ***/
static FORCEINLINE HRESULT IShellLinkDual_get_Path(IShellLinkDual* This,BSTR *pbs) {
    return This->lpVtbl->get_Path(This,pbs);
}
static FORCEINLINE HRESULT IShellLinkDual_put_Path(IShellLinkDual* This,BSTR bs) {
    return This->lpVtbl->put_Path(This,bs);
}
static FORCEINLINE HRESULT IShellLinkDual_get_Description(IShellLinkDual* This,BSTR *pbs) {
    return This->lpVtbl->get_Description(This,pbs);
}
static FORCEINLINE HRESULT IShellLinkDual_put_Description(IShellLinkDual* This,BSTR bs) {
    return This->lpVtbl->put_Description(This,bs);
}
static FORCEINLINE HRESULT IShellLinkDual_get_WorkingDirectory(IShellLinkDual* This,BSTR *pbs) {
    return This->lpVtbl->get_WorkingDirectory(This,pbs);
}
static FORCEINLINE HRESULT IShellLinkDual_put_WorkingDirectory(IShellLinkDual* This,BSTR bs) {
    return This->lpVtbl->put_WorkingDirectory(This,bs);
}
static FORCEINLINE HRESULT IShellLinkDual_get_Arguments(IShellLinkDual* This,BSTR *pbs) {
    return This->lpVtbl->get_Arguments(This,pbs);
}
static FORCEINLINE HRESULT IShellLinkDual_put_Arguments(IShellLinkDual* This,BSTR bs) {
    return This->lpVtbl->put_Arguments(This,bs);
}
static FORCEINLINE HRESULT IShellLinkDual_get_Hotkey(IShellLinkDual* This,int *piHK) {
    return This->lpVtbl->get_Hotkey(This,piHK);
}
static FORCEINLINE HRESULT IShellLinkDual_put_Hotkey(IShellLinkDual* This,int iHK) {
    return This->lpVtbl->put_Hotkey(This,iHK);
}
static FORCEINLINE HRESULT IShellLinkDual_get_ShowCommand(IShellLinkDual* This,int *piShowCommand) {
    return This->lpVtbl->get_ShowCommand(This,piShowCommand);
}
static FORCEINLINE HRESULT IShellLinkDual_put_ShowCommand(IShellLinkDual* This,int iShowCommand) {
    return This->lpVtbl->put_ShowCommand(This,iShowCommand);
}
static FORCEINLINE HRESULT IShellLinkDual_Resolve(IShellLinkDual* This,int fFlags) {
    return This->lpVtbl->Resolve(This,fFlags);
}
static FORCEINLINE HRESULT IShellLinkDual_GetIconLocation(IShellLinkDual* This,BSTR *pbs,int *piIcon) {
    return This->lpVtbl->GetIconLocation(This,pbs,piIcon);
}
static FORCEINLINE HRESULT IShellLinkDual_SetIconLocation(IShellLinkDual* This,BSTR bs,int iIcon) {
    return This->lpVtbl->SetIconLocation(This,bs,iIcon);
}
static FORCEINLINE HRESULT IShellLinkDual_Save(IShellLinkDual* This,VARIANT vWhere) {
    return This->lpVtbl->Save(This,vWhere);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellLinkDual_get_Path_Proxy(
    IShellLinkDual* This,
    BSTR *pbs);
void __RPC_STUB IShellLinkDual_get_Path_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_put_Path_Proxy(
    IShellLinkDual* This,
    BSTR bs);
void __RPC_STUB IShellLinkDual_put_Path_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_get_Description_Proxy(
    IShellLinkDual* This,
    BSTR *pbs);
void __RPC_STUB IShellLinkDual_get_Description_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_put_Description_Proxy(
    IShellLinkDual* This,
    BSTR bs);
void __RPC_STUB IShellLinkDual_put_Description_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_get_WorkingDirectory_Proxy(
    IShellLinkDual* This,
    BSTR *pbs);
void __RPC_STUB IShellLinkDual_get_WorkingDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_put_WorkingDirectory_Proxy(
    IShellLinkDual* This,
    BSTR bs);
void __RPC_STUB IShellLinkDual_put_WorkingDirectory_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_get_Arguments_Proxy(
    IShellLinkDual* This,
    BSTR *pbs);
void __RPC_STUB IShellLinkDual_get_Arguments_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_put_Arguments_Proxy(
    IShellLinkDual* This,
    BSTR bs);
void __RPC_STUB IShellLinkDual_put_Arguments_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_get_Hotkey_Proxy(
    IShellLinkDual* This,
    int *piHK);
void __RPC_STUB IShellLinkDual_get_Hotkey_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_put_Hotkey_Proxy(
    IShellLinkDual* This,
    int iHK);
void __RPC_STUB IShellLinkDual_put_Hotkey_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_get_ShowCommand_Proxy(
    IShellLinkDual* This,
    int *piShowCommand);
void __RPC_STUB IShellLinkDual_get_ShowCommand_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_put_ShowCommand_Proxy(
    IShellLinkDual* This,
    int iShowCommand);
void __RPC_STUB IShellLinkDual_put_ShowCommand_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_Resolve_Proxy(
    IShellLinkDual* This,
    int fFlags);
void __RPC_STUB IShellLinkDual_Resolve_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_GetIconLocation_Proxy(
    IShellLinkDual* This,
    BSTR *pbs,
    int *piIcon);
void __RPC_STUB IShellLinkDual_GetIconLocation_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_SetIconLocation_Proxy(
    IShellLinkDual* This,
    BSTR bs,
    int iIcon);
void __RPC_STUB IShellLinkDual_SetIconLocation_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellLinkDual_Save_Proxy(
    IShellLinkDual* This,
    VARIANT vWhere);
void __RPC_STUB IShellLinkDual_Save_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellLinkDual_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellLinkDual2 interface
 */
#ifndef __IShellLinkDual2_INTERFACE_DEFINED__
#define __IShellLinkDual2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellLinkDual2, 0x317ee249, 0xf12e, 0x11d2, 0xb1,0xe4, 0x00,0xc0,0x4f,0x8e,0xeb,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("317ee249-f12e-11d2-b1e4-00c04f8eeb3e")
IShellLinkDual2 : public IShellLinkDual
{
    virtual HRESULT STDMETHODCALLTYPE get_Target(
        FolderItem **ppfi) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellLinkDual2, 0x317ee249, 0xf12e, 0x11d2, 0xb1,0xe4, 0x00,0xc0,0x4f,0x8e,0xeb,0x3e)
#endif
#else
typedef struct IShellLinkDual2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellLinkDual2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellLinkDual2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellLinkDual2* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellLinkDual2* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellLinkDual2* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellLinkDual2* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellLinkDual2* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellLinkDual methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IShellLinkDual2* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_Path)(
        IShellLinkDual2* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IShellLinkDual2* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IShellLinkDual2* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_WorkingDirectory)(
        IShellLinkDual2* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_WorkingDirectory)(
        IShellLinkDual2* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_Arguments)(
        IShellLinkDual2* This,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *put_Arguments)(
        IShellLinkDual2* This,
        BSTR bs);

    HRESULT (STDMETHODCALLTYPE *get_Hotkey)(
        IShellLinkDual2* This,
        int *piHK);

    HRESULT (STDMETHODCALLTYPE *put_Hotkey)(
        IShellLinkDual2* This,
        int iHK);

    HRESULT (STDMETHODCALLTYPE *get_ShowCommand)(
        IShellLinkDual2* This,
        int *piShowCommand);

    HRESULT (STDMETHODCALLTYPE *put_ShowCommand)(
        IShellLinkDual2* This,
        int iShowCommand);

    HRESULT (STDMETHODCALLTYPE *Resolve)(
        IShellLinkDual2* This,
        int fFlags);

    HRESULT (STDMETHODCALLTYPE *GetIconLocation)(
        IShellLinkDual2* This,
        BSTR *pbs,
        int *piIcon);

    HRESULT (STDMETHODCALLTYPE *SetIconLocation)(
        IShellLinkDual2* This,
        BSTR bs,
        int iIcon);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IShellLinkDual2* This,
        VARIANT vWhere);

    /*** IShellLinkDual2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Target)(
        IShellLinkDual2* This,
        FolderItem **ppfi);

    END_INTERFACE
} IShellLinkDual2Vtbl;
interface IShellLinkDual2 {
    CONST_VTBL IShellLinkDual2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellLinkDual2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellLinkDual2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellLinkDual2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellLinkDual2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellLinkDual2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellLinkDual2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellLinkDual2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellLinkDual methods ***/
#define IShellLinkDual2_get_Path(This,pbs) (This)->lpVtbl->get_Path(This,pbs)
#define IShellLinkDual2_put_Path(This,bs) (This)->lpVtbl->put_Path(This,bs)
#define IShellLinkDual2_get_Description(This,pbs) (This)->lpVtbl->get_Description(This,pbs)
#define IShellLinkDual2_put_Description(This,bs) (This)->lpVtbl->put_Description(This,bs)
#define IShellLinkDual2_get_WorkingDirectory(This,pbs) (This)->lpVtbl->get_WorkingDirectory(This,pbs)
#define IShellLinkDual2_put_WorkingDirectory(This,bs) (This)->lpVtbl->put_WorkingDirectory(This,bs)
#define IShellLinkDual2_get_Arguments(This,pbs) (This)->lpVtbl->get_Arguments(This,pbs)
#define IShellLinkDual2_put_Arguments(This,bs) (This)->lpVtbl->put_Arguments(This,bs)
#define IShellLinkDual2_get_Hotkey(This,piHK) (This)->lpVtbl->get_Hotkey(This,piHK)
#define IShellLinkDual2_put_Hotkey(This,iHK) (This)->lpVtbl->put_Hotkey(This,iHK)
#define IShellLinkDual2_get_ShowCommand(This,piShowCommand) (This)->lpVtbl->get_ShowCommand(This,piShowCommand)
#define IShellLinkDual2_put_ShowCommand(This,iShowCommand) (This)->lpVtbl->put_ShowCommand(This,iShowCommand)
#define IShellLinkDual2_Resolve(This,fFlags) (This)->lpVtbl->Resolve(This,fFlags)
#define IShellLinkDual2_GetIconLocation(This,pbs,piIcon) (This)->lpVtbl->GetIconLocation(This,pbs,piIcon)
#define IShellLinkDual2_SetIconLocation(This,bs,iIcon) (This)->lpVtbl->SetIconLocation(This,bs,iIcon)
#define IShellLinkDual2_Save(This,vWhere) (This)->lpVtbl->Save(This,vWhere)
/*** IShellLinkDual2 methods ***/
#define IShellLinkDual2_get_Target(This,ppfi) (This)->lpVtbl->get_Target(This,ppfi)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellLinkDual2_QueryInterface(IShellLinkDual2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellLinkDual2_AddRef(IShellLinkDual2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellLinkDual2_Release(IShellLinkDual2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellLinkDual2_GetTypeInfoCount(IShellLinkDual2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellLinkDual2_GetTypeInfo(IShellLinkDual2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellLinkDual2_GetIDsOfNames(IShellLinkDual2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellLinkDual2_Invoke(IShellLinkDual2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellLinkDual methods ***/
static FORCEINLINE HRESULT IShellLinkDual2_get_Path(IShellLinkDual2* This,BSTR *pbs) {
    return This->lpVtbl->get_Path(This,pbs);
}
static FORCEINLINE HRESULT IShellLinkDual2_put_Path(IShellLinkDual2* This,BSTR bs) {
    return This->lpVtbl->put_Path(This,bs);
}
static FORCEINLINE HRESULT IShellLinkDual2_get_Description(IShellLinkDual2* This,BSTR *pbs) {
    return This->lpVtbl->get_Description(This,pbs);
}
static FORCEINLINE HRESULT IShellLinkDual2_put_Description(IShellLinkDual2* This,BSTR bs) {
    return This->lpVtbl->put_Description(This,bs);
}
static FORCEINLINE HRESULT IShellLinkDual2_get_WorkingDirectory(IShellLinkDual2* This,BSTR *pbs) {
    return This->lpVtbl->get_WorkingDirectory(This,pbs);
}
static FORCEINLINE HRESULT IShellLinkDual2_put_WorkingDirectory(IShellLinkDual2* This,BSTR bs) {
    return This->lpVtbl->put_WorkingDirectory(This,bs);
}
static FORCEINLINE HRESULT IShellLinkDual2_get_Arguments(IShellLinkDual2* This,BSTR *pbs) {
    return This->lpVtbl->get_Arguments(This,pbs);
}
static FORCEINLINE HRESULT IShellLinkDual2_put_Arguments(IShellLinkDual2* This,BSTR bs) {
    return This->lpVtbl->put_Arguments(This,bs);
}
static FORCEINLINE HRESULT IShellLinkDual2_get_Hotkey(IShellLinkDual2* This,int *piHK) {
    return This->lpVtbl->get_Hotkey(This,piHK);
}
static FORCEINLINE HRESULT IShellLinkDual2_put_Hotkey(IShellLinkDual2* This,int iHK) {
    return This->lpVtbl->put_Hotkey(This,iHK);
}
static FORCEINLINE HRESULT IShellLinkDual2_get_ShowCommand(IShellLinkDual2* This,int *piShowCommand) {
    return This->lpVtbl->get_ShowCommand(This,piShowCommand);
}
static FORCEINLINE HRESULT IShellLinkDual2_put_ShowCommand(IShellLinkDual2* This,int iShowCommand) {
    return This->lpVtbl->put_ShowCommand(This,iShowCommand);
}
static FORCEINLINE HRESULT IShellLinkDual2_Resolve(IShellLinkDual2* This,int fFlags) {
    return This->lpVtbl->Resolve(This,fFlags);
}
static FORCEINLINE HRESULT IShellLinkDual2_GetIconLocation(IShellLinkDual2* This,BSTR *pbs,int *piIcon) {
    return This->lpVtbl->GetIconLocation(This,pbs,piIcon);
}
static FORCEINLINE HRESULT IShellLinkDual2_SetIconLocation(IShellLinkDual2* This,BSTR bs,int iIcon) {
    return This->lpVtbl->SetIconLocation(This,bs,iIcon);
}
static FORCEINLINE HRESULT IShellLinkDual2_Save(IShellLinkDual2* This,VARIANT vWhere) {
    return This->lpVtbl->Save(This,vWhere);
}
/*** IShellLinkDual2 methods ***/
static FORCEINLINE HRESULT IShellLinkDual2_get_Target(IShellLinkDual2* This,FolderItem **ppfi) {
    return This->lpVtbl->get_Target(This,ppfi);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellLinkDual2_get_Target_Proxy(
    IShellLinkDual2* This,
    FolderItem **ppfi);
void __RPC_STUB IShellLinkDual2_get_Target_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellLinkDual2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ShellLinkObject coclass
 */

DEFINE_GUID(CLSID_ShellLinkObject, 0x11219420, 0x1768, 0x11d1, 0x95,0xbe, 0x00,0x60,0x97,0x97,0xea,0x4f);

#ifdef __cplusplus
class DECLSPEC_UUID("11219420-1768-11d1-95be-00609797ea4f") ShellLinkObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellLinkObject, 0x11219420, 0x1768, 0x11d1, 0x95,0xbe, 0x00,0x60,0x97,0x97,0xea,0x4f)
#endif
#endif

/*****************************************************************************
 * IShellFolderViewDual interface
 */
#ifndef __IShellFolderViewDual_INTERFACE_DEFINED__
#define __IShellFolderViewDual_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellFolderViewDual, 0xe7a1af80, 0x4d96, 0x11cf, 0x96,0x0c, 0x00,0x80,0xc7,0xf4,0xee,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e7a1af80-4d96-11cf-960c-0080c7f4ee85")
IShellFolderViewDual : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Folder(
        Folder **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectedItems(
        FolderItems **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FocusedItem(
        FolderItem **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectItem(
        VARIANT *pvfi,
        int dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE PopupItemMenu(
        FolderItem *pfi,
        VARIANT vx,
        VARIANT vy,
        BSTR *pbs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Script(
        IDispatch **ppDisp) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ViewOptions(
        LONG *plViewOptions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellFolderViewDual, 0xe7a1af80, 0x4d96, 0x11cf, 0x96,0x0c, 0x00,0x80,0xc7,0xf4,0xee,0x85)
#endif
#else
typedef struct IShellFolderViewDualVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellFolderViewDual* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellFolderViewDual* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellFolderViewDual* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellFolderViewDual* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellFolderViewDual* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellFolderViewDual* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellFolderViewDual* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellFolderViewDual methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellFolderViewDual* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellFolderViewDual* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Folder)(
        IShellFolderViewDual* This,
        Folder **ppid);

    HRESULT (STDMETHODCALLTYPE *SelectedItems)(
        IShellFolderViewDual* This,
        FolderItems **ppid);

    HRESULT (STDMETHODCALLTYPE *get_FocusedItem)(
        IShellFolderViewDual* This,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *SelectItem)(
        IShellFolderViewDual* This,
        VARIANT *pvfi,
        int dwFlags);

    HRESULT (STDMETHODCALLTYPE *PopupItemMenu)(
        IShellFolderViewDual* This,
        FolderItem *pfi,
        VARIANT vx,
        VARIANT vy,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_Script)(
        IShellFolderViewDual* This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_ViewOptions)(
        IShellFolderViewDual* This,
        LONG *plViewOptions);

    END_INTERFACE
} IShellFolderViewDualVtbl;
interface IShellFolderViewDual {
    CONST_VTBL IShellFolderViewDualVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellFolderViewDual_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellFolderViewDual_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellFolderViewDual_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellFolderViewDual_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellFolderViewDual_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellFolderViewDual_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellFolderViewDual_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellFolderViewDual methods ***/
#define IShellFolderViewDual_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellFolderViewDual_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellFolderViewDual_get_Folder(This,ppid) (This)->lpVtbl->get_Folder(This,ppid)
#define IShellFolderViewDual_SelectedItems(This,ppid) (This)->lpVtbl->SelectedItems(This,ppid)
#define IShellFolderViewDual_get_FocusedItem(This,ppid) (This)->lpVtbl->get_FocusedItem(This,ppid)
#define IShellFolderViewDual_SelectItem(This,pvfi,dwFlags) (This)->lpVtbl->SelectItem(This,pvfi,dwFlags)
#define IShellFolderViewDual_PopupItemMenu(This,pfi,vx,vy,pbs) (This)->lpVtbl->PopupItemMenu(This,pfi,vx,vy,pbs)
#define IShellFolderViewDual_get_Script(This,ppDisp) (This)->lpVtbl->get_Script(This,ppDisp)
#define IShellFolderViewDual_get_ViewOptions(This,plViewOptions) (This)->lpVtbl->get_ViewOptions(This,plViewOptions)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual_QueryInterface(IShellFolderViewDual* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellFolderViewDual_AddRef(IShellFolderViewDual* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellFolderViewDual_Release(IShellFolderViewDual* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual_GetTypeInfoCount(IShellFolderViewDual* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellFolderViewDual_GetTypeInfo(IShellFolderViewDual* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellFolderViewDual_GetIDsOfNames(IShellFolderViewDual* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellFolderViewDual_Invoke(IShellFolderViewDual* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellFolderViewDual methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual_get_Application(IShellFolderViewDual* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual_get_Parent(IShellFolderViewDual* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual_get_Folder(IShellFolderViewDual* This,Folder **ppid) {
    return This->lpVtbl->get_Folder(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual_SelectedItems(IShellFolderViewDual* This,FolderItems **ppid) {
    return This->lpVtbl->SelectedItems(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual_get_FocusedItem(IShellFolderViewDual* This,FolderItem **ppid) {
    return This->lpVtbl->get_FocusedItem(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual_SelectItem(IShellFolderViewDual* This,VARIANT *pvfi,int dwFlags) {
    return This->lpVtbl->SelectItem(This,pvfi,dwFlags);
}
static FORCEINLINE HRESULT IShellFolderViewDual_PopupItemMenu(IShellFolderViewDual* This,FolderItem *pfi,VARIANT vx,VARIANT vy,BSTR *pbs) {
    return This->lpVtbl->PopupItemMenu(This,pfi,vx,vy,pbs);
}
static FORCEINLINE HRESULT IShellFolderViewDual_get_Script(IShellFolderViewDual* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Script(This,ppDisp);
}
static FORCEINLINE HRESULT IShellFolderViewDual_get_ViewOptions(IShellFolderViewDual* This,LONG *plViewOptions) {
    return This->lpVtbl->get_ViewOptions(This,plViewOptions);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellFolderViewDual_get_Application_Proxy(
    IShellFolderViewDual* This,
    IDispatch **ppid);
void __RPC_STUB IShellFolderViewDual_get_Application_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual_get_Parent_Proxy(
    IShellFolderViewDual* This,
    IDispatch **ppid);
void __RPC_STUB IShellFolderViewDual_get_Parent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual_get_Folder_Proxy(
    IShellFolderViewDual* This,
    Folder **ppid);
void __RPC_STUB IShellFolderViewDual_get_Folder_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual_SelectedItems_Proxy(
    IShellFolderViewDual* This,
    FolderItems **ppid);
void __RPC_STUB IShellFolderViewDual_SelectedItems_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual_get_FocusedItem_Proxy(
    IShellFolderViewDual* This,
    FolderItem **ppid);
void __RPC_STUB IShellFolderViewDual_get_FocusedItem_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual_SelectItem_Proxy(
    IShellFolderViewDual* This,
    VARIANT *pvfi,
    int dwFlags);
void __RPC_STUB IShellFolderViewDual_SelectItem_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual_PopupItemMenu_Proxy(
    IShellFolderViewDual* This,
    FolderItem *pfi,
    VARIANT vx,
    VARIANT vy,
    BSTR *pbs);
void __RPC_STUB IShellFolderViewDual_PopupItemMenu_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual_get_Script_Proxy(
    IShellFolderViewDual* This,
    IDispatch **ppDisp);
void __RPC_STUB IShellFolderViewDual_get_Script_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual_get_ViewOptions_Proxy(
    IShellFolderViewDual* This,
    LONG *plViewOptions);
void __RPC_STUB IShellFolderViewDual_get_ViewOptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellFolderViewDual_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellFolderViewDual2 interface
 */
#ifndef __IShellFolderViewDual2_INTERFACE_DEFINED__
#define __IShellFolderViewDual2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellFolderViewDual2, 0x31c147b6, 0x0ade, 0x4a3c, 0xb5,0x14, 0xdd,0xf9,0x32,0xef,0x6d,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("31c147b6-0ade-4a3c-b514-ddf932ef6d17")
IShellFolderViewDual2 : public IShellFolderViewDual
{
    virtual HRESULT STDMETHODCALLTYPE get_CurrentViewMode(
        UINT *pViewMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_CurrentViewMode(
        UINT ViewMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectItemRelative(
        int iRelative) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellFolderViewDual2, 0x31c147b6, 0x0ade, 0x4a3c, 0xb5,0x14, 0xdd,0xf9,0x32,0xef,0x6d,0x17)
#endif
#else
typedef struct IShellFolderViewDual2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellFolderViewDual2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellFolderViewDual2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellFolderViewDual2* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellFolderViewDual2* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellFolderViewDual2* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellFolderViewDual2* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellFolderViewDual2* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellFolderViewDual methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellFolderViewDual2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellFolderViewDual2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Folder)(
        IShellFolderViewDual2* This,
        Folder **ppid);

    HRESULT (STDMETHODCALLTYPE *SelectedItems)(
        IShellFolderViewDual2* This,
        FolderItems **ppid);

    HRESULT (STDMETHODCALLTYPE *get_FocusedItem)(
        IShellFolderViewDual2* This,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *SelectItem)(
        IShellFolderViewDual2* This,
        VARIANT *pvfi,
        int dwFlags);

    HRESULT (STDMETHODCALLTYPE *PopupItemMenu)(
        IShellFolderViewDual2* This,
        FolderItem *pfi,
        VARIANT vx,
        VARIANT vy,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_Script)(
        IShellFolderViewDual2* This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_ViewOptions)(
        IShellFolderViewDual2* This,
        LONG *plViewOptions);

    /*** IShellFolderViewDual2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentViewMode)(
        IShellFolderViewDual2* This,
        UINT *pViewMode);

    HRESULT (STDMETHODCALLTYPE *put_CurrentViewMode)(
        IShellFolderViewDual2* This,
        UINT ViewMode);

    HRESULT (STDMETHODCALLTYPE *SelectItemRelative)(
        IShellFolderViewDual2* This,
        int iRelative);

    END_INTERFACE
} IShellFolderViewDual2Vtbl;
interface IShellFolderViewDual2 {
    CONST_VTBL IShellFolderViewDual2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellFolderViewDual2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellFolderViewDual2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellFolderViewDual2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellFolderViewDual2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellFolderViewDual2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellFolderViewDual2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellFolderViewDual2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellFolderViewDual methods ***/
#define IShellFolderViewDual2_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellFolderViewDual2_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellFolderViewDual2_get_Folder(This,ppid) (This)->lpVtbl->get_Folder(This,ppid)
#define IShellFolderViewDual2_SelectedItems(This,ppid) (This)->lpVtbl->SelectedItems(This,ppid)
#define IShellFolderViewDual2_get_FocusedItem(This,ppid) (This)->lpVtbl->get_FocusedItem(This,ppid)
#define IShellFolderViewDual2_SelectItem(This,pvfi,dwFlags) (This)->lpVtbl->SelectItem(This,pvfi,dwFlags)
#define IShellFolderViewDual2_PopupItemMenu(This,pfi,vx,vy,pbs) (This)->lpVtbl->PopupItemMenu(This,pfi,vx,vy,pbs)
#define IShellFolderViewDual2_get_Script(This,ppDisp) (This)->lpVtbl->get_Script(This,ppDisp)
#define IShellFolderViewDual2_get_ViewOptions(This,plViewOptions) (This)->lpVtbl->get_ViewOptions(This,plViewOptions)
/*** IShellFolderViewDual2 methods ***/
#define IShellFolderViewDual2_get_CurrentViewMode(This,pViewMode) (This)->lpVtbl->get_CurrentViewMode(This,pViewMode)
#define IShellFolderViewDual2_put_CurrentViewMode(This,ViewMode) (This)->lpVtbl->put_CurrentViewMode(This,ViewMode)
#define IShellFolderViewDual2_SelectItemRelative(This,iRelative) (This)->lpVtbl->SelectItemRelative(This,iRelative)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual2_QueryInterface(IShellFolderViewDual2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellFolderViewDual2_AddRef(IShellFolderViewDual2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellFolderViewDual2_Release(IShellFolderViewDual2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual2_GetTypeInfoCount(IShellFolderViewDual2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_GetTypeInfo(IShellFolderViewDual2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_GetIDsOfNames(IShellFolderViewDual2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_Invoke(IShellFolderViewDual2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellFolderViewDual methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual2_get_Application(IShellFolderViewDual2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_get_Parent(IShellFolderViewDual2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_get_Folder(IShellFolderViewDual2* This,Folder **ppid) {
    return This->lpVtbl->get_Folder(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_SelectedItems(IShellFolderViewDual2* This,FolderItems **ppid) {
    return This->lpVtbl->SelectedItems(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_get_FocusedItem(IShellFolderViewDual2* This,FolderItem **ppid) {
    return This->lpVtbl->get_FocusedItem(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_SelectItem(IShellFolderViewDual2* This,VARIANT *pvfi,int dwFlags) {
    return This->lpVtbl->SelectItem(This,pvfi,dwFlags);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_PopupItemMenu(IShellFolderViewDual2* This,FolderItem *pfi,VARIANT vx,VARIANT vy,BSTR *pbs) {
    return This->lpVtbl->PopupItemMenu(This,pfi,vx,vy,pbs);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_get_Script(IShellFolderViewDual2* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Script(This,ppDisp);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_get_ViewOptions(IShellFolderViewDual2* This,LONG *plViewOptions) {
    return This->lpVtbl->get_ViewOptions(This,plViewOptions);
}
/*** IShellFolderViewDual2 methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual2_get_CurrentViewMode(IShellFolderViewDual2* This,UINT *pViewMode) {
    return This->lpVtbl->get_CurrentViewMode(This,pViewMode);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_put_CurrentViewMode(IShellFolderViewDual2* This,UINT ViewMode) {
    return This->lpVtbl->put_CurrentViewMode(This,ViewMode);
}
static FORCEINLINE HRESULT IShellFolderViewDual2_SelectItemRelative(IShellFolderViewDual2* This,int iRelative) {
    return This->lpVtbl->SelectItemRelative(This,iRelative);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellFolderViewDual2_get_CurrentViewMode_Proxy(
    IShellFolderViewDual2* This,
    UINT *pViewMode);
void __RPC_STUB IShellFolderViewDual2_get_CurrentViewMode_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual2_put_CurrentViewMode_Proxy(
    IShellFolderViewDual2* This,
    UINT ViewMode);
void __RPC_STUB IShellFolderViewDual2_put_CurrentViewMode_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual2_SelectItemRelative_Proxy(
    IShellFolderViewDual2* This,
    int iRelative);
void __RPC_STUB IShellFolderViewDual2_SelectItemRelative_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellFolderViewDual2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellFolderViewDual3 interface
 */
#ifndef __IShellFolderViewDual3_INTERFACE_DEFINED__
#define __IShellFolderViewDual3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellFolderViewDual3, 0x29ec8e6c, 0x46d3, 0x411f, 0xba,0xaa, 0x61,0x1a,0x6c,0x9c,0xac,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("29ec8e6c-46d3-411f-baaa-611a6c9cac66")
IShellFolderViewDual3 : public IShellFolderViewDual2
{
    virtual HRESULT STDMETHODCALLTYPE get_GroupBy(
        BSTR *pbstrGroupBy) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_GroupBy(
        BSTR bstrGroupBy) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FolderFlags(
        DWORD *pdwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FolderFlags(
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SortColumns(
        BSTR *pbstrSortColumns) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SortColumns(
        BSTR bstrSortColumns) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IconSize(
        int iIconSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IconSize(
        int *piIconSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE FilterView(
        BSTR bstrFilterText) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellFolderViewDual3, 0x29ec8e6c, 0x46d3, 0x411f, 0xba,0xaa, 0x61,0x1a,0x6c,0x9c,0xac,0x66)
#endif
#else
typedef struct IShellFolderViewDual3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellFolderViewDual3* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellFolderViewDual3* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellFolderViewDual3* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellFolderViewDual3* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellFolderViewDual3* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellFolderViewDual3* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellFolderViewDual3* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellFolderViewDual methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellFolderViewDual3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellFolderViewDual3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Folder)(
        IShellFolderViewDual3* This,
        Folder **ppid);

    HRESULT (STDMETHODCALLTYPE *SelectedItems)(
        IShellFolderViewDual3* This,
        FolderItems **ppid);

    HRESULT (STDMETHODCALLTYPE *get_FocusedItem)(
        IShellFolderViewDual3* This,
        FolderItem **ppid);

    HRESULT (STDMETHODCALLTYPE *SelectItem)(
        IShellFolderViewDual3* This,
        VARIANT *pvfi,
        int dwFlags);

    HRESULT (STDMETHODCALLTYPE *PopupItemMenu)(
        IShellFolderViewDual3* This,
        FolderItem *pfi,
        VARIANT vx,
        VARIANT vy,
        BSTR *pbs);

    HRESULT (STDMETHODCALLTYPE *get_Script)(
        IShellFolderViewDual3* This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_ViewOptions)(
        IShellFolderViewDual3* This,
        LONG *plViewOptions);

    /*** IShellFolderViewDual2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentViewMode)(
        IShellFolderViewDual3* This,
        UINT *pViewMode);

    HRESULT (STDMETHODCALLTYPE *put_CurrentViewMode)(
        IShellFolderViewDual3* This,
        UINT ViewMode);

    HRESULT (STDMETHODCALLTYPE *SelectItemRelative)(
        IShellFolderViewDual3* This,
        int iRelative);

    /*** IShellFolderViewDual3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_GroupBy)(
        IShellFolderViewDual3* This,
        BSTR *pbstrGroupBy);

    HRESULT (STDMETHODCALLTYPE *put_GroupBy)(
        IShellFolderViewDual3* This,
        BSTR bstrGroupBy);

    HRESULT (STDMETHODCALLTYPE *get_FolderFlags)(
        IShellFolderViewDual3* This,
        DWORD *pdwFlags);

    HRESULT (STDMETHODCALLTYPE *put_FolderFlags)(
        IShellFolderViewDual3* This,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *get_SortColumns)(
        IShellFolderViewDual3* This,
        BSTR *pbstrSortColumns);

    HRESULT (STDMETHODCALLTYPE *put_SortColumns)(
        IShellFolderViewDual3* This,
        BSTR bstrSortColumns);

    HRESULT (STDMETHODCALLTYPE *put_IconSize)(
        IShellFolderViewDual3* This,
        int iIconSize);

    HRESULT (STDMETHODCALLTYPE *get_IconSize)(
        IShellFolderViewDual3* This,
        int *piIconSize);

    HRESULT (STDMETHODCALLTYPE *FilterView)(
        IShellFolderViewDual3* This,
        BSTR bstrFilterText);

    END_INTERFACE
} IShellFolderViewDual3Vtbl;
interface IShellFolderViewDual3 {
    CONST_VTBL IShellFolderViewDual3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellFolderViewDual3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellFolderViewDual3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellFolderViewDual3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellFolderViewDual3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellFolderViewDual3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellFolderViewDual3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellFolderViewDual3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellFolderViewDual methods ***/
#define IShellFolderViewDual3_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellFolderViewDual3_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellFolderViewDual3_get_Folder(This,ppid) (This)->lpVtbl->get_Folder(This,ppid)
#define IShellFolderViewDual3_SelectedItems(This,ppid) (This)->lpVtbl->SelectedItems(This,ppid)
#define IShellFolderViewDual3_get_FocusedItem(This,ppid) (This)->lpVtbl->get_FocusedItem(This,ppid)
#define IShellFolderViewDual3_SelectItem(This,pvfi,dwFlags) (This)->lpVtbl->SelectItem(This,pvfi,dwFlags)
#define IShellFolderViewDual3_PopupItemMenu(This,pfi,vx,vy,pbs) (This)->lpVtbl->PopupItemMenu(This,pfi,vx,vy,pbs)
#define IShellFolderViewDual3_get_Script(This,ppDisp) (This)->lpVtbl->get_Script(This,ppDisp)
#define IShellFolderViewDual3_get_ViewOptions(This,plViewOptions) (This)->lpVtbl->get_ViewOptions(This,plViewOptions)
/*** IShellFolderViewDual2 methods ***/
#define IShellFolderViewDual3_get_CurrentViewMode(This,pViewMode) (This)->lpVtbl->get_CurrentViewMode(This,pViewMode)
#define IShellFolderViewDual3_put_CurrentViewMode(This,ViewMode) (This)->lpVtbl->put_CurrentViewMode(This,ViewMode)
#define IShellFolderViewDual3_SelectItemRelative(This,iRelative) (This)->lpVtbl->SelectItemRelative(This,iRelative)
/*** IShellFolderViewDual3 methods ***/
#define IShellFolderViewDual3_get_GroupBy(This,pbstrGroupBy) (This)->lpVtbl->get_GroupBy(This,pbstrGroupBy)
#define IShellFolderViewDual3_put_GroupBy(This,bstrGroupBy) (This)->lpVtbl->put_GroupBy(This,bstrGroupBy)
#define IShellFolderViewDual3_get_FolderFlags(This,pdwFlags) (This)->lpVtbl->get_FolderFlags(This,pdwFlags)
#define IShellFolderViewDual3_put_FolderFlags(This,dwFlags) (This)->lpVtbl->put_FolderFlags(This,dwFlags)
#define IShellFolderViewDual3_get_SortColumns(This,pbstrSortColumns) (This)->lpVtbl->get_SortColumns(This,pbstrSortColumns)
#define IShellFolderViewDual3_put_SortColumns(This,bstrSortColumns) (This)->lpVtbl->put_SortColumns(This,bstrSortColumns)
#define IShellFolderViewDual3_put_IconSize(This,iIconSize) (This)->lpVtbl->put_IconSize(This,iIconSize)
#define IShellFolderViewDual3_get_IconSize(This,piIconSize) (This)->lpVtbl->get_IconSize(This,piIconSize)
#define IShellFolderViewDual3_FilterView(This,bstrFilterText) (This)->lpVtbl->FilterView(This,bstrFilterText)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual3_QueryInterface(IShellFolderViewDual3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellFolderViewDual3_AddRef(IShellFolderViewDual3* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellFolderViewDual3_Release(IShellFolderViewDual3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual3_GetTypeInfoCount(IShellFolderViewDual3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_GetTypeInfo(IShellFolderViewDual3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_GetIDsOfNames(IShellFolderViewDual3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_Invoke(IShellFolderViewDual3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellFolderViewDual methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual3_get_Application(IShellFolderViewDual3* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_get_Parent(IShellFolderViewDual3* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_get_Folder(IShellFolderViewDual3* This,Folder **ppid) {
    return This->lpVtbl->get_Folder(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_SelectedItems(IShellFolderViewDual3* This,FolderItems **ppid) {
    return This->lpVtbl->SelectedItems(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_get_FocusedItem(IShellFolderViewDual3* This,FolderItem **ppid) {
    return This->lpVtbl->get_FocusedItem(This,ppid);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_SelectItem(IShellFolderViewDual3* This,VARIANT *pvfi,int dwFlags) {
    return This->lpVtbl->SelectItem(This,pvfi,dwFlags);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_PopupItemMenu(IShellFolderViewDual3* This,FolderItem *pfi,VARIANT vx,VARIANT vy,BSTR *pbs) {
    return This->lpVtbl->PopupItemMenu(This,pfi,vx,vy,pbs);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_get_Script(IShellFolderViewDual3* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Script(This,ppDisp);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_get_ViewOptions(IShellFolderViewDual3* This,LONG *plViewOptions) {
    return This->lpVtbl->get_ViewOptions(This,plViewOptions);
}
/*** IShellFolderViewDual2 methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual3_get_CurrentViewMode(IShellFolderViewDual3* This,UINT *pViewMode) {
    return This->lpVtbl->get_CurrentViewMode(This,pViewMode);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_put_CurrentViewMode(IShellFolderViewDual3* This,UINT ViewMode) {
    return This->lpVtbl->put_CurrentViewMode(This,ViewMode);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_SelectItemRelative(IShellFolderViewDual3* This,int iRelative) {
    return This->lpVtbl->SelectItemRelative(This,iRelative);
}
/*** IShellFolderViewDual3 methods ***/
static FORCEINLINE HRESULT IShellFolderViewDual3_get_GroupBy(IShellFolderViewDual3* This,BSTR *pbstrGroupBy) {
    return This->lpVtbl->get_GroupBy(This,pbstrGroupBy);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_put_GroupBy(IShellFolderViewDual3* This,BSTR bstrGroupBy) {
    return This->lpVtbl->put_GroupBy(This,bstrGroupBy);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_get_FolderFlags(IShellFolderViewDual3* This,DWORD *pdwFlags) {
    return This->lpVtbl->get_FolderFlags(This,pdwFlags);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_put_FolderFlags(IShellFolderViewDual3* This,DWORD dwFlags) {
    return This->lpVtbl->put_FolderFlags(This,dwFlags);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_get_SortColumns(IShellFolderViewDual3* This,BSTR *pbstrSortColumns) {
    return This->lpVtbl->get_SortColumns(This,pbstrSortColumns);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_put_SortColumns(IShellFolderViewDual3* This,BSTR bstrSortColumns) {
    return This->lpVtbl->put_SortColumns(This,bstrSortColumns);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_put_IconSize(IShellFolderViewDual3* This,int iIconSize) {
    return This->lpVtbl->put_IconSize(This,iIconSize);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_get_IconSize(IShellFolderViewDual3* This,int *piIconSize) {
    return This->lpVtbl->get_IconSize(This,piIconSize);
}
static FORCEINLINE HRESULT IShellFolderViewDual3_FilterView(IShellFolderViewDual3* This,BSTR bstrFilterText) {
    return This->lpVtbl->FilterView(This,bstrFilterText);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_get_GroupBy_Proxy(
    IShellFolderViewDual3* This,
    BSTR *pbstrGroupBy);
void __RPC_STUB IShellFolderViewDual3_get_GroupBy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_put_GroupBy_Proxy(
    IShellFolderViewDual3* This,
    BSTR bstrGroupBy);
void __RPC_STUB IShellFolderViewDual3_put_GroupBy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_get_FolderFlags_Proxy(
    IShellFolderViewDual3* This,
    DWORD *pdwFlags);
void __RPC_STUB IShellFolderViewDual3_get_FolderFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_put_FolderFlags_Proxy(
    IShellFolderViewDual3* This,
    DWORD dwFlags);
void __RPC_STUB IShellFolderViewDual3_put_FolderFlags_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_get_SortColumns_Proxy(
    IShellFolderViewDual3* This,
    BSTR *pbstrSortColumns);
void __RPC_STUB IShellFolderViewDual3_get_SortColumns_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_put_SortColumns_Proxy(
    IShellFolderViewDual3* This,
    BSTR bstrSortColumns);
void __RPC_STUB IShellFolderViewDual3_put_SortColumns_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_put_IconSize_Proxy(
    IShellFolderViewDual3* This,
    int iIconSize);
void __RPC_STUB IShellFolderViewDual3_put_IconSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_get_IconSize_Proxy(
    IShellFolderViewDual3* This,
    int *piIconSize);
void __RPC_STUB IShellFolderViewDual3_get_IconSize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellFolderViewDual3_FilterView_Proxy(
    IShellFolderViewDual3* This,
    BSTR bstrFilterText);
void __RPC_STUB IShellFolderViewDual3_FilterView_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellFolderViewDual3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ShellFolderView coclass
 */

DEFINE_GUID(CLSID_ShellFolderView, 0x62112aa1, 0xebe4, 0x11cf, 0xa5,0xfb, 0x00,0x20,0xaf,0xe7,0x29,0x2d);

#ifdef __cplusplus
class DECLSPEC_UUID("62112aa1-ebe4-11cf-a5fb-0020afe7292d") ShellFolderView;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellFolderView, 0x62112aa1, 0xebe4, 0x11cf, 0xa5,0xfb, 0x00,0x20,0xaf,0xe7,0x29,0x2d)
#endif
#endif

typedef enum ShellFolderViewOptions {
    SFVVO_SHOWALLOBJECTS = 0x1,
    SFVVO_SHOWEXTENSIONS = 0x2,
    SFVVO_SHOWCOMPCOLOR = 0x8,
    SFVVO_SHOWSYSFILES = 0x20,
    SFVVO_WIN95CLASSIC = 0x40,
    SFVVO_DOUBLECLICKINWEBVIEW = 0x80,
    SFVVO_DESKTOPHTML = 0x200
} ShellFolderViewOptions;
/*****************************************************************************
 * IShellDispatch interface
 */
#ifndef __IShellDispatch_INTERFACE_DEFINED__
#define __IShellDispatch_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellDispatch, 0xd8f015c0, 0xc278, 0x11ce, 0xa4,0x9e, 0x44,0x45,0x53,0x54,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d8f015c0-c278-11ce-a49e-************")
IShellDispatch : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE NameSpace(
        VARIANT vDir,
        Folder **ppsdf) = 0;

    virtual HRESULT STDMETHODCALLTYPE BrowseForFolder(
        LONG Hwnd,
        BSTR Title,
        LONG Options,
        VARIANT RootFolder,
        Folder **ppsdf) = 0;

    virtual HRESULT STDMETHODCALLTYPE Windows(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Open(
        VARIANT vDir) = 0;

    virtual HRESULT STDMETHODCALLTYPE Explore(
        VARIANT vDir) = 0;

    virtual HRESULT STDMETHODCALLTYPE MinimizeAll(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE UndoMinimizeALL(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FileRun(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CascadeWindows(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE TileVertically(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE TileHorizontally(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShutdownWindows(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Suspend(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EjectPC(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTime(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE TrayProperties(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Help(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindFiles(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindComputer(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RefreshMenu(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ControlPanelItem(
        BSTR bstrDir) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellDispatch, 0xd8f015c0, 0xc278, 0x11ce, 0xa4,0x9e, 0x44,0x45,0x53,0x54,0x00,0x00)
#endif
#else
typedef struct IShellDispatchVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellDispatch* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellDispatch* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellDispatch* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellDispatch* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellDispatch* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellDispatch* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellDispatch* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellDispatch* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellDispatch* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *NameSpace)(
        IShellDispatch* This,
        VARIANT vDir,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *BrowseForFolder)(
        IShellDispatch* This,
        LONG Hwnd,
        BSTR Title,
        LONG Options,
        VARIANT RootFolder,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *Windows)(
        IShellDispatch* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Open)(
        IShellDispatch* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *Explore)(
        IShellDispatch* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *MinimizeAll)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *UndoMinimizeALL)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *FileRun)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *CascadeWindows)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *TileVertically)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *TileHorizontally)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *ShutdownWindows)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *EjectPC)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *SetTime)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *TrayProperties)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *Help)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *FindFiles)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *RefreshMenu)(
        IShellDispatch* This);

    HRESULT (STDMETHODCALLTYPE *ControlPanelItem)(
        IShellDispatch* This,
        BSTR bstrDir);

    END_INTERFACE
} IShellDispatchVtbl;
interface IShellDispatch {
    CONST_VTBL IShellDispatchVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellDispatch_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellDispatch_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellDispatch_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellDispatch_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellDispatch_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellDispatch_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellDispatch_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellDispatch methods ***/
#define IShellDispatch_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellDispatch_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellDispatch_NameSpace(This,vDir,ppsdf) (This)->lpVtbl->NameSpace(This,vDir,ppsdf)
#define IShellDispatch_BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf) (This)->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf)
#define IShellDispatch_Windows(This,ppid) (This)->lpVtbl->Windows(This,ppid)
#define IShellDispatch_Open(This,vDir) (This)->lpVtbl->Open(This,vDir)
#define IShellDispatch_Explore(This,vDir) (This)->lpVtbl->Explore(This,vDir)
#define IShellDispatch_MinimizeAll(This) (This)->lpVtbl->MinimizeAll(This)
#define IShellDispatch_UndoMinimizeALL(This) (This)->lpVtbl->UndoMinimizeALL(This)
#define IShellDispatch_FileRun(This) (This)->lpVtbl->FileRun(This)
#define IShellDispatch_CascadeWindows(This) (This)->lpVtbl->CascadeWindows(This)
#define IShellDispatch_TileVertically(This) (This)->lpVtbl->TileVertically(This)
#define IShellDispatch_TileHorizontally(This) (This)->lpVtbl->TileHorizontally(This)
#define IShellDispatch_ShutdownWindows(This) (This)->lpVtbl->ShutdownWindows(This)
#define IShellDispatch_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IShellDispatch_EjectPC(This) (This)->lpVtbl->EjectPC(This)
#define IShellDispatch_SetTime(This) (This)->lpVtbl->SetTime(This)
#define IShellDispatch_TrayProperties(This) (This)->lpVtbl->TrayProperties(This)
#define IShellDispatch_Help(This) (This)->lpVtbl->Help(This)
#define IShellDispatch_FindFiles(This) (This)->lpVtbl->FindFiles(This)
#define IShellDispatch_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define IShellDispatch_RefreshMenu(This) (This)->lpVtbl->RefreshMenu(This)
#define IShellDispatch_ControlPanelItem(This,bstrDir) (This)->lpVtbl->ControlPanelItem(This,bstrDir)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellDispatch_QueryInterface(IShellDispatch* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellDispatch_AddRef(IShellDispatch* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellDispatch_Release(IShellDispatch* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch_GetTypeInfoCount(IShellDispatch* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellDispatch_GetTypeInfo(IShellDispatch* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellDispatch_GetIDsOfNames(IShellDispatch* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellDispatch_Invoke(IShellDispatch* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch_get_Application(IShellDispatch* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch_get_Parent(IShellDispatch* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch_NameSpace(IShellDispatch* This,VARIANT vDir,Folder **ppsdf) {
    return This->lpVtbl->NameSpace(This,vDir,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch_BrowseForFolder(IShellDispatch* This,LONG Hwnd,BSTR Title,LONG Options,VARIANT RootFolder,Folder **ppsdf) {
    return This->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch_Windows(IShellDispatch* This,IDispatch **ppid) {
    return This->lpVtbl->Windows(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch_Open(IShellDispatch* This,VARIANT vDir) {
    return This->lpVtbl->Open(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch_Explore(IShellDispatch* This,VARIANT vDir) {
    return This->lpVtbl->Explore(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch_MinimizeAll(IShellDispatch* This) {
    return This->lpVtbl->MinimizeAll(This);
}
static FORCEINLINE HRESULT IShellDispatch_UndoMinimizeALL(IShellDispatch* This) {
    return This->lpVtbl->UndoMinimizeALL(This);
}
static FORCEINLINE HRESULT IShellDispatch_FileRun(IShellDispatch* This) {
    return This->lpVtbl->FileRun(This);
}
static FORCEINLINE HRESULT IShellDispatch_CascadeWindows(IShellDispatch* This) {
    return This->lpVtbl->CascadeWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch_TileVertically(IShellDispatch* This) {
    return This->lpVtbl->TileVertically(This);
}
static FORCEINLINE HRESULT IShellDispatch_TileHorizontally(IShellDispatch* This) {
    return This->lpVtbl->TileHorizontally(This);
}
static FORCEINLINE HRESULT IShellDispatch_ShutdownWindows(IShellDispatch* This) {
    return This->lpVtbl->ShutdownWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch_Suspend(IShellDispatch* This) {
    return This->lpVtbl->Suspend(This);
}
static FORCEINLINE HRESULT IShellDispatch_EjectPC(IShellDispatch* This) {
    return This->lpVtbl->EjectPC(This);
}
static FORCEINLINE HRESULT IShellDispatch_SetTime(IShellDispatch* This) {
    return This->lpVtbl->SetTime(This);
}
static FORCEINLINE HRESULT IShellDispatch_TrayProperties(IShellDispatch* This) {
    return This->lpVtbl->TrayProperties(This);
}
static FORCEINLINE HRESULT IShellDispatch_Help(IShellDispatch* This) {
    return This->lpVtbl->Help(This);
}
static FORCEINLINE HRESULT IShellDispatch_FindFiles(IShellDispatch* This) {
    return This->lpVtbl->FindFiles(This);
}
static FORCEINLINE HRESULT IShellDispatch_FindComputer(IShellDispatch* This) {
    return This->lpVtbl->FindComputer(This);
}
static FORCEINLINE HRESULT IShellDispatch_RefreshMenu(IShellDispatch* This) {
    return This->lpVtbl->RefreshMenu(This);
}
static FORCEINLINE HRESULT IShellDispatch_ControlPanelItem(IShellDispatch* This,BSTR bstrDir) {
    return This->lpVtbl->ControlPanelItem(This,bstrDir);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellDispatch_get_Application_Proxy(
    IShellDispatch* This,
    IDispatch **ppid);
void __RPC_STUB IShellDispatch_get_Application_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_get_Parent_Proxy(
    IShellDispatch* This,
    IDispatch **ppid);
void __RPC_STUB IShellDispatch_get_Parent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_NameSpace_Proxy(
    IShellDispatch* This,
    VARIANT vDir,
    Folder **ppsdf);
void __RPC_STUB IShellDispatch_NameSpace_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_BrowseForFolder_Proxy(
    IShellDispatch* This,
    LONG Hwnd,
    BSTR Title,
    LONG Options,
    VARIANT RootFolder,
    Folder **ppsdf);
void __RPC_STUB IShellDispatch_BrowseForFolder_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_Windows_Proxy(
    IShellDispatch* This,
    IDispatch **ppid);
void __RPC_STUB IShellDispatch_Windows_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_Open_Proxy(
    IShellDispatch* This,
    VARIANT vDir);
void __RPC_STUB IShellDispatch_Open_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_Explore_Proxy(
    IShellDispatch* This,
    VARIANT vDir);
void __RPC_STUB IShellDispatch_Explore_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_MinimizeAll_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_MinimizeAll_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_UndoMinimizeALL_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_UndoMinimizeALL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_FileRun_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_FileRun_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_CascadeWindows_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_CascadeWindows_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_TileVertically_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_TileVertically_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_TileHorizontally_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_TileHorizontally_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_ShutdownWindows_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_ShutdownWindows_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_Suspend_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_Suspend_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_EjectPC_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_EjectPC_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_SetTime_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_SetTime_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_TrayProperties_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_TrayProperties_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_Help_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_Help_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_FindFiles_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_FindFiles_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_FindComputer_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_FindComputer_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_RefreshMenu_Proxy(
    IShellDispatch* This);
void __RPC_STUB IShellDispatch_RefreshMenu_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch_ControlPanelItem_Proxy(
    IShellDispatch* This,
    BSTR bstrDir);
void __RPC_STUB IShellDispatch_ControlPanelItem_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellDispatch_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellDispatch2 interface
 */
#ifndef __IShellDispatch2_INTERFACE_DEFINED__
#define __IShellDispatch2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellDispatch2, 0xa4c6892c, 0x3ba9, 0x11d2, 0x9d,0xea, 0x00,0xc0,0x4f,0xb1,0x61,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a4c6892c-3ba9-11d2-9dea-00c04fb16162")
IShellDispatch2 : public IShellDispatch
{
    virtual HRESULT STDMETHODCALLTYPE IsRestricted(
        BSTR Group,
        BSTR Restriction,
        LONG *plRestrictValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShellExecute(
        BSTR File,
        VARIANT vArgs,
        VARIANT vDir,
        VARIANT vOperation,
        VARIANT vShow) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindPrinter(
        BSTR name,
        BSTR location,
        BSTR model) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSystemInformation(
        BSTR name,
        VARIANT *pv) = 0;

    virtual HRESULT STDMETHODCALLTYPE ServiceStart(
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess) = 0;

    virtual HRESULT STDMETHODCALLTYPE ServiceStop(
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsServiceRunning(
        BSTR ServiceName,
        VARIANT *pRunning) = 0;

    virtual HRESULT STDMETHODCALLTYPE CanStartStopService(
        BSTR ServiceName,
        VARIANT *pCanStartStop) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowBrowserBar(
        BSTR bstrClsid,
        VARIANT bShow,
        VARIANT *pSuccess) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellDispatch2, 0xa4c6892c, 0x3ba9, 0x11d2, 0x9d,0xea, 0x00,0xc0,0x4f,0xb1,0x61,0x62)
#endif
#else
typedef struct IShellDispatch2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellDispatch2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellDispatch2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellDispatch2* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellDispatch2* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellDispatch2* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellDispatch2* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellDispatch2* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellDispatch2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellDispatch2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *NameSpace)(
        IShellDispatch2* This,
        VARIANT vDir,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *BrowseForFolder)(
        IShellDispatch2* This,
        LONG Hwnd,
        BSTR Title,
        LONG Options,
        VARIANT RootFolder,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *Windows)(
        IShellDispatch2* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Open)(
        IShellDispatch2* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *Explore)(
        IShellDispatch2* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *MinimizeAll)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *UndoMinimizeALL)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *FileRun)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *CascadeWindows)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *TileVertically)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *TileHorizontally)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *ShutdownWindows)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *EjectPC)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *SetTime)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *TrayProperties)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *Help)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *FindFiles)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *RefreshMenu)(
        IShellDispatch2* This);

    HRESULT (STDMETHODCALLTYPE *ControlPanelItem)(
        IShellDispatch2* This,
        BSTR bstrDir);

    /*** IShellDispatch2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsRestricted)(
        IShellDispatch2* This,
        BSTR Group,
        BSTR Restriction,
        LONG *plRestrictValue);

    HRESULT (STDMETHODCALLTYPE *ShellExecute)(
        IShellDispatch2* This,
        BSTR File,
        VARIANT vArgs,
        VARIANT vDir,
        VARIANT vOperation,
        VARIANT vShow);

    HRESULT (STDMETHODCALLTYPE *FindPrinter)(
        IShellDispatch2* This,
        BSTR name,
        BSTR location,
        BSTR model);

    HRESULT (STDMETHODCALLTYPE *GetSystemInformation)(
        IShellDispatch2* This,
        BSTR name,
        VARIANT *pv);

    HRESULT (STDMETHODCALLTYPE *ServiceStart)(
        IShellDispatch2* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *ServiceStop)(
        IShellDispatch2* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *IsServiceRunning)(
        IShellDispatch2* This,
        BSTR ServiceName,
        VARIANT *pRunning);

    HRESULT (STDMETHODCALLTYPE *CanStartStopService)(
        IShellDispatch2* This,
        BSTR ServiceName,
        VARIANT *pCanStartStop);

    HRESULT (STDMETHODCALLTYPE *ShowBrowserBar)(
        IShellDispatch2* This,
        BSTR bstrClsid,
        VARIANT bShow,
        VARIANT *pSuccess);

    END_INTERFACE
} IShellDispatch2Vtbl;
interface IShellDispatch2 {
    CONST_VTBL IShellDispatch2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellDispatch2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellDispatch2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellDispatch2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellDispatch2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellDispatch2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellDispatch2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellDispatch2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellDispatch methods ***/
#define IShellDispatch2_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellDispatch2_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellDispatch2_NameSpace(This,vDir,ppsdf) (This)->lpVtbl->NameSpace(This,vDir,ppsdf)
#define IShellDispatch2_BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf) (This)->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf)
#define IShellDispatch2_Windows(This,ppid) (This)->lpVtbl->Windows(This,ppid)
#define IShellDispatch2_Open(This,vDir) (This)->lpVtbl->Open(This,vDir)
#define IShellDispatch2_Explore(This,vDir) (This)->lpVtbl->Explore(This,vDir)
#define IShellDispatch2_MinimizeAll(This) (This)->lpVtbl->MinimizeAll(This)
#define IShellDispatch2_UndoMinimizeALL(This) (This)->lpVtbl->UndoMinimizeALL(This)
#define IShellDispatch2_FileRun(This) (This)->lpVtbl->FileRun(This)
#define IShellDispatch2_CascadeWindows(This) (This)->lpVtbl->CascadeWindows(This)
#define IShellDispatch2_TileVertically(This) (This)->lpVtbl->TileVertically(This)
#define IShellDispatch2_TileHorizontally(This) (This)->lpVtbl->TileHorizontally(This)
#define IShellDispatch2_ShutdownWindows(This) (This)->lpVtbl->ShutdownWindows(This)
#define IShellDispatch2_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IShellDispatch2_EjectPC(This) (This)->lpVtbl->EjectPC(This)
#define IShellDispatch2_SetTime(This) (This)->lpVtbl->SetTime(This)
#define IShellDispatch2_TrayProperties(This) (This)->lpVtbl->TrayProperties(This)
#define IShellDispatch2_Help(This) (This)->lpVtbl->Help(This)
#define IShellDispatch2_FindFiles(This) (This)->lpVtbl->FindFiles(This)
#define IShellDispatch2_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define IShellDispatch2_RefreshMenu(This) (This)->lpVtbl->RefreshMenu(This)
#define IShellDispatch2_ControlPanelItem(This,bstrDir) (This)->lpVtbl->ControlPanelItem(This,bstrDir)
/*** IShellDispatch2 methods ***/
#define IShellDispatch2_IsRestricted(This,Group,Restriction,plRestrictValue) (This)->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue)
#define IShellDispatch2_ShellExecute(This,File,vArgs,vDir,vOperation,vShow) (This)->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow)
#define IShellDispatch2_FindPrinter(This,name,location,model) (This)->lpVtbl->FindPrinter(This,name,location,model)
#define IShellDispatch2_GetSystemInformation(This,name,pv) (This)->lpVtbl->GetSystemInformation(This,name,pv)
#define IShellDispatch2_ServiceStart(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch2_ServiceStop(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch2_IsServiceRunning(This,ServiceName,pRunning) (This)->lpVtbl->IsServiceRunning(This,ServiceName,pRunning)
#define IShellDispatch2_CanStartStopService(This,ServiceName,pCanStartStop) (This)->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop)
#define IShellDispatch2_ShowBrowserBar(This,bstrClsid,bShow,pSuccess) (This)->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellDispatch2_QueryInterface(IShellDispatch2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellDispatch2_AddRef(IShellDispatch2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellDispatch2_Release(IShellDispatch2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch2_GetTypeInfoCount(IShellDispatch2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellDispatch2_GetTypeInfo(IShellDispatch2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellDispatch2_GetIDsOfNames(IShellDispatch2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellDispatch2_Invoke(IShellDispatch2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch2_get_Application(IShellDispatch2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch2_get_Parent(IShellDispatch2* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch2_NameSpace(IShellDispatch2* This,VARIANT vDir,Folder **ppsdf) {
    return This->lpVtbl->NameSpace(This,vDir,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch2_BrowseForFolder(IShellDispatch2* This,LONG Hwnd,BSTR Title,LONG Options,VARIANT RootFolder,Folder **ppsdf) {
    return This->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch2_Windows(IShellDispatch2* This,IDispatch **ppid) {
    return This->lpVtbl->Windows(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch2_Open(IShellDispatch2* This,VARIANT vDir) {
    return This->lpVtbl->Open(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch2_Explore(IShellDispatch2* This,VARIANT vDir) {
    return This->lpVtbl->Explore(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch2_MinimizeAll(IShellDispatch2* This) {
    return This->lpVtbl->MinimizeAll(This);
}
static FORCEINLINE HRESULT IShellDispatch2_UndoMinimizeALL(IShellDispatch2* This) {
    return This->lpVtbl->UndoMinimizeALL(This);
}
static FORCEINLINE HRESULT IShellDispatch2_FileRun(IShellDispatch2* This) {
    return This->lpVtbl->FileRun(This);
}
static FORCEINLINE HRESULT IShellDispatch2_CascadeWindows(IShellDispatch2* This) {
    return This->lpVtbl->CascadeWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch2_TileVertically(IShellDispatch2* This) {
    return This->lpVtbl->TileVertically(This);
}
static FORCEINLINE HRESULT IShellDispatch2_TileHorizontally(IShellDispatch2* This) {
    return This->lpVtbl->TileHorizontally(This);
}
static FORCEINLINE HRESULT IShellDispatch2_ShutdownWindows(IShellDispatch2* This) {
    return This->lpVtbl->ShutdownWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch2_Suspend(IShellDispatch2* This) {
    return This->lpVtbl->Suspend(This);
}
static FORCEINLINE HRESULT IShellDispatch2_EjectPC(IShellDispatch2* This) {
    return This->lpVtbl->EjectPC(This);
}
static FORCEINLINE HRESULT IShellDispatch2_SetTime(IShellDispatch2* This) {
    return This->lpVtbl->SetTime(This);
}
static FORCEINLINE HRESULT IShellDispatch2_TrayProperties(IShellDispatch2* This) {
    return This->lpVtbl->TrayProperties(This);
}
static FORCEINLINE HRESULT IShellDispatch2_Help(IShellDispatch2* This) {
    return This->lpVtbl->Help(This);
}
static FORCEINLINE HRESULT IShellDispatch2_FindFiles(IShellDispatch2* This) {
    return This->lpVtbl->FindFiles(This);
}
static FORCEINLINE HRESULT IShellDispatch2_FindComputer(IShellDispatch2* This) {
    return This->lpVtbl->FindComputer(This);
}
static FORCEINLINE HRESULT IShellDispatch2_RefreshMenu(IShellDispatch2* This) {
    return This->lpVtbl->RefreshMenu(This);
}
static FORCEINLINE HRESULT IShellDispatch2_ControlPanelItem(IShellDispatch2* This,BSTR bstrDir) {
    return This->lpVtbl->ControlPanelItem(This,bstrDir);
}
/*** IShellDispatch2 methods ***/
static FORCEINLINE HRESULT IShellDispatch2_IsRestricted(IShellDispatch2* This,BSTR Group,BSTR Restriction,LONG *plRestrictValue) {
    return This->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue);
}
static FORCEINLINE HRESULT IShellDispatch2_ShellExecute(IShellDispatch2* This,BSTR File,VARIANT vArgs,VARIANT vDir,VARIANT vOperation,VARIANT vShow) {
    return This->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow);
}
static FORCEINLINE HRESULT IShellDispatch2_FindPrinter(IShellDispatch2* This,BSTR name,BSTR location,BSTR model) {
    return This->lpVtbl->FindPrinter(This,name,location,model);
}
static FORCEINLINE HRESULT IShellDispatch2_GetSystemInformation(IShellDispatch2* This,BSTR name,VARIANT *pv) {
    return This->lpVtbl->GetSystemInformation(This,name,pv);
}
static FORCEINLINE HRESULT IShellDispatch2_ServiceStart(IShellDispatch2* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch2_ServiceStop(IShellDispatch2* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch2_IsServiceRunning(IShellDispatch2* This,BSTR ServiceName,VARIANT *pRunning) {
    return This->lpVtbl->IsServiceRunning(This,ServiceName,pRunning);
}
static FORCEINLINE HRESULT IShellDispatch2_CanStartStopService(IShellDispatch2* This,BSTR ServiceName,VARIANT *pCanStartStop) {
    return This->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop);
}
static FORCEINLINE HRESULT IShellDispatch2_ShowBrowserBar(IShellDispatch2* This,BSTR bstrClsid,VARIANT bShow,VARIANT *pSuccess) {
    return This->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellDispatch2_IsRestricted_Proxy(
    IShellDispatch2* This,
    BSTR Group,
    BSTR Restriction,
    LONG *plRestrictValue);
void __RPC_STUB IShellDispatch2_IsRestricted_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch2_ShellExecute_Proxy(
    IShellDispatch2* This,
    BSTR File,
    VARIANT vArgs,
    VARIANT vDir,
    VARIANT vOperation,
    VARIANT vShow);
void __RPC_STUB IShellDispatch2_ShellExecute_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch2_FindPrinter_Proxy(
    IShellDispatch2* This,
    BSTR name,
    BSTR location,
    BSTR model);
void __RPC_STUB IShellDispatch2_FindPrinter_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch2_GetSystemInformation_Proxy(
    IShellDispatch2* This,
    BSTR name,
    VARIANT *pv);
void __RPC_STUB IShellDispatch2_GetSystemInformation_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch2_ServiceStart_Proxy(
    IShellDispatch2* This,
    BSTR ServiceName,
    VARIANT Persistent,
    VARIANT *pSuccess);
void __RPC_STUB IShellDispatch2_ServiceStart_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch2_ServiceStop_Proxy(
    IShellDispatch2* This,
    BSTR ServiceName,
    VARIANT Persistent,
    VARIANT *pSuccess);
void __RPC_STUB IShellDispatch2_ServiceStop_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch2_IsServiceRunning_Proxy(
    IShellDispatch2* This,
    BSTR ServiceName,
    VARIANT *pRunning);
void __RPC_STUB IShellDispatch2_IsServiceRunning_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch2_CanStartStopService_Proxy(
    IShellDispatch2* This,
    BSTR ServiceName,
    VARIANT *pCanStartStop);
void __RPC_STUB IShellDispatch2_CanStartStopService_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch2_ShowBrowserBar_Proxy(
    IShellDispatch2* This,
    BSTR bstrClsid,
    VARIANT bShow,
    VARIANT *pSuccess);
void __RPC_STUB IShellDispatch2_ShowBrowserBar_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellDispatch2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellDispatch3 interface
 */
#ifndef __IShellDispatch3_INTERFACE_DEFINED__
#define __IShellDispatch3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellDispatch3, 0x177160ca, 0xbb5a, 0x411c, 0x84,0x1d, 0xbd,0x38,0xfa,0xcd,0xea,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("177160ca-bb5a-411c-841d-bd38facdeaa0")
IShellDispatch3 : public IShellDispatch2
{
    virtual HRESULT STDMETHODCALLTYPE AddToRecent(
        VARIANT varFile,
        BSTR bstrCategory) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellDispatch3, 0x177160ca, 0xbb5a, 0x411c, 0x84,0x1d, 0xbd,0x38,0xfa,0xcd,0xea,0xa0)
#endif
#else
typedef struct IShellDispatch3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellDispatch3* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellDispatch3* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellDispatch3* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellDispatch3* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellDispatch3* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellDispatch3* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellDispatch3* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellDispatch3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellDispatch3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *NameSpace)(
        IShellDispatch3* This,
        VARIANT vDir,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *BrowseForFolder)(
        IShellDispatch3* This,
        LONG Hwnd,
        BSTR Title,
        LONG Options,
        VARIANT RootFolder,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *Windows)(
        IShellDispatch3* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Open)(
        IShellDispatch3* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *Explore)(
        IShellDispatch3* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *MinimizeAll)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *UndoMinimizeALL)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *FileRun)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *CascadeWindows)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *TileVertically)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *TileHorizontally)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *ShutdownWindows)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *EjectPC)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *SetTime)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *TrayProperties)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *Help)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *FindFiles)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *RefreshMenu)(
        IShellDispatch3* This);

    HRESULT (STDMETHODCALLTYPE *ControlPanelItem)(
        IShellDispatch3* This,
        BSTR bstrDir);

    /*** IShellDispatch2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsRestricted)(
        IShellDispatch3* This,
        BSTR Group,
        BSTR Restriction,
        LONG *plRestrictValue);

    HRESULT (STDMETHODCALLTYPE *ShellExecute)(
        IShellDispatch3* This,
        BSTR File,
        VARIANT vArgs,
        VARIANT vDir,
        VARIANT vOperation,
        VARIANT vShow);

    HRESULT (STDMETHODCALLTYPE *FindPrinter)(
        IShellDispatch3* This,
        BSTR name,
        BSTR location,
        BSTR model);

    HRESULT (STDMETHODCALLTYPE *GetSystemInformation)(
        IShellDispatch3* This,
        BSTR name,
        VARIANT *pv);

    HRESULT (STDMETHODCALLTYPE *ServiceStart)(
        IShellDispatch3* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *ServiceStop)(
        IShellDispatch3* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *IsServiceRunning)(
        IShellDispatch3* This,
        BSTR ServiceName,
        VARIANT *pRunning);

    HRESULT (STDMETHODCALLTYPE *CanStartStopService)(
        IShellDispatch3* This,
        BSTR ServiceName,
        VARIANT *pCanStartStop);

    HRESULT (STDMETHODCALLTYPE *ShowBrowserBar)(
        IShellDispatch3* This,
        BSTR bstrClsid,
        VARIANT bShow,
        VARIANT *pSuccess);

    /*** IShellDispatch3 methods ***/
    HRESULT (STDMETHODCALLTYPE *AddToRecent)(
        IShellDispatch3* This,
        VARIANT varFile,
        BSTR bstrCategory);

    END_INTERFACE
} IShellDispatch3Vtbl;
interface IShellDispatch3 {
    CONST_VTBL IShellDispatch3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellDispatch3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellDispatch3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellDispatch3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellDispatch3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellDispatch3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellDispatch3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellDispatch3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellDispatch methods ***/
#define IShellDispatch3_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellDispatch3_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellDispatch3_NameSpace(This,vDir,ppsdf) (This)->lpVtbl->NameSpace(This,vDir,ppsdf)
#define IShellDispatch3_BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf) (This)->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf)
#define IShellDispatch3_Windows(This,ppid) (This)->lpVtbl->Windows(This,ppid)
#define IShellDispatch3_Open(This,vDir) (This)->lpVtbl->Open(This,vDir)
#define IShellDispatch3_Explore(This,vDir) (This)->lpVtbl->Explore(This,vDir)
#define IShellDispatch3_MinimizeAll(This) (This)->lpVtbl->MinimizeAll(This)
#define IShellDispatch3_UndoMinimizeALL(This) (This)->lpVtbl->UndoMinimizeALL(This)
#define IShellDispatch3_FileRun(This) (This)->lpVtbl->FileRun(This)
#define IShellDispatch3_CascadeWindows(This) (This)->lpVtbl->CascadeWindows(This)
#define IShellDispatch3_TileVertically(This) (This)->lpVtbl->TileVertically(This)
#define IShellDispatch3_TileHorizontally(This) (This)->lpVtbl->TileHorizontally(This)
#define IShellDispatch3_ShutdownWindows(This) (This)->lpVtbl->ShutdownWindows(This)
#define IShellDispatch3_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IShellDispatch3_EjectPC(This) (This)->lpVtbl->EjectPC(This)
#define IShellDispatch3_SetTime(This) (This)->lpVtbl->SetTime(This)
#define IShellDispatch3_TrayProperties(This) (This)->lpVtbl->TrayProperties(This)
#define IShellDispatch3_Help(This) (This)->lpVtbl->Help(This)
#define IShellDispatch3_FindFiles(This) (This)->lpVtbl->FindFiles(This)
#define IShellDispatch3_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define IShellDispatch3_RefreshMenu(This) (This)->lpVtbl->RefreshMenu(This)
#define IShellDispatch3_ControlPanelItem(This,bstrDir) (This)->lpVtbl->ControlPanelItem(This,bstrDir)
/*** IShellDispatch2 methods ***/
#define IShellDispatch3_IsRestricted(This,Group,Restriction,plRestrictValue) (This)->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue)
#define IShellDispatch3_ShellExecute(This,File,vArgs,vDir,vOperation,vShow) (This)->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow)
#define IShellDispatch3_FindPrinter(This,name,location,model) (This)->lpVtbl->FindPrinter(This,name,location,model)
#define IShellDispatch3_GetSystemInformation(This,name,pv) (This)->lpVtbl->GetSystemInformation(This,name,pv)
#define IShellDispatch3_ServiceStart(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch3_ServiceStop(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch3_IsServiceRunning(This,ServiceName,pRunning) (This)->lpVtbl->IsServiceRunning(This,ServiceName,pRunning)
#define IShellDispatch3_CanStartStopService(This,ServiceName,pCanStartStop) (This)->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop)
#define IShellDispatch3_ShowBrowserBar(This,bstrClsid,bShow,pSuccess) (This)->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess)
/*** IShellDispatch3 methods ***/
#define IShellDispatch3_AddToRecent(This,varFile,bstrCategory) (This)->lpVtbl->AddToRecent(This,varFile,bstrCategory)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellDispatch3_QueryInterface(IShellDispatch3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellDispatch3_AddRef(IShellDispatch3* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellDispatch3_Release(IShellDispatch3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch3_GetTypeInfoCount(IShellDispatch3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellDispatch3_GetTypeInfo(IShellDispatch3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellDispatch3_GetIDsOfNames(IShellDispatch3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellDispatch3_Invoke(IShellDispatch3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch3_get_Application(IShellDispatch3* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch3_get_Parent(IShellDispatch3* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch3_NameSpace(IShellDispatch3* This,VARIANT vDir,Folder **ppsdf) {
    return This->lpVtbl->NameSpace(This,vDir,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch3_BrowseForFolder(IShellDispatch3* This,LONG Hwnd,BSTR Title,LONG Options,VARIANT RootFolder,Folder **ppsdf) {
    return This->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch3_Windows(IShellDispatch3* This,IDispatch **ppid) {
    return This->lpVtbl->Windows(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch3_Open(IShellDispatch3* This,VARIANT vDir) {
    return This->lpVtbl->Open(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch3_Explore(IShellDispatch3* This,VARIANT vDir) {
    return This->lpVtbl->Explore(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch3_MinimizeAll(IShellDispatch3* This) {
    return This->lpVtbl->MinimizeAll(This);
}
static FORCEINLINE HRESULT IShellDispatch3_UndoMinimizeALL(IShellDispatch3* This) {
    return This->lpVtbl->UndoMinimizeALL(This);
}
static FORCEINLINE HRESULT IShellDispatch3_FileRun(IShellDispatch3* This) {
    return This->lpVtbl->FileRun(This);
}
static FORCEINLINE HRESULT IShellDispatch3_CascadeWindows(IShellDispatch3* This) {
    return This->lpVtbl->CascadeWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch3_TileVertically(IShellDispatch3* This) {
    return This->lpVtbl->TileVertically(This);
}
static FORCEINLINE HRESULT IShellDispatch3_TileHorizontally(IShellDispatch3* This) {
    return This->lpVtbl->TileHorizontally(This);
}
static FORCEINLINE HRESULT IShellDispatch3_ShutdownWindows(IShellDispatch3* This) {
    return This->lpVtbl->ShutdownWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch3_Suspend(IShellDispatch3* This) {
    return This->lpVtbl->Suspend(This);
}
static FORCEINLINE HRESULT IShellDispatch3_EjectPC(IShellDispatch3* This) {
    return This->lpVtbl->EjectPC(This);
}
static FORCEINLINE HRESULT IShellDispatch3_SetTime(IShellDispatch3* This) {
    return This->lpVtbl->SetTime(This);
}
static FORCEINLINE HRESULT IShellDispatch3_TrayProperties(IShellDispatch3* This) {
    return This->lpVtbl->TrayProperties(This);
}
static FORCEINLINE HRESULT IShellDispatch3_Help(IShellDispatch3* This) {
    return This->lpVtbl->Help(This);
}
static FORCEINLINE HRESULT IShellDispatch3_FindFiles(IShellDispatch3* This) {
    return This->lpVtbl->FindFiles(This);
}
static FORCEINLINE HRESULT IShellDispatch3_FindComputer(IShellDispatch3* This) {
    return This->lpVtbl->FindComputer(This);
}
static FORCEINLINE HRESULT IShellDispatch3_RefreshMenu(IShellDispatch3* This) {
    return This->lpVtbl->RefreshMenu(This);
}
static FORCEINLINE HRESULT IShellDispatch3_ControlPanelItem(IShellDispatch3* This,BSTR bstrDir) {
    return This->lpVtbl->ControlPanelItem(This,bstrDir);
}
/*** IShellDispatch2 methods ***/
static FORCEINLINE HRESULT IShellDispatch3_IsRestricted(IShellDispatch3* This,BSTR Group,BSTR Restriction,LONG *plRestrictValue) {
    return This->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue);
}
static FORCEINLINE HRESULT IShellDispatch3_ShellExecute(IShellDispatch3* This,BSTR File,VARIANT vArgs,VARIANT vDir,VARIANT vOperation,VARIANT vShow) {
    return This->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow);
}
static FORCEINLINE HRESULT IShellDispatch3_FindPrinter(IShellDispatch3* This,BSTR name,BSTR location,BSTR model) {
    return This->lpVtbl->FindPrinter(This,name,location,model);
}
static FORCEINLINE HRESULT IShellDispatch3_GetSystemInformation(IShellDispatch3* This,BSTR name,VARIANT *pv) {
    return This->lpVtbl->GetSystemInformation(This,name,pv);
}
static FORCEINLINE HRESULT IShellDispatch3_ServiceStart(IShellDispatch3* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch3_ServiceStop(IShellDispatch3* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch3_IsServiceRunning(IShellDispatch3* This,BSTR ServiceName,VARIANT *pRunning) {
    return This->lpVtbl->IsServiceRunning(This,ServiceName,pRunning);
}
static FORCEINLINE HRESULT IShellDispatch3_CanStartStopService(IShellDispatch3* This,BSTR ServiceName,VARIANT *pCanStartStop) {
    return This->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop);
}
static FORCEINLINE HRESULT IShellDispatch3_ShowBrowserBar(IShellDispatch3* This,BSTR bstrClsid,VARIANT bShow,VARIANT *pSuccess) {
    return This->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess);
}
/*** IShellDispatch3 methods ***/
static FORCEINLINE HRESULT IShellDispatch3_AddToRecent(IShellDispatch3* This,VARIANT varFile,BSTR bstrCategory) {
    return This->lpVtbl->AddToRecent(This,varFile,bstrCategory);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellDispatch3_AddToRecent_Proxy(
    IShellDispatch3* This,
    VARIANT varFile,
    BSTR bstrCategory);
void __RPC_STUB IShellDispatch3_AddToRecent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellDispatch3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellDispatch4 interface
 */
#ifndef __IShellDispatch4_INTERFACE_DEFINED__
#define __IShellDispatch4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellDispatch4, 0xefd84b2d, 0x4bcf, 0x4298, 0xbe,0x25, 0xeb,0x54,0x2a,0x59,0xfb,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("efd84b2d-4bcf-4298-be25-eb542a59fbda")
IShellDispatch4 : public IShellDispatch3
{
    virtual HRESULT STDMETHODCALLTYPE WindowsSecurity(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ToggleDesktop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExplorerPolicy(
        BSTR bstrPolicyName,
        VARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSetting(
        LONG lSetting,
        VARIANT_BOOL *pResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellDispatch4, 0xefd84b2d, 0x4bcf, 0x4298, 0xbe,0x25, 0xeb,0x54,0x2a,0x59,0xfb,0xda)
#endif
#else
typedef struct IShellDispatch4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellDispatch4* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellDispatch4* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellDispatch4* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellDispatch4* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellDispatch4* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellDispatch4* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellDispatch4* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellDispatch4* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellDispatch4* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *NameSpace)(
        IShellDispatch4* This,
        VARIANT vDir,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *BrowseForFolder)(
        IShellDispatch4* This,
        LONG Hwnd,
        BSTR Title,
        LONG Options,
        VARIANT RootFolder,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *Windows)(
        IShellDispatch4* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Open)(
        IShellDispatch4* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *Explore)(
        IShellDispatch4* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *MinimizeAll)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *UndoMinimizeALL)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *FileRun)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *CascadeWindows)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *TileVertically)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *TileHorizontally)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *ShutdownWindows)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *EjectPC)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *SetTime)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *TrayProperties)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *Help)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *FindFiles)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *RefreshMenu)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *ControlPanelItem)(
        IShellDispatch4* This,
        BSTR bstrDir);

    /*** IShellDispatch2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsRestricted)(
        IShellDispatch4* This,
        BSTR Group,
        BSTR Restriction,
        LONG *plRestrictValue);

    HRESULT (STDMETHODCALLTYPE *ShellExecute)(
        IShellDispatch4* This,
        BSTR File,
        VARIANT vArgs,
        VARIANT vDir,
        VARIANT vOperation,
        VARIANT vShow);

    HRESULT (STDMETHODCALLTYPE *FindPrinter)(
        IShellDispatch4* This,
        BSTR name,
        BSTR location,
        BSTR model);

    HRESULT (STDMETHODCALLTYPE *GetSystemInformation)(
        IShellDispatch4* This,
        BSTR name,
        VARIANT *pv);

    HRESULT (STDMETHODCALLTYPE *ServiceStart)(
        IShellDispatch4* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *ServiceStop)(
        IShellDispatch4* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *IsServiceRunning)(
        IShellDispatch4* This,
        BSTR ServiceName,
        VARIANT *pRunning);

    HRESULT (STDMETHODCALLTYPE *CanStartStopService)(
        IShellDispatch4* This,
        BSTR ServiceName,
        VARIANT *pCanStartStop);

    HRESULT (STDMETHODCALLTYPE *ShowBrowserBar)(
        IShellDispatch4* This,
        BSTR bstrClsid,
        VARIANT bShow,
        VARIANT *pSuccess);

    /*** IShellDispatch3 methods ***/
    HRESULT (STDMETHODCALLTYPE *AddToRecent)(
        IShellDispatch4* This,
        VARIANT varFile,
        BSTR bstrCategory);

    /*** IShellDispatch4 methods ***/
    HRESULT (STDMETHODCALLTYPE *WindowsSecurity)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *ToggleDesktop)(
        IShellDispatch4* This);

    HRESULT (STDMETHODCALLTYPE *ExplorerPolicy)(
        IShellDispatch4* This,
        BSTR bstrPolicyName,
        VARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetSetting)(
        IShellDispatch4* This,
        LONG lSetting,
        VARIANT_BOOL *pResult);

    END_INTERFACE
} IShellDispatch4Vtbl;
interface IShellDispatch4 {
    CONST_VTBL IShellDispatch4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellDispatch4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellDispatch4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellDispatch4_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellDispatch4_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellDispatch4_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellDispatch4_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellDispatch4_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellDispatch methods ***/
#define IShellDispatch4_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellDispatch4_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellDispatch4_NameSpace(This,vDir,ppsdf) (This)->lpVtbl->NameSpace(This,vDir,ppsdf)
#define IShellDispatch4_BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf) (This)->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf)
#define IShellDispatch4_Windows(This,ppid) (This)->lpVtbl->Windows(This,ppid)
#define IShellDispatch4_Open(This,vDir) (This)->lpVtbl->Open(This,vDir)
#define IShellDispatch4_Explore(This,vDir) (This)->lpVtbl->Explore(This,vDir)
#define IShellDispatch4_MinimizeAll(This) (This)->lpVtbl->MinimizeAll(This)
#define IShellDispatch4_UndoMinimizeALL(This) (This)->lpVtbl->UndoMinimizeALL(This)
#define IShellDispatch4_FileRun(This) (This)->lpVtbl->FileRun(This)
#define IShellDispatch4_CascadeWindows(This) (This)->lpVtbl->CascadeWindows(This)
#define IShellDispatch4_TileVertically(This) (This)->lpVtbl->TileVertically(This)
#define IShellDispatch4_TileHorizontally(This) (This)->lpVtbl->TileHorizontally(This)
#define IShellDispatch4_ShutdownWindows(This) (This)->lpVtbl->ShutdownWindows(This)
#define IShellDispatch4_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IShellDispatch4_EjectPC(This) (This)->lpVtbl->EjectPC(This)
#define IShellDispatch4_SetTime(This) (This)->lpVtbl->SetTime(This)
#define IShellDispatch4_TrayProperties(This) (This)->lpVtbl->TrayProperties(This)
#define IShellDispatch4_Help(This) (This)->lpVtbl->Help(This)
#define IShellDispatch4_FindFiles(This) (This)->lpVtbl->FindFiles(This)
#define IShellDispatch4_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define IShellDispatch4_RefreshMenu(This) (This)->lpVtbl->RefreshMenu(This)
#define IShellDispatch4_ControlPanelItem(This,bstrDir) (This)->lpVtbl->ControlPanelItem(This,bstrDir)
/*** IShellDispatch2 methods ***/
#define IShellDispatch4_IsRestricted(This,Group,Restriction,plRestrictValue) (This)->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue)
#define IShellDispatch4_ShellExecute(This,File,vArgs,vDir,vOperation,vShow) (This)->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow)
#define IShellDispatch4_FindPrinter(This,name,location,model) (This)->lpVtbl->FindPrinter(This,name,location,model)
#define IShellDispatch4_GetSystemInformation(This,name,pv) (This)->lpVtbl->GetSystemInformation(This,name,pv)
#define IShellDispatch4_ServiceStart(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch4_ServiceStop(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch4_IsServiceRunning(This,ServiceName,pRunning) (This)->lpVtbl->IsServiceRunning(This,ServiceName,pRunning)
#define IShellDispatch4_CanStartStopService(This,ServiceName,pCanStartStop) (This)->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop)
#define IShellDispatch4_ShowBrowserBar(This,bstrClsid,bShow,pSuccess) (This)->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess)
/*** IShellDispatch3 methods ***/
#define IShellDispatch4_AddToRecent(This,varFile,bstrCategory) (This)->lpVtbl->AddToRecent(This,varFile,bstrCategory)
/*** IShellDispatch4 methods ***/
#define IShellDispatch4_WindowsSecurity(This) (This)->lpVtbl->WindowsSecurity(This)
#define IShellDispatch4_ToggleDesktop(This) (This)->lpVtbl->ToggleDesktop(This)
#define IShellDispatch4_ExplorerPolicy(This,bstrPolicyName,pValue) (This)->lpVtbl->ExplorerPolicy(This,bstrPolicyName,pValue)
#define IShellDispatch4_GetSetting(This,lSetting,pResult) (This)->lpVtbl->GetSetting(This,lSetting,pResult)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellDispatch4_QueryInterface(IShellDispatch4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellDispatch4_AddRef(IShellDispatch4* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellDispatch4_Release(IShellDispatch4* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch4_GetTypeInfoCount(IShellDispatch4* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellDispatch4_GetTypeInfo(IShellDispatch4* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellDispatch4_GetIDsOfNames(IShellDispatch4* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellDispatch4_Invoke(IShellDispatch4* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch4_get_Application(IShellDispatch4* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch4_get_Parent(IShellDispatch4* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch4_NameSpace(IShellDispatch4* This,VARIANT vDir,Folder **ppsdf) {
    return This->lpVtbl->NameSpace(This,vDir,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch4_BrowseForFolder(IShellDispatch4* This,LONG Hwnd,BSTR Title,LONG Options,VARIANT RootFolder,Folder **ppsdf) {
    return This->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch4_Windows(IShellDispatch4* This,IDispatch **ppid) {
    return This->lpVtbl->Windows(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch4_Open(IShellDispatch4* This,VARIANT vDir) {
    return This->lpVtbl->Open(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch4_Explore(IShellDispatch4* This,VARIANT vDir) {
    return This->lpVtbl->Explore(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch4_MinimizeAll(IShellDispatch4* This) {
    return This->lpVtbl->MinimizeAll(This);
}
static FORCEINLINE HRESULT IShellDispatch4_UndoMinimizeALL(IShellDispatch4* This) {
    return This->lpVtbl->UndoMinimizeALL(This);
}
static FORCEINLINE HRESULT IShellDispatch4_FileRun(IShellDispatch4* This) {
    return This->lpVtbl->FileRun(This);
}
static FORCEINLINE HRESULT IShellDispatch4_CascadeWindows(IShellDispatch4* This) {
    return This->lpVtbl->CascadeWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch4_TileVertically(IShellDispatch4* This) {
    return This->lpVtbl->TileVertically(This);
}
static FORCEINLINE HRESULT IShellDispatch4_TileHorizontally(IShellDispatch4* This) {
    return This->lpVtbl->TileHorizontally(This);
}
static FORCEINLINE HRESULT IShellDispatch4_ShutdownWindows(IShellDispatch4* This) {
    return This->lpVtbl->ShutdownWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch4_Suspend(IShellDispatch4* This) {
    return This->lpVtbl->Suspend(This);
}
static FORCEINLINE HRESULT IShellDispatch4_EjectPC(IShellDispatch4* This) {
    return This->lpVtbl->EjectPC(This);
}
static FORCEINLINE HRESULT IShellDispatch4_SetTime(IShellDispatch4* This) {
    return This->lpVtbl->SetTime(This);
}
static FORCEINLINE HRESULT IShellDispatch4_TrayProperties(IShellDispatch4* This) {
    return This->lpVtbl->TrayProperties(This);
}
static FORCEINLINE HRESULT IShellDispatch4_Help(IShellDispatch4* This) {
    return This->lpVtbl->Help(This);
}
static FORCEINLINE HRESULT IShellDispatch4_FindFiles(IShellDispatch4* This) {
    return This->lpVtbl->FindFiles(This);
}
static FORCEINLINE HRESULT IShellDispatch4_FindComputer(IShellDispatch4* This) {
    return This->lpVtbl->FindComputer(This);
}
static FORCEINLINE HRESULT IShellDispatch4_RefreshMenu(IShellDispatch4* This) {
    return This->lpVtbl->RefreshMenu(This);
}
static FORCEINLINE HRESULT IShellDispatch4_ControlPanelItem(IShellDispatch4* This,BSTR bstrDir) {
    return This->lpVtbl->ControlPanelItem(This,bstrDir);
}
/*** IShellDispatch2 methods ***/
static FORCEINLINE HRESULT IShellDispatch4_IsRestricted(IShellDispatch4* This,BSTR Group,BSTR Restriction,LONG *plRestrictValue) {
    return This->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue);
}
static FORCEINLINE HRESULT IShellDispatch4_ShellExecute(IShellDispatch4* This,BSTR File,VARIANT vArgs,VARIANT vDir,VARIANT vOperation,VARIANT vShow) {
    return This->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow);
}
static FORCEINLINE HRESULT IShellDispatch4_FindPrinter(IShellDispatch4* This,BSTR name,BSTR location,BSTR model) {
    return This->lpVtbl->FindPrinter(This,name,location,model);
}
static FORCEINLINE HRESULT IShellDispatch4_GetSystemInformation(IShellDispatch4* This,BSTR name,VARIANT *pv) {
    return This->lpVtbl->GetSystemInformation(This,name,pv);
}
static FORCEINLINE HRESULT IShellDispatch4_ServiceStart(IShellDispatch4* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch4_ServiceStop(IShellDispatch4* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch4_IsServiceRunning(IShellDispatch4* This,BSTR ServiceName,VARIANT *pRunning) {
    return This->lpVtbl->IsServiceRunning(This,ServiceName,pRunning);
}
static FORCEINLINE HRESULT IShellDispatch4_CanStartStopService(IShellDispatch4* This,BSTR ServiceName,VARIANT *pCanStartStop) {
    return This->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop);
}
static FORCEINLINE HRESULT IShellDispatch4_ShowBrowserBar(IShellDispatch4* This,BSTR bstrClsid,VARIANT bShow,VARIANT *pSuccess) {
    return This->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess);
}
/*** IShellDispatch3 methods ***/
static FORCEINLINE HRESULT IShellDispatch4_AddToRecent(IShellDispatch4* This,VARIANT varFile,BSTR bstrCategory) {
    return This->lpVtbl->AddToRecent(This,varFile,bstrCategory);
}
/*** IShellDispatch4 methods ***/
static FORCEINLINE HRESULT IShellDispatch4_WindowsSecurity(IShellDispatch4* This) {
    return This->lpVtbl->WindowsSecurity(This);
}
static FORCEINLINE HRESULT IShellDispatch4_ToggleDesktop(IShellDispatch4* This) {
    return This->lpVtbl->ToggleDesktop(This);
}
static FORCEINLINE HRESULT IShellDispatch4_ExplorerPolicy(IShellDispatch4* This,BSTR bstrPolicyName,VARIANT *pValue) {
    return This->lpVtbl->ExplorerPolicy(This,bstrPolicyName,pValue);
}
static FORCEINLINE HRESULT IShellDispatch4_GetSetting(IShellDispatch4* This,LONG lSetting,VARIANT_BOOL *pResult) {
    return This->lpVtbl->GetSetting(This,lSetting,pResult);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellDispatch4_WindowsSecurity_Proxy(
    IShellDispatch4* This);
void __RPC_STUB IShellDispatch4_WindowsSecurity_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch4_ToggleDesktop_Proxy(
    IShellDispatch4* This);
void __RPC_STUB IShellDispatch4_ToggleDesktop_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch4_ExplorerPolicy_Proxy(
    IShellDispatch4* This,
    BSTR bstrPolicyName,
    VARIANT *pValue);
void __RPC_STUB IShellDispatch4_ExplorerPolicy_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IShellDispatch4_GetSetting_Proxy(
    IShellDispatch4* This,
    LONG lSetting,
    VARIANT_BOOL *pResult);
void __RPC_STUB IShellDispatch4_GetSetting_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellDispatch4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellDispatch5 interface
 */
#ifndef __IShellDispatch5_INTERFACE_DEFINED__
#define __IShellDispatch5_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellDispatch5, 0x866738b9, 0x6cf2, 0x4de8, 0x87,0x67, 0xf7,0x94,0xeb,0xe7,0x4f,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("866738b9-6cf2-4de8-8767-f794ebe74f4e")
IShellDispatch5 : public IShellDispatch4
{
    virtual HRESULT STDMETHODCALLTYPE WindowSwitcher(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellDispatch5, 0x866738b9, 0x6cf2, 0x4de8, 0x87,0x67, 0xf7,0x94,0xeb,0xe7,0x4f,0x4e)
#endif
#else
typedef struct IShellDispatch5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellDispatch5* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellDispatch5* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellDispatch5* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellDispatch5* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellDispatch5* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellDispatch5* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellDispatch5* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellDispatch5* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellDispatch5* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *NameSpace)(
        IShellDispatch5* This,
        VARIANT vDir,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *BrowseForFolder)(
        IShellDispatch5* This,
        LONG Hwnd,
        BSTR Title,
        LONG Options,
        VARIANT RootFolder,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *Windows)(
        IShellDispatch5* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Open)(
        IShellDispatch5* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *Explore)(
        IShellDispatch5* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *MinimizeAll)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *UndoMinimizeALL)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *FileRun)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *CascadeWindows)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *TileVertically)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *TileHorizontally)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *ShutdownWindows)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *EjectPC)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *SetTime)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *TrayProperties)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *Help)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *FindFiles)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *RefreshMenu)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *ControlPanelItem)(
        IShellDispatch5* This,
        BSTR bstrDir);

    /*** IShellDispatch2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsRestricted)(
        IShellDispatch5* This,
        BSTR Group,
        BSTR Restriction,
        LONG *plRestrictValue);

    HRESULT (STDMETHODCALLTYPE *ShellExecute)(
        IShellDispatch5* This,
        BSTR File,
        VARIANT vArgs,
        VARIANT vDir,
        VARIANT vOperation,
        VARIANT vShow);

    HRESULT (STDMETHODCALLTYPE *FindPrinter)(
        IShellDispatch5* This,
        BSTR name,
        BSTR location,
        BSTR model);

    HRESULT (STDMETHODCALLTYPE *GetSystemInformation)(
        IShellDispatch5* This,
        BSTR name,
        VARIANT *pv);

    HRESULT (STDMETHODCALLTYPE *ServiceStart)(
        IShellDispatch5* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *ServiceStop)(
        IShellDispatch5* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *IsServiceRunning)(
        IShellDispatch5* This,
        BSTR ServiceName,
        VARIANT *pRunning);

    HRESULT (STDMETHODCALLTYPE *CanStartStopService)(
        IShellDispatch5* This,
        BSTR ServiceName,
        VARIANT *pCanStartStop);

    HRESULT (STDMETHODCALLTYPE *ShowBrowserBar)(
        IShellDispatch5* This,
        BSTR bstrClsid,
        VARIANT bShow,
        VARIANT *pSuccess);

    /*** IShellDispatch3 methods ***/
    HRESULT (STDMETHODCALLTYPE *AddToRecent)(
        IShellDispatch5* This,
        VARIANT varFile,
        BSTR bstrCategory);

    /*** IShellDispatch4 methods ***/
    HRESULT (STDMETHODCALLTYPE *WindowsSecurity)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *ToggleDesktop)(
        IShellDispatch5* This);

    HRESULT (STDMETHODCALLTYPE *ExplorerPolicy)(
        IShellDispatch5* This,
        BSTR bstrPolicyName,
        VARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetSetting)(
        IShellDispatch5* This,
        LONG lSetting,
        VARIANT_BOOL *pResult);

    /*** IShellDispatch5 methods ***/
    HRESULT (STDMETHODCALLTYPE *WindowSwitcher)(
        IShellDispatch5* This);

    END_INTERFACE
} IShellDispatch5Vtbl;
interface IShellDispatch5 {
    CONST_VTBL IShellDispatch5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellDispatch5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellDispatch5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellDispatch5_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellDispatch5_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellDispatch5_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellDispatch5_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellDispatch5_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellDispatch methods ***/
#define IShellDispatch5_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellDispatch5_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellDispatch5_NameSpace(This,vDir,ppsdf) (This)->lpVtbl->NameSpace(This,vDir,ppsdf)
#define IShellDispatch5_BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf) (This)->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf)
#define IShellDispatch5_Windows(This,ppid) (This)->lpVtbl->Windows(This,ppid)
#define IShellDispatch5_Open(This,vDir) (This)->lpVtbl->Open(This,vDir)
#define IShellDispatch5_Explore(This,vDir) (This)->lpVtbl->Explore(This,vDir)
#define IShellDispatch5_MinimizeAll(This) (This)->lpVtbl->MinimizeAll(This)
#define IShellDispatch5_UndoMinimizeALL(This) (This)->lpVtbl->UndoMinimizeALL(This)
#define IShellDispatch5_FileRun(This) (This)->lpVtbl->FileRun(This)
#define IShellDispatch5_CascadeWindows(This) (This)->lpVtbl->CascadeWindows(This)
#define IShellDispatch5_TileVertically(This) (This)->lpVtbl->TileVertically(This)
#define IShellDispatch5_TileHorizontally(This) (This)->lpVtbl->TileHorizontally(This)
#define IShellDispatch5_ShutdownWindows(This) (This)->lpVtbl->ShutdownWindows(This)
#define IShellDispatch5_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IShellDispatch5_EjectPC(This) (This)->lpVtbl->EjectPC(This)
#define IShellDispatch5_SetTime(This) (This)->lpVtbl->SetTime(This)
#define IShellDispatch5_TrayProperties(This) (This)->lpVtbl->TrayProperties(This)
#define IShellDispatch5_Help(This) (This)->lpVtbl->Help(This)
#define IShellDispatch5_FindFiles(This) (This)->lpVtbl->FindFiles(This)
#define IShellDispatch5_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define IShellDispatch5_RefreshMenu(This) (This)->lpVtbl->RefreshMenu(This)
#define IShellDispatch5_ControlPanelItem(This,bstrDir) (This)->lpVtbl->ControlPanelItem(This,bstrDir)
/*** IShellDispatch2 methods ***/
#define IShellDispatch5_IsRestricted(This,Group,Restriction,plRestrictValue) (This)->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue)
#define IShellDispatch5_ShellExecute(This,File,vArgs,vDir,vOperation,vShow) (This)->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow)
#define IShellDispatch5_FindPrinter(This,name,location,model) (This)->lpVtbl->FindPrinter(This,name,location,model)
#define IShellDispatch5_GetSystemInformation(This,name,pv) (This)->lpVtbl->GetSystemInformation(This,name,pv)
#define IShellDispatch5_ServiceStart(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch5_ServiceStop(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch5_IsServiceRunning(This,ServiceName,pRunning) (This)->lpVtbl->IsServiceRunning(This,ServiceName,pRunning)
#define IShellDispatch5_CanStartStopService(This,ServiceName,pCanStartStop) (This)->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop)
#define IShellDispatch5_ShowBrowserBar(This,bstrClsid,bShow,pSuccess) (This)->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess)
/*** IShellDispatch3 methods ***/
#define IShellDispatch5_AddToRecent(This,varFile,bstrCategory) (This)->lpVtbl->AddToRecent(This,varFile,bstrCategory)
/*** IShellDispatch4 methods ***/
#define IShellDispatch5_WindowsSecurity(This) (This)->lpVtbl->WindowsSecurity(This)
#define IShellDispatch5_ToggleDesktop(This) (This)->lpVtbl->ToggleDesktop(This)
#define IShellDispatch5_ExplorerPolicy(This,bstrPolicyName,pValue) (This)->lpVtbl->ExplorerPolicy(This,bstrPolicyName,pValue)
#define IShellDispatch5_GetSetting(This,lSetting,pResult) (This)->lpVtbl->GetSetting(This,lSetting,pResult)
/*** IShellDispatch5 methods ***/
#define IShellDispatch5_WindowSwitcher(This) (This)->lpVtbl->WindowSwitcher(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellDispatch5_QueryInterface(IShellDispatch5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellDispatch5_AddRef(IShellDispatch5* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellDispatch5_Release(IShellDispatch5* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch5_GetTypeInfoCount(IShellDispatch5* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellDispatch5_GetTypeInfo(IShellDispatch5* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellDispatch5_GetIDsOfNames(IShellDispatch5* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellDispatch5_Invoke(IShellDispatch5* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch5_get_Application(IShellDispatch5* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch5_get_Parent(IShellDispatch5* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch5_NameSpace(IShellDispatch5* This,VARIANT vDir,Folder **ppsdf) {
    return This->lpVtbl->NameSpace(This,vDir,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch5_BrowseForFolder(IShellDispatch5* This,LONG Hwnd,BSTR Title,LONG Options,VARIANT RootFolder,Folder **ppsdf) {
    return This->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch5_Windows(IShellDispatch5* This,IDispatch **ppid) {
    return This->lpVtbl->Windows(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch5_Open(IShellDispatch5* This,VARIANT vDir) {
    return This->lpVtbl->Open(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch5_Explore(IShellDispatch5* This,VARIANT vDir) {
    return This->lpVtbl->Explore(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch5_MinimizeAll(IShellDispatch5* This) {
    return This->lpVtbl->MinimizeAll(This);
}
static FORCEINLINE HRESULT IShellDispatch5_UndoMinimizeALL(IShellDispatch5* This) {
    return This->lpVtbl->UndoMinimizeALL(This);
}
static FORCEINLINE HRESULT IShellDispatch5_FileRun(IShellDispatch5* This) {
    return This->lpVtbl->FileRun(This);
}
static FORCEINLINE HRESULT IShellDispatch5_CascadeWindows(IShellDispatch5* This) {
    return This->lpVtbl->CascadeWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch5_TileVertically(IShellDispatch5* This) {
    return This->lpVtbl->TileVertically(This);
}
static FORCEINLINE HRESULT IShellDispatch5_TileHorizontally(IShellDispatch5* This) {
    return This->lpVtbl->TileHorizontally(This);
}
static FORCEINLINE HRESULT IShellDispatch5_ShutdownWindows(IShellDispatch5* This) {
    return This->lpVtbl->ShutdownWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch5_Suspend(IShellDispatch5* This) {
    return This->lpVtbl->Suspend(This);
}
static FORCEINLINE HRESULT IShellDispatch5_EjectPC(IShellDispatch5* This) {
    return This->lpVtbl->EjectPC(This);
}
static FORCEINLINE HRESULT IShellDispatch5_SetTime(IShellDispatch5* This) {
    return This->lpVtbl->SetTime(This);
}
static FORCEINLINE HRESULT IShellDispatch5_TrayProperties(IShellDispatch5* This) {
    return This->lpVtbl->TrayProperties(This);
}
static FORCEINLINE HRESULT IShellDispatch5_Help(IShellDispatch5* This) {
    return This->lpVtbl->Help(This);
}
static FORCEINLINE HRESULT IShellDispatch5_FindFiles(IShellDispatch5* This) {
    return This->lpVtbl->FindFiles(This);
}
static FORCEINLINE HRESULT IShellDispatch5_FindComputer(IShellDispatch5* This) {
    return This->lpVtbl->FindComputer(This);
}
static FORCEINLINE HRESULT IShellDispatch5_RefreshMenu(IShellDispatch5* This) {
    return This->lpVtbl->RefreshMenu(This);
}
static FORCEINLINE HRESULT IShellDispatch5_ControlPanelItem(IShellDispatch5* This,BSTR bstrDir) {
    return This->lpVtbl->ControlPanelItem(This,bstrDir);
}
/*** IShellDispatch2 methods ***/
static FORCEINLINE HRESULT IShellDispatch5_IsRestricted(IShellDispatch5* This,BSTR Group,BSTR Restriction,LONG *plRestrictValue) {
    return This->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue);
}
static FORCEINLINE HRESULT IShellDispatch5_ShellExecute(IShellDispatch5* This,BSTR File,VARIANT vArgs,VARIANT vDir,VARIANT vOperation,VARIANT vShow) {
    return This->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow);
}
static FORCEINLINE HRESULT IShellDispatch5_FindPrinter(IShellDispatch5* This,BSTR name,BSTR location,BSTR model) {
    return This->lpVtbl->FindPrinter(This,name,location,model);
}
static FORCEINLINE HRESULT IShellDispatch5_GetSystemInformation(IShellDispatch5* This,BSTR name,VARIANT *pv) {
    return This->lpVtbl->GetSystemInformation(This,name,pv);
}
static FORCEINLINE HRESULT IShellDispatch5_ServiceStart(IShellDispatch5* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch5_ServiceStop(IShellDispatch5* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch5_IsServiceRunning(IShellDispatch5* This,BSTR ServiceName,VARIANT *pRunning) {
    return This->lpVtbl->IsServiceRunning(This,ServiceName,pRunning);
}
static FORCEINLINE HRESULT IShellDispatch5_CanStartStopService(IShellDispatch5* This,BSTR ServiceName,VARIANT *pCanStartStop) {
    return This->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop);
}
static FORCEINLINE HRESULT IShellDispatch5_ShowBrowserBar(IShellDispatch5* This,BSTR bstrClsid,VARIANT bShow,VARIANT *pSuccess) {
    return This->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess);
}
/*** IShellDispatch3 methods ***/
static FORCEINLINE HRESULT IShellDispatch5_AddToRecent(IShellDispatch5* This,VARIANT varFile,BSTR bstrCategory) {
    return This->lpVtbl->AddToRecent(This,varFile,bstrCategory);
}
/*** IShellDispatch4 methods ***/
static FORCEINLINE HRESULT IShellDispatch5_WindowsSecurity(IShellDispatch5* This) {
    return This->lpVtbl->WindowsSecurity(This);
}
static FORCEINLINE HRESULT IShellDispatch5_ToggleDesktop(IShellDispatch5* This) {
    return This->lpVtbl->ToggleDesktop(This);
}
static FORCEINLINE HRESULT IShellDispatch5_ExplorerPolicy(IShellDispatch5* This,BSTR bstrPolicyName,VARIANT *pValue) {
    return This->lpVtbl->ExplorerPolicy(This,bstrPolicyName,pValue);
}
static FORCEINLINE HRESULT IShellDispatch5_GetSetting(IShellDispatch5* This,LONG lSetting,VARIANT_BOOL *pResult) {
    return This->lpVtbl->GetSetting(This,lSetting,pResult);
}
/*** IShellDispatch5 methods ***/
static FORCEINLINE HRESULT IShellDispatch5_WindowSwitcher(IShellDispatch5* This) {
    return This->lpVtbl->WindowSwitcher(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellDispatch5_WindowSwitcher_Proxy(
    IShellDispatch5* This);
void __RPC_STUB IShellDispatch5_WindowSwitcher_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellDispatch5_INTERFACE_DEFINED__ */

#if NTDDI_VERSION >= NTDDI_WIN8
/*****************************************************************************
 * IShellDispatch6 interface
 */
#ifndef __IShellDispatch6_INTERFACE_DEFINED__
#define __IShellDispatch6_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellDispatch6, 0x286e6f1b, 0x7113, 0x4355, 0x95,0x62, 0x96,0xb7,0xe9,0xd6,0x4c,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("286e6f1b-**************-96b7e9d64c54")
IShellDispatch6 : public IShellDispatch5
{
    virtual HRESULT STDMETHODCALLTYPE SearchCommand(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellDispatch6, 0x286e6f1b, 0x7113, 0x4355, 0x95,0x62, 0x96,0xb7,0xe9,0xd6,0x4c,0x54)
#endif
#else
typedef struct IShellDispatch6Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellDispatch6* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellDispatch6* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellDispatch6* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellDispatch6* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellDispatch6* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellDispatch6* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellDispatch6* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IShellDispatch6* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IShellDispatch6* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *NameSpace)(
        IShellDispatch6* This,
        VARIANT vDir,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *BrowseForFolder)(
        IShellDispatch6* This,
        LONG Hwnd,
        BSTR Title,
        LONG Options,
        VARIANT RootFolder,
        Folder **ppsdf);

    HRESULT (STDMETHODCALLTYPE *Windows)(
        IShellDispatch6* This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Open)(
        IShellDispatch6* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *Explore)(
        IShellDispatch6* This,
        VARIANT vDir);

    HRESULT (STDMETHODCALLTYPE *MinimizeAll)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *UndoMinimizeALL)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *FileRun)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *CascadeWindows)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *TileVertically)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *TileHorizontally)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *ShutdownWindows)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *EjectPC)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *SetTime)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *TrayProperties)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *Help)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *FindFiles)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *RefreshMenu)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *ControlPanelItem)(
        IShellDispatch6* This,
        BSTR bstrDir);

    /*** IShellDispatch2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsRestricted)(
        IShellDispatch6* This,
        BSTR Group,
        BSTR Restriction,
        LONG *plRestrictValue);

    HRESULT (STDMETHODCALLTYPE *ShellExecute)(
        IShellDispatch6* This,
        BSTR File,
        VARIANT vArgs,
        VARIANT vDir,
        VARIANT vOperation,
        VARIANT vShow);

    HRESULT (STDMETHODCALLTYPE *FindPrinter)(
        IShellDispatch6* This,
        BSTR name,
        BSTR location,
        BSTR model);

    HRESULT (STDMETHODCALLTYPE *GetSystemInformation)(
        IShellDispatch6* This,
        BSTR name,
        VARIANT *pv);

    HRESULT (STDMETHODCALLTYPE *ServiceStart)(
        IShellDispatch6* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *ServiceStop)(
        IShellDispatch6* This,
        BSTR ServiceName,
        VARIANT Persistent,
        VARIANT *pSuccess);

    HRESULT (STDMETHODCALLTYPE *IsServiceRunning)(
        IShellDispatch6* This,
        BSTR ServiceName,
        VARIANT *pRunning);

    HRESULT (STDMETHODCALLTYPE *CanStartStopService)(
        IShellDispatch6* This,
        BSTR ServiceName,
        VARIANT *pCanStartStop);

    HRESULT (STDMETHODCALLTYPE *ShowBrowserBar)(
        IShellDispatch6* This,
        BSTR bstrClsid,
        VARIANT bShow,
        VARIANT *pSuccess);

    /*** IShellDispatch3 methods ***/
    HRESULT (STDMETHODCALLTYPE *AddToRecent)(
        IShellDispatch6* This,
        VARIANT varFile,
        BSTR bstrCategory);

    /*** IShellDispatch4 methods ***/
    HRESULT (STDMETHODCALLTYPE *WindowsSecurity)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *ToggleDesktop)(
        IShellDispatch6* This);

    HRESULT (STDMETHODCALLTYPE *ExplorerPolicy)(
        IShellDispatch6* This,
        BSTR bstrPolicyName,
        VARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetSetting)(
        IShellDispatch6* This,
        LONG lSetting,
        VARIANT_BOOL *pResult);

    /*** IShellDispatch5 methods ***/
    HRESULT (STDMETHODCALLTYPE *WindowSwitcher)(
        IShellDispatch6* This);

    /*** IShellDispatch6 methods ***/
    HRESULT (STDMETHODCALLTYPE *SearchCommand)(
        IShellDispatch6* This);

    END_INTERFACE
} IShellDispatch6Vtbl;
interface IShellDispatch6 {
    CONST_VTBL IShellDispatch6Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellDispatch6_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellDispatch6_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellDispatch6_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellDispatch6_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellDispatch6_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellDispatch6_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellDispatch6_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellDispatch methods ***/
#define IShellDispatch6_get_Application(This,ppid) (This)->lpVtbl->get_Application(This,ppid)
#define IShellDispatch6_get_Parent(This,ppid) (This)->lpVtbl->get_Parent(This,ppid)
#define IShellDispatch6_NameSpace(This,vDir,ppsdf) (This)->lpVtbl->NameSpace(This,vDir,ppsdf)
#define IShellDispatch6_BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf) (This)->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf)
#define IShellDispatch6_Windows(This,ppid) (This)->lpVtbl->Windows(This,ppid)
#define IShellDispatch6_Open(This,vDir) (This)->lpVtbl->Open(This,vDir)
#define IShellDispatch6_Explore(This,vDir) (This)->lpVtbl->Explore(This,vDir)
#define IShellDispatch6_MinimizeAll(This) (This)->lpVtbl->MinimizeAll(This)
#define IShellDispatch6_UndoMinimizeALL(This) (This)->lpVtbl->UndoMinimizeALL(This)
#define IShellDispatch6_FileRun(This) (This)->lpVtbl->FileRun(This)
#define IShellDispatch6_CascadeWindows(This) (This)->lpVtbl->CascadeWindows(This)
#define IShellDispatch6_TileVertically(This) (This)->lpVtbl->TileVertically(This)
#define IShellDispatch6_TileHorizontally(This) (This)->lpVtbl->TileHorizontally(This)
#define IShellDispatch6_ShutdownWindows(This) (This)->lpVtbl->ShutdownWindows(This)
#define IShellDispatch6_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IShellDispatch6_EjectPC(This) (This)->lpVtbl->EjectPC(This)
#define IShellDispatch6_SetTime(This) (This)->lpVtbl->SetTime(This)
#define IShellDispatch6_TrayProperties(This) (This)->lpVtbl->TrayProperties(This)
#define IShellDispatch6_Help(This) (This)->lpVtbl->Help(This)
#define IShellDispatch6_FindFiles(This) (This)->lpVtbl->FindFiles(This)
#define IShellDispatch6_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define IShellDispatch6_RefreshMenu(This) (This)->lpVtbl->RefreshMenu(This)
#define IShellDispatch6_ControlPanelItem(This,bstrDir) (This)->lpVtbl->ControlPanelItem(This,bstrDir)
/*** IShellDispatch2 methods ***/
#define IShellDispatch6_IsRestricted(This,Group,Restriction,plRestrictValue) (This)->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue)
#define IShellDispatch6_ShellExecute(This,File,vArgs,vDir,vOperation,vShow) (This)->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow)
#define IShellDispatch6_FindPrinter(This,name,location,model) (This)->lpVtbl->FindPrinter(This,name,location,model)
#define IShellDispatch6_GetSystemInformation(This,name,pv) (This)->lpVtbl->GetSystemInformation(This,name,pv)
#define IShellDispatch6_ServiceStart(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch6_ServiceStop(This,ServiceName,Persistent,pSuccess) (This)->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess)
#define IShellDispatch6_IsServiceRunning(This,ServiceName,pRunning) (This)->lpVtbl->IsServiceRunning(This,ServiceName,pRunning)
#define IShellDispatch6_CanStartStopService(This,ServiceName,pCanStartStop) (This)->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop)
#define IShellDispatch6_ShowBrowserBar(This,bstrClsid,bShow,pSuccess) (This)->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess)
/*** IShellDispatch3 methods ***/
#define IShellDispatch6_AddToRecent(This,varFile,bstrCategory) (This)->lpVtbl->AddToRecent(This,varFile,bstrCategory)
/*** IShellDispatch4 methods ***/
#define IShellDispatch6_WindowsSecurity(This) (This)->lpVtbl->WindowsSecurity(This)
#define IShellDispatch6_ToggleDesktop(This) (This)->lpVtbl->ToggleDesktop(This)
#define IShellDispatch6_ExplorerPolicy(This,bstrPolicyName,pValue) (This)->lpVtbl->ExplorerPolicy(This,bstrPolicyName,pValue)
#define IShellDispatch6_GetSetting(This,lSetting,pResult) (This)->lpVtbl->GetSetting(This,lSetting,pResult)
/*** IShellDispatch5 methods ***/
#define IShellDispatch6_WindowSwitcher(This) (This)->lpVtbl->WindowSwitcher(This)
/*** IShellDispatch6 methods ***/
#define IShellDispatch6_SearchCommand(This) (This)->lpVtbl->SearchCommand(This)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IShellDispatch6_QueryInterface(IShellDispatch6* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IShellDispatch6_AddRef(IShellDispatch6* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IShellDispatch6_Release(IShellDispatch6* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch6_GetTypeInfoCount(IShellDispatch6* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IShellDispatch6_GetTypeInfo(IShellDispatch6* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IShellDispatch6_GetIDsOfNames(IShellDispatch6* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IShellDispatch6_Invoke(IShellDispatch6* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellDispatch methods ***/
static FORCEINLINE HRESULT IShellDispatch6_get_Application(IShellDispatch6* This,IDispatch **ppid) {
    return This->lpVtbl->get_Application(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch6_get_Parent(IShellDispatch6* This,IDispatch **ppid) {
    return This->lpVtbl->get_Parent(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch6_NameSpace(IShellDispatch6* This,VARIANT vDir,Folder **ppsdf) {
    return This->lpVtbl->NameSpace(This,vDir,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch6_BrowseForFolder(IShellDispatch6* This,LONG Hwnd,BSTR Title,LONG Options,VARIANT RootFolder,Folder **ppsdf) {
    return This->lpVtbl->BrowseForFolder(This,Hwnd,Title,Options,RootFolder,ppsdf);
}
static FORCEINLINE HRESULT IShellDispatch6_Windows(IShellDispatch6* This,IDispatch **ppid) {
    return This->lpVtbl->Windows(This,ppid);
}
static FORCEINLINE HRESULT IShellDispatch6_Open(IShellDispatch6* This,VARIANT vDir) {
    return This->lpVtbl->Open(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch6_Explore(IShellDispatch6* This,VARIANT vDir) {
    return This->lpVtbl->Explore(This,vDir);
}
static FORCEINLINE HRESULT IShellDispatch6_MinimizeAll(IShellDispatch6* This) {
    return This->lpVtbl->MinimizeAll(This);
}
static FORCEINLINE HRESULT IShellDispatch6_UndoMinimizeALL(IShellDispatch6* This) {
    return This->lpVtbl->UndoMinimizeALL(This);
}
static FORCEINLINE HRESULT IShellDispatch6_FileRun(IShellDispatch6* This) {
    return This->lpVtbl->FileRun(This);
}
static FORCEINLINE HRESULT IShellDispatch6_CascadeWindows(IShellDispatch6* This) {
    return This->lpVtbl->CascadeWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch6_TileVertically(IShellDispatch6* This) {
    return This->lpVtbl->TileVertically(This);
}
static FORCEINLINE HRESULT IShellDispatch6_TileHorizontally(IShellDispatch6* This) {
    return This->lpVtbl->TileHorizontally(This);
}
static FORCEINLINE HRESULT IShellDispatch6_ShutdownWindows(IShellDispatch6* This) {
    return This->lpVtbl->ShutdownWindows(This);
}
static FORCEINLINE HRESULT IShellDispatch6_Suspend(IShellDispatch6* This) {
    return This->lpVtbl->Suspend(This);
}
static FORCEINLINE HRESULT IShellDispatch6_EjectPC(IShellDispatch6* This) {
    return This->lpVtbl->EjectPC(This);
}
static FORCEINLINE HRESULT IShellDispatch6_SetTime(IShellDispatch6* This) {
    return This->lpVtbl->SetTime(This);
}
static FORCEINLINE HRESULT IShellDispatch6_TrayProperties(IShellDispatch6* This) {
    return This->lpVtbl->TrayProperties(This);
}
static FORCEINLINE HRESULT IShellDispatch6_Help(IShellDispatch6* This) {
    return This->lpVtbl->Help(This);
}
static FORCEINLINE HRESULT IShellDispatch6_FindFiles(IShellDispatch6* This) {
    return This->lpVtbl->FindFiles(This);
}
static FORCEINLINE HRESULT IShellDispatch6_FindComputer(IShellDispatch6* This) {
    return This->lpVtbl->FindComputer(This);
}
static FORCEINLINE HRESULT IShellDispatch6_RefreshMenu(IShellDispatch6* This) {
    return This->lpVtbl->RefreshMenu(This);
}
static FORCEINLINE HRESULT IShellDispatch6_ControlPanelItem(IShellDispatch6* This,BSTR bstrDir) {
    return This->lpVtbl->ControlPanelItem(This,bstrDir);
}
/*** IShellDispatch2 methods ***/
static FORCEINLINE HRESULT IShellDispatch6_IsRestricted(IShellDispatch6* This,BSTR Group,BSTR Restriction,LONG *plRestrictValue) {
    return This->lpVtbl->IsRestricted(This,Group,Restriction,plRestrictValue);
}
static FORCEINLINE HRESULT IShellDispatch6_ShellExecute(IShellDispatch6* This,BSTR File,VARIANT vArgs,VARIANT vDir,VARIANT vOperation,VARIANT vShow) {
    return This->lpVtbl->ShellExecute(This,File,vArgs,vDir,vOperation,vShow);
}
static FORCEINLINE HRESULT IShellDispatch6_FindPrinter(IShellDispatch6* This,BSTR name,BSTR location,BSTR model) {
    return This->lpVtbl->FindPrinter(This,name,location,model);
}
static FORCEINLINE HRESULT IShellDispatch6_GetSystemInformation(IShellDispatch6* This,BSTR name,VARIANT *pv) {
    return This->lpVtbl->GetSystemInformation(This,name,pv);
}
static FORCEINLINE HRESULT IShellDispatch6_ServiceStart(IShellDispatch6* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStart(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch6_ServiceStop(IShellDispatch6* This,BSTR ServiceName,VARIANT Persistent,VARIANT *pSuccess) {
    return This->lpVtbl->ServiceStop(This,ServiceName,Persistent,pSuccess);
}
static FORCEINLINE HRESULT IShellDispatch6_IsServiceRunning(IShellDispatch6* This,BSTR ServiceName,VARIANT *pRunning) {
    return This->lpVtbl->IsServiceRunning(This,ServiceName,pRunning);
}
static FORCEINLINE HRESULT IShellDispatch6_CanStartStopService(IShellDispatch6* This,BSTR ServiceName,VARIANT *pCanStartStop) {
    return This->lpVtbl->CanStartStopService(This,ServiceName,pCanStartStop);
}
static FORCEINLINE HRESULT IShellDispatch6_ShowBrowserBar(IShellDispatch6* This,BSTR bstrClsid,VARIANT bShow,VARIANT *pSuccess) {
    return This->lpVtbl->ShowBrowserBar(This,bstrClsid,bShow,pSuccess);
}
/*** IShellDispatch3 methods ***/
static FORCEINLINE HRESULT IShellDispatch6_AddToRecent(IShellDispatch6* This,VARIANT varFile,BSTR bstrCategory) {
    return This->lpVtbl->AddToRecent(This,varFile,bstrCategory);
}
/*** IShellDispatch4 methods ***/
static FORCEINLINE HRESULT IShellDispatch6_WindowsSecurity(IShellDispatch6* This) {
    return This->lpVtbl->WindowsSecurity(This);
}
static FORCEINLINE HRESULT IShellDispatch6_ToggleDesktop(IShellDispatch6* This) {
    return This->lpVtbl->ToggleDesktop(This);
}
static FORCEINLINE HRESULT IShellDispatch6_ExplorerPolicy(IShellDispatch6* This,BSTR bstrPolicyName,VARIANT *pValue) {
    return This->lpVtbl->ExplorerPolicy(This,bstrPolicyName,pValue);
}
static FORCEINLINE HRESULT IShellDispatch6_GetSetting(IShellDispatch6* This,LONG lSetting,VARIANT_BOOL *pResult) {
    return This->lpVtbl->GetSetting(This,lSetting,pResult);
}
/*** IShellDispatch5 methods ***/
static FORCEINLINE HRESULT IShellDispatch6_WindowSwitcher(IShellDispatch6* This) {
    return This->lpVtbl->WindowSwitcher(This);
}
/*** IShellDispatch6 methods ***/
static FORCEINLINE HRESULT IShellDispatch6_SearchCommand(IShellDispatch6* This) {
    return This->lpVtbl->SearchCommand(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IShellDispatch6_SearchCommand_Proxy(
    IShellDispatch6* This);
void __RPC_STUB IShellDispatch6_SearchCommand_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IShellDispatch6_INTERFACE_DEFINED__ */

#endif
/*****************************************************************************
 * Shell coclass
 */

DEFINE_GUID(CLSID_Shell, 0x13709620, 0xc279, 0x11ce, 0xa4,0x9e, 0x44,0x45,0x53,0x54,0x00,0x00);

#ifdef __cplusplus
class DECLSPEC_UUID("13709620-c279-11ce-a49e-************") Shell;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(Shell, 0x13709620, 0xc279, 0x11ce, 0xa4,0x9e, 0x44,0x45,0x53,0x54,0x00,0x00)
#endif
#endif

/*****************************************************************************
 * ShellDispatchInproc coclass
 */

DEFINE_GUID(CLSID_ShellDispatchInproc, 0x0a89a860, 0xd7b1, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00);

#ifdef __cplusplus
class DECLSPEC_UUID("0a89a860-d7b1-11ce-8350-************") ShellDispatchInproc;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellDispatchInproc, 0x0a89a860, 0xd7b1, 0x11ce, 0x83,0x50, 0x44,0x45,0x53,0x54,0x00,0x00)
#endif
#endif

typedef enum ShellSpecialFolderConstants {
    ssfDESKTOP = 0x0,
    ssfPROGRAMS = 0x2,
    ssfCONTROLS = 0x3,
    ssfPRINTERS = 0x4,
    ssfPERSONAL = 0x5,
    ssfFAVORITES = 0x6,
    ssfSTARTUP = 0x7,
    ssfRECENT = 0x8,
    ssfSENDTO = 0x9,
    ssfBITBUCKET = 0xa,
    ssfSTARTMENU = 0xb,
    ssfDESKTOPDIRECTORY = 0x10,
    ssfDRIVES = 0x11,
    ssfNETWORK = 0x12,
    ssfNETHOOD = 0x13,
    ssfFONTS = 0x14,
    ssfTEMPLATES = 0x15,
    ssfCOMMONSTARTMENU = 0x16,
    ssfCOMMONPROGRAMS = 0x17,
    ssfCOMMONSTARTUP = 0x18,
    ssfCOMMONDESKTOPDIR = 0x19,
    ssfAPPDATA = 0x1a,
    ssfPRINTHOOD = 0x1b,
    ssfLOCALAPPDATA = 0x1c,
    ssfALTSTARTUP = 0x1d,
    ssfCOMMONALTSTARTUP = 0x1e,
    ssfCOMMONFAVORITES = 0x1f,
    ssfINTERNETCACHE = 0x20,
    ssfCOOKIES = 0x21,
    ssfHISTORY = 0x22,
    ssfCOMMONAPPDATA = 0x23,
    ssfWINDOWS = 0x24,
    ssfSYSTEM = 0x25,
    ssfPROGRAMFILES = 0x26,
    ssfMYPICTURES = 0x27,
    ssfPROFILE = 0x28,
    ssfSYSTEMx86 = 0x29,
    ssfPROGRAMFILESx86 = 0x30
} ShellSpecialFolderConstants;
/*****************************************************************************
 * IFileSearchBand interface
 */
#ifndef __IFileSearchBand_INTERFACE_DEFINED__
#define __IFileSearchBand_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFileSearchBand, 0x2d91eea1, 0x9932, 0x11d2, 0xbe,0x86, 0x00,0xa0,0xc9,0xa8,0x3d,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2d91eea1-9932-11d2-be86-00a0c9a83da1")
IFileSearchBand : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE SetFocus(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSearchParameters(
        BSTR *pbstrSearchID,
        VARIANT_BOOL bNavToResults,
        VARIANT *pvarScope,
        VARIANT *pvarQueryFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SearchID(
        BSTR *pbstrSearchID) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Scope(
        VARIANT *pvarScope) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_QueryFile(
        VARIANT *pvarFile) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFileSearchBand, 0x2d91eea1, 0x9932, 0x11d2, 0xbe,0x86, 0x00,0xa0,0xc9,0xa8,0x3d,0xa1)
#endif
#else
typedef struct IFileSearchBandVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFileSearchBand* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFileSearchBand* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFileSearchBand* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFileSearchBand* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFileSearchBand* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFileSearchBand* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFileSearchBand* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFileSearchBand methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFocus)(
        IFileSearchBand* This);

    HRESULT (STDMETHODCALLTYPE *SetSearchParameters)(
        IFileSearchBand* This,
        BSTR *pbstrSearchID,
        VARIANT_BOOL bNavToResults,
        VARIANT *pvarScope,
        VARIANT *pvarQueryFile);

    HRESULT (STDMETHODCALLTYPE *get_SearchID)(
        IFileSearchBand* This,
        BSTR *pbstrSearchID);

    HRESULT (STDMETHODCALLTYPE *get_Scope)(
        IFileSearchBand* This,
        VARIANT *pvarScope);

    HRESULT (STDMETHODCALLTYPE *get_QueryFile)(
        IFileSearchBand* This,
        VARIANT *pvarFile);

    END_INTERFACE
} IFileSearchBandVtbl;
interface IFileSearchBand {
    CONST_VTBL IFileSearchBandVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFileSearchBand_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFileSearchBand_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFileSearchBand_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFileSearchBand_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFileSearchBand_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFileSearchBand_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFileSearchBand_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFileSearchBand methods ***/
#define IFileSearchBand_SetFocus(This) (This)->lpVtbl->SetFocus(This)
#define IFileSearchBand_SetSearchParameters(This,pbstrSearchID,bNavToResults,pvarScope,pvarQueryFile) (This)->lpVtbl->SetSearchParameters(This,pbstrSearchID,bNavToResults,pvarScope,pvarQueryFile)
#define IFileSearchBand_get_SearchID(This,pbstrSearchID) (This)->lpVtbl->get_SearchID(This,pbstrSearchID)
#define IFileSearchBand_get_Scope(This,pvarScope) (This)->lpVtbl->get_Scope(This,pvarScope)
#define IFileSearchBand_get_QueryFile(This,pvarFile) (This)->lpVtbl->get_QueryFile(This,pvarFile)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IFileSearchBand_QueryInterface(IFileSearchBand* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IFileSearchBand_AddRef(IFileSearchBand* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IFileSearchBand_Release(IFileSearchBand* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IFileSearchBand_GetTypeInfoCount(IFileSearchBand* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IFileSearchBand_GetTypeInfo(IFileSearchBand* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IFileSearchBand_GetIDsOfNames(IFileSearchBand* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IFileSearchBand_Invoke(IFileSearchBand* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFileSearchBand methods ***/
static FORCEINLINE HRESULT IFileSearchBand_SetFocus(IFileSearchBand* This) {
    return This->lpVtbl->SetFocus(This);
}
static FORCEINLINE HRESULT IFileSearchBand_SetSearchParameters(IFileSearchBand* This,BSTR *pbstrSearchID,VARIANT_BOOL bNavToResults,VARIANT *pvarScope,VARIANT *pvarQueryFile) {
    return This->lpVtbl->SetSearchParameters(This,pbstrSearchID,bNavToResults,pvarScope,pvarQueryFile);
}
static FORCEINLINE HRESULT IFileSearchBand_get_SearchID(IFileSearchBand* This,BSTR *pbstrSearchID) {
    return This->lpVtbl->get_SearchID(This,pbstrSearchID);
}
static FORCEINLINE HRESULT IFileSearchBand_get_Scope(IFileSearchBand* This,VARIANT *pvarScope) {
    return This->lpVtbl->get_Scope(This,pvarScope);
}
static FORCEINLINE HRESULT IFileSearchBand_get_QueryFile(IFileSearchBand* This,VARIANT *pvarFile) {
    return This->lpVtbl->get_QueryFile(This,pvarFile);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IFileSearchBand_SetFocus_Proxy(
    IFileSearchBand* This);
void __RPC_STUB IFileSearchBand_SetFocus_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFileSearchBand_SetSearchParameters_Proxy(
    IFileSearchBand* This,
    BSTR *pbstrSearchID,
    VARIANT_BOOL bNavToResults,
    VARIANT *pvarScope,
    VARIANT *pvarQueryFile);
void __RPC_STUB IFileSearchBand_SetSearchParameters_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFileSearchBand_get_SearchID_Proxy(
    IFileSearchBand* This,
    BSTR *pbstrSearchID);
void __RPC_STUB IFileSearchBand_get_SearchID_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFileSearchBand_get_Scope_Proxy(
    IFileSearchBand* This,
    VARIANT *pvarScope);
void __RPC_STUB IFileSearchBand_get_Scope_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IFileSearchBand_get_QueryFile_Proxy(
    IFileSearchBand* This,
    VARIANT *pvarFile);
void __RPC_STUB IFileSearchBand_get_QueryFile_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IFileSearchBand_INTERFACE_DEFINED__ */

/*****************************************************************************
 * FileSearchBand coclass
 */

DEFINE_GUID(CLSID_FileSearchBand, 0xc4ee31f3, 0x4768, 0x11d2, 0xbe,0x5c, 0x00,0xa0,0xc9,0xa8,0x3d,0xa1);

#ifdef __cplusplus
class DECLSPEC_UUID("c4ee31f3-4768-11d2-be5c-00a0c9a83da1") FileSearchBand;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(FileSearchBand, 0xc4ee31f3, 0x4768, 0x11d2, 0xbe,0x5c, 0x00,0xa0,0xc9,0xa8,0x3d,0xa1)
#endif
#endif

/*****************************************************************************
 * IWebWizardHost interface
 */
#ifndef __IWebWizardHost_INTERFACE_DEFINED__
#define __IWebWizardHost_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWebWizardHost, 0x18bcc359, 0x4990, 0x4bfb, 0xb9,0x51, 0x3c,0x83,0x70,0x2b,0xe5,0xf9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("18bcc359-4990-4bfb-b951-3c83702be5f9")
IWebWizardHost : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE FinalBack(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FinalNext(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Caption(
        BSTR bstrCaption) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Caption(
        BSTR *pbstrCaption) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Property(
        BSTR bstrPropertyName,
        VARIANT *pvProperty) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Property(
        BSTR bstrPropertyName,
        VARIANT *pvProperty) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetWizardButtons(
        VARIANT_BOOL vfEnableBack,
        VARIANT_BOOL vfEnableNext,
        VARIANT_BOOL vfLastPage) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHeaderText(
        BSTR bstrHeaderTitle,
        BSTR bstrHeaderSubtitle) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWebWizardHost, 0x18bcc359, 0x4990, 0x4bfb, 0xb9,0x51, 0x3c,0x83,0x70,0x2b,0xe5,0xf9)
#endif
#else
typedef struct IWebWizardHostVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWebWizardHost* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWebWizardHost* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWebWizardHost* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWebWizardHost* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWebWizardHost* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWebWizardHost* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWebWizardHost* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWebWizardHost methods ***/
    HRESULT (STDMETHODCALLTYPE *FinalBack)(
        IWebWizardHost* This);

    HRESULT (STDMETHODCALLTYPE *FinalNext)(
        IWebWizardHost* This);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IWebWizardHost* This);

    HRESULT (STDMETHODCALLTYPE *put_Caption)(
        IWebWizardHost* This,
        BSTR bstrCaption);

    HRESULT (STDMETHODCALLTYPE *get_Caption)(
        IWebWizardHost* This,
        BSTR *pbstrCaption);

    HRESULT (STDMETHODCALLTYPE *put_Property)(
        IWebWizardHost* This,
        BSTR bstrPropertyName,
        VARIANT *pvProperty);

    HRESULT (STDMETHODCALLTYPE *get_Property)(
        IWebWizardHost* This,
        BSTR bstrPropertyName,
        VARIANT *pvProperty);

    HRESULT (STDMETHODCALLTYPE *SetWizardButtons)(
        IWebWizardHost* This,
        VARIANT_BOOL vfEnableBack,
        VARIANT_BOOL vfEnableNext,
        VARIANT_BOOL vfLastPage);

    HRESULT (STDMETHODCALLTYPE *SetHeaderText)(
        IWebWizardHost* This,
        BSTR bstrHeaderTitle,
        BSTR bstrHeaderSubtitle);

    END_INTERFACE
} IWebWizardHostVtbl;
interface IWebWizardHost {
    CONST_VTBL IWebWizardHostVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWebWizardHost_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWebWizardHost_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWebWizardHost_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWebWizardHost_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWebWizardHost_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWebWizardHost_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWebWizardHost_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWebWizardHost methods ***/
#define IWebWizardHost_FinalBack(This) (This)->lpVtbl->FinalBack(This)
#define IWebWizardHost_FinalNext(This) (This)->lpVtbl->FinalNext(This)
#define IWebWizardHost_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IWebWizardHost_put_Caption(This,bstrCaption) (This)->lpVtbl->put_Caption(This,bstrCaption)
#define IWebWizardHost_get_Caption(This,pbstrCaption) (This)->lpVtbl->get_Caption(This,pbstrCaption)
#define IWebWizardHost_put_Property(This,bstrPropertyName,pvProperty) (This)->lpVtbl->put_Property(This,bstrPropertyName,pvProperty)
#define IWebWizardHost_get_Property(This,bstrPropertyName,pvProperty) (This)->lpVtbl->get_Property(This,bstrPropertyName,pvProperty)
#define IWebWizardHost_SetWizardButtons(This,vfEnableBack,vfEnableNext,vfLastPage) (This)->lpVtbl->SetWizardButtons(This,vfEnableBack,vfEnableNext,vfLastPage)
#define IWebWizardHost_SetHeaderText(This,bstrHeaderTitle,bstrHeaderSubtitle) (This)->lpVtbl->SetHeaderText(This,bstrHeaderTitle,bstrHeaderSubtitle)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IWebWizardHost_QueryInterface(IWebWizardHost* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IWebWizardHost_AddRef(IWebWizardHost* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IWebWizardHost_Release(IWebWizardHost* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT IWebWizardHost_GetTypeInfoCount(IWebWizardHost* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT IWebWizardHost_GetTypeInfo(IWebWizardHost* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT IWebWizardHost_GetIDsOfNames(IWebWizardHost* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT IWebWizardHost_Invoke(IWebWizardHost* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWebWizardHost methods ***/
static FORCEINLINE HRESULT IWebWizardHost_FinalBack(IWebWizardHost* This) {
    return This->lpVtbl->FinalBack(This);
}
static FORCEINLINE HRESULT IWebWizardHost_FinalNext(IWebWizardHost* This) {
    return This->lpVtbl->FinalNext(This);
}
static FORCEINLINE HRESULT IWebWizardHost_Cancel(IWebWizardHost* This) {
    return This->lpVtbl->Cancel(This);
}
static FORCEINLINE HRESULT IWebWizardHost_put_Caption(IWebWizardHost* This,BSTR bstrCaption) {
    return This->lpVtbl->put_Caption(This,bstrCaption);
}
static FORCEINLINE HRESULT IWebWizardHost_get_Caption(IWebWizardHost* This,BSTR *pbstrCaption) {
    return This->lpVtbl->get_Caption(This,pbstrCaption);
}
static FORCEINLINE HRESULT IWebWizardHost_put_Property(IWebWizardHost* This,BSTR bstrPropertyName,VARIANT *pvProperty) {
    return This->lpVtbl->put_Property(This,bstrPropertyName,pvProperty);
}
static FORCEINLINE HRESULT IWebWizardHost_get_Property(IWebWizardHost* This,BSTR bstrPropertyName,VARIANT *pvProperty) {
    return This->lpVtbl->get_Property(This,bstrPropertyName,pvProperty);
}
static FORCEINLINE HRESULT IWebWizardHost_SetWizardButtons(IWebWizardHost* This,VARIANT_BOOL vfEnableBack,VARIANT_BOOL vfEnableNext,VARIANT_BOOL vfLastPage) {
    return This->lpVtbl->SetWizardButtons(This,vfEnableBack,vfEnableNext,vfLastPage);
}
static FORCEINLINE HRESULT IWebWizardHost_SetHeaderText(IWebWizardHost* This,BSTR bstrHeaderTitle,BSTR bstrHeaderSubtitle) {
    return This->lpVtbl->SetHeaderText(This,bstrHeaderTitle,bstrHeaderSubtitle);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IWebWizardHost_FinalBack_Proxy(
    IWebWizardHost* This);
void __RPC_STUB IWebWizardHost_FinalBack_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWebWizardHost_FinalNext_Proxy(
    IWebWizardHost* This);
void __RPC_STUB IWebWizardHost_FinalNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWebWizardHost_Cancel_Proxy(
    IWebWizardHost* This);
void __RPC_STUB IWebWizardHost_Cancel_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWebWizardHost_put_Caption_Proxy(
    IWebWizardHost* This,
    BSTR bstrCaption);
void __RPC_STUB IWebWizardHost_put_Caption_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWebWizardHost_get_Caption_Proxy(
    IWebWizardHost* This,
    BSTR *pbstrCaption);
void __RPC_STUB IWebWizardHost_get_Caption_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWebWizardHost_put_Property_Proxy(
    IWebWizardHost* This,
    BSTR bstrPropertyName,
    VARIANT *pvProperty);
void __RPC_STUB IWebWizardHost_put_Property_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWebWizardHost_get_Property_Proxy(
    IWebWizardHost* This,
    BSTR bstrPropertyName,
    VARIANT *pvProperty);
void __RPC_STUB IWebWizardHost_get_Property_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWebWizardHost_SetWizardButtons_Proxy(
    IWebWizardHost* This,
    VARIANT_BOOL vfEnableBack,
    VARIANT_BOOL vfEnableNext,
    VARIANT_BOOL vfLastPage);
void __RPC_STUB IWebWizardHost_SetWizardButtons_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IWebWizardHost_SetHeaderText_Proxy(
    IWebWizardHost* This,
    BSTR bstrHeaderTitle,
    BSTR bstrHeaderSubtitle);
void __RPC_STUB IWebWizardHost_SetHeaderText_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IWebWizardHost_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INewWDEvents interface
 */
#ifndef __INewWDEvents_INTERFACE_DEFINED__
#define __INewWDEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_INewWDEvents, 0x0751c551, 0x7568, 0x41c9, 0x8e,0x5b, 0xe2,0x2e,0x38,0x91,0x92,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0751c551-7568-41c9-8e5b-e22e38919236")
INewWDEvents : public IWebWizardHost
{
    virtual HRESULT STDMETHODCALLTYPE PassportAuthenticate(
        BSTR bstrSignInUrl,
        VARIANT_BOOL *pvfAuthenitcated) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INewWDEvents, 0x0751c551, 0x7568, 0x41c9, 0x8e,0x5b, 0xe2,0x2e,0x38,0x91,0x92,0x36)
#endif
#else
typedef struct INewWDEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INewWDEvents* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INewWDEvents* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INewWDEvents* This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INewWDEvents* This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INewWDEvents* This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INewWDEvents* This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INewWDEvents* This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWebWizardHost methods ***/
    HRESULT (STDMETHODCALLTYPE *FinalBack)(
        INewWDEvents* This);

    HRESULT (STDMETHODCALLTYPE *FinalNext)(
        INewWDEvents* This);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        INewWDEvents* This);

    HRESULT (STDMETHODCALLTYPE *put_Caption)(
        INewWDEvents* This,
        BSTR bstrCaption);

    HRESULT (STDMETHODCALLTYPE *get_Caption)(
        INewWDEvents* This,
        BSTR *pbstrCaption);

    HRESULT (STDMETHODCALLTYPE *put_Property)(
        INewWDEvents* This,
        BSTR bstrPropertyName,
        VARIANT *pvProperty);

    HRESULT (STDMETHODCALLTYPE *get_Property)(
        INewWDEvents* This,
        BSTR bstrPropertyName,
        VARIANT *pvProperty);

    HRESULT (STDMETHODCALLTYPE *SetWizardButtons)(
        INewWDEvents* This,
        VARIANT_BOOL vfEnableBack,
        VARIANT_BOOL vfEnableNext,
        VARIANT_BOOL vfLastPage);

    HRESULT (STDMETHODCALLTYPE *SetHeaderText)(
        INewWDEvents* This,
        BSTR bstrHeaderTitle,
        BSTR bstrHeaderSubtitle);

    /*** INewWDEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *PassportAuthenticate)(
        INewWDEvents* This,
        BSTR bstrSignInUrl,
        VARIANT_BOOL *pvfAuthenitcated);

    END_INTERFACE
} INewWDEventsVtbl;
interface INewWDEvents {
    CONST_VTBL INewWDEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INewWDEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INewWDEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INewWDEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INewWDEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INewWDEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INewWDEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INewWDEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWebWizardHost methods ***/
#define INewWDEvents_FinalBack(This) (This)->lpVtbl->FinalBack(This)
#define INewWDEvents_FinalNext(This) (This)->lpVtbl->FinalNext(This)
#define INewWDEvents_Cancel(This) (This)->lpVtbl->Cancel(This)
#define INewWDEvents_put_Caption(This,bstrCaption) (This)->lpVtbl->put_Caption(This,bstrCaption)
#define INewWDEvents_get_Caption(This,pbstrCaption) (This)->lpVtbl->get_Caption(This,pbstrCaption)
#define INewWDEvents_put_Property(This,bstrPropertyName,pvProperty) (This)->lpVtbl->put_Property(This,bstrPropertyName,pvProperty)
#define INewWDEvents_get_Property(This,bstrPropertyName,pvProperty) (This)->lpVtbl->get_Property(This,bstrPropertyName,pvProperty)
#define INewWDEvents_SetWizardButtons(This,vfEnableBack,vfEnableNext,vfLastPage) (This)->lpVtbl->SetWizardButtons(This,vfEnableBack,vfEnableNext,vfLastPage)
#define INewWDEvents_SetHeaderText(This,bstrHeaderTitle,bstrHeaderSubtitle) (This)->lpVtbl->SetHeaderText(This,bstrHeaderTitle,bstrHeaderSubtitle)
/*** INewWDEvents methods ***/
#define INewWDEvents_PassportAuthenticate(This,bstrSignInUrl,pvfAuthenitcated) (This)->lpVtbl->PassportAuthenticate(This,bstrSignInUrl,pvfAuthenitcated)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT INewWDEvents_QueryInterface(INewWDEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG INewWDEvents_AddRef(INewWDEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG INewWDEvents_Release(INewWDEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static FORCEINLINE HRESULT INewWDEvents_GetTypeInfoCount(INewWDEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static FORCEINLINE HRESULT INewWDEvents_GetTypeInfo(INewWDEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static FORCEINLINE HRESULT INewWDEvents_GetIDsOfNames(INewWDEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static FORCEINLINE HRESULT INewWDEvents_Invoke(INewWDEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWebWizardHost methods ***/
static FORCEINLINE HRESULT INewWDEvents_FinalBack(INewWDEvents* This) {
    return This->lpVtbl->FinalBack(This);
}
static FORCEINLINE HRESULT INewWDEvents_FinalNext(INewWDEvents* This) {
    return This->lpVtbl->FinalNext(This);
}
static FORCEINLINE HRESULT INewWDEvents_Cancel(INewWDEvents* This) {
    return This->lpVtbl->Cancel(This);
}
static FORCEINLINE HRESULT INewWDEvents_put_Caption(INewWDEvents* This,BSTR bstrCaption) {
    return This->lpVtbl->put_Caption(This,bstrCaption);
}
static FORCEINLINE HRESULT INewWDEvents_get_Caption(INewWDEvents* This,BSTR *pbstrCaption) {
    return This->lpVtbl->get_Caption(This,pbstrCaption);
}
static FORCEINLINE HRESULT INewWDEvents_put_Property(INewWDEvents* This,BSTR bstrPropertyName,VARIANT *pvProperty) {
    return This->lpVtbl->put_Property(This,bstrPropertyName,pvProperty);
}
static FORCEINLINE HRESULT INewWDEvents_get_Property(INewWDEvents* This,BSTR bstrPropertyName,VARIANT *pvProperty) {
    return This->lpVtbl->get_Property(This,bstrPropertyName,pvProperty);
}
static FORCEINLINE HRESULT INewWDEvents_SetWizardButtons(INewWDEvents* This,VARIANT_BOOL vfEnableBack,VARIANT_BOOL vfEnableNext,VARIANT_BOOL vfLastPage) {
    return This->lpVtbl->SetWizardButtons(This,vfEnableBack,vfEnableNext,vfLastPage);
}
static FORCEINLINE HRESULT INewWDEvents_SetHeaderText(INewWDEvents* This,BSTR bstrHeaderTitle,BSTR bstrHeaderSubtitle) {
    return This->lpVtbl->SetHeaderText(This,bstrHeaderTitle,bstrHeaderSubtitle);
}
/*** INewWDEvents methods ***/
static FORCEINLINE HRESULT INewWDEvents_PassportAuthenticate(INewWDEvents* This,BSTR bstrSignInUrl,VARIANT_BOOL *pvfAuthenitcated) {
    return This->lpVtbl->PassportAuthenticate(This,bstrSignInUrl,pvfAuthenitcated);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE INewWDEvents_PassportAuthenticate_Proxy(
    INewWDEvents* This,
    BSTR bstrSignInUrl,
    VARIANT_BOOL *pvfAuthenitcated);
void __RPC_STUB INewWDEvents_PassportAuthenticate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __INewWDEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAutoComplete interface
 */
#ifndef __IAutoComplete_INTERFACE_DEFINED__
#define __IAutoComplete_INTERFACE_DEFINED__

typedef IAutoComplete *LPAUTOCOMPLETE;
DEFINE_GUID(IID_IAutoComplete, 0x00bb2762, 0x6a77, 0x11d0, 0xa5,0x35, 0x00,0xc0,0x4f,0xd7,0xd0,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00bb2762-6a77-11d0-a535-00c04fd7d062")
IAutoComplete : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Init(
        HWND hwndEdit,
        IUnknown *punkACL,
        LPCWSTR pwszRegKeyPath,
        LPCWSTR pwszQuickComplete) = 0;

    virtual HRESULT STDMETHODCALLTYPE Enable(
        WINBOOL fEnable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAutoComplete, 0x00bb2762, 0x6a77, 0x11d0, 0xa5,0x35, 0x00,0xc0,0x4f,0xd7,0xd0,0x62)
#endif
#else
typedef struct IAutoCompleteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAutoComplete* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAutoComplete* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAutoComplete* This);

    /*** IAutoComplete methods ***/
    HRESULT (STDMETHODCALLTYPE *Init)(
        IAutoComplete* This,
        HWND hwndEdit,
        IUnknown *punkACL,
        LPCWSTR pwszRegKeyPath,
        LPCWSTR pwszQuickComplete);

    HRESULT (STDMETHODCALLTYPE *Enable)(
        IAutoComplete* This,
        WINBOOL fEnable);

    END_INTERFACE
} IAutoCompleteVtbl;
interface IAutoComplete {
    CONST_VTBL IAutoCompleteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAutoComplete_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAutoComplete_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAutoComplete_Release(This) (This)->lpVtbl->Release(This)
/*** IAutoComplete methods ***/
#define IAutoComplete_Init(This,hwndEdit,punkACL,pwszRegKeyPath,pwszQuickComplete) (This)->lpVtbl->Init(This,hwndEdit,punkACL,pwszRegKeyPath,pwszQuickComplete)
#define IAutoComplete_Enable(This,fEnable) (This)->lpVtbl->Enable(This,fEnable)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAutoComplete_QueryInterface(IAutoComplete* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAutoComplete_AddRef(IAutoComplete* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAutoComplete_Release(IAutoComplete* This) {
    return This->lpVtbl->Release(This);
}
/*** IAutoComplete methods ***/
static FORCEINLINE HRESULT IAutoComplete_Init(IAutoComplete* This,HWND hwndEdit,IUnknown *punkACL,LPCWSTR pwszRegKeyPath,LPCWSTR pwszQuickComplete) {
    return This->lpVtbl->Init(This,hwndEdit,punkACL,pwszRegKeyPath,pwszQuickComplete);
}
static FORCEINLINE HRESULT IAutoComplete_Enable(IAutoComplete* This,WINBOOL fEnable) {
    return This->lpVtbl->Enable(This,fEnable);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAutoComplete_Init_Proxy(
    IAutoComplete* This,
    HWND hwndEdit,
    IUnknown *punkACL,
    LPCWSTR pwszRegKeyPath,
    LPCWSTR pwszQuickComplete);
void __RPC_STUB IAutoComplete_Init_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAutoComplete_Enable_Proxy(
    IAutoComplete* This,
    WINBOOL fEnable);
void __RPC_STUB IAutoComplete_Enable_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IAutoComplete_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAutoComplete2 interface
 */
#ifndef __IAutoComplete2_INTERFACE_DEFINED__
#define __IAutoComplete2_INTERFACE_DEFINED__

typedef IAutoComplete2 *LPAUTOCOMPLETE2;
typedef enum _tagAUTOCOMPLETEOPTIONS {
    ACO_NONE = 0x0,
    ACO_AUTOSUGGEST = 0x1,
    ACO_AUTOAPPEND = 0x2,
    ACO_SEARCH = 0x4,
    ACO_FILTERPREFIXES = 0x8,
    ACO_USETAB = 0x10,
    ACO_UPDOWNKEYDROPSLIST = 0x20,
    ACO_RTLREADING = 0x40,
    ACO_WORD_FILTER = 0x80,
    ACO_NOPREFIXFILTERING = 0x100
} AUTOCOMPLETEOPTIONS;
DEFINE_GUID(IID_IAutoComplete2, 0xeac04bc0, 0x3791, 0x11d2, 0xbb,0x95, 0x00,0x60,0x97,0x7b,0x46,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eac04bc0-3791-11d2-bb95-0060977b464c")
IAutoComplete2 : public IAutoComplete
{
    virtual HRESULT STDMETHODCALLTYPE SetOptions(
        DWORD dwFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOptions(
        DWORD *pdwFlag) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAutoComplete2, 0xeac04bc0, 0x3791, 0x11d2, 0xbb,0x95, 0x00,0x60,0x97,0x7b,0x46,0x4c)
#endif
#else
typedef struct IAutoComplete2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAutoComplete2* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAutoComplete2* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAutoComplete2* This);

    /*** IAutoComplete methods ***/
    HRESULT (STDMETHODCALLTYPE *Init)(
        IAutoComplete2* This,
        HWND hwndEdit,
        IUnknown *punkACL,
        LPCWSTR pwszRegKeyPath,
        LPCWSTR pwszQuickComplete);

    HRESULT (STDMETHODCALLTYPE *Enable)(
        IAutoComplete2* This,
        WINBOOL fEnable);

    /*** IAutoComplete2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetOptions)(
        IAutoComplete2* This,
        DWORD dwFlag);

    HRESULT (STDMETHODCALLTYPE *GetOptions)(
        IAutoComplete2* This,
        DWORD *pdwFlag);

    END_INTERFACE
} IAutoComplete2Vtbl;
interface IAutoComplete2 {
    CONST_VTBL IAutoComplete2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAutoComplete2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAutoComplete2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAutoComplete2_Release(This) (This)->lpVtbl->Release(This)
/*** IAutoComplete methods ***/
#define IAutoComplete2_Init(This,hwndEdit,punkACL,pwszRegKeyPath,pwszQuickComplete) (This)->lpVtbl->Init(This,hwndEdit,punkACL,pwszRegKeyPath,pwszQuickComplete)
#define IAutoComplete2_Enable(This,fEnable) (This)->lpVtbl->Enable(This,fEnable)
/*** IAutoComplete2 methods ***/
#define IAutoComplete2_SetOptions(This,dwFlag) (This)->lpVtbl->SetOptions(This,dwFlag)
#define IAutoComplete2_GetOptions(This,pdwFlag) (This)->lpVtbl->GetOptions(This,pdwFlag)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IAutoComplete2_QueryInterface(IAutoComplete2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IAutoComplete2_AddRef(IAutoComplete2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IAutoComplete2_Release(IAutoComplete2* This) {
    return This->lpVtbl->Release(This);
}
/*** IAutoComplete methods ***/
static FORCEINLINE HRESULT IAutoComplete2_Init(IAutoComplete2* This,HWND hwndEdit,IUnknown *punkACL,LPCWSTR pwszRegKeyPath,LPCWSTR pwszQuickComplete) {
    return This->lpVtbl->Init(This,hwndEdit,punkACL,pwszRegKeyPath,pwszQuickComplete);
}
static FORCEINLINE HRESULT IAutoComplete2_Enable(IAutoComplete2* This,WINBOOL fEnable) {
    return This->lpVtbl->Enable(This,fEnable);
}
/*** IAutoComplete2 methods ***/
static FORCEINLINE HRESULT IAutoComplete2_SetOptions(IAutoComplete2* This,DWORD dwFlag) {
    return This->lpVtbl->SetOptions(This,dwFlag);
}
static FORCEINLINE HRESULT IAutoComplete2_GetOptions(IAutoComplete2* This,DWORD *pdwFlag) {
    return This->lpVtbl->GetOptions(This,pdwFlag);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAutoComplete2_SetOptions_Proxy(
    IAutoComplete2* This,
    DWORD dwFlag);
void __RPC_STUB IAutoComplete2_SetOptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IAutoComplete2_GetOptions_Proxy(
    IAutoComplete2* This,
    DWORD *pdwFlag);
void __RPC_STUB IAutoComplete2_GetOptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IAutoComplete2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumACString interface
 */
#ifndef __IEnumACString_INTERFACE_DEFINED__
#define __IEnumACString_INTERFACE_DEFINED__

typedef IEnumACString *PENUMACSTRING;
typedef IEnumACString *LPENUMACSTRING;
typedef enum _tagACENUMOPTION {
    ACEO_NONE = 0x0,
    ACEO_MOSTRECENTFIRST = 0x1,
    ACEO_FIRSTUNUSED = 0x10000
} ACENUMOPTION;
DEFINE_GUID(IID_IEnumACString, 0x8e74c210, 0xcf9d, 0x4eaf, 0xa4,0x03, 0x73,0x56,0x42,0x8f,0x0a,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8e74c210-cf9d-4eaf-a403-7356428f0a5a")
IEnumACString : public IEnumString
{
    virtual HRESULT STDMETHODCALLTYPE NextItem(
        LPWSTR pszUrl,
        ULONG cchMax,
        ULONG *pulSortIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnumOptions(
        DWORD dwOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumOptions(
        DWORD *pdwOptions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumACString, 0x8e74c210, 0xcf9d, 0x4eaf, 0xa4,0x03, 0x73,0x56,0x42,0x8f,0x0a,0x5a)
#endif
#else
typedef struct IEnumACStringVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumACString* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumACString* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumACString* This);

    /*** IEnumString methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumACString* This,
        ULONG celt,
        LPOLESTR *rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumACString* This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumACString* This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumACString* This,
        IEnumString **ppenum);

    /*** IEnumACString methods ***/
    HRESULT (STDMETHODCALLTYPE *NextItem)(
        IEnumACString* This,
        LPWSTR pszUrl,
        ULONG cchMax,
        ULONG *pulSortIndex);

    HRESULT (STDMETHODCALLTYPE *SetEnumOptions)(
        IEnumACString* This,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *GetEnumOptions)(
        IEnumACString* This,
        DWORD *pdwOptions);

    END_INTERFACE
} IEnumACStringVtbl;
interface IEnumACString {
    CONST_VTBL IEnumACStringVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumACString_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumACString_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumACString_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumString methods ***/
#define IEnumACString_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumACString_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumACString_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumACString_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
/*** IEnumACString methods ***/
#define IEnumACString_NextItem(This,pszUrl,cchMax,pulSortIndex) (This)->lpVtbl->NextItem(This,pszUrl,cchMax,pulSortIndex)
#define IEnumACString_SetEnumOptions(This,dwOptions) (This)->lpVtbl->SetEnumOptions(This,dwOptions)
#define IEnumACString_GetEnumOptions(This,pdwOptions) (This)->lpVtbl->GetEnumOptions(This,pdwOptions)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IEnumACString_QueryInterface(IEnumACString* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IEnumACString_AddRef(IEnumACString* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IEnumACString_Release(IEnumACString* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumString methods ***/
static FORCEINLINE HRESULT IEnumACString_Next(IEnumACString* This,ULONG celt,LPOLESTR *rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static FORCEINLINE HRESULT IEnumACString_Skip(IEnumACString* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static FORCEINLINE HRESULT IEnumACString_Reset(IEnumACString* This) {
    return This->lpVtbl->Reset(This);
}
static FORCEINLINE HRESULT IEnumACString_Clone(IEnumACString* This,IEnumString **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
/*** IEnumACString methods ***/
static FORCEINLINE HRESULT IEnumACString_NextItem(IEnumACString* This,LPWSTR pszUrl,ULONG cchMax,ULONG *pulSortIndex) {
    return This->lpVtbl->NextItem(This,pszUrl,cchMax,pulSortIndex);
}
static FORCEINLINE HRESULT IEnumACString_SetEnumOptions(IEnumACString* This,DWORD dwOptions) {
    return This->lpVtbl->SetEnumOptions(This,dwOptions);
}
static FORCEINLINE HRESULT IEnumACString_GetEnumOptions(IEnumACString* This,DWORD *pdwOptions) {
    return This->lpVtbl->GetEnumOptions(This,pdwOptions);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumACString_NextItem_Proxy(
    IEnumACString* This,
    LPWSTR pszUrl,
    ULONG cchMax,
    ULONG *pulSortIndex);
void __RPC_STUB IEnumACString_NextItem_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumACString_SetEnumOptions_Proxy(
    IEnumACString* This,
    DWORD dwOptions);
void __RPC_STUB IEnumACString_SetEnumOptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IEnumACString_GetEnumOptions_Proxy(
    IEnumACString* This,
    DWORD *pdwOptions);
void __RPC_STUB IEnumACString_GetEnumOptions_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IEnumACString_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDataObjectAsyncCapability interface
 */
#ifndef __IDataObjectAsyncCapability_INTERFACE_DEFINED__
#define __IDataObjectAsyncCapability_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDataObjectAsyncCapability, 0x3d8b0590, 0xf691, 0x11d2, 0x8e,0xa9, 0x00,0x60,0x97,0xdf,0x5b,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3d8b0590-f691-11d2-8ea9-006097df5bd4")
IDataObjectAsyncCapability : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetAsyncMode(
        WINBOOL fDoOpAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAsyncMode(
        WINBOOL *pfIsOpAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartOperation(
        IBindCtx *pbcReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE InOperation(
        WINBOOL *pfInAsyncOp) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndOperation(
        HRESULT hResult,
        IBindCtx *pbcReserved,
        DWORD dwEffects) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDataObjectAsyncCapability, 0x3d8b0590, 0xf691, 0x11d2, 0x8e,0xa9, 0x00,0x60,0x97,0xdf,0x5b,0xd4)
#endif
#else
typedef struct IDataObjectAsyncCapabilityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDataObjectAsyncCapability* This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDataObjectAsyncCapability* This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDataObjectAsyncCapability* This);

    /*** IDataObjectAsyncCapability methods ***/
    HRESULT (STDMETHODCALLTYPE *SetAsyncMode)(
        IDataObjectAsyncCapability* This,
        WINBOOL fDoOpAsync);

    HRESULT (STDMETHODCALLTYPE *GetAsyncMode)(
        IDataObjectAsyncCapability* This,
        WINBOOL *pfIsOpAsync);

    HRESULT (STDMETHODCALLTYPE *StartOperation)(
        IDataObjectAsyncCapability* This,
        IBindCtx *pbcReserved);

    HRESULT (STDMETHODCALLTYPE *InOperation)(
        IDataObjectAsyncCapability* This,
        WINBOOL *pfInAsyncOp);

    HRESULT (STDMETHODCALLTYPE *EndOperation)(
        IDataObjectAsyncCapability* This,
        HRESULT hResult,
        IBindCtx *pbcReserved,
        DWORD dwEffects);

    END_INTERFACE
} IDataObjectAsyncCapabilityVtbl;
interface IDataObjectAsyncCapability {
    CONST_VTBL IDataObjectAsyncCapabilityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDataObjectAsyncCapability_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDataObjectAsyncCapability_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDataObjectAsyncCapability_Release(This) (This)->lpVtbl->Release(This)
/*** IDataObjectAsyncCapability methods ***/
#define IDataObjectAsyncCapability_SetAsyncMode(This,fDoOpAsync) (This)->lpVtbl->SetAsyncMode(This,fDoOpAsync)
#define IDataObjectAsyncCapability_GetAsyncMode(This,pfIsOpAsync) (This)->lpVtbl->GetAsyncMode(This,pfIsOpAsync)
#define IDataObjectAsyncCapability_StartOperation(This,pbcReserved) (This)->lpVtbl->StartOperation(This,pbcReserved)
#define IDataObjectAsyncCapability_InOperation(This,pfInAsyncOp) (This)->lpVtbl->InOperation(This,pfInAsyncOp)
#define IDataObjectAsyncCapability_EndOperation(This,hResult,pbcReserved,dwEffects) (This)->lpVtbl->EndOperation(This,hResult,pbcReserved,dwEffects)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT IDataObjectAsyncCapability_QueryInterface(IDataObjectAsyncCapability* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG IDataObjectAsyncCapability_AddRef(IDataObjectAsyncCapability* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG IDataObjectAsyncCapability_Release(IDataObjectAsyncCapability* This) {
    return This->lpVtbl->Release(This);
}
/*** IDataObjectAsyncCapability methods ***/
static FORCEINLINE HRESULT IDataObjectAsyncCapability_SetAsyncMode(IDataObjectAsyncCapability* This,WINBOOL fDoOpAsync) {
    return This->lpVtbl->SetAsyncMode(This,fDoOpAsync);
}
static FORCEINLINE HRESULT IDataObjectAsyncCapability_GetAsyncMode(IDataObjectAsyncCapability* This,WINBOOL *pfIsOpAsync) {
    return This->lpVtbl->GetAsyncMode(This,pfIsOpAsync);
}
static FORCEINLINE HRESULT IDataObjectAsyncCapability_StartOperation(IDataObjectAsyncCapability* This,IBindCtx *pbcReserved) {
    return This->lpVtbl->StartOperation(This,pbcReserved);
}
static FORCEINLINE HRESULT IDataObjectAsyncCapability_InOperation(IDataObjectAsyncCapability* This,WINBOOL *pfInAsyncOp) {
    return This->lpVtbl->InOperation(This,pfInAsyncOp);
}
static FORCEINLINE HRESULT IDataObjectAsyncCapability_EndOperation(IDataObjectAsyncCapability* This,HRESULT hResult,IBindCtx *pbcReserved,DWORD dwEffects) {
    return This->lpVtbl->EndOperation(This,hResult,pbcReserved,dwEffects);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IDataObjectAsyncCapability_SetAsyncMode_Proxy(
    IDataObjectAsyncCapability* This,
    WINBOOL fDoOpAsync);
void __RPC_STUB IDataObjectAsyncCapability_SetAsyncMode_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObjectAsyncCapability_GetAsyncMode_Proxy(
    IDataObjectAsyncCapability* This,
    WINBOOL *pfIsOpAsync);
void __RPC_STUB IDataObjectAsyncCapability_GetAsyncMode_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObjectAsyncCapability_StartOperation_Proxy(
    IDataObjectAsyncCapability* This,
    IBindCtx *pbcReserved);
void __RPC_STUB IDataObjectAsyncCapability_StartOperation_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObjectAsyncCapability_InOperation_Proxy(
    IDataObjectAsyncCapability* This,
    WINBOOL *pfInAsyncOp);
void __RPC_STUB IDataObjectAsyncCapability_InOperation_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IDataObjectAsyncCapability_EndOperation_Proxy(
    IDataObjectAsyncCapability* This,
    HRESULT hResult,
    IBindCtx *pbcReserved,
    DWORD dwEffects);
void __RPC_STUB IDataObjectAsyncCapability_EndOperation_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);

#endif  /* __IDataObjectAsyncCapability_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __shldisp_h__ */
