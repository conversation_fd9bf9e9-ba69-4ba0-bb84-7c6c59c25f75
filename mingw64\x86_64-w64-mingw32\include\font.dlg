/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
FORMATDLGORD31 DIALOG 13,54,287,196
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU |
      DS_CONTEXTHELP | DS_3DLOOK
CAPTION "Font"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "&Font:",stc1,7,7,40,9
    COMBOBOX cmb1,7,16,98,76,CBS_SIMPLE | CBS_AUTOHSCROLL | CBS_DISABLENOSCROLL |
                    CBS_SORT | WS_VSCROLL | WS_TABSTOP | CBS_HASSTRINGS |
                    CBS_OWNERDRAWFIXED

    LTEXT "Font st&yle:",stc2,110,7,44,9
    COMBOBOX cmb2,110,16,74,76,CBS_SIMPLE | CBS_AUTOHSCROLL | WS_VSCROLL | WS_TABSTOP

    LTEXT "&Size:",stc3,189,7,30,9
    COMBOBOX cmb3,190,16,36,76,CBS_SIMPLE | CBS_AUTOHSCROLL | CBS_DISABLENOSCROLL |
                    CBS_SORT | WS_VSCROLL | WS_TABSTOP | CBS_HASSTRINGS |
                    CBS_OWNERDRAWFIXED

    GROUPBOX "Effects",grp1,7,97,98,72,WS_GROUP
    AUTOCHECKBOX "Stri&keout",chx1,13,110,49,10,WS_TABSTOP
    AUTOCHECKBOX "&Underline",chx2,13,123,51,10

    LTEXT "&Color:",stc4,13,136,30,9
    COMBOBOX cmb4,13,146,82,100,CBS_DROPDOWNLIST | CBS_OWNERDRAWFIXED | CBS_AUTOHSCROLL |
                    CBS_HASSTRINGS | WS_BORDER | WS_VSCROLL | WS_TABSTOP

    GROUPBOX "Sample",grp2,110,97,116,43,WS_GROUP
    CTEXT "AaBbYyZz",stc5,118,111,100,23,SS_NOPREFIX | NOT WS_VISIBLE
    LTEXT "",stc6,7,172,219,20,SS_NOPREFIX | NOT WS_GROUP

    LTEXT "Sc&ript:",stc7,110,147,30,9
    COMBOBOX cmb5,110,157,116,30,CBS_DROPDOWNLIST |
                    CBS_OWNERDRAWFIXED | CBS_AUTOHSCROLL | CBS_HASSTRINGS |
                    WS_BORDER | WS_VSCROLL | WS_TABSTOP

    DEFPUSHBUTTON "OK",IDOK,231,16,45,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,231,32,45,14,WS_GROUP
    PUSHBUTTON "&Apply",psh3,231,48,45,14,WS_GROUP
    PUSHBUTTON "&Help",pshHelp,231,64,45,14,WS_GROUP

END
